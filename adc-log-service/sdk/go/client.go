package sdk

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Client represents a client for the ADC Log Service
type Client struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
	logger     zerolog.Logger
}

// Config represents the client configuration
type Config struct {
	BaseURL    string
	APIKey     string
	Timeout    time.Duration
	Logger     *zerolog.Logger
}

// NewClient creates a new ADC Log Service client
func NewClient(baseURL, apiKey string) *Client {
	return &Client{
		baseURL: baseURL,
		apiKey:  apiKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: log.With().Str("component", "adc-log-sdk").Logger(),
	}
}

// NewClientWithConfig creates a new ADC Log Service client with configuration
func NewClientWithConfig(config Config) *Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	var logger zerolog.Logger
	if config.Logger != nil {
		logger = *config.Logger
	} else {
		logger = log.With().Str("component", "adc-log-sdk").Logger()
	}

	return &Client{
		baseURL: config.BaseURL,
		apiKey:  config.APIKey,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger: logger,
	}
}

// NewClientWithHTTP creates a new client with a custom HTTP client
func NewClientWithHTTP(baseURL, apiKey string, httpClient *http.Client) *Client {
	return &Client{
		baseURL:    baseURL,
		apiKey:     apiKey,
		httpClient: httpClient,
		logger:     log.With().Str("component", "adc-log-sdk").Logger(),
	}
}

// IngestLog sends a single log entry to the service
func (c *Client) IngestLog(ctx context.Context, log *LogIngestRequest) error {
	url := fmt.Sprintf("%s/api/v1/logs", c.baseURL)
	return c.makeRequest(ctx, "POST", url, log, nil)
}

// IngestBatch sends multiple log entries to the service
func (c *Client) IngestBatch(ctx context.Context, logs *LogBatchRequest) error {
	url := fmt.Sprintf("%s/api/v1/logs/batch", c.baseURL)
	return c.makeRequest(ctx, "POST", url, logs, nil)
}

// SearchLogs searches for log entries
func (c *Client) SearchLogs(ctx context.Context, query *LogSearchQuery) (*LogSearchResult, error) {
	params := c.buildSearchParams(query)
	url := fmt.Sprintf("%s/api/v1/logs/search?%s", c.baseURL, params.Encode())
	
	var result LogSearchResult
	err := c.makeRequest(ctx, "GET", url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// GetLogByID retrieves a specific log entry by ID
func (c *Client) GetLogByID(ctx context.Context, id uuid.UUID) (*LogEntry, error) {
	url := fmt.Sprintf("%s/api/v1/logs/%s", c.baseURL, id.String())
	
	var entry LogEntry
	err := c.makeRequest(ctx, "GET", url, nil, &entry)
	if err != nil {
		return nil, err
	}
	return &entry, nil
}

// GetStats retrieves log statistics
func (c *Client) GetStats(ctx context.Context, organizationID *uuid.UUID, startTime, endTime time.Time) (*LogStats, error) {
	params := url.Values{}
	params.Set("start_time", startTime.Format(time.RFC3339))
	params.Set("end_time", endTime.Format(time.RFC3339))
	if organizationID != nil {
		params.Set("organization_id", organizationID.String())
	}

	url := fmt.Sprintf("%s/api/v1/logs/stats?%s", c.baseURL, params.Encode())
	
	var stats LogStats
	err := c.makeRequest(ctx, "GET", url, nil, &stats)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// TriggerCleanup triggers log cleanup for an organization
func (c *Client) TriggerCleanup(ctx context.Context, organizationID *uuid.UUID) (*CleanupResult, error) {
	params := url.Values{}
	if organizationID != nil {
		params.Set("organization_id", organizationID.String())
	}

	url := fmt.Sprintf("%s/api/v1/logs/cleanup?%s", c.baseURL, params.Encode())
	
	var result CleanupResult
	err := c.makeRequest(ctx, "POST", url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// HealthCheck checks if the log service is healthy
func (c *Client) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", c.baseURL)
	return c.makeRequest(ctx, "GET", url, nil, nil)
}

// makeRequest makes an HTTP request to the log service
func (c *Client) makeRequest(ctx context.Context, method, url string, body interface{}, result interface{}) error {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			c.logger.Error().Err(err).Msg("Failed to marshal request body")
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		c.logger.Error().Err(err).Str("url", url).Msg("Failed to create request")
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.apiKey)

	// Add correlation ID if available in context
	if correlationID := ctx.Value("correlation_id"); correlationID != nil {
		if id, ok := correlationID.(string); ok {
			req.Header.Set("X-Correlation-ID", id)
		}
	}

	c.logger.Debug().
		Str("method", method).
		Str("url", url).
		Msg("Making HTTP request to log service")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error().Err(err).Str("url", url).Msg("HTTP request failed")
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		c.logger.Error().
			Int("status_code", resp.StatusCode).
			Str("response_body", string(body)).
			Msg("HTTP request returned error status")

		var errResp struct {
			Error   string `json:"error"`
			Details string `json:"details,omitempty"`
		}
		if json.Unmarshal(body, &errResp) == nil {
			return fmt.Errorf("API error (%d): %s - %s", resp.StatusCode, errResp.Error, errResp.Details)
		}
		return fmt.Errorf("API error (%d)", resp.StatusCode)
	}

	if result != nil {
		if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
			c.logger.Error().Err(err).Msg("Failed to decode response")
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	c.logger.Debug().
		Int("status_code", resp.StatusCode).
		Msg("HTTP request successful")

	return nil
}

// buildSearchParams builds URL parameters for search queries
func (c *Client) buildSearchParams(query *LogSearchQuery) url.Values {
	params := url.Values{}

	if query.StartTime != nil {
		params.Set("start_time", query.StartTime.Format(time.RFC3339))
	}
	if query.EndTime != nil {
		params.Set("end_time", query.EndTime.Format(time.RFC3339))
	}

	for _, level := range query.Levels {
		params.Add("level", string(level))
	}

	for _, service := range query.Services {
		params.Add("service", service)
	}

	for _, component := range query.Components {
		params.Add("component", component)
	}

	if query.Message != "" {
		params.Set("message", query.Message)
	}

	if query.OrganizationID != nil {
		params.Set("organization_id", query.OrganizationID.String())
	}
	if query.ShopID != nil {
		params.Set("shop_id", query.ShopID.String())
	}
	if query.UserID != nil {
		params.Set("user_id", query.UserID.String())
	}

	if query.CorrelationID != "" {
		params.Set("correlation_id", query.CorrelationID)
	}
	if query.RequestID != "" {
		params.Set("request_id", query.RequestID)
	}
	if query.SessionID != "" {
		params.Set("session_id", query.SessionID)
	}
	if query.ErrorCode != "" {
		params.Set("error_code", query.ErrorCode)
	}

	for _, tag := range query.Tags {
		params.Add("tag", tag)
	}

	if query.Limit > 0 {
		params.Set("limit", fmt.Sprintf("%d", query.Limit))
	}
	if query.Offset > 0 {
		params.Set("offset", fmt.Sprintf("%d", query.Offset))
	}

	if query.SortBy != "" {
		params.Set("sort_by", query.SortBy)
	}
	if query.SortOrder != "" {
		params.Set("sort_order", query.SortOrder)
	}

	return params
}

// ZerologLogWriter implements a zerolog writer that sends logs to the ADC Log Service
type ZerologLogWriter struct {
	client      *Client
	serviceName string
	environment string
	buffer      []LogIngestRequest
	bufferSize  int
	flushTimer  *time.Timer
}

// NewZerologLogWriter creates a new zerolog writer for the ADC Log Service
func NewZerologLogWriter(client *Client, serviceName, environment string) *ZerologLogWriter {
	writer := &ZerologLogWriter{
		client:      client,
		serviceName: serviceName,
		environment: environment,
		buffer:      make([]LogIngestRequest, 0, 100),
		bufferSize:  100,
	}

	// Auto-flush every 5 seconds
	writer.flushTimer = time.AfterFunc(5*time.Second, writer.Flush)

	return writer
}

// Write implements the io.Writer interface for zerolog
func (w *ZerologLogWriter) Write(p []byte) (n int, err error) {
	var logData map[string]interface{}
	if err := json.Unmarshal(p, &logData); err != nil {
		return 0, fmt.Errorf("failed to parse log entry: %w", err)
	}

	// Convert zerolog entry to LogIngestRequest
	logEntry := LogIngestRequest{
		Service:     w.serviceName,
		Environment: w.environment,
		Message:     fmt.Sprintf("%v", logData["message"]),
		Timestamp:   time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	// Map zerolog level to our LogLevel
	if levelStr, ok := logData["level"].(string); ok {
		switch levelStr {
		case "trace":
			logEntry.Level = LogLevelTrace
		case "debug":
			logEntry.Level = LogLevelDebug
		case "info":
			logEntry.Level = LogLevelInfo
		case "warn":
			logEntry.Level = LogLevelWarn
		case "error":
			logEntry.Level = LogLevelError
		case "fatal":
			logEntry.Level = LogLevelFatal
		case "panic":
			logEntry.Level = LogLevelPanic
		default:
			logEntry.Level = LogLevelInfo
		}
	}

	// Extract additional fields
	if userID, ok := logData["user_id"].(string); ok {
		if parsedID, err := uuid.Parse(userID); err == nil {
			logEntry.UserID = &parsedID
		}
	}
	if orgID, ok := logData["organization_id"].(string); ok {
		if parsedID, err := uuid.Parse(orgID); err == nil {
			logEntry.OrganizationID = &parsedID
		}
	}
	if shopID, ok := logData["shop_id"].(string); ok {
		if parsedID, err := uuid.Parse(shopID); err == nil {
			logEntry.ShopID = &parsedID
		}
	}
	if correlationID, ok := logData["correlation_id"].(string); ok {
		logEntry.CorrelationID = correlationID
	}
	if requestID, ok := logData["request_id"].(string); ok {
		logEntry.RequestID = requestID
	}
	if sessionID, ok := logData["session_id"].(string); ok {
		logEntry.SessionID = sessionID
	}
	if component, ok := logData["component"].(string); ok {
		logEntry.Component = component
	}

	// Copy all additional fields to metadata
	for key, value := range logData {
		if key != "level" && key != "message" && key != "time" && 
		   key != "user_id" && key != "organization_id" && key != "shop_id" &&
		   key != "correlation_id" && key != "request_id" && key != "session_id" &&
		   key != "component" {
			logEntry.Metadata[key] = value
		}
	}

	// Add to buffer
	w.buffer = append(w.buffer, logEntry)

	// Flush if buffer is full
	if len(w.buffer) >= w.bufferSize {
		w.Flush()
	}

	return len(p), nil
}

// Flush sends all buffered logs to the service
func (w *ZerologLogWriter) Flush() {
	if len(w.buffer) == 0 {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	batchRequest := &LogBatchRequest{
		Logs: w.buffer,
	}

	if err := w.client.IngestBatch(ctx, batchRequest); err != nil {
		w.client.logger.Error().
			Err(err).
			Int("log_count", len(w.buffer)).
			Msg("Failed to flush logs to service")
	} else {
		w.client.logger.Debug().
			Int("log_count", len(w.buffer)).
			Msg("Successfully flushed logs to service")
	}

	// Clear buffer
	w.buffer = w.buffer[:0]

	// Reset timer
	w.flushTimer.Reset(5 * time.Second)
}

// Close flushes any remaining logs and stops the flush timer
func (w *ZerologLogWriter) Close() error {
	w.flushTimer.Stop()
	w.Flush()
	return nil
}

// SetBufferSize sets the buffer size for batch operations
func (w *ZerologLogWriter) SetBufferSize(size int) {
	if size > 0 && size <= 1000 {
		w.bufferSize = size
	}
}
