package main

import (
	"fmt"
	"os"
	"time"

	"github.com/adc-log-service/sdk"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

func main() {
	// Initialize logger
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()

	// Create client configuration
	config := sdk.ClientConfig{
		BaseURL:   "http://localhost:9200", // Log Service URL
		APIKey:    "your-api-key-here",
		Timeout:   30 * time.Second,
		UserAgent: "example-app/1.0.0",
		Logger:    logger,
	}

	// Create client
	client := sdk.NewClient(config)

	// Test health check
	fmt.Println("Testing health check...")
	if err := client.Health(); err != nil {
		logger.Error().Err(err).Msg("Health check failed")
		return
	}
	fmt.Println("Health check passed!")

	// Example 1: Log a single entry
	fmt.Println("\nExample 1: Logging a single entry...")
	userID := uuid.New()
	orgID := uuid.New()

	entry := sdk.LogEntry{
		Level:          sdk.LogLevelInfo,
		Message:        "User successfully logged in",
		Source:         sdk.LogSourceUserAction,
		ServiceName:    "auth-service",
		Environment:    "production",
		UserID:         &userID,
		OrganizationID: &orgID,
		SessionID:      "session-123",
		RequestID:      "req-456",
		CorrelationID:  "corr-789",
		Metadata: map[string]interface{}{
			"login_method": "email",
			"ip_address":   "*************",
		},
		Tags: []string{"authentication", "success"},
	}

	if err := client.LogSingle(entry); err != nil {
		logger.Error().Err(err).Msg("Failed to log single entry")
		return
	}
	fmt.Println("Single log entry sent successfully!")

	// Example 2: Log batch entries
	fmt.Println("\nExample 2: Logging batch entries...")
	entries := []sdk.LogEntry{
		{
			Level:       sdk.LogLevelDebug,
			Message:     "Database connection established",
			Source:      sdk.LogSourceDatabase,
			ServiceName: "user-service",
			Environment: "production",
			Metadata: map[string]interface{}{
				"connection_pool_size": 10,
				"database_host":        "db.example.com",
			},
		},
		{
			Level:       sdk.LogLevelWarn,
			Message:     "High memory usage detected",
			Source:      sdk.LogSourceSystem,
			ServiceName: "user-service",
			Environment: "production",
			Metadata: map[string]interface{}{
				"memory_usage_percent": 85,
				"threshold":            80,
			},
			Tags: []string{"performance", "memory"},
		},
		{
			Level:        sdk.LogLevelError,
			Message:      "Failed to process payment",
			Source:       sdk.LogSourceExternalAPI,
			ServiceName:  "payment-service",
			Environment:  "production",
			ErrorCode:    "PAYMENT_GATEWAY_ERROR",
			ErrorStack:   "payment.go:123\n  processPayment()\n  handleRequest()",
			UserID:       &userID,
			OrganizationID: &orgID,
			Metadata: map[string]interface{}{
				"payment_id":     "pay_123456789",
				"amount":         99.99,
				"currency":       "USD",
				"gateway":        "stripe",
				"gateway_error":  "card_declined",
			},
			Tags: []string{"payment", "error", "stripe"},
		},
	}

	if err := client.LogBatch(entries); err != nil {
		logger.Error().Err(err).Msg("Failed to log batch entries")
		return
	}
	fmt.Println("Batch log entries sent successfully!")

	// Example 3: Search logs
	fmt.Println("\nExample 3: Searching logs...")
	serviceName := "user-service"
	environment := "production"
	level := sdk.LogLevelWarn
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()

	query := sdk.LogQuery{
		ServiceName: &serviceName,
		Environment: &environment,
		Level:       &level,
		StartTime:   &startTime,
		EndTime:     &endTime,
		Limit:       10,
	}

	result, err := client.Search(query)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to search logs")
		return
	}

	fmt.Printf("Found %d logs (total: %d)\n", len(result.Logs), result.Total)
	for i, log := range result.Logs {
		fmt.Printf("  %d. [%s] %s: %s\n", i+1, log.Level, log.ServiceName, log.Message)
	}

	// Example 4: Get logs by request ID
	fmt.Println("\nExample 4: Getting logs by request ID...")
	requestLogs, err := client.GetLogsByRequestID("req-456")
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get logs by request ID")
		return
	}

	fmt.Printf("Found %d logs for request ID 'req-456'\n", len(requestLogs))
	for i, log := range requestLogs {
		fmt.Printf("  %d. [%s] %s\n", i+1, log.Level, log.Message)
	}

	// Example 5: Get error logs
	fmt.Println("\nExample 5: Getting error logs...")
	errorLogs, err := client.GetErrorLogs("payment-service", "production", startTime, endTime, 5)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get error logs")
		return
	}

	fmt.Printf("Found %d error logs for payment-service\n", len(errorLogs))
	for i, log := range errorLogs {
		fmt.Printf("  %d. [%s] %s (Error Code: %s)\n", i+1, log.Level, log.Message, log.ErrorCode)
	}

	// Example 6: Get aggregations
	fmt.Println("\nExample 6: Getting log aggregations...")
	aggregations, err := client.GetAggregations("user-service", "production", startTime, endTime, "hour")
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get aggregations")
		return
	}

	fmt.Printf("Found %d aggregation buckets\n", len(aggregations))
	for i, agg := range aggregations {
		fmt.Printf("  %d. %s: Total=%d, Errors=%d, Warnings=%d\n", 
			i+1, agg.TimeBucket.Format("2006-01-02 15:04:05"), 
			agg.TotalCount, agg.ErrorCount, agg.WarnCount)
	}

	// Example 7: Using the logger adapter
	fmt.Println("\nExample 7: Using logger adapter...")
	adapter := sdk.NewLoggerAdapter(client, "example-app", "development")
	
	// Create a zerolog logger that uses the adapter
	adapterLogger := zerolog.New(adapter).With().
		Str("service", "example-app").
		Str("version", "1.0.0").
		Timestamp().
		Logger()

	// Log messages that will be sent to the Log Service
	adapterLogger.Info().Str("action", "startup").Msg("Application started")
	adapterLogger.Warn().Int("connections", 100).Msg("High connection count")
	adapterLogger.Error().Str("error", "database_timeout").Msg("Database operation failed")

	fmt.Println("Logger adapter examples completed!")
	fmt.Println("\nAll examples completed successfully!")
}