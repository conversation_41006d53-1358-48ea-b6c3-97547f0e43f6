package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/adc-log-service/internal/models"
	"github.com/adc-log-service/internal/storage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// StreamService handles real-time log streaming
type StreamService struct {
	cache      storage.Cache
	keyBuilder *storage.CacheKeyBuilder
	clients    sync.Map // map[string]*StreamClient
}

// NewStreamService creates a new stream service instance
func NewStreamService() *StreamService {
	return &StreamService{
		keyBuilder: storage.NewCacheKeyBuilder("adc_logs:"),
	}
}

// StreamClient represents a connected streaming client
type StreamClient struct {
	ID             string
	OrganizationID *uuid.UUID
	Service        string
	Filters        *StreamFilters
	Channel        chan *models.LogEntry
	Done           chan bool
	LastSeen       time.Time
}

// StreamFilters defines filtering criteria for streaming
type StreamFilters struct {
	Levels         []models.LogLevel `json:"levels,omitempty"`
	Services       []string          `json:"services,omitempty"`
	Components     []string          `json:"components,omitempty"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID        `json:"shop_id,omitempty"`
	UserID         *uuid.UUID        `json:"user_id,omitempty"`
	Message        string            `json:"message,omitempty"`
	Tags           []string          `json:"tags,omitempty"`
}

// StartStream starts a new log stream for a client
func (s *StreamService) StartStream(ctx context.Context, clientID string, organizationID *uuid.UUID, service string, filters *StreamFilters) (*StreamClient, error) {
	// Validate filters
	if err := s.validateFilters(filters); err != nil {
		return nil, fmt.Errorf("invalid filters: %w", err)
	}

	// Create client
	client := &StreamClient{
		ID:             clientID,
		OrganizationID: organizationID,
		Service:        service,
		Filters:        filters,
		Channel:        make(chan *models.LogEntry, 100),
		Done:           make(chan bool),
		LastSeen:       time.Now(),
	}

	// Store client
	s.clients.Store(clientID, client)

	// Start streaming
	go s.startStreamingForClient(ctx, client)

	log.Info().
		Str("client_id", clientID).
		Interface("organization_id", organizationID).
		Str("service", service).
		Msg("Started log stream")

	return client, nil
}

// StopStream stops a log stream for a client
func (s *StreamService) StopStream(clientID string) {
	if client, exists := s.clients.LoadAndDelete(clientID); exists {
		c := client.(*StreamClient)
		close(c.Done)
		close(c.Channel)
		
		log.Info().Str("client_id", clientID).Msg("Stopped log stream")
	}
}

// GetActiveStreams returns the number of active streams
func (s *StreamService) GetActiveStreams() int {
	count := 0
	s.clients.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// CleanupInactiveStreams removes streams that haven't been active
func (s *StreamService) CleanupInactiveStreams(maxInactivity time.Duration) {
	now := time.Now()
	var toDelete []string

	s.clients.Range(func(key, value interface{}) bool {
		clientID := key.(string)
		client := value.(*StreamClient)
		
		if now.Sub(client.LastSeen) > maxInactivity {
			toDelete = append(toDelete, clientID)
		}
		return true
	})

	for _, clientID := range toDelete {
		s.StopStream(clientID)
		log.Debug().Str("client_id", clientID).Msg("Cleaned up inactive stream")
	}
}

// startStreamingForClient starts streaming logs for a specific client
func (s *StreamService) startStreamingForClient(ctx context.Context, client *StreamClient) {
	// Determine the Redis channel to subscribe to
	channel := s.keyBuilder.StreamChannel(client.OrganizationID, client.Service)
	
	// Subscribe to Redis channel
	messages, err := s.cache.Subscribe(ctx, channel)
	if err != nil {
		log.Error().Err(err).Msg("Failed to subscribe to Redis channel")
		return
	}

	for {
		select {
		case <-client.Done:
			return
		case <-ctx.Done():
			return
		case message, ok := <-messages:
			if !ok {
				return
			}

			// Parse the log entry
			var entry models.LogEntry
			if err := json.Unmarshal(message, &entry); err != nil {
				log.Debug().Err(err).Msg("Failed to unmarshal log entry")
				continue
			}

			// Apply filters
			if s.shouldIncludeEntry(&entry, client.Filters) {
				select {
				case client.Channel <- &entry:
					client.LastSeen = time.Now()
				default:
					// Channel is full, drop the message
					log.Debug().Msg("Stream channel full, dropping message")
				}
			}
		}
	}
}

// shouldIncludeEntry determines if a log entry should be included based on filters
func (s *StreamService) shouldIncludeEntry(entry *models.LogEntry, filters *StreamFilters) bool {
	if filters == nil {
		return true
	}

	// Level filter
	if len(filters.Levels) > 0 {
		found := false
		for _, level := range filters.Levels {
			if entry.Level == level {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Service filter
	if len(filters.Services) > 0 {
		found := false
		for _, service := range filters.Services {
			if entry.Service == service {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Component filter
	if len(filters.Components) > 0 {
		found := false
		for _, component := range filters.Components {
			if entry.Component == component {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Organization filter
	if filters.OrganizationID != nil {
		if entry.OrganizationID == nil || *entry.OrganizationID != *filters.OrganizationID {
			return false
		}
	}

	// Shop filter
	if filters.ShopID != nil {
		if entry.ShopID == nil || *entry.ShopID != *filters.ShopID {
			return false
		}
	}

	// User filter
	if filters.UserID != nil {
		if entry.UserID == nil || *entry.UserID != *filters.UserID {
			return false
		}
	}

	// Message filter (contains check)
	if filters.Message != "" {
		if !contains(entry.Message, filters.Message) {
			return false
		}
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		for _, filterTag := range filters.Tags {
			found := false
			for _, entryTag := range entry.Tags {
				if entryTag == filterTag {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	return true
}

// validateFilters validates streaming filters
func (s *StreamService) validateFilters(filters *StreamFilters) error {
	if filters == nil {
		return nil
	}

	// Validate levels
	validLevels := map[models.LogLevel]bool{
		models.LogLevelDebug: true,
		models.LogLevelInfo:  true,
		models.LogLevelWarn:  true,
		models.LogLevelError: true,
		models.LogLevelFatal: true,
	}

	for _, level := range filters.Levels {
		if !validLevels[level] {
			return fmt.Errorf("invalid log level: %s", level)
		}
	}

	return nil
}

// contains checks if a string contains a substring (case-insensitive)
func contains(str, substr string) bool {
	// Simple case-insensitive contains check
	str = toLower(str)
	substr = toLower(substr)
	
	if len(substr) == 0 {
		return true
	}
	if len(str) == 0 {
		return false
	}
	
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// toLower converts string to lowercase
func toLower(s string) string {
	result := make([]byte, len(s))
	for i, b := range []byte(s) {
		if b >= 'A' && b <= 'Z' {
			result[i] = b + 32
		} else {
			result[i] = b
		}
	}
	return string(result)
}