package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AutoTranslationJob represents a translation job for log messages
type AutoTranslationJob struct {
	ID              uuid.UUID `json:"id"`
	ServiceID       string    `json:"service_id"`
	LogLevel        string    `json:"log_level"`
	SourceText      string    `json:"source_text"`
	TargetLanguage  string    `json:"target_language"`
	Namespace       string    `json:"namespace"`
	TranslationKey  string    `json:"translation_key"`
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	CreatedAt       time.Time `json:"created_at"`
}

// AutoTranslationResult represents the result of a translation job
type AutoTranslationResult struct {
	JobID          uuid.UUID `json:"job_id"`
	TranslatedText string    `json:"translated_text"`
	Confidence     float64   `json:"confidence"`
	AutoTranslated bool      `json:"auto_translated"`
	Success        bool      `json:"success"`
	Error          string    `json:"error,omitempty"`
	ProcessedAt    time.Time `json:"processed_at"`
}

// AutoTranslationProcessor handles background translation processing for log messages
type AutoTranslationProcessor struct {
	multiLangClient *MultiLangClient
	workers         int
	isRunning       bool
	jobQueue        chan *AutoTranslationJob
	resultQueue     chan *AutoTranslationResult
	logger          *logrus.Logger
	wg              sync.WaitGroup
	ctx             context.Context
	cancel          context.CancelFunc
}

// NewAutoTranslationProcessor creates a new auto-translation processor
func NewAutoTranslationProcessor(multiLangClient *MultiLangClient, workers int, logger *logrus.Logger) *AutoTranslationProcessor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &AutoTranslationProcessor{
		multiLangClient: multiLangClient,
		workers:         workers,
		isRunning:       false,
		jobQueue:        make(chan *AutoTranslationJob, 1000),
		resultQueue:     make(chan *AutoTranslationResult, 1000),
		logger:          logger,
		ctx:             ctx,
		cancel:          cancel,
	}
}

// Start starts the auto-translation processor workers
func (p *AutoTranslationProcessor) Start() error {
	if p.isRunning {
		return fmt.Errorf("processor is already running")
	}
	
	p.isRunning = true
	p.logger.Infof("Starting auto-translation processor with %d workers", p.workers)
	
	// Start worker goroutines
	for i := 0; i < p.workers; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}
	
	// Start result processor
	p.wg.Add(1)
	go p.resultProcessor()
	
	p.logger.Info("Auto-translation processor started successfully")
	return nil
}

// Stop stops the auto-translation processor
func (p *AutoTranslationProcessor) Stop() error {
	if !p.isRunning {
		return fmt.Errorf("processor is not running")
	}
	
	p.logger.Info("Stopping auto-translation processor...")
	
	p.isRunning = false
	p.cancel()
	
	// Close job queue to signal workers to stop
	close(p.jobQueue)
	
	// Wait for all workers to finish
	p.wg.Wait()
	
	close(p.resultQueue)
	
	p.logger.Info("Auto-translation processor stopped")
	return nil
}

// SubmitJob submits a translation job to the processor
func (p *AutoTranslationProcessor) SubmitJob(job *AutoTranslationJob) error {
	if !p.isRunning {
		return fmt.Errorf("processor is not running")
	}
	
	select {
	case p.jobQueue <- job:
		p.logger.Debugf("Submitted translation job %s for log level %s", job.ID, job.LogLevel)
		return nil
	case <-p.ctx.Done():
		return fmt.Errorf("processor is shutting down")
	default:
		return fmt.Errorf("job queue is full")
	}
}

// ProcessLogTranslation processes a single log message translation
func (p *AutoTranslationProcessor) ProcessLogTranslation(logLevel, message, targetLanguage string, confidenceThreshold float64) (*AutoTranslationResult, error) {
	job := &AutoTranslationJob{
		ID:                  uuid.New(),
		ServiceID:           "adc-log-service",
		LogLevel:            logLevel,
		SourceText:          message,
		TargetLanguage:      targetLanguage,
		Namespace:           fmt.Sprintf("logs_%s", logLevel),
		TranslationKey:      fmt.Sprintf("message_%d", hashString(message)),
		ConfidenceThreshold: confidenceThreshold,
		CreatedAt:          time.Now(),
	}
	
	return p.processTranslationJob(job), nil
}

// worker processes translation jobs from the queue
func (p *AutoTranslationProcessor) worker(workerID int) {
	defer p.wg.Done()
	
	p.logger.Debugf("Auto-translation worker %d started", workerID)
	
	for {
		select {
		case job, ok := <-p.jobQueue:
			if !ok {
				p.logger.Debugf("Worker %d: job queue closed, exiting", workerID)
				return
			}
			
			result := p.processTranslationJob(job)
			
			select {
			case p.resultQueue <- result:
				// Result submitted successfully
			case <-p.ctx.Done():
				p.logger.Debugf("Worker %d: context cancelled, exiting", workerID)
				return
			}
			
		case <-p.ctx.Done():
			p.logger.Debugf("Worker %d: context cancelled, exiting", workerID)
			return
		}
	}
}

// processTranslationJob processes a single translation job
func (p *AutoTranslationProcessor) processTranslationJob(job *AutoTranslationJob) *AutoTranslationResult {
	startTime := time.Now()
	
	result := &AutoTranslationResult{
		JobID:       job.ID,
		ProcessedAt: time.Now(),
	}
	
	// Create translation context with timeout
	ctx, cancel := context.WithTimeout(p.ctx, 30*time.Second)
	defer cancel()
	
	// Call Multi-Languages service for translation
	translatedText, autoTranslated, err := p.multiLangClient.TranslateLogMessage(
		ctx,
		job.LogLevel,
		job.SourceText,
		job.TargetLanguage,
	)
	
	if err != nil {
		result.Success = false
		result.Error = err.Error()
		result.TranslatedText = job.SourceText // Fallback to original text
		result.Confidence = 0.0
		result.AutoTranslated = false
		
		p.logger.Errorf("Translation job %s failed: %v", job.ID, err)
		return result
	}
	
	// Calculate confidence score
	confidence := 0.8 // Default confidence
	if autoTranslated && translatedText != job.SourceText {
		confidence = 0.9
	} else if !autoTranslated {
		confidence = 1.0 // Human translation
	}
	
	result.Success = true
	result.TranslatedText = translatedText
	result.Confidence = confidence
	result.AutoTranslated = autoTranslated
	
	duration := time.Since(startTime)
	p.logger.Debugf("Translation job %s completed in %v (confidence: %.2f, auto: %v)", 
		job.ID, duration, confidence, autoTranslated)
	
	return result
}

// resultProcessor handles translation results
func (p *AutoTranslationProcessor) resultProcessor() {
	defer p.wg.Done()
	
	p.logger.Debug("Translation result processor started")
	
	for {
		select {
		case result, ok := <-p.resultQueue:
			if !ok {
				p.logger.Debug("Result queue closed, result processor exiting")
				return
			}
			
			// Process the result (could store in cache, database, etc.)
			p.handleTranslationResult(result)
			
		case <-p.ctx.Done():
			p.logger.Debug("Result processor context cancelled, exiting")
			return
		}
	}
}

// handleTranslationResult handles a translation result
func (p *AutoTranslationProcessor) handleTranslationResult(result *AutoTranslationResult) {
	if result.Success {
		p.logger.Debugf("Translation result %s: '%s' (confidence: %.2f)", 
			result.JobID, result.TranslatedText, result.Confidence)
	} else {
		p.logger.Warnf("Translation result %s failed: %s", result.JobID, result.Error)
	}
	
	// Here you could:
	// - Store results in cache for faster future lookups
	// - Update translation statistics
	// - Trigger notifications for low-confidence translations
	// - Log translation metrics
}

// GetQueueStatus returns the current status of job and result queues
func (p *AutoTranslationProcessor) GetQueueStatus() map[string]interface{} {
	return map[string]interface{}{
		"running":          p.isRunning,
		"workers":          p.workers,
		"job_queue_length": len(p.jobQueue),
		"result_queue_length": len(p.resultQueue),
	}
}