package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/adc-log-service/internal/models"
	"github.com/adc-log-service/internal/storage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// LogService handles log ingestion and management
type LogService struct {
	storage storage.Storage
	cache   storage.Cache
	keyBuilder *storage.CacheKeyBuilder
}

// NewLogService creates a new log service instance
func NewLogService(st storage.Storage, cache storage.Cache) *LogService {
	return &LogService{
		storage:    st,
		cache:      cache,
		keyBuilder: storage.NewCacheKeyBuilder("adc_logs:"),
	}
}

// IngestLog stores a single log entry
func (s *LogService) IngestLog(ctx context.Context, request *models.LogIngestRequest) error {
	// Validate the request
	if err := request.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Convert to log entry
	entry := request.ToLogEntry()

	// Store in database
	if err := s.storage.Store(ctx, entry); err != nil {
		log.Error().Err(err).Msg("Failed to store log entry")
		return fmt.Errorf("failed to store log: %w", err)
	}

	// Publish to real-time stream (best effort)
	go s.publishToStream(context.Background(), entry)

	// Invalidate relevant caches (best effort)
	go s.invalidateStatsCache(context.Background(), entry)

	return nil
}

// IngestBatch stores multiple log entries in batch
func (s *LogService) IngestBatch(ctx context.Context, request *models.LogBatchRequest) error {
	if len(request.Logs) == 0 {
		return nil
	}

	// Validate all requests
	entries := make([]*models.LogEntry, 0, len(request.Logs))
	for i, logReq := range request.Logs {
		if err := logReq.Validate(); err != nil {
			return fmt.Errorf("validation failed for log %d: %w", i, err)
		}
		entries = append(entries, logReq.ToLogEntry())
	}

	// Store batch in database
	if err := s.storage.StoreBatch(ctx, entries); err != nil {
		log.Error().Err(err).Msg("Failed to store log batch")
		return fmt.Errorf("failed to store log batch: %w", err)
	}

	// Publish to real-time streams (best effort)
	go s.publishBatchToStream(context.Background(), entries)

	// Invalidate relevant caches (best effort)
	go s.invalidateBatchStatsCache(context.Background(), entries)

	return nil
}

// TriggerCleanup manually triggers log cleanup based on retention policies
func (s *LogService) TriggerCleanup(ctx context.Context, organizationID *uuid.UUID) (*CleanupResult, error) {
	// Get retention policies
	var policies []models.RetentionPolicy
	var err error

	if organizationID != nil {
		policies, err = s.storage.GetRetentionPolicies(ctx, *organizationID)
	} else {
		// Global cleanup - this would need to be implemented to get all policies
		policies = []models.RetentionPolicy{}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get retention policies: %w", err)
	}

	// Perform cleanup
	deletedCount, err := s.storage.Cleanup(ctx, policies)
	if err != nil {
		return nil, fmt.Errorf("cleanup failed: %w", err)
	}

	// Invalidate stats cache after cleanup
	go s.invalidateAllStatsCache(context.Background(), organizationID)

	return &CleanupResult{
		DeletedEntries: deletedCount,
		ExecutedAt:     time.Now(),
	}, nil
}

// GetRetentionPolicies retrieves retention policies for an organization
func (s *LogService) GetRetentionPolicies(ctx context.Context, organizationID uuid.UUID) ([]models.RetentionPolicy, error) {
	// Try cache first
	cacheKey := s.keyBuilder.RetentionKey(organizationID)
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && cached != nil {
		var policies []models.RetentionPolicy
		if err := json.Unmarshal(cached, &policies); err == nil {
			return policies, nil
		}
	}

	// Fetch from storage
	policies, err := s.storage.GetRetentionPolicies(ctx, organizationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get retention policies: %w", err)
	}

	// Cache the result (best effort)
	go func() {
		if data, err := json.Marshal(policies); err == nil {
			s.cache.Set(context.Background(), cacheKey, data, 15*time.Minute)
		}
	}()

	return policies, nil
}

// UpdateRetentionPolicy updates a retention policy
func (s *LogService) UpdateRetentionPolicy(ctx context.Context, policy *models.RetentionPolicy) error {
	if err := s.storage.UpdateRetentionPolicy(ctx, policy); err != nil {
		return fmt.Errorf("failed to update retention policy: %w", err)
	}

	// Invalidate cache
	cacheKey := s.keyBuilder.RetentionKey(policy.OrganizationID)
	go s.cache.Delete(context.Background(), cacheKey)

	return nil
}

// publishToStream publishes a log entry to real-time stream
func (s *LogService) publishToStream(ctx context.Context, entry *models.LogEntry) {
	channel := s.keyBuilder.StreamChannel(entry.OrganizationID, entry.Service)
	
	if data, err := json.Marshal(entry); err == nil {
		if err := s.cache.Publish(ctx, channel, data); err != nil {
			log.Debug().Err(err).Msg("Failed to publish to stream")
		}
	}
}

// publishBatchToStream publishes multiple log entries to real-time streams
func (s *LogService) publishBatchToStream(ctx context.Context, entries []*models.LogEntry) {
	// Group by service for efficient publishing
	serviceGroups := make(map[string][]*models.LogEntry)
	for _, entry := range entries {
		key := entry.Service
		if entry.OrganizationID != nil {
			key = entry.OrganizationID.String() + ":" + entry.Service
		}
		serviceGroups[key] = append(serviceGroups[key], entry)
	}

	// Publish each group
	for _, group := range serviceGroups {
		if len(group) > 0 {
			entry := group[0] // Use first entry for channel determination
			channel := s.keyBuilder.StreamChannel(entry.OrganizationID, entry.Service)
			
			for _, e := range group {
				if data, err := json.Marshal(e); err == nil {
					if err := s.cache.Publish(ctx, channel, data); err != nil {
						log.Debug().Err(err).Msg("Failed to publish to stream")
					}
				}
			}
		}
	}
}

// invalidateStatsCache invalidates stats cache for affected keys
func (s *LogService) invalidateStatsCache(ctx context.Context, entry *models.LogEntry) {
	// Invalidate daily stats
	today := time.Now().Truncate(24 * time.Hour)
	cacheKey := s.keyBuilder.StatsKey(entry.OrganizationID, today, today.Add(24*time.Hour))
	s.cache.Delete(ctx, cacheKey)
	
	// Invalidate weekly stats
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	weekCacheKey := s.keyBuilder.StatsKey(entry.OrganizationID, weekStart, weekStart.Add(7*24*time.Hour))
	s.cache.Delete(ctx, weekCacheKey)
}

// invalidateBatchStatsCache invalidates stats cache for a batch of entries
func (s *LogService) invalidateBatchStatsCache(ctx context.Context, entries []*models.LogEntry) {
	// Find unique organization IDs
	orgIDs := make(map[*uuid.UUID]bool)
	for _, entry := range entries {
		orgIDs[entry.OrganizationID] = true
	}

	// Invalidate for each organization
	for orgID := range orgIDs {
		today := time.Now().Truncate(24 * time.Hour)
		cacheKey := s.keyBuilder.StatsKey(orgID, today, today.Add(24*time.Hour))
		s.cache.Delete(ctx, cacheKey)
	}
}

// invalidateAllStatsCache invalidates all stats cache for an organization
func (s *LogService) invalidateAllStatsCache(ctx context.Context, organizationID *uuid.UUID) {
	// This is a simplified implementation
	// In production, you'd want a more sophisticated cache invalidation strategy
	today := time.Now().Truncate(24 * time.Hour)
	for i := 0; i < 30; i++ { // Invalidate last 30 days
		date := today.AddDate(0, 0, -i)
		cacheKey := s.keyBuilder.StatsKey(organizationID, date, date.Add(24*time.Hour))
		s.cache.Delete(ctx, cacheKey)
	}
}

// CleanupResult represents the result of a cleanup operation
type CleanupResult struct {
	DeletedEntries int64     `json:"deleted_entries"`
	ExecutedAt     time.Time `json:"executed_at"`
}