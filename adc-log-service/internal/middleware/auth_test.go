package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-log-service/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestSSOAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing Authorization Header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Authorization header required",
		},
		{
			name:           "Invalid Token Format - No Bearer",
			authHeader:     "InvalidToken",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid token format, Bearer token required",
		},
		{
			name:           "Invalid Token Format - Bearer Only",
			authHeader:     "Bearer",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid token format, Bearer token required",
		},
		{
			name:           "Empty Token",
			authHeader:     "Bearer ",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Token is required",
		},
		{
			name:           "Valid Token Format (Development Mode)",
			authHeader:     "Bearer valid-token-123",
			expectedStatus: http.StatusOK,
			expectedError:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create middleware
			middleware := NewSSOAuth("http://localhost:9000", "test-secret")
			
			// Create test router
			router := gin.New()
			router.Use(middleware.SSOAuth())
			router.GET("/test", func(c *gin.Context) {
				// Verify user context is set for successful auth
				if tt.expectedStatus == http.StatusOK {
					user, exists := GetAuthUser(c)
					assert.True(t, exists, "User should be set in context")
					assert.NotNil(t, user, "User should not be nil")
					assert.NotEqual(t, uuid.Nil, user.ID, "User ID should be set")
					assert.Equal(t, "<EMAIL>", user.Email, "User email should be set")
					
					userID, exists := GetUserID(c)
					assert.True(t, exists, "User ID should be retrievable")
					assert.NotEqual(t, uuid.Nil, userID, "User ID should not be nil")
					
					authType, exists := c.Get("auth_type")
					assert.True(t, exists, "Auth type should be set")
					assert.Equal(t, "development", authType, "Auth type should be development")
				}
				
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// Create request
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			// Record response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestAPIKeyAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		InternalAPIKeys: []string{"valid-key-123", "another-valid-key"},
	}

	tests := []struct {
		name           string
		headers        map[string]string
		queryParams    map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing API Key",
			headers:        map[string]string{},
			queryParams:    map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "API key required",
		},
		{
			name: "Valid API Key in X-API-Key Header",
			headers: map[string]string{
				"X-API-Key": "valid-key-123",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Valid API Key in Authorization Header",
			headers: map[string]string{
				"Authorization": "Bearer valid-key-123",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "Valid API Key in Query Parameter",
			queryParams: map[string]string{"api_key": "another-valid-key"},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Invalid API Key",
			headers: map[string]string{
				"X-API-Key": "invalid-key",
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid API key",
		},
		{
			name: "Empty API Key in Header",
			headers: map[string]string{
				"X-API-Key": "",
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "API key required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create middleware
			middleware := NewAPIKeyAuth(cfg)
			
			// Create test router
			router := gin.New()
			router.Use(middleware.APIKeyAuth())
			router.GET("/test", func(c *gin.Context) {
				// Verify API key context is set for successful auth
				if tt.expectedStatus == http.StatusOK {
					apiKey, exists := c.Get("api_key")
					assert.True(t, exists, "API key should be set in context")
					assert.NotEmpty(t, apiKey, "API key should not be empty")
					
					authType, exists := c.Get("auth_type")
					assert.True(t, exists, "Auth type should be set")
					assert.Equal(t, "api_key", authType, "Auth type should be api_key")
				}
				
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// Build URL with query parameters
			url := "/test"
			if len(tt.queryParams) > 0 {
				url += "?"
				for key, value := range tt.queryParams {
					url += key + "=" + value
				}
			}

			// Create request
			req := httptest.NewRequest("GET", url, nil)
			
			// Set headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Record response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestAPIKeyValidation(t *testing.T) {
	cfg := &config.Config{
		InternalAPIKeys: []string{"key1", "key2", "key3"},
	}

	middleware := NewAPIKeyAuth(cfg)

	tests := []struct {
		name     string
		apiKey   string
		expected bool
	}{
		{
			name:     "Valid Key 1",
			apiKey:   "key1",
			expected: true,
		},
		{
			name:     "Valid Key 2",
			apiKey:   "key2",
			expected: true,
		},
		{
			name:     "Valid Key 3",
			apiKey:   "key3",
			expected: true,
		},
		{
			name:     "Invalid Key",
			apiKey:   "invalid-key",
			expected: false,
		},
		{
			name:     "Empty Key",
			apiKey:   "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := middleware.isValidAPIKey(tt.apiKey)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAPIKeyPrefix(t *testing.T) {
	tests := []struct {
		name     string
		apiKey   string
		expected string
	}{
		{
			name:     "Short Key",
			apiKey:   "short",
			expected: "short",
		},
		{
			name:     "Exactly 8 Characters",
			apiKey:   "12345678",
			expected: "12345678",
		},
		{
			name:     "Long Key",
			apiKey:   "very-long-api-key-that-should-be-truncated",
			expected: "very-lon...",
		},
		{
			name:     "Empty Key",
			apiKey:   "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getAPIKeyPrefix(tt.apiKey)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvenienceFunctions(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("SSOAuth Function", func(t *testing.T) {
		handler := SSOAuth("http://localhost:9000")
		assert.NotNil(t, handler, "SSOAuth should return a handler function")
	})

	t.Run("APIKeyAuth Function", func(t *testing.T) {
		cfg := &config.Config{
			InternalAPIKeys: []string{"test-key"},
		}
		handler := APIKeyAuth(cfg)
		assert.NotNil(t, handler, "APIKeyAuth should return a handler function")
	})
}

func TestAuthUserHelpers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("GetAuthUser with Valid User", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			// Set mock user
			userID := uuid.New()
			user := &AuthUser{
				ID:       userID,
				Email:    "<EMAIL>",
				Username: "testuser",
				FullName: "Test User",
				Role:     "admin",
			}
			c.Set("user", user)

			// Test GetAuthUser
			retrievedUser, exists := GetAuthUser(c)
			assert.True(t, exists, "User should exist")
			assert.Equal(t, user, retrievedUser, "Retrieved user should match set user")

			c.JSON(http.StatusOK, gin.H{"success": true})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetAuthUser Fallback to UserID", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			// Set only user_id
			userID := uuid.New()
			c.Set("user_id", userID)

			// Test GetAuthUser fallback
			retrievedUser, exists := GetAuthUser(c)
			assert.True(t, exists, "User should exist via fallback")
			assert.Equal(t, userID, retrievedUser.ID, "User ID should match")

			c.JSON(http.StatusOK, gin.H{"success": true})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetAuthUser No User", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			// Don't set any user context

			// Test GetAuthUser
			retrievedUser, exists := GetAuthUser(c)
			assert.False(t, exists, "User should not exist")
			assert.Nil(t, retrievedUser, "Retrieved user should be nil")

			c.JSON(http.StatusOK, gin.H{"success": true})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetUserID", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			// Set user_id
			expectedID := uuid.New()
			c.Set("user_id", expectedID)

			// Test GetUserID
			retrievedID, exists := GetUserID(c)
			assert.True(t, exists, "User ID should exist")
			assert.Equal(t, expectedID, retrievedID, "User ID should match")

			c.JSON(http.StatusOK, gin.H{"success": true})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetUserID No User", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			// Don't set user_id

			// Test GetUserID
			retrievedID, exists := GetUserID(c)
			assert.False(t, exists, "User ID should not exist")
			assert.Equal(t, uuid.Nil, retrievedID, "User ID should be nil UUID")

			c.JSON(http.StatusOK, gin.H{"success": true})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})
}

func TestNilConfigHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("APIKeyAuth with Nil Config", func(t *testing.T) {
		middleware := NewAPIKeyAuth(nil)
		
		// Test that nil config is handled gracefully
		assert.False(t, middleware.isValidAPIKey("any-key"), "Should return false for nil config")
		
		// Test middleware with nil config
		router := gin.New()
		router.Use(middleware.APIKeyAuth())
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-API-Key", "any-key")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid API key")
	})

	t.Run("APIKeyAuth with Empty Internal Keys", func(t *testing.T) {
		cfg := &config.Config{
			InternalAPIKeys: []string{},
		}
		
		middleware := NewAPIKeyAuth(cfg)
		
		// Test that empty keys list returns false
		assert.False(t, middleware.isValidAPIKey("any-key"), "Should return false for empty keys list")
	})
}