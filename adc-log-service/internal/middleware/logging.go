package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// Logging returns a gin.HandlerFunc for logging HTTP requests
func Logging() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Extract correlation ID from context
		correlationID := param.Keys["correlation_id"]
		if correlationID == nil {
			correlationID = "unknown"
		}

		// Extract user ID from context
		userID := param.Keys["user_id"]
		if userID == nil {
			userID = "anonymous"
		}

		// Log the request using structured logging
		log.Info().
			Time("timestamp", param.TimeStamp).
			Int("status", param.StatusCode).
			Dur("latency", param.Latency).
			Str("client_ip", param.ClientIP).
			Str("method", param.Method).
			Str("path", param.Path).
			Interface("correlation_id", correlationID).
			Interface("user_id", userID).
			Str("user_agent", param.Request.UserAgent()).
			Str("error_message", param.ErrorMessage).
			Msg("HTTP Request")

		return "" // We handle logging with zerolog
	})
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if correlation ID already exists in headers
		correlationID := c.GetHeader("X-Correlation-ID")
		if correlationID == "" {
			correlationID = c.GetHeader("X-Request-ID")
		}

		// Generate new correlation ID if not provided
		if correlationID == "" {
			correlationID = uuid.New().String()
		}

		// Set correlation ID in context and response headers
		c.Set("correlation_id", correlationID)
		c.Header("X-Correlation-ID", correlationID)
		c.Header("X-Request-ID", correlationID)

		c.Next()
	}
}

// Recovery returns a gin.HandlerFunc for recovering from panics
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		correlationID, _ := c.Get("correlation_id")
		userID, _ := c.Get("user_id")

		log.Error().
			Interface("correlation_id", correlationID).
			Interface("user_id", userID).
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Interface("panic", recovered).
			Msg("Panic recovered")

		c.JSON(500, gin.H{
			"error": "Internal server error",
			"correlation_id": correlationID,
		})
	})
}
