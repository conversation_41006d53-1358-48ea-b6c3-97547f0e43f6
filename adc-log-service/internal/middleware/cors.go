package middleware

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"time"
)

// CORS returns a CORS middleware with appropriate settings for the log service
func CORS() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowOrigins: []string{
			"http://localhost:3000",  // ADC SSO Service frontend
			"http://localhost:3300",  // Multi-Languages Service frontend
			"http://localhost:3800",  // Credit Service frontend
			"http://localhost:9000",  // SSO Service
			"http://localhost:9100",  // Subscription Service
			"https://*.adc-platform.com", // Production domains
		},
		AllowMethods: []string{
			"GET",
			"POST",
			"PUT",
			"PATCH",
			"DELETE",
			"OPTIONS",
		},
		AllowHeaders: []string{
			"Origin",
			"Content-Type",
			"Content-Length",
			"Accept-Encoding",
			"X-CSRF-Token",
			"Authorization",
			"Accept",
			"Cache-Control",
			"X-Requested-With",
			"X-API-Key",
			"X-Correlation-ID",
			"X-Request-ID",
		},
		ExposeHeaders: []string{
			"Content-Length",
			"X-Total-Count",
			"X-Has-More",
			"X-Search-Time",
		},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	})
}
