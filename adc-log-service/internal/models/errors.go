package models

import (
	"errors"
	"fmt"
)

// Common errors for the log service
var (
	// Validation errors
	ErrInvalidLogLevel   = errors.New("invalid log level")
	ErrInvalidLogMessage = errors.New("log message cannot be empty")
	ErrInvalidLogService = errors.New("log service cannot be empty")
	ErrInvalidTimeRange  = errors.New("invalid time range")
	ErrInvalidSearchQuery = errors.New("invalid search query")
	
	// Authorization errors
	ErrUnauthorized      = errors.New("unauthorized access")
	ErrForbidden         = errors.New("forbidden access")
	ErrInvalidAPIKey     = errors.New("invalid API key")
	
	// Storage errors
	ErrLogNotFound       = errors.New("log entry not found")
	ErrStorageFailure    = errors.New("storage operation failed")
	ErrCacheFailure      = errors.New("cache operation failed")
	ErrDatabaseFailure   = errors.New("database operation failed")
	
	// Service errors
	ErrServiceUnavailable = errors.New("service temporarily unavailable")
	ErrRateLimitExceeded  = errors.New("rate limit exceeded")
	ErrQuotaExceeded      = errors.New("quota exceeded")
	ErrBatchTooLarge      = errors.New("batch size too large")
	
	// Configuration errors
	ErrInvalidConfiguration = errors.New("invalid configuration")
	ErrMissingConfiguration = errors.New("missing required configuration")
)

// LogServiceError wraps errors with additional context
type LogServiceError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Cause   error  `json:"-"`
}

func (e *LogServiceError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *LogServiceError) Unwrap() error {
	return e.Cause
}

// NewLogServiceError creates a new service error
func NewLogServiceError(code, message string, cause error) *LogServiceError {
	return &LogServiceError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// Error codes
const (
	ErrCodeValidation     = "VALIDATION_ERROR"
	ErrCodeUnauthorized   = "UNAUTHORIZED"
	ErrCodeForbidden      = "FORBIDDEN"
	ErrCodeNotFound       = "NOT_FOUND"
	ErrCodeStorage        = "STORAGE_ERROR"
	ErrCodeService        = "SERVICE_ERROR"
	ErrCodeRateLimit      = "RATE_LIMIT_EXCEEDED"
	ErrCodeQuota          = "QUOTA_EXCEEDED"
	ErrCodeConfiguration  = "CONFIGURATION_ERROR"
)