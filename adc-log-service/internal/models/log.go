package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// LogLevel represents the severity level of a log entry
type LogLevel string

const (
	LogLevelDebug LogLevel = "DEBUG"
	LogLevelInfo  LogLevel = "INFO"
	LogLevelWarn  LogLevel = "WARN"
	LogLevelError LogLevel = "ERROR"
	LogLevelFatal LogLevel = "FATAL"
)

// LogEntry represents a single log entry in the system
type LogEntry struct {
	ID              uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Timestamp       time.Time              `json:"timestamp" gorm:"index"`
	Level           LogLevel               `json:"level" gorm:"index"`
	Message         string                 `json:"message"`
	Service         string                 `json:"service" gorm:"index"`
	Component       string                 `json:"component"`
	
	// Multi-tenant fields
	OrganizationID  *uuid.UUID             `json:"organization_id,omitempty" gorm:"type:uuid;index"`
	ShopID          *uuid.UUID             `json:"shop_id,omitempty" gorm:"type:uuid;index"`
	UserID          *uuid.UUID             `json:"user_id,omitempty" gorm:"type:uuid;index"`
	
	// Request context
	CorrelationID   string                 `json:"correlation_id,omitempty" gorm:"index"`
	RequestID       string                 `json:"request_id,omitempty"`
	SessionID       string                 `json:"session_id,omitempty"`
	
	// Additional context
	Fields          map[string]interface{} `json:"fields,omitempty" gorm:"type:jsonb"`
	Tags            []string               `json:"tags,omitempty" gorm:"type:text[]"`
	
	// Error context (for error level logs)
	ErrorCode       string                 `json:"error_code,omitempty"`
	ErrorStack      string                 `json:"error_stack,omitempty"`
	
	// Performance metrics
	Duration        *int64                 `json:"duration,omitempty"` // in milliseconds
	MemoryUsage     *int64                 `json:"memory_usage,omitempty"` // in bytes
	
	// Source information
	Source          LogSource              `json:"source"`
	
	CreatedAt       time.Time              `json:"created_at"`
}

// LogSource contains information about where the log originated
type LogSource struct {
	Hostname     string `json:"hostname,omitempty"`
	IP           string `json:"ip,omitempty"`
	Environment  string `json:"environment,omitempty"`
	Version      string `json:"version,omitempty"`
	BuildCommit  string `json:"build_commit,omitempty"`
}

// LogSearchQuery represents parameters for searching logs
type LogSearchQuery struct {
	// Time range
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	
	// Filtering
	Levels         []LogLevel `json:"levels,omitempty"`
	Services       []string   `json:"services,omitempty"`
	Components     []string   `json:"components,omitempty"`
	
	// Multi-tenant filtering
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID `json:"shop_id,omitempty"`
	UserID         *uuid.UUID `json:"user_id,omitempty"`
	
	// Request context filtering
	CorrelationID  string `json:"correlation_id,omitempty"`
	RequestID      string `json:"request_id,omitempty"`
	SessionID      string `json:"session_id,omitempty"`
	
	// Content filtering
	Message        string   `json:"message,omitempty"`        // Full-text search
	Tags           []string `json:"tags,omitempty"`
	ErrorCode      string   `json:"error_code,omitempty"`
	
	// Pagination and sorting
	Limit          int    `json:"limit,omitempty"`
	Offset         int    `json:"offset,omitempty"`
	SortBy         string `json:"sort_by,omitempty"`         // "timestamp", "level", "service"
	SortOrder      string `json:"sort_order,omitempty"`      // "asc", "desc"
}

// LogSearchResult represents the result of a log search
type LogSearchResult struct {
	Logs       []LogEntry `json:"logs"`
	Total      int64      `json:"total"`
	Limit      int        `json:"limit"`
	Offset     int        `json:"offset"`
	HasMore    bool       `json:"has_more"`
	SearchTime int64      `json:"search_time"` // in milliseconds
}

// LogStats represents statistics about logs
type LogStats struct {
	TotalLogs      int64                    `json:"total_logs"`
	LogsByLevel    map[LogLevel]int64       `json:"logs_by_level"`
	LogsByService  map[string]int64         `json:"logs_by_service"`
	TimeRange      LogTimeRange             `json:"time_range"`
	ErrorRate      float64                  `json:"error_rate"`       // Percentage of error/fatal logs
	TopErrors      []LogErrorSummary        `json:"top_errors"`
	RecentActivity []LogActivitySummary     `json:"recent_activity"`
}

// LogTimeRange represents the time range covered by logs
type LogTimeRange struct {
	Earliest time.Time `json:"earliest"`
	Latest   time.Time `json:"latest"`
}

// LogErrorSummary represents a summary of a specific error
type LogErrorSummary struct {
	ErrorCode string `json:"error_code"`
	Message   string `json:"message"`
	Count     int64  `json:"count"`
	LastSeen  time.Time `json:"last_seen"`
}

// LogActivitySummary represents log activity over time
type LogActivitySummary struct {
	Timestamp time.Time `json:"timestamp"`
	Count     int64     `json:"count"`
	Level     LogLevel  `json:"level"`
}

// RetentionPolicy represents log retention configuration
type RetentionPolicy struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID  uuid.UUID `json:"organization_id" gorm:"type:uuid;index"`
	Service         string    `json:"service"`
	Level           LogLevel  `json:"level"`
	RetentionDays   int       `json:"retention_days"`
	IsDefault       bool      `json:"is_default"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// LogBatchRequest represents a batch of logs to be ingested
type LogBatchRequest struct {
	Logs []LogIngestRequest `json:"logs"`
}

// LogIngestRequest represents a single log to be ingested
type LogIngestRequest struct {
	Timestamp       *time.Time             `json:"timestamp,omitempty"`
	Level           LogLevel               `json:"level"`
	Message         string                 `json:"message"`
	Service         string                 `json:"service"`
	Component       string                 `json:"component,omitempty"`
	
	// Multi-tenant fields
	OrganizationID  *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID          *uuid.UUID             `json:"shop_id,omitempty"`
	UserID          *uuid.UUID             `json:"user_id,omitempty"`
	
	// Request context
	CorrelationID   string                 `json:"correlation_id,omitempty"`
	RequestID       string                 `json:"request_id,omitempty"`
	SessionID       string                 `json:"session_id,omitempty"`
	
	// Additional context
	Fields          map[string]interface{} `json:"fields,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	
	// Error context
	ErrorCode       string                 `json:"error_code,omitempty"`
	ErrorStack      string                 `json:"error_stack,omitempty"`
	
	// Performance metrics
	Duration        *int64                 `json:"duration,omitempty"`
	MemoryUsage     *int64                 `json:"memory_usage,omitempty"`
	
	// Source information
	Source          *LogSource             `json:"source,omitempty"`
}

// ToLogEntry converts a LogIngestRequest to a LogEntry
func (r *LogIngestRequest) ToLogEntry() *LogEntry {
	entry := &LogEntry{
		ID:             uuid.New(),
		Level:          r.Level,
		Message:        r.Message,
		Service:        r.Service,
		Component:      r.Component,
		OrganizationID: r.OrganizationID,
		ShopID:         r.ShopID,
		UserID:         r.UserID,
		CorrelationID:  r.CorrelationID,
		RequestID:      r.RequestID,
		SessionID:      r.SessionID,
		Fields:         r.Fields,
		Tags:           r.Tags,
		ErrorCode:      r.ErrorCode,
		ErrorStack:     r.ErrorStack,
		Duration:       r.Duration,
		MemoryUsage:    r.MemoryUsage,
		CreatedAt:      time.Now(),
	}

	if r.Timestamp != nil {
		entry.Timestamp = *r.Timestamp
	} else {
		entry.Timestamp = time.Now()
	}

	if r.Source != nil {
		entry.Source = *r.Source
	}

	return entry
}

// Validate checks if the log entry is valid
func (r *LogIngestRequest) Validate() error {
	if r.Level == "" {
		return ErrInvalidLogLevel
	}
	
	if r.Message == "" {
		return ErrInvalidLogMessage
	}
	
	if r.Service == "" {
		return ErrInvalidLogService
	}

	// Validate log level
	validLevels := map[LogLevel]bool{
		LogLevelDebug: true,
		LogLevelInfo:  true,
		LogLevelWarn:  true,
		LogLevelError: true,
		LogLevelFatal: true,
	}
	
	if !validLevels[r.Level] {
		return ErrInvalidLogLevel
	}

	return nil
}

// MarshalJSON custom JSON marshaling for Fields
func (e *LogEntry) MarshalJSON() ([]byte, error) {
	type Alias LogEntry
	return json.Marshal(&struct {
		*Alias
		Fields string `json:"fields"`
	}{
		Alias:  (*Alias)(e),
		Fields: fieldsToString(e.Fields),
	})
}

func fieldsToString(fields map[string]interface{}) string {
	if fields == nil {
		return "{}"
	}
	
	b, err := json.Marshal(fields)
	if err != nil {
		return "{}"
	}
	
	return string(b)
}