package models

import (
	"time"

	"github.com/google/uuid"
)

// AutoTranslationSettings represents auto-translation configuration for log services
type AutoTranslationSettings struct {
	ID                  uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ServiceID           string    `json:"service_id" gorm:"not null;index"`
	UserID              *string   `json:"user_id,omitempty" gorm:"index"`
	OrganizationID      *string   `json:"organization_id,omitempty" gorm:"index"`
	
	// Translation settings
	Enabled             bool     `json:"enabled" gorm:"default:false"`
	TargetLanguage      string   `json:"target_language" gorm:"default:'en'"`
	ConfidenceThreshold float64  `json:"confidence_threshold" gorm:"default:0.8"`
	
	// Log-specific settings
	LogLevels           []string `json:"log_levels" gorm:"type:text[]"`
	TranslateErrors     bool     `json:"translate_errors" gorm:"default:true"`
	TranslateWarnings   bool     `json:"translate_warnings" gorm:"default:true"`
	TranslateInfo       bool     `json:"translate_info" gorm:"default:false"`
	TranslateDebug      bool     `json:"translate_debug" gorm:"default:false"`
	
	// Processing settings
	BatchProcessing     bool     `json:"batch_processing" gorm:"default:true"`
	RealTimeMode        bool     `json:"real_time_mode" gorm:"default:false"`
	SmartTriggers       bool     `json:"smart_triggers" gorm:"default:true"`
	
	// Metadata
	CreatedAt           time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy           string    `json:"created_by"`
	UpdatedBy           string    `json:"updated_by"`
}

// TableName returns the table name for auto-translation settings
func (AutoTranslationSettings) TableName() string {
	return "auto_translation_settings"
}

// GetEnabledLogLevels returns the log levels that have translation enabled
func (ats *AutoTranslationSettings) GetEnabledLogLevels() []string {
	if !ats.Enabled {
		return nil
	}
	
	var enabledLevels []string
	
	if ats.TranslateErrors {
		enabledLevels = append(enabledLevels, "error")
	}
	if ats.TranslateWarnings {
		enabledLevels = append(enabledLevels, "warning", "warn")
	}
	if ats.TranslateInfo {
		enabledLevels = append(enabledLevels, "info")
	}
	if ats.TranslateDebug {
		enabledLevels = append(enabledLevels, "debug")
	}
	
	// Add any custom log levels
	for _, level := range ats.LogLevels {
		enabledLevels = append(enabledLevels, level)
	}
	
	return enabledLevels
}

// ShouldTranslateLevel checks if a specific log level should be translated
func (ats *AutoTranslationSettings) ShouldTranslateLevel(level string) bool {
	if !ats.Enabled {
		return false
	}
	
	switch level {
	case "error", "fatal", "panic":
		return ats.TranslateErrors
	case "warning", "warn":
		return ats.TranslateWarnings
	case "info":
		return ats.TranslateInfo
	case "debug", "trace":
		return ats.TranslateDebug
	default:
		// Check custom log levels
		for _, customLevel := range ats.LogLevels {
			if customLevel == level {
				return true
			}
		}
		return false
	}
}

// AutoTranslationStatistics represents translation statistics for monitoring
type AutoTranslationStatistics struct {
	ID                     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ServiceID              string    `json:"service_id" gorm:"not null;index"`
	SettingsID             uuid.UUID `json:"settings_id" gorm:"type:uuid;not null"`
	
	// Translation counts
	TotalLogMessages       int64     `json:"total_log_messages" gorm:"default:0"`
	TranslatedMessages     int64     `json:"translated_messages" gorm:"default:0"`
	FailedTranslations     int64     `json:"failed_translations" gorm:"default:0"`
	SuccessfulTranslations int64     `json:"successful_translations" gorm:"default:0"`
	
	// Performance metrics
	AverageConfidenceScore float64   `json:"average_confidence_score" gorm:"default:0"`
	AverageProcessingTime  float64   `json:"average_processing_time" gorm:"default:0"` // in milliseconds
	
	// Language breakdown
	TargetLanguage         string    `json:"target_language"`
	
	// Time period
	PeriodStart            time.Time `json:"period_start"`
	PeriodEnd              time.Time `json:"period_end"`
	
	// Metadata
	CreatedAt              time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt              time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for auto-translation statistics
func (AutoTranslationStatistics) TableName() string {
	return "auto_translation_statistics"
}

// CalculateSuccessRate returns the translation success rate as a percentage
func (ats *AutoTranslationStatistics) CalculateSuccessRate() float64 {
	if ats.TotalLogMessages == 0 {
		return 0
	}
	return (float64(ats.SuccessfulTranslations) / float64(ats.TotalLogMessages)) * 100
}

// CalculateTranslationRate returns the percentage of logs that were translated
func (ats *AutoTranslationStatistics) CalculateTranslationRate() float64 {
	if ats.TotalLogMessages == 0 {
		return 0
	}
	return (float64(ats.TranslatedMessages) / float64(ats.TotalLogMessages)) * 100
}