package storage

import (
	"context"
	"fmt"
	"time"

	"github.com/adc-log-service/internal/models"
	"github.com/adc-log-service/pkg/logger"
	"github.com/google/uuid"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// PostgreSQLStorage implements the Storage interface using PostgreSQL
type PostgreSQLStorage struct {
	db     *gorm.DB
	config *StorageConfig
}

// NewPostgreSQLStorage creates a new PostgreSQL storage instance
func NewPostgreSQLStorage(databaseURL string) (Storage, error) {
	config := &gorm.Config{
		Logger: logger.NewGormLogger(),
	}

	db, err := gorm.Open(postgres.Open(databaseURL), config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	storage := &PostgreSQLStorage{
		db: db,
		config: &StorageConfig{
			DatabaseURL:    databaseURL,
			QueryTimeout:   30 * time.Second,
			BatchSize:      1000,
			RetryAttempts:  3,
			Retry<PERSON>elay:     time.Second,
		},
	}

	// Run migrations
	if err := storage.migrate(); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	return storage, nil
}

// migrate runs database migrations
func (s *PostgreSQLStorage) migrate() error {
	return s.db.AutoMigrate(
		&models.LogEntry{},
		&models.RetentionPolicy{},
	)
}

// Store stores a single log entry
func (s *PostgreSQLStorage) Store(ctx context.Context, entry *models.LogEntry) error {
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()

	if err := s.db.WithContext(ctx).Create(entry).Error; err != nil {
		return fmt.Errorf("failed to store log entry: %w", err)
	}

	return nil
}

// StoreBatch stores multiple log entries in a single transaction
func (s *PostgreSQLStorage) StoreBatch(ctx context.Context, entries []*models.LogEntry) error {
	if len(entries) == 0 {
		return nil
	}

	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()

	// Process in batches to avoid memory issues
	batchSize := s.config.BatchSize
	for i := 0; i < len(entries); i += batchSize {
		end := i + batchSize
		if end > len(entries) {
			end = len(entries)
		}

		batch := entries[i:end]
		if err := s.db.WithContext(ctx).CreateInBatches(batch, len(batch)).Error; err != nil {
			return fmt.Errorf("failed to store log batch: %w", err)
		}
	}

	return nil
}

// Search searches for log entries based on the given query
func (s *PostgreSQLStorage) Search(ctx context.Context, query *models.LogSearchQuery) (*models.LogSearchResult, error) {
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()

	startTime := time.Now()

	// Build the query
	db := s.db.WithContext(ctx).Model(&models.LogEntry{})

	// Apply filters
	db = s.applyFilters(db, query)

	// Count total results
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count log entries: %w", err)
	}

	// Apply sorting
	sortBy := "timestamp"
	if query.SortBy != "" {
		sortBy = query.SortBy
	}

	sortOrder := "desc"
	if query.SortOrder != "" {
		sortOrder = query.SortOrder
	}

	db = db.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	limit := 100
	if query.Limit > 0 {
		limit = query.Limit
	}

	offset := 0
	if query.Offset > 0 {
		offset = query.Offset
	}

	db = db.Limit(limit).Offset(offset)

	// Execute query
	var logs []models.LogEntry
	if err := db.Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("failed to search log entries: %w", err)
	}

	searchTime := time.Since(startTime).Milliseconds()

	return &models.LogSearchResult{
		Logs:       logs,
		Total:      total,
		Limit:      limit,
		Offset:     offset,
		HasMore:    int64(offset+len(logs)) < total,
		SearchTime: searchTime,
	}, nil
}

// applyFilters applies search filters to the query
func (s *PostgreSQLStorage) applyFilters(db *gorm.DB, query *models.LogSearchQuery) *gorm.DB {
	// Time range filter
	if query.StartTime != nil {
		db = db.Where("timestamp >= ?", *query.StartTime)
	}
	if query.EndTime != nil {
		db = db.Where("timestamp <= ?", *query.EndTime)
	}

	// Level filter
	if len(query.Levels) > 0 {
		db = db.Where("level IN ?", query.Levels)
	}

	// Service filter
	if len(query.Services) > 0 {
		db = db.Where("service IN ?", query.Services)
	}

	// Component filter
	if len(query.Components) > 0 {
		db = db.Where("component IN ?", query.Components)
	}

	// Multi-tenant filters
	if query.OrganizationID != nil {
		db = db.Where("organization_id = ?", *query.OrganizationID)
	}
	if query.ShopID != nil {
		db = db.Where("shop_id = ?", *query.ShopID)
	}
	if query.UserID != nil {
		db = db.Where("user_id = ?", *query.UserID)
	}

	// Request context filters
	if query.CorrelationID != "" {
		db = db.Where("correlation_id = ?", query.CorrelationID)
	}
	if query.RequestID != "" {
		db = db.Where("request_id = ?", query.RequestID)
	}
	if query.SessionID != "" {
		db = db.Where("session_id = ?", query.SessionID)
	}

	// Content filters
	if query.Message != "" {
		db = db.Where("message ILIKE ?", "%"+query.Message+"%")
	}
	if query.ErrorCode != "" {
		db = db.Where("error_code = ?", query.ErrorCode)
	}

	// Tags filter
	if len(query.Tags) > 0 {
		for _, tag := range query.Tags {
			db = db.Where("? = ANY(tags)", tag)
		}
	}

	return db
}

// GetByID retrieves a log entry by its ID
func (s *PostgreSQLStorage) GetByID(ctx context.Context, id uuid.UUID) (*models.LogEntry, error) {
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()

	var entry models.LogEntry
	if err := s.db.WithContext(ctx).First(&entry, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, models.ErrLogNotFound
		}
		return nil, fmt.Errorf("failed to get log entry: %w", err)
	}

	return &entry, nil
}

// GetStats retrieves log statistics
func (s *PostgreSQLStorage) GetStats(ctx context.Context, organizationID *uuid.UUID, startTime, endTime time.Time) (*models.LogStats, error) {
	// This is a simplified implementation - in production, you'd want more efficient aggregations
	ctx, cancel := context.WithTimeout(ctx, s.config.QueryTimeout)
	defer cancel()

	stats := &models.LogStats{
		LogsByLevel:   make(map[models.LogLevel]int64),
		LogsByService: make(map[string]int64),
		TopErrors:     make([]models.LogErrorSummary, 0),
	}

	// Build base query
	db := s.db.WithContext(ctx).Model(&models.LogEntry{}).
		Where("timestamp >= ? AND timestamp <= ?", startTime, endTime)

	if organizationID != nil {
		db = db.Where("organization_id = ?", *organizationID)
	}

	// Total logs count
	if err := db.Count(&stats.TotalLogs).Error; err != nil {
		return nil, fmt.Errorf("failed to count total logs: %w", err)
	}

	// This is a placeholder implementation
	// In production, you'd use more efficient aggregation queries
	
	return stats, nil
}

// Cleanup removes old log entries based on retention policies
func (s *PostgreSQLStorage) Cleanup(ctx context.Context, retentionPolicies []models.RetentionPolicy) (int64, error) {
	// Placeholder implementation
	return 0, nil
}

// GetRetentionPolicies retrieves retention policies for an organization
func (s *PostgreSQLStorage) GetRetentionPolicies(ctx context.Context, organizationID uuid.UUID) ([]models.RetentionPolicy, error) {
	// Placeholder implementation
	return nil, nil
}

// UpdateRetentionPolicy updates a retention policy
func (s *PostgreSQLStorage) UpdateRetentionPolicy(ctx context.Context, policy *models.RetentionPolicy) error {
	// Placeholder implementation
	return nil
}

// HealthCheck checks if the database connection is healthy
func (s *PostgreSQLStorage) HealthCheck() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// Close closes the database connection
func (s *PostgreSQLStorage) Close() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}