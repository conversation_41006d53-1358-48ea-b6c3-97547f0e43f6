package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/adc-analytics-service/internal/config"
	"github.com/adc-analytics-service/internal/handlers"
	"github.com/adc-analytics-service/internal/middleware"
	"github.com/adc-analytics-service/internal/services"
	"github.com/adc-analytics-service/internal/storage"
	"github.com/adc-analytics-service/pkg/logger"
	"github.com/gin-gonic/gin"
	zlog "github.com/rs/zerolog/log"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set up logging
	logger.Initialize(cfg.Environment, cfg.LogLevel)

	zlog.Info().Str("port", cfg.Port).Msg("Starting ADC Analytics Service")

	// Initialize storage
	db, err := storage.NewPostgreSQLStorage(cfg.DatabaseURL)
	if err != nil {
		zlog.Fatal().Err(err).Msg("Failed to initialize database")
	}
	defer db.Close()

	// Initialize cache
	cache, err := storage.NewRedisCache(cfg.RedisURL)
	if err != nil {
		zlog.Fatal().Err(err).Msg("Failed to initialize cache")
	}
	defer cache.Close()

	// Initialize time series storage (if enabled)
	var tsStorage storage.TimeSeriesStorage
	if cfg.TimeSeriesEnabled {
		tsStorage, err = storage.NewInfluxDBStorage(cfg.InfluxDBURL, cfg.InfluxDBToken, cfg.InfluxDBOrg, cfg.InfluxDBBucket)
		if err != nil {
				zlog.Warn().Err(err).Msg("Failed to initialize time series storage")
		}
		defer tsStorage.Close()
	}

	// Initialize services
	metricsService := services.NewMetricsService(db, cache)
	analyticsService := services.NewAnalyticsService(db, cache, metricsService)
	reportsService := services.NewReportsService(db, cache, analyticsService, metricsService)
	aggregationService := services.NewAggregationService(db, cache)
	dashboardService := services.NewDashboardService(db, cache, analyticsService, metricsService)

	// Initialize handlers
	metricsHandler := handlers.NewMetricsHandler(metricsService)
	analyticsHandler := handlers.NewAnalyticsHandler(analyticsService, metricsService)
	reportsHandler := handlers.NewReportsHandler(reportsService)
	dashboardHandler := handlers.NewDashboardHandler(dashboardService)
	healthHandler := handlers.NewHealthHandler(db, cache)

	// Setup router
	router := setupRouter(cfg, metricsHandler, analyticsHandler, reportsHandler, dashboardHandler, healthHandler)

	// Start background workers
	startBackgroundWorkers(cfg, aggregationService)

	// Create HTTP server
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		zlog.Info().Str("addr", srv.Addr).Msg("Server starting")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			zlog.Fatal().Err(err).Msg("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	zlog.Info().Msg("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		zlog.Error().Err(err).Msg("Server forced to shutdown")
	}

	zlog.Info().Msg("Server exited")
}

// setupRouter configures the HTTP router with all routes and middleware
func setupRouter(cfg *config.Config, metricsHandler *handlers.MetricsHandler, analyticsHandler *handlers.AnalyticsHandler, reportsHandler *handlers.ReportsHandler, dashboardHandler *handlers.DashboardHandler, healthHandler *handlers.HealthHandler) *gin.Engine {
	// Set gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Global middleware
	router.Use(middleware.Recovery())
	router.Use(middleware.RequestID())
	router.Use(middleware.Logging())
	router.Use(middleware.CORS())

	// Health check endpoints (no auth required)
	router.GET("/health", healthHandler.Health)
	router.GET("/health/detailed", healthHandler.HealthDetailed)
	router.GET("/health/ready", healthHandler.Readiness)
	router.GET("/health/live", healthHandler.Liveness)
	
	// Kubernetes-style health endpoints
	router.GET("/readiness", healthHandler.Readiness)
	router.GET("/liveness", healthHandler.Liveness)
	
	// Monitoring endpoints
	router.GET("/metrics", healthHandler.Metrics)

	// API routes
	api := router.Group("/api/v1")
	api.Use(middleware.SSOAuth(cfg.SSOServiceURL))

	// Metrics endpoints
	metrics := api.Group("/metrics")
	{
		metrics.POST("/", metricsHandler.IngestMetric)
		metrics.POST("/batch", metricsHandler.IngestBatch)
		metrics.GET("/query", metricsHandler.QueryMetrics)
		metrics.GET("/series", metricsHandler.GetTimeSeries)
		metrics.GET("/aggregations", metricsHandler.GetAggregations)
	}

	// Analytics endpoints
	analytics := api.Group("/analytics")
	{
		analytics.GET("/events", analyticsHandler.GetEvents)
		analytics.POST("/events", analyticsHandler.TrackEvent)
		analytics.GET("/funnels", analyticsHandler.GetFunnel)
		analytics.GET("/cohorts", analyticsHandler.GetCohort)
		analytics.GET("/retention", analyticsHandler.GetRetention)
		analytics.GET("/insights", analyticsHandler.GetInsights)
	}

	// Reports endpoints
	reports := api.Group("/reports")
	{
		reports.GET("/", reportsHandler.GetReports)
		reports.GET("/:id", reportsHandler.GetReport)
		reports.POST("/", reportsHandler.CreateReport)
		reports.PUT("/:id", reportsHandler.UpdateReport)
		reports.DELETE("/:id", reportsHandler.DeleteReport)
		reports.POST("/:id/generate", reportsHandler.GenerateReport)
		reports.GET("/:id/export", reportsHandler.ExportReport)
	}

	// Dashboard endpoints
	dashboard := api.Group("/dashboard")
	{
		dashboard.GET("/overview", dashboardHandler.GetOverview)
		dashboard.GET("/kpis", dashboardHandler.GetKPIs)
		dashboard.GET("/charts", dashboardHandler.GetCharts)
		dashboard.GET("/realtime", dashboardHandler.GetRealtime)
	}

	// Organization-specific analytics
	orgAnalytics := api.Group("/organizations/:organizationId/analytics")
	{
		orgAnalytics.GET("/overview", analyticsHandler.GetOrganizationOverview)
		orgAnalytics.GET("/usage", analyticsHandler.GetUsageAnalytics)
		orgAnalytics.GET("/performance", analyticsHandler.GetPerformanceMetrics)
		orgAnalytics.GET("/trends", analyticsHandler.GetTrends)
	}

	// Internal API endpoints (for service-to-service communication)
	internal := router.Group("/internal/v1")
	internal.Use(middleware.APIKeyAuth(cfg))
	{
		internal.POST("/metrics/bulk", metricsHandler.IngestBatch)
		internal.GET("/metrics/export", metricsHandler.ExportMetrics)
		internal.POST("/events/batch", analyticsHandler.BatchTrackEvents)
		internal.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"status": "ok"})
		})
	}

	return router
}

// startBackgroundWorkers starts background workers for data processing
func startBackgroundWorkers(cfg *config.Config, aggregationService *services.AggregationService) {
	// Start metrics aggregation worker
	go func() {
		ticker := time.NewTicker(cfg.AggregationInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := aggregationService.ProcessPendingAggregations(context.Background()); err != nil {
					zlog.Error().Err(err).Msg("Failed to process pending aggregations")
				}
			}
		}
	}()

	// Start data cleanup worker
	go func() {
		ticker := time.NewTicker(cfg.CleanupInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := aggregationService.CleanupOldData(context.Background()); err != nil {
					zlog.Error().Err(err).Msg("Failed to cleanup old data")
				}
			}
		}
	}()

	zlog.Info().Msg("Background workers started")
}

