package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/adc-analytics-service/internal/config"
	"github.com/adc-analytics-service/internal/database"
	"github.com/adc-analytics-service/pkg/logger"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	var (
		command = flag.String("command", "migrate", "Migration command: migrate, rollback, status, validate")
		migrationID = flag.String("id", "", "Migration ID for rollback command")
		configPath = flag.String("config", "", "Path to config file")
	)
	flag.Parse()

	// Initialize logger
	log := logger.New(logger.Config{
		Level:  "info",
		Format: "console",
	})

	// Load configuration
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Connect to database
	db, err := gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{
		Logger: logger.NewGormLogger(log),
	})
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// Create migrator
	migrator := database.NewMigrator(db, log)

	// Initialize migration system
	if err := migrator.Initialize(); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize migration system")
	}

	// Execute command
	switch *command {
	case "migrate":
		if err := migrator.ApplyPendingMigrations(); err != nil {
			log.Fatal().Err(err).Msg("Migration failed")
		}
		log.Info().Msg("Migrations completed successfully")

	case "rollback":
		if *migrationID == "" {
			log.Fatal().Msg("Migration ID is required for rollback command")
		}
		if err := migrator.RollbackMigration(*migrationID); err != nil {
			log.Fatal().Err(err).Msg("Rollback failed")
		}
		log.Info().Str("id", *migrationID).Msg("Migration rolled back successfully")

	case "status":
		migrations, err := migrator.GetMigrationStatus()
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to get migration status")
		}

		fmt.Printf("Migration Status:\n")
		fmt.Printf("================\n")
		for _, migration := range migrations {
			status := string(migration.Status)
			appliedAt := "N/A"
			if migration.AppliedAt != nil {
				appliedAt = migration.AppliedAt.Format("2006-01-02 15:04:05")
			}
			fmt.Printf("%-10s %-30s %-15s %s\n", migration.ID, migration.Name, status, appliedAt)
		}

		// Show current version
		version, err := migrator.GetDatabaseVersion()
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to get database version")
		}
		fmt.Printf("\nCurrent database version: %s\n", version)

	case "validate":
		if err := migrator.ValidateMigrations(); err != nil {
			log.Fatal().Err(err).Msg("Migration validation failed")
		}
		log.Info().Msg("Migration validation successful")

	default:
		log.Fatal().Str("command", *command).Msg("Unknown command")
	}
}