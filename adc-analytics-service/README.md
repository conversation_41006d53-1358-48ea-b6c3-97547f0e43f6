# ADC Analytics Service

The ADC Analytics Service is a core component of the ADC platform, responsible for ingesting, processing, and serving analytics data. It provides a comprehensive suite of features for tracking and analyzing user behavior, including metrics, events, funnels, cohorts, and retention analysis. The service also includes powerful reporting and dashboarding capabilities to visualize and understand key performance indicators (KPIs).

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

*   [Go](https://golang.org/doc/install) (version 1.21 or higher)
*   [Docker](https://docs.docker.com/get-docker/)
*   [Docker Compose](https://docs.docker.com/compose/install/)

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/adc-platform/adc-analytics-service.git
    cd adc-analytics-service
    ```

2.  **Install dependencies:**

    ```bash
    go mod tidy
    ```

## Usage

### Running the Service

1.  **Set up environment variables:**

    Create a `.env` file by copying the example:

    ```bash
    cp .env.example .env
    ```

    Update the `.env` file with your local configuration, including database credentials, Redis connection details, and any other required settings.

2.  **Start the service and its dependencies:**

    ```bash
    docker-compose up -d
    ```

3.  **Run the service:**

    ```bash
    go run cmd/server/main.go
    ```

    The service will be available at `http://localhost:8080`.

### Running Tests

To run the test suite, use the following command:

```bash
go test ./...
```

### Configuration

The service is configured using environment variables. The following table lists the available options:

| Variable              | Description                                       | Default                |
| --------------------- | ------------------------------------------------- | ---------------------- |
| `PORT`                | The port the service will listen on.              | `8080`                 |
| `HOST`                | The host the service will bind to.                | `0.0.0.0`              |
| `ENVIRONMENT`         | The application environment.                      | `development`          |
| `LOG_LEVEL`           | The logging level.                                | `info`                 |
| `DATABASE_URL`        | The connection string for the PostgreSQL database. |                        |
| `REDIS_URL`           | The connection string for the Redis cache.        |                        |
| `SSO_SERVICE_URL`     | The URL of the SSO service for authentication.    |                        |
| `TIME_SERIES_ENABLED` | Enable or disable time series storage.            | `false`                |
| `INFLUXDB_URL`        | The URL of the InfluxDB instance.                 |                        |
| `INFLUXDB_TOKEN`      | The authentication token for InfluxDB.            |                        |
| `INFLUXDB_ORG`        | The organization to use in InfluxDB.              |                        |
| `INFLUXDB_BUCKET`     | The bucket to use in InfluxDB.                    |                        |

## API Reference

The service exposes a RESTful API for interacting with its features. The API is documented using the OpenAPI specification. When the service is running, you can access the API documentation at `http://localhost:8080/swagger/index.html`.

The main API endpoints include:

*   `/api/v1/metrics`: For ingesting and querying metrics.
*   `/api/v1/analytics`: For tracking events and analyzing user behavior.
*   `/api/v1/reports`: For creating, managing, and generating reports.
*   `/api/v1/dashboard`: For retrieving data for dashboards.

For more details on the API, please refer to the OpenAPI documentation.

## Dependencies

The ADC Analytics Service relies on several key technologies and services:

*   **Go:** The primary programming language for the service.
*   **Gin:** A high-performance HTTP web framework for Go.
*   **PostgreSQL:** The main data store for analytics data.
*   **Redis:** Used for caching and as a message broker.
*   **InfluxDB:** An optional time series database for storing metrics.
*   **ADC SSO Service:** Used for authentication and authorization.
