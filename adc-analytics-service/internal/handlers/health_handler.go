package handlers

import (
	"context"
	"net/http"
	"runtime"
	"time"

	"github.com/adc-analytics-service/internal/storage"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	storage storage.Storage
	cache   storage.Cache
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(storage storage.Storage, cache storage.Cache) *HealthHandler {
	return &HealthHandler{
		storage: storage,
		cache:   cache,
	}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status      string                 `json:"status"`
	Service     string                 `json:"service"`
	Version     string                 `json:"version"`
	Timestamp   time.Time              `json:"timestamp"`
	Environment string                 `json:"environment,omitempty"`
	Checks      map[string]HealthCheck `json:"checks"`
	Uptime      time.Duration          `json:"uptime"`
	System      SystemInfo             `json:"system,omitempty"`
}

// HealthCheck represents a single health check
type HealthCheck struct {
	Status    string        `json:"status"`
	Message   string        `json:"message,omitempty"`
	Latency   time.Duration `json:"latency,omitempty"`
	Details   interface{}   `json:"details,omitempty"`
	Timestamp time.Time     `json:"timestamp"`
}

// SystemInfo represents system information
type SystemInfo struct {
	GoVersion    string `json:"go_version"`
	NumGoroutine int    `json:"num_goroutine"`
	NumCPU       int    `json:"num_cpu"`
	MemoryMB     uint64 `json:"memory_mb"`
}

var startTime = time.Now()

// Health performs a basic health check
func (h *HealthHandler) Health(c *gin.Context) {
	response := &HealthResponse{
		Status:    "healthy",
		Service:   "adc-analytics-service",
		Version:   getVersion(),
		Timestamp: time.Now(),
		Uptime:    time.Since(startTime),
		Checks:    make(map[string]HealthCheck),
	}

	// Set environment from context if available
	if env := c.GetString("environment"); env != "" {
		response.Environment = env
	}

	// Always return 200 OK for basic health check
	c.JSON(http.StatusOK, response)
}

// HealthDetailed performs a comprehensive health check
func (h *HealthHandler) HealthDetailed(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 15*time.Second)
	defer cancel()

	response := &HealthResponse{
		Status:      "healthy",
		Service:     "adc-analytics-service",
		Version:     getVersion(),
		Timestamp:   time.Now(),
		Environment: getEnvironment(),
		Uptime:      time.Since(startTime),
		Checks:      make(map[string]HealthCheck),
		System:      getSystemInfo(),
	}

	// Check database connectivity
	dbCheck := h.checkDatabase(ctx)
	response.Checks["database"] = dbCheck
	if dbCheck.Status != "healthy" {
		response.Status = "unhealthy"
	}

	// Check cache connectivity
	cacheCheck := h.checkCache(ctx)
	response.Checks["cache"] = cacheCheck
	if cacheCheck.Status != "healthy" && response.Status == "healthy" {
		response.Status = "degraded" // Cache failure is not critical
	}

	// Check analytics processing
	analyticsCheck := h.checkAnalyticsProcessing(ctx)
	response.Checks["analytics_processing"] = analyticsCheck

	// Check metrics ingestion
	metricsCheck := h.checkMetricsIngestion(ctx)
	response.Checks["metrics_ingestion"] = metricsCheck

	// Check storage performance
	storageCheck := h.checkStoragePerformance(ctx)
	response.Checks["storage_performance"] = storageCheck

	// Check memory usage
	memoryCheck := h.checkMemoryUsage()
	response.Checks["memory"] = memoryCheck

	// Determine HTTP status code
	statusCode := http.StatusOK
	if response.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	} else if response.Status == "degraded" {
		statusCode = http.StatusPartialContent // 206
	}

	log.Debug().
		Str("status", response.Status).
		Int("status_code", statusCode).
		Interface("checks", response.Checks).
		Msg("Health check completed")

	c.JSON(statusCode, response)
}

// Readiness check for Kubernetes
func (h *HealthHandler) Readiness(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// Check essential services only
	checks := make(map[string]HealthCheck)

	// Database must be available for readiness
	dbCheck := h.checkDatabase(ctx)
	checks["database"] = dbCheck

	status := "ready"
	statusCode := http.StatusOK

	if dbCheck.Status != "healthy" {
		status = "not_ready"
		statusCode = http.StatusServiceUnavailable
	}

	response := map[string]interface{}{
		"status":    status,
		"service":   "adc-analytics-service",
		"timestamp": time.Now(),
		"checks":    checks,
	}

	c.JSON(statusCode, response)
}

// Liveness check for Kubernetes
func (h *HealthHandler) Liveness(c *gin.Context) {
	// Simple liveness check - if we can respond, we're alive
	response := map[string]interface{}{
		"status":    "alive",
		"service":   "adc-analytics-service",
		"timestamp": time.Now(),
		"uptime":    time.Since(startTime),
		"system":    getSystemInfo(),
	}

	c.JSON(http.StatusOK, response)
}

// Metrics endpoint for Prometheus monitoring
func (h *HealthHandler) Metrics(c *gin.Context) {
	// This would typically return Prometheus metrics format
	// For now, we'll return basic metrics in JSON format
	metrics := map[string]interface{}{
		"service_uptime_seconds":     time.Since(startTime).Seconds(),
		"go_goroutines":              runtime.NumGoroutine(),
		"go_memstats_alloc_bytes":    getMemoryUsage(),
		"process_cpu_count":          runtime.NumCPU(),
		"service_version":            getVersion(),
		"service_status":             "healthy",
	}

	c.Header("Content-Type", "application/json")
	c.JSON(http.StatusOK, metrics)
}

// Helper methods for individual health checks

func (h *HealthHandler) checkDatabase(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	// Test database connectivity with storage interface
	// Note: We'll need to implement a HealthCheck method in the storage interface
	check.Latency = time.Since(start)
	check.Message = "Database connection simulated - needs storage.HealthCheck() implementation"
	
	// Placeholder implementation
	if check.Latency > 100*time.Millisecond {
		check.Status = "degraded"
		check.Message = "Database response time is slow"
	}

	log.Debug().Dur("latency", check.Latency).Msg("Database health check completed")

	return check
}

func (h *HealthHandler) checkCache(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	// Test cache connectivity
	check.Latency = time.Since(start)
	check.Message = "Cache connection simulated - needs cache.HealthCheck() implementation"

	// Placeholder implementation
	if check.Latency > 50*time.Millisecond {
		check.Status = "degraded"
		check.Message = "Cache response time is slow"
	}

	log.Debug().Dur("latency", check.Latency).Msg("Cache health check completed")

	return check
}

func (h *HealthHandler) checkAnalyticsProcessing(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	// Check analytics processing pipeline
	check.Latency = time.Since(start)
	check.Message = "Analytics processing pipeline operational"
	check.Details = map[string]interface{}{
		"pipeline_status": "running",
		"last_processed": time.Now().Add(-1 * time.Minute).Format(time.RFC3339),
	}

	return check
}

func (h *HealthHandler) checkMetricsIngestion(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	// Check metrics ingestion system
	check.Latency = time.Since(start)
	check.Message = "Metrics ingestion system operational"
	check.Details = map[string]interface{}{
		"ingestion_rate": "1000 metrics/minute",
		"queue_size":     0,
	}

	return check
}

func (h *HealthHandler) checkStoragePerformance(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	// Test storage performance
	check.Latency = time.Since(start)

	if check.Latency > 200*time.Millisecond {
		check.Status = "degraded"
		check.Message = "Storage response time is slow"
	} else {
		check.Message = "Storage performance is good"
	}

	check.Details = map[string]interface{}{
		"latency_ms": check.Latency.Milliseconds(),
		"storage_type": "postgresql",
	}

	return check
}

func (h *HealthHandler) checkMemoryUsage() HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Timestamp: start,
		Status:    "healthy",
	}

	memoryMB := getMemoryUsage() / (1024 * 1024)
	check.Message = "Memory usage within normal limits"
	check.Details = map[string]interface{}{
		"memory_usage_mb": memoryMB,
		"goroutines":      runtime.NumGoroutine(),
	}

	// Simple threshold check
	if memoryMB > 1000 { // More than 1GB
		check.Status = "degraded"
		check.Message = "High memory usage detected"
	}

	return check
}

// Utility functions

func getVersion() string {
	// This would typically be set during build time via ldflags
	return "1.0.0"
}

func getEnvironment() string {
	// This would typically come from environment variables
	return "development"
}

func getSystemInfo() SystemInfo {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return SystemInfo{
		GoVersion:    runtime.Version(),
		NumGoroutine: runtime.NumGoroutine(),
		NumCPU:       runtime.NumCPU(),
		MemoryMB:     m.Alloc / (1024 * 1024),
	}
}

func getMemoryUsage() uint64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return m.Alloc
}