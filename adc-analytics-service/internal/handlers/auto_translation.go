package handlers

import (
	"net/http"
	"strconv"

	"github.com/adc-analytics-service/internal/models"
	"github.com/adc-analytics-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AutoTranslationHandler handles auto-translation related endpoints for analytics service
type AutoTranslationHandler struct {
	processor       *services.AutoTranslationProcessor
	multiLangClient *services.MultiLangClient
	logger          *logrus.Logger
}

// NewAutoTranslationHandler creates a new auto-translation handler
func NewAutoTranslationHandler(processor *services.AutoTranslationProcessor, multiLangClient *services.MultiLangClient, logger *logrus.Logger) *AutoTranslationHandler {
	return &AutoTranslationHandler{
		processor:       processor,
		multiLangClient: multiLangClient,
		logger:          logger,
	}
}

// GetAutoTranslationSettings handles GET /api/v1/auto-translation/settings/:service_id
func (h *AutoTranslationHandler) GetAutoTranslationSettings(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	// Mock settings for now - in production this would come from database
	settings := &models.AutoTranslationSettings{
		ID:                              uuid.New(),
		ServiceID:                       serviceID,
		Enabled:                         true,
		TargetLanguage:                  "es",
		ConfidenceThreshold:             0.8,
		TranslateDashboardTitles:        true,
		TranslateDashboardDescriptions:  true,
		TranslateMetricNames:            true,
		TranslateMetricDescriptions:     true,
		TranslateChartTitles:            true,
		TranslateChartLabels:            true,
		TranslateReportTitles:           true,
		TranslateReportDescriptions:     true,
		TranslateEventNames:             true,
		TranslateEventDescriptions:      true,
		TranslateFilterLabels:           true,
		TranslateErrorMessages:          true,
		TranslateAlertMessages:          true,
		TranslateInsightTitles:          true,
		TranslateInsightDescriptions:    true,
		TranslateTimeRangeLabels:        true,
		EnabledDashboardTypes:           []string{"overview", "performance", "usage"},
		EnabledMetricTypes:              []string{"counter", "gauge", "histogram"},
		EnabledChartTypes:               []string{"line", "bar", "pie"},
		EnabledReportTypes:              []string{"daily", "weekly", "monthly"},
		EnabledEventCategories:          []string{"user", "system", "api"},
		EnabledFilterTypes:              []string{"date", "status", "category"},
		EnabledAlertTypes:               []string{"warning", "error", "info"},
		EnabledInsightTypes:             []string{"trend", "anomaly", "recommendation"},
		EnabledTimeRangeTypes:           []string{"relative", "absolute", "custom"},
		BatchProcessing:                 true,
		RealTimeMode:                    false,
		SmartTriggers:                   true,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// UpdateAutoTranslationSettings handles PUT /api/v1/auto-translation/settings/:service_id
func (h *AutoTranslationHandler) UpdateAutoTranslationSettings(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	var updateData map[string]interface{}
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid JSON data",
		})
		return
	}

	h.logger.Infof("Updating auto-translation settings for service %s: %+v", serviceID, updateData)

	// Mock update - in production this would update the database
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Auto-translation settings updated successfully",
		"data": gin.H{
			"service_id":     serviceID,
			"updated_fields": updateData,
		},
	})
}

// TriggerAnalyticsTranslation handles POST /api/v1/auto-translation/analytics/:service_id/trigger
func (h *AutoTranslationHandler) TriggerAnalyticsTranslation(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	var request struct {
		TranslationType     string  `json:"translation_type" binding:"required"` // dashboard_title, metric_name, chart_title, etc.
		DashboardType       string  `json:"dashboard_type,omitempty"`
		MetricType          string  `json:"metric_type,omitempty"`
		ChartType           string  `json:"chart_type,omitempty"`
		Text                string  `json:"text" binding:"required"`
		TargetLanguage      string  `json:"target_language" binding:"required"`
		ConfidenceThreshold *float64 `json:"confidence_threshold,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	confidenceThreshold := 0.8
	if request.ConfidenceThreshold != nil {
		confidenceThreshold = *request.ConfidenceThreshold
	}

	// Process the translation
	result, err := h.processor.ProcessAnalyticsTranslation(
		request.DashboardType,
		request.MetricType,
		request.ChartType,
		request.TranslationType,
		request.Text,
		request.TargetLanguage,
		confidenceThreshold,
	)

	if err != nil {
		h.logger.Errorf("Analytics translation trigger failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to process analytics translation",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Analytics translation completed",
		"data": gin.H{
			"service_id":        serviceID,
			"translation_type":  request.TranslationType,
			"dashboard_type":    request.DashboardType,
			"metric_type":       request.MetricType,
			"chart_type":        request.ChartType,
			"original_text":     request.Text,
			"translated_text":   result.TranslatedText,
			"target_language":   request.TargetLanguage,
			"confidence":        result.Confidence,
			"auto_translated":   result.AutoTranslated,
			"job_id":           result.JobID,
		},
	})
}

// GetTranslationStatus handles GET /api/v1/auto-translation/analytics/:service_id/status
func (h *AutoTranslationHandler) GetTranslationStatus(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	// Get processor status
	queueStatus := h.processor.GetQueueStatus()

	// Mock statistics - in production this would come from database
	statistics := map[string]interface{}{
		"total_analytics_content":         450,
		"translated_dashboard_titles":     25,
		"translated_dashboard_descriptions": 22,
		"translated_metric_names":         85,
		"translated_metric_descriptions":  78,
		"translated_chart_titles":         65,
		"translated_chart_labels":         120,
		"translated_report_titles":        18,
		"translated_report_descriptions":  15,
		"translated_event_names":          95,
		"translated_event_descriptions":   88,
		"translated_filter_labels":        40,
		"translated_error_messages":       35,
		"translated_alert_messages":       45,
		"translated_insight_titles":       30,
		"translated_insight_descriptions": 28,
		"translated_time_range_labels":    25,
		"failed_translations":             8,
		"success_rate":                    98.2,
		"average_confidence":              0.89,
		"enabled_dashboard_types":         []string{"overview", "performance", "usage"},
		"enabled_metric_types":            []string{"counter", "gauge", "histogram"},
		"coverage_percentage":             94.1,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"service_id":              serviceID,
			"auto_translation_enabled": true,
			"processor_status":        queueStatus,
			"statistics":              statistics,
		},
	})
}

// HealthCheck handles GET /api/v1/auto-translation/health
func (h *AutoTranslationHandler) HealthCheck(c *gin.Context) {
	// Check Multi-Languages service connectivity
	ctx := c.Request.Context()
	err := h.multiLangClient.HealthCheck(ctx)
	
	status := gin.H{
		"service":           "adc-analytics-service auto-translation",
		"processor_running": h.processor.GetQueueStatus()["running"],
		"multilang_service": err == nil,
	}

	if err != nil {
		status["multilang_error"] = err.Error()
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"data":    status,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// BatchTranslateAnalyticsContent handles POST /api/v1/auto-translation/analytics/:service_id/batch
func (h *AutoTranslationHandler) BatchTranslateAnalyticsContent(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	var request struct {
		Content []struct {
			TranslationType string `json:"translation_type" binding:"required"`
			DashboardType   string `json:"dashboard_type,omitempty"`
			MetricType      string `json:"metric_type,omitempty"`
			ChartType       string `json:"chart_type,omitempty"`
			Text            string `json:"text" binding:"required"`
		} `json:"content" binding:"required"`
		TargetLanguage      string  `json:"target_language" binding:"required"`
		ConfidenceThreshold *float64 `json:"confidence_threshold,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	confidenceThreshold := 0.8
	if request.ConfidenceThreshold != nil {
		confidenceThreshold = *request.ConfidenceThreshold
	}

	// Process translations
	var results []map[string]interface{}
	successful := 0
	failed := 0

	for _, content := range request.Content {
		result, err := h.processor.ProcessAnalyticsTranslation(
			content.DashboardType,
			content.MetricType,
			content.ChartType,
			content.TranslationType,
			content.Text,
			request.TargetLanguage,
			confidenceThreshold,
		)

		if err != nil {
			failed++
			results = append(results, map[string]interface{}{
				"translation_type": content.TranslationType,
				"dashboard_type":   content.DashboardType,
				"metric_type":      content.MetricType,
				"chart_type":       content.ChartType,
				"original_text":    content.Text,
				"success":          false,
				"error":            err.Error(),
			})
		} else {
			successful++
			results = append(results, map[string]interface{}{
				"translation_type": content.TranslationType,
				"dashboard_type":   content.DashboardType,
				"metric_type":      content.MetricType,
				"chart_type":       content.ChartType,
				"original_text":    content.Text,
				"translated_text":  result.TranslatedText,
				"confidence":       result.Confidence,
				"auto_translated":  result.AutoTranslated,
				"success":          true,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Batch analytics translation completed",
		"data": gin.H{
			"service_id":      serviceID,
			"target_language": request.TargetLanguage,
			"total_content":   len(request.Content),
			"successful":      successful,
			"failed":          failed,
			"results":         results,
		},
	})
}

// TranslateDashboardContent handles POST /api/v1/auto-translation/dashboards/:service_id/translate
func (h *AutoTranslationHandler) TranslateDashboardContent(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	var request struct {
		DashboardType       string  `json:"dashboard_type" binding:"required"` // overview, performance, usage
		TranslationType     string  `json:"translation_type" binding:"required"` // title, description
		Content             string  `json:"content" binding:"required"`
		TargetLanguage      string  `json:"target_language" binding:"required"`
		ConfidenceThreshold *float64 `json:"confidence_threshold,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Translate dashboard content
	ctx := c.Request.Context()
	var translatedText string
	var autoTranslated bool
	var err error

	switch request.TranslationType {
	case "title":
		translatedText, autoTranslated, err = h.multiLangClient.TranslateDashboardTitle(
			ctx,
			request.DashboardType,
			request.Content,
			request.TargetLanguage,
		)
	case "description":
		translatedText, autoTranslated, err = h.multiLangClient.TranslateDashboardDescription(
			ctx,
			request.DashboardType,
			request.Content,
			request.TargetLanguage,
		)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid translation_type. Must be 'title' or 'description'",
		})
		return
	}

	if err != nil {
		h.logger.Errorf("Dashboard content translation failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to translate dashboard content",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Dashboard content translation completed",
		"data": gin.H{
			"service_id":        serviceID,
			"dashboard_type":    request.DashboardType,
			"translation_type":  request.TranslationType,
			"original_content":  request.Content,
			"translated_content": translatedText,
			"target_language":   request.TargetLanguage,
			"auto_translated":   autoTranslated,
		},
	})
}

// GetTranslationHistory handles GET /api/v1/auto-translation/analytics/:service_id/history
func (h *AutoTranslationHandler) GetTranslationHistory(c *gin.Context) {
	serviceID := c.Param("service_id")
	if serviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "service_id is required",
		})
		return
	}

	// Parse query parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil {
			page = parsed
		}
	}

	limit := 50
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed <= 100 {
			limit = parsed
		}
	}

	translationType := c.Query("translation_type")
	dashboardType := c.Query("dashboard_type")

	// Mock translation history - in production this would come from database
	history := []map[string]interface{}{
		{
			"id":               "123e4567-e89b-12d3-a456-426614174000",
			"translation_type": "dashboard_title",
			"dashboard_type":   "overview",
			"original_text":    "System Overview Dashboard",
			"translated_text":  "Panel de Resumen del Sistema",
			"target_language":  "es",
			"confidence":       0.95,
			"auto_translated":  true,
			"processed_at":     "2025-06-30T12:00:00Z",
		},
		{
			"id":               "123e4567-e89b-12d3-a456-426614174001",
			"translation_type": "metric_description",
			"metric_type":      "counter",
			"original_text":    "Total number of API requests",
			"translated_text":  "Número total de solicitudes de API",
			"target_language":  "es",
			"confidence":       0.92,
			"auto_translated":  true,
			"processed_at":     "2025-06-30T12:01:00Z",
		},
	}

	// Apply filters
	var filteredHistory []map[string]interface{}
	for _, item := range history {
		if translationType != "" && item["translation_type"] != translationType {
			continue
		}
		if dashboardType != "" && item["dashboard_type"] != dashboardType {
			continue
		}
		filteredHistory = append(filteredHistory, item)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"service_id":           serviceID,
			"page":                page,
			"limit":               limit,
			"total":               len(filteredHistory),
			"translation_filter":  translationType,
			"dashboard_filter":    dashboardType,
			"history":             filteredHistory,
		},
	})
}