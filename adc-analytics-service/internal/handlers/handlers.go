package handlers

import (
	"net/http"
	"time"

	"github.com/adc-analytics-service/internal/models"
	"github.com/adc-analytics-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// AnalyticsHandler handles analytics endpoints
type AnalyticsHandler struct {
	analyticsService *services.AnalyticsService
	metricsService   *services.MetricsService
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *services.AnalyticsService, metricsService *services.MetricsService) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		metricsService:   metricsService,
	}
}

// TrackEvent handles POST /api/v1/events
func (h *AnalyticsHandler) TrackEvent(c *gin.Context) {
	var request models.EventRequest
	if !h.validateRequest(c, &request) {
		return
	}

	analyticsCtx := h.extractAnalyticsContext(c)
	event, err := h.analyticsService.TrackEvent(c.Request.Context(), &request, analyticsCtx)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to track event", err)
		return
	}

	response, err := event.ToResponse()
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to format event response", err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{"event": response})
}

// BatchTrackEvents handles POST /api/v1/events/batch
func (h *AnalyticsHandler) BatchTrackEvents(c *gin.Context) {
	var request models.BatchEventRequest
	if !h.validateRequest(c, &request) {
		return
	}

	if len(request.Events) == 0 {
		h.respondWithError(c, http.StatusBadRequest, "No events provided", nil)
		return
	}

	if len(request.Events) > 1000 {
		h.respondWithError(c, http.StatusBadRequest, "Too many events (max 1000)", nil)
		return
	}

	analyticsCtx := h.extractAnalyticsContext(c)
	result, err := h.analyticsService.TrackEvents(c.Request.Context(), request.Events, analyticsCtx)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to track events batch", err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetEvents handles GET /api/v1/events
func (h *AnalyticsHandler) GetEvents(c *gin.Context) {
	var query models.EventQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	// Set defaults
	if query.Limit == 0 {
		query.Limit = 100
	}
	if query.Limit > 1000 {
		query.Limit = 1000
	}

	events, err := h.analyticsService.GetEvents(c.Request.Context(), &query)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve events", err)
		return
	}

	// Convert to response format
	responses := make([]*models.EventResponse, len(events))
	for i, event := range events {
		response, err := event.ToResponse()
		if err != nil {
			log.Error().Err(err).Str("event_id", event.ID.String()).Msg("Failed to convert event to response")
			continue
		}
		responses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"events": responses,
		"total":  len(responses),
		"limit":  query.Limit,
		"offset": query.Offset,
	})
}

// GetFunnel handles GET /api/v1/analytics/funnel
func (h *AnalyticsHandler) GetFunnel(c *gin.Context) {
	steps := c.QueryArray("steps")
	if len(steps) == 0 {
		h.respondWithError(c, http.StatusBadRequest, "Funnel steps are required", nil)
		return
	}

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := h.extractFilters(c)
	funnelData, err := h.analyticsService.GetFunnelAnalysis(c.Request.Context(), steps, filters, startTime, endTime)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to generate funnel analysis", err)
		return
	}

	c.JSON(http.StatusOK, funnelData)
}

// GetCohort handles GET /api/v1/analytics/cohort
func (h *AnalyticsHandler) GetCohort(c *gin.Context) {
	cohortPeriod := c.DefaultQuery("cohort_period", "week")
	analysisPeriod := c.DefaultQuery("analysis_period", "week")

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	cohortData, err := h.analyticsService.GetCohortAnalysis(c.Request.Context(), cohortPeriod, analysisPeriod, startTime, endTime)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to generate cohort analysis", err)
		return
	}

	c.JSON(http.StatusOK, cohortData)
}

// GetRetention handles GET /api/v1/analytics/retention
func (h *AnalyticsHandler) GetRetention(c *gin.Context) {
	period := c.DefaultQuery("period", "week")

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := h.extractFilters(c)
	retentionData, err := h.analyticsService.GetRetentionAnalysis(c.Request.Context(), period, startTime, endTime, filters)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to generate retention analysis", err)
		return
	}

	c.JSON(http.StatusOK, retentionData)
}

// GetInsights handles GET /api/v1/analytics/insights
func (h *AnalyticsHandler) GetInsights(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := h.extractFilters(c)
	insights, err := h.analyticsService.GetInsights(c.Request.Context(), filters, startTime, endTime)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to generate insights", err)
		return
	}

	c.JSON(http.StatusOK, insights)
}

// GetOrganizationOverview handles GET /api/v1/analytics/organization/:organizationId/overview
func (h *AnalyticsHandler) GetOrganizationOverview(c *gin.Context) {
	orgID, err := uuid.Parse(c.Param("organizationId"))
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid organization ID", err)
		return
	}

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := map[string]interface{}{
		"organization_id": orgID.String(),
	}

	insights, err := h.analyticsService.GetInsights(c.Request.Context(), filters, startTime, endTime)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to generate organization overview", err)
		return
	}

	c.JSON(http.StatusOK, insights)
}

// GetUsageAnalytics handles GET /api/v1/analytics/usage
func (h *AnalyticsHandler) GetUsageAnalytics(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := h.extractFilters(c)
	
	// Get events for usage analysis
	eventQuery := &models.EventQuery{
		Type:      getEventTypeFromQuery(c, "api_call"),
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     10000,
	}

	// Apply filters to event query
	if orgID, ok := filters["organization_id"]; ok {
		if id, err := uuid.Parse(orgID.(string)); err == nil {
			eventQuery.OrganizationID = &id
		}
	}

	events, err := h.analyticsService.GetEvents(c.Request.Context(), eventQuery)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve usage data", err)
		return
	}

	usageData := h.calculateUsageAnalytics(events, startTime, endTime)
	c.JSON(http.StatusOK, usageData)
}

// GetPerformanceMetrics handles GET /api/v1/analytics/performance
func (h *AnalyticsHandler) GetPerformanceMetrics(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	filters := h.extractFilters(c)
	
	// Get performance metrics
	metricQuery := &models.MetricQuery{
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     10000,
	}

	// Apply filters
	if orgID, ok := filters["organization_id"]; ok {
		if id, err := uuid.Parse(orgID.(string)); err == nil {
			metricQuery.OrganizationID = &id
		}
	}

	metrics, err := h.metricsService.GetMetrics(c.Request.Context(), metricQuery)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve performance metrics", err)
		return
	}

	performanceData := h.calculatePerformanceMetrics(metrics)
	c.JSON(http.StatusOK, performanceData)
}

// GetTrends handles GET /api/v1/analytics/trends
func (h *AnalyticsHandler) GetTrends(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	metricName := c.Query("metric")
	if metricName == "" {
		h.respondWithError(c, http.StatusBadRequest, "Metric name is required", nil)
		return
	}

	interval := c.DefaultQuery("interval", "1h")
	aggregation := models.AggregationType(c.DefaultQuery("aggregation", "avg"))

	filters := h.extractFilters(c)
	
	timeSeries, err := h.metricsService.GetTimeSeries(c.Request.Context(), metricName, startTime, endTime, interval, aggregation, filters)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve trend data", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metric":     metricName,
		"time_series": timeSeries,
		"interval":   interval,
		"aggregation": aggregation,
	})
}

// MetricsHandler handles metrics endpoints
type MetricsHandler struct {
	metricsService *services.MetricsService
}

// NewMetricsHandler creates a new metrics handler
func NewMetricsHandler(metricsService *services.MetricsService) *MetricsHandler {
	return &MetricsHandler{
		metricsService: metricsService,
	}
}

// IngestMetric handles POST /api/v1/metrics
func (h *MetricsHandler) IngestMetric(c *gin.Context) {
	var request models.MetricRequest
	if !h.validateRequest(c, &request) {
		return
	}

	if err := h.metricsService.RecordMetric(c.Request.Context(), &request); err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to record metric", err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{"status": "recorded"})
}

// IngestBatch handles POST /api/v1/metrics/batch
func (h *MetricsHandler) IngestBatch(c *gin.Context) {
	var request models.BatchMetricRequest
	if !h.validateRequest(c, &request) {
		return
	}

	if len(request.Metrics) == 0 {
		h.respondWithError(c, http.StatusBadRequest, "No metrics provided", nil)
		return
	}

	if len(request.Metrics) > 1000 {
		h.respondWithError(c, http.StatusBadRequest, "Too many metrics (max 1000)", nil)
		return
	}

	result, err := h.metricsService.RecordMetrics(c.Request.Context(), request.Metrics)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to record metrics batch", err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// QueryMetrics handles GET /api/v1/metrics
func (h *MetricsHandler) QueryMetrics(c *gin.Context) {
	var query models.MetricQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	// Set defaults
	if query.Limit == 0 {
		query.Limit = 100
	}
	if query.Limit > 1000 {
		query.Limit = 1000
	}

	metrics, err := h.metricsService.GetMetrics(c.Request.Context(), &query)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve metrics", err)
		return
	}

	// Convert to response format
	responses := make([]*models.MetricResponse, len(metrics))
	for i, metric := range metrics {
		response, err := metric.ToResponse()
		if err != nil {
			log.Error().Err(err).Str("metric_id", metric.ID.String()).Msg("Failed to convert metric to response")
			continue
		}
		responses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": responses,
		"total":   len(responses),
		"limit":   query.Limit,
		"offset":  query.Offset,
	})
}

// GetTimeSeries handles GET /api/v1/metrics/timeseries
func (h *MetricsHandler) GetTimeSeries(c *gin.Context) {
	metricName := c.Query("metric")
	if metricName == "" {
		h.respondWithError(c, http.StatusBadRequest, "Metric name is required", nil)
		return
	}

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	interval := c.DefaultQuery("interval", "1h")
	aggregation := models.AggregationType(c.DefaultQuery("aggregation", "avg"))

	filters := h.extractFilters(c)
	
	timeSeries, err := h.metricsService.GetTimeSeries(c.Request.Context(), metricName, startTime, endTime, interval, aggregation, filters)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve time series", err)
		return
	}

	c.JSON(http.StatusOK, timeSeries)
}

// GetAggregations handles GET /api/v1/metrics/aggregations
func (h *MetricsHandler) GetAggregations(c *gin.Context) {
	metrics := c.QueryArray("metrics")
	if len(metrics) == 0 {
		h.respondWithError(c, http.StatusBadRequest, "Metric names are required", nil)
		return
	}

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid time range", err)
		return
	}

	aggregation := models.AggregationType(c.DefaultQuery("aggregation", "avg"))
	groupBy := c.QueryArray("group_by")
	filters := h.extractFilters(c)

	aggregatedMetrics, err := h.metricsService.GetAggregatedMetrics(c.Request.Context(), metrics, aggregation, startTime, endTime, groupBy, filters)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to aggregate metrics", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"aggregated_metrics": aggregatedMetrics,
		"aggregation":        aggregation,
		"time_range": gin.H{
			"start": startTime,
			"end":   endTime,
		},
	})
}

// ExportMetrics handles GET /api/v1/metrics/export
func (h *MetricsHandler) ExportMetrics(c *gin.Context) {
	format := c.DefaultQuery("format", "json")
	
	var query models.MetricQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	// Set a reasonable limit for export
	if query.Limit == 0 {
		query.Limit = 10000
	}
	if query.Limit > 50000 {
		query.Limit = 50000
	}

	metrics, err := h.metricsService.GetMetrics(c.Request.Context(), &query)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve metrics for export", err)
		return
	}

	switch format {
	case "csv":
		h.exportMetricsAsCSV(c, metrics)
	case "json":
		h.exportMetricsAsJSON(c, metrics)
	default:
		h.respondWithError(c, http.StatusBadRequest, "Unsupported export format", nil)
	}
}

// ReportsHandler handles report endpoints
type ReportsHandler struct {
	reportsService *services.ReportsService
}

// NewReportsHandler creates a new reports handler
func NewReportsHandler(reportsService *services.ReportsService) *ReportsHandler {
	return &ReportsHandler{
		reportsService: reportsService,
	}
}

// GetReports handles GET /api/v1/reports
func (h *ReportsHandler) GetReports(c *gin.Context) {
	var query models.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	// Set defaults
	if query.Limit == 0 {
		query.Limit = 50
	}
	if query.Limit > 200 {
		query.Limit = 200
	}

	reports, err := h.reportsService.GetReports(c.Request.Context(), &query)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve reports", err)
		return
	}

	// Convert to response format
	responses := make([]*models.ReportResponse, len(reports))
	for i, report := range reports {
		response, err := report.ToResponse()
		if err != nil {
			log.Error().Err(err).Str("report_id", report.ID.String()).Msg("Failed to convert report to response")
			continue
		}
		responses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{
		"reports": responses,
		"total":   len(responses),
		"limit":   query.Limit,
		"offset":  query.Offset,
	})
}

// GetReport handles GET /api/v1/reports/:id
func (h *ReportsHandler) GetReport(c *gin.Context) {
	reportID := c.Param("id")
	if reportID == "" {
		h.respondWithError(c, http.StatusBadRequest, "Report ID is required", nil)
		return
	}

	report, err := h.reportsService.GetReport(c.Request.Context(), reportID)
	if err != nil {
		if err == models.ErrReportNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
			return
		}
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve report", err)
		return
	}

	response, err := report.ToResponse()
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to format report response", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"report": response})
}

// CreateReport handles POST /api/v1/reports
func (h *ReportsHandler) CreateReport(c *gin.Context) {
	var request models.ReportRequest
	if !h.validateRequest(c, &request) {
		return
	}

	analyticsCtx := h.extractAnalyticsContext(c)
	report, err := h.reportsService.CreateReport(c.Request.Context(), &request, analyticsCtx)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to create report", err)
		return
	}

	response, err := report.ToResponse()
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to format report response", err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{"report": response})
}

// UpdateReport handles PUT /api/v1/reports/:id
func (h *ReportsHandler) UpdateReport(c *gin.Context) {
	reportID := c.Param("id")
	if reportID == "" {
		h.respondWithError(c, http.StatusBadRequest, "Report ID is required", nil)
		return
	}

	var request models.ReportRequest
	if !h.validateRequest(c, &request) {
		return
	}

	// Get current report
	currentReport, err := h.reportsService.GetReport(c.Request.Context(), reportID)
	if err != nil {
		if err == models.ErrReportNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
			return
		}
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve current report", err)
		return
	}

	// Convert request to report
	updatedReport, err := request.ToReport()
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid report data", err)
		return
	}

	// Preserve ID and timestamps
	updatedReport.ID = currentReport.ID
	updatedReport.CreatedAt = currentReport.CreatedAt
	updatedReport.CreatedBy = currentReport.CreatedBy

	if err := h.reportsService.UpdateReport(c.Request.Context(), updatedReport); err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to update report", err)
		return
	}

	response, err := updatedReport.ToResponse()
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to format report response", err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"report": response})
}

// DeleteReport handles DELETE /api/v1/reports/:id
func (h *ReportsHandler) DeleteReport(c *gin.Context) {
	reportID := c.Param("id")
	if reportID == "" {
		h.respondWithError(c, http.StatusBadRequest, "Report ID is required", nil)
		return
	}

	if err := h.reportsService.DeleteReport(c.Request.Context(), reportID); err != nil {
		if err == models.ErrReportNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
			return
		}
		h.respondWithError(c, http.StatusInternalServerError, "Failed to delete report", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GenerateReport handles POST /api/v1/reports/:id/generate
func (h *ReportsHandler) GenerateReport(c *gin.Context) {
	reportID := c.Param("id")
	if reportID == "" {
		h.respondWithError(c, http.StatusBadRequest, "Report ID is required", nil)
		return
	}

	reportUUID, err := uuid.Parse(reportID)
	if err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid report ID format", err)
		return
	}

	// Generate report asynchronously
	go func() {
		if err := h.reportsService.GenerateReport(c.Request.Context(), reportUUID); err != nil {
			log.Error().Err(err).Str("report_id", reportID).Msg("Report generation failed")
		}
	}()

	c.JSON(http.StatusAccepted, gin.H{
		"message":   "Report generation started",
		"report_id": reportID,
	})
}

// ExportReport handles GET /api/v1/reports/:id/export
func (h *ReportsHandler) ExportReport(c *gin.Context) {
	reportID := c.Param("id")
	format := c.DefaultQuery("format", "json")

	report, err := h.reportsService.GetReport(c.Request.Context(), reportID)
	if err != nil {
		if err == models.ErrReportNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
			return
		}
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve report", err)
		return
	}

	if report.Status != models.ReportStatusCompleted {
		h.respondWithError(c, http.StatusBadRequest, "Report is not ready for export", nil)
		return
	}

	switch format {
	case "json":
		h.exportReportAsJSON(c, report)
	case "csv":
		h.exportReportAsCSV(c, report)
	case "pdf":
		h.exportReportAsPDF(c, report)
	default:
		h.respondWithError(c, http.StatusBadRequest, "Unsupported export format", nil)
	}
}

// DashboardHandler handles dashboard endpoints
type DashboardHandler struct {
	dashboardService *services.DashboardService
}

// NewDashboardHandler creates a new dashboard handler
func NewDashboardHandler(dashboardService *services.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetOverview handles GET /api/v1/dashboard/overview
func (h *DashboardHandler) GetOverview(c *gin.Context) {
	dashboardID := c.DefaultQuery("dashboard_id", "default")
	
	timeRange := make(map[string]time.Time)
	startTime, endTime, err := h.parseTimeRange(c)
	if err == nil {
		timeRange["start"] = startTime
		timeRange["end"] = endTime
	} else {
		// Default to last 7 days
		timeRange["end"] = time.Now()
		timeRange["start"] = timeRange["end"].AddDate(0, 0, -7)
	}

	dashboardData, err := h.dashboardService.GetDashboardData(c.Request.Context(), dashboardID, timeRange)
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to retrieve dashboard data", err)
		return
	}

	c.JSON(http.StatusOK, dashboardData)
}

// GetKPIs handles GET /api/v1/dashboard/kpis
func (h *DashboardHandler) GetKPIs(c *gin.Context) {
	// Generate placeholder KPI data
	kpis := []models.KPI{
		{
			Name:        "Total Events",
			Value:       12450,
			Unit:        "events",
			Change:      &[]float64{15.2}[0],
			Trend:       "up",
			Description: "Total events tracked in the last 24 hours",
			Timestamp:   time.Now(),
		},
		{
			Name:        "Active Users",
			Value:       3420,
			Unit:        "users",
			Change:      &[]float64{8.7}[0],
			Trend:       "up",
			Description: "Unique active users today",
			Timestamp:   time.Now(),
		},
		{
			Name:        "API Response Time",
			Value:       245,
			Unit:        "ms",
			Change:      &[]float64{-12.3}[0],
			Trend:       "down",
			Target:      &[]float64{200}[0],
			Description: "Average API response time (P95)",
			Timestamp:   time.Now(),
		},
	}

	c.JSON(http.StatusOK, gin.H{"kpis": kpis})
}

// GetCharts handles GET /api/v1/dashboard/charts
func (h *DashboardHandler) GetCharts(c *gin.Context) {
	chartType := c.DefaultQuery("type", "events_over_time")
	
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		// Default to last 24 hours
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour)
	}

	switch chartType {
	case "events_over_time":
		chartData := h.generateEventsOverTimeChart(startTime, endTime)
		c.JSON(http.StatusOK, chartData)
	case "user_activity":
		chartData := h.generateUserActivityChart(startTime, endTime)
		c.JSON(http.StatusOK, chartData)
	default:
		h.respondWithError(c, http.StatusBadRequest, "Unsupported chart type", nil)
	}
}

// GetRealtime handles GET /api/v1/dashboard/realtime
func (h *DashboardHandler) GetRealtime(c *gin.Context) {
	realtimeData := gin.H{
		"current_active_users": 142,
		"events_per_minute":    847,
		"error_rate":           0.3,
		"avg_response_time":    234,
		"last_updated":         time.Now(),
	}

	c.JSON(http.StatusOK, realtimeData)
}

// Helper methods

func (h *AnalyticsHandler) validateRequest(c *gin.Context, target interface{}) bool {
	if err := c.ShouldBindJSON(target); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid request format", err)
		return false
	}
	return true
}

func (h *AnalyticsHandler) respondWithError(c *gin.Context, statusCode int, message string, err error) {
	log.Error().
		Err(err).
		Str("path", c.Request.URL.Path).
		Str("method", c.Request.Method).
		Msg(message)

	errorResponse := models.NewErrorResponse(err)
	c.JSON(statusCode, errorResponse)
}

func (h *AnalyticsHandler) extractAnalyticsContext(c *gin.Context) *models.AnalyticsContext {
	analyticsCtx := models.NewAnalyticsContext()
	analyticsCtx.IPAddress = c.ClientIP()
	analyticsCtx.UserAgent = c.GetHeader("User-Agent")
	analyticsCtx.RequestID = c.GetHeader("X-Request-ID")

	// Extract user ID from context (set by auth middleware)
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uuid.UUID); ok {
			analyticsCtx.UserID = &uid
		}
	}

	// Extract organization ID from context
	if orgID, exists := c.Get("organization_id"); exists {
		if oid, ok := orgID.(uuid.UUID); ok {
			analyticsCtx.OrganizationID = &oid
		}
	}

	// Extract shop ID from context
	if shopID, exists := c.Get("shop_id"); exists {
		if sid, ok := shopID.(uuid.UUID); ok {
			analyticsCtx.ShopID = &sid
		}
	}

	return analyticsCtx
}

func (h *AnalyticsHandler) parseTimeRange(c *gin.Context) (time.Time, time.Time, error) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -7) // Default to 7 days ago
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		endTime = time.Now() // Default to now
	}

	if startTime.After(endTime) {
		return time.Time{}, time.Time{}, models.ErrInvalidTimeRange
	}

	return startTime, endTime, nil
}

func (h *AnalyticsHandler) extractFilters(c *gin.Context) map[string]interface{} {
	filters := make(map[string]interface{})

	if orgID := c.Query("organization_id"); orgID != "" {
		filters["organization_id"] = orgID
	}

	if userID := c.Query("user_id"); userID != "" {
		filters["user_id"] = userID
	}

	if shopID := c.Query("shop_id"); shopID != "" {
		filters["shop_id"] = shopID
	}

	if source := c.Query("source"); source != "" {
		filters["source"] = source
	}

	return filters
}

func (h *AnalyticsHandler) calculateUsageAnalytics(events []*models.Event, startTime, endTime time.Time) map[string]interface{} {
	usageData := make(map[string]interface{})
	
	// Calculate basic usage statistics
	totalEvents := len(events)
	uniqueUsers := make(map[string]bool)
	uniqueSessions := make(map[string]bool)
	eventsByHour := make(map[int]int)

	for _, event := range events {
		if event.UserID != nil {
			uniqueUsers[event.UserID.String()] = true
		}
		if event.SessionID != "" {
			uniqueSessions[event.SessionID] = true
		}
		hour := event.Timestamp.Hour()
		eventsByHour[hour]++
	}

	usageData["total_events"] = totalEvents
	usageData["unique_users"] = len(uniqueUsers)
	usageData["unique_sessions"] = len(uniqueSessions)
	usageData["events_by_hour"] = eventsByHour
	usageData["time_range"] = map[string]interface{}{
		"start": startTime,
		"end":   endTime,
	}

	if totalEvents > 0 {
		usageData["avg_events_per_user"] = float64(totalEvents) / float64(len(uniqueUsers))
		usageData["avg_events_per_session"] = float64(totalEvents) / float64(len(uniqueSessions))
	}

	return usageData
}

func (h *AnalyticsHandler) calculatePerformanceMetrics(metrics []*models.Metric) map[string]interface{} {
	performanceData := make(map[string]interface{})
	
	if len(metrics) == 0 {
		return performanceData
	}

	// Group metrics by name and calculate statistics
	metricGroups := make(map[string][]float64)
	for _, metric := range metrics {
		metricGroups[metric.Name] = append(metricGroups[metric.Name], metric.Value)
	}

	results := make(map[string]interface{})
	for name, values := range metricGroups {
		if len(values) == 0 {
			continue
		}

		// Calculate statistics
		sum := 0.0
		min := values[0]
		max := values[0]

		for _, value := range values {
			sum += value
			if value < min {
				min = value
			}
			if value > max {
				max = value
			}
		}

		avg := sum / float64(len(values))

		results[name] = map[string]interface{}{
			"count": len(values),
			"avg":   avg,
			"min":   min,
			"max":   max,
			"sum":   sum,
		}
	}

	performanceData["metrics"] = results
	performanceData["total_metrics"] = len(metrics)
	
	return performanceData
}

func getEventTypeFromQuery(c *gin.Context, defaultType string) *models.EventType {
	typeStr := c.DefaultQuery("event_type", defaultType)
	eventType := models.EventType(typeStr)
	return &eventType
}

// Helper methods for MetricsHandler

func (h *MetricsHandler) validateRequest(c *gin.Context, target interface{}) bool {
	if err := c.ShouldBindJSON(target); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid request format", err)
		return false
	}
	return true
}

func (h *MetricsHandler) respondWithError(c *gin.Context, statusCode int, message string, err error) {
	log.Error().
		Err(err).
		Str("path", c.Request.URL.Path).
		Str("method", c.Request.Method).
		Msg(message)

	errorResponse := models.NewErrorResponse(err)
	c.JSON(statusCode, errorResponse)
}

func (h *MetricsHandler) parseTimeRange(c *gin.Context) (time.Time, time.Time, error) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -1) // Default to 24 hours ago
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		endTime = time.Now() // Default to now
	}

	return startTime, endTime, nil
}

func (h *MetricsHandler) extractFilters(c *gin.Context) map[string]interface{} {
	filters := make(map[string]interface{})

	if orgID := c.Query("organization_id"); orgID != "" {
		filters["organization_id"] = orgID
	}

	if userID := c.Query("user_id"); userID != "" {
		filters["user_id"] = userID
	}

	if shopID := c.Query("shop_id"); shopID != "" {
		filters["shop_id"] = shopID
	}

	if source := c.Query("source"); source != "" {
		filters["source"] = source
	}

	return filters
}

func (h *MetricsHandler) exportMetricsAsJSON(c *gin.Context, metrics []*models.Metric) {
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=metrics.json")

	responses := make([]*models.MetricResponse, len(metrics))
	for i, metric := range metrics {
		response, err := metric.ToResponse()
		if err != nil {
			log.Error().Err(err).Str("metric_id", metric.ID.String()).Msg("Failed to convert metric to response")
			continue
		}
		responses[i] = response
	}

	c.JSON(http.StatusOK, gin.H{"metrics": responses})
}

func (h *MetricsHandler) exportMetricsAsCSV(c *gin.Context, metrics []*models.Metric) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=metrics.csv")

	// Write CSV header
	c.String(http.StatusOK, "id,name,type,value,unit,timestamp,created_at\n")

	// Write metric data
	for _, metric := range metrics {
		c.String(http.StatusOK, "%s,%s,%s,%f,%s,%s,%s\n",
			metric.ID.String(),
			metric.Name,
			metric.Type,
			metric.Value,
			metric.Unit,
			metric.Timestamp.Format(time.RFC3339),
			metric.CreatedAt.Format(time.RFC3339),
		)
	}
}

// Helper methods for ReportsHandler

func (h *ReportsHandler) validateRequest(c *gin.Context, target interface{}) bool {
	if err := c.ShouldBindJSON(target); err != nil {
		h.respondWithError(c, http.StatusBadRequest, "Invalid request format", err)
		return false
	}
	return true
}

func (h *ReportsHandler) respondWithError(c *gin.Context, statusCode int, message string, err error) {
	log.Error().
		Err(err).
		Str("path", c.Request.URL.Path).
		Str("method", c.Request.Method).
		Msg(message)

	errorResponse := models.NewErrorResponse(err)
	c.JSON(statusCode, errorResponse)
}

func (h *ReportsHandler) extractAnalyticsContext(c *gin.Context) *models.AnalyticsContext {
	analyticsCtx := models.NewAnalyticsContext()
	analyticsCtx.IPAddress = c.ClientIP()
	analyticsCtx.UserAgent = c.GetHeader("User-Agent")
	analyticsCtx.RequestID = c.GetHeader("X-Request-ID")

	// Extract user ID from context (set by auth middleware)
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uuid.UUID); ok {
			analyticsCtx.UserID = &uid
		}
	}

	return analyticsCtx
}

func (h *ReportsHandler) exportReportAsJSON(c *gin.Context, report *models.Report) {
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename="+report.Name+".json")

	response, err := report.ToResponse()
	if err != nil {
		h.respondWithError(c, http.StatusInternalServerError, "Failed to format report", err)
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *ReportsHandler) exportReportAsCSV(c *gin.Context, report *models.Report) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename="+report.Name+".csv")

	// Simplified CSV export - would need more sophisticated logic for different report types
	c.String(http.StatusOK, "Report: %s\nGenerated: %s\nStatus: %s\n",
		report.Name,
		report.CompletedAt.Format(time.RFC3339),
		report.Status,
	)
}

func (h *ReportsHandler) exportReportAsPDF(c *gin.Context, report *models.Report) {
	// PDF export would require a PDF generation library
	h.respondWithError(c, http.StatusNotImplemented, "PDF export not implemented", nil)
}

// Helper methods for DashboardHandler

func (h *DashboardHandler) parseTimeRange(c *gin.Context) (time.Time, time.Time, error) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -7) // Default to 7 days ago
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}
	} else {
		endTime = time.Now() // Default to now
	}

	return startTime, endTime, nil
}

func (h *DashboardHandler) respondWithError(c *gin.Context, statusCode int, message string, err error) {
	log.Error().
		Err(err).
		Str("path", c.Request.URL.Path).
		Str("method", c.Request.Method).
		Msg(message)

	errorResponse := models.NewErrorResponse(err)
	c.JSON(statusCode, errorResponse)
}

func (h *DashboardHandler) generateEventsOverTimeChart(startTime, endTime time.Time) map[string]interface{} {
	// Generate sample data for events over time
	points := make([]map[string]interface{}, 0)
	current := startTime
	interval := time.Hour

	for current.Before(endTime) {
		points = append(points, map[string]interface{}{
			"timestamp": current.Format(time.RFC3339),
			"value":     100 + (current.Hour() * 10), // Sample data
		})
		current = current.Add(interval)
	}

	return map[string]interface{}{
		"type":   "line",
		"title":  "Events Over Time",
		"points": points,
		"time_range": map[string]interface{}{
			"start": startTime,
			"end":   endTime,
		},
	}
}

func (h *DashboardHandler) generateUserActivityChart(startTime, endTime time.Time) map[string]interface{} {
	// Generate sample data for user activity
	return map[string]interface{}{
		"type":  "bar",
		"title": "User Activity",
		"data": []map[string]interface{}{
			{"category": "Page Views", "value": 1234},
			{"category": "API Calls", "value": 567},
			{"category": "Errors", "value": 23},
			{"category": "Conversions", "value": 89},
		},
		"time_range": map[string]interface{}{
			"start": startTime,
			"end":   endTime,
		},
	}
}