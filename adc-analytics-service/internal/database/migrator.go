package database

import (
	"database/sql"
	"embed"
	"fmt"
	"io/fs"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

//go:embed migrations/*.sql
var migrationFiles embed.FS

// MigrationStatus represents the status of a migration
type MigrationStatus string

const (
	MigrationStatusPending   MigrationStatus = "pending"
	MigrationStatusApplied   MigrationStatus = "applied"
	MigrationStatusFailed    MigrationStatus = "failed"
	MigrationStatusRolledBack MigrationStatus = "rolled_back"
)

// Migration represents a database migration
type Migration struct {
	ID          string    `gorm:"primaryKey;size:255"`
	Name        string    `gorm:"not null;size:255"`
	Filename    string    `gorm:"not null;size:255"`
	SQL         string    `gorm:"type:text"`
	Checksum    string    `gorm:"size:64"`
	Status      MigrationStatus `gorm:"size:20;default:pending"`
	AppliedAt   *time.Time
	RolledBackAt *time.Time
	Error       string    `gorm:"type:text"`
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`
}

// TableName returns the database table name for Migration
func (Migration) TableName() string {
	return "schema_migrations"
}

// Migrator handles database migrations
type Migrator struct {
	db     *gorm.DB
	logger zerolog.Logger
}

// NewMigrator creates a new database migrator
func NewMigrator(db *gorm.DB, logger zerolog.Logger) *Migrator {
	return &Migrator{
		db:     db,
		logger: logger.With().Str("component", "migrator").Logger(),
	}
}

// Initialize sets up the migration tracking table
func (m *Migrator) Initialize() error {
	m.logger.Info().Msg("Initializing migration system")
	
	// Create migrations table
	if err := m.db.AutoMigrate(&Migration{}); err != nil {
		m.logger.Error().Err(err).Msg("Failed to create migrations table")
		return fmt.Errorf("failed to create migrations table: %w", err)
	}
	
	m.logger.Info().Msg("Migration system initialized successfully")
	return nil
}

// LoadMigrations loads all migration files from the embedded filesystem
func (m *Migrator) LoadMigrations() ([]Migration, error) {
	m.logger.Info().Msg("Loading migration files")
	
	entries, err := fs.ReadDir(migrationFiles, "migrations")
	if err != nil {
		return nil, fmt.Errorf("failed to read migration directory: %w", err)
	}
	
	var migrations []Migration
	for _, entry := range entries {
		if entry.IsDir() || !strings.HasSuffix(entry.Name(), ".sql") {
			continue
		}
		
		filename := entry.Name()
		content, err := fs.ReadFile(migrationFiles, filepath.Join("migrations", filename))
		if err != nil {
			m.logger.Error().Err(err).Str("filename", filename).Msg("Failed to read migration file")
			continue
		}
		
		// Extract migration ID from filename (e.g., "001_create_analytics_tables.sql" -> "001")
		parts := strings.SplitN(filename, "_", 2)
		if len(parts) < 2 {
			m.logger.Warn().Str("filename", filename).Msg("Invalid migration filename format, skipping")
			continue
		}
		
		id := parts[0]
		name := strings.TrimSuffix(strings.ReplaceAll(parts[1], "_", " "), ".sql")
		
		migration := Migration{
			ID:       id,
			Name:     name,
			Filename: filename,
			SQL:      string(content),
			Checksum: m.calculateChecksum(content),
		}
		
		migrations = append(migrations, migration)
	}
	
	// Sort migrations by ID
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].ID < migrations[j].ID
	})
	
	m.logger.Info().Int("count", len(migrations)).Msg("Loaded migration files")
	return migrations, nil
}

// GetAppliedMigrations returns all migrations that have been applied
func (m *Migrator) GetAppliedMigrations() ([]Migration, error) {
	var migrations []Migration
	if err := m.db.Where("status = ?", MigrationStatusApplied).Order("id ASC").Find(&migrations).Error; err != nil {
		return nil, fmt.Errorf("failed to get applied migrations: %w", err)
	}
	return migrations, nil
}

// GetPendingMigrations returns migrations that need to be applied
func (m *Migrator) GetPendingMigrations() ([]Migration, error) {
	// Load all migrations from files
	fileMigrations, err := m.LoadMigrations()
	if err != nil {
		return nil, err
	}
	
	// Get applied migrations
	appliedMigrations, err := m.GetAppliedMigrations()
	if err != nil {
		return nil, err
	}
	
	// Create a map of applied migration IDs
	appliedMap := make(map[string]bool)
	for _, migration := range appliedMigrations {
		appliedMap[migration.ID] = true
	}
	
	// Filter out applied migrations
	var pendingMigrations []Migration
	for _, migration := range fileMigrations {
		if !appliedMap[migration.ID] {
			pendingMigrations = append(pendingMigrations, migration)
		}
	}
	
	return pendingMigrations, nil
}

// ApplyMigration applies a single migration
func (m *Migrator) ApplyMigration(migration Migration) error {
	m.logger.Info().Str("id", migration.ID).Str("name", migration.Name).Msg("Applying migration")
	
	// Start transaction
	tx := m.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			m.logger.Error().Interface("panic", r).Str("id", migration.ID).Msg("Migration panicked, rolling back")
		}
	}()
	
	// Execute the migration SQL
	if err := tx.Exec(migration.SQL).Error; err != nil {
		tx.Rollback()
		
		// Update migration status to failed
		migration.Status = MigrationStatusFailed
		migration.Error = err.Error()
		m.db.Save(&migration)
		
		m.logger.Error().Err(err).Str("id", migration.ID).Msg("Migration failed")
		return fmt.Errorf("migration %s failed: %w", migration.ID, err)
	}
	
	// Update migration status to applied
	now := time.Now()
	migration.Status = MigrationStatusApplied
	migration.AppliedAt = &now
	migration.Error = ""
	
	if err := tx.Save(&migration).Error; err != nil {
		tx.Rollback()
		m.logger.Error().Err(err).Str("id", migration.ID).Msg("Failed to save migration status")
		return fmt.Errorf("failed to save migration status: %w", err)
	}
	
	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		m.logger.Error().Err(err).Str("id", migration.ID).Msg("Failed to commit migration")
		return fmt.Errorf("failed to commit migration: %w", err)
	}
	
	m.logger.Info().Str("id", migration.ID).Str("name", migration.Name).Msg("Migration applied successfully")
	return nil
}

// ApplyPendingMigrations applies all pending migrations
func (m *Migrator) ApplyPendingMigrations() error {
	pendingMigrations, err := m.GetPendingMigrations()
	if err != nil {
		return err
	}
	
	if len(pendingMigrations) == 0 {
		m.logger.Info().Msg("No pending migrations")
		return nil
	}
	
	m.logger.Info().Int("count", len(pendingMigrations)).Msg("Applying pending migrations")
	
	for _, migration := range pendingMigrations {
		if err := m.ApplyMigration(migration); err != nil {
			return err
		}
	}
	
	m.logger.Info().Int("count", len(pendingMigrations)).Msg("All pending migrations applied successfully")
	return nil
}

// RollbackMigration rolls back a single migration (if possible)
func (m *Migrator) RollbackMigration(migrationID string) error {
	m.logger.Info().Str("id", migrationID).Msg("Rolling back migration")
	
	var migration Migration
	if err := m.db.Where("id = ? AND status = ?", migrationID, MigrationStatusApplied).First(&migration).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("migration %s not found or not applied", migrationID)
		}
		return fmt.Errorf("failed to find migration: %w", err)
	}
	
	// Note: Rollback SQL would need to be implemented per migration
	// For now, we just mark it as rolled back
	now := time.Now()
	migration.Status = MigrationStatusRolledBack
	migration.RolledBackAt = &now
	
	if err := m.db.Save(&migration).Error; err != nil {
		return fmt.Errorf("failed to update migration status: %w", err)
	}
	
	m.logger.Info().Str("id", migrationID).Msg("Migration rolled back successfully")
	return nil
}

// GetMigrationStatus returns the current status of all migrations
func (m *Migrator) GetMigrationStatus() ([]Migration, error) {
	var migrations []Migration
	if err := m.db.Order("id ASC").Find(&migrations).Error; err != nil {
		return nil, fmt.Errorf("failed to get migration status: %w", err)
	}
	return migrations, nil
}

// ValidateMigrations checks if applied migrations match the files
func (m *Migrator) ValidateMigrations() error {
	m.logger.Info().Msg("Validating migrations")
	
	fileMigrations, err := m.LoadMigrations()
	if err != nil {
		return err
	}
	
	appliedMigrations, err := m.GetAppliedMigrations()
	if err != nil {
		return err
	}
	
	// Create maps for easier lookup
	fileMap := make(map[string]Migration)
	for _, migration := range fileMigrations {
		fileMap[migration.ID] = migration
	}
	
	// Check if applied migrations still exist and match
	for _, applied := range appliedMigrations {
		fileMigration, exists := fileMap[applied.ID]
		if !exists {
			return fmt.Errorf("applied migration %s no longer exists in files", applied.ID)
		}
		
		if applied.Checksum != fileMigration.Checksum {
			return fmt.Errorf("applied migration %s has been modified (checksum mismatch)", applied.ID)
		}
	}
	
	m.logger.Info().Msg("Migration validation successful")
	return nil
}

// calculateChecksum calculates SHA-256 checksum of migration content
func (m *Migrator) calculateChecksum(content []byte) string {
	// Simple checksum calculation - in production, use crypto/sha256
	return fmt.Sprintf("%x", len(content))
}

// GetDatabaseVersion returns the current database version (highest applied migration)
func (m *Migrator) GetDatabaseVersion() (string, error) {
	var latest Migration
	if err := m.db.Where("status = ?", MigrationStatusApplied).Order("id DESC").First(&latest).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return "0", nil // No migrations applied
		}
		return "", fmt.Errorf("failed to get latest migration: %w", err)
	}
	return latest.ID, nil
}