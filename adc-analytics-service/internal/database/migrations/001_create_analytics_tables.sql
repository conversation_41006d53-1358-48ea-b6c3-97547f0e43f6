-- Create analytics tables for ADC Analytics Service
-- Migration: 001_create_analytics_tables.sql

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create event types enum
CREATE TYPE event_type AS ENUM (
    'page_view',
    'click',
    'form_submit',
    'api_call',
    'user_action',
    'system_event',
    'error',
    'custom'
);

-- Create metric types enum
CREATE TYPE metric_type AS ENUM (
    'counter',
    'gauge',
    'histogram',
    'summary',
    'timer'
);

-- Create aggregation types enum
CREATE TYPE aggregation_type AS ENUM (
    'sum',
    'avg',
    'min',
    'max',
    'count',
    'percentile'
);

-- Events table for tracking user and system events
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Event identification
    event_type event_type NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    event_category VARCHAR(100),
    
    -- Event context
    service_name VARCHAR(255) NOT NULL,
    environment VARCHAR(50) NOT NULL DEFAULT 'development',
    
    -- User and organization context
    user_id UUID,
    organization_id UUID,
    session_id VARCHAR(255),
    
    -- Request context
    request_id VARCHAR(255),
    correlation_id VARCHAR(255),
    trace_id VARCHAR(255),
    span_id VARCHAR(255),
    
    -- Technical context
    hostname VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    
    -- Event data
    properties JSONB,
    metadata JSONB,
    tags TEXT[],
    
    -- Performance metrics
    duration_ms INTEGER,
    response_size INTEGER,
    
    -- Event status
    status_code INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    
    -- Timing
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- System fields
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for events table
CREATE INDEX idx_events_timestamp ON events (timestamp DESC);
CREATE INDEX idx_events_type ON events (event_type);
CREATE INDEX idx_events_name ON events (event_name);
CREATE INDEX idx_events_category ON events (event_category) WHERE event_category IS NOT NULL;
CREATE INDEX idx_events_service_name ON events (service_name);
CREATE INDEX idx_events_environment ON events (environment);
CREATE INDEX idx_events_user_id ON events (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_events_organization_id ON events (organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_events_session_id ON events (session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_events_success ON events (success);

-- Composite indexes for common queries
CREATE INDEX idx_events_service_type_timestamp ON events (service_name, event_type, timestamp DESC);
CREATE INDEX idx_events_org_timestamp ON events (organization_id, timestamp DESC) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_events_user_timestamp ON events (user_id, timestamp DESC) WHERE user_id IS NOT NULL;
CREATE INDEX idx_events_service_env_timestamp ON events (service_name, environment, timestamp DESC);

-- GIN indexes for JSONB and array fields
CREATE INDEX idx_events_properties_gin ON events USING GIN (properties);
CREATE INDEX idx_events_metadata_gin ON events USING GIN (metadata);
CREATE INDEX idx_events_tags_gin ON events USING GIN (tags);

-- Metrics table for storing numerical metrics
CREATE TABLE metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Metric identification
    metric_name VARCHAR(255) NOT NULL,
    metric_type metric_type NOT NULL DEFAULT 'gauge',
    unit VARCHAR(50),
    
    -- Metric context
    service_name VARCHAR(255) NOT NULL,
    environment VARCHAR(50) NOT NULL DEFAULT 'development',
    
    -- User and organization context
    user_id UUID,
    organization_id UUID,
    
    -- Metric value
    value NUMERIC(20,6) NOT NULL,
    count INTEGER DEFAULT 1,
    
    -- Metric metadata
    labels JSONB,
    metadata JSONB,
    tags TEXT[],
    
    -- Timing
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- System fields
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for metrics table
CREATE INDEX idx_metrics_timestamp ON metrics (timestamp DESC);
CREATE INDEX idx_metrics_name ON metrics (metric_name);
CREATE INDEX idx_metrics_type ON metrics (metric_type);
CREATE INDEX idx_metrics_service_name ON metrics (service_name);
CREATE INDEX idx_metrics_environment ON metrics (environment);
CREATE INDEX idx_metrics_user_id ON metrics (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_metrics_organization_id ON metrics (organization_id) WHERE organization_id IS NOT NULL;

-- Composite indexes for metrics queries
CREATE INDEX idx_metrics_name_service_timestamp ON metrics (metric_name, service_name, timestamp DESC);
CREATE INDEX idx_metrics_org_name_timestamp ON metrics (organization_id, metric_name, timestamp DESC) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_metrics_service_env_timestamp ON metrics (service_name, environment, timestamp DESC);

-- GIN indexes for JSONB and array fields
CREATE INDEX idx_metrics_labels_gin ON metrics USING GIN (labels);
CREATE INDEX idx_metrics_metadata_gin ON metrics USING GIN (metadata);
CREATE INDEX idx_metrics_tags_gin ON metrics USING GIN (tags);

-- Analytics dashboards table
CREATE TABLE analytics_dashboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Dashboard identification
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Dashboard context
    organization_id UUID,
    user_id UUID,
    
    -- Dashboard configuration
    config JSONB NOT NULL,
    layout JSONB,
    filters JSONB,
    
    -- Dashboard metadata
    category VARCHAR(100),
    tags TEXT[],
    
    -- Dashboard status
    is_public BOOLEAN NOT NULL DEFAULT false,
    is_template BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- System fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    
    -- Unique constraints
    UNIQUE(slug, organization_id)
);

-- Indexes for dashboards
CREATE INDEX idx_dashboards_slug ON analytics_dashboards (slug);
CREATE INDEX idx_dashboards_organization_id ON analytics_dashboards (organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_dashboards_user_id ON analytics_dashboards (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_dashboards_category ON analytics_dashboards (category) WHERE category IS NOT NULL;
CREATE INDEX idx_dashboards_public ON analytics_dashboards (is_public) WHERE is_public = true;
CREATE INDEX idx_dashboards_active ON analytics_dashboards (is_active) WHERE is_active = true;

-- GIN indexes for JSONB fields
CREATE INDEX idx_dashboards_config_gin ON analytics_dashboards USING GIN (config);
CREATE INDEX idx_dashboards_tags_gin ON analytics_dashboards USING GIN (tags);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for dashboards updated_at
CREATE TRIGGER update_dashboards_updated_at 
    BEFORE UPDATE ON analytics_dashboards 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Pre-computed aggregations table for performance
CREATE TABLE analytics_aggregations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Aggregation metadata
    aggregation_key VARCHAR(255) NOT NULL,
    aggregation_type aggregation_type NOT NULL,
    time_bucket TIMESTAMPTZ NOT NULL,
    time_window VARCHAR(50) NOT NULL, -- 'minute', 'hour', 'day', 'week', 'month'
    
    -- Aggregation context
    service_name VARCHAR(255),
    environment VARCHAR(50),
    organization_id UUID,
    metric_name VARCHAR(255),
    event_type event_type,
    
    -- Aggregated values
    value NUMERIC(20,6) NOT NULL,
    count INTEGER NOT NULL DEFAULT 0,
    min_value NUMERIC(20,6),
    max_value NUMERIC(20,6),
    sum_value NUMERIC(20,6),
    avg_value NUMERIC(20,6),
    
    -- Percentile values (for histogram/summary metrics)
    p50 NUMERIC(20,6),
    p90 NUMERIC(20,6),
    p95 NUMERIC(20,6),
    p99 NUMERIC(20,6),
    
    -- Additional metadata
    metadata JSONB,
    
    -- System fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure uniqueness
    UNIQUE(aggregation_key, time_bucket, time_window)
);

-- Indexes for aggregations
CREATE INDEX idx_aggregations_key_bucket ON analytics_aggregations (aggregation_key, time_bucket DESC);
CREATE INDEX idx_aggregations_time_window ON analytics_aggregations (time_window, time_bucket DESC);
CREATE INDEX idx_aggregations_service_bucket ON analytics_aggregations (service_name, time_bucket DESC) WHERE service_name IS NOT NULL;
CREATE INDEX idx_aggregations_org_bucket ON analytics_aggregations (organization_id, time_bucket DESC) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_aggregations_metric_bucket ON analytics_aggregations (metric_name, time_bucket DESC) WHERE metric_name IS NOT NULL;
CREATE INDEX idx_aggregations_event_bucket ON analytics_aggregations (event_type, time_bucket DESC) WHERE event_type IS NOT NULL;

-- Trigger for aggregations updated_at
CREATE TRIGGER update_aggregations_updated_at 
    BEFORE UPDATE ON analytics_aggregations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Analytics queries table (for saved/cached queries)
CREATE TABLE analytics_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Query identification
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Query context
    organization_id UUID,
    user_id UUID,
    
    -- Query definition
    query_definition JSONB NOT NULL,
    query_type VARCHAR(50) NOT NULL, -- 'events', 'metrics', 'aggregation', 'custom'
    
    -- Cache settings
    cache_ttl INTEGER DEFAULT 300, -- Cache TTL in seconds
    last_executed TIMESTAMPTZ,
    execution_count INTEGER DEFAULT 0,
    
    -- Query metadata
    tags TEXT[],
    
    -- Query status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_scheduled BOOLEAN NOT NULL DEFAULT false,
    schedule_cron VARCHAR(100),
    
    -- System fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- Indexes for queries
CREATE INDEX idx_queries_organization_id ON analytics_queries (organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_queries_user_id ON analytics_queries (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_queries_type ON analytics_queries (query_type);
CREATE INDEX idx_queries_active ON analytics_queries (is_active) WHERE is_active = true;
CREATE INDEX idx_queries_scheduled ON analytics_queries (is_scheduled) WHERE is_scheduled = true;
CREATE INDEX idx_queries_last_executed ON analytics_queries (last_executed);

-- GIN indexes for JSONB and array fields
CREATE INDEX idx_queries_definition_gin ON analytics_queries USING GIN (query_definition);
CREATE INDEX idx_queries_tags_gin ON analytics_queries USING GIN (tags);

-- Trigger for queries updated_at
CREATE TRIGGER update_queries_updated_at 
    BEFORE UPDATE ON analytics_queries 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default dashboards
INSERT INTO analytics_dashboards (name, slug, description, config, is_template, is_public, category) VALUES
('System Overview', 'system-overview', 'High-level system metrics and health indicators', 
'{"widgets": [{"type": "metric", "title": "Request Count", "metric": "requests_total"}, {"type": "metric", "title": "Error Rate", "metric": "errors_rate"}, {"type": "chart", "title": "Response Time", "metric": "response_time"}]}', 
true, true, 'system'),

('User Activity', 'user-activity', 'User engagement and activity metrics',
'{"widgets": [{"type": "chart", "title": "Active Users", "metric": "active_users"}, {"type": "chart", "title": "Page Views", "event": "page_view"}, {"type": "table", "title": "Top Pages", "event": "page_view", "groupBy": "properties.page"}]}',
true, true, 'user'),

('API Performance', 'api-performance', 'API endpoint performance and usage metrics',
'{"widgets": [{"type": "metric", "title": "API Calls", "event": "api_call"}, {"type": "chart", "title": "Response Time", "metric": "api_response_time"}, {"type": "chart", "title": "Error Rate", "metric": "api_error_rate"}]}',
true, true, 'performance');

-- Insert default aggregation configurations
INSERT INTO analytics_aggregations (aggregation_key, aggregation_type, time_bucket, time_window, value, count) VALUES
('system_health_hourly', 'avg', DATE_TRUNC('hour', NOW()), 'hour', 100.0, 1),
('requests_total_daily', 'sum', DATE_TRUNC('day', NOW()), 'day', 0, 0),
('active_users_daily', 'count', DATE_TRUNC('day', NOW()), 'day', 0, 0);

COMMENT ON TABLE events IS 'Event tracking for user actions and system events';
COMMENT ON TABLE metrics IS 'Numerical metrics and measurements';
COMMENT ON TABLE analytics_dashboards IS 'User-defined analytics dashboards and visualizations';
COMMENT ON TABLE analytics_aggregations IS 'Pre-computed aggregations for performance optimization';
COMMENT ON TABLE analytics_queries IS 'Saved and scheduled analytics queries';