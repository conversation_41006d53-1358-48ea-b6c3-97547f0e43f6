package middleware

import (
	"net/http"
	"strings"

	"github.com/adc-analytics-service/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// SSOAuthMiddleware handles authentication (simplified version for compilation)
type SSOAuthMiddleware struct {
	ssoServiceURL string
	jwtSecret     string
}

// NewSSOAuth creates a new SSO authentication middleware
func NewSSOAuth(ssoServiceURL, jwtSecret string) *SSOAuthMiddleware {
	if ssoServiceURL != "" {
		log.Info().Str("sso_url", ssoServiceURL).Msg("SSO authentication middleware initialized")
	} else {
		log.Warn().Msg("SSO service URL not provided")
	}

	return &SSOAuthMiddleware{
		ssoServiceURL: ssoServiceURL,
		jwtSecret:     jwtSecret,
	}
}

// SSOAuth returns the SSO authentication middleware
func (m *SSOAuthMiddleware) SSOAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Unauthorized",
				"error":   "Authorization header required",
			})
			c.Abort()
			return
		}

		// Check for Bearer token format
		const bearerPrefix = "Bearer "
		if len(authHeader) < len(bearerPrefix) || authHeader[:len(bearerPrefix)] != bearerPrefix {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Unauthorized",
				"error":   "Invalid token format, Bearer token required",
			})
			c.Abort()
			return
		}

		// Extract the token
		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Unauthorized",
				"error":   "Token is required",
			})
			c.Abort()
			return
		}

		// TODO: Implement actual SSO validation when SDK is available
		// For now, accept any Bearer token for development
		log.Debug().Msg("Authentication bypassed for development")

		// Set mock user information in context
		userUUID := uuid.New()
		c.Set("user_id", userUUID)
		c.Set("user", &AuthUser{
			ID:       userUUID,
			Email:    "<EMAIL>",
			Username: "dev_user",
			FullName: "Development User",
			Role:     "admin",
		})
		c.Set("auth_type", "development")

		c.Next()
	}
}

// AuthUser represents an authenticated user
type AuthUser struct {
	ID       uuid.UUID `json:"id"`
	Email    string    `json:"email"`
	Username string    `json:"username"`
	FullName string    `json:"full_name"`
	Role     string    `json:"role"`
}

// GetAuthUser retrieves the authenticated user from the context
func GetAuthUser(c *gin.Context) (*AuthUser, bool) {
	if user, exists := c.Get("user"); exists {
		if authUser, ok := user.(*AuthUser); ok {
			return authUser, true
		}
	}

	// Fallback: try to construct from user_id
	if userID, exists := c.Get("user_id"); exists {
		if uuid, ok := userID.(uuid.UUID); ok {
			return &AuthUser{ID: uuid}, true
		}
	}

	return nil, false
}

// GetUserID retrieves the user ID from the context
func GetUserID(c *gin.Context) (uuid.UUID, bool) {
	if userID, exists := c.Get("user_id"); exists {
		if uuid, ok := userID.(uuid.UUID); ok {
			return uuid, true
		}
	}
	return uuid.Nil, false
}

// SSOAuth creates an SSO authentication middleware instance
func SSOAuth(ssoServiceURL string) gin.HandlerFunc {
	middleware := NewSSOAuth(ssoServiceURL, "")
	return middleware.SSOAuth()
}

// APIKeyAuthMiddleware handles API key authentication for service-to-service communication
type APIKeyAuthMiddleware struct {
	config *config.Config
}

// NewAPIKeyAuth creates a new API key authentication middleware
func NewAPIKeyAuth(cfg *config.Config) *APIKeyAuthMiddleware {
	return &APIKeyAuthMiddleware{
		config: cfg,
	}
}

// APIKeyAuth returns the API key authentication middleware
func (m *APIKeyAuthMiddleware) APIKeyAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get API key from header
		apiKey := c.GetHeader("X-API-Key")
		
		// Also check Authorization header for Bearer format
		if apiKey == "" {
			authHeader := c.GetHeader("Authorization")
			if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
				apiKey = strings.TrimPrefix(authHeader, "Bearer ")
			}
		}

		// Also check query parameter as fallback
		if apiKey == "" {
			apiKey = c.Query("api_key")
		}

		if apiKey == "" {
			log.Warn().Msg("API key authentication failed: no API key provided")
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Unauthorized",
				"error":   "API key required",
			})
			c.Abort()
			return
		}

		// Validate API key
		if !m.isValidAPIKey(apiKey) {
			log.Warn().
				Str("api_key_prefix", getAPIKeyPrefix(apiKey)).
				Msg("API key authentication failed: invalid API key")
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Unauthorized",
				"error":   "Invalid API key",
			})
			c.Abort()
			return
		}

		log.Debug().
			Str("api_key_prefix", getAPIKeyPrefix(apiKey)).
			Msg("API key authentication successful")

		// Set API key info in context
		c.Set("api_key", apiKey)
		c.Set("auth_type", "api_key")

		c.Next()
	}
}

// isValidAPIKey validates the API key against configured keys
func (m *APIKeyAuthMiddleware) isValidAPIKey(apiKey string) bool {
	if m.config == nil {
		return false
	}

	// Check against configured internal API keys
	for _, validKey := range m.config.InternalAPIKeys {
		if validKey != "" && apiKey == validKey {
			return true
		}
	}

	// TODO: Add database validation for external API keys
	return false
}

// getAPIKeyPrefix returns the first 8 characters of an API key for logging
func getAPIKeyPrefix(apiKey string) string {
	if len(apiKey) <= 8 {
		return apiKey
	}
	return apiKey[:8] + "..."
}

// APIKeyAuth creates an API key authentication middleware instance (convenience function)
func APIKeyAuth(cfg *config.Config) gin.HandlerFunc {
	middleware := NewAPIKeyAuth(cfg)
	return middleware.APIKeyAuth()
}