package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-analytics-service/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSSOAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing Authorization Header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Authorization header required",
		},
		{
			name:           "Invalid Token Format - No Bearer",
			authHeader:     "InvalidToken",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid token format, Bearer token required",
		},
		{
			name:           "Invalid Token Format - Bearer Only",
			authHeader:     "Bearer",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid token format, Bearer token required",
		},
		{
			name:           "Empty Token",
			authHeader:     "Bearer ",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Token is required",
		},
		{
			name:           "Valid Token Format (Development Mode)",
			authHeader:     "Bearer valid-analytics-token-123",
			expectedStatus: http.StatusOK,
			expectedError:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create middleware
			middleware := NewSSOAuth("http://localhost:9000", "test-secret")
			
			// Create test router
			router := gin.New()
			router.Use(middleware.SSOAuth())
			router.GET("/test", func(c *gin.Context) {
				// Verify user context is set for successful auth
				if tt.expectedStatus == http.StatusOK {
					user, exists := GetAuthUser(c)
					assert.True(t, exists, "User should be set in context")
					assert.NotNil(t, user, "User should not be nil")
					assert.NotEqual(t, uuid.Nil, user.ID, "User ID should be set")
					assert.Equal(t, "<EMAIL>", user.Email, "User email should be set")
					
					userID, exists := GetUserID(c)
					assert.True(t, exists, "User ID should be retrievable")
					assert.NotEqual(t, uuid.Nil, userID, "User ID should not be nil")
					
					authType, exists := c.Get("auth_type")
					assert.True(t, exists, "Auth type should be set")
					assert.Equal(t, "development", authType, "Auth type should be development")
				}
				
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// Create request
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			// Record response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestAPIKeyAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		InternalAPIKeys: []string{"analytics-internal-key-1", "analytics-internal-key-2"},
	}

	tests := []struct {
		name           string
		headers        map[string]string
		queryParams    map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing API Key",
			headers:        map[string]string{},
			queryParams:    map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "API key required",
		},
		{
			name: "Valid API Key in X-API-Key Header",
			headers: map[string]string{
				"X-API-Key": "analytics-internal-key-1",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Valid API Key in Authorization Header",
			headers: map[string]string{
				"Authorization": "Bearer analytics-internal-key-1",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "Valid API Key in Query Parameter",
			queryParams: map[string]string{"api_key": "analytics-internal-key-2"},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Invalid API Key",
			headers: map[string]string{
				"X-API-Key": "invalid-analytics-key",
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Invalid API key",
		},
		{
			name: "Empty API Key in Header",
			headers: map[string]string{
				"X-API-Key": "",
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "API key required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create middleware
			middleware := NewAPIKeyAuth(cfg)
			
			// Create test router
			router := gin.New()
			router.Use(middleware.APIKeyAuth())
			router.GET("/test", func(c *gin.Context) {
				// Verify API key context is set for successful auth
				if tt.expectedStatus == http.StatusOK {
					apiKey, exists := c.Get("api_key")
					assert.True(t, exists, "API key should be set in context")
					assert.NotEmpty(t, apiKey, "API key should not be empty")
					
					authType, exists := c.Get("auth_type")
					assert.True(t, exists, "Auth type should be set")
					assert.Equal(t, "api_key", authType, "Auth type should be api_key")
				}
				
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// Build URL with query parameters
			url := "/test"
			if len(tt.queryParams) > 0 {
				url += "?"
				for key, value := range tt.queryParams {
					url += key + "=" + value
				}
			}

			// Create request
			req := httptest.NewRequest("GET", url, nil)
			
			// Set headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Record response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestAnalyticsServiceSpecificScenarios(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("Analytics Events API Integration", func(t *testing.T) {
		cfg := &config.Config{
			InternalAPIKeys: []string{"analytics-internal-key-1"},
		}

		// Test analytics-specific route structure
		router := gin.New()

		// SSO-protected analytics routes
		api := router.Group("/api/v1")
		api.Use(SSOAuth("http://localhost:9000"))
		
		// Analytics endpoints
		analytics := api.Group("/analytics")
		analytics.GET("/events", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "events retrieved",
				"user_id": user.ID,
			})
		})
		
		analytics.POST("/events", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "event tracked",
				"user_id": user.ID,
			})
		})

		// Metrics endpoints
		metrics := api.Group("/metrics")
		metrics.POST("/", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "metric ingested",
				"user_id": user.ID,
			})
		})

		// Internal API key protected routes
		internal := router.Group("/internal/v1")
		internal.Use(APIKeyAuth(cfg))
		
		internal.POST("/metrics/bulk", func(c *gin.Context) {
			apiKey, exists := c.Get("api_key")
			require.True(t, exists, "API key should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "bulk metrics ingested",
				"api_key": getAPIKeyPrefix(apiKey.(string)),
			})
		})
		
		internal.POST("/events/batch", func(c *gin.Context) {
			apiKey, exists := c.Get("api_key")
			require.True(t, exists, "API key should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "batch events tracked",
				"api_key": getAPIKeyPrefix(apiKey.(string)),
			})
		})

		// Test analytics routes
		req := httptest.NewRequest("GET", "/api/v1/analytics/events", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "events retrieved")

		req = httptest.NewRequest("POST", "/api/v1/analytics/events", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "event tracked")

		// Test metrics routes
		req = httptest.NewRequest("POST", "/api/v1/metrics/", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "metric ingested")

		// Test internal bulk routes
		req = httptest.NewRequest("POST", "/internal/v1/metrics/bulk", nil)
		req.Header.Set("X-API-Key", "analytics-internal-key-1")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "bulk metrics ingested")

		req = httptest.NewRequest("POST", "/internal/v1/events/batch", nil)
		req.Header.Set("X-API-Key", "analytics-internal-key-1")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "batch events tracked")
	})

	t.Run("Organization Analytics Context", func(t *testing.T) {
		router := gin.New()
		router.Use(SSOAuth("http://localhost:9000"))
		
		// Organization-specific analytics
		orgAnalytics := router.Group("/api/v1/organizations/:organizationId/analytics")
		orgAnalytics.GET("/overview", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			
			orgID := c.Param("organizationId")
			assert.NotEmpty(t, orgID, "Organization ID should be provided")
			
			c.JSON(http.StatusOK, gin.H{
				"message":         "organization analytics overview",
				"organization_id": orgID,
				"user_id":        user.ID,
			})
		})
		
		orgAnalytics.GET("/usage", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			
			orgID := c.Param("organizationId")
			c.JSON(http.StatusOK, gin.H{
				"message":         "organization usage analytics",
				"organization_id": orgID,
				"user_id":        user.ID,
			})
		})

		// Test organization analytics routes
		req := httptest.NewRequest("GET", "/api/v1/organizations/org-123/analytics/overview", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "organization analytics overview")
		assert.Contains(t, w.Body.String(), "org-123")

		req = httptest.NewRequest("GET", "/api/v1/organizations/org-456/analytics/usage", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "organization usage analytics")
		assert.Contains(t, w.Body.String(), "org-456")
	})

	t.Run("Dashboard and Reports Authentication", func(t *testing.T) {
		router := gin.New()
		router.Use(SSOAuth("http://localhost:9000"))

		// Dashboard endpoints
		dashboard := router.Group("/api/v1/dashboard")
		dashboard.GET("/overview", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "dashboard overview",
				"user_id": user.ID,
			})
		})
		
		dashboard.GET("/kpis", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "dashboard KPIs",
				"user_id": user.ID,
			})
		})

		// Reports endpoints
		reports := router.Group("/api/v1/reports")
		reports.GET("/", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "reports list",
				"user_id": user.ID,
			})
		})
		
		reports.POST("/", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			c.JSON(http.StatusOK, gin.H{
				"message": "report created",
				"user_id": user.ID,
			})
		})

		// Test dashboard routes
		req := httptest.NewRequest("GET", "/api/v1/dashboard/overview", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "dashboard overview")

		req = httptest.NewRequest("GET", "/api/v1/dashboard/kpis", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "dashboard KPIs")

		// Test reports routes
		req = httptest.NewRequest("GET", "/api/v1/reports/", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "reports list")

		req = httptest.NewRequest("POST", "/api/v1/reports/", nil)
		req.Header.Set("Authorization", "Bearer test-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "report created")
	})
}

func TestAPIKeyValidation(t *testing.T) {
	cfg := &config.Config{
		InternalAPIKeys: []string{"analytics-internal-key-1", "analytics-internal-key-2"},
	}

	middleware := NewAPIKeyAuth(cfg)

	tests := []struct {
		name     string
		apiKey   string
		expected bool
	}{
		{
			name:     "Valid Key 1",
			apiKey:   "analytics-internal-key-1",
			expected: true,
		},
		{
			name:     "Valid Key 2",
			apiKey:   "analytics-internal-key-2",
			expected: true,
		},
		{
			name:     "Invalid Key",
			apiKey:   "invalid-analytics-key",
			expected: false,
		},
		{
			name:     "Empty Key",
			apiKey:   "",
			expected: false,
		},
		{
			name:     "Partial Match",
			apiKey:   "analytics-internal",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := middleware.isValidAPIKey(tt.apiKey)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHighVolumeAnalyticsScenarios(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("Concurrent Event Tracking", func(t *testing.T) {
		router := gin.New()
		router.Use(SSOAuth("http://localhost:9000"))
		
		eventCount := 0
		router.POST("/api/v1/analytics/events", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			require.True(t, exists, "User should be authenticated")
			eventCount++
			c.JSON(http.StatusOK, gin.H{
				"message":    "event tracked",
				"event_id":   eventCount,
				"user_id":    user.ID,
			})
		})

		// Simulate concurrent event tracking
		done := make(chan bool, 10)
		for i := 0; i < 10; i++ {
			go func() {
				defer func() { done <- true }()
				
				req := httptest.NewRequest("POST", "/api/v1/analytics/events", nil)
				req.Header.Set("Authorization", "Bearer test-token")
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				
				assert.Equal(t, http.StatusOK, w.Code)
				assert.Contains(t, w.Body.String(), "event tracked")
			}()
		}

		// Wait for all requests to complete
		for i := 0; i < 10; i++ {
			<-done
		}

		assert.Equal(t, 10, eventCount, "All events should be tracked")
	})

	t.Run("Bulk Operations with API Keys", func(t *testing.T) {
		cfg := &config.Config{
			InternalAPIKeys: []string{"analytics-bulk-key"},
		}

		router := gin.New()
		internal := router.Group("/internal/v1")
		internal.Use(APIKeyAuth(cfg))
		
		internal.POST("/metrics/bulk", func(c *gin.Context) {
			apiKey, exists := c.Get("api_key")
			require.True(t, exists, "API key should be authenticated")
			
			c.JSON(http.StatusOK, gin.H{
				"message":       "bulk operation completed",
				"metrics_count": 1000,
				"api_key":       getAPIKeyPrefix(apiKey.(string)),
			})
		})

		req := httptest.NewRequest("POST", "/internal/v1/metrics/bulk", nil)
		req.Header.Set("X-API-Key", "analytics-bulk-key")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "bulk operation completed")
		assert.Contains(t, w.Body.String(), "1000")
	})
}