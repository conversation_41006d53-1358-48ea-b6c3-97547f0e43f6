package middleware

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-analytics-service/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestCompleteAuthenticationFlow tests the complete authentication flow as it would be used in the main application
func TestCompleteAuthenticationFlow(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		InternalAPIKeys: []string{"analytics-test-key", "analytics-internal-key-1"},
		SSOServiceURL:   "http://localhost:9000",
	}

	// Create router similar to main application
	router := gin.New()

	// Add middleware
	router.Use(Recovery())
	router.Use(RequestID())
	router.Use(CORS())

	// Health endpoints (no auth required)
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "adc-analytics-service",
		})
	})

	// API routes with SSO authentication
	api := router.Group("/api/v1")
	api.Use(SSOAuth(cfg.SSOServiceURL))

	// Mock analytics endpoints
	analytics := api.Group("/analytics")
	{
		analytics.GET("/events", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "events retrieved",
				"user_id": user.ID,
			})
		})

		analytics.POST("/events", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "event tracked",
				"user_id": user.ID,
			})
		})
	}

	// Mock metrics endpoints
	metrics := api.Group("/metrics")
	{
		metrics.POST("/", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "metric ingested",
				"user_id": user.ID,
			})
		})

		metrics.GET("/query", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "metrics queried",
				"user_id": user.ID,
			})
		})
	}

	// Organization-specific analytics
	orgAnalytics := api.Group("/organizations/:organizationId/analytics")
	{
		orgAnalytics.GET("/overview", func(c *gin.Context) {
			user, exists := GetAuthUser(c)
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
				return
			}
			orgID := c.Param("organizationId")
			c.JSON(http.StatusOK, gin.H{
				"message":         "organization analytics overview",
				"organization_id": orgID,
				"user_id":        user.ID,
			})
		})
	}

	// Internal API endpoints (for service-to-service communication)
	internal := router.Group("/internal/v1")
	internal.Use(APIKeyAuth(cfg))
	{
		internal.POST("/metrics/bulk", func(c *gin.Context) {
			apiKey, exists := c.Get("api_key")
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "bulk metrics ingested",
				"api_key": getAPIKeyPrefix(apiKey.(string)),
			})
		})

		internal.POST("/events/batch", func(c *gin.Context) {
			apiKey, exists := c.Get("api_key")
			if !exists {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not authenticated"})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"message": "batch events tracked",
				"api_key": getAPIKeyPrefix(apiKey.(string)),
			})
		})

		internal.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"status": "ok"})
		})
	}

	// Run comprehensive tests
	t.Run("Public Health Endpoints", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "adc-analytics-service")
	})

	t.Run("SSO Authentication Flow", func(t *testing.T) {
		// Test without authentication
		req := httptest.NewRequest("GET", "/api/v1/analytics/events", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Authorization header required")

		// Test with valid authentication
		req = httptest.NewRequest("GET", "/api/v1/analytics/events", nil)
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "events retrieved")

		// Test POST endpoint
		eventData := map[string]interface{}{
			"type": "page_view",
			"name": "dashboard",
		}
		jsonData, _ := json.Marshal(eventData)

		req = httptest.NewRequest("POST", "/api/v1/analytics/events", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "event tracked")
	})

	t.Run("Metrics Endpoints Authentication", func(t *testing.T) {
		// Test metrics ingestion
		metricData := map[string]interface{}{
			"name":  "test_metric",
			"value": 42,
		}
		jsonData, _ := json.Marshal(metricData)

		req := httptest.NewRequest("POST", "/api/v1/metrics/", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "metric ingested")

		// Test metrics query
		req = httptest.NewRequest("GET", "/api/v1/metrics/query?metric=test_metric", nil)
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "metrics queried")
	})

	t.Run("Organization Context Authentication", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/organizations/org-123/analytics/overview", nil)
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "organization analytics overview")
		assert.Contains(t, w.Body.String(), "org-123")
	})

	t.Run("API Key Authentication Flow", func(t *testing.T) {
		// Test without API key
		req := httptest.NewRequest("POST", "/internal/v1/metrics/bulk", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "API key required")

		// Test with invalid API key
		req = httptest.NewRequest("POST", "/internal/v1/metrics/bulk", nil)
		req.Header.Set("X-API-Key", "invalid-key")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid API key")

		// Test with valid API key
		bulkData := map[string]interface{}{
			"metrics": []map[string]interface{}{
				{"name": "bulk_metric_1", "value": 10},
				{"name": "bulk_metric_2", "value": 20},
			},
		}
		jsonData, _ := json.Marshal(bulkData)

		req = httptest.NewRequest("POST", "/internal/v1/metrics/bulk", bytes.NewBuffer(jsonData))
		req.Header.Set("X-API-Key", "analytics-test-key")
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "bulk metrics ingested")

		// Test batch events endpoint
		eventsData := map[string]interface{}{
			"events": []map[string]interface{}{
				{"type": "page_view", "name": "home"},
				{"type": "click", "name": "button"},
			},
		}
		jsonData, _ = json.Marshal(eventsData)

		req = httptest.NewRequest("POST", "/internal/v1/events/batch", bytes.NewBuffer(jsonData))
		req.Header.Set("X-API-Key", "analytics-internal-key-1")
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "batch events tracked")

		// Test internal health endpoint
		req = httptest.NewRequest("GET", "/internal/v1/health", nil)
		req.Header.Set("X-API-Key", "analytics-test-key")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "ok")
	})

	t.Run("Cross-Authentication Validation", func(t *testing.T) {
		// Test that SSO token doesn't work on API key endpoints (it's treated as an invalid API key)
		req := httptest.NewRequest("POST", "/internal/v1/metrics/bulk", nil)
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		// The middleware extracts "valid-sso-token" from Bearer and treats it as an invalid API key
		assert.Contains(t, w.Body.String(), "Invalid API key")

		// Test that API key doesn't work on SSO endpoints
		req = httptest.NewRequest("GET", "/api/v1/analytics/events", nil)
		req.Header.Set("X-API-Key", "analytics-test-key")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "Authorization header required")
	})

	t.Run("Request ID and CORS Middleware", func(t *testing.T) {
		// Test that request ID is added
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.NotEmpty(t, w.Header().Get("X-Correlation-ID"))
		assert.NotEmpty(t, w.Header().Get("X-Request-ID"))

		// Test CORS headers
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "GET")
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Headers"), "Authorization")
	})

	t.Run("Authentication Context Persistence", func(t *testing.T) {
		// Test that user context persists through middleware chain
		req := httptest.NewRequest("GET", "/api/v1/analytics/events", nil)
		req.Header.Set("Authorization", "Bearer valid-sso-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		
		assert.Equal(t, "events retrieved", response["message"])
		assert.NotNil(t, response["user_id"])

		// Test that API key context persists
		req = httptest.NewRequest("POST", "/internal/v1/metrics/bulk", bytes.NewBuffer([]byte("{}")))
		req.Header.Set("X-API-Key", "analytics-test-key")
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		
		assert.Equal(t, "bulk metrics ingested", response["message"])
		assert.Equal(t, "analytic...", response["api_key"])
	})
}