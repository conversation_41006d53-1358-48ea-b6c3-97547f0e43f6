package storage

import (
	"context"
	"time"

	"github.com/adc-analytics-service/internal/models"
)

// Storage interface for main data operations
type Storage interface {
	// Event operations
	CreateEvent(ctx context.Context, event *models.Event) error
	CreateEvents(ctx context.Context, events []*models.Event) error
	GetEvents(ctx context.Context, query *models.EventQuery) ([]*models.Event, error)
	GetEvent(ctx context.Context, id string) (*models.Event, error)
	DeleteEvent(ctx context.Context, id string) error

	// Metric operations
	CreateMetric(ctx context.Context, metric *models.Metric) error
	CreateMetrics(ctx context.Context, metrics []*models.Metric) error
	GetMetrics(ctx context.Context, query *models.MetricQuery) ([]*models.Metric, error)
	GetMetric(ctx context.Context, id string) (*models.Metric, error)
	DeleteMetric(ctx context.Context, id string) error

	// Report operations
	CreateReport(ctx context.Context, report *models.Report) error
	GetReports(ctx context.Context, query *models.ReportQuery) ([]*models.Report, error)
	GetReport(ctx context.Context, id string) (*models.Report, error)
	UpdateReport(ctx context.Context, report *models.Report) error
	DeleteReport(ctx context.Context, id string) error

	// Dashboard operations
	CreateDashboard(ctx context.Context, dashboard *models.Dashboard) error
	GetDashboards(ctx context.Context, filters map[string]interface{}) ([]*models.Dashboard, error)
	GetDashboard(ctx context.Context, id string) (*models.Dashboard, error)
	UpdateDashboard(ctx context.Context, dashboard *models.Dashboard) error
	DeleteDashboard(ctx context.Context, id string) error

	// Close the storage connection
	Close() error
}

// Cache interface for caching operations
type Cache interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value string, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Clear(ctx context.Context) error
	Close() error
}

// CacheKeyBuilder helps build consistent cache keys
type CacheKeyBuilder struct {
	prefix string
}

// NewCacheKeyBuilder creates a new cache key builder
func NewCacheKeyBuilder(prefix string) *CacheKeyBuilder {
	return &CacheKeyBuilder{prefix: prefix}
}

// Build builds a cache key with the prefix
func (b *CacheKeyBuilder) Build(key string) string {
	return b.prefix + key
}