package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds all configuration values for the analytics service
type Config struct {
	// Server configuration
	Port        string `json:"port"`
	Host        string `json:"host"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`

	// Database configuration
	DatabaseURL string `json:"database_url"`

	// Redis configuration
	RedisURL string `json:"redis_url"`

	// Time series database configuration (InfluxDB)
	TimeSeriesEnabled bool   `json:"timeseries_enabled"`
	InfluxDBURL       string `json:"influxdb_url"`
	InfluxDBToken     string `json:"influxdb_token"`
	InfluxDBOrg       string `json:"influxdb_org"`
	InfluxDBBucket    string `json:"influxdb_bucket"`

	// ADC SSO Service integration
	SSOServiceURL   string `json:"sso_service_url"`
	SSOClientID     string `json:"sso_client_id"`
	SSOClientSecret string `json:"sso_client_secret"`

	// Multi-Languages service integration
	MultiLangServiceURL   string `json:"multilang_service_url"`
	MultiLangInternalKey  string `json:"multilang_internal_key"`
	MultiLangProjectID    string `json:"multilang_project_id"`
	MultiLangOrganization string `json:"multilang_organization"`

	// Internal API keys for service-to-service communication
	InternalAPIKeys []string `json:"internal_api_keys"`

	// Cache configuration
	CacheTTL          time.Duration `json:"cache_ttl"`
	CacheMaxMemory    string        `json:"cache_max_memory"`
	MetricsCacheTTL   time.Duration `json:"metrics_cache_ttl"`
	AnalyticsCacheTTL time.Duration `json:"analytics_cache_ttl"`

	// Analytics-specific configuration
	EventBatchSize        int           `json:"event_batch_size"`
	EventFlushInterval    time.Duration `json:"event_flush_interval"`
	MetricsRetentionDays  int           `json:"metrics_retention_days"`
	EventsRetentionDays   int           `json:"events_retention_days"`
	AggregationInterval   time.Duration `json:"aggregation_interval"`
	CleanupInterval       time.Duration `json:"cleanup_interval"`
	MaxQueryTimeRange     time.Duration `json:"max_query_time_range"`
	MaxMetricsPerRequest  int           `json:"max_metrics_per_request"`
	MaxEventsPerRequest   int           `json:"max_events_per_request"`

	// Real-time processing
	RealtimeEnabled      bool          `json:"realtime_enabled"`
	RealtimeWindowSize   time.Duration `json:"realtime_window_size"`
	RealtimeBufferSize   int           `json:"realtime_buffer_size"`
	RealtimeFlushTimeout time.Duration `json:"realtime_flush_timeout"`

	// Data export configuration
	ExportEnabled       bool   `json:"export_enabled"`
	ExportStoragePath   string `json:"export_storage_path"`
	ExportRetentionDays int    `json:"export_retention_days"`
	MaxExportFileSize   int64  `json:"max_export_file_size"`

	// Security configuration
	CORSAllowedOrigins []string `json:"cors_allowed_origins"`
	RateLimitRequests  int      `json:"rate_limit_requests"`
	RateLimitWindow    string   `json:"rate_limit_window"`
	DataEncryptionKey  string   `json:"data_encryption_key"`

	// Performance configuration
	MaxConnections    int           `json:"max_connections"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	IdleTimeout       time.Duration `json:"idle_timeout"`
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	WorkerPoolSize    int           `json:"worker_pool_size"`

	// Monitoring configuration
	MetricsEnabled    bool   `json:"metrics_enabled"`
	MetricsPort       string `json:"metrics_port"`
	ProfilingEnabled  bool   `json:"profiling_enabled"`
	ProfilingPort     string `json:"profiling_port"`
	TracingEnabled    bool   `json:"tracing_enabled"`
	TracingEndpoint   string `json:"tracing_endpoint"`

	// Development settings
	DBLogQueries  bool `json:"db_log_queries"`
	EnableSwagger bool `json:"enable_swagger"`
	DebugMode     bool `json:"debug_mode"`
	MockData      bool `json:"mock_data"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		// Server defaults
		Port:        getEnvOrDefault("PORT", "9030"),
		Host:        getEnvOrDefault("HOST", "0.0.0.0"),
		Environment: getEnvOrDefault("ENVIRONMENT", "development"),
		LogLevel:    getEnvOrDefault("LOG_LEVEL", "info"),

		// Database
		DatabaseURL: getEnvOrDefault("DATABASE_URL", "postgresql://user:password@localhost/adc_analytics"),

		// Redis
		RedisURL: getEnvOrDefault("REDIS_URL", "redis://localhost:6379/0"),

		// Time series database
		TimeSeriesEnabled: parseBoolOrDefault("TIMESERIES_ENABLED", true),
		InfluxDBURL:       getEnvOrDefault("INFLUXDB_URL", "http://localhost:8086"),
		InfluxDBToken:     getEnvOrDefault("INFLUXDB_TOKEN", ""),
		InfluxDBOrg:       getEnvOrDefault("INFLUXDB_ORG", "adc-platform"),
		InfluxDBBucket:    getEnvOrDefault("INFLUXDB_BUCKET", "analytics"),

		// SSO Service
		SSOServiceURL:   getEnvOrDefault("SSO_SERVICE_URL", "http://localhost:9000"),
		SSOClientID:     getEnvOrDefault("SSO_CLIENT_ID", "adc-analytics-service"),
		SSOClientSecret: getEnvOrDefault("SSO_CLIENT_SECRET", "analytics-service-secret"),

		// Multi-Languages service integration
		MultiLangServiceURL:   getEnvOrDefault("MULTILANG_SERVICE_URL", "http://localhost:8300"),
		MultiLangInternalKey:  getEnvOrDefault("MULTILANG_INTERNAL_KEY", "adc-analytics-internal-2024"),
		MultiLangProjectID:    getEnvOrDefault("MULTILANG_PROJECT_ID", "analytics-service-project-id"),
		MultiLangOrganization: getEnvOrDefault("MULTILANG_ORGANIZATION", "adc-analytics"),

		// Internal API keys
		InternalAPIKeys: parseStringSlice(getEnvOrDefault("INTERNAL_API_KEYS", "analytics-internal-key-1,analytics-internal-key-2")),

		// Cache defaults
		CacheTTL:          parseDurationOrDefault("CACHE_TTL", "300s"),
		CacheMaxMemory:    getEnvOrDefault("CACHE_MAX_MEMORY", "512mb"),
		MetricsCacheTTL:   parseDurationOrDefault("METRICS_CACHE_TTL", "60s"),
		AnalyticsCacheTTL: parseDurationOrDefault("ANALYTICS_CACHE_TTL", "300s"),

		// Analytics-specific defaults
		EventBatchSize:        parseIntOrDefault("EVENT_BATCH_SIZE", 1000),
		EventFlushInterval:    parseDurationOrDefault("EVENT_FLUSH_INTERVAL", "10s"),
		MetricsRetentionDays:  parseIntOrDefault("METRICS_RETENTION_DAYS", 90),
		EventsRetentionDays:   parseIntOrDefault("EVENTS_RETENTION_DAYS", 365),
		AggregationInterval:   parseDurationOrDefault("AGGREGATION_INTERVAL", "5m"),
		CleanupInterval:       parseDurationOrDefault("CLEANUP_INTERVAL", "24h"),
		MaxQueryTimeRange:     parseDurationOrDefault("MAX_QUERY_TIME_RANGE", "30d"),
		MaxMetricsPerRequest:  parseIntOrDefault("MAX_METRICS_PER_REQUEST", 10000),
		MaxEventsPerRequest:   parseIntOrDefault("MAX_EVENTS_PER_REQUEST", 5000),

		// Real-time processing defaults
		RealtimeEnabled:      parseBoolOrDefault("REALTIME_ENABLED", true),
		RealtimeWindowSize:   parseDurationOrDefault("REALTIME_WINDOW_SIZE", "1m"),
		RealtimeBufferSize:   parseIntOrDefault("REALTIME_BUFFER_SIZE", 10000),
		RealtimeFlushTimeout: parseDurationOrDefault("REALTIME_FLUSH_TIMEOUT", "5s"),

		// Data export defaults
		ExportEnabled:       parseBoolOrDefault("EXPORT_ENABLED", true),
		ExportStoragePath:   getEnvOrDefault("EXPORT_STORAGE_PATH", "/tmp/analytics-exports"),
		ExportRetentionDays: parseIntOrDefault("EXPORT_RETENTION_DAYS", 30),
		MaxExportFileSize:   int64(parseIntOrDefault("MAX_EXPORT_FILE_SIZE", 104857600)), // 100MB

		// Security defaults
		CORSAllowedOrigins: parseStringSlice(getEnvOrDefault("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:3300,http://localhost:3800")),
		RateLimitRequests:  parseIntOrDefault("RATE_LIMIT_REQUESTS", 5000),
		RateLimitWindow:    getEnvOrDefault("RATE_LIMIT_WINDOW", "1h"),
		DataEncryptionKey:  getEnvOrDefault("DATA_ENCRYPTION_KEY", ""),

		// Performance defaults
		MaxConnections:    parseIntOrDefault("MAX_CONNECTIONS", 200),
		ConnectionTimeout: parseDurationOrDefault("CONNECTION_TIMEOUT", "30s"),
		IdleTimeout:       parseDurationOrDefault("IDLE_TIMEOUT", "300s"),
		ReadTimeout:       parseDurationOrDefault("READ_TIMEOUT", "30s"),
		WriteTimeout:      parseDurationOrDefault("WRITE_TIMEOUT", "30s"),
		WorkerPoolSize:    parseIntOrDefault("WORKER_POOL_SIZE", 10),

		// Monitoring defaults
		MetricsEnabled:   parseBoolOrDefault("METRICS_ENABLED", true),
		MetricsPort:      getEnvOrDefault("METRICS_PORT", "9401"),
		ProfilingEnabled: parseBoolOrDefault("PROFILING_ENABLED", false),
		ProfilingPort:    getEnvOrDefault("PROFILING_PORT", "6060"),
		TracingEnabled:   parseBoolOrDefault("TRACING_ENABLED", false),
		TracingEndpoint:  getEnvOrDefault("TRACING_ENDPOINT", ""),

		// Development defaults
		DBLogQueries:  parseBoolOrDefault("DB_LOG_QUERIES", false),
		EnableSwagger: parseBoolOrDefault("ENABLE_SWAGGER", true),
		DebugMode:     parseBoolOrDefault("DEBUG_MODE", false),
		MockData:      parseBoolOrDefault("MOCK_DATA", false),
	}

	// Validate required configuration
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// validate checks that all required configuration values are present and valid
func (c *Config) validate() error {
	if c.DatabaseURL == "" {
		return fmt.Errorf("DATABASE_URL is required")
	}

	if c.RedisURL == "" {
		return fmt.Errorf("REDIS_URL is required")
	}

	if c.SSOServiceURL == "" {
		return fmt.Errorf("SSO_SERVICE_URL is required")
	}

	if c.SSOClientID == "" {
		return fmt.Errorf("SSO_CLIENT_ID is required")
	}

	if c.SSOClientSecret == "" {
		return fmt.Errorf("SSO_CLIENT_SECRET is required")
	}

	if len(c.InternalAPIKeys) == 0 {
		return fmt.Errorf("at least one INTERNAL_API_KEY is required")
	}

	if c.Port == "" {
		return fmt.Errorf("PORT is required")
	}

	// Validate time series configuration if enabled
	if c.TimeSeriesEnabled {
		if c.InfluxDBURL == "" {
			return fmt.Errorf("INFLUXDB_URL is required when time series is enabled")
		}
		if c.InfluxDBOrg == "" {
			return fmt.Errorf("INFLUXDB_ORG is required when time series is enabled")
		}
		if c.InfluxDBBucket == "" {
			return fmt.Errorf("INFLUXDB_BUCKET is required when time series is enabled")
		}
	}

	return nil
}

// IsProduction returns true if the service is running in production mode
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment returns true if the service is running in development mode
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// Helper functions for parsing environment variables

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func parseBoolOrDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func parseDurationOrDefault(key string, defaultValue string) time.Duration {
	value := getEnvOrDefault(key, defaultValue)
	if parsed, err := time.ParseDuration(value); err == nil {
		return parsed
	}
	// Fallback to default if parsing fails
	if parsed, err := time.ParseDuration(defaultValue); err == nil {
		return parsed
	}
	return 5 * time.Minute // Ultimate fallback
}

func parseStringSlice(value string) []string {
	if value == "" {
		return []string{}
	}
	return strings.Split(value, ",")
}
