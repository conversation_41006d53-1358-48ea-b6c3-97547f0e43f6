package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// MetricType defines the type of metric
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

// AggregationType defines how metrics should be aggregated
type AggregationType string

const (
	AggregationSum     AggregationType = "sum"
	AggregationAvg     AggregationType = "avg"
	AggregationMin     AggregationType = "min"
	AggregationMax     AggregationType = "max"
	AggregationCount   AggregationType = "count"
	AggregationP50     AggregationType = "p50"
	AggregationP90     AggregationType = "p90"
	AggregationP95     AggregationType = "p95"
	AggregationP99     AggregationType = "p99"
)

// Metric represents a time-series metric
type Metric struct {
	ID             uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string          `json:"name" gorm:"not null;index"`
	Type           MetricType      `json:"type" gorm:"not null"`
	Value          float64         `json:"value" gorm:"not null"`
	Unit           string          `json:"unit,omitempty"`
	Tags           string          `json:"tags,omitempty" gorm:"type:jsonb"`
	UserID         *uuid.UUID      `json:"user_id,omitempty" gorm:"type:uuid;index"`
	OrganizationID *uuid.UUID      `json:"organization_id,omitempty" gorm:"type:uuid;index"`
	ShopID         *uuid.UUID      `json:"shop_id,omitempty" gorm:"type:uuid;index"`
	Source         string          `json:"source,omitempty"`
	Timestamp      time.Time       `json:"timestamp" gorm:"not null;index"`
	CreatedAt      time.Time       `json:"created_at" gorm:"autoCreateTime"`
}

// TableName returns the table name for the Metric model
func (Metric) TableName() string {
	return "analytics_metrics"
}

// GetParsedTags returns the metric tags parsed as a map
func (m *Metric) GetParsedTags() (map[string]string, error) {
	if m.Tags == "" {
		return nil, nil
	}

	var tags map[string]string
	err := json.Unmarshal([]byte(m.Tags), &tags)
	return tags, err
}

// MetricRequest represents a request to create a metric
type MetricRequest struct {
	Name           string             `json:"name" binding:"required,min=1,max=255"`
	Type           MetricType         `json:"type" binding:"required"`
	Value          float64            `json:"value" binding:"required"`
	Unit           string             `json:"unit,omitempty"`
	Tags           map[string]string  `json:"tags,omitempty"`
	UserID         *uuid.UUID         `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID         `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID         `json:"shop_id,omitempty"`
	Source         string             `json:"source,omitempty"`
	Timestamp      *time.Time         `json:"timestamp,omitempty"`
}

// ToMetric converts a MetricRequest to a Metric model
func (r *MetricRequest) ToMetric() (*Metric, error) {
	// Serialize tags to string
	var tagsStr string
	if r.Tags != nil {
		tagsBytes, err := json.Marshal(r.Tags)
		if err != nil {
			return nil, err
		}
		tagsStr = string(tagsBytes)
	}

	// Set timestamp to now if not provided
	timestamp := time.Now()
	if r.Timestamp != nil {
		timestamp = *r.Timestamp
	}

	return &Metric{
		Name:           r.Name,
		Type:           r.Type,
		Value:          r.Value,
		Unit:           r.Unit,
		Tags:           tagsStr,
		UserID:         r.UserID,
		OrganizationID: r.OrganizationID,
		ShopID:         r.ShopID,
		Source:         r.Source,
		Timestamp:      timestamp,
	}, nil
}

// MetricResponse represents a metric in API responses
type MetricResponse struct {
	ID             uuid.UUID         `json:"id"`
	Name           string            `json:"name"`
	Type           MetricType        `json:"type"`
	Value          float64           `json:"value"`
	Unit           string            `json:"unit,omitempty"`
	Tags           map[string]string `json:"tags,omitempty"`
	UserID         *uuid.UUID        `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID        `json:"shop_id,omitempty"`
	Source         string            `json:"source,omitempty"`
	Timestamp      time.Time         `json:"timestamp"`
	CreatedAt      time.Time         `json:"created_at"`
}

// ToResponse converts a Metric to a MetricResponse
func (m *Metric) ToResponse() (*MetricResponse, error) {
	tags, err := m.GetParsedTags()
	if err != nil {
		return nil, err
	}

	return &MetricResponse{
		ID:             m.ID,
		Name:           m.Name,
		Type:           m.Type,
		Value:          m.Value,
		Unit:           m.Unit,
		Tags:           tags,
		UserID:         m.UserID,
		OrganizationID: m.OrganizationID,
		ShopID:         m.ShopID,
		Source:         m.Source,
		Timestamp:      m.Timestamp,
		CreatedAt:      m.CreatedAt,
	}, nil
}

// MetricQuery represents parameters for querying metrics
type MetricQuery struct {
	Name           string             `form:"name"`
	Type           *MetricType        `form:"type"`
	UserID         *uuid.UUID         `form:"user_id"`
	OrganizationID *uuid.UUID         `form:"organization_id"`
	ShopID         *uuid.UUID         `form:"shop_id"`
	Source         string             `form:"source"`
	Aggregation    *AggregationType   `form:"aggregation"`
	GroupBy        []string           `form:"group_by"`
	StartTime      *time.Time         `form:"start_time"`
	EndTime        *time.Time         `form:"end_time"`
	Interval       string             `form:"interval"` // 1m, 5m, 1h, 1d, etc.
	Limit          int                `form:"limit"`
	Offset         int                `form:"offset"`
}

// BatchMetricRequest represents a request to create multiple metrics
type BatchMetricRequest struct {
	Metrics []MetricRequest `json:"metrics" binding:"required,dive"`
}

// AggregatedMetric represents an aggregated metric value
type AggregatedMetric struct {
	Name        string              `json:"name"`
	Type        MetricType          `json:"type"`
	Aggregation AggregationType     `json:"aggregation"`
	Value       float64             `json:"value"`
	Unit        string              `json:"unit,omitempty"`
	Tags        map[string]string   `json:"tags,omitempty"`
	GroupBy     map[string]string   `json:"group_by,omitempty"`
	Timestamp   time.Time           `json:"timestamp"`
	Count       int64               `json:"count"`
}

// TimeSeriesPoint represents a single point in a time series
type TimeSeriesPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// TimeSeries represents a time series of metric values
type TimeSeries struct {
	Name        string              `json:"name"`
	Type        MetricType          `json:"type"`
	Unit        string              `json:"unit,omitempty"`
	Tags        map[string]string   `json:"tags,omitempty"`
	Points      []TimeSeriesPoint   `json:"points"`
	Aggregation AggregationType     `json:"aggregation,omitempty"`
}