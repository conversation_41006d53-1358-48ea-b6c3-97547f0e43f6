package models

import (
	"time"
	"github.com/google/uuid"
)

// AutoTranslationSettings represents the auto-translation configuration for analytics service
type AutoTranslationSettings struct {
	ID                   uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ServiceID            string    `json:"service_id" gorm:"not null;index"`
	Enabled              bool      `json:"enabled" gorm:"default:false"`
	TargetLanguage       string    `json:"target_language" gorm:"default:'en'"`
	ConfidenceThreshold  float64   `json:"confidence_threshold" gorm:"default:0.8"`
	
	// Analytics-specific translation settings
	TranslateDashboardTitles     bool `json:"translate_dashboard_titles" gorm:"default:true"`     // Dashboard titles
	TranslateDashboardDescriptions bool `json:"translate_dashboard_descriptions" gorm:"default:true"` // Dashboard descriptions
	TranslateMetricNames         bool `json:"translate_metric_names" gorm:"default:true"`         // Metric names and labels
	TranslateMetricDescriptions  bool `json:"translate_metric_descriptions" gorm:"default:true"`  // Metric descriptions
	TranslateChartTitles         bool `json:"translate_chart_titles" gorm:"default:true"`         // Chart titles
	TranslateChartLabels         bool `json:"translate_chart_labels" gorm:"default:true"`         // Chart axis labels
	TranslateReportTitles        bool `json:"translate_report_titles" gorm:"default:true"`        // Report titles
	TranslateReportDescriptions  bool `json:"translate_report_descriptions" gorm:"default:true"`  // Report descriptions
	TranslateEventNames          bool `json:"translate_event_names" gorm:"default:true"`          // Event names
	TranslateEventDescriptions   bool `json:"translate_event_descriptions" gorm:"default:true"`   // Event descriptions
	TranslateFilterLabels        bool `json:"translate_filter_labels" gorm:"default:true"`        // Filter labels
	TranslateErrorMessages       bool `json:"translate_error_messages" gorm:"default:true"`       // Error messages
	TranslateAlertMessages       bool `json:"translate_alert_messages" gorm:"default:true"`       // Alert messages
	TranslateInsightTitles       bool `json:"translate_insight_titles" gorm:"default:true"`       // Insight titles
	TranslateInsightDescriptions bool `json:"translate_insight_descriptions" gorm:"default:true"` // Insight descriptions
	TranslateTimeRangeLabels     bool `json:"translate_time_range_labels" gorm:"default:true"`    // Time range labels
	
	// Enabled content types for translation
	EnabledDashboardTypes []string `json:"enabled_dashboard_types" gorm:"type:text[]"` // overview, performance, usage, etc.
	EnabledMetricTypes    []string `json:"enabled_metric_types" gorm:"type:text[]"`    // counter, gauge, histogram, etc.
	EnabledChartTypes     []string `json:"enabled_chart_types" gorm:"type:text[]"`     // line, bar, pie, etc.
	EnabledReportTypes    []string `json:"enabled_report_types" gorm:"type:text[]"`    // daily, weekly, monthly, etc.
	EnabledEventCategories []string `json:"enabled_event_categories" gorm:"type:text[]"` // user, system, api, etc.
	EnabledFilterTypes    []string `json:"enabled_filter_types" gorm:"type:text[]"`    // date, status, category, etc.
	EnabledAlertTypes     []string `json:"enabled_alert_types" gorm:"type:text[]"`     // warning, error, info, etc.
	EnabledInsightTypes   []string `json:"enabled_insight_types" gorm:"type:text[]"`   // trend, anomaly, recommendation, etc.
	EnabledTimeRangeTypes []string `json:"enabled_time_range_types" gorm:"type:text[]"` // relative, absolute, custom, etc.
	
	// Processing settings
	BatchProcessing    bool `json:"batch_processing" gorm:"default:true"`
	RealTimeMode       bool `json:"real_time_mode" gorm:"default:false"`
	SmartTriggers      bool `json:"smart_triggers" gorm:"default:true"`
	
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// AutoTranslationStatistics represents translation statistics for analytics service
type AutoTranslationStatistics struct {
	ID                        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ServiceID                 string    `json:"service_id" gorm:"not null;index"`
	
	// Translation counts by type
	TranslatedDashboardTitles     int64 `json:"translated_dashboard_titles" gorm:"default:0"`
	TranslatedDashboardDescriptions int64 `json:"translated_dashboard_descriptions" gorm:"default:0"`
	TranslatedMetricNames         int64 `json:"translated_metric_names" gorm:"default:0"`
	TranslatedMetricDescriptions  int64 `json:"translated_metric_descriptions" gorm:"default:0"`
	TranslatedChartTitles         int64 `json:"translated_chart_titles" gorm:"default:0"`
	TranslatedChartLabels         int64 `json:"translated_chart_labels" gorm:"default:0"`
	TranslatedReportTitles        int64 `json:"translated_report_titles" gorm:"default:0"`
	TranslatedReportDescriptions  int64 `json:"translated_report_descriptions" gorm:"default:0"`
	TranslatedEventNames          int64 `json:"translated_event_names" gorm:"default:0"`
	TranslatedEventDescriptions   int64 `json:"translated_event_descriptions" gorm:"default:0"`
	TranslatedFilterLabels        int64 `json:"translated_filter_labels" gorm:"default:0"`
	TranslatedErrorMessages       int64 `json:"translated_error_messages" gorm:"default:0"`
	TranslatedAlertMessages       int64 `json:"translated_alert_messages" gorm:"default:0"`
	TranslatedInsightTitles       int64 `json:"translated_insight_titles" gorm:"default:0"`
	TranslatedInsightDescriptions int64 `json:"translated_insight_descriptions" gorm:"default:0"`
	TranslatedTimeRangeLabels     int64 `json:"translated_time_range_labels" gorm:"default:0"`
	
	// Failure tracking
	FailedTranslations int64 `json:"failed_translations" gorm:"default:0"`
	
	// Quality metrics
	AverageConfidence  float64 `json:"average_confidence" gorm:"default:0.0"`
	SuccessRate        float64 `json:"success_rate" gorm:"default:0.0"`
	
	// Coverage metrics
	TotalDashboardTitles     int64 `json:"total_dashboard_titles" gorm:"default:0"`
	TotalDashboardDescriptions int64 `json:"total_dashboard_descriptions" gorm:"default:0"`
	TotalMetricNames         int64 `json:"total_metric_names" gorm:"default:0"`
	TotalMetricDescriptions  int64 `json:"total_metric_descriptions" gorm:"default:0"`
	TotalChartTitles         int64 `json:"total_chart_titles" gorm:"default:0"`
	TotalChartLabels         int64 `json:"total_chart_labels" gorm:"default:0"`
	TotalReportTitles        int64 `json:"total_report_titles" gorm:"default:0"`
	TotalReportDescriptions  int64 `json:"total_report_descriptions" gorm:"default:0"`
	TotalEventNames          int64 `json:"total_event_names" gorm:"default:0"`
	TotalEventDescriptions   int64 `json:"total_event_descriptions" gorm:"default:0"`
	TotalFilterLabels        int64 `json:"total_filter_labels" gorm:"default:0"`
	TotalErrorMessages       int64 `json:"total_error_messages" gorm:"default:0"`
	TotalAlertMessages       int64 `json:"total_alert_messages" gorm:"default:0"`
	TotalInsightTitles       int64 `json:"total_insight_titles" gorm:"default:0"`
	TotalInsightDescriptions int64 `json:"total_insight_descriptions" gorm:"default:0"`
	TotalTimeRangeLabels     int64 `json:"total_time_range_labels" gorm:"default:0"`
	
	LastUpdated time.Time `json:"last_updated" gorm:"autoUpdateTime"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// TableName sets the table name for AutoTranslationSettings
func (AutoTranslationSettings) TableName() string {
	return "analytics_auto_translation_settings"
}

// TableName sets the table name for AutoTranslationStatistics
func (AutoTranslationStatistics) TableName() string {
	return "analytics_auto_translation_statistics"
}

// GetDefaultSettings returns default auto-translation settings for analytics service
func GetDefaultSettings(serviceID string) *AutoTranslationSettings {
	return &AutoTranslationSettings{
		ServiceID:                      serviceID,
		Enabled:                        true,
		TargetLanguage:                 "es",
		ConfidenceThreshold:            0.8,
		TranslateDashboardTitles:       true,
		TranslateDashboardDescriptions: true,
		TranslateMetricNames:           true,
		TranslateMetricDescriptions:    true,
		TranslateChartTitles:           true,
		TranslateChartLabels:           true,
		TranslateReportTitles:          true,
		TranslateReportDescriptions:    true,
		TranslateEventNames:            true,
		TranslateEventDescriptions:     true,
		TranslateFilterLabels:          true,
		TranslateErrorMessages:         true,
		TranslateAlertMessages:         true,
		TranslateInsightTitles:         true,
		TranslateInsightDescriptions:   true,
		TranslateTimeRangeLabels:       true,
		EnabledDashboardTypes:          []string{"overview", "performance", "usage"},
		EnabledMetricTypes:             []string{"counter", "gauge", "histogram"},
		EnabledChartTypes:              []string{"line", "bar", "pie"},
		EnabledReportTypes:             []string{"daily", "weekly", "monthly"},
		EnabledEventCategories:         []string{"user", "system", "api"},
		EnabledFilterTypes:             []string{"date", "status", "category"},
		EnabledAlertTypes:              []string{"warning", "error", "info"},
		EnabledInsightTypes:            []string{"trend", "anomaly", "recommendation"},
		EnabledTimeRangeTypes:          []string{"relative", "absolute", "custom"},
		BatchProcessing:                true,
		RealTimeMode:                   false,
		SmartTriggers:                  true,
	}
}

// CalculateCoveragePercentage calculates the translation coverage percentage
func (s *AutoTranslationStatistics) CalculateCoveragePercentage() float64 {
	totalContent := s.TotalDashboardTitles + s.TotalDashboardDescriptions + 
		s.TotalMetricNames + s.TotalMetricDescriptions + s.TotalChartTitles + 
		s.TotalChartLabels + s.TotalReportTitles + s.TotalReportDescriptions + 
		s.TotalEventNames + s.TotalEventDescriptions + s.TotalFilterLabels + 
		s.TotalErrorMessages + s.TotalAlertMessages + s.TotalInsightTitles + 
		s.TotalInsightDescriptions + s.TotalTimeRangeLabels
	
	if totalContent == 0 {
		return 0.0
	}
	
	translatedContent := s.TranslatedDashboardTitles + s.TranslatedDashboardDescriptions + 
		s.TranslatedMetricNames + s.TranslatedMetricDescriptions + 
		s.TranslatedChartTitles + s.TranslatedChartLabels + 
		s.TranslatedReportTitles + s.TranslatedReportDescriptions + 
		s.TranslatedEventNames + s.TranslatedEventDescriptions + 
		s.TranslatedFilterLabels + s.TranslatedErrorMessages + 
		s.TranslatedAlertMessages + s.TranslatedInsightTitles + 
		s.TranslatedInsightDescriptions + s.TranslatedTimeRangeLabels
	
	return (float64(translatedContent) / float64(totalContent)) * 100.0
}

// UpdateSuccessRate calculates and updates the success rate
func (s *AutoTranslationStatistics) UpdateSuccessRate() {
	totalAttempts := s.TranslatedDashboardTitles + s.TranslatedDashboardDescriptions + 
		s.TranslatedMetricNames + s.TranslatedMetricDescriptions + 
		s.TranslatedChartTitles + s.TranslatedChartLabels + 
		s.TranslatedReportTitles + s.TranslatedReportDescriptions + 
		s.TranslatedEventNames + s.TranslatedEventDescriptions + 
		s.TranslatedFilterLabels + s.TranslatedErrorMessages + 
		s.TranslatedAlertMessages + s.TranslatedInsightTitles + 
		s.TranslatedInsightDescriptions + s.TranslatedTimeRangeLabels + s.FailedTranslations
	
	if totalAttempts == 0 {
		s.SuccessRate = 0.0
		return
	}
	
	successfulTranslations := totalAttempts - s.FailedTranslations
	s.SuccessRate = (float64(successfulTranslations) / float64(totalAttempts)) * 100.0
}