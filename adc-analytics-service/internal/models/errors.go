package models

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Common errors
var (
	ErrEventNotFound     = errors.New("event not found")
	ErrMetricNotFound    = errors.New("metric not found")
	ErrReportNotFound    = errors.New("report not found")
	ErrDashboardNotFound = errors.New("dashboard not found")
	ErrInvalidTimeRange  = errors.New("invalid time range")
	ErrInvalidEventType  = errors.New("invalid event type")
	ErrInvalidMetricType = errors.New("invalid metric type")
	ErrInvalidReportType = errors.New("invalid report type")
	ErrUnauthorized      = errors.New("unauthorized access")
	ErrQuotaExceeded     = errors.New("quota exceeded")
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error     string                 `json:"error"`
	Message   string                 `json:"message"`
	Code      string                 `json:"code,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	RequestID string                 `json:"request_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewErrorResponse creates a new error response
func NewErrorResponse(err error) *ErrorResponse {
	return &ErrorResponse{
		Error:     err.Error(),
		Message:   err.Error(),
		Timestamp: time.Now(),
	}
}

// NewErrorResponseWithDetails creates a new error response with details
func NewErrorResponseWithDetails(err error, code string, details map[string]interface{}) *ErrorResponse {
	return &ErrorResponse{
		Error:     err.Error(),
		Message:   err.Error(),
		Code:      code,
		Details:   details,
		Timestamp: time.Now(),
	}
}

// AnalyticsError represents a custom analytics error
type AnalyticsError struct {
	Type      string                 `json:"type"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// Error implements the error interface
func (e *AnalyticsError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// NewAnalyticsError creates a new analytics error
func NewAnalyticsError(errorType, message string, details map[string]interface{}) *AnalyticsError {
	return &AnalyticsError{
		Type:      errorType,
		Message:   message,
		Details:   details,
		Timestamp: time.Now(),
	}
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid   bool              `json:"valid"`
	Errors  []ValidationError `json:"errors,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// AnalyticsContext represents context for analytics operations
type AnalyticsContext struct {
	UserID         *uuid.UUID `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID `json:"shop_id,omitempty"`
	SessionID      string     `json:"session_id,omitempty"`
	IPAddress      string     `json:"ip_address,omitempty"`
	UserAgent      string     `json:"user_agent,omitempty"`
	RequestID      string     `json:"request_id,omitempty"`
	CorrelationID  string     `json:"correlation_id,omitempty"`
	Timestamp      time.Time  `json:"timestamp"`
}

// NewAnalyticsContext creates a new analytics context
func NewAnalyticsContext() *AnalyticsContext {
	return &AnalyticsContext{
		Timestamp: time.Now(),
	}
}

// BulkOperationResult represents the result of a bulk operation
type BulkOperationResult struct {
	Success       int      `json:"success"`
	Failed        int      `json:"failed"`
	Errors        []string `json:"errors,omitempty"`
	ProcessedIDs  []uuid.UUID `json:"processed_ids,omitempty"`
	FailedItems   []int    `json:"failed_items,omitempty"` // Indices of failed items
}

// QuotaInfo represents quota information
type QuotaInfo struct {
	Limit     int64 `json:"limit"`
	Used      int64 `json:"used"`
	Remaining int64 `json:"remaining"`
	ResetAt   time.Time `json:"reset_at"`
}