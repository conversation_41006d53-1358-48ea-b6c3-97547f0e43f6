package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// ReportType defines the type of report
type ReportType string

const (
	ReportTypeAnalytics  ReportType = "analytics"
	ReportTypeMetrics    ReportType = "metrics"
	ReportTypeFunnel     ReportType = "funnel"
	ReportTypeCohort     ReportType = "cohort"
	ReportTypeRetention  ReportType = "retention"
	ReportTypeCustom     ReportType = "custom"
)

// ReportStatus defines the status of a report
type ReportStatus string

const (
	ReportStatusPending   ReportStatus = "pending"
	ReportStatusRunning   ReportStatus = "running"
	ReportStatusCompleted ReportStatus = "completed"
	ReportStatusFailed    ReportStatus = "failed"
	ReportStatusScheduled ReportStatus = "scheduled"
)

// ReportFormat defines the format of report output
type ReportFormat string

const (
	ReportFormatJSON ReportFormat = "json"
	ReportFormatCSV  ReportFormat = "csv"
	ReportFormatPDF  ReportFormat = "pdf"
	ReportFormatXLSX ReportFormat = "xlsx"
)

// Report represents an analytics report
type Report struct {
	ID             uuid.UUID    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string       `json:"name" gorm:"not null"`
	Description    string       `json:"description,omitempty"`
	Type           ReportType   `json:"type" gorm:"not null"`
	Status         ReportStatus `json:"status" gorm:"not null;default:'pending'"`
	Configuration  string       `json:"configuration,omitempty" gorm:"type:jsonb"`
	Data           string       `json:"data,omitempty" gorm:"type:jsonb"`
	ErrorMessage   string       `json:"error_message,omitempty"`
	Format         ReportFormat `json:"format" gorm:"not null;default:'json'"`
	UserID         *uuid.UUID   `json:"user_id,omitempty" gorm:"type:uuid;index"`
	OrganizationID *uuid.UUID   `json:"organization_id,omitempty" gorm:"type:uuid;index"`
	ShopID         *uuid.UUID   `json:"shop_id,omitempty" gorm:"type:uuid;index"`
	ScheduledAt    *time.Time   `json:"scheduled_at,omitempty"`
	StartedAt      *time.Time   `json:"started_at,omitempty"`
	CompletedAt    *time.Time   `json:"completed_at,omitempty"`
	CreatedBy      *uuid.UUID   `json:"created_by,omitempty" gorm:"type:uuid"`
	CreatedAt      time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time    `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for the Report model
func (Report) TableName() string {
	return "analytics_reports"
}

// GetParsedConfiguration returns the report configuration parsed as a map
func (r *Report) GetParsedConfiguration() (map[string]interface{}, error) {
	if r.Configuration == "" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(r.Configuration), &config)
	return config, err
}

// GetParsedData returns the report data parsed as a map
func (r *Report) GetParsedData() (map[string]interface{}, error) {
	if r.Data == "" {
		return nil, nil
	}

	var data map[string]interface{}
	err := json.Unmarshal([]byte(r.Data), &data)
	return data, err
}

// ReportRequest represents a request to create a report
type ReportRequest struct {
	Name           string                 `json:"name" binding:"required,min=1,max=255"`
	Description    string                 `json:"description,omitempty"`
	Type           ReportType             `json:"type" binding:"required"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
	Format         ReportFormat           `json:"format,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	ScheduledAt    *time.Time             `json:"scheduled_at,omitempty"`
}

// ToReport converts a ReportRequest to a Report model
func (r *ReportRequest) ToReport() (*Report, error) {
	// Serialize configuration to string
	var configStr string
	if r.Configuration != nil {
		configBytes, err := json.Marshal(r.Configuration)
		if err != nil {
			return nil, err
		}
		configStr = string(configBytes)
	}

	// Set default format if not provided
	format := r.Format
	if format == "" {
		format = ReportFormatJSON
	}

	return &Report{
		Name:           r.Name,
		Description:    r.Description,
		Type:           r.Type,
		Status:         ReportStatusPending,
		Configuration:  configStr,
		Format:         format,
		UserID:         r.UserID,
		OrganizationID: r.OrganizationID,
		ShopID:         r.ShopID,
		ScheduledAt:    r.ScheduledAt,
	}, nil
}

// ReportResponse represents a report in API responses
type ReportResponse struct {
	ID             uuid.UUID              `json:"id"`
	Name           string                 `json:"name"`
	Description    string                 `json:"description,omitempty"`
	Type           ReportType             `json:"type"`
	Status         ReportStatus           `json:"status"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
	Data           map[string]interface{} `json:"data,omitempty"`
	ErrorMessage   string                 `json:"error_message,omitempty"`
	Format         ReportFormat           `json:"format"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	ScheduledAt    *time.Time             `json:"scheduled_at,omitempty"`
	StartedAt      *time.Time             `json:"started_at,omitempty"`
	CompletedAt    *time.Time             `json:"completed_at,omitempty"`
	CreatedBy      *uuid.UUID             `json:"created_by,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// ToResponse converts a Report to a ReportResponse
func (r *Report) ToResponse() (*ReportResponse, error) {
	config, err := r.GetParsedConfiguration()
	if err != nil {
		return nil, err
	}

	data, err := r.GetParsedData()
	if err != nil {
		return nil, err
	}

	return &ReportResponse{
		ID:             r.ID,
		Name:           r.Name,
		Description:    r.Description,
		Type:           r.Type,
		Status:         r.Status,
		Configuration:  config,
		Data:           data,
		ErrorMessage:   r.ErrorMessage,
		Format:         r.Format,
		UserID:         r.UserID,
		OrganizationID: r.OrganizationID,
		ShopID:         r.ShopID,
		ScheduledAt:    r.ScheduledAt,
		StartedAt:      r.StartedAt,
		CompletedAt:    r.CompletedAt,
		CreatedBy:      r.CreatedBy,
		CreatedAt:      r.CreatedAt,
		UpdatedAt:      r.UpdatedAt,
	}, nil
}

// ReportQuery represents parameters for querying reports
type ReportQuery struct {
	Type           *ReportType    `form:"type"`
	Status         *ReportStatus  `form:"status"`
	UserID         *uuid.UUID     `form:"user_id"`
	OrganizationID *uuid.UUID     `form:"organization_id"`
	ShopID         *uuid.UUID     `form:"shop_id"`
	CreatedBy      *uuid.UUID     `form:"created_by"`
	StartTime      *time.Time     `form:"start_time"`
	EndTime        *time.Time     `form:"end_time"`
	Limit          int            `form:"limit"`
	Offset         int            `form:"offset"`
	SortBy         string         `form:"sort_by"`
	SortOrder      string         `form:"sort_order"`
}

// ReportGenerationRequest represents a request to generate a report
type ReportGenerationRequest struct {
	ReportID uuid.UUID `json:"report_id" binding:"required"`
	Priority int       `json:"priority,omitempty"` // 1-10, higher = more priority
}

// Dashboard represents a collection of analytics widgets
type Dashboard struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string     `json:"name" gorm:"not null"`
	Description    string     `json:"description,omitempty"`
	Configuration  string     `json:"configuration,omitempty" gorm:"type:jsonb"`
	UserID         *uuid.UUID `json:"user_id,omitempty" gorm:"type:uuid;index"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"type:uuid;index"`
	ShopID         *uuid.UUID `json:"shop_id,omitempty" gorm:"type:uuid;index"`
	IsPublic       bool       `json:"is_public" gorm:"default:false"`
	CreatedBy      *uuid.UUID `json:"created_by,omitempty" gorm:"type:uuid"`
	CreatedAt      time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for the Dashboard model
func (Dashboard) TableName() string {
	return "analytics_dashboards"
}

// KPI represents a Key Performance Indicator
type KPI struct {
	Name        string      `json:"name"`
	Value       float64     `json:"value"`
	Unit        string      `json:"unit,omitempty"`
	Change      *float64    `json:"change,omitempty"`      // Percentage change
	Trend       string      `json:"trend,omitempty"`       // up, down, stable
	Target      *float64    `json:"target,omitempty"`      // Target value
	Description string      `json:"description,omitempty"`
	Timestamp   time.Time   `json:"timestamp"`
}

// Widget represents a dashboard widget
type Widget struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"` // chart, kpi, table, etc.
	Title         string                 `json:"title"`
	Configuration map[string]interface{} `json:"configuration"`
	Data          map[string]interface{} `json:"data"`
	Position      map[string]int         `json:"position"` // x, y, width, height
}

// DashboardResponse represents a dashboard with its data
type DashboardResponse struct {
	ID             uuid.UUID `json:"id"`
	Name           string    `json:"name"`
	Description    string    `json:"description,omitempty"`
	Widgets        []Widget  `json:"widgets"`
	UserID         *uuid.UUID `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID `json:"shop_id,omitempty"`
	IsPublic       bool      `json:"is_public"`
	CreatedBy      *uuid.UUID `json:"created_by,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}