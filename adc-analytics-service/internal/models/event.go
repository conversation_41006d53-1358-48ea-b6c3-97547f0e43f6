package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// EventType defines the type of analytics event
type EventType string

const (
	EventTypePageView     EventType = "page_view"
	EventTypeUserAction   EventType = "user_action"
	EventTypeAPICall      EventType = "api_call"
	EventTypeConversion   EventType = "conversion"
	EventTypeError        EventType = "error"
	EventTypePerformance  EventType = "performance"
	EventTypeCustom       EventType = "custom"
)

// Event represents an analytics event
type Event struct {
	ID             uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Type           EventType              `json:"type" gorm:"not null;index"`
	Name           string                 `json:"name" gorm:"not null;index"`
	Category       string                 `json:"category,omitempty" gorm:"index"`
	Action         string                 `json:"action,omitempty"`
	Label          string                 `json:"label,omitempty"`
	Value          *float64               `json:"value,omitempty"`
	Properties     string                 `json:"properties,omitempty" gorm:"type:jsonb"`
	UserID         *uuid.UUID             `json:"user_id,omitempty" gorm:"type:uuid;index"`
	SessionID      string                 `json:"session_id,omitempty" gorm:"index"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty" gorm:"type:uuid;index"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty" gorm:"type:uuid;index"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Referrer       string                 `json:"referrer,omitempty"`
	URL            string                 `json:"url,omitempty"`
	Timestamp      time.Time              `json:"timestamp" gorm:"not null;index"`
	CreatedAt      time.Time              `json:"created_at" gorm:"autoCreateTime"`
}

// TableName returns the table name for the Event model
func (Event) TableName() string {
	return "analytics_events"
}

// GetParsedProperties returns the event properties parsed as a map
func (e *Event) GetParsedProperties() (map[string]interface{}, error) {
	if e.Properties == "" {
		return nil, nil
	}

	var properties map[string]interface{}
	err := json.Unmarshal([]byte(e.Properties), &properties)
	return properties, err
}

// EventRequest represents a request to create an event
type EventRequest struct {
	Type           EventType              `json:"type" binding:"required"`
	Name           string                 `json:"name" binding:"required,min=1,max=255"`
	Category       string                 `json:"category,omitempty"`
	Action         string                 `json:"action,omitempty"`
	Label          string                 `json:"label,omitempty"`
	Value          *float64               `json:"value,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	SessionID      string                 `json:"session_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Referrer       string                 `json:"referrer,omitempty"`
	URL            string                 `json:"url,omitempty"`
	Timestamp      *time.Time             `json:"timestamp,omitempty"`
}

// ToEvent converts an EventRequest to an Event model
func (r *EventRequest) ToEvent() (*Event, error) {
	// Serialize properties to string
	var propertiesStr string
	if r.Properties != nil {
		propertiesBytes, err := json.Marshal(r.Properties)
		if err != nil {
			return nil, err
		}
		propertiesStr = string(propertiesBytes)
	}

	// Set timestamp to now if not provided
	timestamp := time.Now()
	if r.Timestamp != nil {
		timestamp = *r.Timestamp
	}

	return &Event{
		Type:           r.Type,
		Name:           r.Name,
		Category:       r.Category,
		Action:         r.Action,
		Label:          r.Label,
		Value:          r.Value,
		Properties:     propertiesStr,
		UserID:         r.UserID,
		SessionID:      r.SessionID,
		OrganizationID: r.OrganizationID,
		ShopID:         r.ShopID,
		IPAddress:      r.IPAddress,
		UserAgent:      r.UserAgent,
		Referrer:       r.Referrer,
		URL:            r.URL,
		Timestamp:      timestamp,
	}, nil
}

// EventResponse represents an event in API responses
type EventResponse struct {
	ID             uuid.UUID              `json:"id"`
	Type           EventType              `json:"type"`
	Name           string                 `json:"name"`
	Category       string                 `json:"category,omitempty"`
	Action         string                 `json:"action,omitempty"`
	Label          string                 `json:"label,omitempty"`
	Value          *float64               `json:"value,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	SessionID      string                 `json:"session_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Referrer       string                 `json:"referrer,omitempty"`
	URL            string                 `json:"url,omitempty"`
	Timestamp      time.Time              `json:"timestamp"`
	CreatedAt      time.Time              `json:"created_at"`
}

// ToResponse converts an Event to an EventResponse
func (e *Event) ToResponse() (*EventResponse, error) {
	properties, err := e.GetParsedProperties()
	if err != nil {
		return nil, err
	}

	return &EventResponse{
		ID:             e.ID,
		Type:           e.Type,
		Name:           e.Name,
		Category:       e.Category,
		Action:         e.Action,
		Label:          e.Label,
		Value:          e.Value,
		Properties:     properties,
		UserID:         e.UserID,
		SessionID:      e.SessionID,
		OrganizationID: e.OrganizationID,
		ShopID:         e.ShopID,
		IPAddress:      e.IPAddress,
		UserAgent:      e.UserAgent,
		Referrer:       e.Referrer,
		URL:            e.URL,
		Timestamp:      e.Timestamp,
		CreatedAt:      e.CreatedAt,
	}, nil
}

// EventQuery represents parameters for querying events
type EventQuery struct {
	Type           *EventType `form:"type"`
	Name           string     `form:"name"`
	Category       string     `form:"category"`
	UserID         *uuid.UUID `form:"user_id"`
	SessionID      string     `form:"session_id"`
	OrganizationID *uuid.UUID `form:"organization_id"`
	ShopID         *uuid.UUID `form:"shop_id"`
	StartTime      *time.Time `form:"start_time"`
	EndTime        *time.Time `form:"end_time"`
	Limit          int        `form:"limit"`
	Offset         int        `form:"offset"`
	SortBy         string     `form:"sort_by"`
	SortOrder      string     `form:"sort_order"`
}

// BatchEventRequest represents a request to create multiple events
type BatchEventRequest struct {
	Events []EventRequest `json:"events" binding:"required,dive"`
}