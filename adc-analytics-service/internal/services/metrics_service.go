package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/adc-analytics-service/internal/models"
	"github.com/adc-analytics-service/internal/storage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// MetricsService handles metrics collection and processing
type MetricsService struct {
	storage    storage.Storage
	cache      storage.Cache
	keyBuilder *storage.CacheKeyBuilder
}

// NewMetricsService creates a new metrics service instance
func NewMetricsService(storageInstance storage.Storage, cache storage.Cache) *MetricsService {
	return &MetricsService{
		storage:    storageInstance,
		cache:      cache,
		keyBuilder: storage.NewCacheKeyBuilder("adc_metrics:"),
	}
}

// RecordMetric records a single metric
func (s *MetricsService) RecordMetric(ctx context.Context, request *models.MetricRequest) error {
	// Validate metric request
	if err := s.validateMetricRequest(request); err != nil {
		return fmt.Errorf("invalid metric request: %w", err)
	}

	// Convert request to metric
	metric, err := request.ToMetric()
	if err != nil {
		return fmt.Errorf("failed to convert metric request: %w", err)
	}

	// Store metric
	if err := s.storage.CreateMetric(ctx, metric); err != nil {
		log.Error().
			Err(err).
			Str("metric_name", metric.Name).
			Float64("metric_value", metric.Value).
			Msg("Failed to store metric")
		return fmt.Errorf("failed to store metric: %w", err)
	}

	// Invalidate relevant caches
	s.invalidateMetricCaches(metric)

	log.Debug().
		Str("metric_id", metric.ID.String()).
		Str("metric_name", metric.Name).
		Float64("value", metric.Value).
		Msg("Metric recorded")

	return nil
}

// RecordMetrics records multiple metrics in batch
func (s *MetricsService) RecordMetrics(ctx context.Context, requests []models.MetricRequest) (*models.BulkOperationResult, error) {
	result := &models.BulkOperationResult{
		Success:      0,
		Failed:       0,
		Errors:       []string{},
		ProcessedIDs: []uuid.UUID{},
		FailedItems:  []int{},
	}

	if len(requests) == 0 {
		return result, nil
	}

	if len(requests) > 1000 {
		return nil, fmt.Errorf("batch size too large (max 1000 metrics)")
	}

	metrics := make([]*models.Metric, 0, len(requests))

	// Validate and convert all requests
	for i, request := range requests {
		if err := s.validateMetricRequest(&request); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("Metric %d: %v", i, err))
			result.FailedItems = append(result.FailedItems, i)
			continue
		}

		metric, err := request.ToMetric()
		if err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("Metric %d: %v", i, err))
			result.FailedItems = append(result.FailedItems, i)
			continue
		}

		metrics = append(metrics, metric)
	}

	if len(metrics) == 0 {
		return result, nil
	}

	// Store metrics in batch
	if err := s.storage.CreateMetrics(ctx, metrics); err != nil {
		log.Error().
			Err(err).
			Int("metric_count", len(metrics)).
			Msg("Failed to store metrics batch")
		return nil, fmt.Errorf("failed to store metrics batch: %w", err)
	}

	// Update result
	result.Success = len(metrics)
	for _, metric := range metrics {
		result.ProcessedIDs = append(result.ProcessedIDs, metric.ID)
	}

	// Invalidate caches asynchronously
	go func() {
		for _, metric := range metrics {
			s.invalidateMetricCaches(metric)
		}
	}()

	log.Info().
		Int("success_count", result.Success).
		Int("failed_count", result.Failed).
		Msg("Batch metrics recorded")

	return result, nil
}

// GetMetrics retrieves metrics based on query parameters
func (s *MetricsService) GetMetrics(ctx context.Context, query *models.MetricQuery) ([]*models.Metric, error) {
	// Set default limits
	if query.Limit == 0 {
		query.Limit = 100
	}
	if query.Limit > 1000 {
		query.Limit = 1000
	}

	// Try cache first for simple queries
	cacheKey := s.buildMetricCacheKey(query)
	if cached := s.getFromCache(cacheKey); cached != nil {
		if metrics, ok := cached.([]*models.Metric); ok {
			log.Debug().Str("cache_key", cacheKey).Msg("Metrics retrieved from cache")
			return metrics, nil
		}
	}

	// Retrieve from storage
	metrics, err := s.storage.GetMetrics(ctx, query)
	if err != nil {
		log.Error().
			Err(err).
			Interface("query", query).
			Msg("Failed to retrieve metrics")
		return nil, fmt.Errorf("failed to retrieve metrics: %w", err)
	}

	// Cache results for future queries
	s.setInCache(cacheKey, metrics, 2*time.Minute)

	log.Debug().
		Int("count", len(metrics)).
		Interface("query", query).
		Msg("Retrieved metrics")

	return metrics, nil
}

// GetTimeSeries retrieves time series data for metrics
func (s *MetricsService) GetTimeSeries(ctx context.Context, name string, startTime, endTime time.Time, interval string, aggregation models.AggregationType, filters map[string]interface{}) (*models.TimeSeries, error) {
	query := &models.MetricQuery{
		Name:        name,
		StartTime:   &startTime,
		EndTime:     &endTime,
		Interval:    interval,
		Aggregation: &aggregation,
		Limit:       10000, // Large limit for time series
	}

	// Apply filters
	s.applyFiltersToMetricQuery(query, filters)

	// Check cache first
	cacheKey := s.buildTimeSeriesCacheKey(name, startTime, endTime, interval, aggregation, filters)
	if cached := s.getFromCache(cacheKey); cached != nil {
		if timeSeries, ok := cached.(*models.TimeSeries); ok {
			log.Debug().Str("cache_key", cacheKey).Msg("Time series retrieved from cache")
			return timeSeries, nil
		}
	}

	// Get raw metrics
	metrics, err := s.GetMetrics(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get metrics for time series: %w", err)
	}

	// Group metrics by time interval and aggregate
	timeSeries, err := s.aggregateToTimeSeries(metrics, interval, aggregation)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate time series: %w", err)
	}

	timeSeries.Name = name
	timeSeries.Aggregation = aggregation

	// Cache the result
	s.setInCache(cacheKey, timeSeries, 5*time.Minute)

	log.Info().
		Str("metric_name", name).
		Int("points", len(timeSeries.Points)).
		Str("aggregation", string(aggregation)).
		Msg("Time series generated")

	return timeSeries, nil
}

// GetAggregatedMetrics retrieves aggregated metrics
func (s *MetricsService) GetAggregatedMetrics(ctx context.Context, names []string, aggregation models.AggregationType, startTime, endTime time.Time, groupBy []string, filters map[string]interface{}) ([]models.AggregatedMetric, error) {
	if len(names) == 0 {
		return []models.AggregatedMetric{}, nil
	}

	if len(names) > 50 {
		return nil, fmt.Errorf("too many metrics requested (max 50)")
	}

	var aggregatedMetrics []models.AggregatedMetric

	for _, name := range names {
		query := &models.MetricQuery{
			Name:        name,
			StartTime:   &startTime,
			EndTime:     &endTime,
			Aggregation: &aggregation,
			GroupBy:     groupBy,
			Limit:       10000,
		}

		// Apply filters
		s.applyFiltersToMetricQuery(query, filters)

		metrics, err := s.GetMetrics(ctx, query)
		if err != nil {
			log.Error().Err(err).Str("metric_name", name).Msg("Failed to get metrics for aggregation")
			continue
		}

		if len(metrics) == 0 {
			continue
		}

		// Perform aggregation
		aggregated := s.performAggregation(metrics, aggregation, groupBy)
		aggregated.Name = name
		aggregated.Aggregation = aggregation
		aggregated.Timestamp = endTime

		aggregatedMetrics = append(aggregatedMetrics, aggregated)
	}

	log.Info().
		Int("metric_count", len(names)).
		Int("results", len(aggregatedMetrics)).
		Str("aggregation", string(aggregation)).
		Msg("Metrics aggregated")

	return aggregatedMetrics, nil
}

// GetMetricsSummary provides a summary of metrics for a time range
func (s *MetricsService) GetMetricsSummary(ctx context.Context, startTime, endTime time.Time, filters map[string]interface{}) (map[string]interface{}, error) {
	query := &models.MetricQuery{
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     50000, // Large limit for summary
	}

	// Apply filters
	s.applyFiltersToMetricQuery(query, filters)

	metrics, err := s.GetMetrics(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get metrics for summary: %w", err)
	}

	summary := make(map[string]interface{})

	// Calculate basic statistics
	metricCounts := make(map[string]int)
	typeCounts := make(map[models.MetricType]int)
	valueSum := make(map[string]float64)
	valueCount := make(map[string]int)

	for _, metric := range metrics {
		metricCounts[metric.Name]++
		typeCounts[metric.Type]++
		valueSum[metric.Name] += metric.Value
		valueCount[metric.Name]++
	}

	// Calculate averages
	averages := make(map[string]float64)
	for name, sum := range valueSum {
		if count := valueCount[name]; count > 0 {
			averages[name] = sum / float64(count)
		}
	}

	summary["total_metrics"] = len(metrics)
	summary["unique_names"] = len(metricCounts)
	summary["metric_counts"] = metricCounts
	summary["type_distribution"] = typeCounts
	summary["averages"] = averages
	summary["time_range"] = map[string]interface{}{
		"start": startTime,
		"end":   endTime,
	}

	log.Info().
		Int("total_metrics", len(metrics)).
		Int("unique_names", len(metricCounts)).
		Msg("Metrics summary generated")

	return summary, nil
}

// Helper methods

func (s *MetricsService) validateMetricRequest(request *models.MetricRequest) error {
	if request.Name == "" {
		return fmt.Errorf("metric name is required")
	}

	if len(request.Name) > 255 {
		return fmt.Errorf("metric name too long (max 255 characters)")
	}

	if request.Type == "" {
		return fmt.Errorf("metric type is required")
	}

	// Validate metric type
	validTypes := map[models.MetricType]bool{
		models.MetricTypeCounter:   true,
		models.MetricTypeGauge:     true,
		models.MetricTypeHistogram: true,
		models.MetricTypeSummary:   true,
	}

	if !validTypes[request.Type] {
		return fmt.Errorf("invalid metric type: %s", request.Type)
	}

	// Validate value
	if math.IsNaN(request.Value) || math.IsInf(request.Value, 0) {
		return fmt.Errorf("invalid metric value: %f", request.Value)
	}

	return nil
}

func (s *MetricsService) applyFiltersToMetricQuery(query *models.MetricQuery, filters map[string]interface{}) {
	if orgID, ok := filters["organization_id"]; ok {
		if id, err := uuid.Parse(fmt.Sprintf("%v", orgID)); err == nil {
			query.OrganizationID = &id
		}
	}

	if userID, ok := filters["user_id"]; ok {
		if id, err := uuid.Parse(fmt.Sprintf("%v", userID)); err == nil {
			query.UserID = &id
		}
	}

	if shopID, ok := filters["shop_id"]; ok {
		if id, err := uuid.Parse(fmt.Sprintf("%v", shopID)); err == nil {
			query.ShopID = &id
		}
	}

	if source, ok := filters["source"]; ok {
		query.Source = fmt.Sprintf("%v", source)
	}

	if metricType, ok := filters["type"]; ok {
		if typeStr := fmt.Sprintf("%v", metricType); typeStr != "" {
			mType := models.MetricType(typeStr)
			query.Type = &mType
		}
	}
}

func (s *MetricsService) aggregateToTimeSeries(metrics []*models.Metric, interval string, aggregation models.AggregationType) (*models.TimeSeries, error) {
	if len(metrics) == 0 {
		return &models.TimeSeries{Points: []models.TimeSeriesPoint{}}, nil
	}

	// Parse interval duration
	intervalDuration, err := s.parseInterval(interval)
	if err != nil {
		return nil, fmt.Errorf("invalid interval: %w", err)
	}

	// Group metrics by time buckets
	buckets := make(map[int64][]float64)
	var unit string
	var tags map[string]string

	for _, metric := range metrics {
		// Get the first metric's metadata
		if unit == "" {
			unit = metric.Unit
		}
		if tags == nil {
			tags, _ = metric.GetParsedTags()
		}

		// Calculate bucket timestamp
		bucketTime := metric.Timestamp.Truncate(intervalDuration).Unix()
		buckets[bucketTime] = append(buckets[bucketTime], metric.Value)
	}

	// Create time series points
	var points []models.TimeSeriesPoint
	for timestamp, values := range buckets {
		aggregatedValue := s.aggregateValues(values, aggregation)
		points = append(points, models.TimeSeriesPoint{
			Timestamp: time.Unix(timestamp, 0),
			Value:     aggregatedValue,
		})
	}

	// Sort points by timestamp
	sort.Slice(points, func(i, j int) bool {
		return points[i].Timestamp.Before(points[j].Timestamp)
	})

	return &models.TimeSeries{
		Type:   metrics[0].Type,
		Unit:   unit,
		Tags:   tags,
		Points: points,
	}, nil
}

func (s *MetricsService) performAggregation(metrics []*models.Metric, aggregation models.AggregationType, groupBy []string) models.AggregatedMetric {
	if len(metrics) == 0 {
		return models.AggregatedMetric{Count: 0}
	}

	values := make([]float64, len(metrics))
	for i, metric := range metrics {
		values[i] = metric.Value
	}

	aggregatedValue := s.aggregateValues(values, aggregation)

	// Get metadata from first metric
	var unit string
	var tags map[string]string
	if len(metrics) > 0 {
		unit = metrics[0].Unit
		tags, _ = metrics[0].GetParsedTags()
	}

	return models.AggregatedMetric{
		Type:  metrics[0].Type,
		Value: aggregatedValue,
		Unit:  unit,
		Tags:  tags,
		Count: int64(len(metrics)),
	}
}

func (s *MetricsService) aggregateValues(values []float64, aggregation models.AggregationType) float64 {
	if len(values) == 0 {
		return 0
	}

	switch aggregation {
	case models.AggregationSum:
		sum := 0.0
		for _, v := range values {
			sum += v
		}
		return sum

	case models.AggregationAvg:
		sum := 0.0
		for _, v := range values {
			sum += v
		}
		return sum / float64(len(values))

	case models.AggregationMin:
		min := values[0]
		for _, v := range values[1:] {
			if v < min {
				min = v
			}
		}
		return min

	case models.AggregationMax:
		max := values[0]
		for _, v := range values[1:] {
			if v > max {
				max = v
			}
		}
		return max

	case models.AggregationCount:
		return float64(len(values))

	case models.AggregationP50, models.AggregationP90, models.AggregationP95, models.AggregationP99:
		return s.calculatePercentile(values, aggregation)

	default:
		return 0
	}
}

func (s *MetricsService) calculatePercentile(values []float64, aggregation models.AggregationType) float64 {
	if len(values) == 0 {
		return 0
	}

	// Sort values
	sorted := make([]float64, len(values))
	copy(sorted, values)
	sort.Float64s(sorted)

	var percentile float64
	switch aggregation {
	case models.AggregationP50:
		percentile = 0.5
	case models.AggregationP90:
		percentile = 0.9
	case models.AggregationP95:
		percentile = 0.95
	case models.AggregationP99:
		percentile = 0.99
	default:
		return 0
	}

	index := percentile * float64(len(sorted)-1)
	lower := int(index)
	upper := lower + 1

	if upper >= len(sorted) {
		return sorted[len(sorted)-1]
	}

	weight := index - float64(lower)
	return sorted[lower]*(1-weight) + sorted[upper]*weight
}

func (s *MetricsService) parseInterval(interval string) (time.Duration, error) {
	switch interval {
	case "1m":
		return time.Minute, nil
	case "5m":
		return 5 * time.Minute, nil
	case "15m":
		return 15 * time.Minute, nil
	case "30m":
		return 30 * time.Minute, nil
	case "1h":
		return time.Hour, nil
	case "6h":
		return 6 * time.Hour, nil
	case "12h":
		return 12 * time.Hour, nil
	case "1d":
		return 24 * time.Hour, nil
	case "7d":
		return 7 * 24 * time.Hour, nil
	default:
		return time.ParseDuration(interval)
	}
}

func (s *MetricsService) invalidateMetricCaches(metric *models.Metric) {
	patterns := []string{
		"metrics:*",
		fmt.Sprintf("metrics:name:%s", metric.Name),
		fmt.Sprintf("metrics:type:%s", metric.Type),
		"timeseries:*",
	}

	if metric.OrganizationID != nil {
		patterns = append(patterns, fmt.Sprintf("metrics:org:%s", metric.OrganizationID.String()))
	}

	for _, pattern := range patterns {
		cacheKey := s.keyBuilder.Build(pattern)
		s.cache.Delete(context.Background(), cacheKey)
	}
}

func (s *MetricsService) buildMetricCacheKey(query *models.MetricQuery) string {
	key := "metrics"
	
	if query.Name != "" {
		key += fmt.Sprintf(":name:%s", query.Name)
	}
	if query.Type != nil {
		key += fmt.Sprintf(":type:%s", *query.Type)
	}
	if query.OrganizationID != nil {
		key += fmt.Sprintf(":org:%s", query.OrganizationID.String())
	}
	if query.StartTime != nil {
		key += fmt.Sprintf(":start:%d", query.StartTime.Unix())
	}
	if query.EndTime != nil {
		key += fmt.Sprintf(":end:%d", query.EndTime.Unix())
	}
	key += fmt.Sprintf(":limit:%d:offset:%d", query.Limit, query.Offset)

	return s.keyBuilder.Build(key)
}

func (s *MetricsService) buildTimeSeriesCacheKey(name string, startTime, endTime time.Time, interval string, aggregation models.AggregationType, filters map[string]interface{}) string {
	key := fmt.Sprintf("timeseries:%s:%d:%d:%s:%s", name, startTime.Unix(), endTime.Unix(), interval, aggregation)
	
	if orgID, ok := filters["organization_id"]; ok {
		key += fmt.Sprintf(":org:%v", orgID)
	}

	return s.keyBuilder.Build(key)
}

func (s *MetricsService) getFromCache(key string) interface{} {
	data, err := s.cache.Get(context.Background(), key)
	if err != nil {
		return nil
	}

	var result interface{}
	if err := json.Unmarshal([]byte(data), &result); err != nil {
		return nil
	}

	return result
}

func (s *MetricsService) setInCache(key string, value interface{}, ttl time.Duration) {
	data, err := json.Marshal(value)
	if err != nil {
		return
	}

	s.cache.Set(context.Background(), key, string(data), ttl)
}