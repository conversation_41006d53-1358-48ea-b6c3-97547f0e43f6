package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
	"crypto/sha256"
)

// MultiLangClient handles communication with the Multi-Languages service
type MultiLangClient struct {
	baseURL      string
	internalKey  string
	projectID    string
	organization string
	httpClient   *http.Client
}

// TranslationRequest represents a translation request
type TranslationRequest struct {
	Text               string `json:"text"`
	SourceLanguage     string `json:"source_language"`
	TargetLanguage     string `json:"target_language"`
	Namespace          string `json:"namespace,omitempty"`
	Key                string `json:"key,omitempty"`
	ConfidenceThreshold float64 `json:"confidence_threshold,omitempty"`
}

// TranslationResponse represents a translation response
type TranslationResponse struct {
	TranslatedText  string  `json:"translated_text"`
	Confidence      float64 `json:"confidence"`
	AutoTranslated  bool    `json:"auto_translated"`
	SourceLanguage  string  `json:"source_language"`
	TargetLanguage  string  `json:"target_language"`
}

// NewMultiLangClient creates a new Multi-Languages service client
func NewMultiLangClient(baseURL, internalKey, projectID, organization string) *MultiLangClient {
	return &MultiLangClient{
		baseURL:      baseURL,
		internalKey:  internalKey,
		projectID:    projectID,
		organization: organization,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// HealthCheck checks if the Multi-Languages service is healthy
func (c *MultiLangClient) HealthCheck(ctx context.Context) error {
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/health", nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Multi-Languages service unhealthy: status %d", resp.StatusCode)
	}

	return nil
}

// TranslateText performs a translation using the Multi-Languages service
func (c *MultiLangClient) TranslateText(ctx context.Context, text, sourceLanguage, targetLanguage, namespace, key string) (string, bool, error) {
	translationReq := TranslationRequest{
		Text:           text,
		SourceLanguage: sourceLanguage,
		TargetLanguage: targetLanguage,
		Namespace:      namespace,
		Key:            key,
		ConfidenceThreshold: 0.8,
	}

	jsonData, err := json.Marshal(translationReq)
	if err != nil {
		return "", false, fmt.Errorf("failed to marshal translation request: %w", err)
	}

	// Use internal API endpoint for service-to-service communication
	endpoint := fmt.Sprintf("%s/internal/v1/projects/%s/translate", c.baseURL, c.projectID)
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", false, fmt.Errorf("failed to create translation request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.internalKey)
	req.Header.Set("X-Organization", c.organization)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", false, fmt.Errorf("translation request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", false, fmt.Errorf("translation failed: %s", string(bodyBytes))
	}

	var translationResp TranslationResponse
	if err := json.NewDecoder(resp.Body).Decode(&translationResp); err != nil {
		return "", false, fmt.Errorf("failed to decode translation response: %w", err)
	}

	return translationResp.TranslatedText, translationResp.AutoTranslated, nil
}

// Analytics-specific translation methods

// TranslateDashboardTitle translates dashboard titles and names
func (c *MultiLangClient) TranslateDashboardTitle(ctx context.Context, dashboardType, title, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("dashboards_%s", dashboardType)
	key := fmt.Sprintf("title_%d", hashString(title))
	return c.TranslateText(ctx, title, "en", targetLocale, namespace, key)
}

// TranslateDashboardDescription translates dashboard descriptions
func (c *MultiLangClient) TranslateDashboardDescription(ctx context.Context, dashboardType, description, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("dashboards_%s", dashboardType)
	key := fmt.Sprintf("description_%d", hashString(description))
	return c.TranslateText(ctx, description, "en", targetLocale, namespace, key)
}

// TranslateMetricName translates metric names and labels
func (c *MultiLangClient) TranslateMetricName(ctx context.Context, metricType, name, targetLocale string) (string, bool, error) {
	namespace := "metrics"
	key := fmt.Sprintf("name_%s_%d", metricType, hashString(name))
	return c.TranslateText(ctx, name, "en", targetLocale, namespace, key)
}

// TranslateMetricDescription translates metric descriptions and help text
func (c *MultiLangClient) TranslateMetricDescription(ctx context.Context, metricType, description, targetLocale string) (string, bool, error) {
	namespace := "metrics"
	key := fmt.Sprintf("description_%s_%d", metricType, hashString(description))
	return c.TranslateText(ctx, description, "en", targetLocale, namespace, key)
}

// TranslateChartTitle translates chart titles and legends
func (c *MultiLangClient) TranslateChartTitle(ctx context.Context, chartType, title, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("charts_%s", chartType)
	key := fmt.Sprintf("title_%d", hashString(title))
	return c.TranslateText(ctx, title, "en", targetLocale, namespace, key)
}

// TranslateChartLabel translates chart axis labels and legends
func (c *MultiLangClient) TranslateChartLabel(ctx context.Context, chartType, label, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("charts_%s", chartType)
	key := fmt.Sprintf("label_%d", hashString(label))
	return c.TranslateText(ctx, label, "en", targetLocale, namespace, key)
}

// TranslateReportTitle translates report titles and sections
func (c *MultiLangClient) TranslateReportTitle(ctx context.Context, reportType, title, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("reports_%s", reportType)
	key := fmt.Sprintf("title_%d", hashString(title))
	return c.TranslateText(ctx, title, "en", targetLocale, namespace, key)
}

// TranslateReportDescription translates report descriptions and summaries
func (c *MultiLangClient) TranslateReportDescription(ctx context.Context, reportType, description, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("reports_%s", reportType)
	key := fmt.Sprintf("description_%d", hashString(description))
	return c.TranslateText(ctx, description, "en", targetLocale, namespace, key)
}

// TranslateEventName translates event names and types
func (c *MultiLangClient) TranslateEventName(ctx context.Context, eventCategory, name, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("events_%s", eventCategory)
	key := fmt.Sprintf("name_%d", hashString(name))
	return c.TranslateText(ctx, name, "en", targetLocale, namespace, key)
}

// TranslateEventDescription translates event descriptions and details
func (c *MultiLangClient) TranslateEventDescription(ctx context.Context, eventCategory, description, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("events_%s", eventCategory)
	key := fmt.Sprintf("description_%d", hashString(description))
	return c.TranslateText(ctx, description, "en", targetLocale, namespace, key)
}

// TranslateFilterLabel translates filter labels and options
func (c *MultiLangClient) TranslateFilterLabel(ctx context.Context, filterType, label, targetLocale string) (string, bool, error) {
	namespace := "filters"
	key := fmt.Sprintf("label_%s_%d", filterType, hashString(label))
	return c.TranslateText(ctx, label, "en", targetLocale, namespace, key)
}

// TranslateErrorMessage translates analytics error messages
func (c *MultiLangClient) TranslateErrorMessage(ctx context.Context, errorCode, message, targetLocale string) (string, bool, error) {
	namespace := "errors"
	key := fmt.Sprintf("error_%s_%d", errorCode, hashString(message))
	return c.TranslateText(ctx, message, "en", targetLocale, namespace, key)
}

// TranslateAlertMessage translates alert messages and notifications
func (c *MultiLangClient) TranslateAlertMessage(ctx context.Context, alertType, message, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("alerts_%s", alertType)
	key := fmt.Sprintf("message_%d", hashString(message))
	return c.TranslateText(ctx, message, "en", targetLocale, namespace, key)
}

// TranslateInsightTitle translates insight titles and findings
func (c *MultiLangClient) TranslateInsightTitle(ctx context.Context, insightType, title, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("insights_%s", insightType)
	key := fmt.Sprintf("title_%d", hashString(title))
	return c.TranslateText(ctx, title, "en", targetLocale, namespace, key)
}

// TranslateInsightDescription translates insight descriptions and recommendations
func (c *MultiLangClient) TranslateInsightDescription(ctx context.Context, insightType, description, targetLocale string) (string, bool, error) {
	namespace := fmt.Sprintf("insights_%s", insightType)
	key := fmt.Sprintf("description_%d", hashString(description))
	return c.TranslateText(ctx, description, "en", targetLocale, namespace, key)
}

// TranslateTimeRangeLabel translates time range labels (Last 7 days, This month, etc.)
func (c *MultiLangClient) TranslateTimeRangeLabel(ctx context.Context, rangeType, label, targetLocale string) (string, bool, error) {
	namespace := "time_ranges"
	key := fmt.Sprintf("range_%s_%d", rangeType, hashString(label))
	return c.TranslateText(ctx, label, "en", targetLocale, namespace, key)
}

// hashString creates a simple hash for cache keys
func hashString(s string) uint32 {
	hash := sha256.Sum256([]byte(s))
	return uint32(hash[0])<<24 | uint32(hash[1])<<16 | uint32(hash[2])<<8 | uint32(hash[3])
}

// buildURL constructs a URL with query parameters
func (c *MultiLangClient) buildURL(endpoint string, params map[string]string) string {
	u, _ := url.Parse(c.baseURL + endpoint)
	q := u.Query()
	for key, value := range params {
		q.Set(key, value)
	}
	u.RawQuery = q.Encode()
	return u.String()
}