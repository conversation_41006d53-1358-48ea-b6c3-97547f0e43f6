package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/adc-analytics-service/internal/models"
	"github.com/adc-analytics-service/internal/storage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// AnalyticsService handles analytics operations
type AnalyticsService struct {
	storage     storage.Storage
	cache       storage.Cache
	metrics     *MetricsService
	keyBuilder  *storage.CacheKeyBuilder
}

// NewAnalyticsService creates a new analytics service instance
func NewAnalyticsService(storageInstance storage.Storage, cache storage.Cache, metrics *MetricsService) *AnalyticsService {
	return &AnalyticsService{
		storage:    storageInstance,
		cache:      cache,
		metrics:    metrics,
		keyBuilder: storage.NewCacheKeyBuilder("adc_analytics:"),
	}
}

// TrackEvent tracks an analytics event
func (s *AnalyticsService) TrackEvent(ctx context.Context, request *models.EventRequest, analyticsCtx *models.AnalyticsContext) (*models.Event, error) {
	// Validate event request
	if err := s.validateEventRequest(request); err != nil {
		return nil, fmt.Errorf("invalid event request: %w", err)
	}

	// Convert request to event
	event, err := request.ToEvent()
	if err != nil {
		return nil, fmt.Errorf("failed to convert event request: %w", err)
	}

	// Enhance event with context if available
	if analyticsCtx != nil {
		if event.UserID == nil && analyticsCtx.UserID != nil {
			event.UserID = analyticsCtx.UserID
		}
		if event.OrganizationID == nil && analyticsCtx.OrganizationID != nil {
			event.OrganizationID = analyticsCtx.OrganizationID
		}
		if event.ShopID == nil && analyticsCtx.ShopID != nil {
			event.ShopID = analyticsCtx.ShopID
		}
		if event.SessionID == "" && analyticsCtx.SessionID != "" {
			event.SessionID = analyticsCtx.SessionID
		}
		if event.IPAddress == "" && analyticsCtx.IPAddress != "" {
			event.IPAddress = analyticsCtx.IPAddress
		}
		if event.UserAgent == "" && analyticsCtx.UserAgent != "" {
			event.UserAgent = analyticsCtx.UserAgent
		}
	}

	// Store event
	if err := s.storage.CreateEvent(ctx, event); err != nil {
		log.Error().
			Err(err).
			Str("event_type", string(event.Type)).
			Str("event_name", event.Name).
			Msg("Failed to store event")
		return nil, fmt.Errorf("failed to store event: %w", err)
	}

	// Update related metrics asynchronously
	go s.updateEventMetrics(context.Background(), event)

	// Invalidate relevant caches
	s.invalidateEventCaches(event)

	log.Info().
		Str("event_id", event.ID.String()).
		Str("event_type", string(event.Type)).
		Str("event_name", event.Name).
		Msg("Event tracked successfully")

	return event, nil
}

// TrackEvents tracks multiple events in batch
func (s *AnalyticsService) TrackEvents(ctx context.Context, requests []models.EventRequest, analyticsCtx *models.AnalyticsContext) (*models.BulkOperationResult, error) {
	result := &models.BulkOperationResult{
		Success:      0,
		Failed:       0,
		Errors:       []string{},
		ProcessedIDs: []uuid.UUID{},
		FailedItems:  []int{},
	}

	if len(requests) == 0 {
		return result, nil
	}

	if len(requests) > 1000 {
		return nil, fmt.Errorf("batch size too large (max 1000 events)")
	}

	events := make([]*models.Event, 0, len(requests))

	// Validate and convert all requests
	for i, request := range requests {
		if err := s.validateEventRequest(&request); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("Event %d: %v", i, err))
			result.FailedItems = append(result.FailedItems, i)
			continue
		}

		event, err := request.ToEvent()
		if err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("Event %d: %v", i, err))
			result.FailedItems = append(result.FailedItems, i)
			continue
		}

		// Enhance with context
		if analyticsCtx != nil {
			s.enhanceEventWithContext(event, analyticsCtx)
		}

		events = append(events, event)
	}

	if len(events) == 0 {
		return result, nil
	}

	// Store events in batch
	if err := s.storage.CreateEvents(ctx, events); err != nil {
		log.Error().
			Err(err).
			Int("event_count", len(events)).
			Msg("Failed to store events batch")
		return nil, fmt.Errorf("failed to store events batch: %w", err)
	}

	// Update result
	result.Success = len(events)
	for _, event := range events {
		result.ProcessedIDs = append(result.ProcessedIDs, event.ID)
	}

	// Update metrics and invalidate caches asynchronously
	go func() {
		for _, event := range events {
			s.updateEventMetrics(context.Background(), event)
			s.invalidateEventCaches(event)
		}
	}()

	log.Info().
		Int("success_count", result.Success).
		Int("failed_count", result.Failed).
		Msg("Batch events tracked")

	return result, nil
}

// GetEvents retrieves events based on query parameters
func (s *AnalyticsService) GetEvents(ctx context.Context, query *models.EventQuery) ([]*models.Event, error) {
	// Set default limits
	if query.Limit == 0 {
		query.Limit = 100
	}
	if query.Limit > 1000 {
		query.Limit = 1000
	}

	// Try cache first for simple queries
	cacheKey := s.buildEventCacheKey(query)
	if cached := s.getFromCache(cacheKey); cached != nil {
		if events, ok := cached.([]*models.Event); ok {
			log.Debug().Str("cache_key", cacheKey).Msg("Events retrieved from cache")
			return events, nil
		}
	}

	// Retrieve from storage
	events, err := s.storage.GetEvents(ctx, query)
	if err != nil {
		log.Error().
			Err(err).
			Interface("query", query).
			Msg("Failed to retrieve events")
		return nil, fmt.Errorf("failed to retrieve events: %w", err)
	}

	// Cache results for future queries
	s.setInCache(cacheKey, events, 5*time.Minute)

	log.Debug().
		Int("count", len(events)).
		Interface("query", query).
		Msg("Retrieved events")

	return events, nil
}

// GetEventsByUser retrieves events for a specific user
func (s *AnalyticsService) GetEventsByUser(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) ([]*models.Event, error) {
	query := &models.EventQuery{
		UserID:    &userID,
		StartTime: startTime,
		EndTime:   endTime,
		Limit:     1000,
	}

	return s.GetEvents(ctx, query)
}

// GetEventsBySession retrieves events for a specific session
func (s *AnalyticsService) GetEventsBySession(ctx context.Context, sessionID string, startTime, endTime *time.Time) ([]*models.Event, error) {
	query := &models.EventQuery{
		SessionID: sessionID,
		StartTime: startTime,
		EndTime:   endTime,
		Limit:     1000,
	}

	return s.GetEvents(ctx, query)
}

// GetFunnelAnalysis performs funnel analysis
func (s *AnalyticsService) GetFunnelAnalysis(ctx context.Context, steps []string, filters map[string]interface{}, startTime, endTime time.Time) (map[string]interface{}, error) {
	if len(steps) == 0 {
		return nil, fmt.Errorf("funnel steps cannot be empty")
	}

	if len(steps) > 10 {
		return nil, fmt.Errorf("too many funnel steps (max 10)")
	}

	// This is a simplified implementation - in production, you'd want more sophisticated funnel analysis
	funnelData := make(map[string]interface{})
	stepCounts := make([]int64, len(steps))

	for i, step := range steps {
		query := &models.EventQuery{
			Name:      step,
			StartTime: &startTime,
			EndTime:   &endTime,
			Limit:     10000,
		}

		// Apply filters if provided
		if orgID, ok := filters["organization_id"]; ok {
			if id, err := uuid.Parse(fmt.Sprintf("%v", orgID)); err == nil {
				query.OrganizationID = &id
			}
		}

		events, err := s.GetEvents(ctx, query)
		if err != nil {
			log.Error().Err(err).Str("step", step).Msg("Failed to get funnel step data")
			continue
		}

		stepCounts[i] = int64(len(events))
	}

	// Calculate conversion rates
	conversions := make([]float64, len(steps))
	for i, count := range stepCounts {
		if i == 0 {
			conversions[i] = 100.0 // First step is always 100%
		} else if stepCounts[0] > 0 {
			conversions[i] = float64(count) / float64(stepCounts[0]) * 100
		}
	}

	funnelData["steps"] = steps
	funnelData["counts"] = stepCounts
	funnelData["conversions"] = conversions
	funnelData["total_entered"] = stepCounts[0]
	funnelData["total_completed"] = stepCounts[len(stepCounts)-1]
	funnelData["overall_conversion"] = conversions[len(conversions)-1]

	log.Info().
		Interface("steps", steps).
		Interface("counts", stepCounts).
		Msg("Funnel analysis completed")

	return funnelData, nil
}

// GetCohortAnalysis performs cohort analysis
func (s *AnalyticsService) GetCohortAnalysis(ctx context.Context, cohortPeriod string, analysisPeriod string, startDate, endDate time.Time) (map[string]interface{}, error) {
	// This is a simplified implementation
	cohortData := make(map[string]interface{})

	// For now, return a placeholder response
	cohortData["cohort_period"] = cohortPeriod
	cohortData["analysis_period"] = analysisPeriod
	cohortData["start_date"] = startDate
	cohortData["end_date"] = endDate
	cohortData["message"] = "Cohort analysis implementation pending"

	log.Info().
		Str("cohort_period", cohortPeriod).
		Str("analysis_period", analysisPeriod).
		Msg("Cohort analysis requested")

	return cohortData, nil
}

// GetRetentionAnalysis performs retention analysis
func (s *AnalyticsService) GetRetentionAnalysis(ctx context.Context, period string, startDate, endDate time.Time, filters map[string]interface{}) (map[string]interface{}, error) {
	retentionData := make(map[string]interface{})

	// This is a simplified implementation
	retentionData["period"] = period
	retentionData["start_date"] = startDate
	retentionData["end_date"] = endDate
	retentionData["message"] = "Retention analysis implementation pending"

	log.Info().
		Str("period", period).
		Msg("Retention analysis requested")

	return retentionData, nil
}

// GetInsights generates analytics insights
func (s *AnalyticsService) GetInsights(ctx context.Context, filters map[string]interface{}, startTime, endTime time.Time) (map[string]interface{}, error) {
	insights := make(map[string]interface{})

	// Get basic event statistics
	query := &models.EventQuery{
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     10000,
	}

	// Apply filters
	if orgID, ok := filters["organization_id"]; ok {
		if id, err := uuid.Parse(fmt.Sprintf("%v", orgID)); err == nil {
			query.OrganizationID = &id
		}
	}

	events, err := s.GetEvents(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get events for insights: %w", err)
	}

	// Calculate insights
	eventCounts := make(map[string]int)
	categoryCounts := make(map[string]int)
	userCounts := make(map[string]bool)

	for _, event := range events {
		eventCounts[event.Name]++
		if event.Category != "" {
			categoryCounts[event.Category]++
		}
		if event.UserID != nil {
			userCounts[event.UserID.String()] = true
		}
	}

	insights["total_events"] = len(events)
	insights["unique_users"] = len(userCounts)
	insights["top_events"] = s.getTopN(eventCounts, 10)
	insights["top_categories"] = s.getTopN(categoryCounts, 5)
	insights["time_range"] = map[string]interface{}{
		"start": startTime,
		"end":   endTime,
	}

	log.Info().
		Int("total_events", len(events)).
		Int("unique_users", len(userCounts)).
		Msg("Analytics insights generated")

	return insights, nil
}

// Helper methods

func (s *AnalyticsService) validateEventRequest(request *models.EventRequest) error {
	if request.Name == "" {
		return fmt.Errorf("event name is required")
	}

	if len(request.Name) > 255 {
		return fmt.Errorf("event name too long (max 255 characters)")
	}

	if request.Type == "" {
		return fmt.Errorf("event type is required")
	}

	// Validate event type
	validTypes := map[models.EventType]bool{
		models.EventTypePageView:    true,
		models.EventTypeUserAction:  true,
		models.EventTypeAPICall:     true,
		models.EventTypeConversion:  true,
		models.EventTypeError:       true,
		models.EventTypePerformance: true,
		models.EventTypeCustom:      true,
	}

	if !validTypes[request.Type] {
		return fmt.Errorf("invalid event type: %s", request.Type)
	}

	return nil
}

func (s *AnalyticsService) enhanceEventWithContext(event *models.Event, ctx *models.AnalyticsContext) {
	if event.UserID == nil && ctx.UserID != nil {
		event.UserID = ctx.UserID
	}
	if event.OrganizationID == nil && ctx.OrganizationID != nil {
		event.OrganizationID = ctx.OrganizationID
	}
	if event.ShopID == nil && ctx.ShopID != nil {
		event.ShopID = ctx.ShopID
	}
	if event.SessionID == "" && ctx.SessionID != "" {
		event.SessionID = ctx.SessionID
	}
	if event.IPAddress == "" && ctx.IPAddress != "" {
		event.IPAddress = ctx.IPAddress
	}
	if event.UserAgent == "" && ctx.UserAgent != "" {
		event.UserAgent = ctx.UserAgent
	}
}

func (s *AnalyticsService) updateEventMetrics(ctx context.Context, event *models.Event) {
	// Update metrics based on the event
	metricRequest := &models.MetricRequest{
		Name:           fmt.Sprintf("events.%s", event.Name),
		Type:           models.MetricTypeCounter,
		Value:          1,
		Tags:           map[string]string{
			"event_type": string(event.Type),
			"category":   event.Category,
		},
		UserID:         event.UserID,
		OrganizationID: event.OrganizationID,
		ShopID:         event.ShopID,
		Source:         "analytics_service",
	}

	if err := s.metrics.RecordMetric(ctx, metricRequest); err != nil {
		log.Error().Err(err).Str("event_id", event.ID.String()).Msg("Failed to update event metrics")
	}
}

func (s *AnalyticsService) invalidateEventCaches(event *models.Event) {
	// Invalidate relevant cache keys
	patterns := []string{
		"events:*",
		fmt.Sprintf("events:type:%s", event.Type),
		fmt.Sprintf("events:name:%s", event.Name),
	}

	if event.OrganizationID != nil {
		patterns = append(patterns, fmt.Sprintf("events:org:%s", event.OrganizationID.String()))
	}

	for _, pattern := range patterns {
		cacheKey := s.keyBuilder.Build(pattern)
		s.cache.Delete(context.Background(), cacheKey)
	}
}

func (s *AnalyticsService) buildEventCacheKey(query *models.EventQuery) string {
	// Build a deterministic cache key from query parameters
	key := "events"
	
	if query.Type != nil {
		key += fmt.Sprintf(":type:%s", *query.Type)
	}
	if query.Name != "" {
		key += fmt.Sprintf(":name:%s", query.Name)
	}
	if query.OrganizationID != nil {
		key += fmt.Sprintf(":org:%s", query.OrganizationID.String())
	}
	if query.UserID != nil {
		key += fmt.Sprintf(":user:%s", query.UserID.String())
	}
	if query.StartTime != nil {
		key += fmt.Sprintf(":start:%d", query.StartTime.Unix())
	}
	if query.EndTime != nil {
		key += fmt.Sprintf(":end:%d", query.EndTime.Unix())
	}
	key += fmt.Sprintf(":limit:%d:offset:%d", query.Limit, query.Offset)

	return s.keyBuilder.Build(key)
}

func (s *AnalyticsService) getFromCache(key string) interface{} {
	data, err := s.cache.Get(context.Background(), key)
	if err != nil {
		return nil
	}

	var result interface{}
	if err := json.Unmarshal([]byte(data), &result); err != nil {
		return nil
	}

	return result
}

func (s *AnalyticsService) setInCache(key string, value interface{}, ttl time.Duration) {
	data, err := json.Marshal(value)
	if err != nil {
		return
	}

	s.cache.Set(context.Background(), key, string(data), ttl)
}

func (s *AnalyticsService) getTopN(counts map[string]int, n int) []map[string]interface{} {
	type item struct {
		key   string
		count int
	}

	// Convert to slice and sort
	items := make([]item, 0, len(counts))
	for k, v := range counts {
		items = append(items, item{k, v})
	}

	// Simple sort (in production, use a proper sorting algorithm)
	for i := 0; i < len(items)-1; i++ {
		for j := i + 1; j < len(items); j++ {
			if items[j].count > items[i].count {
				items[i], items[j] = items[j], items[i]
			}
		}
	}

	// Take top N
	if n > len(items) {
		n = len(items)
	}

	result := make([]map[string]interface{}, n)
	for i := 0; i < n; i++ {
		result[i] = map[string]interface{}{
			"name":  items[i].key,
			"count": items[i].count,
		}
	}

	return result
}