package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AutoTranslationProcessor handles background translation processing for analytics service
type AutoTranslationProcessor struct {
	multiLangClient *MultiLangClient
	workerCount     int
	jobQueue        chan *AutoTranslationJob
	resultQueue     chan *AutoTranslationResult
	workers         []*worker
	running         bool
	mutex           sync.RWMutex
	logger          *logrus.Logger
}

// AutoTranslationJob represents a translation job for analytics-specific content
type AutoTranslationJob struct {
	ID                  uuid.UUID `json:"id"`
	ServiceID           string    `json:"service_id"`
	TranslationType     string    `json:"translation_type"` // dashboard_title, dashboard_description, metric_name, metric_description, chart_title, chart_label, report_title, report_description, event_name, event_description, filter_label, error, alert, insight_title, insight_description, time_range
	ContentType         string    `json:"content_type,omitempty"` // For categorizing content within translation type
	DashboardType       string    `json:"dashboard_type,omitempty"`
	MetricType          string    `json:"metric_type,omitempty"`
	ChartType           string    `json:"chart_type,omitempty"`
	ReportType          string    `json:"report_type,omitempty"`
	EventCategory       string    `json:"event_category,omitempty"`
	FilterType          string    `json:"filter_type,omitempty"`
	ErrorCode           string    `json:"error_code,omitempty"`
	AlertType           string    `json:"alert_type,omitempty"`
	InsightType         string    `json:"insight_type,omitempty"`
	RangeType           string    `json:"range_type,omitempty"`
	SourceText          string    `json:"source_text"`
	TargetLanguage      string    `json:"target_language"`
	ConfidenceThreshold float64   `json:"confidence_threshold"`
	CreatedAt          time.Time `json:"created_at"`
}

// AutoTranslationResult represents the result of a translation job
type AutoTranslationResult struct {
	JobID           uuid.UUID `json:"job_id"`
	TranslatedText  string    `json:"translated_text"`
	Confidence      float64   `json:"confidence"`
	AutoTranslated  bool      `json:"auto_translated"`
	ProcessedAt     time.Time `json:"processed_at"`
	Error           string    `json:"error,omitempty"`
}

// worker represents a background worker for processing translation jobs
type worker struct {
	id        int
	processor *AutoTranslationProcessor
	quit      chan bool
}

// NewAutoTranslationProcessor creates a new auto-translation processor
func NewAutoTranslationProcessor(multiLangClient *MultiLangClient, workerCount int, logger *logrus.Logger) *AutoTranslationProcessor {
	return &AutoTranslationProcessor{
		multiLangClient: multiLangClient,
		workerCount:     workerCount,
		jobQueue:        make(chan *AutoTranslationJob, 100),
		resultQueue:     make(chan *AutoTranslationResult, 100),
		logger:          logger,
	}
}

// Start starts the auto-translation processor
func (p *AutoTranslationProcessor) Start() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.running {
		return fmt.Errorf("processor is already running")
	}

	p.logger.Infof("Starting auto-translation processor with %d workers", p.workerCount)

	// Start workers
	p.workers = make([]*worker, p.workerCount)
	for i := 0; i < p.workerCount; i++ {
		p.workers[i] = &worker{
			id:        i + 1,
			processor: p,
			quit:      make(chan bool),
		}
		go p.workers[i].start()
	}

	// Start result processor
	go p.processResults()

	p.running = true
	p.logger.Info("Auto-translation processor started successfully")
	return nil
}

// Stop stops the auto-translation processor
func (p *AutoTranslationProcessor) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.running {
		return fmt.Errorf("processor is not running")
	}

	p.logger.Info("Stopping auto-translation processor...")

	// Stop all workers
	for _, worker := range p.workers {
		worker.quit <- true
	}

	p.running = false
	p.logger.Info("Auto-translation processor stopped")
	return nil
}

// SubmitJob submits a translation job for processing
func (p *AutoTranslationProcessor) SubmitJob(job *AutoTranslationJob) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if !p.running {
		return fmt.Errorf("processor is not running")
	}

	select {
	case p.jobQueue <- job:
		return nil
	default:
		return fmt.Errorf("job queue is full")
	}
}

// ProcessAnalyticsTranslation processes an analytics-related translation
func (p *AutoTranslationProcessor) ProcessAnalyticsTranslation(dashboardType, metricType, chartType, translationType, text, targetLanguage string, confidenceThreshold float64) (*AutoTranslationResult, error) {
	ctx := context.Background()
	var translatedText string
	var autoTranslated bool
	var err error

	switch translationType {
	case "dashboard_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateDashboardTitle(ctx, dashboardType, text, targetLanguage)
	case "dashboard_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateDashboardDescription(ctx, dashboardType, text, targetLanguage)
	case "metric_name":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateMetricName(ctx, metricType, text, targetLanguage)
	case "metric_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateMetricDescription(ctx, metricType, text, targetLanguage)
	case "chart_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateChartTitle(ctx, chartType, text, targetLanguage)
	case "chart_label":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateChartLabel(ctx, chartType, text, targetLanguage)
	default:
		return nil, fmt.Errorf("unsupported translation type: %s", translationType)
	}

	if err != nil {
		return nil, err
	}

	return &AutoTranslationResult{
		JobID:          uuid.New(),
		TranslatedText: translatedText,
		Confidence:     getConfidenceScore(translatedText, autoTranslated),
		AutoTranslated: autoTranslated,
		ProcessedAt:    time.Now(),
	}, nil
}

// GetQueueStatus returns the current status of the processor queues
func (p *AutoTranslationProcessor) GetQueueStatus() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return map[string]interface{}{
		"running":            p.running,
		"workers":            len(p.workers),
		"job_queue_length":   len(p.jobQueue),
		"result_queue_length": len(p.resultQueue),
	}
}

// worker methods

// start starts the worker
func (w *worker) start() {
	w.processor.logger.Debugf("Worker %d started", w.id)

	for {
		select {
		case job := <-w.processor.jobQueue:
			w.processJob(job)
		case <-w.quit:
			w.processor.logger.Debugf("Worker %d stopped", w.id)
			return
		}
	}
}

// processJob processes a single translation job
func (w *worker) processJob(job *AutoTranslationJob) {
	w.processor.logger.Debugf("Worker %d processing job %s", w.id, job.ID)

	result, err := w.processor.processTranslationJob(job)
	if err != nil {
		w.processor.logger.Errorf("Translation job %s failed: %v", job.ID, err)
		result = &AutoTranslationResult{
			JobID:       job.ID,
			ProcessedAt: time.Now(),
			Error:       err.Error(),
		}
	}

	// Send result to result queue
	select {
	case w.processor.resultQueue <- result:
		// Result sent successfully
	default:
		w.processor.logger.Warnf("Result queue full, dropping result for job %s", job.ID)
	}
}

// processTranslationJob processes the actual translation
func (p *AutoTranslationProcessor) processTranslationJob(job *AutoTranslationJob) (*AutoTranslationResult, error) {
	ctx := context.Background()
	var translatedText string
	var autoTranslated bool
	var err error

	switch job.TranslationType {
	case "dashboard_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateDashboardTitle(ctx, job.DashboardType, job.SourceText, job.TargetLanguage)
	case "dashboard_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateDashboardDescription(ctx, job.DashboardType, job.SourceText, job.TargetLanguage)
	case "metric_name":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateMetricName(ctx, job.MetricType, job.SourceText, job.TargetLanguage)
	case "metric_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateMetricDescription(ctx, job.MetricType, job.SourceText, job.TargetLanguage)
	case "chart_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateChartTitle(ctx, job.ChartType, job.SourceText, job.TargetLanguage)
	case "chart_label":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateChartLabel(ctx, job.ChartType, job.SourceText, job.TargetLanguage)
	case "report_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateReportTitle(ctx, job.ReportType, job.SourceText, job.TargetLanguage)
	case "report_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateReportDescription(ctx, job.ReportType, job.SourceText, job.TargetLanguage)
	case "event_name":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateEventName(ctx, job.EventCategory, job.SourceText, job.TargetLanguage)
	case "event_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateEventDescription(ctx, job.EventCategory, job.SourceText, job.TargetLanguage)
	case "filter_label":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateFilterLabel(ctx, job.FilterType, job.SourceText, job.TargetLanguage)
	case "error":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateErrorMessage(ctx, job.ErrorCode, job.SourceText, job.TargetLanguage)
	case "alert":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateAlertMessage(ctx, job.AlertType, job.SourceText, job.TargetLanguage)
	case "insight_title":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateInsightTitle(ctx, job.InsightType, job.SourceText, job.TargetLanguage)
	case "insight_description":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateInsightDescription(ctx, job.InsightType, job.SourceText, job.TargetLanguage)
	case "time_range":
		translatedText, autoTranslated, err = p.multiLangClient.TranslateTimeRangeLabel(ctx, job.RangeType, job.SourceText, job.TargetLanguage)
	default:
		return nil, fmt.Errorf("unsupported translation type: %s", job.TranslationType)
	}

	if err != nil {
		return nil, err
	}

	return &AutoTranslationResult{
		JobID:          job.ID,
		TranslatedText: translatedText,
		Confidence:     getConfidenceScore(translatedText, autoTranslated),
		AutoTranslated: autoTranslated,
		ProcessedAt:    time.Now(),
	}, nil
}

// processResults processes translation results
func (p *AutoTranslationProcessor) processResults() {
	for result := range p.resultQueue {
		if result.Error != "" {
			p.logger.Warnf("Translation result %s failed: %s", result.JobID, result.Error)
		} else {
			p.logger.Debugf("Translation result %s completed: confidence %.2f", result.JobID, result.Confidence)
		}

		// Here you could store results in database or send to another service
		// For now, we just log the completion
	}
}

// getConfidenceScore calculates a confidence score for the translation
func getConfidenceScore(translatedText string, autoTranslated bool) float64 {
	if !autoTranslated {
		return 0.0 // Human translation or no translation occurred
	}
	
	// Simple confidence calculation based on text length and complexity
	// In a real system, this would be provided by the translation service
	if len(translatedText) > 0 {
		return 0.85 // Default confidence for auto-translated text
	}
	return 0.0
}