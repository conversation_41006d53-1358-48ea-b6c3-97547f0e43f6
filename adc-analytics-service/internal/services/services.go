package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/adc-analytics-service/internal/models"
	"github.com/adc-analytics-service/internal/storage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// ReportsService handles report generation and management
type ReportsService struct {
	storage    storage.Storage
	cache      storage.Cache
	analytics  *AnalyticsService
	metrics    *MetricsService
	keyBuilder *storage.CacheKeyBuilder
}

// NewReportsService creates a new reports service instance
func NewReportsService(storageInstance storage.Storage, cache storage.Cache, analytics *AnalyticsService, metrics *MetricsService) *ReportsService {
	return &ReportsService{
		storage:    storageInstance,
		cache:      cache,
		analytics:  analytics,
		metrics:    metrics,
		keyBuilder: storage.NewCacheKeyBuilder("adc_reports:"),
	}
}

// CreateReport creates a new report
func (s *ReportsService) CreateReport(ctx context.Context, request *models.ReportRequest, context *models.AnalyticsContext) (*models.Report, error) {
	// Validate report request
	if err := s.validateReportRequest(request); err != nil {
		return nil, fmt.Errorf("invalid report request: %w", err)
	}

	// Convert request to report
	report, err := request.ToReport()
	if err != nil {
		return nil, fmt.Errorf("failed to convert report request: %w", err)
	}

	// Set created by if available
	if context != nil && context.UserID != nil {
		report.CreatedBy = context.UserID
	}

	// Store report
	if err := s.storage.CreateReport(ctx, report); err != nil {
		log.Error().
			Err(err).
			Str("report_name", report.Name).
			Msg("Failed to create report")
		return nil, fmt.Errorf("failed to create report: %w", err)
	}

	log.Info().
		Str("report_id", report.ID.String()).
		Str("report_name", report.Name).
		Str("report_type", string(report.Type)).
		Msg("Report created")

	return report, nil
}

// GetReports retrieves reports based on query parameters
func (s *ReportsService) GetReports(ctx context.Context, query *models.ReportQuery) ([]*models.Report, error) {
	// Set default limits
	if query.Limit == 0 {
		query.Limit = 50
	}
	if query.Limit > 200 {
		query.Limit = 200
	}

	reports, err := s.storage.GetReports(ctx, query)
	if err != nil {
		log.Error().
			Err(err).
			Interface("query", query).
			Msg("Failed to retrieve reports")
		return nil, fmt.Errorf("failed to retrieve reports: %w", err)
	}

	log.Debug().
		Int("count", len(reports)).
		Interface("query", query).
		Msg("Retrieved reports")

	return reports, nil
}

// GetReport retrieves a single report by ID
func (s *ReportsService) GetReport(ctx context.Context, id string) (*models.Report, error) {
	report, err := s.storage.GetReport(ctx, id)
	if err != nil {
		log.Error().
			Err(err).
			Str("report_id", id).
			Msg("Failed to retrieve report")
		return nil, fmt.Errorf("failed to retrieve report: %w", err)
	}

	return report, nil
}

// UpdateReport updates an existing report
func (s *ReportsService) UpdateReport(ctx context.Context, report *models.Report) error {
	if err := s.storage.UpdateReport(ctx, report); err != nil {
		log.Error().
			Err(err).
			Str("report_id", report.ID.String()).
			Msg("Failed to update report")
		return fmt.Errorf("failed to update report: %w", err)
	}

	log.Info().
		Str("report_id", report.ID.String()).
		Msg("Report updated")

	return nil
}

// DeleteReport deletes a report
func (s *ReportsService) DeleteReport(ctx context.Context, id string) error {
	if err := s.storage.DeleteReport(ctx, id); err != nil {
		log.Error().
			Err(err).
			Str("report_id", id).
			Msg("Failed to delete report")
		return fmt.Errorf("failed to delete report: %w", err)
	}

	log.Info().
		Str("report_id", id).
		Msg("Report deleted")

	return nil
}

// GenerateReport generates a report based on its configuration
func (s *ReportsService) GenerateReport(ctx context.Context, reportID uuid.UUID) error {
	report, err := s.GetReport(ctx, reportID.String())
	if err != nil {
		return err
	}

	// Update status to running
	report.Status = models.ReportStatusRunning
	report.StartedAt = &time.Time{}
	*report.StartedAt = time.Now()

	if err := s.UpdateReport(ctx, report); err != nil {
		return err
	}

	// Generate report data based on type
	var reportData map[string]interface{}
	var generateErr error

	switch report.Type {
	case models.ReportTypeAnalytics:
		reportData, generateErr = s.generateAnalyticsReport(ctx, report)
	case models.ReportTypeMetrics:
		reportData, generateErr = s.generateMetricsReport(ctx, report)
	case models.ReportTypeFunnel:
		reportData, generateErr = s.generateFunnelReport(ctx, report)
	case models.ReportTypeCohort:
		reportData, generateErr = s.generateCohortReport(ctx, report)
	case models.ReportTypeRetention:
		reportData, generateErr = s.generateRetentionReport(ctx, report)
	default:
		generateErr = fmt.Errorf("unsupported report type: %s", report.Type)
	}

	// Update report with results
	now := time.Now()
	report.CompletedAt = &now

	if generateErr != nil {
		report.Status = models.ReportStatusFailed
		report.ErrorMessage = generateErr.Error()
	} else {
		report.Status = models.ReportStatusCompleted
		// Convert data to JSON string
		if dataJSON, err := json.Marshal(reportData); err == nil {
			report.Data = string(dataJSON)
		}
	}

	if err := s.UpdateReport(ctx, report); err != nil {
		log.Error().Err(err).Msg("Failed to update report status")
	}

	if generateErr != nil {
		log.Error().
			Err(generateErr).
			Str("report_id", reportID.String()).
			Msg("Report generation failed")
		return generateErr
	}

	log.Info().
		Str("report_id", reportID.String()).
		Msg("Report generated successfully")

	return nil
}

// AggregationService handles data aggregation tasks
type AggregationService struct {
	storage storage.Storage
	cache   storage.Cache
}

// NewAggregationService creates a new aggregation service instance
func NewAggregationService(storageInstance storage.Storage, cache storage.Cache) *AggregationService {
	return &AggregationService{
		storage: storageInstance,
		cache:   cache,
	}
}

// ProcessPendingAggregations processes pending data aggregations
func (s *AggregationService) ProcessPendingAggregations(ctx context.Context) error {
	log.Info().Msg("Processing pending aggregations")

	// This is a placeholder for aggregation logic
	// In a real implementation, you would:
	// 1. Find data that needs aggregation
	// 2. Perform aggregation calculations
	// 3. Store aggregated results
	// 4. Update aggregation status

	return nil
}

// CleanupOldData removes old data based on retention policies
func (s *AggregationService) CleanupOldData(ctx context.Context) error {
	log.Info().Msg("Cleaning up old data")

	// This is a placeholder for cleanup logic
	// In a real implementation, you would:
	// 1. Define retention policies for different data types
	// 2. Find data older than retention period
	// 3. Archive or delete old data
	// 4. Update cleanup metrics

	return nil
}

// DashboardService handles dashboard operations
type DashboardService struct {
	storage   storage.Storage
	cache     storage.Cache
	analytics *AnalyticsService
	metrics   *MetricsService
}

// NewDashboardService creates a new dashboard service instance
func NewDashboardService(storageInstance storage.Storage, cache storage.Cache, analytics *AnalyticsService, metrics *MetricsService) *DashboardService {
	return &DashboardService{
		storage:   storageInstance,
		cache:     cache,
		analytics: analytics,
		metrics:   metrics,
	}
}

// GetDashboardData retrieves dashboard data with widgets
func (s *DashboardService) GetDashboardData(ctx context.Context, dashboardID string, timeRange map[string]time.Time) (*models.DashboardResponse, error) {
	// Get dashboard configuration
	dashboard, err := s.storage.GetDashboard(ctx, dashboardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard: %w", err)
	}

	// Parse dashboard configuration to get widgets
	config, err := s.parseDashboardConfig(dashboard.Configuration)
	if err != nil {
		return nil, fmt.Errorf("failed to parse dashboard config: %w", err)
	}

	// Generate data for each widget
	widgets := make([]models.Widget, 0)
	for _, widgetConfig := range config.Widgets {
		widget, err := s.generateWidgetData(ctx, widgetConfig, timeRange)
		if err != nil {
			log.Error().Err(err).Str("widget_id", widgetConfig.ID).Msg("Failed to generate widget data")
			continue
		}
		widgets = append(widgets, *widget)
	}

	return &models.DashboardResponse{
		ID:             dashboard.ID,
		Name:           dashboard.Name,
		Description:    dashboard.Description,
		Widgets:        widgets,
		UserID:         dashboard.UserID,
		OrganizationID: dashboard.OrganizationID,
		ShopID:         dashboard.ShopID,
		IsPublic:       dashboard.IsPublic,
		CreatedBy:      dashboard.CreatedBy,
		CreatedAt:      dashboard.CreatedAt,
		UpdatedAt:      dashboard.UpdatedAt,
	}, nil
}

// Helper methods for ReportsService

func (s *ReportsService) validateReportRequest(request *models.ReportRequest) error {
	if request.Name == "" {
		return fmt.Errorf("report name is required")
	}

	if len(request.Name) > 255 {
		return fmt.Errorf("report name too long (max 255 characters)")
	}

	if request.Type == "" {
		return fmt.Errorf("report type is required")
	}

	// Validate report type
	validTypes := map[models.ReportType]bool{
		models.ReportTypeAnalytics: true,
		models.ReportTypeMetrics:   true,
		models.ReportTypeFunnel:    true,
		models.ReportTypeCohort:    true,
		models.ReportTypeRetention: true,
		models.ReportTypeCustom:    true,
	}

	if !validTypes[request.Type] {
		return fmt.Errorf("invalid report type: %s", request.Type)
	}

	return nil
}

func (s *ReportsService) generateAnalyticsReport(ctx context.Context, report *models.Report) (map[string]interface{}, error) {
	// Parse report configuration
	config, err := report.GetParsedConfiguration()
	if err != nil {
		return nil, fmt.Errorf("failed to parse report configuration: %w", err)
	}

	// Extract time range from config
	startTime := time.Now().AddDate(0, 0, -7) // Default to last 7 days
	endTime := time.Now()

	if startStr, ok := config["start_time"].(string); ok {
		if parsed, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = parsed
		}
	}

	if endStr, ok := config["end_time"].(string); ok {
		if parsed, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = parsed
		}
	}

	// Generate insights
	filters := make(map[string]interface{})
	if report.OrganizationID != nil {
		filters["organization_id"] = report.OrganizationID.String()
	}

	insights, err := s.analytics.GetInsights(ctx, filters, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to generate analytics insights: %w", err)
	}

	return insights, nil
}

func (s *ReportsService) generateMetricsReport(ctx context.Context, report *models.Report) (map[string]interface{}, error) {
	// Parse report configuration
	config, err := report.GetParsedConfiguration()
	if err != nil {
		return nil, fmt.Errorf("failed to parse report configuration: %w", err)
	}

	// Extract time range from config
	startTime := time.Now().AddDate(0, 0, -7) // Default to last 7 days
	endTime := time.Now()

	if startStr, ok := config["start_time"].(string); ok {
		if parsed, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = parsed
		}
	}

	if endStr, ok := config["end_time"].(string); ok {
		if parsed, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = parsed
		}
	}

	// Generate metrics summary
	filters := make(map[string]interface{})
	if report.OrganizationID != nil {
		filters["organization_id"] = report.OrganizationID.String()
	}

	summary, err := s.metrics.GetMetricsSummary(ctx, startTime, endTime, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to generate metrics summary: %w", err)
	}

	return summary, nil
}

func (s *ReportsService) generateFunnelReport(ctx context.Context, report *models.Report) (map[string]interface{}, error) {
	config, err := report.GetParsedConfiguration()
	if err != nil {
		return nil, fmt.Errorf("failed to parse report configuration: %w", err)
	}

	// Extract funnel steps from config
	stepsInterface, ok := config["steps"]
	if !ok {
		return nil, fmt.Errorf("funnel steps not specified in configuration")
	}

	steps := make([]string, 0)
	if stepsArray, ok := stepsInterface.([]interface{}); ok {
		for _, step := range stepsArray {
			if stepStr, ok := step.(string); ok {
				steps = append(steps, stepStr)
			}
		}
	}

	if len(steps) == 0 {
		return nil, fmt.Errorf("no valid funnel steps found")
	}

	// Extract time range
	startTime := time.Now().AddDate(0, 0, -7)
	endTime := time.Now()

	filters := make(map[string]interface{})
	if report.OrganizationID != nil {
		filters["organization_id"] = report.OrganizationID.String()
	}

	return s.analytics.GetFunnelAnalysis(ctx, steps, filters, startTime, endTime)
}

func (s *ReportsService) generateCohortReport(ctx context.Context, report *models.Report) (map[string]interface{}, error) {
	config, err := report.GetParsedConfiguration()
	if err != nil {
		return nil, fmt.Errorf("failed to parse report configuration: %w", err)
	}

	cohortPeriod := "week"
	if period, ok := config["cohort_period"].(string); ok {
		cohortPeriod = period
	}

	analysisPeriod := "week"
	if period, ok := config["analysis_period"].(string); ok {
		analysisPeriod = period
	}

	startTime := time.Now().AddDate(0, 0, -30)
	endTime := time.Now()

	return s.analytics.GetCohortAnalysis(ctx, cohortPeriod, analysisPeriod, startTime, endTime)
}

func (s *ReportsService) generateRetentionReport(ctx context.Context, report *models.Report) (map[string]interface{}, error) {
	config, err := report.GetParsedConfiguration()
	if err != nil {
		return nil, fmt.Errorf("failed to parse report configuration: %w", err)
	}

	period := "week"
	if p, ok := config["period"].(string); ok {
		period = p
	}

	startTime := time.Now().AddDate(0, 0, -30)
	endTime := time.Now()

	filters := make(map[string]interface{})
	if report.OrganizationID != nil {
		filters["organization_id"] = report.OrganizationID.String()
	}

	return s.analytics.GetRetentionAnalysis(ctx, period, startTime, endTime, filters)
}

// Helper types and methods for DashboardService

type DashboardConfig struct {
	Widgets []WidgetConfig `json:"widgets"`
}

type WidgetConfig struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Title         string                 `json:"title"`
	Configuration map[string]interface{} `json:"configuration"`
	Position      map[string]int         `json:"position"`
}

func (s *DashboardService) parseDashboardConfig(configStr string) (*DashboardConfig, error) {
	if configStr == "" {
		return &DashboardConfig{Widgets: []WidgetConfig{}}, nil
	}

	var config DashboardConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		return nil, err
	}

	return &config, nil
}

func (s *DashboardService) generateWidgetData(ctx context.Context, config WidgetConfig, timeRange map[string]time.Time) (*models.Widget, error) {
	widget := &models.Widget{
		ID:            config.ID,
		Type:          config.Type,
		Title:         config.Title,
		Configuration: config.Configuration,
		Position:      config.Position,
		Data:          make(map[string]interface{}),
	}

	// Generate data based on widget type
	switch config.Type {
	case "kpi":
		data, err := s.generateKPIData(ctx, config, timeRange)
		if err != nil {
			return nil, err
		}
		widget.Data = data

	case "chart":
		data, err := s.generateChartData(ctx, config, timeRange)
		if err != nil {
			return nil, err
		}
		widget.Data = data

	case "table":
		data, err := s.generateTableData(ctx, config, timeRange)
		if err != nil {
			return nil, err
		}
		widget.Data = data

	default:
		widget.Data["message"] = fmt.Sprintf("Widget type '%s' not implemented", config.Type)
	}

	return widget, nil
}

func (s *DashboardService) generateKPIData(ctx context.Context, config WidgetConfig, timeRange map[string]time.Time) (map[string]interface{}, error) {
	// Placeholder KPI data generation
	return map[string]interface{}{
		"value":       1234.56,
		"unit":        "users",
		"change":      15.2,
		"trend":       "up",
		"description": "Total active users",
	}, nil
}

func (s *DashboardService) generateChartData(ctx context.Context, config WidgetConfig, timeRange map[string]time.Time) (map[string]interface{}, error) {
	// Placeholder chart data generation
	return map[string]interface{}{
		"type": "line",
		"data": []map[string]interface{}{
			{"x": "2024-01-01", "y": 100},
			{"x": "2024-01-02", "y": 120},
			{"x": "2024-01-03", "y": 90},
		},
	}, nil
}

func (s *DashboardService) generateTableData(ctx context.Context, config WidgetConfig, timeRange map[string]time.Time) (map[string]interface{}, error) {
	// Placeholder table data generation
	return map[string]interface{}{
		"columns": []string{"Name", "Value", "Change"},
		"rows": [][]interface{}{
			{"Metric A", 123, "+5%"},
			{"Metric B", 456, "-2%"},
			{"Metric C", 789, "+12%"},
		},
	}, nil
}