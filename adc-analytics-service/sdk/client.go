package sdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

// EventType represents the type of an analytics event
type EventType string

const (
	EventTypePageView     EventType = "page_view"
	EventTypeClick        EventType = "click"
	EventTypeFormSubmit   EventType = "form_submit"
	EventTypeAPICall      EventType = "api_call"
	EventTypeUserAction   EventType = "user_action"
	EventTypeSystemEvent  EventType = "system_event"
	EventTypeError        EventType = "error"
	EventTypeCustom       EventType = "custom"
)

// MetricType represents the type of a metric
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
	MetricTypeTimer     MetricType = "timer"
)

// AggregationType represents the type of aggregation
type AggregationType string

const (
	AggregationSum        AggregationType = "sum"
	AggregationAvg        AggregationType = "avg"
	AggregationMin        AggregationType = "min"
	AggregationMax        AggregationType = "max"
	AggregationCount      AggregationType = "count"
	AggregationPercentile AggregationType = "percentile"
)

// Event represents an analytics event
type Event struct {
	ID             uuid.UUID              `json:"id,omitempty"`
	EventType      EventType              `json:"event_type"`
	EventName      string                 `json:"event_name"`
	EventCategory  string                 `json:"event_category,omitempty"`
	ServiceName    string                 `json:"service_name"`
	Environment    string                 `json:"environment,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	SessionID      string                 `json:"session_id,omitempty"`
	RequestID      string                 `json:"request_id,omitempty"`
	CorrelationID  string                 `json:"correlation_id,omitempty"`
	TraceID        string                 `json:"trace_id,omitempty"`
	SpanID         string                 `json:"span_id,omitempty"`
	Hostname       string                 `json:"hostname,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Referrer       string                 `json:"referrer,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	Tags           []string               `json:"tags,omitempty"`
	DurationMS     *int                   `json:"duration_ms,omitempty"`
	ResponseSize   *int                   `json:"response_size,omitempty"`
	StatusCode     *int                   `json:"status_code,omitempty"`
	Success        *bool                  `json:"success,omitempty"`
	ErrorMessage   string                 `json:"error_message,omitempty"`
	Timestamp      *time.Time             `json:"timestamp,omitempty"`
	CreatedAt      time.Time              `json:"created_at,omitempty"`
}

// Metric represents an analytics metric
type Metric struct {
	ID             uuid.UUID              `json:"id,omitempty"`
	MetricName     string                 `json:"metric_name"`
	MetricType     MetricType             `json:"metric_type"`
	Unit           string                 `json:"unit,omitempty"`
	ServiceName    string                 `json:"service_name"`
	Environment    string                 `json:"environment,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	Value          float64                `json:"value"`
	Count          int                    `json:"count,omitempty"`
	Labels         map[string]interface{} `json:"labels,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	Tags           []string               `json:"tags,omitempty"`
	Timestamp      *time.Time             `json:"timestamp,omitempty"`
	CreatedAt      time.Time              `json:"created_at,omitempty"`
}

// Dashboard represents an analytics dashboard
type Dashboard struct {
	ID             uuid.UUID              `json:"id"`
	Name           string                 `json:"name"`
	Slug           string                 `json:"slug"`
	Description    string                 `json:"description,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	Config         map[string]interface{} `json:"config"`
	Layout         map[string]interface{} `json:"layout,omitempty"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
	Category       string                 `json:"category,omitempty"`
	Tags           []string               `json:"tags,omitempty"`
	IsPublic       bool                   `json:"is_public"`
	IsTemplate     bool                   `json:"is_template"`
	IsActive       bool                   `json:"is_active"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	CreatedBy      *uuid.UUID             `json:"created_by,omitempty"`
	UpdatedBy      *uuid.UUID             `json:"updated_by,omitempty"`
}

// Aggregation represents aggregated analytics data
type Aggregation struct {
	ID               uuid.UUID         `json:"id,omitempty"`
	AggregationKey   string            `json:"aggregation_key"`
	AggregationType  AggregationType   `json:"aggregation_type"`
	TimeBucket       time.Time         `json:"time_bucket"`
	TimeWindow       string            `json:"time_window"`
	ServiceName      string            `json:"service_name,omitempty"`
	Environment      string            `json:"environment,omitempty"`
	OrganizationID   *uuid.UUID        `json:"organization_id,omitempty"`
	MetricName       string            `json:"metric_name,omitempty"`
	EventType        EventType         `json:"event_type,omitempty"`
	Value            float64           `json:"value"`
	Count            int               `json:"count"`
	MinValue         *float64          `json:"min_value,omitempty"`
	MaxValue         *float64          `json:"max_value,omitempty"`
	SumValue         *float64          `json:"sum_value,omitempty"`
	AvgValue         *float64          `json:"avg_value,omitempty"`
	P50              *float64          `json:"p50,omitempty"`
	P90              *float64          `json:"p90,omitempty"`
	P95              *float64          `json:"p95,omitempty"`
	P99              *float64          `json:"p99,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt        time.Time         `json:"created_at,omitempty"`
	UpdatedAt        time.Time         `json:"updated_at,omitempty"`
}

// EventQuery represents a query for events
type EventQuery struct {
	EventTypes     []EventType `json:"event_types,omitempty"`
	EventNames     []string    `json:"event_names,omitempty"`
	EventCategory  *string     `json:"event_category,omitempty"`
	ServiceName    *string     `json:"service_name,omitempty"`
	Environment    *string     `json:"environment,omitempty"`
	UserID         *uuid.UUID  `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID  `json:"organization_id,omitempty"`
	SessionID      *string     `json:"session_id,omitempty"`
	RequestID      *string     `json:"request_id,omitempty"`
	CorrelationID  *string     `json:"correlation_id,omitempty"`
	StartTime      *time.Time  `json:"start_time,omitempty"`
	EndTime        *time.Time  `json:"end_time,omitempty"`
	Tags           []string    `json:"tags,omitempty"`
	Success        *bool       `json:"success,omitempty"`
	Limit          int         `json:"limit,omitempty"`
	Offset         int         `json:"offset,omitempty"`
}

// MetricQuery represents a query for metrics
type MetricQuery struct {
	MetricNames    []string    `json:"metric_names,omitempty"`
	MetricTypes    []MetricType `json:"metric_types,omitempty"`
	ServiceName    *string     `json:"service_name,omitempty"`
	Environment    *string     `json:"environment,omitempty"`
	UserID         *uuid.UUID  `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID  `json:"organization_id,omitempty"`
	StartTime      *time.Time  `json:"start_time,omitempty"`
	EndTime        *time.Time  `json:"end_time,omitempty"`
	Tags           []string    `json:"tags,omitempty"`
	Limit          int         `json:"limit,omitempty"`
	Offset         int         `json:"offset,omitempty"`
}

// AggregationQuery represents a query for aggregations
type AggregationQuery struct {
	AggregationKeys []string         `json:"aggregation_keys,omitempty"`
	AggregationType *AggregationType `json:"aggregation_type,omitempty"`
	TimeWindow      *string          `json:"time_window,omitempty"`
	ServiceName     *string          `json:"service_name,omitempty"`
	Environment     *string          `json:"environment,omitempty"`
	OrganizationID  *uuid.UUID       `json:"organization_id,omitempty"`
	MetricName      *string          `json:"metric_name,omitempty"`
	EventType       *EventType       `json:"event_type,omitempty"`
	StartTime       *time.Time       `json:"start_time,omitempty"`
	EndTime         *time.Time       `json:"end_time,omitempty"`
	Limit           int              `json:"limit,omitempty"`
	Offset          int              `json:"offset,omitempty"`
}

// EventsResult represents the result of an event query
type EventsResult struct {
	Events  []Event `json:"events"`
	Total   int     `json:"total"`
	Offset  int     `json:"offset"`
	Limit   int     `json:"limit"`
	HasMore bool    `json:"has_more"`
}

// MetricsResult represents the result of a metric query
type MetricsResult struct {
	Metrics []Metric `json:"metrics"`
	Total   int      `json:"total"`
	Offset  int      `json:"offset"`
	Limit   int      `json:"limit"`
	HasMore bool     `json:"has_more"`
}

// ClientConfig represents the configuration for the Analytics Service client
type ClientConfig struct {
	BaseURL    string
	APIKey     string
	Timeout    time.Duration
	UserAgent  string
	Logger     zerolog.Logger
}

// Client represents the Analytics Service SDK client
type Client struct {
	config     ClientConfig
	httpClient *http.Client
	logger     zerolog.Logger
}

// NewClient creates a new Analytics Service client
func NewClient(config ClientConfig) *Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.UserAgent == "" {
		config.UserAgent = "adc-analytics-service-sdk/1.0.0"
	}

	client := &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger: config.Logger.With().Str("component", "analytics-service-sdk").Logger(),
	}

	return client
}

// TrackEvent tracks a single analytics event
func (c *Client) TrackEvent(event Event) error {
	return c.makeRequest("POST", "/api/v1/analytics/events", event, nil)
}

// TrackEvents tracks multiple analytics events in batch
func (c *Client) TrackEvents(events []Event) error {
	request := map[string]interface{}{
		"events": events,
	}
	return c.makeRequest("POST", "/api/v1/analytics/events/batch", request, nil)
}

// RecordMetric records a single metric
func (c *Client) RecordMetric(metric Metric) error {
	return c.makeRequest("POST", "/api/v1/metrics", metric, nil)
}

// RecordMetrics records multiple metrics in batch
func (c *Client) RecordMetrics(metrics []Metric) error {
	request := map[string]interface{}{
		"metrics": metrics,
	}
	return c.makeRequest("POST", "/api/v1/metrics/batch", request, nil)
}

// QueryEvents queries events based on the provided criteria
func (c *Client) QueryEvents(query EventQuery) (*EventsResult, error) {
	var result EventsResult
	if err := c.makeRequest("POST", "/api/v1/analytics/events/search", query, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

// QueryMetrics queries metrics based on the provided criteria
func (c *Client) QueryMetrics(query MetricQuery) (*MetricsResult, error) {
	var result MetricsResult
	if err := c.makeRequest("POST", "/api/v1/metrics/search", query, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

// GetAggregations retrieves aggregated data
func (c *Client) GetAggregations(query AggregationQuery) ([]Aggregation, error) {
	var result []Aggregation
	if err := c.makeRequest("POST", "/api/v1/analytics/aggregations", query, &result); err != nil {
		return nil, err
	}
	return result, nil
}

// CreateDashboard creates a new analytics dashboard
func (c *Client) CreateDashboard(dashboard Dashboard) (*Dashboard, error) {
	var result Dashboard
	if err := c.makeRequest("POST", "/api/v1/analytics/dashboards", dashboard, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

// GetDashboard retrieves a dashboard by ID
func (c *Client) GetDashboard(dashboardID uuid.UUID) (*Dashboard, error) {
	var result Dashboard
	path := fmt.Sprintf("/api/v1/analytics/dashboards/%s", dashboardID)
	if err := c.makeRequest("GET", path, nil, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

// UpdateDashboard updates an existing dashboard
func (c *Client) UpdateDashboard(dashboardID uuid.UUID, dashboard Dashboard) (*Dashboard, error) {
	var result Dashboard
	path := fmt.Sprintf("/api/v1/analytics/dashboards/%s", dashboardID)
	if err := c.makeRequest("PUT", path, dashboard, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

// DeleteDashboard deletes a dashboard
func (c *Client) DeleteDashboard(dashboardID uuid.UUID) error {
	path := fmt.Sprintf("/api/v1/analytics/dashboards/%s", dashboardID)
	return c.makeRequest("DELETE", path, nil, nil)
}

// GetUserAnalytics retrieves analytics data for a specific user
func (c *Client) GetUserAnalytics(userID uuid.UUID, startTime, endTime time.Time) (*EventsResult, error) {
	query := EventQuery{
		UserID:    &userID,
		StartTime: &startTime,
		EndTime:   &endTime,
		Limit:     1000,
	}
	return c.QueryEvents(query)
}

// GetOrganizationAnalytics retrieves analytics data for a specific organization
func (c *Client) GetOrganizationAnalytics(organizationID uuid.UUID, startTime, endTime time.Time) (*EventsResult, error) {
	query := EventQuery{
		OrganizationID: &organizationID,
		StartTime:      &startTime,
		EndTime:        &endTime,
		Limit:          1000,
	}
	return c.QueryEvents(query)
}

// GetServiceMetrics retrieves metrics for a specific service
func (c *Client) GetServiceMetrics(serviceName, environment string, startTime, endTime time.Time) (*MetricsResult, error) {
	query := MetricQuery{
		ServiceName: &serviceName,
		Environment: &environment,
		StartTime:   &startTime,
		EndTime:     &endTime,
		Limit:       1000,
	}
	return c.QueryMetrics(query)
}

// TrackPageView is a convenience method for tracking page views
func (c *Client) TrackPageView(userID *uuid.UUID, organizationID *uuid.UUID, serviceName, page, referrer string, properties map[string]interface{}) error {
	event := Event{
		EventType:      EventTypePageView,
		EventName:      "page_view",
		ServiceName:    serviceName,
		UserID:         userID,
		OrganizationID: organizationID,
		Referrer:       referrer,
		Properties: map[string]interface{}{
			"page": page,
		},
	}

	// Merge additional properties
	if properties != nil {
		for k, v := range properties {
			event.Properties[k] = v
		}
	}

	return c.TrackEvent(event)
}

// TrackAPICall is a convenience method for tracking API calls
func (c *Client) TrackAPICall(userID *uuid.UUID, organizationID *uuid.UUID, serviceName, method, endpoint string, statusCode int, durationMS int, success bool) error {
	event := Event{
		EventType:      EventTypeAPICall,
		EventName:      "api_call",
		ServiceName:    serviceName,
		UserID:         userID,
		OrganizationID: organizationID,
		StatusCode:     &statusCode,
		DurationMS:     &durationMS,
		Success:        &success,
		Properties: map[string]interface{}{
			"method":   method,
			"endpoint": endpoint,
		},
	}

	return c.TrackEvent(event)
}

// RecordResponseTime is a convenience method for recording response time metrics
func (c *Client) RecordResponseTime(serviceName, environment, endpoint string, durationMS float64, labels map[string]interface{}) error {
	metric := Metric{
		MetricName:  "response_time",
		MetricType:  MetricTypeTimer,
		Unit:        "ms",
		ServiceName: serviceName,
		Environment: environment,
		Value:       durationMS,
		Labels: map[string]interface{}{
			"endpoint": endpoint,
		},
	}

	// Merge additional labels
	if labels != nil {
		for k, v := range labels {
			metric.Labels[k] = v
		}
	}

	return c.RecordMetric(metric)
}

// IncrementCounter is a convenience method for incrementing counters
func (c *Client) IncrementCounter(serviceName, environment, counterName string, increment float64, labels map[string]interface{}) error {
	metric := Metric{
		MetricName:  counterName,
		MetricType:  MetricTypeCounter,
		ServiceName: serviceName,
		Environment: environment,
		Value:       increment,
		Labels:      labels,
	}

	return c.RecordMetric(metric)
}

// SetGauge is a convenience method for setting gauge values
func (c *Client) SetGauge(serviceName, environment, gaugeName, unit string, value float64, labels map[string]interface{}) error {
	metric := Metric{
		MetricName:  gaugeName,
		MetricType:  MetricTypeGauge,
		Unit:        unit,
		ServiceName: serviceName,
		Environment: environment,
		Value:       value,
		Labels:      labels,
	}

	return c.RecordMetric(metric)
}

// Health checks the health of the Analytics Service
func (c *Client) Health() error {
	return c.makeRequest("GET", "/health", nil, nil)
}

// makeRequest makes an HTTP request to the Analytics Service
func (c *Client) makeRequest(method, path string, body interface{}, result interface{}) error {
	url := c.config.BaseURL + path

	var requestBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		requestBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, url, requestBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.config.UserAgent)
	if c.config.APIKey != "" {
		req.Header.Set("X-API-Key", c.config.APIKey)
	}

	c.logger.Debug().
		Str("method", method).
		Str("url", url).
		Msg("Making request to Analytics Service")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error().
			Err(err).
			Str("method", method).
			Str("url", url).
			Msg("Request failed")
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode >= 400 {
		c.logger.Error().
			Int("status_code", resp.StatusCode).
			Str("method", method).
			Str("url", url).
			Str("response_body", string(responseBody)).
			Msg("Request returned error status")
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	if result != nil && len(responseBody) > 0 {
		if err := json.Unmarshal(responseBody, result); err != nil {
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	c.logger.Debug().
		Int("status_code", resp.StatusCode).
		Str("method", method).
		Str("url", url).
		Msg("Request completed successfully")

	return nil
}