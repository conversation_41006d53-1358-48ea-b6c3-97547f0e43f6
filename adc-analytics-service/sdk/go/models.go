package sdk

import (
	"time"

	"github.com/google/uuid"
)

// EventType represents the type of an analytics event
type EventType string

const (
	EventTypePageView    EventType = "page_view"
	EventTypeUserAction  EventType = "user_action"
	EventTypeCustom      EventType = "custom"
	EventTypeError       EventType = "error"
	EventTypePerformance EventType = "performance"
	EventTypeConversion  EventType = "conversion"
)

// MetricType represents the type of a metric
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

// AggregationType represents the type of aggregation for metrics
type AggregationType string

const (
	AggregationSum   AggregationType = "sum"
	AggregationAvg   AggregationType = "avg"
	AggregationMin   AggregationType = "min"
	AggregationMax   AggregationType = "max"
	AggregationCount AggregationType = "count"
	AggregationP50   AggregationType = "p50"
	AggregationP90   AggregationType = "p90"
	AggregationP95   AggregationType = "p95"
	AggregationP99   AggregationType = "p99"
)

// ReportType represents the type of a report
type ReportType string

const (
	ReportTypeAnalytics ReportType = "analytics"
	ReportTypeMetrics   ReportType = "metrics"
	ReportTypeFunnel    ReportType = "funnel"
	ReportTypeCohort    ReportType = "cohort"
	ReportTypeRetention ReportType = "retention"
	ReportTypeCustom    ReportType = "custom"
)

// ReportStatus represents the status of a report
type ReportStatus string

const (
	ReportStatusPending   ReportStatus = "pending"
	ReportStatusRunning   ReportStatus = "running"
	ReportStatusCompleted ReportStatus = "completed"
	ReportStatusFailed    ReportStatus = "failed"
)

// ReportFormat represents the format of a report
type ReportFormat string

const (
	ReportFormatJSON ReportFormat = "json"
	ReportFormatCSV  ReportFormat = "csv"
	ReportFormatPDF  ReportFormat = "pdf"
	ReportFormatXLSX ReportFormat = "xlsx"
)

// HealthResponse represents the health check response
type HealthResponse struct {
	Status      string                 `json:"status"`
	Version     string                 `json:"version"`
	Timestamp   time.Time              `json:"timestamp"`
	Services    map[string]string      `json:"services,omitempty"`
	Environment string                 `json:"environment,omitempty"`
	Details     map[string]interface{} `json:"details,omitempty"`
}

// EventRequest represents a request to track an event
type EventRequest struct {
	Type           EventType              `json:"type" validate:"required"`
	Name           string                 `json:"name" validate:"required,min=1,max=255"`
	Category       string                 `json:"category,omitempty" validate:"max=100"`
	Action         string                 `json:"action,omitempty" validate:"max=100"`
	Label          string                 `json:"label,omitempty" validate:"max=255"`
	Value          *float64               `json:"value,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	SessionID      string                 `json:"session_id,omitempty" validate:"max=255"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty" validate:"max=45"`
	UserAgent      string                 `json:"user_agent,omitempty" validate:"max=500"`
	Referrer       *string                `json:"referrer,omitempty" validate:"omitempty,max=500"`
	URL            *string                `json:"url,omitempty" validate:"omitempty,max=500"`
	Timestamp      time.Time              `json:"timestamp"`
}

// EventResponse represents an event response
type EventResponse struct {
	ID             string                 `json:"id"`
	Type           EventType              `json:"type"`
	Name           string                 `json:"name"`
	Category       string                 `json:"category,omitempty"`
	Action         string                 `json:"action,omitempty"`
	Label          string                 `json:"label,omitempty"`
	Value          *float64               `json:"value,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	SessionID      string                 `json:"session_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	IPAddress      string                 `json:"ip_address,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	Referrer       *string                `json:"referrer,omitempty"`
	URL            *string                `json:"url,omitempty"`
	Timestamp      time.Time              `json:"timestamp"`
	CreatedAt      time.Time              `json:"created_at"`
}

// BatchEventRequest represents a request to track multiple events
type BatchEventRequest struct {
	Events []EventRequest `json:"events" validate:"required,min=1,max=1000"`
}

// BulkEventResponse represents the response from bulk event operations
type BulkEventResponse struct {
	Success      int         `json:"success"`
	Failed       int         `json:"failed"`
	Errors       []string    `json:"errors,omitempty"`
	ProcessedIDs []uuid.UUID `json:"processed_ids,omitempty"`
	FailedItems  []int       `json:"failed_items,omitempty"`
}

// EventQuery represents query parameters for retrieving events
type EventQuery struct {
	Type           EventType   `json:"type,omitempty"`
	Name           string      `json:"name,omitempty"`
	Category       string      `json:"category,omitempty"`
	UserID         *uuid.UUID  `json:"user_id,omitempty"`
	SessionID      string      `json:"session_id,omitempty"`
	OrganizationID *uuid.UUID  `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID  `json:"shop_id,omitempty"`
	StartTime      time.Time   `json:"start_time,omitempty"`
	EndTime        time.Time   `json:"end_time,omitempty"`
	Limit          int         `json:"limit,omitempty"`
	Offset         int         `json:"offset,omitempty"`
}

// EventListResponse represents a list of events response
type EventListResponse struct {
	Events []EventResponse `json:"events"`
	Total  int             `json:"total"`
	Limit  int             `json:"limit"`
	Offset int             `json:"offset"`
}

// MetricRequest represents a request to record a metric
type MetricRequest struct {
	Name           string            `json:"name" validate:"required,min=1,max=255"`
	Type           MetricType        `json:"type" validate:"required"`
	Value          float64           `json:"value" validate:"required"`
	Unit           string            `json:"unit,omitempty" validate:"max=50"`
	Tags           map[string]string `json:"tags,omitempty"`
	UserID         *uuid.UUID        `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID        `json:"shop_id,omitempty"`
	Source         string            `json:"source,omitempty" validate:"max=100"`
	Timestamp      time.Time         `json:"timestamp"`
}

// MetricResponse represents a metric response
type MetricResponse struct {
	ID             string            `json:"id"`
	Name           string            `json:"name"`
	Type           MetricType        `json:"type"`
	Value          float64           `json:"value"`
	Unit           string            `json:"unit,omitempty"`
	Tags           map[string]string `json:"tags,omitempty"`
	UserID         *uuid.UUID        `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID        `json:"shop_id,omitempty"`
	Source         string            `json:"source,omitempty"`
	Timestamp      time.Time         `json:"timestamp"`
	CreatedAt      time.Time         `json:"created_at"`
}

// BatchMetricRequest represents a request to record multiple metrics
type BatchMetricRequest struct {
	Metrics []MetricRequest `json:"metrics" validate:"required,min=1,max=1000"`
}

// BulkMetricResponse represents the response from bulk metric operations
type BulkMetricResponse struct {
	Success      int         `json:"success"`
	Failed       int         `json:"failed"`
	Errors       []string    `json:"errors,omitempty"`
	ProcessedIDs []uuid.UUID `json:"processed_ids,omitempty"`
	FailedItems  []int       `json:"failed_items,omitempty"`
}

// MetricQuery represents query parameters for retrieving metrics
type MetricQuery struct {
	Name           string       `json:"name,omitempty"`
	Type           *MetricType  `json:"type,omitempty"`
	UserID         *uuid.UUID   `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID   `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID   `json:"shop_id,omitempty"`
	Source         string       `json:"source,omitempty"`
	StartTime      time.Time    `json:"start_time,omitempty"`
	EndTime        time.Time    `json:"end_time,omitempty"`
	Limit          int          `json:"limit,omitempty"`
	Offset         int          `json:"offset,omitempty"`
}

// MetricListResponse represents a list of metrics response
type MetricListResponse struct {
	Metrics []MetricResponse `json:"metrics"`
	Total   int              `json:"total"`
	Limit   int              `json:"limit"`
	Offset  int              `json:"offset"`
}

// TimeSeriesPoint represents a single point in a time series
type TimeSeriesPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// TimeSeriesResponse represents time series data response
type TimeSeriesResponse struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Unit        string            `json:"unit,omitempty"`
	Aggregation AggregationType   `json:"aggregation"`
	Tags        map[string]string `json:"tags,omitempty"`
	Points      []TimeSeriesPoint `json:"points"`
	StartTime   time.Time         `json:"start_time"`
	EndTime     time.Time         `json:"end_time"`
	Interval    string            `json:"interval"`
}

// ReportRequest represents a request to create a report
type ReportRequest struct {
	Name           string                 `json:"name" validate:"required,min=1,max=255"`
	Description    string                 `json:"description,omitempty" validate:"max=500"`
	Type           ReportType             `json:"type" validate:"required"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
	Format         ReportFormat           `json:"format,omitempty"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	ScheduledAt    *time.Time             `json:"scheduled_at,omitempty"`
	CreatedBy      *uuid.UUID             `json:"created_by,omitempty"`
}

// ReportResponse represents a report response
type ReportResponse struct {
	ID             string                 `json:"id"`
	Name           string                 `json:"name"`
	Description    string                 `json:"description,omitempty"`
	Type           ReportType             `json:"type"`
	Status         ReportStatus           `json:"status"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
	Data           map[string]interface{} `json:"data,omitempty"`
	ErrorMessage   string                 `json:"error_message,omitempty"`
	Format         ReportFormat           `json:"format"`
	UserID         *uuid.UUID             `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID             `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID             `json:"shop_id,omitempty"`
	ScheduledAt    *time.Time             `json:"scheduled_at,omitempty"`
	StartedAt      *time.Time             `json:"started_at,omitempty"`
	CompletedAt    *time.Time             `json:"completed_at,omitempty"`
	CreatedBy      *uuid.UUID             `json:"created_by,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// ReportQuery represents query parameters for retrieving reports
type ReportQuery struct {
	Type           *ReportType    `json:"type,omitempty"`
	Status         *ReportStatus  `json:"status,omitempty"`
	UserID         *uuid.UUID     `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID     `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID     `json:"shop_id,omitempty"`
	CreatedBy      *uuid.UUID     `json:"created_by,omitempty"`
	Limit          int            `json:"limit,omitempty"`
	Offset         int            `json:"offset,omitempty"`
}

// ReportListResponse represents a list of reports response
type ReportListResponse struct {
	Reports []ReportResponse `json:"reports"`
	Total   int              `json:"total"`
	Limit   int              `json:"limit"`
	Offset  int              `json:"offset"`
}

// Widget represents a dashboard widget
type Widget struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Title         string                 `json:"title"`
	Configuration map[string]interface{} `json:"configuration,omitempty"`
	Data          map[string]interface{} `json:"data,omitempty"`
	Position      map[string]int         `json:"position,omitempty"`
}

// DashboardResponse represents a dashboard response
type DashboardResponse struct {
	ID             uuid.UUID   `json:"id"`
	Name           string      `json:"name"`
	Description    string      `json:"description,omitempty"`
	Widgets        []Widget    `json:"widgets"`
	UserID         *uuid.UUID  `json:"user_id,omitempty"`
	OrganizationID *uuid.UUID  `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID  `json:"shop_id,omitempty"`
	IsPublic       bool        `json:"is_public"`
	CreatedBy      *uuid.UUID  `json:"created_by,omitempty"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
}

// FunnelAnalysisRequest represents a request for funnel analysis
type FunnelAnalysisRequest struct {
	Steps     []string               `json:"steps" validate:"required,min=2"`
	StartTime time.Time              `json:"start_time" validate:"required"`
	EndTime   time.Time              `json:"end_time" validate:"required"`
	Filters   map[string]interface{} `json:"filters,omitempty"`
}

// FunnelStep represents a step in a funnel analysis
type FunnelStep struct {
	Step         string  `json:"step"`
	Users        int     `json:"users"`
	Conversions  int     `json:"conversions"`
	DropoffRate  float64 `json:"dropoff_rate"`
	ConversionRate float64 `json:"conversion_rate"`
}

// FunnelAnalysisResponse represents the response from funnel analysis
type FunnelAnalysisResponse struct {
	Steps         []FunnelStep           `json:"steps"`
	TotalUsers    int                    `json:"total_users"`
	Conversions   int                    `json:"conversions"`
	ConversionRate float64               `json:"conversion_rate"`
	AverageTime   time.Duration          `json:"average_time"`
	Filters       map[string]interface{} `json:"filters,omitempty"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
}

// CohortData represents cohort analysis data
type CohortData struct {
	Cohort     string            `json:"cohort"`
	Size       int               `json:"size"`
	Periods    map[string]int    `json:"periods"`
	Retention  map[string]float64 `json:"retention"`
}

// CohortAnalysisResponse represents the response from cohort analysis
type CohortAnalysisResponse struct {
	Cohorts        []CohortData `json:"cohorts"`
	CohortPeriod   string       `json:"cohort_period"`
	AnalysisPeriod string       `json:"analysis_period"`
	StartTime      time.Time    `json:"start_time"`
	EndTime        time.Time    `json:"end_time"`
}

// RetentionData represents retention analysis data
type RetentionData struct {
	Period         string  `json:"period"`
	NewUsers       int     `json:"new_users"`
	ReturnedUsers  int     `json:"returned_users"`
	RetentionRate  float64 `json:"retention_rate"`
}

// RetentionAnalysisResponse represents the response from retention analysis
type RetentionAnalysisResponse struct {
	RetentionData []RetentionData        `json:"retention_data"`
	Period        string                 `json:"period"`
	AverageRetention float64             `json:"average_retention"`
	Filters       map[string]interface{} `json:"filters,omitempty"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
}

// Insight represents an analytics insight
type Insight struct {
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Value       interface{}            `json:"value"`
	Change      *float64               `json:"change,omitempty"`
	Trend       string                 `json:"trend,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// InsightsResponse represents the response from insights analysis
type InsightsResponse struct {
	Insights  []Insight              `json:"insights"`
	Summary   map[string]interface{} `json:"summary,omitempty"`
	Filters   map[string]interface{} `json:"filters,omitempty"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
}

// ErrorResponse represents an error response from the API
type ErrorResponse struct {
	Error     string                 `json:"error"`
	Message   string                 `json:"message"`
	Code      string                 `json:"code,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// AggregatedMetric represents an aggregated metric value
type AggregatedMetric struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Aggregation AggregationType   `json:"aggregation"`
	Value       float64           `json:"value"`
	Unit        string            `json:"unit,omitempty"`
	Tags        map[string]string `json:"tags,omitempty"`
	Count       int64             `json:"count"`
	Timestamp   time.Time         `json:"timestamp"`
}

// BulkOperationResult represents the result of a bulk operation
type BulkOperationResult struct {
	Success      int         `json:"success"`
	Failed       int         `json:"failed"`
	Errors       []string    `json:"errors,omitempty"`
	ProcessedIDs []uuid.UUID `json:"processed_ids,omitempty"`
	FailedItems  []int       `json:"failed_items,omitempty"`
}

// AnalyticsContext represents the context for analytics operations
type AnalyticsContext struct {
	UserID         *uuid.UUID `json:"user_id,omitempty"`
	SessionID      string     `json:"session_id,omitempty"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	ShopID         *uuid.UUID `json:"shop_id,omitempty"`
	IPAddress      string     `json:"ip_address,omitempty"`
	UserAgent      string     `json:"user_agent,omitempty"`
}

// EventFilter represents filters for event queries
type EventFilter struct {
	Types          []EventType `json:"types,omitempty"`
	Categories     []string    `json:"categories,omitempty"`
	UserIDs        []uuid.UUID `json:"user_ids,omitempty"`
	OrganizationIDs []uuid.UUID `json:"organization_ids,omitempty"`
	ShopIDs        []uuid.UUID `json:"shop_ids,omitempty"`
	Properties     map[string]interface{} `json:"properties,omitempty"`
}

// MetricFilter represents filters for metric queries
type MetricFilter struct {
	Types          []MetricType `json:"types,omitempty"`
	Names          []string     `json:"names,omitempty"`
	Sources        []string     `json:"sources,omitempty"`
	UserIDs        []uuid.UUID  `json:"user_ids,omitempty"`
	OrganizationIDs []uuid.UUID `json:"organization_ids,omitempty"`
	ShopIDs        []uuid.UUID  `json:"shop_ids,omitempty"`
	Tags           map[string]string `json:"tags,omitempty"`
}