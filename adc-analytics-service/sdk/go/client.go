package sdk

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Client represents the ADC Analytics Service SDK client
type Client struct {
	baseURL    string
	httpClient *http.Client
	apiKey     string
	logger     zerolog.Logger
}

// Config represents the client configuration
type Config struct {
	BaseURL    string
	APIKey     string
	Timeout    time.Duration
	Logger     *zerolog.Logger
}

// NewClient creates a new ADC Analytics Service client
func NewClient(baseURL, apiKey string) *Client {
	return &Client{
		baseURL: baseURL,
		apiKey:  apiKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: log.With().Str("component", "adc-analytics-sdk").Logger(),
	}
}

// NewClientWithConfig creates a new ADC Analytics Service client with configuration
func NewClientWithConfig(config Config) *Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	var logger zerolog.Logger
	if config.Logger != nil {
		logger = *config.Logger
	} else {
		logger = log.With().Str("component", "adc-analytics-sdk").Logger()
	}

	return &Client{
		baseURL: config.BaseURL,
		apiKey:  config.APIKey,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		logger: logger,
	}
}

// Health checks the health of the analytics service
func (c *Client) Health(ctx context.Context) (*HealthResponse, error) {
	req, err := c.newRequest(ctx, "GET", "/health", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create health request: %w", err)
	}

	var response HealthResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}

	return &response, nil
}

// TrackEvent tracks a single analytics event
func (c *Client) TrackEvent(ctx context.Context, event EventRequest) (*EventResponse, error) {
	req, err := c.newRequest(ctx, "POST", "/api/v1/events", event)
	if err != nil {
		return nil, fmt.Errorf("failed to create track event request: %w", err)
	}

	var response EventResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to track event: %w", err)
	}

	c.logger.Debug().
		Str("event_id", response.ID).
		Str("event_type", string(event.Type)).
		Str("event_name", event.Name).
		Msg("Event tracked successfully")

	return &response, nil
}

// TrackEvents tracks multiple events in batch
func (c *Client) TrackEvents(ctx context.Context, events []EventRequest) (*BulkEventResponse, error) {
	if len(events) == 0 {
		return &BulkEventResponse{Success: 0, Failed: 0}, nil
	}

	if len(events) > 1000 {
		return nil, fmt.Errorf("batch size too large (max 1000 events)")
	}

	request := BatchEventRequest{Events: events}
	req, err := c.newRequest(ctx, "POST", "/api/v1/events/batch", request)
	if err != nil {
		return nil, fmt.Errorf("failed to create batch events request: %w", err)
	}

	var response BulkEventResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to track events batch: %w", err)
	}

	c.logger.Info().
		Int("success_count", response.Success).
		Int("failed_count", response.Failed).
		Msg("Batch events tracked")

	return &response, nil
}

// GetEvents retrieves events based on query parameters
func (c *Client) GetEvents(ctx context.Context, query EventQuery) (*EventListResponse, error) {
	endpoint := "/api/v1/events"
	params := c.buildEventQueryParams(query)

	if len(params) > 0 {
		endpoint += "?" + params.Encode()
	}

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create get events request: %w", err)
	}

	var response EventListResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get events: %w", err)
	}

	c.logger.Debug().
		Int("event_count", len(response.Events)).
		Interface("query", query).
		Msg("Events retrieved successfully")

	return &response, nil
}

// RecordMetric records a single metric
func (c *Client) RecordMetric(ctx context.Context, metric MetricRequest) error {
	req, err := c.newRequest(ctx, "POST", "/api/v1/metrics", metric)
	if err != nil {
		return fmt.Errorf("failed to create record metric request: %w", err)
	}

	if err := c.doRequest(req, nil); err != nil {
		return fmt.Errorf("failed to record metric: %w", err)
	}

	c.logger.Debug().
		Str("metric_name", metric.Name).
		Float64("metric_value", metric.Value).
		Str("metric_type", string(metric.Type)).
		Msg("Metric recorded successfully")

	return nil
}

// RecordMetrics records multiple metrics in batch
func (c *Client) RecordMetrics(ctx context.Context, metrics []MetricRequest) (*BulkMetricResponse, error) {
	if len(metrics) == 0 {
		return &BulkMetricResponse{Success: 0, Failed: 0}, nil
	}

	if len(metrics) > 1000 {
		return nil, fmt.Errorf("batch size too large (max 1000 metrics)")
	}

	request := BatchMetricRequest{Metrics: metrics}
	req, err := c.newRequest(ctx, "POST", "/api/v1/metrics/batch", request)
	if err != nil {
		return nil, fmt.Errorf("failed to create batch metrics request: %w", err)
	}

	var response BulkMetricResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to record metrics batch: %w", err)
	}

	c.logger.Info().
		Int("success_count", response.Success).
		Int("failed_count", response.Failed).
		Msg("Batch metrics recorded")

	return &response, nil
}

// GetMetrics retrieves metrics based on query parameters
func (c *Client) GetMetrics(ctx context.Context, query MetricQuery) (*MetricListResponse, error) {
	endpoint := "/api/v1/metrics"
	params := c.buildMetricQueryParams(query)

	if len(params) > 0 {
		endpoint += "?" + params.Encode()
	}

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create get metrics request: %w", err)
	}

	var response MetricListResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get metrics: %w", err)
	}

	c.logger.Debug().
		Int("metric_count", len(response.Metrics)).
		Interface("query", query).
		Msg("Metrics retrieved successfully")

	return &response, nil
}

// GetTimeSeries retrieves time series data for metrics
func (c *Client) GetTimeSeries(ctx context.Context, name string, startTime, endTime time.Time, interval string, aggregation AggregationType) (*TimeSeriesResponse, error) {
	endpoint := fmt.Sprintf("/api/v1/metrics/%s/timeseries", name)
	params := url.Values{}
	params.Set("start_time", startTime.Format(time.RFC3339))
	params.Set("end_time", endTime.Format(time.RFC3339))
	params.Set("interval", interval)
	params.Set("aggregation", string(aggregation))

	endpoint += "?" + params.Encode()

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create time series request: %w", err)
	}

	var response TimeSeriesResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get time series: %w", err)
	}

	c.logger.Debug().
		Str("metric_name", name).
		Int("data_points", len(response.Points)).
		Str("aggregation", string(aggregation)).
		Msg("Time series retrieved successfully")

	return &response, nil
}

// CreateReport creates a new analytics report
func (c *Client) CreateReport(ctx context.Context, report ReportRequest) (*ReportResponse, error) {
	req, err := c.newRequest(ctx, "POST", "/api/v1/reports", report)
	if err != nil {
		return nil, fmt.Errorf("failed to create report request: %w", err)
	}

	var response ReportResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to create report: %w", err)
	}

	c.logger.Info().
		Str("report_id", response.ID).
		Str("report_name", report.Name).
		Str("report_type", string(report.Type)).
		Msg("Report created successfully")

	return &response, nil
}

// GetReport retrieves a report by ID
func (c *Client) GetReport(ctx context.Context, id string) (*ReportResponse, error) {
	endpoint := fmt.Sprintf("/api/v1/reports/%s", id)
	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create get report request: %w", err)
	}

	var response ReportResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get report: %w", err)
	}

	return &response, nil
}

// GetReports retrieves multiple reports based on query parameters
func (c *Client) GetReports(ctx context.Context, query ReportQuery) (*ReportListResponse, error) {
	endpoint := "/api/v1/reports"
	params := c.buildReportQueryParams(query)

	if len(params) > 0 {
		endpoint += "?" + params.Encode()
	}

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create get reports request: %w", err)
	}

	var response ReportListResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get reports: %w", err)
	}

	c.logger.Debug().
		Int("report_count", len(response.Reports)).
		Interface("query", query).
		Msg("Reports retrieved successfully")

	return &response, nil
}

// GenerateReport generates a report based on its configuration
func (c *Client) GenerateReport(ctx context.Context, id string) error {
	endpoint := fmt.Sprintf("/api/v1/reports/%s/generate", id)
	req, err := c.newRequest(ctx, "POST", endpoint, nil)
	if err != nil {
		return fmt.Errorf("failed to create generate report request: %w", err)
	}

	if err := c.doRequest(req, nil); err != nil {
		return fmt.Errorf("failed to generate report: %w", err)
	}

	c.logger.Info().
		Str("report_id", id).
		Msg("Report generation triggered")

	return nil
}

// GetDashboard retrieves dashboard data
func (c *Client) GetDashboard(ctx context.Context, id string, timeRange map[string]time.Time) (*DashboardResponse, error) {
	endpoint := fmt.Sprintf("/api/v1/dashboards/%s", id)
	params := url.Values{}

	if startTime, ok := timeRange["start"]; ok {
		params.Set("start_time", startTime.Format(time.RFC3339))
	}
	if endTime, ok := timeRange["end"]; ok {
		params.Set("end_time", endTime.Format(time.RFC3339))
	}

	if len(params) > 0 {
		endpoint += "?" + params.Encode()
	}

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create get dashboard request: %w", err)
	}

	var response DashboardResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get dashboard: %w", err)
	}

	c.logger.Debug().
		Str("dashboard_id", id).
		Int("widget_count", len(response.Widgets)).
		Msg("Dashboard retrieved successfully")

	return &response, nil
}

// GetFunnelAnalysis retrieves funnel analysis data
func (c *Client) GetFunnelAnalysis(ctx context.Context, steps []string, startTime, endTime time.Time, filters map[string]interface{}) (*FunnelAnalysisResponse, error) {
	request := FunnelAnalysisRequest{
		Steps:     steps,
		StartTime: startTime,
		EndTime:   endTime,
		Filters:   filters,
	}

	req, err := c.newRequest(ctx, "POST", "/api/v1/analytics/funnel", request)
	if err != nil {
		return nil, fmt.Errorf("failed to create funnel analysis request: %w", err)
	}

	var response FunnelAnalysisResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get funnel analysis: %w", err)
	}

	c.logger.Debug().
		Int("step_count", len(steps)).
		Msg("Funnel analysis retrieved successfully")

	return &response, nil
}

// GetCohortAnalysis retrieves cohort analysis data
func (c *Client) GetCohortAnalysis(ctx context.Context, cohortPeriod, analysisPeriod string, startTime, endTime time.Time) (*CohortAnalysisResponse, error) {
	endpoint := "/api/v1/analytics/cohort"
	params := url.Values{}
	params.Set("cohort_period", cohortPeriod)
	params.Set("analysis_period", analysisPeriod)
	params.Set("start_time", startTime.Format(time.RFC3339))
	params.Set("end_time", endTime.Format(time.RFC3339))

	endpoint += "?" + params.Encode()

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create cohort analysis request: %w", err)
	}

	var response CohortAnalysisResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get cohort analysis: %w", err)
	}

	c.logger.Debug().
		Str("cohort_period", cohortPeriod).
		Str("analysis_period", analysisPeriod).
		Msg("Cohort analysis retrieved successfully")

	return &response, nil
}

// GetRetentionAnalysis retrieves retention analysis data
func (c *Client) GetRetentionAnalysis(ctx context.Context, period string, startTime, endTime time.Time, filters map[string]interface{}) (*RetentionAnalysisResponse, error) {
	endpoint := "/api/v1/analytics/retention"
	params := url.Values{}
	params.Set("period", period)
	params.Set("start_time", startTime.Format(time.RFC3339))
	params.Set("end_time", endTime.Format(time.RFC3339))

	// Add filters as query parameters
	for key, value := range filters {
		params.Set(key, fmt.Sprintf("%v", value))
	}

	endpoint += "?" + params.Encode()

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create retention analysis request: %w", err)
	}

	var response RetentionAnalysisResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get retention analysis: %w", err)
	}

	c.logger.Debug().
		Str("period", period).
		Msg("Retention analysis retrieved successfully")

	return &response, nil
}

// GetInsights retrieves analytics insights
func (c *Client) GetInsights(ctx context.Context, filters map[string]interface{}, startTime, endTime time.Time) (*InsightsResponse, error) {
	endpoint := "/api/v1/analytics/insights"
	params := url.Values{}
	params.Set("start_time", startTime.Format(time.RFC3339))
	params.Set("end_time", endTime.Format(time.RFC3339))

	// Add filters as query parameters
	for key, value := range filters {
		params.Set(key, fmt.Sprintf("%v", value))
	}

	endpoint += "?" + params.Encode()

	req, err := c.newRequest(ctx, "GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create insights request: %w", err)
	}

	var response InsightsResponse
	if err := c.doRequest(req, &response); err != nil {
		return nil, fmt.Errorf("failed to get insights: %w", err)
	}

	c.logger.Debug().
		Int("insight_count", len(response.Insights)).
		Msg("Insights retrieved successfully")

	return &response, nil
}

// Helper methods

func (c *Client) newRequest(ctx context.Context, method, endpoint string, body interface{}) (*http.Request, error) {
	url := c.baseURL + endpoint

	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "adc-analytics-sdk/1.0.0")

	// Set API key if provided
	if c.apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+c.apiKey)
	}

	return req, nil
}

func (c *Client) doRequest(req *http.Request, result interface{}) error {
	c.logger.Debug().
		Str("method", req.Method).
		Str("url", req.URL.String()).
		Msg("Making HTTP request to analytics service")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error().
			Err(err).
			Str("url", req.URL.String()).
			Msg("HTTP request failed")
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode >= 400 {
		c.logger.Error().
			Int("status_code", resp.StatusCode).
			Str("response_body", string(body)).
			Msg("HTTP request returned error status")

		var errorResp ErrorResponse
		if json.Unmarshal(body, &errorResp) == nil {
			return fmt.Errorf("API error (%d): %s", resp.StatusCode, errorResp.Message)
		}
		return fmt.Errorf("HTTP error (%d): %s", resp.StatusCode, string(body))
	}

	if result != nil {
		if err := json.Unmarshal(body, result); err != nil {
			c.logger.Error().Err(err).Msg("Failed to unmarshal response")
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	c.logger.Debug().
		Int("status_code", resp.StatusCode).
		Msg("HTTP request successful")

	return nil
}

// Query parameter builders

func (c *Client) buildEventQueryParams(query EventQuery) url.Values {
	params := url.Values{}

	if query.Type != "" {
		params.Set("type", string(query.Type))
	}
	if query.Name != "" {
		params.Set("name", query.Name)
	}
	if query.Category != "" {
		params.Set("category", query.Category)
	}
	if query.UserID != nil {
		params.Set("user_id", query.UserID.String())
	}
	if query.OrganizationID != nil {
		params.Set("organization_id", query.OrganizationID.String())
	}
	if query.ShopID != nil {
		params.Set("shop_id", query.ShopID.String())
	}
	if query.SessionID != "" {
		params.Set("session_id", query.SessionID)
	}
	if !query.StartTime.IsZero() {
		params.Set("start_time", query.StartTime.Format(time.RFC3339))
	}
	if !query.EndTime.IsZero() {
		params.Set("end_time", query.EndTime.Format(time.RFC3339))
	}
	if query.Limit > 0 {
		params.Set("limit", strconv.Itoa(query.Limit))
	}
	if query.Offset > 0 {
		params.Set("offset", strconv.Itoa(query.Offset))
	}

	return params
}

func (c *Client) buildMetricQueryParams(query MetricQuery) url.Values {
	params := url.Values{}

	if query.Name != "" {
		params.Set("name", query.Name)
	}
	if query.Type != nil {
		params.Set("type", string(*query.Type))
	}
	if query.UserID != nil {
		params.Set("user_id", query.UserID.String())
	}
	if query.OrganizationID != nil {
		params.Set("organization_id", query.OrganizationID.String())
	}
	if query.ShopID != nil {
		params.Set("shop_id", query.ShopID.String())
	}
	if query.Source != "" {
		params.Set("source", query.Source)
	}
	if !query.StartTime.IsZero() {
		params.Set("start_time", query.StartTime.Format(time.RFC3339))
	}
	if !query.EndTime.IsZero() {
		params.Set("end_time", query.EndTime.Format(time.RFC3339))
	}
	if query.Limit > 0 {
		params.Set("limit", strconv.Itoa(query.Limit))
	}
	if query.Offset > 0 {
		params.Set("offset", strconv.Itoa(query.Offset))
	}

	return params
}

func (c *Client) buildReportQueryParams(query ReportQuery) url.Values {
	params := url.Values{}

	if query.Type != nil {
		params.Set("type", string(*query.Type))
	}
	if query.Status != nil {
		params.Set("status", string(*query.Status))
	}
	if query.UserID != nil {
		params.Set("user_id", query.UserID.String())
	}
	if query.OrganizationID != nil {
		params.Set("organization_id", query.OrganizationID.String())
	}
	if query.CreatedBy != nil {
		params.Set("created_by", query.CreatedBy.String())
	}
	if query.Limit > 0 {
		params.Set("limit", strconv.Itoa(query.Limit))
	}
	if query.Offset > 0 {
		params.Set("offset", strconv.Itoa(query.Offset))
	}

	return params
}

// AnalyticsManager provides a high-level interface for analytics operations
type AnalyticsManager struct {
	client *Client
	logger zerolog.Logger
}

// NewAnalyticsManager creates a new analytics manager
func NewAnalyticsManager(client *Client) *AnalyticsManager {
	return &AnalyticsManager{
		client: client,
		logger: client.logger.With().Str("component", "analytics-manager").Logger(),
	}
}

// TrackPageView tracks a page view event
func (m *AnalyticsManager) TrackPageView(ctx context.Context, userID *uuid.UUID, sessionID, url, referrer string) error {
	event := EventRequest{
		Type:      EventTypePageView,
		Name:      "page_view",
		Category:  "navigation",
		UserID:    userID,
		SessionID: sessionID,
		URL:       &url,
		Referrer:  &referrer,
		Timestamp: time.Now(),
	}

	_, err := m.client.TrackEvent(ctx, event)
	return err
}

// TrackUserAction tracks a user action event
func (m *AnalyticsManager) TrackUserAction(ctx context.Context, userID *uuid.UUID, action, category string, properties map[string]interface{}) error {
	event := EventRequest{
		Type:       EventTypeUserAction,
		Name:       action,
		Category:   category,
		UserID:     userID,
		Properties: properties,
		Timestamp:  time.Now(),
	}

	_, err := m.client.TrackEvent(ctx, event)
	return err
}

// TrackCustomEvent tracks a custom event
func (m *AnalyticsManager) TrackCustomEvent(ctx context.Context, name, category string, userID *uuid.UUID, properties map[string]interface{}) error {
	event := EventRequest{
		Type:       EventTypeCustom,
		Name:       name,
		Category:   category,
		UserID:     userID,
		Properties: properties,
		Timestamp:  time.Now(),
	}

	_, err := m.client.TrackEvent(ctx, event)
	return err
}

// RecordCounter records a counter metric
func (m *AnalyticsManager) RecordCounter(ctx context.Context, name string, value float64, tags map[string]string) error {
	metric := MetricRequest{
		Name:      name,
		Type:      MetricTypeCounter,
		Value:     value,
		Tags:      tags,
		Timestamp: time.Now(),
	}

	return m.client.RecordMetric(ctx, metric)
}

// RecordGauge records a gauge metric
func (m *AnalyticsManager) RecordGauge(ctx context.Context, name string, value float64, tags map[string]string) error {
	metric := MetricRequest{
		Name:      name,
		Type:      MetricTypeGauge,
		Value:     value,
		Tags:      tags,
		Timestamp: time.Now(),
	}

	return m.client.RecordMetric(ctx, metric)
}

// RecordHistogram records a histogram metric
func (m *AnalyticsManager) RecordHistogram(ctx context.Context, name string, value float64, tags map[string]string) error {
	metric := MetricRequest{
		Name:      name,
		Type:      MetricTypeHistogram,
		Value:     value,
		Tags:      tags,
		Timestamp: time.Now(),
	}

	return m.client.RecordMetric(ctx, metric)
}

// GetUserEvents retrieves events for a specific user
func (m *AnalyticsManager) GetUserEvents(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time, limit int) (*EventListResponse, error) {
	query := EventQuery{
		UserID:    &userID,
		StartTime: startTime,
		EndTime:   endTime,
		Limit:     limit,
	}

	return m.client.GetEvents(ctx, query)
}

// GetOrganizationEvents retrieves events for a specific organization
func (m *AnalyticsManager) GetOrganizationEvents(ctx context.Context, orgID uuid.UUID, startTime, endTime time.Time, limit int) (*EventListResponse, error) {
	query := EventQuery{
		OrganizationID: &orgID,
		StartTime:      startTime,
		EndTime:        endTime,
		Limit:          limit,
	}

	return m.client.GetEvents(ctx, query)
}