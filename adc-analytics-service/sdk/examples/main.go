package main

import (
	"fmt"
	"os"
	"time"

	"github.com/adc-analytics-service/sdk"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

func main() {
	// Initialize logger
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()

	// Create client configuration
	config := sdk.ClientConfig{
		BaseURL:   "http://localhost:9400", // Analytics Service URL
		APIKey:    "your-api-key-here",
		Timeout:   30 * time.Second,
		UserAgent: "example-app/1.0.0",
		Logger:    logger,
	}

	// Create client
	client := sdk.NewClient(config)

	// Test health check
	fmt.Println("Testing health check...")
	if err := client.Health(); err != nil {
		logger.Error().Err(err).Msg("Health check failed")
		return
	}
	fmt.Println("Health check passed!")

	// Example 1: Track a single event
	fmt.Println("\nExample 1: Tracking a single event...")
	userID := uuid.New()
	orgID := uuid.New()

	event := sdk.Event{
		EventType:      sdk.EventTypePageView,
		EventName:      "dashboard_view",
		EventCategory:  "navigation",
		ServiceName:    "frontend-app",
		Environment:    "production",
		UserID:         &userID,
		OrganizationID: &orgID,
		SessionID:      "session-123",
		RequestID:      "req-456",
		IPAddress:      "*************",
		UserAgent:      "Mozilla/5.0 (Chrome/91.0)",
		Referrer:       "https://example.com/login",
		Properties: map[string]interface{}{
			"page":     "/dashboard",
			"section":  "overview",
			"viewport": "1920x1080",
		},
		Metadata: map[string]interface{}{
			"load_time_ms": 250,
			"ab_test":      "variant_a",
		},
		Tags: []string{"dashboard", "analytics", "page_view"},
	}

	if err := client.TrackEvent(event); err != nil {
		logger.Error().Err(err).Msg("Failed to track event")
		return
	}
	fmt.Println("Event tracked successfully!")

	// Example 2: Track multiple events in batch
	fmt.Println("\nExample 2: Tracking batch events...")
	events := []sdk.Event{
		{
			EventType:     sdk.EventTypeClick,
			EventName:     "button_click",
			EventCategory: "interaction",
			ServiceName:   "frontend-app",
			Environment:   "production",
			UserID:        &userID,
			OrganizationID: &orgID,
			Properties: map[string]interface{}{
				"button_id":   "create_project",
				"button_text": "Create New Project",
				"position":    "header",
			},
			Tags: []string{"button", "click", "create"},
		},
		{
			EventType:     sdk.EventTypeFormSubmit,
			EventName:     "project_created",
			EventCategory: "form",
			ServiceName:   "backend-api",
			Environment:   "production",
			UserID:        &userID,
			OrganizationID: &orgID,
			DurationMS:    intPtr(1200),
			Success:       boolPtr(true),
			Properties: map[string]interface{}{
				"project_name": "New Analytics Project",
				"project_type": "dashboard",
				"team_size":    5,
			},
			Tags: []string{"project", "create", "form"},
		},
		{
			EventType:     sdk.EventTypeAPICall,
			EventName:     "api_request",
			EventCategory: "api",
			ServiceName:   "analytics-api",
			Environment:   "production",
			UserID:        &userID,
			OrganizationID: &orgID,
			DurationMS:    intPtr(45),
			StatusCode:    intPtr(200),
			Success:       boolPtr(true),
			Properties: map[string]interface{}{
				"endpoint": "/api/v1/projects",
				"method":   "POST",
				"size":     1024,
			},
			Tags: []string{"api", "project", "create"},
		},
	}

	if err := client.TrackEvents(events); err != nil {
		logger.Error().Err(err).Msg("Failed to track batch events")
		return
	}
	fmt.Println("Batch events tracked successfully!")

	// Example 3: Record metrics
	fmt.Println("\nExample 3: Recording metrics...")
	metrics := []sdk.Metric{
		{
			MetricName:  "response_time",
			MetricType:  sdk.MetricTypeTimer,
			Unit:        "ms",
			ServiceName: "api-service",
			Environment: "production",
			Value:       45.5,
			Labels: map[string]interface{}{
				"endpoint": "/api/v1/users",
				"method":   "GET",
				"status":   "200",
			},
			Tags: []string{"performance", "api"},
		},
		{
			MetricName:  "active_users",
			MetricType:  sdk.MetricTypeGauge,
			Unit:        "count",
			ServiceName: "session-service",
			Environment: "production",
			Value:       1250,
			Labels: map[string]interface{}{
				"region": "us-east-1",
			},
			Tags: []string{"users", "sessions"},
		},
		{
			MetricName:  "requests_total",
			MetricType:  sdk.MetricTypeCounter,
			Unit:        "count",
			ServiceName: "api-gateway",
			Environment: "production",
			Value:       1,
			Labels: map[string]interface{}{
				"service": "user-api",
				"version": "v1",
			},
			Tags: []string{"requests", "api"},
		},
	}

	if err := client.RecordMetrics(metrics); err != nil {
		logger.Error().Err(err).Msg("Failed to record metrics")
		return
	}
	fmt.Println("Metrics recorded successfully!")

	// Example 4: Query events
	fmt.Println("\nExample 4: Querying events...")
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()
	serviceName := "frontend-app"

	eventQuery := sdk.EventQuery{
		ServiceName: &serviceName,
		EventTypes:  []sdk.EventType{sdk.EventTypePageView, sdk.EventTypeClick},
		StartTime:   &startTime,
		EndTime:     &endTime,
		Limit:       10,
	}

	eventResults, err := client.QueryEvents(eventQuery)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to query events")
		return
	}

	fmt.Printf("Found %d events (total: %d)\n", len(eventResults.Events), eventResults.Total)
	for i, e := range eventResults.Events {
		fmt.Printf("  %d. [%s] %s: %s\n", i+1, e.EventType, e.ServiceName, e.EventName)
	}

	// Example 5: Query metrics
	fmt.Println("\nExample 5: Querying metrics...")
	metricServiceName := "api-service"
	metricQuery := sdk.MetricQuery{
		ServiceName: &metricServiceName,
		MetricNames: []string{"response_time", "requests_total"},
		StartTime:   &startTime,
		EndTime:     &endTime,
		Limit:       20,
	}

	metricResults, err := client.QueryMetrics(metricQuery)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to query metrics")
		return
	}

	fmt.Printf("Found %d metrics (total: %d)\n", len(metricResults.Metrics), metricResults.Total)
	for i, m := range metricResults.Metrics {
		fmt.Printf("  %d. %s (%s): %v %s\n", i+1, m.MetricName, m.MetricType, m.Value, m.Unit)
	}

	// Example 6: Get aggregations
	fmt.Println("\nExample 6: Getting aggregations...")
	timeWindow := "hour"
	aggQuery := sdk.AggregationQuery{
		ServiceName: &serviceName,
		TimeWindow:  &timeWindow,
		StartTime:   &startTime,
		EndTime:     &endTime,
		Limit:       24, // Last 24 hours
	}

	aggregations, err := client.GetAggregations(aggQuery)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get aggregations")
		return
	}

	fmt.Printf("Found %d aggregation buckets\n", len(aggregations))
	for i, agg := range aggregations {
		fmt.Printf("  %d. %s (%s): Value=%v, Count=%d\n", 
			i+1, agg.TimeBucket.Format("2006-01-02 15:04:05"), 
			agg.TimeWindow, agg.Value, agg.Count)
	}

	// Example 7: Create a dashboard
	fmt.Println("\nExample 7: Creating a dashboard...")
	dashboard := sdk.Dashboard{
		Name:           "User Activity Dashboard",
		Slug:           "user-activity",
		Description:    "Monitor user engagement and activity metrics",
		OrganizationID: &orgID,
		Config: map[string]interface{}{
			"widgets": []map[string]interface{}{
				{
					"type":     "chart",
					"title":    "Page Views",
					"chart_type": "line",
					"data_source": map[string]interface{}{
						"event_type": "page_view",
						"time_range": "24h",
					},
				},
				{
					"type":     "metric",
					"title":    "Active Users",
					"data_source": map[string]interface{}{
						"metric_name": "active_users",
						"aggregation": "avg",
					},
				},
			},
		},
		Layout: map[string]interface{}{
			"grid": []map[string]interface{}{
				{"x": 0, "y": 0, "w": 6, "h": 4, "widget_id": 0},
				{"x": 6, "y": 0, "w": 6, "h": 4, "widget_id": 1},
			},
		},
		Category: "user_analytics",
		Tags:     []string{"users", "activity", "engagement"},
		IsPublic: false,
	}

	createdDashboard, err := client.CreateDashboard(dashboard)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to create dashboard")
		return
	}
	fmt.Printf("Created dashboard: %s (ID: %s)\n", createdDashboard.Name, createdDashboard.ID)

	// Example 8: Update dashboard
	fmt.Println("\nExample 8: Updating dashboard...")
	updatedDashboard := *createdDashboard
	updatedDashboard.Description = "Updated description: Monitor user engagement, activity metrics, and performance"
	updatedDashboard.Tags = append(updatedDashboard.Tags, "performance")

	finalDashboard, err := client.UpdateDashboard(createdDashboard.ID, updatedDashboard)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to update dashboard")
		return
	}
	fmt.Printf("Updated dashboard: %s\n", finalDashboard.Description)

	// Example 9: Get dashboard
	fmt.Println("\nExample 9: Retrieving dashboard...")
	retrievedDashboard, err := client.GetDashboard(createdDashboard.ID)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get dashboard")
		return
	}
	fmt.Printf("Retrieved dashboard: %s (Tags: %v)\n", retrievedDashboard.Name, retrievedDashboard.Tags)

	// Example 10: Helper methods for common analytics patterns
	fmt.Println("\nExample 10: Using helper methods...")

	// Track page view
	if err := client.TrackPageView(&userID, &orgID, "frontend-app", "/settings", "/dashboard", map[string]interface{}{
		"section": "account_settings",
	}); err == nil {
		fmt.Println("Page view tracked using helper method")
	}

	// Track API call
	if err := client.TrackAPICall(&userID, &orgID, "api-service", "POST", "/api/v1/settings", 201, 120, true); err == nil {
		fmt.Println("API call tracked using helper method")
	}

	// Record response time
	if err := client.RecordResponseTime("api-service", "production", "/api/v1/users", 85.5, map[string]interface{}{
		"cache_hit": true,
	}); err == nil {
		fmt.Println("Response time recorded using helper method")
	}

	// Increment counter
	if err := client.IncrementCounter("payment-service", "production", "payments_processed", 1, map[string]interface{}{
		"payment_method": "credit_card",
		"currency": "USD",
	}); err == nil {
		fmt.Println("Counter incremented using helper method")
	}

	// Set gauge
	if err := client.SetGauge("cache-service", "production", "cache_hit_ratio", "percent", 94.5, map[string]interface{}{
		"cache_type": "redis",
	}); err == nil {
		fmt.Println("Gauge value set using helper method")
	}

	// Example 11: Analytics for specific contexts
	fmt.Println("\nExample 11: Context-specific analytics...")

	// Get user analytics
	userAnalytics, err := client.GetUserAnalytics(userID, startTime, endTime)
	if err == nil {
		fmt.Printf("User analytics: %d events found\n", userAnalytics.Total)
	}

	// Get organization analytics
	orgAnalytics, err := client.GetOrganizationAnalytics(orgID, startTime, endTime)
	if err == nil {
		fmt.Printf("Organization analytics: %d events found\n", orgAnalytics.Total)
	}

	// Get service metrics
	serviceMetrics, err := client.GetServiceMetrics("api-service", "production", startTime, endTime)
	if err == nil {
		fmt.Printf("Service metrics: %d metrics found\n", serviceMetrics.Total)
	}

	// Cleanup: Delete the dashboard we created
	fmt.Println("\nCleaning up: Deleting test dashboard...")
	if err := client.DeleteDashboard(createdDashboard.ID); err != nil {
		logger.Warn().Err(err).Msg("Failed to delete test dashboard")
	} else {
		fmt.Println("Test dashboard deleted successfully")
	}

	fmt.Println("\nAll examples completed successfully!")
}

// Helper functions for pointer creation
func intPtr(i int) *int { return &i }
func boolPtr(b bool) *bool { return &b }