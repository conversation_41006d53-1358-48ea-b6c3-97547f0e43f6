package logger

import (
	"context"
	"io"
	"os"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm/logger"
)

// Logger is the global logger instance
var Logger zerolog.Logger

// Initialize sets up the global logger based on environment and log level
func Initialize(env, level string) {
	// Configure zerolog
	zerolog.TimeFieldFormat = time.RFC3339

	// Set log level
	logLevel := zerolog.InfoLevel
	switch level {
	case "debug":
		logLevel = zerolog.DebugLevel
	case "info":
		logLevel = zerolog.InfoLevel
	case "warn":
		logLevel = zerolog.WarnLevel
	case "error":
		logLevel = zerolog.ErrorLevel
	case "fatal":
		logLevel = zerolog.FatalLevel
	}
	zerolog.SetGlobalLevel(logLevel)

	// Set output format based on environment
	var writer io.Writer
	if env != "production" {
		// Pretty logging for development
		writer = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: "15:04:05",
		}
	} else {
		// JSON logging for production
		writer = os.Stdout
	}

	// Initialize global logger
	Logger = zerolog.New(writer).With().
		Timestamp().
		Str("service", "adc-analytics-service").
		Logger()

	// Set as global logger
	log.Logger = Logger
}

// GormLogger is a GORM logger adapter for zerolog
type GormLogger struct {
	logger zerolog.Logger
}

// NewGormLogger creates a new GORM logger with zerolog backend
func NewGormLogger() logger.Interface {
	return &GormLogger{
		logger: Logger.With().Str("component", "gorm").Logger(),
	}
}

func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Info().Msgf(msg, data...)
}

func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Warn().Msgf(msg, data...)
}

func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	l.logger.Error().Msgf(msg, data...)
}

func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	if err != nil {
		l.logger.Error().
			Err(err).
			Dur("elapsed", elapsed).
			Int64("rows", rows).
			Str("sql", sql).
			Msg("SQL query failed")
	} else if elapsed > 200*time.Millisecond {
		// Log slow queries
		l.logger.Warn().
			Dur("elapsed", elapsed).
			Int64("rows", rows).
			Str("sql", sql).
			Msg("Slow SQL query")
	} else {
		l.logger.Debug().
			Dur("elapsed", elapsed).
			Int64("rows", rows).
			Str("sql", sql).
			Msg("SQL query")
	}
}