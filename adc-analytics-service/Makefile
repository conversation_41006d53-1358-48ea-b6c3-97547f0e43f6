# ADC Analytics Service Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=adc-analytics-service
BINARY_UNIX=$(BINARY_NAME)_unix

# Build info
VERSION?=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%dT%H:%M:%S%z)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Service configuration
SERVICE_NAME=adc-analytics-service
SERVICE_PORT=9030
DATABASE_URL?=postgresql://user:password@localhost:5432/adc_analytics
REDIS_URL?=redis://localhost:6379
INFLUXDB_URL?=http://localhost:8086
ENVIRONMENT?=development

.PHONY: all build clean test coverage deps run dev docker help

## Build the binary
build:
	@echo "Building $(BINARY_NAME)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o ./bin/$(BINARY_NAME) ./cmd/server
	@echo "Built $(BINARY_NAME) successfully"

## Build for current OS
build-local:
	@echo "Building $(BINARY_NAME) for current OS..."
	$(GOBUILD) $(LDFLAGS) -o ./bin/$(BINARY_NAME) ./cmd/server
	@echo "Built $(BINARY_NAME) successfully"

## Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf ./bin
	rm -rf ./tmp
	@echo "Cleaned successfully"

## Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

## Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

## Run tests with race detector
test-race:
	@echo "Running tests with race detector..."
	$(GOTEST) -v -race ./...

## Benchmark tests
benchmark:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

## Update dependencies
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "Dependencies updated"

## Run the service in development mode
dev:
	@echo "Starting $(SERVICE_NAME) in development mode..."
	@echo "Service will be available at: http://localhost:$(SERVICE_PORT)"
	@echo "Health check: http://localhost:$(SERVICE_PORT)/health"
	@echo "Metrics: http://localhost:$(SERVICE_PORT)/metrics"
	@echo ""
	DATABASE_URL=$(DATABASE_URL) \
	REDIS_URL=$(REDIS_URL) \
	INFLUXDB_URL=$(INFLUXDB_URL) \
	ENVIRONMENT=$(ENVIRONMENT) \
	PORT=$(SERVICE_PORT) \
	LOG_LEVEL=debug \
	$(GOBUILD) $(LDFLAGS) -o ./tmp/$(BINARY_NAME) ./cmd/server && ./tmp/$(BINARY_NAME)

## Run the service
run: build-local
	@echo "Starting $(SERVICE_NAME)..."
	DATABASE_URL=$(DATABASE_URL) \
	REDIS_URL=$(REDIS_URL) \
	INFLUXDB_URL=$(INFLUXDB_URL) \
	ENVIRONMENT=$(ENVIRONMENT) \
	PORT=$(SERVICE_PORT) \
	./bin/$(BINARY_NAME)

## Health check
health:
	@echo "Checking service health..."
	@curl -f http://localhost:$(SERVICE_PORT)/health || (echo "Service is not healthy" && exit 1)
	@echo "Service is healthy"

## Detailed health check
health-detailed:
	@echo "Performing detailed health check..."
	@curl -s http://localhost:$(SERVICE_PORT)/health/detailed | jq '.' || echo "Service is not responding"

## Check service metrics
metrics:
	@echo "Fetching service metrics..."
	@curl -s http://localhost:$(SERVICE_PORT)/metrics | jq '.' || echo "Metrics not available"

## Format code
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

## Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

## Vet code
vet:
	@echo "Vetting code..."
	$(GOCMD) vet ./...

## Security scan
security:
	@echo "Running security scan..."
	gosec ./...

## Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(SERVICE_NAME):$(VERSION) .
	docker tag $(SERVICE_NAME):$(VERSION) $(SERVICE_NAME):latest

## Docker run
docker-run:
	@echo "Running Docker container..."
	docker run -p $(SERVICE_PORT):$(SERVICE_PORT) \
		-e DATABASE_URL=$(DATABASE_URL) \
		-e REDIS_URL=$(REDIS_URL) \
		-e INFLUXDB_URL=$(INFLUXDB_URL) \
		-e ENVIRONMENT=$(ENVIRONMENT) \
		$(SERVICE_NAME):latest

## Generate API documentation
docs:
	@echo "Generating API documentation..."
	swag init -g cmd/server/main.go

## Database migration (placeholder)
migrate-up:
	@echo "Running database migrations..."
	# Add your migration command here
	@echo "Migrations completed"

## Database rollback (placeholder)
migrate-down:
	@echo "Rolling back database migrations..."
	# Add your rollback command here
	@echo "Rollback completed"

## Load test the analytics endpoints
load-test:
	@echo "Running load test on analytics endpoints..."
	# Test event tracking
	hey -n 1000 -c 10 -m POST -H "Content-Type: application/json" \
		-d '{"type":"page_view","name":"test_page","category":"test"}' \
		http://localhost:$(SERVICE_PORT)/api/v1/events
	# Test health endpoint
	hey -n 1000 -c 10 http://localhost:$(SERVICE_PORT)/health

## Performance test with profiling
perf-test:
	@echo "Running performance test with profiling..."
	$(GOTEST) -bench=. -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof ./...

## Profile CPU
profile-cpu:
	@echo "Profiling CPU..."
	$(GOCMD) tool pprof cpu.prof

## Profile memory
profile-mem:
	@echo "Profiling memory..."
	$(GOCMD) tool pprof mem.prof

## Install development tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	$(GOGET) github.com/swaggo/swag/cmd/swag@latest
	@echo "Development tools installed"

## Show service status with analytics info
status:
	@echo "Analytics Service Status:"
	@echo "========================"
	@curl -s http://localhost:$(SERVICE_PORT)/health | jq '.' 2>/dev/null || echo "Service not running"
	@echo ""
	@echo "Metrics:"
	@curl -s http://localhost:$(SERVICE_PORT)/metrics | jq '.' 2>/dev/null || echo "Metrics not available"

## Test event ingestion
test-events:
	@echo "Testing event ingestion..."
	@curl -X POST http://localhost:$(SERVICE_PORT)/api/v1/events \
		-H "Content-Type: application/json" \
		-H "Authorization: Bearer test-token" \
		-d '{"type":"page_view","name":"test_page","category":"test","timestamp":"$(shell date -u +%Y-%m-%dT%H:%M:%S.000Z)"}' \
		| jq '.' || echo "Event ingestion test failed"

## Test metrics recording
test-metrics:
	@echo "Testing metrics recording..."
	@curl -X POST http://localhost:$(SERVICE_PORT)/api/v1/metrics \
		-H "Content-Type: application/json" \
		-H "Authorization: Bearer test-token" \
		-d '{"name":"test_metric","type":"counter","value":1,"timestamp":"$(shell date -u +%Y-%m-%dT%H:%M:%S.000Z)"}' \
		| jq '.' || echo "Metrics recording test failed"

## Analytics processing test
test-analytics:
	@echo "Testing analytics processing..."
	@curl -s http://localhost:$(SERVICE_PORT)/api/v1/analytics/insights \
		-H "Authorization: Bearer test-token" \
		| jq '.' || echo "Analytics processing test failed"

## Show service logs (placeholder)
logs:
	@echo "Showing service logs..."
	# Add log viewing command here (e.g., tail -f logs/analytics.log)

## Stop service (placeholder)
stop:
	@echo "Stopping service..."
	# Add stop command here (e.g., pkill -f $(BINARY_NAME))

## Start all dependencies (PostgreSQL, Redis, InfluxDB)
deps-up:
	@echo "Starting analytics dependencies..."
	# Start PostgreSQL, Redis, and InfluxDB using Docker Compose
	# docker-compose -f docker-compose.deps.yml up -d

## Stop all dependencies
deps-down:
	@echo "Stopping analytics dependencies..."
	# docker-compose -f docker-compose.deps.yml down

## Full development setup
setup: deps install-tools
	@echo "Analytics service development environment setup complete"
	@echo "Run 'make dev' to start the service"

## Display help
help:
	@echo "ADC Analytics Service - Available Commands:"
	@echo "=========================================="
	@echo ""
	@echo "Building:"
	@echo "  build          Build the binary for Linux"
	@echo "  build-local    Build the binary for current OS"
	@echo "  clean          Clean build artifacts"
	@echo ""
	@echo "Testing:"
	@echo "  test           Run tests"
	@echo "  test-coverage  Run tests with coverage"
	@echo "  test-race      Run tests with race detector"
	@echo "  benchmark      Run benchmark tests"
	@echo "  test-events    Test event ingestion endpoint"
	@echo "  test-metrics   Test metrics recording endpoint"
	@echo "  test-analytics Test analytics processing"
	@echo ""
	@echo "Development:"
	@echo "  dev            Run service in development mode"
	@echo "  run            Run the service"
	@echo "  deps           Update dependencies"
	@echo "  fmt            Format code"
	@echo "  lint           Lint code"
	@echo "  vet            Vet code"
	@echo ""
	@echo "Health & Monitoring:"
	@echo "  health         Check service health"
	@echo "  health-detailed Detailed health check"
	@echo "  metrics        Fetch service metrics"
	@echo "  status         Show service status with analytics info"
	@echo "  load-test      Run load test on analytics endpoints"
	@echo "  perf-test      Run performance test with profiling"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build   Build Docker image"
	@echo "  docker-run     Run Docker container"
	@echo ""
	@echo "Utilities:"
	@echo "  docs           Generate API documentation"
	@echo "  security       Run security scan"
	@echo "  install-tools  Install development tools"
	@echo "  setup          Full development setup"
	@echo "  help           Show this help message"
	@echo ""
	@echo "Configuration:"
	@echo "  SERVICE_PORT=$(SERVICE_PORT)"
	@echo "  DATABASE_URL=$(DATABASE_URL)"
	@echo "  REDIS_URL=$(REDIS_URL)"
	@echo "  INFLUXDB_URL=$(INFLUXDB_URL)"
	@echo "  ENVIRONMENT=$(ENVIRONMENT)"

# Default target
all: build