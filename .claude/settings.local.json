{"permissions": {"allow": ["<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(make status:*)", "<PERSON><PERSON>(curl:*)", "Bash(go mod:*)", "Bash(go build:*)", "<PERSON><PERSON>(cat:*)", "Bash(go list:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(sed:*)", "Bash(go tool compile:*)", "Bash(rg:*)", "<PERSON><PERSON>(go run:*)", "Bash(rm:*)", "Bash(timeout 5 go run cmd/api/main.go)", "<PERSON><PERSON>(mkdir:*)", "Bash(go get:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./cmd/settings-migrate/settings-migrate:*)", "Bash(./settings-migrate:*)", "<PERSON><PERSON>(env)", "Bash(psql:*)", "<PERSON><PERSON>(chmod:*)", "Bash(PORT=9200 DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres REDIS_URL=redis://localhost:6379 go run cmd/server/main.go)", "Bash(node:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(gtimeout:*)", "Bash(redis-cli:*)", "<PERSON>sh(redis-server:*)", "Bash(/dev/null)", "<PERSON><PERSON>(time curl:*)", "Bash(./quick_validation.sh:*)", "Bash(for port in 9000 9100 9200 8300 8400 3300 3400)", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(if nc -z localhost $port)", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(fi)", "Bash(done)", "Bash(./test_available_services.sh:*)", "Bash(./test_framework_validation.sh:*)", "Bash(./run_test_suite.sh:*)", "Bash(./comprehensive_tests.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(get_timestamp)", "Bash(get_tools_report)", "Bash(float_compare \"1.5\" \"2.0\" \"lt\")", "Bash(float_compare:*)", "Bash(__NEW_LINE__ echo \"Testing safe_calc:\")", "Bash(safe_calc:*)", "Bash(./performance_benchmarks.sh:*)", "Bash(./test_simple_perf.sh:*)", "Bash(./validate_all_tests.sh)", "Bash(bash:*)", "Bash(./debug_perf.sh:*)", "Bash(discover_services)", "Bash(./test_all_improvements.sh:*)", "Bash(./service_status_check.sh)", "Bash(./start.sh:*)", "Bash(nc:*)", "Bash(kill:*)", "Bash(PGPASSWORD=\"S#bQhDQArr.J2gr\" psql -h aws-0-ap-southeast-1.pooler.supabase.com -p 6543 -U postgres.wodsfmfdfxhzqbiqlzuh -d postgres -c \"\\dt\" 2 > /dev/null)", "Bash(PGPASSWORD:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright install:*)", "Bash(npm run test:e2e:*)", "Bash(npx tsc:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(./scripts/setup-i18n-project.sh:*)", "<PERSON><PERSON>(bun playwright test:*)", "Bash(PORT=8400 go run cmd/api/temp_main.go)", "<PERSON><PERSON>(set -a)", "Bash(set +a)", "Bash(npm run build:*)", "Bash(./scripts/test-translation-flows.sh:*)", "Bash(bun:*)", "Bash(PORT=8400 go run cmd/api/main.go)", "Bash(PORT=9010 go run cmd/server/main.go)", "Bash(STRIPE_SECRET_KEY=sk_test_dummy DATABASE_URL=dummy go run cmd/test-auto-translate/main.go)", "Bash(NEXT_PUBLIC_PORT=3800 bun run dev --no-turbo)", "Bash(GOPROXY=direct go build -a -o credit-server cmd/api/main.go)", "<PERSON><PERSON>(go clean:*)", "Bash(ORG_ID=\"43fb52c9-d3a7-4299-912b-0a7b751a7292\")", "Bash(./setup-translations.sh:*)", "Bash(./setup-platform-i18n.sh:*)", "Bash(./populate-platform-translations.sh:*)", "Bash(# Test accessing a translated page\ncurl -s \"http://localhost:3300/dashboard\" | grep -o \"Dashboard\\|Loading\\|Error\" | head -5)", "<PERSON>sh(./populate-chinese-translations.sh:*)", "Bash(./create-source-translations.sh:*)", "Bash(./enable-auto-translation.sh:*)", "Bash(./update-source-translations.sh:*)", "Bash(./check-db-schema.sh:*)", "Bash(./run_nav_population.sh:*)", "Bash(./run_french_nav_population.sh:*)", "Bash(open http://localhost:3300)", "Bash(open http://localhost:3300/dashboard)", "Bash(-H 'X-Internal-Key: adc-internal-2024' -v)", "Bash(npx @puppeteer/browsers install:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(DATABASE_URL=\"postgresql://postgres:<EMAIL>:5432/postgres\" go run cmd/seed-locales/main.go)", "Bash(open http://localhost:3300/?locale=ko)", "Bash(DATABASE_URL:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '[\"\"statsTitle\"\", \"\"statsSubtitle\"\", \"\"languagesSupported\"\", \"\"activeUsers\"\", \"\"translationAccuracy\"\", \"\"languagesSupportedChange\"\", \"\"activeUsersChange\"\", \"\"translationAccuracyChange\"\"]')", "<PERSON>sh(./test_arabic_translation.sh:*)", "Bash(./manual_arabic_test.sh:*)", "Bash(./debug_translation_creation.sh:*)", "Bash(./proper_arabic_test.sh:*)", "Bash(./test_fixed_flow.sh:*)", "Bash(./simple_test.sh:*)", "Bash(./test_english_first.sh:*)", "Bash(./run_tests.sh)", "Bash(cp:*)", "Bash(/tmp/test_backend_batch_init.sh:*)", "Bash(/tmp/test_with_english_values.sh:*)", "Bash(/tmp/test_gemini_quota.sh:*)", "Bash(/tmp/test_after_wait.sh:*)", "Bash(/tmp/test_new_api_key.sh:*)", "mcp__ide__getDiagnostics", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(open:*)", "Bash(GENERATE_LOCALES=true npm run dev)", "Bash(npm run zip:*)", "<PERSON><PERSON>(postcss:*)", "Bash(npm run copy-assets:*)", "Bash(for id in api-key-input api-url-input enabled-toggle indicators-toggle preload-toggle project-id-input save-settings-btn help-link toggle-api-key-visibility)", "Bash(do echo -n \"$id: \")", "Bash(git reset:*)"], "deny": []}}