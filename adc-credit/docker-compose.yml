version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8400}:${BACKEND_PORT:-8400}"
    environment:
      - PORT=${BACKEND_PORT:-8400}
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3400}
      - SCHEDULER_API_KEY=${SCHEDULER_API_KEY}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - GIN_MODE=${GIN_MODE:-debug}
    restart: unless-stopped
    networks:
      - adc-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.simple
    ports:
      - "${FRONTEND_PORT:-3400}:${FRONTEND_PORT:-3400}"
    environment:
      - PORT=${FRONTEND_PORT:-3400}
      - NEXT_PUBLIC_BACKEND_URL=${BACKEND_URL:-http://backend:8400}
      - NEXTAUTH_URL=${FRONTEND_URL:-http://localhost:3400}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - NODE_ENV=${NODE_ENV:-development}
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - adc-network

networks:
  adc-network:
    driver: bridge
