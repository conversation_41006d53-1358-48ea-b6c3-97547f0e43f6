steps:
  # Build the backend container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/adc-credit-backend:latest', './backend']

  # Push the backend container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/adc-credit-backend:latest']

  # Deploy backend container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'adc-credit-backend'
      - '--image=gcr.io/$PROJECT_ID/adc-credit-backend:latest'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--set-env-vars=DATABASE_URL=${_DATABASE_URL},JWT_SECRET=${_JWT_SECRET},JWT_REFRESH_SECRET=${_JWT_REFRESH_SECRET},GOOGLE_CLIENT_ID=${_GOOGLE_CLIENT_ID},GOOGLE_CLIENT_SECRET=${_GOOGLE_CLIENT_SECRET},FRONTEND_URL=${_FRONTEND_URL},SCHEDULER_API_KEY=${_SCHEDULER_API_KEY},STRIPE_SECRET_KEY=${_STRIPE_SECRET_KEY},STRIPE_WEBHOOK_SECRET=${_STRIPE_WEBHOOK_SECRET}'

  # Map custom domain for backend API
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "Domain mapping needs to be configured manually."
        echo "Run the following command after DNS is configured:"
        echo "gcloud run domain-mappings create --service=adc-credit-backend --domain=api.credit.adcshop.store --region=${_REGION}"

  # Note about scheduled credits
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "Note: The scheduled credits function deployment has been disabled in the unified deployment."
        echo "To deploy the scheduled credits function, use the separate command:"
        echo "make deploy-scheduled-credits"

  # Print deployment information
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "Deployment completed successfully!"
        backend_url=$(gcloud run services describe adc-credit-backend --region=${_REGION} --format="value(status.url)")
        echo "Backend URL: $backend_url"

images:
  - 'gcr.io/$PROJECT_ID/adc-credit-backend:latest'

substitutions:
  _REGION: 'us-central1'
  _DATABASE_URL: ''
  _JWT_SECRET: ''
  _JWT_REFRESH_SECRET: ''
  _GOOGLE_CLIENT_ID: ''
  _GOOGLE_CLIENT_SECRET: ''
  _FRONTEND_URL: ''
  _SCHEDULER_API_KEY: ''
  _STRIPE_SECRET_KEY: ''
  _STRIPE_WEBHOOK_SECRET: ''

options:
  logging: CLOUD_LOGGING_ONLY
