# Credit Service → ADC Subscription Service Architecture Mapping

## Overview

This document outlines the architectural mapping between the Credit Service and ADC Subscription Service, where **Shop = Organization** at the same hierarchical level, with **Branch** as the lower-level sub-unit.

## Core Architecture Principles

### 1. **Hierarchical Alignment**
```
Credit Service          ADC Subscription Service
─────────────          ─────────────────────────
Shop (UUID)       ←→   Organization (UUID)     [SAME LEVEL]
├── Branch (UUID) ←→   ├── Sub-unit (UUID)     [LOWER LEVEL]
├── Customer      ←→   ├── Member
├── API Key       ←→   ├── API Access
└── Subscription  ←→   └── Subscription
```

### 2. **UUID-First Design**
- **All IDs use UUID format** (not uint)
- **Shop.ID = Organization.ID** (1:1 direct mapping)
- **Branch.ID = Sub-unit.ID** (1:1 direct mapping)
- **Maintains data consistency** across systems

### 3. **Same-Level Entities**
- **Shop** and **Organization** are equivalent entities
- Both represent the primary business unit
- Both have subscriptions, users, and sub-units
- Both have the same ID and represent the same business entity

## Data Mapping Structure

### Shop ↔ Organization Mapping

```go
type ShopOrganizationMapping struct {
    // Identity (1:1 mapping)
    ShopID         uuid.UUID  // Credit service shop ID
    OrganizationID uuid.UUID  // ADC organization ID (same as ShopID)
    
    // Basic Information
    ShopSlug       string     // Shop slug/identifier
    ShopName       string     // Shop display name
    OwnerUserID    uuid.UUID  // Shop owner
    
    // Platform Configuration
    TenantID       string     // "credit" (platform tenant)
    Platform       string     // "credit" (platform type)
    
    // Subscription Mapping
    SubscriptionPlan string   // Mapped from SubscriptionTier
    
    // Hierarchical Sub-units
    Branches       []BranchMapping // Shop branches → organization sub-units
}
```

### Branch ↔ Sub-unit Mapping

```go
type BranchMapping struct {
    BranchID   uuid.UUID  // Credit service branch ID
    BranchName string     // Branch display name
    BranchSlug string     // Branch identifier
    Location   string     // Physical location
    IsActive   bool       // Operational status
}
```

## Implementation Architecture

### 1. **Service Layer**

```go
// Credit Service Architecture
ShopService
├── SubscriptionGuard      (legacy)
├── ShopSubscriptionGuard  (legacy)
└── HybridSubscriptionService (new)
    ├── OrganizationMappingService
    └── ADC SubscriptionClient
```

### 2. **Hybrid Operation Mode**

During migration, the system operates in **dual mode**:

- **Legacy-first**: Checks Credit service subscription system first
- **ADC-fallback**: Falls back to ADC Subscription Service if legacy fails
- **Migration-ready**: Can switch to ADC-first mode when ready

### 3. **Data Flow**

```mermaid
graph TD
    A[Credit Shop Request] --> B[Hybrid Service]
    B --> C{Migration Mode?}
    C -->|Legacy-first| D[Credit Subscription Guard]
    C -->|ADC-first| E[Organization Mapping]
    D --> F{Success?}
    F -->|No| E
    F -->|Yes| G[Return Credit Result]
    E --> H[ADC Subscription Client]
    H --> I[Return ADC Result]
    G --> J[Unified Response]
    I --> J
```

## Key Benefits

### 1. **Conceptual Clarity**
- **Shop = Organization**: Clear 1:1 business entity mapping
- **Branch = Sub-unit**: Logical hierarchical relationship
- **No artificial nesting**: Shop is not "under" organization

### 2. **Data Consistency**
- **Same UUIDs**: Shop ID becomes Organization ID directly
- **Referential integrity**: All related data maintains relationships
- **Migration safety**: Rollback uses same identifiers

### 3. **Scalability**
- **Branch support**: Organizations can have multiple sub-units
- **Multi-tenant**: Clear tenant isolation per platform
- **Feature parity**: All Credit service features map to ADC

## Migration Strategy

### Phase 1: ✅ **Infrastructure Setup**
- SDK integration
- Environment configuration
- Connectivity testing

### Phase 2: 🔄 **Mapping Layer** (Current)
- Organization mapping service
- Hybrid subscription service
- UUID architecture alignment

### Phase 3: ⏳ **Dual-Write Mode**
- Write to both systems
- Validation and consistency checks
- Branch mapping implementation

### Phase 4: ⏳ **ADC-First Mode**
- Switch to ADC as primary
- Legacy as fallback
- Performance optimization

### Phase 5: ⏳ **Legacy Cleanup**
- Remove legacy subscription code
- Database cleanup
- Final validation

## Technical Implementation

### API Endpoints

```go
// Organization Management (Shop = Organization)
GET    /api/v1/organizations/{org-id}           // Get shop/organization
POST   /api/v1/organizations                    // Create shop/organization
PUT    /api/v1/organizations/{org-id}           // Update shop/organization

// Branch Management (Sub-units)
GET    /api/v1/organizations/{org-id}/branches  // Get organization branches
POST   /api/v1/organizations/{org-id}/branches  // Create branch/sub-unit
PUT    /api/v1/organizations/{org-id}/branches/{branch-id}  // Update branch

// Subscription Management
GET    /api/v1/organizations/{org-id}/subscription         // Get subscription
POST   /api/v1/organizations/{org-id}/subscription         // Create subscription
PUT    /api/v1/organizations/{org-id}/subscription         // Update subscription
```

### Database Schema Alignment

```sql
-- Credit Service
shops (
    id UUID PRIMARY KEY,        -- Maps to ADC organizations.id
    slug VARCHAR,
    name VARCHAR,
    owner_user_id UUID
);

shop_branches (
    id UUID PRIMARY KEY,        -- Maps to ADC organization_sub_units.id
    shop_id UUID,              -- Maps to ADC organization_sub_units.organization_id
    name VARCHAR,
    location VARCHAR
);

-- ADC Subscription Service  
organizations (
    id UUID PRIMARY KEY,        -- Same as Credit shops.id
    name VARCHAR,
    tenant_id VARCHAR,
    platform VARCHAR
);

organization_sub_units (
    id UUID PRIMARY KEY,        -- Same as Credit shop_branches.id
    organization_id UUID,       -- Same as Credit shop_branches.shop_id
    name VARCHAR,
    metadata JSONB
);
```

## Validation & Testing

### 1. **ID Consistency**
- Verify Shop.ID = Organization.ID
- Verify Branch.ID = Sub-unit.ID
- Test UUID format compliance

### 2. **Functional Parity**
- All subscription limits work
- All features available
- Performance within acceptable range

### 3. **Migration Safety**
- Rollback capability verified
- Data integrity maintained
- No data loss scenarios

## Conclusion

This architecture provides a clean, scalable mapping where **Shop and Organization are peers** at the same level, with **Branches as proper sub-units**. The UUID-first design ensures data consistency and enables seamless migration between systems while maintaining business logic integrity.