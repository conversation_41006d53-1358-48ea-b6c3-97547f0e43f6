-- Monthly usage tracking table for subscription limits
CREATE TABLE monthly_usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    
    -- Counts for monthly limits
    qr_codes_generated INTEGER DEFAULT 0,
    api_calls_made INTEGER DEFAULT 0,
    credits_consumed INTEGER DEFAULT 0,
    
    -- Persistent counts (don't reset monthly, but tracked for analytics)
    shops_created INTEGER DEFAULT 0,
    customers_added INTEGER DEFAULT 0,
    api_keys_created INTEGER DEFAULT 0,
    branches_created INTEGER DEFAULT 0,
    webhooks_created INTEGER DEFAULT 0,
    
    -- Metadata
    last_reset_date TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Unique constraint to ensure one record per user per month
    UNIQUE(user_id, year, month)
);

-- Indexes for efficient querying
CREATE INDEX idx_monthly_usage_user_date ON monthly_usage_tracking(user_id, year, month);
CREATE INDEX idx_monthly_usage_reset_date ON monthly_usage_tracking(last_reset_date);

-- Add monthly usage reset date to users table
ALTER TABLE users ADD COLUMN monthly_usage_reset_date TIMESTAMP DEFAULT (DATE_TRUNC('month', NOW()) + INTERVAL '1 month');

-- Add monthly usage limit fields to subscription_tiers (if not already present)
ALTER TABLE subscription_tiers ADD COLUMN IF NOT EXISTS max_api_calls_per_month INTEGER DEFAULT 10000;
ALTER TABLE subscription_tiers ADD COLUMN IF NOT EXISTS max_credits_per_month INTEGER DEFAULT 1000;