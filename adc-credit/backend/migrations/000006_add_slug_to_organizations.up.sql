-- Add slug column to organizations table
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS slug VARCHAR(255);

-- Generate initial slugs for existing organizations
DO $$
DECLARE
    org RECORD;
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER;
    slug_exists BOOLEAN;
BEGIN
    FOR org IN SELECT id, name FROM organizations WHERE slug IS NULL OR slug = '' LOOP
        -- Generate base slug from name
        base_slug := LOWER(REGEXP_REPLACE(org.name, '[^a-zA-Z0-9]', '-', 'g'));

        -- Remove leading and trailing hyphens
        base_slug := TRIM(BOTH '-' FROM base_slug);

        -- If empty, use a default
        IF base_slug = '' THEN
            base_slug := 'organization';
        END IF;

        -- Start with the base slug
        final_slug := base_slug;
        counter := 1;

        -- Check if slug exists and append counter if needed
        LOOP
            SELECT EXISTS(SELECT 1 FROM organizations WHERE slug = final_slug AND id != org.id) INTO slug_exists;
            EXIT WHEN NOT slug_exists;

            -- Append counter to make slug unique
            final_slug := base_slug || '-' || counter;
            counter := counter + 1;
        END LOOP;

        -- Update the organization with the unique slug
        UPDATE organizations SET slug = final_slug WHERE id = org.id;
    END LOOP;
END $$;

-- Create a unique index on the slug column
CREATE UNIQUE INDEX IF NOT EXISTS organizations_slug_idx ON organizations (slug);

-- Make slug column NOT NULL after populating it
ALTER TABLE organizations ALTER COLUMN slug SET NOT NULL;
