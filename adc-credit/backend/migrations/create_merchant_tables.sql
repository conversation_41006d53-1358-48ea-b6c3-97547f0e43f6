-- Create merchant_shops table
CREATE TABLE IF NOT EXISTS merchant_shops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT,
    description TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    owner_user_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    slug TEXT,
    CONSTRAINT fk_merchant_shops_owner FOREI<PERSON><PERSON> KEY (owner_user_id) REFERENCES users(id)
);

-- Create indexes for merchant_shops
CREATE INDEX IF NOT EXISTS idx_merchant_shops_deleted_at ON merchant_shops(deleted_at);
CREATE UNIQUE INDEX IF NOT EXISTS idx_merchant_shops_slug ON merchant_shops(slug);

-- Create shop_customers table
CREATE TABLE IF NOT EXISTS shop_customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL,
    user_id UUID NOT NULL,
    phone TEXT,
    credit_balance BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT fk_merchant_shops_customers FOREIGN KEY (shop_id) REFERENCES merchant_shops(id),
    CONSTRAINT fk_shop_customers_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for shop_customers
CREATE INDEX IF NOT EXISTS idx_shop_customers_deleted_at ON shop_customers(deleted_at);

-- Create credit_codes table
CREATE TABLE IF NOT EXISTS credit_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL,
    code TEXT NOT NULL,
    amount BIGINT,
    description TEXT,
    is_redeemed BOOLEAN DEFAULT false,
    redeemed_by_id UUID,
    redeemed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT fk_merchant_shops_credit_codes FOREIGN KEY (shop_id) REFERENCES merchant_shops(id),
    CONSTRAINT fk_credit_codes_redeemed_by FOREIGN KEY (redeemed_by_id) REFERENCES users(id)
);

-- Create indexes for credit_codes
CREATE INDEX IF NOT EXISTS idx_credit_codes_deleted_at ON credit_codes(deleted_at);
CREATE UNIQUE INDEX IF NOT EXISTS idx_credit_codes_code ON credit_codes(code);

-- Create shop_credit_transactions table
CREATE TABLE IF NOT EXISTS shop_credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    type TEXT,
    amount BIGINT,
    description TEXT,
    reference TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT fk_shop_credit_transactions_shop FOREIGN KEY (shop_id) REFERENCES merchant_shops(id),
    CONSTRAINT fk_shop_credit_transactions_customer FOREIGN KEY (customer_id) REFERENCES shop_customers(id)
);

-- Create indexes for shop_credit_transactions
CREATE INDEX IF NOT EXISTS idx_shop_credit_transactions_deleted_at ON shop_credit_transactions(deleted_at);
