steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/adc-credit-backend:$COMMIT_SHA', './backend']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/adc-credit-backend:$COMMIT_SHA']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'adc-credit-backend'
      - '--image=gcr.io/$PROJECT_ID/adc-credit-backend:$COMMIT_SHA'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--set-env-vars=DATABASE_URL=${_DATABASE_URL}'
      - '--set-env-vars=JWT_SECRET=${_JWT_SECRET}'
      - '--set-env-vars=GOOGLE_CLIENT_ID=${_GOOGLE_CLIENT_ID}'
      - '--set-env-vars=GOOGLE_CLIENT_SECRET=${_GOOGLE_CLIENT_SECRET}'
      - '--set-env-vars=FRONTEND_URL=${_FRONTEND_URL}'
      - '--set-env-vars=SCHEDULER_API_KEY=${_SCHEDULER_API_KEY}'

  # Deploy the scheduled credits Cloud Function
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        cd backend
        gcloud functions deploy process-scheduled-credits \
          --gen2 \
          --runtime=go123 \
          --region=us-central1 \
          --source=./cmd/cloud-functions/process_scheduled_credits \
          --entry-point=ProcessScheduledCredits \
          --trigger-http \
          --allow-unauthenticated \
          --set-env-vars="DATABASE_URL=${_DATABASE_URL},SCHEDULER_API_KEY=${_SCHEDULER_API_KEY}"

  # Create or update the Cloud Scheduler job
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        FUNCTION_URL=$(gcloud functions describe process-scheduled-credits --gen2 --region=us-central1 --format="value(serviceConfig.uri)")

        # Check if the scheduler job already exists
        if gcloud scheduler jobs describe daily-credit-refresh --location=us-central1 &> /dev/null; then
          # Update existing job
          gcloud scheduler jobs update http daily-credit-refresh \
            --location=us-central1 \
            --schedule="0 0 * * *" \
            --uri="$FUNCTION_URL" \
            --http-method=POST \
            --headers="X-API-Key=${_SCHEDULER_API_KEY}"
        else
          # Create new job
          gcloud scheduler jobs create http daily-credit-refresh \
            --location=us-central1 \
            --schedule="0 0 * * *" \
            --uri="$FUNCTION_URL" \
            --http-method=POST \
            --headers="X-API-Key=${_SCHEDULER_API_KEY}"
        fi

images:
  - 'gcr.io/$PROJECT_ID/adc-credit-backend:$COMMIT_SHA'

substitutions:
  _DATABASE_URL: ''
  _JWT_SECRET: ''
  _GOOGLE_CLIENT_ID: ''
  _GOOGLE_CLIENT_SECRET: ''
  _FRONTEND_URL: ''
  _SCHEDULER_API_KEY: ''

options:
  logging: CLOUD_LOGGING_ONLY
