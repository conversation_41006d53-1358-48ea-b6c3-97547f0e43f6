package routes

import (
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/middleware"
	"github.com/gin-gonic/gin"
)

// RegisterMerchantRoutes registers all routes related to the merchant credit system
func RegisterMerchantRoutes(r *gin.Engine) {
	// Protected routes (require authentication)
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// Merchant shop management
		shops := protected.Group("/merchant-shops")
		{
			shops.GET("", handlers.GetMerchantShops)
			shops.POST("", handlers.CreateMerchantShop)
			shops.GET("/:id", handlers.GetMerchantShop)
			shops.GET("/slug/:slug", handlers.GetMerchantShopBySlug)
			shops.PUT("/:id", handlers.UpdateMerchantShop)
			shops.DELETE("/:id", handlers.DeleteMerchantShop)

			// Customer management
			shops.GET("/:id/customers", handlers.GetShopCustomers)
			shops.POST("/:id/customers", handlers.AddShopCustomer)
			shops.POST("/:id/customers/:customerId/credits", handlers.AddShopCredit)

			// Credit code management
			shops.GET("/:id/credit-codes", handlers.GetCreditCodes)
			shops.POST("/:id/credit-codes", handlers.GenerateCreditCode)
			shops.POST("/:id/credit-codes/qr", handlers.GenerateQRCode)

			// Transaction management
			shops.GET("/:id/transactions", handlers.GetShopTransactions)
		}

		// Merchant credit statistics
		protected.GET("/merchant/credit-stats", handlers.GetMerchantCreditStats)

		// Customer routes
		customers := protected.Group("/customer")
		{
			customers.GET("/shops", handlers.GetCustomerShops)
			customers.GET("/shops/:id", handlers.GetCustomerShop)
			customers.GET("/shops/:id/transactions", handlers.GetCustomerTransactions)
			customers.POST("/shops/:id/use-credit", handlers.UseShopCredit)
			customers.POST("/redeem-code", handlers.RedeemCreditCode)
		}
	}
}
