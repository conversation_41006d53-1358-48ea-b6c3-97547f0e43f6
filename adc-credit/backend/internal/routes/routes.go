package routes

import (
	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/middleware"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// RegisterRoutes sets up all API routes
func RegisterRoutes(r *gin.Engine, ssoClient interface{}, subscriptionClient *subscriptionSDK.Client, analyticsClient interface{}) {
	// Create SSO client stub for service container (temporary until full integration)
	ssoStub := services.NewSSOClient("http://localhost:9000", "sso-api-key")
	
	// Initialize service container with database and all clients
	serviceContainer := services.NewServiceContainer(database.DB, subscriptionClient, ssoStub)
	
	// Initialize usage middleware with the service container
	usageMiddleware := middleware.NewUsageTrackingMiddleware(serviceContainer.GetUsageReportingService(), nil)
	
	// Register unified shop routes
	RegisterShopRoutes(r, subscriptionClient, serviceContainer)
	// Register merchant routes (legacy - will be deprecated)
	// RegisterMerchantRoutes(r) // Temporarily disabled due to route conflicts
	// Public routes
	public := r.Group("/api/v1")
	{
		// Health check
		public.GET("/health", handlers.HealthCheck)
		
		// Subscription service connectivity test
		public.GET("/test/subscription-service", handlers.TestSubscriptionServiceConnectivity(subscriptionClient))

		// Scheduled tasks (protected by API key) - V2 shop-based
		scheduledCreditV2Handler := handlers.NewScheduledCreditV2Handler(serviceContainer)
		public.POST("/tasks/process-scheduled-credits", middleware.ValidateAPIKey(), scheduledCreditV2Handler.ProcessScheduledCredits)
		public.POST("/tasks/process-monthly-quota-reset", middleware.ValidateAPIKey(), handlers.ProcessMonthlyQuotaReset)

		// Authentication
		auth := public.Group("/auth")
		{
			auth.POST("/login", handlers.Login)
			auth.POST("/google", handlers.GoogleAuth)
			auth.POST("/refresh", handlers.RefreshToken)
			auth.POST("/credentials", handlers.Login) // Use the same Login handler for credentials
			auth.POST("/register", handlers.RegisterUser)
			auth.POST("/forgot-password", handlers.ForgotPassword)
			auth.POST("/reset-password", handlers.ResetPassword)
			auth.POST("/customer-login", handlers.CustomerLogin) // Customer login with username/password
			
			// SSO routes
			sso := auth.Group("/sso")
			{
				sso.GET("/login", handlers.SSOLogin(ssoClient))
				sso.GET("/callback", handlers.SSOCallback(ssoClient))
			}
		}

		// External API for third-party systems with usage tracking
		creditV2Handler := handlers.NewCreditV2Handler(serviceContainer)
		external := public.Group("/external")
		{
			external.POST("/verify", creditV2Handler.VerifyAPIKey)
			external.POST("/consume", middleware.RateLimitMiddleware(), middleware.ValidateAPIKey(), usageMiddleware.TrackAPIUsage(), usageMiddleware.TrackCreditConsumption(), creditV2Handler.ConsumeCredits)
		}

		// Public subscription endpoints - REMOVED: Now handled by ADC Subscription Service
		// publicSubscriptions := public.Group("/public/subscriptions")
		// {
		//     publicSubscriptions.GET("/tiers", handlers.GetSubscriptionTiers)
		// }
	}

	// Protected routes (require authentication)
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// User management
		users := protected.Group("/users")
		{
			users.GET("/me", handlers.GetCurrentUser)
			users.PUT("/me", handlers.UpdateUser)
		}

		// Organization management - moved to shop routes for unified system
		// protected.GET("/organizations", handlers.GetOrganizations)
		// protected.POST("/organizations", handlers.CreateOrganization)
		// protected.GET("/organizations/:slug", handlers.GetOrganizationBySlug)
		// protected.PUT("/organizations/:slug", handlers.UpdateOrganization)
		// protected.DELETE("/organizations/:slug", handlers.DeleteOrganization)

		// Legacy organization endpoints (for backward compatibility) - moved to shop routes
		// protected.GET("/organizations/id/:id", handlers.GetOrganization)

		// Branch management - moved to shop routes
		// protected.GET("/org-branches", handlers.GetBranches)
		// protected.POST("/org-branches", handlers.CreateBranch)
		// protected.GET("/org-branches/:id", handlers.GetBranch)
		// protected.PUT("/org-branches/:id", handlers.UpdateBranch)
		// protected.DELETE("/org-branches/:id", handlers.DeleteBranch)

		// External user management - moved to shop routes
		// protected.GET("/org-users", handlers.GetExternalUsers)
		// protected.POST("/org-users", handlers.CreateExternalUser)
		// protected.GET("/org-users/:id", handlers.GetExternalUser)
		// protected.PUT("/org-users/:id", handlers.UpdateExternalUser)
		// protected.DELETE("/org-users/:id", handlers.DeleteExternalUser)
		// protected.POST("/org-users/:id/credits/add", handlers.AddCreditsToExternalUser)
		// protected.POST("/org-users/:id/credits/reduce", handlers.ReduceCreditsFromExternalUser)

		// API key management
		apiKeys := protected.Group("/apikeys")
		{
			apiKeys.GET("", handlers.GetAPIKeys)
			apiKeys.POST("", handlers.CreateAPIKey)
			apiKeys.GET("/:id", handlers.GetAPIKey)
			apiKeys.PUT("/:id", handlers.UpdateAPIKey)
			apiKeys.DELETE("/:id", handlers.DeleteAPIKey)
		}

		// Credit management with usage tracking (V2 - shop-based)
		credits := protected.Group("/credits")
		{
			credits.GET("", usageMiddleware.TrackAPIUsage(), creditV2Handler.GetCreditBalance)
			credits.POST("/add", usageMiddleware.TrackAPIUsage(), usageMiddleware.TrackCreditConsumption(), creditV2Handler.AddCredits)
			credits.GET("/transactions", usageMiddleware.TrackAPIUsage(), creditV2Handler.GetTransactions)
			credits.GET("/scheduled/next", usageMiddleware.TrackAPIUsage(), scheduledCreditV2Handler.GetNextScheduledCreditDate)
			credits.GET("/scheduled/history", usageMiddleware.TrackAPIUsage(), scheduledCreditV2Handler.GetScheduledCreditHistory)
		}

		// Usage statistics
		usage := protected.Group("/usage")
		{
			usage.GET("", handlers.GetUsage)
			usage.GET("/summary", handlers.GetUsageSummary)
		}

		// Subscription management (shop-based system with ADC service integration)
		shopHandler := handlers.NewShopSubscriptionHandler(serviceContainer)
		subscriptions := protected.Group("/subscriptions")
		{
			// Shop-based subscription endpoints (primary routes)
			subscriptions.GET("/tiers", shopHandler.GetSubscriptionTiers)
			subscriptions.GET("/shops/:id", shopHandler.GetShopSubscription)
			subscriptions.POST("/shops/:id", shopHandler.CreateShopSubscription)
			subscriptions.PUT("/shops/:id", shopHandler.UpdateShopSubscription)
			subscriptions.GET("/shops/:id/limits", shopHandler.CheckSubscriptionLimits)
			subscriptions.GET("/shops/:id/features", shopHandler.GetShopSubscriptionFeatures)
		}

		// Subscription limits
		limitsHandler := handlers.NewSubscriptionLimitsHandler(serviceContainer)
		limits := protected.Group("/subscription-limits")
		{
			limits.GET("", limitsHandler.GetUserLimits)
			// Shop-based subscription limit checks
			limits.GET("/shops/check", limitsHandler.CheckShopLimit)
			limits.GET("/shops/:shopId/customers/check", limitsHandler.CheckCustomerLimit)
			limits.GET("/shops/:shopId/api-keys/check", limitsHandler.CheckAPIKeyLimit)
			limits.GET("/shops/:shopId/branches/check", limitsHandler.CheckBranchLimit)
			limits.GET("/qr-codes/check", limitsHandler.CheckQRCodeLimit)
			limits.GET("/webhooks/check", limitsHandler.CheckWebhookLimit)
			limits.GET("/shop-types/check", limitsHandler.CheckShopTypeAllowed)
			limits.GET("/credits/check", limitsHandler.CheckCreditBalance)
		}

		// Stripe integration - Legacy routes removed (deprecated)

		// Centralized Stripe integration via ADC Subscription Service
		centralizedStripeHandler := handlers.NewCentralizedStripeHandler(serviceContainer)
		stripeV2 := protected.Group("/stripe/v2")
		{
			stripeV2.POST("/create-checkout-session", centralizedStripeHandler.CreateCheckoutSessionCentralized)
			stripeV2.GET("/plans", centralizedStripeHandler.GetSubscriptionPlansCentralized)
			stripeV2.GET("/subscription/:shopId", centralizedStripeHandler.GetShopSubscriptionCentralized)
			stripeV2.POST("/subscription/:shopId/cancel", centralizedStripeHandler.CancelSubscriptionCentralized)
		}

		// Stripe webhooks (no authentication required)
		stripeWebhookV2Handler := handlers.NewStripeWebhookV2Handler(serviceContainer)
		r.POST("/webhook/stripe/v2", stripeWebhookV2Handler.HandleStripeWebhookV2) // New shop-based handling
		// Note: Centralized webhook handling is done by the ADC Subscription Service directly

		// Webhook management
		shopGuardMiddleware := middleware.NewShopSubscriptionGuardMiddleware(serviceContainer)
		webhooks := protected.Group("/webhooks")
		{
			webhooks.GET("", handlers.GetWebhooks)
			webhooks.POST("", shopGuardMiddleware.CheckWebhookLimit(), handlers.CreateWebhook)
			webhooks.GET("/:id", handlers.GetWebhook)
			webhooks.PUT("/:id", handlers.UpdateWebhook)
			webhooks.DELETE("/:id", handlers.DeleteWebhook)
			webhooks.GET("/:id/deliveries", handlers.GetWebhookDeliveries)
		}

		// Advanced analytics
		analytics := protected.Group("/analytics")
		{
			analytics.GET("/summary", handlers.GetAnalyticsSummary)
			analytics.GET("/trends", handlers.GetAnalyticsTrends)
			analytics.GET("/endpoints", handlers.GetEndpointAnalytics)
			analytics.GET("/performance", handlers.GetPerformanceMetrics)
		}

		// Translation endpoints (proxy to Multi-Languages service)
		translations := protected.Group("/translations")
		{
			translations.GET("/:namespace/:key/:locale", handlers.GetTranslation)
			translations.GET("/:namespace/:locale", handlers.GetNamespaceTranslations)
		}

		// Auto-translation settings and processing
		autoTranslationHandler := handlers.NewAutoTranslationHandler(serviceContainer.GetSettingsService(), serviceContainer.GetAutoTranslationProcessor(), nil)
		autoTranslation := protected.Group("/auto-translation")
		{
			// Health check
			autoTranslation.GET("/health", autoTranslationHandler.HealthCheck)
			
			// User auto-translation settings
			autoTranslation.GET("/users/:user_id/settings", autoTranslationHandler.GetUserAutoTranslationSettings)
			autoTranslation.PUT("/users/:user_id/settings", autoTranslationHandler.UpdateUserAutoTranslationSettings)
			
			// Shop auto-translation settings
			autoTranslation.GET("/shops/:shop_id/settings", autoTranslationHandler.GetShopAutoTranslationSettings)
			autoTranslation.PUT("/shops/:shop_id/settings", autoTranslationHandler.UpdateShopAutoTranslationSettings)
			
			// Auto-translation processing
			autoTranslation.POST("/shops/:shop_id/trigger", autoTranslationHandler.TriggerAutoTranslation)
			autoTranslation.GET("/shops/:shop_id/status", autoTranslationHandler.GetAutoTranslationStatus)
		}

		// ADC Sync endpoints for migration and testing
		adcSyncHandler := handlers.NewADCSyncHandler(serviceContainer)
		adcSync := protected.Group("/adc-sync")
		{
			adcSync.POST("/shops/:shopId", adcSyncHandler.SyncShopToADC)
			adcSync.POST("/shops/all", adcSyncHandler.SyncAllShopsToADC)
			adcSync.GET("/shops/:shopId/status", adcSyncHandler.GetADCSubscriptionStatus)
		}

		// Usage reporting endpoints (ADC integration) - TODO: Implement NewUsageReportingHandler
		// usageReportingHandler := handlers.NewUsageReportingHandler(serviceContainer, logger.New())
		// usageReporting := protected.Group("/usage-reporting")
		// {
		//     // Manual usage reporting
		//     usageReporting.POST("/report", usageReportingHandler.ReportUsage)
		//     
		//     // Usage analytics and metrics
		//     usageReporting.GET("/metrics", usageReportingHandler.GetUsageMetrics)
		//     usageReporting.GET("/health", usageReportingHandler.GetUsageHealth)
		//     
		//     // Shop-specific usage endpoints
		//     usageReporting.GET("/shops/:shop_id", usageReportingHandler.GetShopUsage)
		//     usageReporting.GET("/shops/:shop_id/recent", usageReportingHandler.GetRecentUsage)
		//     usageReporting.GET("/shops/:shop_id/aggregates", usageReportingHandler.GetUsageAggregates)
		//     usageReporting.POST("/shops/:shop_id/trigger-report", usageReportingHandler.TriggerUsageReport)
		// }
	}

	// Admin routes (require admin role)
	admin := r.Group("/api/v1/admin")
	admin.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		admin.GET("/users", handlers.GetAllUsers)
		admin.GET("/users/:id", handlers.GetUser)
		admin.PUT("/users/:id", handlers.UpdateUserByAdmin)
		admin.DELETE("/users/:id", handlers.DeleteUser)

		// Subscription tier management - REMOVED: Now handled by ADC Subscription Service
		// admin.POST("/subscription-tiers", handlers.CreateSubscriptionTier)
		// admin.PUT("/subscription-tiers/:id", handlers.UpdateSubscriptionTier)
		// admin.DELETE("/subscription-tiers/:id", handlers.DeleteSubscriptionTier)

		// Scheduled credits admin endpoints (V2 - shop-based) - Using public endpoint
		// admin.POST("/credits/scheduled/process", scheduledCreditV2Handler.ProcessScheduledCredits)
		// admin.POST("/credits/scheduled/manual", scheduledCreditV2Handler.ManuallyAddScheduledCredits)

		// Monthly quota management
		quota := admin.Group("/quota")
		{
			quota.POST("/reset-monthly", handlers.ProcessMonthlyQuotaReset)
			quota.GET("/usage/:user_id", handlers.GetUserMonthlyUsage)
			quota.POST("/reset-user/:user_id", handlers.ForceResetUserQuota)
			quota.GET("/stats", handlers.GetMonthlyUsageStats)
		}
	}
}
