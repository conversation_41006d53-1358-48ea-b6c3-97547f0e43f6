package testing

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"
	"testing"

	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	TestDB     *gorm.DB
	TestRouter *gin.Engine
)

// SetupTestEnvironment initializes the test environment
func SetupTestEnvironment() error {
	// Load test environment variables
	if err := godotenv.Load(".env.test"); err != nil {
		if err := godotenv.Load("../.env.test"); err != nil {
			if err := godotenv.Load("../../.env.test"); err != nil {
				logrus.Info("No .env.test file found, using environment variables")
			}
		}
	}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test database
	if err := setupTestDatabase(); err != nil {
		return fmt.Errorf("failed to setup test database: %w", err)
	}

	// Initialize test router
	setupTestRouter()

	return nil
}

// setupTestDatabase initializes the test database connection
func setupTestDatabase() error {
	// Get test database URL
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		// Fallback to main database URL with test suffix
		mainDBURL := os.Getenv("DATABASE_URL")
		if mainDBURL == "" {
			return fmt.Errorf("TEST_DATABASE_URL or DATABASE_URL environment variable is required")
		}
		testDBURL = mainDBURL + "_test"
	}

	// Connect to test database
	db, err := gorm.Open(postgres.Open(testDBURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Reduce log noise in tests
	})
	if err != nil {
		return fmt.Errorf("failed to connect to test database: %w", err)
	}

	TestDB = db

	// Skip migration for now - assume database is already set up
	// This is a temporary workaround for testing against existing database
	// In a production test setup, we would use a separate test database

	return nil
}

// useMainDatabaseMigrations uses the main application's migration logic
func useMainDatabaseMigrations() error {
	// Import and use the main database package
	// Note: This may require restructuring imports to avoid circular dependencies
	
	// For now, let's use a simplified version that mirrors the main migrations
	// but handles existing tables gracefully
	return TestDB.AutoMigrate(
		// Core models first (no dependencies)
		&models.User{},
		// Organization models
		&models.Organization{},
		&models.Shop{},
		&models.MerchantShop{},
		// Models that depend on Organization/Shop
		&models.Branch{},
		&models.ShopBranch{},
		// Subscription and relationship models
		&models.ShopSubscription{},
		&models.ShopUser{},
		&models.ShopTransaction{},
		&models.ShopMonthlyUsageTracking{},
		&models.APIKey{},
		// Usage and analytics models
		&models.Usage{},
		&models.Transaction{},
		&models.Webhook{},
		&models.WebhookDelivery{},
		&models.AnalyticsData{},
		&models.MonthlyUsageTracking{},
	)
}

// autoMigrateTestDB runs auto-migration for all models
func autoMigrateTestDB() error {
	// Use a simpler approach - migrate in several small batches to avoid dependency issues
	
	// Step 1: Core models with no dependencies
	if err := TestDB.AutoMigrate(
		&models.User{},
		&models.Organization{},
		&models.Branch{},
	); err != nil {
		return err
	}

	// Step 2: Shop-related models
	if err := TestDB.AutoMigrate(
		&models.Shop{},
		&models.MerchantShop{},
		&models.ShopBranch{},
	); err != nil {
		return err
	}

	// Step 3: Models that depend on Shop and User
	if err := TestDB.AutoMigrate(
		&models.ShopUser{},
		&models.ShopSubscription{},
		&models.APIKey{},
	); err != nil {
		return err
	}

	// Step 4: Transaction and usage models
	if err := TestDB.AutoMigrate(
		&models.ShopTransaction{},
		&models.ShopMonthlyUsageTracking{},
		&models.Usage{},
		&models.Transaction{},
	); err != nil {
		return err
	}

	// Step 5: Other models
	if err := TestDB.AutoMigrate(
		&models.Webhook{},
		&models.WebhookDelivery{},
		&models.AnalyticsData{},
		&models.MonthlyUsageTracking{},
	); err != nil {
		return err
	}

	// Step 6: Customer and credit models (if they exist)
	// Note: Only migrate if these models exist in the codebase
	migratableModels := []interface{}{}
	
	// Add models that exist to avoid "relation does not exist" errors
	if err := TestDB.AutoMigrate(migratableModels...); err != nil {
		return err
	}

	return nil
}

// setupTestRouter initializes the test Gin router
func setupTestRouter() {
	TestRouter = gin.New()
	// Add middleware and routes here
	// This will be populated when we create the route tests
}

// CleanupTestEnvironment cleans up after tests
func CleanupTestEnvironment() {
	if TestDB != nil {
		// Clean up test data
		CleanTestData()

		// Close database connection
		sqlDB, err := TestDB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
}

// CleanTestData removes all test data from the database
func CleanTestData() {
	if TestDB == nil {
		return
	}

	// Delete in reverse order of dependencies
	TestDB.Exec("DELETE FROM webhook_deliveries")
	TestDB.Exec("DELETE FROM webhooks")
	TestDB.Exec("DELETE FROM analytics_data")
	TestDB.Exec("DELETE FROM shop_credit_transactions")
	TestDB.Exec("DELETE FROM credit_codes")
	TestDB.Exec("DELETE FROM shop_customers")
	TestDB.Exec("DELETE FROM shop_branches")
	TestDB.Exec("DELETE FROM shops")
	TestDB.Exec("DELETE FROM merchant_shops")
	TestDB.Exec("DELETE FROM usage")
	TestDB.Exec("DELETE FROM transactions")
	TestDB.Exec("DELETE FROM api_keys")
	TestDB.Exec("DELETE FROM subscriptions")
	TestDB.Exec("DELETE FROM subscription_tiers")
	TestDB.Exec("DELETE FROM branches")
	TestDB.Exec("DELETE FROM organizations")
	TestDB.Exec("DELETE FROM users")
}

// TestMain sets up and tears down the test environment
func TestMain(m *testing.M) {
	// Setup
	if err := SetupTestEnvironment(); err != nil {
		logrus.Fatalf("Failed to setup test environment: %v", err)
	}

	// Run tests
	code := m.Run()

	// Cleanup
	CleanupTestEnvironment()

	// Exit
	os.Exit(code)
}

// CreateTestUser creates a test user for testing
func CreateTestUser(email, name string) (*models.User, error) {
	userID := uuid.New()
	user := &models.User{
		ID:       userID,
		Email:    email,
		Name:     name,
		Role:     "user",
		GoogleID: "test_google_" + userID.String(), // Use unique GoogleID for each test user
	}

	if err := TestDB.Create(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// CreateTestAPIKey creates a test API key for testing
func CreateTestAPIKey(userID uuid.UUID, name string) (*models.APIKey, error) {
	apiKey := &models.APIKey{
		ID:     uuid.New(),
		UserID: userID,
		Name:   name,
		Key:    "test_" + uuid.New().String(),
	}

	if err := TestDB.Create(apiKey).Error; err != nil {
		return nil, err
	}

	return apiKey, nil
}

// CreateTestSubscriptionTier creates a test subscription tier
// DEPRECATED: Subscription tiers are now managed by ADC Subscription Service
func CreateTestSubscriptionTier(name string, creditLimit int) (interface{}, error) {
	// No longer needed - subscription tiers are managed by ADC Subscription Service
	return nil, nil

	/* LEGACY CODE - COMMENTED OUT
	tier := &models.SubscriptionTier{
		Name:        name,
		Description: "Test tier",
		Price:       9.99,
		CreditLimit: creditLimit,
		Features:    models.StringSlice{"feature1", "feature2"},
	}

	if err := TestDB.Create(tier).Error; err != nil {
		return nil, err
	}

	return tier, nil
	*/
}

// CreateTestSubscriptionForUser creates a test shop subscription for a user (replacing legacy Subscription)
func CreateTestSubscriptionForUser(userID uuid.UUID) (*models.ShopSubscription, error) {
	// First create a shop for the user
	shop := &models.Shop{
		ID:           uuid.New(),
		Name:         "Test Shop",
		Description:  "Test shop for subscription",
		OwnerUserID:  userID,
		ShopType:     "api_service",
	}

	if err := TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	// Create shop subscription using existing helper
	shopSubscription := CreateTestShopSubscription(TestDB, shop.ID)

	return shopSubscription, nil
}

// CreateTestOrganization creates a test organization
func CreateTestOrganization(name, slug string, ownerID uuid.UUID) (*models.Organization, error) {
	org := &models.Organization{
		ID:          uuid.New(),
		Name:        name,
		Slug:        slug,
		Description: "Test organization",
		OwnerUserID: ownerID,
	}

	if err := TestDB.Create(org).Error; err != nil {
		return nil, err
	}

	return org, nil
}

// CreateTestMerchantShop creates a test merchant shop
func CreateTestMerchantShop(name, slug string, ownerID uuid.UUID) (*models.MerchantShop, error) {
	shop := &models.MerchantShop{
		ID:           uuid.New(),
		Name:         name,
		Slug:         slug,
		Description:  "Test shop",
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  ownerID,
	}

	if err := TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	return shop, nil
}

// CreateTestShop creates a test unified shop
func CreateTestShop(name, slug string, ownerUserID uuid.UUID, shopType string) (*models.Shop, error) {
	shop := &models.Shop{
		ID:           uuid.New(),
		Name:         name,
		Slug:         slug,
		Description:  "Test shop description",
		ShopType:     shopType,
		ContactEmail: "<EMAIL>",
		ContactPhone: "+1234567890",
		OwnerUserID:  ownerUserID,
	}

	if err := TestDB.Create(shop).Error; err != nil {
		return nil, err
	}

	return shop, nil
}

// SetupTestDB initializes the test database (alias for SetupTestEnvironment)
func SetupTestDB() error {
	return SetupTestEnvironment()
}

// CleanupTestDB cleans up the test database (alias for CleanupTestEnvironment)
func CleanupTestDB() {
	CleanupTestEnvironment()
}

// CleanupAllTestData removes all test data (alias for CleanTestData)
func CleanupAllTestData() {
	CleanTestData()
}
