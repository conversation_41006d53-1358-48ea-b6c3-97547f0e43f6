package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// AutoTranslationJob represents a translation job to be processed
type AutoTranslationJob struct {
	ID                  uuid.UUID `json:"id"`
	ShopID              uuid.UUID `json:"shop_id"`
	UserID              *uuid.UUID `json:"user_id,omitempty"`
	TranslationKey      string    `json:"translation_key"`
	SourceText          string    `json:"source_text"`
	TargetLanguage      string    `json:"target_language"`
	ConfidenceThreshold float64   `json:"confidence_threshold"`
	Priority            int       `json:"priority"` // 1 = high, 2 = medium, 3 = low
	CreatedAt           time.Time `json:"created_at"`
	Retries             int       `json:"retries"`
	MaxRetries          int       `json:"max_retries"`
	Status              string    `json:"status"` // pending, processing, completed, failed
}

// AutoTranslationResult represents the result of a translation job
type AutoTranslationResult struct {
	JobID              uuid.UUID `json:"job_id"`
	TranslatedText     string    `json:"translated_text"`
	ConfidenceScore    float64   `json:"confidence_score"`
	Success            bool      `json:"success"`
	ErrorMessage       string    `json:"error_message,omitempty"`
	ProcessingDuration time.Duration `json:"processing_duration"`
	ProcessedAt        time.Time `json:"processed_at"`
}

// AutoTranslationProcessor handles background auto-translation processing
type AutoTranslationProcessor struct {
	settingsService    *SettingsService
	multiLangClient    *MultiLangClient    // Client for Multi-Languages service
	jobQueue           chan *AutoTranslationJob
	resultChannel      chan *AutoTranslationResult
	workers            int
	isRunning          bool
	mutex              sync.RWMutex
	ctx                context.Context
	cancel             context.CancelFunc
}

// NewAutoTranslationProcessor creates a new auto-translation processor
func NewAutoTranslationProcessor(settingsService *SettingsService, multiLangClient *MultiLangClient, workers int) *AutoTranslationProcessor {
	if workers <= 0 {
		workers = 3 // Default number of workers
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &AutoTranslationProcessor{
		settingsService:    settingsService,
		multiLangClient:    multiLangClient,
		jobQueue:           make(chan *AutoTranslationJob, 1000), // Buffer for 1000 jobs
		resultChannel:      make(chan *AutoTranslationResult, 1000),
		workers:            workers,
		isRunning:          false,
		ctx:                ctx,
		cancel:             cancel,
	}
}

// Start starts the auto-translation processor
func (p *AutoTranslationProcessor) Start() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.isRunning {
		return fmt.Errorf("processor is already running")
	}

	p.isRunning = true
	log.Printf("Starting auto-translation processor with %d workers", p.workers)

	// Start worker goroutines
	for i := 0; i < p.workers; i++ {
		go p.worker(i)
	}

	// Start result handler
	go p.resultHandler()

	// Start job scheduler (for periodic auto-translation)
	go p.jobScheduler()

	log.Printf("Auto-translation processor started successfully")
	return nil
}

// Stop stops the auto-translation processor
func (p *AutoTranslationProcessor) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isRunning {
		return fmt.Errorf("processor is not running")
	}

	log.Printf("Stopping auto-translation processor...")
	p.cancel()
	p.isRunning = false

	log.Printf("Auto-translation processor stopped")
	return nil
}

// SubmitJob submits a new translation job
func (p *AutoTranslationProcessor) SubmitJob(job *AutoTranslationJob) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if !p.isRunning {
		return fmt.Errorf("processor is not running")
	}

	// Validate job
	if job.ID == uuid.Nil {
		job.ID = uuid.New()
	}
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now()
	}
	if job.MaxRetries == 0 {
		job.MaxRetries = 3
	}
	if job.Status == "" {
		job.Status = "pending"
	}

	select {
	case p.jobQueue <- job:
		log.Printf("Auto-translation job submitted: %s", job.ID)
		return nil
	case <-p.ctx.Done():
		return fmt.Errorf("processor is shutting down")
	default:
		return fmt.Errorf("job queue is full")
	}
}

// SubmitShopTranslationJob creates and submits a translation job for a shop
func (p *AutoTranslationProcessor) SubmitShopTranslationJob(shopID uuid.UUID, translationKey, sourceText, targetLanguage string) error {
	// Get shop auto-translation settings
	settings, err := p.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		return fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		return fmt.Errorf("auto-translation is not enabled for shop %s", shopID)
	}

	// Get confidence threshold
	confidenceThreshold := 0.8 // default
	if threshold, ok := settings["confidence_threshold"].(float64); ok {
		confidenceThreshold = threshold
	}

	// Determine priority based on settings
	priority := 2 // medium priority by default
	if realTimeMode, ok := settings["real_time_mode"].(bool); ok && realTimeMode {
		priority = 1 // high priority for real-time mode
	}

	job := &AutoTranslationJob{
		ID:                  uuid.New(),
		ShopID:              shopID,
		TranslationKey:      translationKey,
		SourceText:          sourceText,
		TargetLanguage:      targetLanguage,
		ConfidenceThreshold: confidenceThreshold,
		Priority:            priority,
		CreatedAt:           time.Now(),
		MaxRetries:          3,
		Status:              "pending",
	}

	return p.SubmitJob(job)
}

// worker processes translation jobs
func (p *AutoTranslationProcessor) worker(workerID int) {
	log.Printf("Auto-translation worker %d started", workerID)

	for {
		select {
		case job := <-p.jobQueue:
			startTime := time.Now()
			result := p.processJob(job, workerID)
			result.ProcessingDuration = time.Since(startTime)
			result.ProcessedAt = time.Now()

			select {
			case p.resultChannel <- result:
				// Result sent successfully
			case <-p.ctx.Done():
				return
			}

		case <-p.ctx.Done():
			log.Printf("Auto-translation worker %d stopping", workerID)
			return
		}
	}
}

// processJob processes a single translation job
func (p *AutoTranslationProcessor) processJob(job *AutoTranslationJob, workerID int) *AutoTranslationResult {
	log.Printf("Worker %d processing job %s for shop %s", workerID, job.ID, job.ShopID)

	result := &AutoTranslationResult{
		JobID:   job.ID,
		Success: false,
	}

	// Update job status
	job.Status = "processing"

	// Call Multi-Languages service for real AI translation
	translatedText, confidenceScore, err := p.performRealTranslation(job)
	if err != nil {
		result.ErrorMessage = err.Error()
		job.Retries++
		
		// Retry logic
		if job.Retries < job.MaxRetries {
			log.Printf("Job %s failed, retrying (%d/%d): %v", job.ID, job.Retries, job.MaxRetries, err)
			job.Status = "pending"
			// Re-queue the job with delay
			go func() {
				time.Sleep(time.Duration(job.Retries) * 30 * time.Second) // Exponential backoff
				p.SubmitJob(job)
			}()
		} else {
			log.Printf("Job %s failed permanently after %d retries: %v", job.ID, job.Retries, err)
			job.Status = "failed"
		}
		return result
	}

	// Check confidence threshold
	if confidenceScore < job.ConfidenceThreshold {
		result.ErrorMessage = fmt.Sprintf("confidence score %.2f below threshold %.2f", confidenceScore, job.ConfidenceThreshold)
		job.Status = "failed"
		log.Printf("Job %s failed confidence check: score %.2f, threshold %.2f", job.ID, confidenceScore, job.ConfidenceThreshold)
		return result
	}

	// Success
	result.Success = true
	result.TranslatedText = translatedText
	result.ConfidenceScore = confidenceScore
	job.Status = "completed"

	log.Printf("Job %s completed successfully with confidence %.2f", job.ID, confidenceScore)
	return result
}

// performRealTranslation performs real AI translation via Multi-Languages service
func (p *AutoTranslationProcessor) performRealTranslation(job *AutoTranslationJob) (string, float64, error) {
	if p.multiLangClient == nil {
		return "", 0, fmt.Errorf("Multi-Languages client not configured")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Generate namespace based on shop ID and translation type
	namespace := fmt.Sprintf("shop_%s", job.ShopID.String()[:8])
	
	// Use translation key as the key for the translation
	translationKey := job.TranslationKey
	if translationKey == "" {
		translationKey = fmt.Sprintf("auto_key_%s", job.ID.String()[:8])
	}

	// Call Multi-Languages service to translate text
	// We'll assume English as source and use the target language from job
	response, err := p.multiLangClient.TranslateText(
		ctx,
		job.SourceText,
		"en",  // Source locale (English)
		job.TargetLanguage,
		namespace,
		translationKey,
	)

	if err != nil {
		return "", 0, fmt.Errorf("Multi-Languages service error: %w", err)
	}

	if !response.Success {
		return "", 0, fmt.Errorf("translation failed: %s", response.Message)
	}

	// Extract translated text
	translatedText := response.Data.Value
	if translatedText == "" {
		return "", 0, fmt.Errorf("translation returned empty result")
	}

	// Calculate confidence score based on auto-translate status and other factors
	confidenceScore := 0.8 // Default confidence
	
	if response.Data.AutoTranslated {
		// AI-translated content gets higher confidence if it's actually translated
		if translatedText != job.SourceText {
			confidenceScore = 0.9
		} else {
			// If AI didn't actually translate (same as source), lower confidence
			confidenceScore = 0.6
		}
	} else {
		// Manual translation gets highest confidence
		confidenceScore = 0.95
	}

	// Adjust confidence based on text length (longer texts might have lower confidence)
	if len(job.SourceText) > 100 {
		confidenceScore *= 0.95
	}
	if len(job.SourceText) > 300 {
		confidenceScore *= 0.9
	}

	log.Printf("Translation completed via Multi-Languages service: key=%s, auto_translated=%v, confidence=%.2f", 
		translationKey, response.Data.AutoTranslated, confidenceScore)

	return translatedText, confidenceScore, nil
}

// resultHandler handles translation results
func (p *AutoTranslationProcessor) resultHandler() {
	log.Printf("Auto-translation result handler started")

	for {
		select {
		case result := <-p.resultChannel:
			p.handleResult(result)
		case <-p.ctx.Done():
			log.Printf("Auto-translation result handler stopping")
			return
		}
	}
}

// handleResult processes a translation result
func (p *AutoTranslationProcessor) handleResult(result *AutoTranslationResult) {
	if result.Success {
		log.Printf("Translation completed successfully: job %s, confidence %.2f, duration %v",
			result.JobID, result.ConfidenceScore, result.ProcessingDuration)
		
		// In a real implementation, this would:
		// 1. Save the translation to the Multi-Languages service
		// 2. Update the shop's translation cache
		// 3. Notify relevant services
		// 4. Update analytics
		
	} else {
		log.Printf("Translation failed: job %s, error: %s, duration %v",
			result.JobID, result.ErrorMessage, result.ProcessingDuration)
		
		// In a real implementation, this would:
		// 1. Log the failure for analysis
		// 2. Update error metrics
		// 3. Possibly notify administrators for persistent failures
	}
}

// jobScheduler periodically checks for shops that need auto-translation
func (p *AutoTranslationProcessor) jobScheduler() {
	log.Printf("Auto-translation job scheduler started")
	
	ticker := time.NewTicker(5 * time.Minute) // Check every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.scheduleAutoTranslationJobs()
		case <-p.ctx.Done():
			log.Printf("Auto-translation job scheduler stopping")
			return
		}
	}
}

// scheduleAutoTranslationJobs finds shops that need auto-translation and schedules jobs
func (p *AutoTranslationProcessor) scheduleAutoTranslationJobs() {
	log.Printf("Checking for shops that need auto-translation...")

	// In a real implementation, this would:
	// 1. Query the database for shops with auto-translation enabled
	// 2. Check for untranslated content
	// 3. Create translation jobs based on priority and settings
	// 4. Handle batch processing vs real-time mode

	// For now, just log that the scheduler is running
	log.Printf("Auto-translation scheduler check completed")
}

// GetStatus returns the current status of the processor
func (p *AutoTranslationProcessor) GetStatus() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return map[string]interface{}{
		"running":          p.isRunning,
		"workers":          p.workers,
		"queue_length":     len(p.jobQueue),
		"queue_capacity":   cap(p.jobQueue),
		"result_queue_length": len(p.resultChannel),
	}
}

// GetJobStats returns statistics about processed jobs
func (p *AutoTranslationProcessor) GetJobStats() map[string]interface{} {
	// In a real implementation, this would track actual statistics
	return map[string]interface{}{
		"total_jobs_processed":     0,
		"successful_translations":  0,
		"failed_translations":      0,
		"average_confidence_score": 0.0,
		"average_processing_time":  "0ms",
	}
}