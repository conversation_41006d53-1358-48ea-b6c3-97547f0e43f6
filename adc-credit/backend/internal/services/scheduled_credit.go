package services

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ScheduledCreditService handles scheduled credit operations
type ScheduledCreditService struct {
	db             *gorm.DB
	webhookService *WebhookService
}

// NewScheduledCreditService creates a new scheduled credit service
func NewScheduledCreditService() *ScheduledCreditService {
	return &ScheduledCreditService{
		db:             database.DB,
		webhookService: NewWebhookService(),
	}
}

// SetDB sets the database connection for the service
func (s *ScheduledCreditService) SetDB(db *gorm.DB) {
	s.db = db
}

// ProcessScheduledCredits processes all active subscriptions and adds scheduled credits
// This is the main function that will be called by the Cloud Scheduler
func (s *ScheduledCreditService) ProcessScheduledCredits() (int, error) {
	// Get all active shop subscriptions
	var shopSubscriptions []models.ShopSubscription
	if err := s.db.Where("status = ?", "active").
		Preload("SubscriptionTier").
		Preload("Shop").
		Find(&shopSubscriptions).Error; err != nil {
		return 0, fmt.Errorf("failed to fetch active shop subscriptions: %w", err)
	}

	logrus.Infof("Found %d active shop subscriptions to process", len(shopSubscriptions))

	// Process each shop subscription
	processedCount := 0
	for _, subscription := range shopSubscriptions {
		// Check if it's time to add credits
		if s.shouldAddCreditsToShop(subscription) {
			if err := s.AddScheduledCreditsToShopSubscription(subscription); err != nil {
				logrus.Infof("Error adding scheduled credits to shop subscription %s: %v", subscription.ID, err)
				continue
			}
			processedCount++
		}
	}

	return processedCount, nil
}

// shouldAddCreditsToShop determines if a shop subscription should receive scheduled credits
func (s *ScheduledCreditService) shouldAddCreditsToShop(subscription models.ShopSubscription) bool {
	// Get the last scheduled credit transaction for this shop subscription
	var lastTransaction models.ShopTransaction
	result := s.db.Where("shop_subscription_id = ? AND type = ?", subscription.ID, "credit_scheduled").
		Order("created_at DESC").
		First(&lastTransaction)

	// If no previous scheduled transaction, check if subscription is at least 1 day old
	if result.Error == gorm.ErrRecordNotFound {
		// For new subscriptions, only add credits if they're at least 1 day old
		// This prevents double-crediting on the first day
		return time.Since(subscription.StartDate) >= 24*time.Hour
	}

	// If there was an error other than "not found"
	if result.Error != nil {
		logrus.Infof("Error checking last transaction for shop subscription %s: %v", subscription.ID, result.Error)
		return false
	}

	// Check if it's been a month since the last scheduled credit
	// For demo purposes, we're using a shorter interval (1 day)
	// In production, you'd use 30 days or calculate based on subscription renewal date
	return time.Since(lastTransaction.CreatedAt) >= 24*time.Hour
}

// AddScheduledCreditsToShopSubscription adds the scheduled credits to a shop subscription
func (s *ScheduledCreditService) AddScheduledCreditsToShopSubscription(subscription models.ShopSubscription) error {
	// Start a transaction
	tx := s.db.Begin()

	// Get the credit amount from the subscription tier
	creditAmount := subscription.SubscriptionTier.CreditLimit

	// Update credit balance
	subscription.CreditBalance += creditAmount
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update shop subscription credit balance: %w", err)
	}

	// Create shop transaction record
	transaction := models.ShopTransaction{
		ID:                   uuid.New(),
		ShopID:               subscription.ShopID,
		ShopSubscriptionID:   subscription.ID,
		InitiatedByUserID:    subscription.Shop.OwnerUserID, // Use shop owner as initiator
		Type:                 "credit_scheduled",
		Amount:               creditAmount,
		Description:          fmt.Sprintf("Scheduled credit addition for %s subscription", subscription.SubscriptionTier.Name),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create shop transaction record: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Send webhook notification to shop owner
	go func() {
		webhookData := map[string]interface{}{
			"shop_id":                subscription.ShopID.String(),
			"shop_name":              subscription.Shop.Name,
			"owner_user_id":          subscription.Shop.OwnerUserID.String(),
			"credits":                creditAmount,
			"credit_balance":         subscription.CreditBalance,
			"transaction_id":         transaction.ID.String(),
			"shop_subscription_id":   subscription.ID.String(),
			"description":            transaction.Description,
			"scheduled":              true,
		}
		s.webhookService.SendWebhook(subscription.Shop.OwnerUserID, "credit.scheduled", webhookData)
	}()

	logrus.Infof("Added %d scheduled credits to shop subscription %s (shop: %s)", creditAmount, subscription.ID, subscription.ShopID)
	return nil
}

// GetNextScheduledCreditDate returns the date when the next scheduled credit will be added for a shop subscription
func (s *ScheduledCreditService) GetNextScheduledCreditDate(shopSubscriptionID uuid.UUID) (*time.Time, error) {
	// Validate shop subscription ID
	if shopSubscriptionID == uuid.Nil {
		return nil, nil
	}

	// Get the shop subscription
	var subscription models.ShopSubscription
	if err := s.db.Where("id = ?", shopSubscriptionID).First(&subscription).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return nil instead of error if subscription not found
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch shop subscription: %w", err)
	}

	// Get the last scheduled credit transaction
	var lastTransaction models.ShopTransaction
	result := s.db.Where("shop_subscription_id = ? AND type = ?", shopSubscriptionID, "credit_scheduled").
		Order("created_at DESC").
		First(&lastTransaction)

	var nextDate time.Time
	if result.Error == gorm.ErrRecordNotFound {
		// If no previous scheduled transaction, base it on subscription start date
		if subscription.StartDate.IsZero() {
			// If start date is not set, use current time
			nextDate = time.Now().Add(24 * time.Hour)
		} else {
			nextDate = subscription.StartDate.Add(24 * time.Hour)
		}
	} else if result.Error != nil {
		return nil, fmt.Errorf("failed to fetch last transaction: %w", result.Error)
	} else {
		// Base it on the last transaction date
		nextDate = lastTransaction.CreatedAt.Add(24 * time.Hour)
	}

	// If the next date is in the past, set it to tomorrow
	if nextDate.Before(time.Now()) {
		tomorrow := time.Now().Add(24 * time.Hour)
		nextDate = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	}

	return &nextDate, nil
}
