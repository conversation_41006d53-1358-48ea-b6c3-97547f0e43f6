package services

import (
	"fmt"
	"net/smtp"
	"os"
	
	"github.com/adc-credit/backend/internal/utils"
	"github.com/sirupsen/logrus"
)

// EmailService handles sending emails
type EmailService struct {
	SMTPHost     string
	SMTPPort     string
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
	FromName     string
}

// NewEmailService creates a new email service instance
func NewEmailService() *EmailService {
	return &EmailService{
		SMTPHost:     utils.GetEnvOrDefault("SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:     utils.GetEnvOrDefault("SMTP_PORT", "587"),
		SMTPUsername: os.Getenv("SMTP_USERNAME"),
		SMTPPassword: os.Getenv("SMTP_PASSWORD"),
		FromEmail:    utils.GetEnvOrDefault("FROM_EMAIL", os.<PERSON>env("SMTP_USERNAME")),
		FromName:     utils.GetEnvOrDefault("FROM_NAME", "ADC Credit System"),
	}
}

// SendPasswordResetEmail sends a password reset email
func (e *EmailService) SendPasswordResetEmail(toEmail, resetToken string) error {
	// Skip sending email if SMTP is not configured
	if e.SMTPUsername == "" || e.SMTPPassword == "" {
		logrus.WithFields(logrus.Fields{
			"recipient": toEmail,
			"token":     resetToken,
		}).Info("SMTP not configured, simulating password reset email")
		return nil
	}

	frontendURL := utils.GetEnvOrDefault("FRONTEND_URL", "http://localhost:3000")
	resetLink := fmt.Sprintf("%s/auth/reset-password?token=%s", frontendURL, resetToken)

	subject := "Password Reset Request"
	body := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Password Reset Request</h2>
        
        <p>Hello,</p>
        
        <p>You have requested to reset your password for your ADC Credit System account.</p>
        
        <p>Please click the button below to reset your password:</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="%s" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #3498db;">%s</p>
        
        <p><strong>This link will expire in 1 hour for security reasons.</strong></p>
        
        <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="font-size: 12px; color: #666;">
            This email was sent by ADC Credit System. If you have any questions, please contact our support team.
        </p>
    </div>
</body>
</html>
`, resetLink, resetLink)

	return e.sendEmail(toEmail, subject, body)
}

// SendWelcomeEmail sends a welcome email to new users
func (e *EmailService) SendWelcomeEmail(toEmail, userName string) error {
	// Skip sending email if SMTP is not configured
	if e.SMTPUsername == "" || e.SMTPPassword == "" {
		logrus.WithField("recipient", toEmail).Info("SMTP not configured, simulating welcome email")
		return nil
	}

	subject := "Welcome to ADC Credit System"
	body := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome to ADC Credit System</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Welcome to ADC Credit System!</h2>
        
        <p>Hello %s,</p>
        
        <p>Welcome to ADC Credit System! Your account has been successfully created.</p>
        
        <p>You can now:</p>
        <ul>
            <li>Create and manage shops</li>
            <li>Generate credit codes and QR codes</li>
            <li>Track customer transactions</li>
            <li>Access analytics and reports</li>
        </ul>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="%s" style="background-color: #27ae60; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Get Started</a>
        </div>
        
        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="font-size: 12px; color: #666;">
            This email was sent by ADC Credit System. If you have any questions, please contact our support team.
        </p>
    </div>
</body>
</html>
`, userName, utils.GetEnvOrDefault("FRONTEND_URL", "http://localhost:3000"))

	return e.sendEmail(toEmail, subject, body)
}

// sendEmail sends an email using SMTP
func (e *EmailService) sendEmail(to, subject, body string) error {
	// Create message
	message := fmt.Sprintf("From: %s <%s>\r\n", e.FromName, e.FromEmail)
	message += fmt.Sprintf("To: %s\r\n", to)
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += "MIME-Version: 1.0\r\n"
	message += "Content-Type: text/html; charset=UTF-8\r\n"
	message += "\r\n"
	message += body

	// Set up authentication
	auth := smtp.PlainAuth("", e.SMTPUsername, e.SMTPPassword, e.SMTPHost)

	// Send email
	addr := fmt.Sprintf("%s:%s", e.SMTPHost, e.SMTPPort)
	err := smtp.SendMail(addr, auth, e.FromEmail, []string{to}, []byte(message))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}


// IsEmailConfigured checks if email service is properly configured
func (e *EmailService) IsEmailConfigured() bool {
	return e.SMTPUsername != "" && e.SMTPPassword != ""
}

// SendCreditCodeEmail sends an email with credit code information
func (e *EmailService) SendCreditCodeEmail(toEmail, shopName, creditCode string, amount int) error {
	// Skip sending email if SMTP is not configured
	if e.SMTPUsername == "" || e.SMTPPassword == "" {
		logrus.WithField("recipient", toEmail).Info("SMTP not configured, simulating credit code email")
		return nil
	}

	subject := fmt.Sprintf("Credit Code from %s", shopName)
	body := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Credit Code</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">You've Received a Credit Code!</h2>
        
        <p>Hello,</p>
        
        <p>You have received a credit code from <strong>%s</strong>.</p>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;">
            <h3 style="margin: 0; color: #27ae60;">Credit Code</h3>
            <p style="font-size: 24px; font-weight: bold; margin: 10px 0; letter-spacing: 2px; color: #2c3e50;">%s</p>
            <p style="margin: 0; color: #666;">Amount: %d credits</p>
        </div>
        
        <p>To redeem this credit code:</p>
        <ol>
            <li>Open the ADC Credit System app</li>
            <li>Go to the "Scan" or "Redeem Code" section</li>
            <li>Enter the code above</li>
        </ol>
        
        <p><strong>Note:</strong> This credit code can only be used once and may have an expiration date.</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="font-size: 12px; color: #666;">
            This email was sent by ADC Credit System on behalf of %s.
        </p>
    </div>
</body>
</html>
`, shopName, creditCode, amount, shopName)

	return e.sendEmail(toEmail, subject, body)
}
