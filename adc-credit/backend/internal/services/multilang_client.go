package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// MultiLangClient handles communication with the ADC Multi-Languages service
type MultiLangClient struct {
	baseURL      string
	internalKey  string
	httpClient   *http.Client
	projectID    string // ADC Credit service project ID in Multi-Languages service
	organization string // Organization identifier
}

// NewMultiLangClient creates a new Multi-Languages service client
func NewMultiLangClient(baseURL, internalKey, projectID, organization string) *MultiLangClient {
	return &MultiLangClient{
		baseURL:      baseURL,
		internalKey:  internalKey,
		projectID:    projectID,
		organization: organization,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TranslationRequest represents a request for translation
type TranslationRequest struct {
	ProjectID    string `json:"project_id"`
	Namespace    string `json:"namespace"`
	Key          string `json:"key"`
	Locale       string `json:"locale"`
	DefaultValue string `json:"default_value"`
}

// TranslationResponse represents a response from the translation service
type TranslationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Key               string `json:"key"`
		Namespace         string `json:"namespace"`
		Locale            string `json:"locale"`
		Value             string `json:"value"`
		AutoTranslated    bool   `json:"auto_translated"`
		TranslationKeyID  string `json:"translation_key_id"`
		TranslationID     string `json:"translation_id"`
		CreatedNew        bool   `json:"created_new"`
	} `json:"data"`
	Meta struct {
		Timestamp string `json:"timestamp"`
		RequestID string `json:"request_id"`
		Version   string `json:"version"`
	} `json:"meta"`
}

// GetOrCreateTranslation gets an existing translation or creates/translates a new one
func (c *MultiLangClient) GetOrCreateTranslation(ctx context.Context, namespace, key, locale, defaultValue string) (*TranslationResponse, error) {
	request := TranslationRequest{
		ProjectID:    c.projectID,
		Namespace:    namespace,
		Key:          key,
		Locale:       locale,
		DefaultValue: defaultValue,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url := fmt.Sprintf("%s/api/v2/internal/translations/get-or-create", c.baseURL)
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", c.internalKey)
	req.Header.Set("X-Service-Name", "adc-credit")
	req.Header.Set("X-Organization", c.organization)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("translation service error (status %d): %s", resp.StatusCode, string(body))
	}

	var response TranslationResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}

// TranslateText performs direct translation via Multi-Languages service
func (c *MultiLangClient) TranslateText(ctx context.Context, sourceText, sourceLocale, targetLocale string, namespace, key string) (*TranslationResponse, error) {
	// First, ensure we have a source translation
	if sourceLocale != "" {
		_, err := c.GetOrCreateTranslation(ctx, namespace, key, sourceLocale, sourceText)
		if err != nil {
			return nil, fmt.Errorf("failed to create source translation: %w", err)
		}
	}

	// Now request the target translation (should trigger auto-translate)
	return c.GetOrCreateTranslation(ctx, namespace, key, targetLocale, "")
}

// HealthCheck checks if the Multi-Languages service is available
func (c *MultiLangClient) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", c.baseURL)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Multi-Languages service unhealthy (status %d)", resp.StatusCode)
	}

	return nil
}

// BatchTranslate translates multiple texts in a single operation
func (c *MultiLangClient) BatchTranslate(ctx context.Context, translations []BatchTranslationItem) ([]*TranslationResponse, error) {
	results := make([]*TranslationResponse, len(translations))
	errors := make([]error, len(translations))

	// Process translations sequentially to avoid overwhelming the service
	for i, item := range translations {
		result, err := c.TranslateText(ctx, item.SourceText, item.SourceLocale, item.TargetLocale, item.Namespace, item.Key)
		results[i] = result
		errors[i] = err
		
		// Add small delay between requests to be respectful
		if i < len(translations)-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	// Check if any translations failed
	var firstError error
	successCount := 0
	for i, err := range errors {
		if err != nil {
			if firstError == nil {
				firstError = fmt.Errorf("translation %d failed: %w", i, err)
			}
		} else {
			successCount++
		}
	}

	// Return results even if some failed
	return results, firstError
}

// BatchTranslationItem represents a single item in a batch translation request
type BatchTranslationItem struct {
	Key          string `json:"key"`
	Namespace    string `json:"namespace"`
	SourceText   string `json:"source_text"`
	SourceLocale string `json:"source_locale"`
	TargetLocale string `json:"target_locale"`
}

// GetTranslationsByNamespace retrieves all translations for a specific namespace
func (c *MultiLangClient) GetTranslationsByNamespace(ctx context.Context, namespace string) ([]TranslationResponse, error) {
	url := fmt.Sprintf("%s/api/v2/internal/translations/by-namespace?project_id=%s&namespace=%s", 
		c.baseURL, c.projectID, namespace)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("X-Internal-Key", c.internalKey)
	req.Header.Set("X-Service-Name", "adc-credit")
	req.Header.Set("X-Organization", c.organization)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("translation service error (status %d): %s", resp.StatusCode, string(body))
	}

	var response struct {
		Success bool                  `json:"success"`
		Data    []TranslationResponse `json:"data"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return response.Data, nil
}