package services

import (
	"github.com/sirupsen/logrus"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"gorm.io/gorm"
)

// UsageReportingService handles usage reporting to subscription service
type UsageReportingService struct {
	db                 *gorm.DB
	subscriptionClient *subscriptionSDK.Client
	analyticsClient    interface{} // Disabled for now
	shopOrgService     *ShopOrganizationService
	logger             *logrus.Logger
}

// NewUsageReportingService creates a new usage reporting service
func NewUsageReportingService(
	db *gorm.DB,
	subscriptionClient *subscriptionSDK.Client,
	analyticsClient interface{},
	shopOrgService *ShopOrganizationService,
	logger *logrus.Logger,
) *UsageReportingService {
	return &UsageReportingService{
		db:                 db,
		subscriptionClient: subscriptionClient,
		analyticsClient:    analyticsClient,
		shopOrgService:     shopOrgService,
		logger:             logger,
	}
}

// ReportUsage reports usage metrics to subscription service
func (s *UsageReportingService) ReportUsage() error {
	s.logger.Info("Reporting usage metrics")
	// TODO: Implement usage reporting logic
	return nil
}