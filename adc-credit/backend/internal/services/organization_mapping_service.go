package services

import (
	"context"
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/models"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// OrganizationMappingService handles the mapping between Credit service shops and ADC Subscription Service
// In this architecture: Shop == Organization (same level), Branch is lower level
type OrganizationMappingService struct {
	subscriptionClient *subscriptionSDK.Client
}

// NewOrganizationMappingService creates a new organization mapping service
func NewOrganizationMappingService(subscriptionClient *subscriptionSDK.Client) *OrganizationMappingService {
	return &OrganizationMappingService{
		subscriptionClient: subscriptionClient,
	}
}

// BranchMapping defines how Credit service branches map to organization sub-units
type BranchMapping struct {
	BranchID     uuid.UUID `json:"branch_id"`     // Credit service branch ID
	BranchName   string    `json:"branch_name"`   // Credit service branch name
	BranchSlug   string    `json:"branch_slug"`   // Credit service branch slug
	Location     string    `json:"location"`      // Branch location/address
	IsActive     bool      `json:"is_active"`     // Branch status
}

// ShopOrganizationMapping defines how Credit service shops map as organizations in ADC
// Architecture: Shop = Organization (same level), Branch is sub-level of Shop
type ShopOrganizationMapping struct {
	ShopID           uuid.UUID       `json:"shop_id"`            // Credit service shop ID  
	ShopSlug         string          `json:"shop_slug"`          // Credit service shop slug
	ShopName         string          `json:"shop_name"`          // Credit service shop name
	OwnerUserID      uuid.UUID       `json:"owner_user_id"`      // Shop owner in credit service
	OrganizationID   uuid.UUID       `json:"organization_id"`    // ADC organization ID (same as shop ID)
	TenantID         string          `json:"tenant_id"`          // Always "credit" for credit service
	Platform         string          `json:"platform"`           // Always "credit" platform
	SubscriptionPlan string          `json:"subscription_plan"`  // Mapped from SubscriptionTier
	Branches         []BranchMapping `json:"branches"`           // Shop branches mapped to organization sub-units
}

// MapShopToOrganization creates an organization mapping for a Credit service shop
// In this architecture: Shop ID becomes Organization ID (1:1 mapping)
func (s *OrganizationMappingService) MapShopToOrganization(shop *models.MerchantShop, user *models.User, subscriptionTier *models.SubscriptionTier) (*ShopOrganizationMapping, error) {
	// Map shop branches to organization sub-units
	branches, err := s.mapShopBranches(shop.ID)
	if err != nil {
		log.Printf("Warning: Failed to map branches for shop %s: %v", shop.ID, err)
		branches = []BranchMapping{} // Continue with empty branches
	}
	
	mapping := &ShopOrganizationMapping{
		ShopID:           shop.ID,
		ShopSlug:         shop.Slug,
		ShopName:         shop.Name,
		OwnerUserID:      shop.OwnerUserID,
		OrganizationID:   shop.ID, // Organization ID is the same as Shop ID
		TenantID:         "credit", // All credit service shops belong to the "credit" tenant
		Platform:         "credit", // Platform is always "credit"
		SubscriptionPlan: s.mapSubscriptionTierToPlan(subscriptionTier),
		Branches:         branches,
	}

	return mapping, nil
}

// mapShopBranches retrieves and maps shop branches to organization sub-units
func (s *OrganizationMappingService) mapShopBranches(shopID uuid.UUID) ([]BranchMapping, error) {
	// TODO: Implement branch retrieval from database
	// For now, return empty branches - this would query the database for shop branches
	// 
	// Example implementation:
	// var branches []models.ShopBranch
	// if err := db.Where("shop_id = ?", shopID).Find(&branches).Error; err != nil {
	//     return nil, err
	// }
	//
	// var mappings []BranchMapping
	// for _, branch := range branches {
	//     mappings = append(mappings, BranchMapping{
	//         BranchID:   branch.ID,
	//         BranchName: branch.Name,
	//         BranchSlug: branch.Slug,
	//         Location:   branch.Address,
	//         IsActive:   branch.IsActive,
	//     })
	// }
	// return mappings, nil
	
	return []BranchMapping{}, nil
}

// mapSubscriptionTierToPlan maps Credit service subscription tiers to ADC subscription plan names
func (s *OrganizationMappingService) mapSubscriptionTierToPlan(tier *models.SubscriptionTier) string {
	if tier == nil {
		return "free" // Default to free plan
	}

	// Map based on tier name or price
	switch tier.Name {
	case "Free", "free":
		return "free"
	case "Pro", "pro", "Professional":
		return "pro"
	case "Enterprise", "enterprise":
		return "enterprise"
	default:
		// Map based on price if name doesn't match
		if tier.Price == 0 {
			return "free"
		} else if tier.Price < 50 {
			return "pro"
		} else {
			return "enterprise"
		}
	}
}

// GetADCSubscriptionPlanLimits converts Credit service subscription tier limits to ADC format
func (s *OrganizationMappingService) GetADCSubscriptionPlanLimits(tier *models.SubscriptionTier) map[string]interface{} {
	if tier == nil {
		return s.getDefaultFreeLimits()
	}

	limits := map[string]interface{}{
		// Core platform limits
		"max_users":               tier.MaxShops,                  // Map shops to users concept
		"max_api_calls_per_month": tier.MaxAPIKeysPerShop * 1000, // Estimate API calls from API keys
		"max_storage_gb":          float64(10),                   // Default storage limit
		"max_bandwidth_gb":        float64(100),                  // Default bandwidth limit
		
		// Credit platform specific limits
		"credit_platform": map[string]interface{}{
			"max_shops":               tier.MaxShops,
			"max_customers_per_shop":  tier.MaxCustomersPerShop,
			"max_api_keys_per_shop":   tier.MaxAPIKeysPerShop,
			"max_branches_per_shop":   tier.MaxBranchesPerShop,
			"max_qr_codes_per_month":  tier.MaxQRCodesPerMonth,
			"max_webhooks":            tier.MaxWebhooks,
			"credit_limit":            tier.CreditLimit,
			"analytics_history_days":  tier.AnalyticsHistoryDays,
			"support_level":           tier.SupportLevel,
			"advanced_analytics":      tier.AdvancedAnalytics,
		},
	}

	return limits
}

// getDefaultFreeLimits returns the default limits for free tier
func (s *OrganizationMappingService) getDefaultFreeLimits() map[string]interface{} {
	return map[string]interface{}{
		"max_users":               5,
		"max_api_calls_per_month": 1000,
		"max_storage_gb":          float64(1),
		"max_bandwidth_gb":        float64(10),
		"credit_platform": map[string]interface{}{
			"max_shops":               1,
			"max_customers_per_shop":  100,
			"max_api_keys_per_shop":   2,
			"max_branches_per_shop":   1,
			"max_qr_codes_per_month":  50,
			"max_webhooks":            1,
			"credit_limit":            1000,
			"analytics_history_days":  30,
			"support_level":           "community",
			"advanced_analytics":      false,
		},
	}
}

// CreateOrganizationInADC creates an organization and subscription in the ADC Subscription Service
// Organization uses Shop ID directly, branches are created as sub-units
func (s *OrganizationMappingService) CreateOrganizationInADC(mapping *ShopOrganizationMapping, tier *models.SubscriptionTier) error {
	ctx := context.Background()
	
	log.Printf("Creating organization in ADC: ID=%s, Name=%s, Tenant=%s, Branches=%d", 
		mapping.OrganizationID, mapping.ShopName, mapping.TenantID, len(mapping.Branches))
	
	// First, try to get existing subscription to see if organization already exists
	existingSubscription, err := s.subscriptionClient.GetOrganizationSubscription(ctx, mapping.OrganizationID)
	if err == nil && existingSubscription != nil {
		log.Printf("Organization %s already has subscription in ADC: plan=%s, status=%s", 
			mapping.OrganizationID, existingSubscription.Plan.Name, existingSubscription.Status)
		return nil // Organization already exists with subscription
	}
	
	// Get available plans for the credit service
	plans, err := s.subscriptionClient.GetPlans(ctx, "credit")
	if err != nil {
		log.Printf("Failed to get credit service plans: %v", err)
		return fmt.Errorf("failed to get credit service plans: %w", err)
	}
	
	if len(plans) == 0 {
		return fmt.Errorf("no plans available for credit service")
	}
	
	// Find the appropriate plan based on the subscription tier
	var selectedPlan *subscriptionSDK.Plan
	planName := s.mapSubscriptionTierToPlan(tier)
	
	for _, plan := range plans {
		if plan.Name == planName || plan.ServiceScope == "credit" {
			selectedPlan = plan
			break
		}
	}
	
	// If no specific plan found, use the first available plan
	if selectedPlan == nil {
		selectedPlan = plans[0]
		log.Printf("No matching plan found for tier '%s', using default plan: %s", planName, selectedPlan.Name)
	}
	
	// Create subscription for the organization (this implicitly creates the organization)
	subRequest := &subscriptionSDK.CreateSubscriptionRequest{
		OrganizationID: mapping.OrganizationID,
		PlanID:         selectedPlan.ID,
		BillingCycle:   "monthly",
	}
	
	subscription, err := s.subscriptionClient.CreateSubscription(ctx, subRequest)
	if err != nil {
		log.Printf("Failed to create subscription for organization %s: %v", mapping.OrganizationID, err)
		return fmt.Errorf("failed to create subscription in ADC: %w", err)
	}
	
	log.Printf("Successfully created organization and subscription in ADC: ID=%s, Plan=%s, Status=%s", 
		mapping.OrganizationID, subscription.Plan.Name, subscription.Status)
	
	return nil
}

// GetSubscriptionForShop retrieves subscription information from ADC for a given shop
// Shop ID = Organization ID in the new architecture
func (s *OrganizationMappingService) GetSubscriptionForShop(shopID uuid.UUID) (*subscriptionSDK.Subscription, error) {
	// Shop ID is used directly as Organization ID in ADC
	organizationID := shopID
	
	ctx := context.Background()
	
	log.Printf("Getting subscription for organization: %s", organizationID)
	
	// Call the ADC Subscription Service to get the subscription
	subscription, err := s.subscriptionClient.GetOrganizationSubscription(ctx, organizationID)
	if err != nil {
		log.Printf("Failed to get subscription from ADC for organization %s: %v", organizationID, err)
		return nil, fmt.Errorf("failed to retrieve subscription from ADC: %w", err)
	}

	if subscription == nil {
		log.Printf("No subscription found in ADC for organization: %s", organizationID)
		return nil, fmt.Errorf("no subscription found for organization %s", organizationID)
	}

	log.Printf("Successfully retrieved subscription for organization %s: plan=%s, status=%s", 
		organizationID, subscription.Plan.Name, subscription.Status)
	
	return subscription, nil
}

// CheckSubscriptionLimits checks if an action is allowed based on ADC subscription limits
// Uses Shop ID directly as Organization ID in ADC
// If isInternalAPIKey is true, bypasses all subscription limits
func (s *OrganizationMappingService) CheckSubscriptionLimits(shopID uuid.UUID, limitType string, currentUsage int, isInternalAPIKey bool) (bool, error) {
	// Bypass all limits for internal API keys
	if isInternalAPIKey {
		log.Printf("Internal API key detected - bypassing subscription limits for organization %s, type %s", shopID, limitType)
		return true, nil
	}
	organizationID := shopID // Use shop ID directly as organization ID
	ctx := context.Background()
	
	log.Printf("Checking limit for organization %s: type=%s, usage=%d", organizationID, limitType, currentUsage)
	
	// Map Credit service limit types to ADC feature names
	featureName := s.mapLimitTypeToFeatureName(limitType)
	if featureName == "" {
		log.Printf("Unknown limit type: %s, allowing by default", limitType)
		return true, nil
	}
	
	// Check if the organization can use this feature with current usage + 1
	allowed, err := s.subscriptionClient.CanUseFeature(ctx, organizationID, "credit", featureName, int64(currentUsage+1))
	if err != nil {
		log.Printf("Failed to check limit for organization %s, feature %s: %v", organizationID, featureName, err)
		// Fail open - allow the action if we can't check the limit
		return true, nil
	}
	
	log.Printf("Limit check result for organization %s, feature %s: allowed=%t", organizationID, featureName, allowed)
	return allowed, nil
}

// mapLimitTypeToFeatureName maps Credit service limit types to ADC feature names
func (s *OrganizationMappingService) mapLimitTypeToFeatureName(limitType string) string {
	featureMap := map[string]string{
		"shops":      "max_shops",
		"customers":  "max_customers_per_shop", 
		"api_keys":   "max_api_keys_per_shop",
		"branches":   "max_branches_per_shop",
		"qr_codes":   "max_qr_codes_per_month",
		"webhooks":   "max_webhooks",
		"credits":    "credit_limit",
	}
	
	return featureMap[limitType]
}

// CheckSubscriptionLimitsFromContext is a convenience method that extracts the internal API key flag
// from the Gin context and calls CheckSubscriptionLimits
func (s *OrganizationMappingService) CheckSubscriptionLimitsFromContext(c *gin.Context, shopID uuid.UUID, limitType string, currentUsage int) (bool, error) {
	isInternal, _ := c.Get("is_internal_api_key")
	isInternalAPIKey, ok := isInternal.(bool)
	if !ok {
		isInternalAPIKey = false // Default to false if not set or wrong type
	}
	
	return s.CheckSubscriptionLimits(shopID, limitType, currentUsage, isInternalAPIKey)
}

// MigrateShopSubscriptionToADC migrates a shop's subscription to the ADC Subscription Service
// TODO: Remove this legacy migration function - commented out due to legacy Subscription dependency
/*
// Shop becomes an Organization with same ID, branches become sub-units
func (s *OrganizationMappingService) MigrateShopSubscriptionToADC(shop *models.MerchantShop, user *models.User, subscription *models.Subscription, tier *models.SubscriptionTier) error {
	// Create the organization mapping
	mapping, err := s.MapShopToOrganization(shop, user, tier)
	if err != nil {
		return fmt.Errorf("failed to create organization mapping: %w", err)
	}

	// Create organization in ADC (Shop ID = Organization ID)
	err = s.CreateOrganizationInADC(mapping, tier)
	if err != nil {
		return fmt.Errorf("failed to create organization in ADC: %w", err)
	}

	log.Printf("Successfully migrated shop %s (%s) to ADC organization %s with %d branches", 
		shop.ID, shop.Name, mapping.OrganizationID, len(mapping.Branches))

	return nil
}
*/