package services

import (
	"math"
	"sort"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AnalyticsService handles analytics data collection and aggregation
type AnalyticsService struct {
	db *gorm.DB
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService() *AnalyticsService {
	// Use a function to get the database connection to avoid nil issues
	return &AnalyticsService{
		db: database.DB,
	}
}

// GetDB returns the database connection, creating one if needed
func (s *AnalyticsService) GetDB() *gorm.DB {
	if s.db == nil {
		s.db = database.DB
	}
	return s.db
}

// RecordAPIUsage records API usage for analytics
func (s *AnalyticsService) RecordAPIUsage(usage models.Usage) {
	// Check if database connection is available
	if s.db == nil {
		logrus.Error("Database connection not available for analytics")
		return
	}

	// Get user ID from API key
	var apiKey models.APIKey
	if err := s.db.First(&apiKey, "id = ?", usage.APIKeyID).Error; err != nil {
		logrus.WithError(err).Error("Error finding API key for analytics")
		return
	}

	// Check if shop has advanced analytics enabled
	var shopSubscription models.ShopSubscription
	if apiKey.ShopID != nil {
		// API key is associated with a shop - check shop subscription
		if err := s.db.Where("shop_id = ? AND status = ?", *apiKey.ShopID, "active").
			First(&shopSubscription).Error; err != nil {
			// Shop doesn't have an active subscription, skip analytics
			return
		}
		// TODO: Check advanced analytics via ADC Subscription Service
		// For now, allow all analytics recording
	} else {
		// API key not associated with shop - skip analytics for now
		// TODO: Handle user-level API keys if needed
		return
	}

	// Continue with analytics recording only if advanced analytics is enabled
	if false { // Placeholder to prevent compilation errors
		// User doesn't have advanced analytics, skip
		return
	}

	// Record usage for analytics
	go s.processUsageForAnalytics(usage, apiKey.UserID)
}

// processUsageForAnalytics processes API usage for analytics
func (s *AnalyticsService) processUsageForAnalytics(usage models.Usage, userID uuid.UUID) {
	// Check if database connection is available
	if s.db == nil {
		logrus.Error("Database connection not available for analytics processing")
		return
	}

	// Get today's date (truncated to day)
	today := time.Now().Truncate(24 * time.Hour)

	// Check if we already have an analytics record for this day and endpoint
	var analytics models.AnalyticsData
	result := s.db.Where("user_id = ? AND date = ? AND endpoint = ? AND api_key_id = ?",
		userID, today, usage.Endpoint, usage.APIKeyID).First(&analytics)

	if result.Error != nil {
		// Create new analytics record
		analytics = models.AnalyticsData{
			ID:              uuid.New(),
			UserID:          userID,
			Date:            today,
			APIKeyID:        &usage.APIKeyID,
			Endpoint:        usage.Endpoint,
			TotalRequests:   1,
			TotalCredits:    usage.Credits,
			AvgResponseTime: usage.ResponseTime,
			ErrorRate:       0,
			P95ResponseTime: usage.ResponseTime,
			P99ResponseTime: usage.ResponseTime,
		}

		// Set error rate if request failed
		if !usage.Success {
			analytics.ErrorRate = 100.0
		}

		if err := s.db.Create(&analytics).Error; err != nil {
			logrus.WithError(err).Error("Error creating analytics record")
		}
	} else {
		// Update existing analytics record
		analytics.TotalRequests++
		analytics.TotalCredits += usage.Credits

		// Update average response time
		analytics.AvgResponseTime = (analytics.AvgResponseTime*(analytics.TotalRequests-1) + usage.ResponseTime) / analytics.TotalRequests

		// Update error rate
		if !usage.Success {
			analytics.ErrorRate = (analytics.ErrorRate*(float64(analytics.TotalRequests)-1) + 100.0) / float64(analytics.TotalRequests)
		} else {
			analytics.ErrorRate = (analytics.ErrorRate * (float64(analytics.TotalRequests) - 1)) / float64(analytics.TotalRequests)
		}

		// Update percentiles
		// For simplicity, we'll just use the max value for now
		// In a real implementation, you would store all response times and calculate true percentiles
		if usage.ResponseTime > analytics.P95ResponseTime {
			analytics.P95ResponseTime = usage.ResponseTime
		}
		if usage.ResponseTime > analytics.P99ResponseTime {
			analytics.P99ResponseTime = usage.ResponseTime
		}

		if err := s.db.Save(&analytics).Error; err != nil {
			logrus.WithError(err).Error("Error updating analytics record")
		}
	}

	// Also update the overall analytics for the day (without endpoint)
	var overallAnalytics models.AnalyticsData
	result = s.db.Where("user_id = ? AND date = ? AND endpoint = '' AND api_key_id IS NULL",
		userID, today).First(&overallAnalytics)

	if result.Error != nil {
		// Create new overall analytics record
		overallAnalytics = models.AnalyticsData{
			ID:              uuid.New(),
			UserID:          userID,
			Date:            today,
			APIKeyID:        nil,
			Endpoint:        "",
			TotalRequests:   1,
			TotalCredits:    usage.Credits,
			AvgResponseTime: usage.ResponseTime,
			ErrorRate:       0,
			P95ResponseTime: usage.ResponseTime,
			P99ResponseTime: usage.ResponseTime,
		}

		// Set error rate if request failed
		if !usage.Success {
			overallAnalytics.ErrorRate = 100.0
		}

		if err := s.db.Create(&overallAnalytics).Error; err != nil {
			logrus.WithError(err).Error("Error creating overall analytics record")
		}
	} else {
		// Update existing overall analytics record
		overallAnalytics.TotalRequests++
		overallAnalytics.TotalCredits += usage.Credits

		// Update average response time
		overallAnalytics.AvgResponseTime = (overallAnalytics.AvgResponseTime*(overallAnalytics.TotalRequests-1) + usage.ResponseTime) / overallAnalytics.TotalRequests

		// Update error rate
		if !usage.Success {
			overallAnalytics.ErrorRate = (overallAnalytics.ErrorRate*(float64(overallAnalytics.TotalRequests)-1) + 100.0) / float64(overallAnalytics.TotalRequests)
		} else {
			overallAnalytics.ErrorRate = (overallAnalytics.ErrorRate * (float64(overallAnalytics.TotalRequests) - 1)) / float64(overallAnalytics.TotalRequests)
		}

		// Update percentiles
		if usage.ResponseTime > overallAnalytics.P95ResponseTime {
			overallAnalytics.P95ResponseTime = usage.ResponseTime
		}
		if usage.ResponseTime > overallAnalytics.P99ResponseTime {
			overallAnalytics.P99ResponseTime = usage.ResponseTime
		}

		if err := s.db.Save(&overallAnalytics).Error; err != nil {
			logrus.WithError(err).Error("Error updating overall analytics record")
		}
	}
}

// CalculatePercentiles calculates percentiles from a slice of values
func (s *AnalyticsService) CalculatePercentiles(values []int, percentiles []float64) map[float64]int {
	if len(values) == 0 {
		return make(map[float64]int)
	}

	// Sort values
	sort.Ints(values)

	// Calculate percentiles
	result := make(map[float64]int)
	for _, p := range percentiles {
		idx := int(math.Ceil(float64(len(values))*p/100.0)) - 1
		if idx < 0 {
			idx = 0
		}
		if idx >= len(values) {
			idx = len(values) - 1
		}
		result[p] = values[idx]
	}

	return result
}

// AggregateHistoricalData aggregates historical usage data into analytics
// This can be run as a background job to process older data
func (s *AnalyticsService) AggregateHistoricalData(startDate, endDate time.Time) error {
	// Process one day at a time
	for date := startDate; date.Before(endDate); date = date.Add(24 * time.Hour) {
		nextDate := date.Add(24 * time.Hour)

		// Get all usage for this day
		var usages []models.Usage
		if err := s.db.Where("timestamp BETWEEN ? AND ?", date, nextDate).Find(&usages).Error; err != nil {
			return err
		}

		// Group usage by user and endpoint
		userEndpointUsage := make(map[uuid.UUID]map[string][]models.Usage)
		for _, usage := range usages {
			// Get API key for this usage
			var apiKey models.APIKey
			if err := s.db.First(&apiKey, "id = ?", usage.APIKeyID).Error; err != nil {
				// Skip if API key is not found
				continue
			}

			userID := apiKey.UserID
			if _, exists := userEndpointUsage[userID]; !exists {
				userEndpointUsage[userID] = make(map[string][]models.Usage)
			}

			userEndpointUsage[userID][usage.Endpoint] = append(userEndpointUsage[userID][usage.Endpoint], usage)
		}

		// Process each user's data
		for userID, endpoints := range userEndpointUsage {
			// TODO: Update analytics to check shop-based subscriptions instead of user subscriptions
			// For now, process analytics for all users during legacy cleanup
			
			// Skip advanced analytics check temporarily during legacy cleanup
			if true { // TODO: Check advanced analytics via ADC Subscription Service
				// User doesn't have advanced analytics, skip
				continue
			}

			// Process each endpoint
			for endpoint, endpointUsages := range endpoints {
				// Calculate metrics
				totalRequests := len(endpointUsages)
				totalCredits := 0
				totalResponseTime := 0
				errorCount := 0
				responseTimes := make([]int, 0, totalRequests)

				for _, usage := range endpointUsages {
					totalCredits += usage.Credits
					totalResponseTime += usage.ResponseTime
					responseTimes = append(responseTimes, usage.ResponseTime)
					if !usage.Success {
						errorCount++
					}
				}

				avgResponseTime := 0
				if totalRequests > 0 {
					avgResponseTime = totalResponseTime / totalRequests
				}

				errorRate := 0.0
				if totalRequests > 0 {
					errorRate = float64(errorCount) / float64(totalRequests) * 100.0
				}

				// Calculate percentiles
				percentiles := s.CalculatePercentiles(responseTimes, []float64{95.0, 99.0})

				// Create or update analytics record
				var analytics models.AnalyticsData
				result := s.db.Where("user_id = ? AND date = ? AND endpoint = ?",
					userID, date, endpoint).First(&analytics)

				if result.Error != nil {
					// Create new record
					analytics = models.AnalyticsData{
						ID:              uuid.New(),
						UserID:          userID,
						Date:            date,
						Endpoint:        endpoint,
						TotalRequests:   totalRequests,
						TotalCredits:    totalCredits,
						AvgResponseTime: avgResponseTime,
						ErrorRate:       errorRate,
						P95ResponseTime: percentiles[95.0],
						P99ResponseTime: percentiles[99.0],
					}

					if err := s.db.Create(&analytics).Error; err != nil {
						logrus.WithError(err).Error("Error creating historical analytics record")
					}
				} else {
					// Update existing record
					analytics.TotalRequests = totalRequests
					analytics.TotalCredits = totalCredits
					analytics.AvgResponseTime = avgResponseTime
					analytics.ErrorRate = errorRate
					analytics.P95ResponseTime = percentiles[95.0]
					analytics.P99ResponseTime = percentiles[99.0]

					if err := s.db.Save(&analytics).Error; err != nil {
						logrus.WithError(err).Error("Error updating historical analytics record")
					}
				}
			}
		}
	}

	return nil
}
