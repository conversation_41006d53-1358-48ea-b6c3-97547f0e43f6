package services

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MonthlyQuotaResetService handles monthly quota resets for subscription limits
type MonthlyQuotaResetService struct {
	db *gorm.DB
}

// NewMonthlyQuotaResetService creates a new monthly quota reset service
func NewMonthlyQuotaResetService() *MonthlyQuotaResetService {
	return &MonthlyQuotaResetService{
		db: database.DB,
	}
}

// MonthlyQuotaResetResult represents the result of a monthly quota reset operation
type MonthlyQuotaResetResult struct {
	UsersProcessed     int       `json:"users_processed"`
	RecordsCreated     int       `json:"records_created"`
	RecordsReset       int       `json:"records_reset"`
	ProcessingTime     string    `json:"processing_time"`
	ResetDate          time.Time `json:"reset_date"`
	Message            string    `json:"message"`
	Errors             []string  `json:"errors,omitempty"`
}

// ProcessMonthlyQuotaResets processes monthly quota resets for all users
func (mqrs *MonthlyQuotaResetService) ProcessMonthlyQuotaResets() (*MonthlyQuotaResetResult, error) {
	startTime := time.Now()
	now := time.Now()
	result := &MonthlyQuotaResetResult{
		ResetDate: now,
		Errors:    []string{},
	}

	logrus.Infof("Starting monthly quota reset process...")

	// Get all users who need their monthly quotas reset
	users, err := mqrs.getUsersNeedingReset(now)
	if err != nil {
		return nil, fmt.Errorf("failed to get users needing reset: %w", err)
	}

	result.UsersProcessed = len(users)
	logrus.Infof("Found %d users needing monthly quota reset", len(users))

	for _, user := range users {
		if err := mqrs.resetUserMonthlyQuota(user, now); err != nil {
			errorMsg := fmt.Sprintf("Failed to reset quota for user %s: %v", user.ID, err)
			result.Errors = append(result.Errors, errorMsg)
			logrus.Infof("ERROR: %s", errorMsg)
			continue
		}
		result.RecordsReset++
	}

	// Create monthly usage tracking records for new month
	if err := mqrs.createMonthlyUsageRecords(now); err != nil {
		errorMsg := fmt.Sprintf("Failed to create monthly usage records: %v", err)
		result.Errors = append(result.Errors, errorMsg)
		logrus.Infof("ERROR: %s", errorMsg)
	}

	result.ProcessingTime = time.Since(startTime).String()
	result.Message = fmt.Sprintf("Successfully reset monthly quotas for %d/%d users", 
		result.RecordsReset, result.UsersProcessed)

	logrus.Infof("Monthly quota reset completed: %s", result.Message)
	return result, nil
}

// getUsersNeedingReset finds all users whose monthly usage needs to be reset
func (mqrs *MonthlyQuotaResetService) getUsersNeedingReset(now time.Time) ([]models.User, error) {
	var users []models.User
	
	// Find users where monthly_usage_reset_date is null or is before the current month
	currentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	
	err := mqrs.db.Where("monthly_usage_reset_date IS NULL OR monthly_usage_reset_date < ?", currentMonth).
		Find(&users).Error
	
	return users, err
}

// resetUserMonthlyQuota resets the monthly quota for a single user
func (mqrs *MonthlyQuotaResetService) resetUserMonthlyQuota(user models.User, now time.Time) error {
	return mqrs.db.Transaction(func(tx *gorm.DB) error {
		// Update user's monthly usage reset date to next month
		nextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
		if err := tx.Model(&user).Update("monthly_usage_reset_date", nextMonth).Error; err != nil {
			return fmt.Errorf("failed to update user reset date: %w", err)
		}

		// Reset monthly usage tracking for current month
		if err := mqrs.resetMonthlyUsageTracking(tx, user.ID, now); err != nil {
			return fmt.Errorf("failed to reset monthly usage tracking: %w", err)
		}

		return nil
	})
}

// resetMonthlyUsageTracking resets or creates monthly usage tracking record
func (mqrs *MonthlyQuotaResetService) resetMonthlyUsageTracking(tx *gorm.DB, userID uuid.UUID, now time.Time) error {
	year := now.Year()
	month := int(now.Month())

	// Try to find existing record for current month
	var existingRecord models.MonthlyUsageTracking
	err := tx.Where("user_id = ? AND year = ? AND month = ?", userID, year, month).
		First(&existingRecord).Error

	if err == gorm.ErrRecordNotFound {
		// Create new record for current month
		newRecord := models.MonthlyUsageTracking{
			UserID:           userID,
			Year:             year,
			Month:            month,
			QRCodesGenerated: 0,
			APICallsMade:     0,
			CreditsConsumed:  0,
			LastResetDate:    now,
		}
		return tx.Create(&newRecord).Error
	} else if err != nil {
		return err
	}

	// Reset existing record's monthly counters
	updates := map[string]interface{}{
		"qr_codes_generated": 0,
		"api_calls_made":     0,
		"credits_consumed":   0,
		"last_reset_date":    now,
		"updated_at":         now,
	}

	return tx.Model(&existingRecord).Updates(updates).Error
}

// createMonthlyUsageRecords ensures all active users have tracking records for current month
func (mqrs *MonthlyQuotaResetService) createMonthlyUsageRecords(now time.Time) error {
	year := now.Year()
	month := int(now.Month())

	// Get all users who don't have a record for current month
	var usersWithoutRecords []models.User
	subQuery := mqrs.db.Model(&models.MonthlyUsageTracking{}).
		Select("user_id").
		Where("year = ? AND month = ?", year, month)

	err := mqrs.db.Where("id NOT IN (?)", subQuery).Find(&usersWithoutRecords).Error
	if err != nil {
		return fmt.Errorf("failed to find users without monthly records: %w", err)
	}

	// Create records for users who don't have them
	for _, user := range usersWithoutRecords {
		record := models.MonthlyUsageTracking{
			UserID:           user.ID,
			Year:             year,
			Month:            month,
			QRCodesGenerated: 0,
			APICallsMade:     0,
			CreditsConsumed:  0,
			LastResetDate:    now,
		}

		if err := mqrs.db.Create(&record).Error; err != nil {
			logrus.Infof("Failed to create monthly usage record for user %s: %v", user.ID, err)
		}
	}

	return nil
}

// GetUserMonthlyUsage gets the monthly usage for a specific user and month
func (mqrs *MonthlyQuotaResetService) GetUserMonthlyUsage(userID uuid.UUID, year, month int) (*models.MonthlyUsageTracking, error) {
	var usage models.MonthlyUsageTracking
	err := mqrs.db.Where("user_id = ? AND year = ? AND month = ?", userID, year, month).
		First(&usage).Error
	
	if err == gorm.ErrRecordNotFound {
		// Create record if it doesn't exist
		usage = models.MonthlyUsageTracking{
			UserID:           userID,
			Year:             year,
			Month:            month,
			QRCodesGenerated: 0,
			APICallsMade:     0,
			CreditsConsumed:  0,
			LastResetDate:    time.Now(),
		}
		if err := mqrs.db.Create(&usage).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}

	return &usage, nil
}

// IncrementQRCodeUsage increments QR code usage for a user
func (mqrs *MonthlyQuotaResetService) IncrementQRCodeUsage(userID uuid.UUID) error {
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	return mqrs.db.Model(&models.MonthlyUsageTracking{}).
		Where("user_id = ? AND year = ? AND month = ?", userID, year, month).
		UpdateColumn("qr_codes_generated", gorm.Expr("qr_codes_generated + 1")).Error
}

// IncrementAPICallUsage increments API call usage for a user
func (mqrs *MonthlyQuotaResetService) IncrementAPICallUsage(userID uuid.UUID) error {
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	return mqrs.db.Model(&models.MonthlyUsageTracking{}).
		Where("user_id = ? AND year = ? AND month = ?", userID, year, month).
		UpdateColumn("api_calls_made", gorm.Expr("api_calls_made + 1")).Error
}

// IncrementCreditUsage increments credit usage for a user
func (mqrs *MonthlyQuotaResetService) IncrementCreditUsage(userID uuid.UUID, credits int) error {
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	return mqrs.db.Model(&models.MonthlyUsageTracking{}).
		Where("user_id = ? AND year = ? AND month = ?", userID, year, month).
		UpdateColumn("credits_consumed", gorm.Expr("credits_consumed + ?", credits)).Error
}

// ForceResetUserQuota manually resets quota for a specific user (admin function)
func (mqrs *MonthlyQuotaResetService) ForceResetUserQuota(userID uuid.UUID) error {
	var user models.User
	if err := mqrs.db.First(&user, "id = ?", userID).Error; err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	return mqrs.resetUserMonthlyQuota(user, time.Now())
}
