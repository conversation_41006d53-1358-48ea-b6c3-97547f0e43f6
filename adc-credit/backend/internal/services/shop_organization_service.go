package services

import (
	"fmt"

	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ShopOrganizationService handles the integration between shops and ADC SSO organizations
type ShopOrganizationService struct {
	db        *gorm.DB
	ssoClient *SSOClient // Using local stub
	logger    *logrus.Logger
}

// NewShopOrganizationService creates a new shop organization service
func NewShopOrganizationService(db *gorm.DB, ssoClient *SSOClient, logger *logrus.Logger) *ShopOrganizationService {
	return &ShopOrganizationService{
		db:        db,
		ssoClient: ssoClient,
		logger:    logger,
	}
}

// CreateShopWithOrganization creates a shop and corresponding ADC organization
func (s *ShopOrganizationService) CreateShopWithOrganization(shop *models.Shop, userID uuid.UUID) error {
	// Start a database transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Create the shop in local database first
	if err := tx.Create(shop).Error; err != nil {
		return fmt.Errorf("failed to create shop: %w", err)
	}

	// Add the owner as a shop user
	shopUser := models.ShopUser{
		ID:     uuid.New(),
		ShopID: shop.ID,
		UserID: userID,
		Role:   "owner",
	}

	if err := tx.Create(&shopUser).Error; err != nil {
		return fmt.Errorf("failed to create shop user relationship: %w", err)
	}

	// Create corresponding organization in ADC SSO Service
	if err := s.createOrganizationInSSO(shop, userID); err != nil {
		logrus.Errorf("Failed to create organization in SSO service for shop %s: %v", shop.ID, err)
		// Don't fail the entire operation if SSO is unavailable
		// This allows for graceful degradation and later synchronization
		logrus.Warn("Continuing with local shop creation despite SSO service failure")
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logrus.Infof("Successfully created shop %s (%s) with organization integration", shop.ID, shop.Name)
	return nil
}

// createOrganizationInSSO creates an organization in the ADC SSO Service
func (s *ShopOrganizationService) createOrganizationInSSO(shop *models.Shop, ownerUserID uuid.UUID) error {
	if s.ssoClient == nil {
		return fmt.Errorf("SSO client not available")
	}

	// Mock organization creation using SSO client stub
	logrus.Infof("Creating organization in SSO service for shop %s (%s)", shop.ID, shop.Name)
	
	// For now, just log the organization creation (SSO integration pending)
	logrus.Infof("Organization would be created with:")
	logrus.Infof("  Name: %s", shop.Name)
	logrus.Infof("  Description: %s", shop.Description)
	logrus.Infof("  External ID: %s", shop.ID.String())
	logrus.Infof("  Owner: %s", ownerUserID.String())
	
	// Simulate successful organization creation
	logrus.Infof("Mock organization created successfully for shop %s", shop.ID)

	return nil
}

// SyncShopToOrganization synchronizes an existing shop with ADC SSO organization
func (s *ShopOrganizationService) SyncShopToOrganization(shopID uuid.UUID) error {
	// Get the shop from database
	var shop models.Shop
	if err := s.db.First(&shop, "id = ?", shopID).Error; err != nil {
		return fmt.Errorf("shop not found: %w", err)
	}

	// Get shop owner
	var shopUser models.ShopUser
	if err := s.db.Where("shop_id = ? AND role = ?", shopID, "owner").First(&shopUser).Error; err != nil {
		return fmt.Errorf("shop owner not found: %w", err)
	}

	// Check if organization already exists (mock)
	logrus.Infof("Mock: Checking if organization exists for shop %s", shopID)
	// Simulate organization doesn't exist

	// Create the organization if it doesn't exist
	return s.createOrganizationInSSO(&shop, shopUser.UserID)
}

// GetOrganizationForShop retrieves the SSO organization for a given shop (mock implementation)
func (s *ShopOrganizationService) GetOrganizationForShop(shopID uuid.UUID) (interface{}, error) {
	logrus.Infof("Mock: Getting organization for shop %s", shopID)
	
	// Mock organization object
	mockOrg := map[string]interface{}{
		"id": shopID.String(),
		"name": "Mock Organization",
		"external_id": shopID.String(),
	}
	
	return mockOrg, nil
}

// AddUserToShopOrganization adds a user to both local shop and SSO organization
func (s *ShopOrganizationService) AddUserToShopOrganization(shopID, userID uuid.UUID, role string) error {
	// Start transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Add to local shop
	shopUser := models.ShopUser{
		ID:     uuid.New(),
		ShopID: shopID,
		UserID: userID,
		Role:   role,
	}

	if err := tx.Create(&shopUser).Error; err != nil {
		return fmt.Errorf("failed to add user to shop: %w", err)
	}

	// Add to SSO organization
	if err := s.addUserToSSOrganization(shopID, userID, role); err != nil {
		logrus.Errorf("Failed to add user to SSO organization for shop %s: %v", shopID, err)
		// Continue with local operation even if SSO fails
		logrus.Warn("Continuing with local user addition despite SSO service failure")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logrus.Infof("Successfully added user %s to shop %s with role %s", userID, shopID, role)
	return nil
}

// addUserToSSOrganization adds a user to the SSO organization (mock implementation)
func (s *ShopOrganizationService) addUserToSSOrganization(shopID, userID uuid.UUID, role string) error {
	logrus.Infof("Mock: Adding user %s to SSO organization for shop %s with role %s", userID, shopID, role)
	
	// Mock member addition
	logrus.Infof("  Shop ID: %s", shopID)
	logrus.Infof("  User ID: %s", userID)
	logrus.Infof("  Role: %s", role)
	
	// Simulate successful addition
	return nil
}

// RemoveUserFromShopOrganization removes a user from both local shop and SSO organization
func (s *ShopOrganizationService) RemoveUserFromShopOrganization(shopID, userID uuid.UUID) error {
	// Start transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Remove from local shop
	if err := tx.Where("shop_id = ? AND user_id = ?", shopID, userID).Delete(&models.ShopUser{}).Error; err != nil {
		return fmt.Errorf("failed to remove user from shop: %w", err)
	}

	// Remove from SSO organization
	if err := s.removeUserFromSSOrganization(shopID, userID); err != nil {
		logrus.Errorf("Failed to remove user from SSO organization for shop %s: %v", shopID, err)
		// Continue with local operation even if SSO fails
		logrus.Warn("Continuing with local user removal despite SSO service failure")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logrus.Infof("Successfully removed user %s from shop %s", userID, shopID)
	return nil
}

// removeUserFromSSOrganization removes a user from the SSO organization (mock implementation)
func (s *ShopOrganizationService) removeUserFromSSOrganization(shopID, userID uuid.UUID) error {
	logrus.Infof("Mock: Removing user %s from SSO organization for shop %s", userID, shopID)
	
	// Simulate successful removal
	return nil
}

// UpdateShopOrganization updates both local shop and SSO organization
func (s *ShopOrganizationService) UpdateShopOrganization(shopID uuid.UUID, updateData map[string]interface{}) error {
	// Start transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Update local shop
	if err := tx.Model(&models.Shop{}).Where("id = ?", shopID).Updates(updateData).Error; err != nil {
		return fmt.Errorf("failed to update shop: %w", err)
	}

	// Update SSO organization
	if err := s.updateSSOrganization(shopID, updateData); err != nil {
		logrus.Errorf("Failed to update SSO organization for shop %s: %v", shopID, err)
		// Continue with local operation even if SSO fails
		logrus.Warn("Continuing with local shop update despite SSO service failure")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logrus.Infof("Successfully updated shop %s and organization", shopID)
	return nil
}

// updateSSOrganization updates the SSO organization (mock implementation)
func (s *ShopOrganizationService) updateSSOrganization(shopID uuid.UUID, updateData map[string]interface{}) error {
	logrus.Infof("Mock: Updating SSO organization for shop %s", shopID)
	
	// Log what would be updated
	for key, value := range updateData {
		logrus.Infof("  %s: %v", key, value)
	}
	
	// Simulate successful update
	return nil
}