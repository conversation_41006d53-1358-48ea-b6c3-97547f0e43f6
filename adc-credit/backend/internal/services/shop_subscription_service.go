package services

import (
	"fmt"
	"log"

	"github.com/adc-credit/backend/internal/models"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ShopSubscriptionService manages shop-based subscriptions with ADC Subscription Service integration
//
// Architecture: Shop = Organization (same level), Branch = sub-level of Shop/Organization
// - Credit Service Shop ID = ADC Organization ID (1:1 mapping)
// - Credit Service Branches = ADC Organization sub-units
type ShopSubscriptionService struct {
	db                        *gorm.DB
	shopSubscriptionGuard     *ShopSubscriptionGuard
	organizationMapper        *OrganizationMappingService
	subscriptionClient        *subscriptionSDK.Client
	useLegacyFirst           bool // Flag to control which system to try first (deprecated - always false now)
}

// NewShopSubscriptionService creates a new shop subscription service with ADC integration
func NewShopSubscriptionService(
	db *gorm.DB,
	shopSubscriptionGuard *ShopSubscriptionGuard,
	organizationMapper *OrganizationMappingService,
	subscriptionClient *subscriptionSDK.Client,
) *ShopSubscriptionService {
	return &ShopSubscriptionService{
		db:                        db,
		shopSubscriptionGuard:     shopSubscriptionGuard,
		organizationMapper:        organizationMapper,
		subscriptionClient:        subscriptionClient,
		useLegacyFirst:           false, // Legacy system removed - always use shop-based system
	}
}

// SetMigrationMode controls which subscription system to prioritize
func (s *ShopSubscriptionService) SetMigrationMode(useLegacyFirst bool) {
	s.useLegacyFirst = useLegacyFirst
	log.Printf("Shop subscription service migration mode: useLegacyFirst=%t", useLegacyFirst)
}

// GetShopSubscriptionInfo retrieves subscription information for a shop using both systems
// Now prioritizes ADC system as the primary source of truth
func (s *ShopSubscriptionService) GetShopSubscriptionInfo(shopID uuid.UUID) (*models.SubscriptionTier, error) {
	// Always try ADC system first (it's now our primary system)
	tier, err := s.getShopSubscriptionFromADC(shopID)
	if err == nil && tier != nil {
		log.Printf("Successfully retrieved subscription from ADC for shop %s: tier=%s", shopID, tier.Name)
		return tier, nil
	}
	
	log.Printf("ADC subscription not found for shop %s, trying legacy fallback: %v", shopID, err)
	
	// Fallback to legacy system only if ADC fails
	legacyTier, legacyErr := s.getShopSubscriptionFromLegacy(shopID)
	if legacyErr == nil && legacyTier != nil {
		log.Printf("Using legacy subscription for shop %s: tier=%s", shopID, legacyTier.Name)
		return legacyTier, nil
	}
	
	// Both systems failed
	return nil, fmt.Errorf("no subscription found in ADC (%v) or legacy system (%v)", err, legacyErr)
}

// getShopSubscriptionFromLegacy retrieves subscription from the legacy Credit service system
func (s *ShopSubscriptionService) getShopSubscriptionFromLegacy(shopID uuid.UUID) (*models.SubscriptionTier, error) {
	// First try to get shop subscription using shop subscription guard
	var shopSubscription models.ShopSubscription
	if err := s.db.Preload("SubscriptionTier").Where("shop_id = ?", shopID).First(&shopSubscription).Error; err == nil {
		log.Printf("Found shop subscription for shop %s: tier=%s", shopID, shopSubscription.SubscriptionTier.Name)
		return &shopSubscription.SubscriptionTier, nil
	}

	// No shop subscription found - return error
	return nil, fmt.Errorf("no active shop subscription found for shop %s", shopID)
}

// getShopSubscriptionFromADC retrieves subscription from the ADC Subscription Service
func (s *ShopSubscriptionService) getShopSubscriptionFromADC(shopID uuid.UUID) (*models.SubscriptionTier, error) {
	// Get subscription from ADC
	adcSubscription, err := s.organizationMapper.GetSubscriptionForShop(shopID)
	if err != nil {
		return nil, fmt.Errorf("failed to get ADC subscription: %w", err)
	}

	if adcSubscription == nil {
		return nil, fmt.Errorf("no ADC subscription found for shop %d", shopID)
	}

	// Convert ADC subscription to Credit service SubscriptionTier
	tier := s.convertADCSubscriptionToTier(adcSubscription)
	log.Printf("Found ADC subscription for shop %d: plan=%s", shopID, adcSubscription.Plan.Name)
	
	return tier, nil
}

// convertADCSubscriptionToTier converts an ADC subscription to a Credit service SubscriptionTier
func (s *ShopSubscriptionService) convertADCSubscriptionToTier(adcSub *subscriptionSDK.Subscription) *models.SubscriptionTier {
	// Create a SubscriptionTier based on ADC subscription
	tier := &models.SubscriptionTier{
		Name:  adcSub.Plan.Name,
		Price: int(adcSub.Plan.MonthlyPrice),
	}

	// Map ADC plan limits to Credit service limits
	if adcSub.Plan.Limits != nil {
		// Extract credit platform specific limits
		if creditLimits, ok := adcSub.Plan.Limits["credit_platform"].(map[string]interface{}); ok {
			if maxShops, ok := creditLimits["max_shops"].(float64); ok {
				tier.MaxShops = int(maxShops)
			}
			if maxCustomers, ok := creditLimits["max_customers_per_shop"].(float64); ok {
				tier.MaxCustomersPerShop = int(maxCustomers)
			}
			if maxAPIKeys, ok := creditLimits["max_api_keys_per_shop"].(float64); ok {
				tier.MaxAPIKeysPerShop = int(maxAPIKeys)
			}
			if maxBranches, ok := creditLimits["max_branches_per_shop"].(float64); ok {
				tier.MaxBranchesPerShop = int(maxBranches)
			}
			if maxQRCodes, ok := creditLimits["max_qr_codes_per_month"].(float64); ok {
				tier.MaxQRCodesPerMonth = int(maxQRCodes)
			}
			if maxWebhooks, ok := creditLimits["max_webhooks"].(float64); ok {
				tier.MaxWebhooks = int(maxWebhooks)
			}
			if creditLimit, ok := creditLimits["credit_limit"].(float64); ok {
				tier.CreditLimit = int(creditLimit)
			}
			if historyDays, ok := creditLimits["analytics_history_days"].(float64); ok {
				tier.AnalyticsHistoryDays = int(historyDays)
			}
			if supportLevel, ok := creditLimits["support_level"].(string); ok {
				tier.SupportLevel = supportLevel
			}
			if advancedAnalytics, ok := creditLimits["advanced_analytics"].(bool); ok {
				tier.AdvancedAnalytics = advancedAnalytics
			}
		}
	}

	// Set default values if not specified
	if tier.MaxShops == 0 {
		tier.MaxShops = 1
	}
	if tier.MaxCustomersPerShop == 0 {
		tier.MaxCustomersPerShop = 100
	}
	if tier.MaxAPIKeysPerShop == 0 {
		tier.MaxAPIKeysPerShop = 2
	}

	return tier
}

// CheckSubscriptionLimit checks subscription limits using both systems
func (s *ShopSubscriptionService) CheckSubscriptionLimit(shopID uuid.UUID, limitType string, currentUsage int) (bool, error) {
	// Get subscription info
	tier, err := s.GetShopSubscriptionInfo(shopID)
	if err != nil {
		log.Printf("Failed to get subscription for limit check (shop %d): %v", shopID, err)
		// Allow the action if we can't determine subscription (fail open)
		return true, nil
	}

	// Check limits based on limit type
	switch limitType {
	case "shops":
		return currentUsage < tier.MaxShops, nil
	case "customers":
		return currentUsage < tier.MaxCustomersPerShop, nil
	case "api_keys":
		return currentUsage < tier.MaxAPIKeysPerShop, nil
	case "branches":
		return currentUsage < tier.MaxBranchesPerShop, nil
	case "qr_codes":
		return currentUsage < tier.MaxQRCodesPerMonth, nil
	case "webhooks":
		return currentUsage < tier.MaxWebhooks, nil
	case "credits":
		return currentUsage < tier.CreditLimit, nil
	default:
		log.Printf("Unknown limit type: %s", limitType)
		return true, nil // Allow unknown limit types
	}
}

// TODO: Remove this legacy migration function - commented out due to legacy Subscription dependency
/*
// MigrateShopToADC migrates a shop's subscription from legacy system to ADC
func (s *ShopSubscriptionService) MigrateShopToADC(shopID uuid.UUID) error {
	// Get the shop and its subscription from legacy system
	var shop models.MerchantShop
	if err := s.db.Preload("Owner").First(&shop, shopID).Error; err != nil {
		return fmt.Errorf("shop not found: %w", err)
	}

	// Get legacy subscription
	tier, err := s.getShopSubscriptionFromLegacy(shopID)
	if err != nil {
		return fmt.Errorf("failed to get legacy subscription: %w", err)
	}

	// Get user subscription for migration
	var userSub models.Subscription
	if err := s.db.Preload("SubscriptionTier").Where("user_id = ? AND status = ?", shop.OwnerUserID, "active").First(&userSub).Error; err != nil {
		return fmt.Errorf("failed to get user subscription: %w", err)
	}

	// Migrate to ADC
	err = s.organizationMapper.MigrateShopSubscriptionToADC(&shop, &shop.Owner, &userSub, tier)
	if err != nil {
		return fmt.Errorf("failed to migrate to ADC: %w", err)
	}

	log.Printf("Successfully migrated shop %d to ADC subscription service", shopID)
	return nil
}
*/

// GetSubscriptionUsage retrieves current usage statistics for a shop/organization
// In the new architecture: Shop = Organization, so we track organization-level usage
func (s *ShopSubscriptionService) GetSubscriptionUsage(shopID uuid.UUID) (map[string]int, error) {
	usage := make(map[string]int)

	// Count shops (for multi-shop users) - this becomes organization count
	var shopCount int64
	var shop models.MerchantShop
	if err := s.db.First(&shop, shopID).Error; err != nil {
		return nil, fmt.Errorf("shop/organization not found: %w", err)
	}

	if err := s.db.Model(&models.MerchantShop{}).Where("owner_user_id = ?", shop.OwnerUserID).Count(&shopCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count organizations: %w", err)
	}
	usage["organizations"] = int(shopCount)
	usage["shops"] = int(shopCount) // Backward compatibility

	// Count customers for this organization (shop)
	var customerCount int64
	if err := s.db.Model(&models.ShopCustomer{}).Where("shop_id = ?", shopID).Count(&customerCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count customers: %w", err)
	}
	usage["customers"] = int(customerCount)

	// Count API keys for this organization (shop)
	var apiKeyCount int64
	if err := s.db.Model(&models.APIKey{}).Where("shop_id = ?", shopID).Count(&apiKeyCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count API keys: %w", err)
	}
	usage["api_keys"] = int(apiKeyCount)

	// Count branches for this organization (shop)
	var branchCount int64
	if err := s.db.Model(&models.ShopBranch{}).Where("shop_id = ?", shopID).Count(&branchCount).Error; err != nil {
		log.Printf("Warning: failed to count branches for shop %s: %v", shopID, err)
		branchCount = 0 // Continue with 0 if branches table doesn't exist yet
	}
	usage["branches"] = int(branchCount)

	// TODO: Add other usage metrics (QR codes, webhooks, etc.)

	return usage, nil
}

// IsFeatureEnabled checks if a feature is enabled for a shop's subscription
func (s *ShopSubscriptionService) IsFeatureEnabled(shopID uuid.UUID, feature string) (bool, error) {
	tier, err := s.GetShopSubscriptionInfo(shopID)
	if err != nil {
		log.Printf("Failed to get subscription for feature check (shop %s): %v", shopID, err)
		// Allow the feature if we can't determine subscription (fail open)
		return true, nil
	}

	switch feature {
	case "advanced_analytics":
		return tier.AdvancedAnalytics, nil
	case "premium_support":
		return tier.SupportLevel == "premium" || tier.SupportLevel == "enterprise", nil
	case "unlimited_customers":
		return tier.MaxCustomersPerShop <= 0, nil // 0 or negative means unlimited
	default:
		log.Printf("Unknown feature: %s", feature)
		return true, nil // Allow unknown features
	}
}

// CreateShopWithADCIntegration creates a shop and automatically creates the corresponding organization in ADC
func (s *ShopSubscriptionService) CreateShopWithADCIntegration(shop *models.MerchantShop, user *models.User, tier *models.SubscriptionTier) error {
	// Create the shop-to-organization mapping
	mapping, err := s.organizationMapper.MapShopToOrganization(shop, user, tier)
	if err != nil {
		return fmt.Errorf("failed to create organization mapping: %w", err)
	}

	// Create organization and subscription in ADC
	err = s.organizationMapper.CreateOrganizationInADC(mapping, tier)
	if err != nil {
		log.Printf("Failed to create organization in ADC for shop %s: %v", shop.ID, err)
		// Don't fail shop creation if ADC is unavailable, but log the error
		log.Printf("Shop %s created locally but not synced to ADC - manual sync may be needed", shop.ID)
		return nil
	}

	log.Printf("Successfully created shop %s and organization %s in ADC with tier %s", 
		shop.ID, mapping.OrganizationID, tier.Name)
	
	return nil
}

// SyncShopToADC manually syncs an existing shop to ADC (for migration purposes)
func (s *ShopSubscriptionService) SyncShopToADC(shopID uuid.UUID) error {
	// Get shop and user information
	var shop models.MerchantShop
	if err := s.db.Preload("Owner").First(&shop, shopID).Error; err != nil {
		return fmt.Errorf("shop not found: %w", err)
	}

	// Get current subscription tier
	tier, err := s.getShopSubscriptionFromLegacy(shopID)
	if err != nil {
		// If no legacy subscription, use default free tier
		tier = &models.SubscriptionTier{
			Name:                  "Free",
			Price:                 0,
			MaxShops:             1,
			MaxCustomersPerShop:  100,
			MaxAPIKeysPerShop:    2,
			MaxBranchesPerShop:   1,
			MaxQRCodesPerMonth:   50,
			MaxWebhooks:          1,
			CreditLimit:          1000,
			AnalyticsHistoryDays: 30,
			SupportLevel:         "community",
			AdvancedAnalytics:    false,
		}
		log.Printf("No legacy subscription found for shop %s, using default free tier", shopID)
	}

	// Create organization mapping and sync to ADC
	return s.CreateShopWithADCIntegration(&shop, &shop.Owner, tier)
}