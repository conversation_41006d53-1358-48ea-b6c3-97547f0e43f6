package services

import (
	"fmt"

	"github.com/adc-credit/backend/internal/config"
	"github.com/adc-credit/backend/internal/models"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	// ssoSDK "github.com/adc-sso-service/sdk" // Temporarily disabled due to compilation issues
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ServiceContainer holds all initialized services for dependency injection
type ServiceContainer struct {
	// Database
	DB *gorm.DB

	// Shop subscription services (moved from legacy)
	ShopSubscriptionGuard   *ShopSubscriptionGuard

	// Shop subscription services  
	OrganizationMapper      *OrganizationMappingService
	ShopSubscription        *ShopSubscriptionService

	// Shop organization integration
	ShopOrganization        *ShopOrganizationService

	// Subscription synchronization services
	SubscriptionSync        *SubscriptionSyncService
	SyncScheduler           *SyncSchedulerService

	// Usage reporting services
	UsageReporting          *UsageReportingService

	// External clients
	SubscriptionClient *subscriptionSDK.Client
	SSOClient          *SSOClient // Using local stub
	SettingsService    *SettingsService
	MultiLangClient    *MultiLangClient // Multi-Languages service client
	
	// Auto-translation processor
	AutoTranslationProcessor *AutoTranslationProcessor
	
	// Smart translation triggers
	SmartTranslationTriggers *SmartTranslationTriggers
}

// NewServiceContainer initializes all services with proper dependencies
func NewServiceContainer(db *gorm.DB, subscriptionClient *subscriptionSDK.Client, ssoClient *SSOClient) *ServiceContainer {
	logrus.Info("Initializing service container with subscription service integration")

	// Load Multi-Languages service configuration
	multiLangConfig := config.LoadMultiLangConfig()
	
	container := &ServiceContainer{
		DB:                 db,
		SubscriptionClient: subscriptionClient,
		SSOClient:          ssoClient,
		SettingsService:    NewSettingsService(),
		MultiLangClient:    NewMultiLangClient(
			multiLangConfig.BaseURL,
			multiLangConfig.InternalKey,
			multiLangConfig.ProjectID,
			multiLangConfig.Organization,
		),
	}
	
	// Initialize auto-translation processor with Multi-Languages client
	container.AutoTranslationProcessor = NewAutoTranslationProcessor(container.SettingsService, container.MultiLangClient, 3)
	
	// Initialize smart translation triggers after processor
	container.SmartTranslationTriggers = NewSmartTranslationTriggers(container.SettingsService, container.AutoTranslationProcessor)

	// Initialize legacy services first
	container.initLegacyServices()

	// Initialize shop subscription services
	container.initShopSubscriptionServices()

	// Initialize shop organization integration
	container.initShopOrganizationServices()

	// Initialize subscription sync services
	container.initSubscriptionSyncServices()

	// Initialize usage reporting services
	container.initUsageReportingServices()

	// Start auto-translation processor
	if err := container.AutoTranslationProcessor.Start(); err != nil {
		logrus.Errorf("Failed to start auto-translation processor: %v", err)
	} else {
		logrus.Info("Auto-translation processor started successfully")
	}

	// Start smart translation triggers
	if err := container.SmartTranslationTriggers.Start(); err != nil {
		logrus.Errorf("Failed to start smart translation triggers: %v", err)
	} else {
		logrus.Info("Smart translation triggers started successfully")
	}

	logrus.Info("Service container initialized successfully")
	return container
}

// initLegacyServices initializes the remaining shop subscription services
func (c *ServiceContainer) initLegacyServices() {
	logrus.Debug("Initializing shop subscription guard")

	// Initialize shop subscription guard with centralized service client
	c.ShopSubscriptionGuard = NewShopSubscriptionGuard(c.SubscriptionClient)

	logrus.Debug("Shop subscription guard initialized")
}

// initShopSubscriptionServices initializes the shop subscription services for ADC integration
func (c *ServiceContainer) initShopSubscriptionServices() {
	logrus.Debug("Initializing shop subscription services")

	// Initialize organization mapping service
	c.OrganizationMapper = NewOrganizationMappingService(c.SubscriptionClient)

	// Initialize shop subscription service
	c.ShopSubscription = NewShopSubscriptionService(
		c.DB,
		c.ShopSubscriptionGuard,
		c.OrganizationMapper,
		c.SubscriptionClient,
	)

	// Start in legacy-first mode during transition
	c.ShopSubscription.SetMigrationMode(true)

	logrus.Debug("Shop subscription services initialized")
}

// initShopOrganizationServices initializes the shop organization integration services
func (c *ServiceContainer) initShopOrganizationServices() {
	logrus.Debug("Initializing shop organization services")

	// Initialize logger for shop organization service
	shopOrgLogger := logrus.New()

	// Initialize shop organization service
	c.ShopOrganization = NewShopOrganizationService(c.DB, c.SSOClient, shopOrgLogger)

	logrus.Debug("Shop organization services initialized")
}

// GetSubscriptionService returns the shop subscription service
func (c *ServiceContainer) GetSubscriptionService() SubscriptionServiceInterface {
	// Return the shop subscription service
	return c.ShopSubscription
}

// SetMigrationMode switches between legacy-first and ADC-first modes
func (c *ServiceContainer) SetMigrationMode(useLegacyFirst bool) {
	if c.ShopSubscription != nil {
		c.ShopSubscription.SetMigrationMode(useLegacyFirst)
		logrus.Infof("Migration mode updated: useLegacyFirst=%t", useLegacyFirst)
	}
}

// SubscriptionServiceInterface defines the contract for subscription services
type SubscriptionServiceInterface interface {
	GetShopSubscriptionInfo(shopID uuid.UUID) (*models.SubscriptionTier, error)
	CheckSubscriptionLimit(shopID uuid.UUID, limitType string, currentUsage int) (bool, error)
	IsFeatureEnabled(shopID uuid.UUID, feature string) (bool, error)
}

// Ensure ShopSubscriptionService implements the interface
var _ SubscriptionServiceInterface = (*ShopSubscriptionService)(nil)

// GetShopSubscriptionGuard returns the shop subscription guard
func (c *ServiceContainer) GetShopSubscriptionGuard() *ShopSubscriptionGuard {
	return c.ShopSubscriptionGuard
}

// GetOrganizationMapper returns the organization mapping service
func (c *ServiceContainer) GetOrganizationMapper() *OrganizationMappingService {
	return c.OrganizationMapper
}

// GetShopSubscriptionService returns the shop subscription service  
func (c *ServiceContainer) GetShopSubscriptionService() *ShopSubscriptionService {
	return c.ShopSubscription
}

// GetShopOrganizationService returns the shop organization service
func (c *ServiceContainer) GetShopOrganizationService() *ShopOrganizationService {
	return c.ShopOrganization
}

// initSubscriptionSyncServices initializes subscription synchronization services
func (c *ServiceContainer) initSubscriptionSyncServices() {
	logrus.Debug("Initializing subscription sync services")

	// Initialize logger for sync services
	syncLogger := logrus.New()

	// Initialize subscription sync service
	c.SubscriptionSync = NewSubscriptionSyncService(
		c.DB,
		c.SubscriptionClient,
		c.ShopOrganization,
		syncLogger,
	)

	// Initialize sync scheduler service
	c.SyncScheduler = NewSyncSchedulerService(c.SubscriptionSync, syncLogger)

	// Start scheduler by default
	if err := c.SyncScheduler.Start(); err != nil {
		logrus.Errorf("Failed to start sync scheduler: %v", err)
	} else {
		logrus.Info("Sync scheduler started successfully")
	}

	logrus.Debug("Subscription sync services initialized")
}

// initUsageReportingServices initializes usage reporting services
func (c *ServiceContainer) initUsageReportingServices() {
	logrus.Debug("Initializing usage reporting services")

	// Initialize logger for usage reporting
	usageLogger := logrus.New()

	// Initialize usage reporting service
	c.UsageReporting = NewUsageReportingService(
		c.DB,
		c.SubscriptionClient,
		nil, // Analytics client disabled
		c.ShopOrganization,
		usageLogger,
	)

	logrus.Debug("Usage reporting services initialized")
}

// GetSubscriptionSyncService returns the subscription sync service
func (c *ServiceContainer) GetSubscriptionSyncService() *SubscriptionSyncService {
	return c.SubscriptionSync
}

// GetSyncSchedulerService returns the sync scheduler service
func (c *ServiceContainer) GetSyncSchedulerService() *SyncSchedulerService {
	return c.SyncScheduler
}

// GetUsageReportingService returns the usage reporting service
func (c *ServiceContainer) GetUsageReportingService() *UsageReportingService {
	return c.UsageReporting
}

// GetSettingsService returns the settings service
func (c *ServiceContainer) GetSettingsService() *SettingsService {
	return c.SettingsService
}

// GetAutoTranslationProcessor returns the auto-translation processor
func (c *ServiceContainer) GetAutoTranslationProcessor() *AutoTranslationProcessor {
	return c.AutoTranslationProcessor
}

// GetSmartTranslationTriggers returns the smart translation triggers
func (c *ServiceContainer) GetSmartTranslationTriggers() *SmartTranslationTriggers {
	return c.SmartTranslationTriggers
}

// TODO: Remove this legacy migration function - commented out due to legacy dependencies
/*
// MigrateShopToADC migrates a specific shop to the ADC Subscription Service
func (c *ServiceContainer) MigrateShopToADC(shopID uuid.UUID) error {
	if c.HybridSubscription == nil {
		return fmt.Errorf("shop subscription service not initialized")
	}

	logrus.Infof("Starting migration of shop %d to ADC subscription service", shopID)
	err := c.ShopSubscription.MigrateShopToADC(shopID)
	if err != nil {
		logrus.Errorf("Failed to migrate shop %d: %v", shopID, err)
		return err
	}

	logrus.Infof("Successfully migrated shop %d to ADC subscription service", shopID)
	return nil
}
*/

// GetSubscriptionUsage returns current usage statistics for a shop
func (c *ServiceContainer) GetSubscriptionUsage(shopID uuid.UUID) (map[string]int, error) {
	if c.ShopSubscription == nil {
		return nil, fmt.Errorf("shop subscription service not initialized")
	}

	return c.ShopSubscription.GetSubscriptionUsage(shopID)
}

// ValidateSubscriptionAccessWithContext validates if a user has access to perform an action with internal API key bypass
func (c *ServiceContainer) ValidateSubscriptionAccessWithContext(c2 *gin.Context, shopID uuid.UUID, action string) error {
	// Check for internal API key bypass first
	if c2 != nil {
		isInternal, exists := c2.Get("is_internal_api_key")
		if exists {
			if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
				logrus.Infof("Internal API key detected - bypassing subscription access validation for action: %s", action)
				return nil
			}
		}
	}
	
	return c.ValidateSubscriptionAccess(shopID, action)
}

// ValidateSubscriptionAccess validates if a user has access to perform an action
func (c *ServiceContainer) ValidateSubscriptionAccess(shopID uuid.UUID, action string) error {
	// Get current usage for the specific action
	usage, err := c.GetSubscriptionUsage(shopID)
	if err != nil {
		logrus.Warnf("Failed to get usage for shop %d: %v", shopID, err)
		// Allow action if we can't determine usage (fail open)
		return nil
	}

	// Check if the action is allowed based on current usage
	currentUsage := usage[action]
	allowed, err := c.ShopSubscription.CheckSubscriptionLimit(shopID, action, currentUsage)
	if err != nil {
		logrus.Warnf("Failed to check subscription limit for shop %d, action %s: %v", shopID, action, err)
		// Allow action if we can't determine limits (fail open)
		return nil
	}

	if !allowed {
		return fmt.Errorf("subscription limit exceeded for %s", action)
	}

	return nil
}