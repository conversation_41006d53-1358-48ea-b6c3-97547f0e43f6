package services

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// WebhookService handles webhook delivery
type WebhookService struct {
	client *http.Client
}

// NewWebhookService creates a new webhook service
func NewWebhookService() *WebhookService {
	return &WebhookService{
		client: &http.Client{
			Timeout: time.Second * 10,
		},
	}
}

// WebhookPayload represents the data sent in a webhook
type WebhookPayload struct {
	ID        string                 `json:"id"`
	Event     string                 `json:"event"`
	CreatedAt time.Time              `json:"created_at"`
	Data      map[string]interface{} `json:"data"`
}

// SendWebhook sends a webhook notification for an event
func (s *WebhookService) SendWebhook(userID uuid.UUID, event string, data map[string]interface{}) {
	// Find all active webhooks for this user that are subscribed to this event
	var webhooks []models.Webhook
	if err := database.DB.Where("user_id = ? AND active = ? AND ? = ANY(events)", userID, true, event).Find(&webhooks).Error; err != nil {
		logrus.WithError(err).Error("Error finding webhooks")
		return
	}

	// If no webhooks found, return
	if len(webhooks) == 0 {
		return
	}

	// Create payload
	payload := WebhookPayload{
		ID:        uuid.New().String(),
		Event:     event,
		CreatedAt: time.Now(),
		Data:      data,
	}

	// Send webhook to each endpoint
	for _, webhook := range webhooks {
		go s.deliverWebhook(webhook, payload)
	}
}

// deliverWebhook sends the webhook to a specific endpoint
func (s *WebhookService) deliverWebhook(webhook models.Webhook, payload WebhookPayload) {
	// Convert payload to JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		logrus.WithError(err).Error("Error marshaling webhook payload")
		s.recordDelivery(webhook.ID, payload.Event, string(payloadBytes), 0, err.Error(), false, 0)
		return
	}

	// Create request
	req, err := http.NewRequest("POST", webhook.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		logrus.WithError(err).Error("Error creating webhook request")
		s.recordDelivery(webhook.ID, payload.Event, string(payloadBytes), 0, err.Error(), false, 0)
		return
	}

	// Add headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "ADC-Credit-Webhook/1.0")
	req.Header.Set("X-ADC-Event", payload.Event)
	req.Header.Set("X-ADC-Delivery", payload.ID)

	// Add signature if secret is set
	if webhook.Secret != "" {
		signature := s.generateSignature(payloadBytes, webhook.Secret)
		req.Header.Set("X-ADC-Signature", signature)
	}

	// Send request
	startTime := time.Now()
	resp, err := s.client.Do(req)
	duration := time.Since(startTime).Milliseconds()

	if err != nil {
		logrus.WithError(err).Error("Error sending webhook")
		s.recordDelivery(webhook.ID, payload.Event, string(payloadBytes), 0, err.Error(), false, int(duration))
		s.updateWebhookStatus(webhook.ID, false)
		return
	}
	defer resp.Body.Close()

	// Check response
	success := resp.StatusCode >= 200 && resp.StatusCode < 300

	// Record delivery
	s.recordDelivery(webhook.ID, payload.Event, string(payloadBytes), resp.StatusCode, "", success, int(duration))
	s.updateWebhookStatus(webhook.ID, success)
}

// generateSignature creates an HMAC signature for the payload
func (s *WebhookService) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// recordDelivery records a webhook delivery attempt
func (s *WebhookService) recordDelivery(webhookID uuid.UUID, event string, payload string, statusCode int, response string, success bool, duration int) {
	delivery := models.WebhookDelivery{
		ID:         uuid.New(),
		WebhookID:  webhookID,
		Event:      event,
		Payload:    payload,
		StatusCode: statusCode,
		Response:   response,
		Success:    success,
		Duration:   duration,
	}

	if err := database.DB.Create(&delivery).Error; err != nil {
		logrus.WithError(err).Error("Error recording webhook delivery")
	}
}

// updateWebhookStatus updates the webhook status based on delivery success
func (s *WebhookService) updateWebhookStatus(webhookID uuid.UUID, success bool) {
	now := time.Now()
	
	// Build update map to avoid touching the Events field
	updates := map[string]interface{}{
		"last_called": now,
		"updated_at":  now,
	}

	if !success {
		// Get current fail count
		var currentFailCount int
		if err := database.DB.Model(&models.Webhook{}).
			Where("id = ?", webhookID).
			Pluck("fail_count", &currentFailCount).Error; err != nil {
			logrus.WithError(err).Error("Error getting current fail count")
			return
		}
		
		currentFailCount++
		updates["fail_count"] = currentFailCount
		
		// Disable webhook after 10 consecutive failures
		if currentFailCount >= 10 {
			updates["active"] = false
		}
	} else {
		updates["fail_count"] = 0
	}

	// Update only the specific fields without touching Events
	if err := database.DB.Model(&models.Webhook{}).
		Where("id = ?", webhookID).
		Updates(updates).Error; err != nil {
		logrus.WithError(err).Error("Error updating webhook status")
	}
}
