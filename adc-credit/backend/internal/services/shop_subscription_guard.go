package services

import (
	"context"
	"fmt"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"gorm.io/gorm"
)

// LimitCheckResult represents the result of a limit check
type LimitCheckResult struct {
	Allowed      bool   `json:"allowed"`
	CurrentUsage int    `json:"current_usage"`
	Limit        int    `json:"limit"`
	Unlimited    bool   `json:"unlimited"`
	Message      string `json:"message"`
}

// ShopSubscriptionGuard provides shop-based subscription limit checking and enforcement
type ShopSubscriptionGuard struct {
	db                 *gorm.DB
	subscriptionClient *subscriptionSDK.Client // For centralized service integration
}

// NewShopSubscriptionGuard creates a new shop subscription guard instance
func NewShopSubscriptionGuard(subscriptionClient *subscriptionSDK.Client) *ShopSubscriptionGuard {
	return &ShopSubscriptionGuard{
		db:                 database.DB,
		subscriptionClient: subscriptionClient,
	}
}

// SetSubscriptionClient sets the centralized subscription service client
func (ssg *ShopSubscriptionGuard) SetSubscriptionClient(client *subscriptionSDK.Client) {
	ssg.subscriptionClient = client
}

// isInternalAPIKeyFromContext extracts and checks if the current request is from an internal API key
func (ssg *ShopSubscriptionGuard) isInternalAPIKeyFromContext(c *gin.Context) bool {
	if c == nil {
		return false
	}
	
	isInternal, exists := c.Get("is_internal_api_key")
	if !exists {
		return false
	}
	
	internal, ok := isInternal.(bool)
	return ok && internal
}

// GetShopActiveSubscription gets the shop's active subscription with tier
func (ssg *ShopSubscriptionGuard) GetShopActiveSubscription(shopID uuid.UUID) (*models.ShopSubscription, error) {
	var subscription models.ShopSubscription
	err := ssg.db.Where("shop_id = ? AND status = ?", shopID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error
	
	if err != nil {
		return nil, err
	}

	return &subscription, nil
}

// CheckShopLimitWithContext checks if user can create more shops with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckShopLimitWithContext(c *gin.Context, userID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - shop limits bypassed",
		}, nil
	}
	
	return ssg.CheckShopLimit(userID)
}

// CheckShopLimit checks if user can create more shops (user-level check)
func (ssg *ShopSubscriptionGuard) CheckShopLimit(userID uuid.UUID) (*LimitCheckResult, error) {
	// For shop limits, we still check at user level since users create shops
	// but we look at their personal subscription or the highest tier they have access to
	
	// Check if user is owner of any shops with subscriptions (shop-based system only)
	var shopUser models.ShopUser
	err := ssg.db.Where("user_id = ? AND role = ?", userID, "owner").
		Preload("Shop").
		Preload("Shop.Subscription").
		Preload("Shop.Subscription.SubscriptionTier").
		First(&shopUser).Error
	
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No shop subscription found - user must be owner of a shop with an active subscription",
		}, err
	}
	
	if shopUser.Shop.Subscription == nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "Shop has no active subscription",
		}, fmt.Errorf("shop has no active subscription")
	}
	
	// Use shop subscription tier for limits
	return ssg.checkShopLimitWithTier(userID, shopUser.Shop.Subscription.SubscriptionTier)
}

func (ssg *ShopSubscriptionGuard) checkShopLimitWithTier(userID uuid.UUID, tier models.SubscriptionTier) (*LimitCheckResult, error) {
	// Count current shops owned by user
	var currentShops int64
	if err := ssg.db.Model(&models.ShopUser{}).
		Where("user_id = ? AND role = ?", userID, "owner").
		Count(&currentShops).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if tier.UnlimitedShops {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentShops),
			Unlimited:    true,
			Message:      "Unlimited shops allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentShops) < tier.MaxShops
	message := fmt.Sprintf("Using %d of %d shops", currentShops, tier.MaxShops)
	if !allowed {
		message = fmt.Sprintf("Shop limit reached (%d/%d). Upgrade your subscription to create more shops.",
			currentShops, tier.MaxShops)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentShops),
		Limit:        tier.MaxShops,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckCustomerLimitWithContext checks if shop can add more customers with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckCustomerLimitWithContext(c *gin.Context, shopID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - customer limits bypassed",
		}, nil
	}
	
	return ssg.CheckCustomerLimit(shopID)
}

// CheckCustomerLimit checks if shop can add more customers
func (ssg *ShopSubscriptionGuard) CheckCustomerLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Try centralized service first if available
	if ssg.subscriptionClient != nil {
		return ssg.checkCustomerLimitCentralized(shopID)
	}

	// Fallback to local subscription system
	return ssg.checkCustomerLimitLocal(shopID)
}

// checkCustomerLimitCentralized checks customer limits via centralized subscription service
func (ssg *ShopSubscriptionGuard) checkCustomerLimitCentralized(shopID uuid.UUID) (*LimitCheckResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get usage stats from centralized service
	stats, err := ssg.subscriptionClient.GetUsageStats(ctx, shopID, "credit", "customers")
	if err != nil {
		logrus.Warnf("Failed to get customer usage stats from centralized service for shop %s: %v", shopID, err)
		// Fallback to local checking
		return ssg.checkCustomerLimitLocal(shopID)
	}

	allowed := !stats.IsUnlimited && stats.CurrentUsage < stats.UsageLimit
	if stats.IsUnlimited {
		allowed = true
	}

	message := fmt.Sprintf("Using %d of %d customers", stats.CurrentUsage, stats.UsageLimit)
	if stats.IsUnlimited {
		message = "Unlimited customers allowed"
	} else if !allowed {
		message = fmt.Sprintf("Customer limit reached (%d/%d). Upgrade your subscription to add more customers.",
			stats.CurrentUsage, stats.UsageLimit)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(stats.CurrentUsage),
		Limit:        int(stats.UsageLimit),
		Unlimited:    stats.IsUnlimited,
		Message:      message,
	}, nil
}

// checkCustomerLimitLocal checks customer limits using local subscription system (fallback)
func (ssg *ShopSubscriptionGuard) checkCustomerLimitLocal(shopID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	// Count current customers for this shop
	var currentCustomers int64
	if err := ssg.db.Model(&models.ShopCustomer{}).Where("shop_id = ?", shopID).Count(&currentCustomers).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedCustomers {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentCustomers),
			Unlimited:    true,
			Message:      "Unlimited customers allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentCustomers) < subscription.SubscriptionTier.MaxCustomersPerShop
	message := fmt.Sprintf("Using %d of %d customers", currentCustomers, subscription.SubscriptionTier.MaxCustomersPerShop)
	if !allowed {
		message = fmt.Sprintf("Customer limit reached (%d/%d). Upgrade your shop subscription to add more customers.",
			currentCustomers, subscription.SubscriptionTier.MaxCustomersPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentCustomers),
		Limit:        subscription.SubscriptionTier.MaxCustomersPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckAPIKeyLimitWithContext checks if shop can create more API keys with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckAPIKeyLimitWithContext(c *gin.Context, shopID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - API key limits bypassed",
		}, nil
	}
	
	return ssg.CheckAPIKeyLimit(shopID)
}

// CheckAPIKeyLimit checks if shop can create more API keys
func (ssg *ShopSubscriptionGuard) CheckAPIKeyLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Try centralized service first if available
	if ssg.subscriptionClient != nil {
		return ssg.checkAPIKeyLimitCentralized(shopID)
	}

	// Fallback to local subscription system
	return ssg.checkAPIKeyLimitLocal(shopID)
}

// checkAPIKeyLimitCentralized checks API key limits via centralized subscription service
func (ssg *ShopSubscriptionGuard) checkAPIKeyLimitCentralized(shopID uuid.UUID) (*LimitCheckResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get usage stats from centralized service
	stats, err := ssg.subscriptionClient.GetUsageStats(ctx, shopID, "credit", "api_keys")
	if err != nil {
		logrus.Warnf("Failed to get API key usage stats from centralized service for shop %s: %v", shopID, err)
		// Fallback to local checking
		return ssg.checkAPIKeyLimitLocal(shopID)
	}

	allowed := !stats.IsUnlimited && stats.CurrentUsage < stats.UsageLimit
	if stats.IsUnlimited {
		allowed = true
	}

	message := fmt.Sprintf("Using %d of %d API keys", stats.CurrentUsage, stats.UsageLimit)
	if stats.IsUnlimited {
		message = "Unlimited API keys allowed"
	} else if !allowed {
		message = fmt.Sprintf("API key limit reached (%d/%d). Upgrade your subscription to create more API keys.",
			stats.CurrentUsage, stats.UsageLimit)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(stats.CurrentUsage),
		Limit:        int(stats.UsageLimit),
		Unlimited:    stats.IsUnlimited,
		Message:      message,
	}, nil
}

// checkAPIKeyLimitLocal checks API key limits using local subscription system (fallback)
func (ssg *ShopSubscriptionGuard) checkAPIKeyLimitLocal(shopID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	// Count current API keys for this shop
	var currentAPIKeys int64
	if err := ssg.db.Model(&models.APIKey{}).Where("shop_id = ?", shopID).Count(&currentAPIKeys).Error; err != nil {
		return nil, err
	}

	// Check limit
	allowed := int(currentAPIKeys) < subscription.SubscriptionTier.MaxAPIKeysPerShop
	message := fmt.Sprintf("Using %d of %d API keys", currentAPIKeys, subscription.SubscriptionTier.MaxAPIKeysPerShop)
	if !allowed {
		message = fmt.Sprintf("API key limit reached (%d/%d). Upgrade your shop subscription to create more API keys.",
			currentAPIKeys, subscription.SubscriptionTier.MaxAPIKeysPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentAPIKeys),
		Limit:        subscription.SubscriptionTier.MaxAPIKeysPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckBranchLimitWithContext checks if shop can create more branches with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckBranchLimitWithContext(c *gin.Context, shopID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - branch limits bypassed",
		}, nil
	}
	
	return ssg.CheckBranchLimit(shopID)
}

// CheckBranchLimit checks if shop can create more branches
func (ssg *ShopSubscriptionGuard) CheckBranchLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Try centralized service first if available
	if ssg.subscriptionClient != nil {
		return ssg.checkBranchLimitCentralized(shopID)
	}

	// Fallback to local subscription system
	return ssg.checkBranchLimitLocal(shopID)
}

// checkBranchLimitCentralized checks branch limits via centralized subscription service
func (ssg *ShopSubscriptionGuard) checkBranchLimitCentralized(shopID uuid.UUID) (*LimitCheckResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get usage stats from centralized service
	stats, err := ssg.subscriptionClient.GetUsageStats(ctx, shopID, "credit", "branches")
	if err != nil {
		logrus.Warnf("Failed to get branch usage stats from centralized service for shop %s: %v", shopID, err)
		// Fallback to local checking
		return ssg.checkBranchLimitLocal(shopID)
	}

	allowed := !stats.IsUnlimited && stats.CurrentUsage < stats.UsageLimit
	if stats.IsUnlimited {
		allowed = true
	}

	message := fmt.Sprintf("Using %d of %d branches", stats.CurrentUsage, stats.UsageLimit)
	if stats.IsUnlimited {
		message = "Unlimited branches allowed"
	} else if !allowed {
		message = fmt.Sprintf("Branch limit reached (%d/%d). Upgrade your subscription to create more branches.",
			stats.CurrentUsage, stats.UsageLimit)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(stats.CurrentUsage),
		Limit:        int(stats.UsageLimit),
		Unlimited:    stats.IsUnlimited,
		Message:      message,
	}, nil
}

// checkBranchLimitLocal checks branch limits using local subscription system (fallback)
func (ssg *ShopSubscriptionGuard) checkBranchLimitLocal(shopID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	// Count current branches for this shop
	var currentBranches int64
	if err := ssg.db.Model(&models.ShopBranch{}).Where("shop_id = ?", shopID).Count(&currentBranches).Error; err != nil {
		return nil, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedBranches {
		return &LimitCheckResult{
			Allowed:      true,
			CurrentUsage: int(currentBranches),
			Unlimited:    true,
			Message:      "Unlimited branches allowed",
		}, nil
	}

	// Check limit
	allowed := int(currentBranches) < subscription.SubscriptionTier.MaxBranchesPerShop
	message := fmt.Sprintf("Using %d of %d branches", currentBranches, subscription.SubscriptionTier.MaxBranchesPerShop)
	if !allowed {
		message = fmt.Sprintf("Branch limit reached (%d/%d). Upgrade your shop subscription to create more branches.",
			currentBranches, subscription.SubscriptionTier.MaxBranchesPerShop)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentBranches),
		Limit:        subscription.SubscriptionTier.MaxBranchesPerShop,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckQRCodeLimitWithContext checks if shop can generate more QR codes this month with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckQRCodeLimitWithContext(c *gin.Context, shopID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - QR code limits bypassed",
		}, nil
	}
	
	return ssg.CheckQRCodeLimit(shopID)
}

// CheckQRCodeLimit checks if shop can generate more QR codes this month
func (ssg *ShopSubscriptionGuard) CheckQRCodeLimit(shopID uuid.UUID) (*LimitCheckResult, error) {
	// Try centralized service first if available
	if ssg.subscriptionClient != nil {
		return ssg.checkQRCodeLimitCentralized(shopID)
	}

	// Fallback to local subscription system
	return ssg.checkQRCodeLimitLocal(shopID)
}

// checkQRCodeLimitCentralized checks QR code limits via centralized subscription service
func (ssg *ShopSubscriptionGuard) checkQRCodeLimitCentralized(shopID uuid.UUID) (*LimitCheckResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get usage stats from centralized service
	stats, err := ssg.subscriptionClient.GetUsageStats(ctx, shopID, "credit", "qr_codes")
	if err != nil {
		logrus.Warnf("Failed to get QR code usage stats from centralized service for shop %s: %v", shopID, err)
		// Fallback to local checking
		return ssg.checkQRCodeLimitLocal(shopID)
	}

	allowed := !stats.IsUnlimited && stats.CurrentUsage < stats.UsageLimit
	if stats.IsUnlimited {
		allowed = true
	}

	message := fmt.Sprintf("Using %d of %d QR codes this month", stats.CurrentUsage, stats.UsageLimit)
	if stats.IsUnlimited {
		message = "Unlimited QR codes allowed"
	} else if !allowed {
		message = fmt.Sprintf("Monthly QR code limit reached (%d/%d). Upgrade your subscription to generate more QR codes.",
			stats.CurrentUsage, stats.UsageLimit)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(stats.CurrentUsage),
		Limit:        int(stats.UsageLimit),
		Unlimited:    stats.IsUnlimited,
		Message:      message,
	}, nil
}

// checkQRCodeLimitLocal checks QR code limits using local subscription system (fallback)
func (ssg *ShopSubscriptionGuard) checkQRCodeLimitLocal(shopID uuid.UUID) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	// Check if unlimited
	if subscription.SubscriptionTier.UnlimitedQRCodes {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Unlimited QR codes allowed",
		}, nil
	}

	// Get shop monthly usage tracking
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	var monthlyUsage models.ShopMonthlyUsageTracking
	err = ssg.db.Where("shop_id = ? AND year = ? AND month = ?", shopID, year, month).
		First(&monthlyUsage).Error
	
	var currentQRCodes int
	if err == gorm.ErrRecordNotFound {
		// Create new record if doesn't exist
		monthlyUsage = models.ShopMonthlyUsageTracking{
			ShopID:           shopID,
			Year:             year,
			Month:            month,
			QRCodesGenerated: 0,
			APICallsMade:     0,
			CreditsConsumed:  0,
			LastResetDate:    now,
		}
		if err := ssg.db.Create(&monthlyUsage).Error; err != nil {
			return nil, err
		}
		currentQRCodes = 0
	} else if err != nil {
		return nil, err
	} else {
		currentQRCodes = monthlyUsage.QRCodesGenerated
	}

	// Check limit
	allowed := currentQRCodes < subscription.SubscriptionTier.MaxQRCodesPerMonth
	message := fmt.Sprintf("Using %d of %d QR codes this month", currentQRCodes, subscription.SubscriptionTier.MaxQRCodesPerMonth)
	if !allowed {
		message = fmt.Sprintf("QR code limit reached (%d/%d). Upgrade your shop subscription to generate more QR codes.",
			currentQRCodes, subscription.SubscriptionTier.MaxQRCodesPerMonth)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: currentQRCodes,
		Limit:        subscription.SubscriptionTier.MaxQRCodesPerMonth,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// CheckShopTypeAllowed checks if shop type is allowed for the subscription
func (ssg *ShopSubscriptionGuard) CheckShopTypeAllowed(shopID uuid.UUID, shopType string) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	// Check if shop type is allowed
	allowedTypes := subscription.SubscriptionTier.AllowedShopTypes
	for _, allowedType := range allowedTypes {
		if allowedType == shopType {
			return &LimitCheckResult{
				Allowed: true,
				Message: fmt.Sprintf("Shop type '%s' is allowed", shopType),
			}, nil
		}
	}

	return &LimitCheckResult{
		Allowed: false,
		Message: fmt.Sprintf("Shop type '%s' is not allowed in your subscription tier. Allowed types: %v",
			shopType, allowedTypes),
	}, nil
}

// CheckCreditBalanceWithContext checks if shop has enough credits with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckCreditBalanceWithContext(c *gin.Context, shopID uuid.UUID, requiredCredits int) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - credit balance checks bypassed",
		}, nil
	}
	
	return ssg.CheckCreditBalance(shopID, requiredCredits)
}

// CheckCreditBalance checks if shop has enough credits
func (ssg *ShopSubscriptionGuard) CheckCreditBalance(shopID uuid.UUID, requiredCredits int) (*LimitCheckResult, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No active subscription found for shop",
		}, err
	}

	allowed := subscription.CreditBalance >= requiredCredits
	message := fmt.Sprintf("Available credits: %d, Required: %d", subscription.CreditBalance, requiredCredits)
	if !allowed {
		message = fmt.Sprintf("Insufficient credits. Available: %d, Required: %d",
			subscription.CreditBalance, requiredCredits)
	}

	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: subscription.CreditBalance,
		Message:      message,
	}, nil
}

// CheckWebhookLimitWithContext checks if user can create more webhooks with internal API key bypass
func (ssg *ShopSubscriptionGuard) CheckWebhookLimitWithContext(c *gin.Context, userID uuid.UUID) (*LimitCheckResult, error) {
	// Bypass all limits for internal API keys
	if ssg.isInternalAPIKeyFromContext(c) {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Internal API key - webhook limits bypassed",
		}, nil
	}
	
	return ssg.CheckWebhookLimit(userID)
}

// CheckWebhookLimit checks if user can create more webhooks (based on user's shop subscriptions)
func (ssg *ShopSubscriptionGuard) CheckWebhookLimit(userID uuid.UUID) (*LimitCheckResult, error) {
	// Find user's primary shop (owner role)
	var shopUser models.ShopUser
	err := ssg.db.Where("user_id = ? AND role = ?", userID, "owner").
		Preload("Shop").
		Preload("Shop.Subscription").
		Preload("Shop.Subscription.SubscriptionTier").
		First(&shopUser).Error
	
	if err != nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "No shop subscription found for webhook limit check",
		}, err
	}
	
	if shopUser.Shop.Subscription == nil {
		return &LimitCheckResult{
			Allowed: false,
			Message: "Shop has no active subscription",
		}, fmt.Errorf("shop has no active subscription")
	}
	
	tier := shopUser.Shop.Subscription.SubscriptionTier
	
	// Count current webhooks for this user
	var currentWebhooks int64
	if err := ssg.db.Model(&models.Webhook{}).Where("user_id = ?", userID).Count(&currentWebhooks).Error; err != nil {
		return nil, err
	}
	
	// Check if unlimited
	if tier.MaxWebhooks == 0 {
		return &LimitCheckResult{
			Allowed:   true,
			Unlimited: true,
			Message:   "Unlimited webhooks allowed",
		}, nil
	}
	
	// Check limit
	allowed := int(currentWebhooks) < tier.MaxWebhooks
	message := fmt.Sprintf("Using %d of %d webhooks", currentWebhooks, tier.MaxWebhooks)
	if !allowed {
		message = fmt.Sprintf("Webhook limit reached (%d/%d). Upgrade your subscription to create more webhooks.",
			currentWebhooks, tier.MaxWebhooks)
	}
	
	return &LimitCheckResult{
		Allowed:      allowed,
		CurrentUsage: int(currentWebhooks),
		Limit:        tier.MaxWebhooks,
		Unlimited:    false,
		Message:      message,
	}, nil
}

// GetAnalyticsHistoryLimit returns the analytics history limit in days for a shop
func (ssg *ShopSubscriptionGuard) GetAnalyticsHistoryLimit(shopID uuid.UUID) (int, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return 0, err
	}

	return subscription.SubscriptionTier.AnalyticsHistoryDays, nil
}

// GetSupportLevel returns the support level for the shop
func (ssg *ShopSubscriptionGuard) GetSupportLevel(shopID uuid.UUID) (string, error) {
	subscription, err := ssg.GetShopActiveSubscription(shopID)
	if err != nil {
		return "none", err
	}

	return subscription.SubscriptionTier.SupportLevel, nil
}

// ConsumeShopCredits consumes credits from shop subscription
func (ssg *ShopSubscriptionGuard) ConsumeShopCredits(shopID uuid.UUID, credits int, userID uuid.UUID, reference string) error {
	// Start transaction
	tx := ssg.db.Begin()

	// Get shop subscription
	var subscription models.ShopSubscription
	if err := tx.Where("shop_id = ? AND status = ?", shopID, "active").
		First(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("no active subscription found for shop: %w", err)
	}

	// Check if enough credits
	if subscription.CreditBalance < credits {
		tx.Rollback()
		return fmt.Errorf("insufficient credits: have %d, need %d", subscription.CreditBalance, credits)
	}

	// Update credit balance
	subscription.CreditBalance -= credits
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update credit balance: %w", err)
	}

	// Create transaction record
	shopTransaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopID,
		ShopSubscriptionID: subscription.ID,
		InitiatedByUserID:  userID,
		Type:               "credit_use",
		Amount:             -credits,
		Description:        fmt.Sprintf("Credits consumed: %s", reference),
		Reference:          reference,
	}

	if err := tx.Create(&shopTransaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}