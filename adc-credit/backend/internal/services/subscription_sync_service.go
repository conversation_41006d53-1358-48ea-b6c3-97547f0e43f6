package services

import (
	"github.com/sirupsen/logrus"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"gorm.io/gorm"
)

// SubscriptionSyncService handles synchronization with ADC Subscription Service
type SubscriptionSyncService struct {
	db                 *gorm.DB
	subscriptionClient *subscriptionSDK.Client
	shopOrgService     *ShopOrganizationService
	logger             *logrus.Logger
}

// NewSubscriptionSyncService creates a new subscription sync service
func NewSubscriptionSyncService(
	db *gorm.DB,
	subscriptionClient *subscriptionSDK.Client,
	shopOrgService *ShopOrganizationService,
	logger *logrus.Logger,
) *SubscriptionSyncService {
	return &SubscriptionSyncService{
		db:                 db,
		subscriptionClient: subscriptionClient,
		shopOrgService:     shopOrgService,
		logger:             logger,
	}
}

// SyncAllShops synchronizes all shops with subscription service
func (s *SubscriptionSyncService) SyncAllShops() error {
	s.logger.Info("Starting subscription sync for all shops")
	// TODO: Implement sync logic
	return nil
}