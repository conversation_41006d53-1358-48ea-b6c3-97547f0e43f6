package services

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// TranslationTrigger represents different types of triggers that can initiate auto-translation
type TranslationTriggerType string

const (
	TriggerContentChange     TranslationTriggerType = "content_change"
	TriggerNewContent        TranslationTriggerType = "new_content"
	TriggerLanguageRequest   TranslationTriggerType = "language_request"
	TriggerScheduled         TranslationTriggerType = "scheduled"
	TriggerMissingTranslation TranslationTriggerType = "missing_translation"
)

// TranslationTriggerEvent represents an event that may trigger auto-translation
type TranslationTriggerEvent struct {
	ID            uuid.UUID              `json:"id"`
	Type          TranslationTriggerType `json:"type"`
	ShopID        uuid.UUID              `json:"shop_id"`
	UserID        *uuid.UUID             `json:"user_id,omitempty"`
	SourceText    string                 `json:"source_text"`
	TranslationKey string                `json:"translation_key"`
	Namespace     string                 `json:"namespace,omitempty"`
	TargetLanguages []string             `json:"target_languages"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt     time.Time              `json:"created_at"`
	Priority      int                    `json:"priority"` // 1 = high, 2 = medium, 3 = low
}

// TranslationTriggerConfig represents the configuration for translation triggers
type TranslationTriggerConfig struct {
	ContentChangeThreshold time.Duration `json:"content_change_threshold"` // Minimum time between content changes
	BatchProcessingDelay   time.Duration `json:"batch_processing_delay"`   // Delay before processing batched events
	MaxBatchSize          int           `json:"max_batch_size"`           // Maximum number of events to process in a batch
	EnableRealTimeMode    bool          `json:"enable_real_time_mode"`    // Process events immediately
	ConfidenceThreshold   float64       `json:"confidence_threshold"`     // Minimum confidence for auto-translation
	SupportedLanguages    []string      `json:"supported_languages"`      // Languages to auto-translate to
}

// SmartTranslationTriggers handles smart triggers for auto-translation
type SmartTranslationTriggers struct {
	settingsService          *SettingsService
	autoTranslationProcessor *AutoTranslationProcessor
	eventQueue              chan *TranslationTriggerEvent
	batchEvents             map[uuid.UUID][]*TranslationTriggerEvent // Batched events by shop
	lastProcessed           map[string]time.Time                    // Last processed time for content changes
	config                  TranslationTriggerConfig
	isRunning               bool
	mutex                   sync.RWMutex
	ctx                     context.Context
	cancel                  context.CancelFunc
}

// NewSmartTranslationTriggers creates a new smart translation triggers system
func NewSmartTranslationTriggers(settingsService *SettingsService, processor *AutoTranslationProcessor) *SmartTranslationTriggers {
	ctx, cancel := context.WithCancel(context.Background())

	return &SmartTranslationTriggers{
		settingsService:          settingsService,
		autoTranslationProcessor: processor,
		eventQueue:              make(chan *TranslationTriggerEvent, 500),
		batchEvents:             make(map[uuid.UUID][]*TranslationTriggerEvent),
		lastProcessed:           make(map[string]time.Time),
		config: TranslationTriggerConfig{
			ContentChangeThreshold: 30 * time.Second,
			BatchProcessingDelay:   5 * time.Minute,
			MaxBatchSize:          50,
			EnableRealTimeMode:    false,
			ConfidenceThreshold:   0.8,
			SupportedLanguages:    []string{"en", "es", "fr", "de", "ja", "zh"},
		},
		isRunning: false,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Start starts the smart translation triggers system
func (t *SmartTranslationTriggers) Start() error {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if t.isRunning {
		return fmt.Errorf("smart translation triggers already running")
	}

	t.isRunning = true
	log.Printf("Starting smart translation triggers system")

	// Start event processor
	go t.eventProcessor()

	// Start batch processor
	go t.batchProcessor()

	// Start periodic cleanup
	go t.periodicCleanup()

	log.Printf("Smart translation triggers started successfully")
	return nil
}

// Stop stops the smart translation triggers system
func (t *SmartTranslationTriggers) Stop() error {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if !t.isRunning {
		return fmt.Errorf("smart translation triggers not running")
	}

	log.Printf("Stopping smart translation triggers...")
	t.cancel()
	t.isRunning = false

	log.Printf("Smart translation triggers stopped")
	return nil
}

// TriggerContentChange triggers auto-translation when content changes
func (t *SmartTranslationTriggers) TriggerContentChange(shopID uuid.UUID, translationKey, oldContent, newContent string) error {
	// Check if content actually changed significantly
	if !t.isSignificantChange(oldContent, newContent) {
		return nil
	}

	// Check if enough time has passed since last change for this key
	changeKey := fmt.Sprintf("%s:%s", shopID.String(), translationKey)
	if lastTime, exists := t.lastProcessed[changeKey]; exists {
		if time.Since(lastTime) < t.config.ContentChangeThreshold {
			log.Printf("Content change for %s too recent, skipping", translationKey)
			return nil
		}
	}

	// Get shop settings to determine target languages
	settings, err := t.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		return fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		return nil // Auto-translation not enabled
	}

	// Check if smart triggers are enabled
	smartTriggers, ok := settings["smart_triggers"].(bool)
	if !ok || !smartTriggers {
		return nil // Smart triggers not enabled
	}

	event := &TranslationTriggerEvent{
		ID:              uuid.New(),
		Type:            TriggerContentChange,
		ShopID:          shopID,
		SourceText:      newContent,
		TranslationKey:  translationKey,
		TargetLanguages: t.config.SupportedLanguages,
		CreatedAt:       time.Now(),
		Priority:        2, // Medium priority for content changes
		Metadata: map[string]interface{}{
			"old_content": oldContent,
			"change_size": len(newContent) - len(oldContent),
		},
	}

	return t.submitEvent(event)
}

// TriggerNewContent triggers auto-translation for new content
func (t *SmartTranslationTriggers) TriggerNewContent(shopID uuid.UUID, translationKey, content, namespace string) error {
	// Get shop settings
	settings, err := t.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		return fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		return nil
	}

	// Check if smart triggers are enabled
	smartTriggers, ok := settings["smart_triggers"].(bool)
	if !ok || !smartTriggers {
		return nil
	}

	event := &TranslationTriggerEvent{
		ID:              uuid.New(),
		Type:            TriggerNewContent,
		ShopID:          shopID,
		SourceText:      content,
		TranslationKey:  translationKey,
		Namespace:       namespace,
		TargetLanguages: t.config.SupportedLanguages,
		CreatedAt:       time.Now(),
		Priority:        1, // High priority for new content
		Metadata: map[string]interface{}{
			"content_length": len(content),
			"namespace":     namespace,
		},
	}

	return t.submitEvent(event)
}

// TriggerLanguageRequest triggers auto-translation when a specific language is requested
func (t *SmartTranslationTriggers) TriggerLanguageRequest(shopID uuid.UUID, userID *uuid.UUID, translationKey, sourceText, targetLanguage string) error {
	// Get shop settings
	settings, err := t.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		return fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		return nil
	}

	// Check if real-time mode is enabled for immediate processing
	realTimeMode, ok := settings["real_time_mode"].(bool)
	priority := 2 // Medium priority by default
	if ok && realTimeMode {
		priority = 1 // High priority for real-time requests
	}

	event := &TranslationTriggerEvent{
		ID:              uuid.New(),
		Type:            TriggerLanguageRequest,
		ShopID:          shopID,
		UserID:          userID,
		SourceText:      sourceText,
		TranslationKey:  translationKey,
		TargetLanguages: []string{targetLanguage},
		CreatedAt:       time.Now(),
		Priority:        priority,
		Metadata: map[string]interface{}{
			"requested_language": targetLanguage,
			"user_triggered":    userID != nil,
		},
	}

	return t.submitEvent(event)
}

// TriggerMissingTranslation triggers auto-translation when a translation is missing
func (t *SmartTranslationTriggers) TriggerMissingTranslation(shopID uuid.UUID, translationKey, fallbackText, targetLanguage string) error {
	// Get shop settings
	settings, err := t.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		return fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		return nil
	}

	// Check if smart triggers are enabled
	smartTriggers, ok := settings["smart_triggers"].(bool)
	if !ok || !smartTriggers {
		return nil
	}

	event := &TranslationTriggerEvent{
		ID:              uuid.New(),
		Type:            TriggerMissingTranslation,
		ShopID:          shopID,
		SourceText:      fallbackText,
		TranslationKey:  translationKey,
		TargetLanguages: []string{targetLanguage},
		CreatedAt:       time.Now(),
		Priority:        3, // Low priority for missing translations
		Metadata: map[string]interface{}{
			"fallback_used":    true,
			"target_language": targetLanguage,
		},
	}

	return t.submitEvent(event)
}

// submitEvent submits an event to the processing queue
func (t *SmartTranslationTriggers) submitEvent(event *TranslationTriggerEvent) error {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	if !t.isRunning {
		return fmt.Errorf("smart translation triggers not running")
	}

	select {
	case t.eventQueue <- event:
		log.Printf("Translation trigger event submitted: %s for shop %s", event.Type, event.ShopID)
		return nil
	case <-t.ctx.Done():
		return fmt.Errorf("system is shutting down")
	default:
		return fmt.Errorf("event queue is full")
	}
}

// eventProcessor processes translation trigger events
func (t *SmartTranslationTriggers) eventProcessor() {
	log.Printf("Translation trigger event processor started")

	for {
		select {
		case event := <-t.eventQueue:
			t.processEvent(event)
		case <-t.ctx.Done():
			log.Printf("Translation trigger event processor stopping")
			return
		}
	}
}

// processEvent processes a single translation trigger event
func (t *SmartTranslationTriggers) processEvent(event *TranslationTriggerEvent) {
	log.Printf("Processing translation trigger event: %s for shop %s", event.Type, event.ShopID)

	// Get shop settings to determine processing mode
	settings, err := t.settingsService.GetShopAutoTranslationSettings(event.ShopID)
	if err != nil {
		log.Printf("Failed to get shop settings for event %s: %v", event.ID, err)
		return
	}

	// Check processing mode
	realTimeMode, _ := settings["real_time_mode"].(bool)
	batchProcessing, _ := settings["batch_processing"].(bool)

	if realTimeMode || event.Priority == 1 {
		// Process immediately
		t.processEventImmediately(event)
	} else if batchProcessing {
		// Add to batch
		t.addToBatch(event)
	} else {
		// Default: process immediately
		t.processEventImmediately(event)
	}

	// Update last processed time
	changeKey := fmt.Sprintf("%s:%s", event.ShopID.String(), event.TranslationKey)
	t.lastProcessed[changeKey] = time.Now()
}

// processEventImmediately processes an event immediately
func (t *SmartTranslationTriggers) processEventImmediately(event *TranslationTriggerEvent) {
	for _, targetLanguage := range event.TargetLanguages {
		err := t.autoTranslationProcessor.SubmitShopTranslationJob(
			event.ShopID,
			event.TranslationKey,
			event.SourceText,
			targetLanguage,
		)
		if err != nil {
			log.Printf("Failed to submit translation job for event %s, language %s: %v", 
				event.ID, targetLanguage, err)
		} else {
			log.Printf("Translation job submitted for event %s, language %s", 
				event.ID, targetLanguage)
		}
	}
}

// addToBatch adds an event to the batch processor
func (t *SmartTranslationTriggers) addToBatch(event *TranslationTriggerEvent) {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if t.batchEvents[event.ShopID] == nil {
		t.batchEvents[event.ShopID] = make([]*TranslationTriggerEvent, 0)
	}

	t.batchEvents[event.ShopID] = append(t.batchEvents[event.ShopID], event)
	log.Printf("Added event %s to batch for shop %s (batch size: %d)", 
		event.ID, event.ShopID, len(t.batchEvents[event.ShopID]))
}

// batchProcessor processes batched events periodically
func (t *SmartTranslationTriggers) batchProcessor() {
	log.Printf("Translation trigger batch processor started")
	
	ticker := time.NewTicker(t.config.BatchProcessingDelay)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			t.processBatches()
		case <-t.ctx.Done():
			log.Printf("Translation trigger batch processor stopping")
			return
		}
	}
}

// processBatches processes all pending batches
func (t *SmartTranslationTriggers) processBatches() {
	t.mutex.Lock()
	batchesToProcess := make(map[uuid.UUID][]*TranslationTriggerEvent)
	for shopID, events := range t.batchEvents {
		if len(events) > 0 {
			batchesToProcess[shopID] = make([]*TranslationTriggerEvent, len(events))
			copy(batchesToProcess[shopID], events)
			t.batchEvents[shopID] = t.batchEvents[shopID][:0] // Clear the batch
		}
	}
	t.mutex.Unlock()

	for shopID, events := range batchesToProcess {
		log.Printf("Processing batch of %d events for shop %s", len(events), shopID)
		for _, event := range events {
			t.processEventImmediately(event)
		}
	}
}

// periodicCleanup cleans up old entries periodically
func (t *SmartTranslationTriggers) periodicCleanup() {
	log.Printf("Translation trigger cleanup processor started")
	
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			t.cleanup()
		case <-t.ctx.Done():
			log.Printf("Translation trigger cleanup processor stopping")
			return
		}
	}
}

// cleanup removes old entries from memory
func (t *SmartTranslationTriggers) cleanup() {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	// Clean up last processed times older than 24 hours
	cutoff := time.Now().Add(-24 * time.Hour)
	for key, lastTime := range t.lastProcessed {
		if lastTime.Before(cutoff) {
			delete(t.lastProcessed, key)
		}
	}

	log.Printf("Cleaned up old trigger data, remaining entries: %d", len(t.lastProcessed))
}

// isSignificantChange determines if a content change is significant enough to trigger translation
func (t *SmartTranslationTriggers) isSignificantChange(oldContent, newContent string) bool {
	// Basic checks
	if oldContent == newContent {
		return false
	}

	// Check if change is just whitespace
	if strings.TrimSpace(oldContent) == strings.TrimSpace(newContent) {
		return false
	}

	// Check minimum change threshold (e.g., at least 10% change or 5 characters)
	minChangeChars := 5
	changeRatio := 0.1
	
	oldLen := len(oldContent)
	newLen := len(newContent)
	
	// Check absolute character change
	if abs(newLen-oldLen) < minChangeChars {
		return false
	}
	
	// Check relative change
	if oldLen > 0 {
		ratio := float64(abs(newLen-oldLen)) / float64(oldLen)
		if ratio < changeRatio {
			return false
		}
	}

	return true
}

// GetStatus returns the current status of the smart translation triggers
func (t *SmartTranslationTriggers) GetStatus() map[string]interface{} {
	t.mutex.RLock()
	defer t.mutex.RUnlock()

	totalBatchedEvents := 0
	for _, events := range t.batchEvents {
		totalBatchedEvents += len(events)
	}

	return map[string]interface{}{
		"running":              t.isRunning,
		"queue_length":         len(t.eventQueue),
		"queue_capacity":       cap(t.eventQueue),
		"batched_events":       totalBatchedEvents,
		"tracked_keys":         len(t.lastProcessed),
		"batch_processing_shops": len(t.batchEvents),
		"config":               t.config,
	}
}

// Helper function for absolute value
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}