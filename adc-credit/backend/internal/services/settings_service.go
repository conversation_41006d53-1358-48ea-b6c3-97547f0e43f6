package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/google/uuid"
)

// SettingScope represents the scope of a setting
type SettingScope string

const (
	ScopeGlobal       SettingScope = "global"
	ScopeOrganization SettingScope = "organization"
	ScopeUser         SettingScope = "user"
	ScopeShop         SettingScope = "shop"
)

// SettingDataType represents the data type of a setting
type SettingDataType string

const (
	DataTypeString  SettingDataType = "string"
	DataTypeInteger SettingDataType = "integer"
	DataTypeFloat   SettingDataType = "float"
	DataTypeBoolean SettingDataType = "boolean"
	DataTypeJSON    SettingDataType = "json"
	DataTypeArray   SettingDataType = "array"
	DataTypeObject  SettingDataType = "object"
)

// Setting represents a configuration setting
type Setting struct {
	ID                 uuid.UUID               `json:"id"`
	Key                string                  `json:"key"`
	Name               string                  `json:"name"`
	Description        string                  `json:"description,omitempty"`
	Value              interface{}             `json:"value"`
	DataType           SettingDataType         `json:"data_type"`
	DefaultValue       interface{}             `json:"default_value,omitempty"`
	Scope              SettingScope            `json:"scope"`
	Visibility         string                  `json:"visibility"`
	OrganizationID     *uuid.UUID              `json:"organization_id,omitempty"`
	UserID             *uuid.UUID              `json:"user_id,omitempty"`
	ShopID             *uuid.UUID              `json:"shop_id,omitempty"`
	ServiceName        string                  `json:"service_name,omitempty"`
	Environment        string                  `json:"environment,omitempty"`
	Category           string                  `json:"category,omitempty"`
	Tags               []string                `json:"tags,omitempty"`
	Metadata           map[string]interface{}  `json:"metadata,omitempty"`
	ValidationSchema   map[string]interface{}  `json:"validation_schema,omitempty"`
	IsRequired         bool                    `json:"is_required"`
	IsReadonly         bool                    `json:"is_readonly"`
	IsEncrypted        bool                    `json:"is_encrypted"`
	IsActive           bool                    `json:"is_active"`
	IsSystem           bool                    `json:"is_system"`
	Version            int                     `json:"version"`
	CreatedAt          time.Time               `json:"created_at"`
	UpdatedAt          time.Time               `json:"updated_at"`
	CreatedBy          *uuid.UUID              `json:"created_by,omitempty"`
	UpdatedBy          *uuid.UUID              `json:"updated_by,omitempty"`
}

// CreateSettingRequest represents a request to create a new setting
type CreateSettingRequest struct {
	Key                string                  `json:"key"`
	Name               string                  `json:"name"`
	Description        string                  `json:"description,omitempty"`
	Value              interface{}             `json:"value"`
	DataType           SettingDataType         `json:"data_type"`
	DefaultValue       interface{}             `json:"default_value,omitempty"`
	Scope              SettingScope            `json:"scope"`
	Visibility         string                  `json:"visibility,omitempty"`
	OrganizationID     *uuid.UUID              `json:"organization_id,omitempty"`
	UserID             *uuid.UUID              `json:"user_id,omitempty"`
	ShopID             *uuid.UUID              `json:"shop_id,omitempty"`
	ServiceName        string                  `json:"service_name,omitempty"`
	Environment        string                  `json:"environment,omitempty"`
	Category           string                  `json:"category,omitempty"`
	Tags               []string                `json:"tags,omitempty"`
	Metadata           map[string]interface{}  `json:"metadata,omitempty"`
	ValidationSchema   map[string]interface{}  `json:"validation_schema,omitempty"`
	IsRequired         bool                    `json:"is_required"`
	IsReadonly         bool                    `json:"is_readonly"`
	IsEncrypted        bool                    `json:"is_encrypted"`
}

// SettingsQuery represents a query for retrieving settings
type SettingsQuery struct {
	Keys           []string          `json:"keys,omitempty"`
	Scope          *SettingScope     `json:"scope,omitempty"`
	OrganizationID *uuid.UUID        `json:"organization_id,omitempty"`
	UserID         *uuid.UUID        `json:"user_id,omitempty"`
	ShopID         *uuid.UUID        `json:"shop_id,omitempty"`
	ServiceName    *string           `json:"service_name,omitempty"`
	Environment    *string           `json:"environment,omitempty"`
	Category       *string           `json:"category,omitempty"`
	Tags           []string          `json:"tags,omitempty"`
	IsActive       *bool             `json:"is_active,omitempty"`
	IsSystem       *bool             `json:"is_system,omitempty"`
	Limit          int               `json:"limit,omitempty"`
	Offset         int               `json:"offset,omitempty"`
}

// SettingsResult represents the result of a settings query
type SettingsResult struct {
	Settings []Setting `json:"settings"`
	Total    int       `json:"total"`
	Offset   int       `json:"offset"`
	Limit    int       `json:"limit"`
	HasMore  bool      `json:"has_more"`
}

// Auto-translation specific setting keys
var AutoTranslationSettings = struct {
	USER struct {
		ENABLED              string
		TARGET_LANGUAGE      string
		CONFIDENCE_THRESHOLD string
		FALLBACK_ENABLED     string
	}
	SHOP struct {
		ENABLED         string
		BATCH_PROCESSING string
		REAL_TIME_MODE   string
		SMART_TRIGGERS   string
	}
	GLOBAL struct {
		DEFAULT_CONFIDENCE_THRESHOLD string
		SUPPORTED_LANGUAGES          string
		MAX_RETRIES                  string
	}
}{
	USER: struct {
		ENABLED              string
		TARGET_LANGUAGE      string
		CONFIDENCE_THRESHOLD string
		FALLBACK_ENABLED     string
	}{
		ENABLED:              "user.auto_translation.enabled",
		TARGET_LANGUAGE:      "user.auto_translation.target_language",
		CONFIDENCE_THRESHOLD: "user.auto_translation.confidence_threshold",
		FALLBACK_ENABLED:     "user.auto_translation.fallback_enabled",
	},
	SHOP: struct {
		ENABLED         string
		BATCH_PROCESSING string
		REAL_TIME_MODE   string
		SMART_TRIGGERS   string
	}{
		ENABLED:         "shop.auto_translation.enabled",
		BATCH_PROCESSING: "shop.auto_translation.batch_processing",
		REAL_TIME_MODE:   "shop.auto_translation.real_time_mode",
		SMART_TRIGGERS:   "shop.auto_translation.smart_triggers",
	},
	GLOBAL: struct {
		DEFAULT_CONFIDENCE_THRESHOLD string
		SUPPORTED_LANGUAGES          string
		MAX_RETRIES                  string
	}{
		DEFAULT_CONFIDENCE_THRESHOLD: "global.auto_translation.default_confidence_threshold",
		SUPPORTED_LANGUAGES:          "global.auto_translation.supported_languages",
		MAX_RETRIES:                  "global.auto_translation.max_retries",
	},
}

// SettingsService handles communication with the ADC Settings Service
type SettingsService struct {
	baseURL   string
	apiKey    string
	clientID  string
	secret    string
	client    *http.Client
}

// NewSettingsService creates a new Settings Service client
func NewSettingsService() *SettingsService {
	return &SettingsService{
		baseURL:  getEnvOrDefault("SETTINGS_SERVICE_URL", "http://localhost:9200"),
		apiKey:   getEnvOrDefault("SETTINGS_INTERNAL_API_KEY", "settings-internal-key-1"),
		clientID: getEnvOrDefault("SETTINGS_CLIENT_ID", "adc-credit"),
		secret:   getEnvOrDefault("SETTINGS_CLIENT_SECRET", "credit-settings-secret"),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// makeRequest makes an HTTP request to the Settings Service
func (s *SettingsService) makeRequest(method, path string, body interface{}) (*http.Response, error) {
	url := s.baseURL + path

	var requestBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		requestBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, url, requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "adc-credit-service/1.0.0")
	if s.apiKey != "" {
		req.Header.Set("X-API-Key", s.apiKey)
	}

	log.Printf("Making %s request to Settings Service: %s", method, url)

	resp, err := s.client.Do(req)
	if err != nil {
		log.Printf("Settings Service request failed: %v", err)
		return nil, fmt.Errorf("request failed: %w", err)
	}

	if resp.StatusCode >= 400 {
		responseBody, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		log.Printf("Settings Service returned error status %d: %s", resp.StatusCode, string(responseBody))
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	log.Printf("Settings Service request completed successfully with status %d", resp.StatusCode)
	return resp, nil
}

// CreateSetting creates a new setting
func (s *SettingsService) CreateSetting(request CreateSettingRequest) (*Setting, error) {
	resp, err := s.makeRequest("POST", "/api/v1/settings", request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var setting Setting
	if err := json.NewDecoder(resp.Body).Decode(&setting); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &setting, nil
}

// GetSetting retrieves a setting by key and context
func (s *SettingsService) GetSetting(key string, scope SettingScope, organizationID, userID, shopID *uuid.UUID) (*Setting, error) {
	query := SettingsQuery{
		Keys:           []string{key},
		Scope:          &scope,
		OrganizationID: organizationID,
		UserID:         userID,
		ShopID:         shopID,
		Limit:          1,
	}

	result, err := s.GetSettings(query)
	if err != nil {
		return nil, err
	}

	if len(result.Settings) == 0 {
		return nil, fmt.Errorf("setting not found: %s", key)
	}

	return &result.Settings[0], nil
}

// GetSettings retrieves settings based on the provided query
func (s *SettingsService) GetSettings(query SettingsQuery) (*SettingsResult, error) {
	resp, err := s.makeRequest("POST", "/api/v1/settings/search", query)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result SettingsResult
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// Health checks the health of the Settings Service
func (s *SettingsService) Health() error {
	resp, err := s.makeRequest("GET", "/health", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return nil
}

// Auto-translation specific methods

// GetUserAutoTranslationSettings retrieves auto-translation settings for a user
func (s *SettingsService) GetUserAutoTranslationSettings(userID uuid.UUID) (map[string]interface{}, error) {
	settings := make(map[string]interface{})

	// Default values
	defaults := map[string]interface{}{
		"enabled":              false,
		"target_language":      "en",
		"confidence_threshold": 0.8,
		"fallback_enabled":     true,
	}

	// Try to get each setting, use default if not found
	keys := []string{
		AutoTranslationSettings.USER.ENABLED,
		AutoTranslationSettings.USER.TARGET_LANGUAGE,
		AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD,
		AutoTranslationSettings.USER.FALLBACK_ENABLED,
	}

	for _, key := range keys {
		setting, err := s.GetSetting(key, ScopeUser, nil, &userID, nil)
		if err != nil {
			// Use default value if setting not found
			switch key {
			case AutoTranslationSettings.USER.ENABLED:
				settings["enabled"] = defaults["enabled"]
			case AutoTranslationSettings.USER.TARGET_LANGUAGE:
				settings["target_language"] = defaults["target_language"]
			case AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD:
				settings["confidence_threshold"] = defaults["confidence_threshold"]
			case AutoTranslationSettings.USER.FALLBACK_ENABLED:
				settings["fallback_enabled"] = defaults["fallback_enabled"]
			}
		} else {
			switch key {
			case AutoTranslationSettings.USER.ENABLED:
				settings["enabled"] = setting.Value
			case AutoTranslationSettings.USER.TARGET_LANGUAGE:
				settings["target_language"] = setting.Value
			case AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD:
				settings["confidence_threshold"] = setting.Value
			case AutoTranslationSettings.USER.FALLBACK_ENABLED:
				settings["fallback_enabled"] = setting.Value
			}
		}
	}

	return settings, nil
}

// GetShopAutoTranslationSettings retrieves auto-translation settings for a shop
func (s *SettingsService) GetShopAutoTranslationSettings(shopID uuid.UUID) (map[string]interface{}, error) {
	settings := make(map[string]interface{})

	// Default values
	defaults := map[string]interface{}{
		"enabled":         false,
		"batch_processing": true,
		"real_time_mode":   false,
		"smart_triggers":   true,
	}

	// Try to get each setting, use default if not found
	keys := []string{
		AutoTranslationSettings.SHOP.ENABLED,
		AutoTranslationSettings.SHOP.BATCH_PROCESSING,
		AutoTranslationSettings.SHOP.REAL_TIME_MODE,
		AutoTranslationSettings.SHOP.SMART_TRIGGERS,
	}

	for _, key := range keys {
		setting, err := s.GetSetting(key, ScopeShop, nil, nil, &shopID)
		if err != nil {
			// Use default value if setting not found
			switch key {
			case AutoTranslationSettings.SHOP.ENABLED:
				settings["enabled"] = defaults["enabled"]
			case AutoTranslationSettings.SHOP.BATCH_PROCESSING:
				settings["batch_processing"] = defaults["batch_processing"]
			case AutoTranslationSettings.SHOP.REAL_TIME_MODE:
				settings["real_time_mode"] = defaults["real_time_mode"]
			case AutoTranslationSettings.SHOP.SMART_TRIGGERS:
				settings["smart_triggers"] = defaults["smart_triggers"]
			}
		} else {
			switch key {
			case AutoTranslationSettings.SHOP.ENABLED:
				settings["enabled"] = setting.Value
			case AutoTranslationSettings.SHOP.BATCH_PROCESSING:
				settings["batch_processing"] = setting.Value
			case AutoTranslationSettings.SHOP.REAL_TIME_MODE:
				settings["real_time_mode"] = setting.Value
			case AutoTranslationSettings.SHOP.SMART_TRIGGERS:
				settings["smart_triggers"] = setting.Value
			}
		}
	}

	return settings, nil
}

// SetUserAutoTranslationSetting sets an auto-translation setting for a user
func (s *SettingsService) SetUserAutoTranslationSetting(userID uuid.UUID, key string, value interface{}) error {
	setting := CreateSettingRequest{
		Key:         key,
		Name:        getSettingDisplayName(key),
		Description: getSettingDescription(key),
		Value:       value,
		DataType:    inferDataType(value),
		Scope:       ScopeUser,
		UserID:      &userID,
		Visibility:  "private",
		Category:    "translation",
		Tags:        []string{"auto-translation", "user-preference"},
	}

	_, err := s.CreateSetting(setting)
	return err
}

// SetShopAutoTranslationSetting sets an auto-translation setting for a shop
func (s *SettingsService) SetShopAutoTranslationSetting(shopID uuid.UUID, key string, value interface{}) error {
	setting := CreateSettingRequest{
		Key:         key,
		Name:        getSettingDisplayName(key),
		Description: getSettingDescription(key),
		Value:       value,
		DataType:    inferDataType(value),
		Scope:       ScopeShop,
		ShopID:      &shopID,
		Visibility:  "private",
		Category:    "translation",
		Tags:        []string{"auto-translation", "shop-preference"},
	}

	_, err := s.CreateSetting(setting)
	return err
}

// Helper functions

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func inferDataType(value interface{}) SettingDataType {
	switch value.(type) {
	case string:
		return DataTypeString
	case int, int32, int64:
		return DataTypeInteger
	case float32, float64:
		return DataTypeFloat
	case bool:
		return DataTypeBoolean
	case []interface{}:
		return DataTypeArray
	case map[string]interface{}:
		return DataTypeObject
	default:
		return DataTypeString
	}
}

func getSettingDisplayName(key string) string {
	names := map[string]string{
		AutoTranslationSettings.USER.ENABLED:              "Auto Translation Enabled",
		AutoTranslationSettings.USER.TARGET_LANGUAGE:      "Target Language",
		AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD: "Confidence Threshold",
		AutoTranslationSettings.USER.FALLBACK_ENABLED:     "Fallback Enabled",
		AutoTranslationSettings.SHOP.ENABLED:              "Shop Auto Translation Enabled",
		AutoTranslationSettings.SHOP.BATCH_PROCESSING:     "Batch Processing",
		AutoTranslationSettings.SHOP.REAL_TIME_MODE:       "Real-time Mode",
		AutoTranslationSettings.SHOP.SMART_TRIGGERS:       "Smart Triggers",
	}
	if name, exists := names[key]; exists {
		return name
	}
	return key
}

func getSettingDescription(key string) string {
	descriptions := map[string]string{
		AutoTranslationSettings.USER.ENABLED:              "Whether auto-translation is enabled for the user",
		AutoTranslationSettings.USER.TARGET_LANGUAGE:      "Preferred target language for auto-translation",
		AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD: "Minimum confidence score required for auto-translation",
		AutoTranslationSettings.USER.FALLBACK_ENABLED:     "Whether to use fallback translations when auto-translation fails",
		AutoTranslationSettings.SHOP.ENABLED:              "Whether auto-translation is enabled for the shop",
		AutoTranslationSettings.SHOP.BATCH_PROCESSING:     "Whether to use batch processing for auto-translation",
		AutoTranslationSettings.SHOP.REAL_TIME_MODE:       "Whether to enable real-time auto-translation",
		AutoTranslationSettings.SHOP.SMART_TRIGGERS:       "Whether to use smart triggers for auto-translation",
	}
	if desc, exists := descriptions[key]; exists {
		return desc
	}
	return ""
}