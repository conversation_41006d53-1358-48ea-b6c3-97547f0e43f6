package config

import (
	"fmt"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// ConfigValidationError represents a configuration validation error
type ConfigValidationError struct {
	Field   string
	Value   string
	Message string
}

func (e ConfigValidationError) Error() string {
	return fmt.Sprintf("config validation error for %s='%s': %s", e.Field, e.Value, e.Message)
}

// ValidationResult holds the result of configuration validation
type ValidationResult struct {
	IsValid bool
	Errors  []ConfigValidationError
	Warnings []string
}

// ValidateConfiguration performs comprehensive configuration validation
func ValidateConfiguration() *ValidationResult {
	result := &ValidationResult{
		IsValid:  true,
		Errors:   []ConfigValidationError{},
		Warnings: []string{},
	}

	// Validate database configuration
	validateDatabaseConfig(result)
	
	// Validate JWT configuration
	validateJWTConfig(result)
	
	// Validate server configuration
	validateServerConfig(result)
	
	// Validate external service configuration
	validateExternalServiceConfig(result)
	
	// Validate security configuration
	validateSecurityConfig(result)
	
	// Validate rate limiting configuration
	validateRateLimitConfig(result)
	
	// Validate subscription configuration
	validateSubscriptionConfig(result)

	// Log validation results
	logValidationResults(result)

	return result
}

// validateDatabaseConfig validates database-related configuration
func validateDatabaseConfig(result *ValidationResult) {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		result.addError("DATABASE_URL", "", "database URL is required")
		return
	}

	// Parse and validate database URL
	parsedURL, err := url.Parse(dbURL)
	if err != nil {
		result.addError("DATABASE_URL", dbURL, "invalid database URL format")
		return
	}

	if parsedURL.Scheme != "postgresql" && parsedURL.Scheme != "postgres" {
		result.addError("DATABASE_URL", parsedURL.Scheme, "only PostgreSQL databases are supported")
	}

	if parsedURL.Host == "" {
		result.addError("DATABASE_URL", "", "database host is required")
	}

	// Validate connection pool settings
	validateIntRange(result, "DB_MAX_IDLE_CONNECTIONS", 1, 100, 10)
	validateIntRange(result, "DB_MAX_OPEN_CONNECTIONS", 1, 1000, 100)
	validateDurationRange(result, "DB_CONNECTION_MAX_LIFETIME", time.Minute, 24*time.Hour, time.Hour)
}

// validateJWTConfig validates JWT configuration
func validateJWTConfig(result *ValidationResult) {
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		result.addError("JWT_SECRET", "", "JWT secret is required")
	} else if len(jwtSecret) < 32 {
		result.addError("JWT_SECRET", "***", "JWT secret must be at least 32 characters long")
	} else if strings.Contains(strings.ToLower(jwtSecret), "development") || 
		strings.Contains(strings.ToLower(jwtSecret), "test") {
		result.addWarning("JWT_SECRET appears to be a development/test key")
	}

	refreshSecret := os.Getenv("JWT_REFRESH_SECRET")
	if refreshSecret == "" {
		result.addError("JWT_REFRESH_SECRET", "", "JWT refresh secret is required")
	} else if len(refreshSecret) < 32 {
		result.addError("JWT_REFRESH_SECRET", "***", "JWT refresh secret must be at least 32 characters long")
	}

	if jwtSecret == refreshSecret {
		result.addError("JWT_SECRETS", "", "JWT secret and refresh secret must be different")
	}
}

// validateServerConfig validates server configuration
func validateServerConfig(result *ValidationResult) {
	// Validate port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8400" // default
	}
	
	if portNum, err := strconv.Atoi(port); err != nil {
		result.addError("PORT", port, "port must be a valid integer")
	} else if portNum < 1 || portNum > 65535 {
		result.addError("PORT", port, "port must be between 1 and 65535")
	}

	// Validate CORS origin
	corsOrigin := os.Getenv("CORS_ORIGIN")
	if corsOrigin != "" {
		if corsOrigin != "*" {
			if _, err := url.Parse(corsOrigin); err != nil {
				result.addError("CORS_ORIGIN", corsOrigin, "invalid CORS origin URL")
			}
		}
	}

	// Validate frontend URL
	frontendURL := os.Getenv("FRONTEND_URL")
	if frontendURL != "" {
		if _, err := url.Parse(frontendURL); err != nil {
			result.addError("FRONTEND_URL", frontendURL, "invalid frontend URL")
		}
	}
}

// validateExternalServiceConfig validates external service configuration
func validateExternalServiceConfig(result *ValidationResult) {
	// Validate Google OAuth
	googleClientID := os.Getenv("GOOGLE_CLIENT_ID")
	googleClientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	
	if googleClientID == "" {
		result.addWarning("GOOGLE_CLIENT_ID not set - Google OAuth will not work")
	} else if !strings.HasSuffix(googleClientID, ".apps.googleusercontent.com") {
		result.addError("GOOGLE_CLIENT_ID", googleClientID, "invalid Google Client ID format")
	}
	
	if googleClientSecret == "" {
		result.addWarning("GOOGLE_CLIENT_SECRET not set - Google OAuth will not work")
	}

	// Validate Stripe configuration
	stripeSecret := os.Getenv("STRIPE_SECRET_KEY")
	if stripeSecret == "" {
		result.addWarning("STRIPE_SECRET_KEY not set - payment processing will not work")
	} else {
		if !strings.HasPrefix(stripeSecret, "sk_") {
			result.addError("STRIPE_SECRET_KEY", "sk_***", "invalid Stripe secret key format")
		}
		
		if strings.HasPrefix(stripeSecret, "sk_live_") && 
			strings.Contains(strings.ToLower(os.Getenv("ENVIRONMENT")), "dev") {
			result.addWarning("Using live Stripe keys in development environment")
		}
	}

	// Validate NextAuth
	nextAuthSecret := os.Getenv("NEXTAUTH_SECRET")
	if nextAuthSecret == "" {
		result.addError("NEXTAUTH_SECRET", "", "NextAuth secret is required")
	} else if len(nextAuthSecret) < 32 {
		result.addError("NEXTAUTH_SECRET", "***", "NextAuth secret must be at least 32 characters long")
	}

	// Validate ADC service URLs
	validateServiceURL(result, "SSO_SERVICE_URL", "http://localhost:9000")
	validateServiceURL(result, "SUBSCRIPTION_SERVICE_URL", "http://localhost:9100")
}

// validateSecurityConfig validates security-related configuration
func validateSecurityConfig(result *ValidationResult) {
	// Validate internal API keys
	internalKeys := os.Getenv("INTERNAL_API_KEYS")
	if internalKeys != "" {
		keys := strings.Split(internalKeys, ",")
		for _, key := range keys {
			key = strings.TrimSpace(key)
			if len(key) < 16 {
				result.addError("INTERNAL_API_KEYS", key, "internal API key too short (minimum 16 characters)")
			}
			if strings.Contains(strings.ToLower(key), "test") || 
				strings.Contains(strings.ToLower(key), "dev") {
				result.addWarning(fmt.Sprintf("Internal API key '%s' appears to be for development", key))
			}
		}
	}

	// Check environment
	environment := os.Getenv("ENVIRONMENT")
	if environment == "" {
		result.addWarning("ENVIRONMENT not set - defaulting to development")
	}
}

// validateRateLimitConfig validates rate limiting configuration
func validateRateLimitConfig(result *ValidationResult) {
	validateIntRange(result, "RATE_LIMIT_CLEANUP_INTERVAL", 60, 86400, 3600)
	validateIntRange(result, "RATE_LIMIT_BUCKET_EXPIRY", 3600, 604800, 86400)
	
	// Validate tier-specific rate limits
	validateIntRange(result, "RATE_LIMIT_FREE_MAX_TOKENS", 1, 1000, 10)
	validateFloatRange(result, "RATE_LIMIT_FREE_REFILL_RATE", 0.01, 10.0, 0.166)
	
	validateIntRange(result, "RATE_LIMIT_PRO_MAX_TOKENS", 10, 10000, 60)
	validateFloatRange(result, "RATE_LIMIT_PRO_REFILL_RATE", 0.1, 100.0, 1.0)
	
	validateIntRange(result, "RATE_LIMIT_ENTERPRISE_MAX_TOKENS", 100, 100000, 600)
	validateFloatRange(result, "RATE_LIMIT_ENTERPRISE_REFILL_RATE", 1.0, 1000.0, 10.0)
}

// validateSubscriptionConfig validates subscription tier configuration
func validateSubscriptionConfig(result *ValidationResult) {
	// Free tier
	validateFloatRange(result, "SUBSCRIPTION_FREE_PRICE", 0, 0, 0)
	validateIntRange(result, "SUBSCRIPTION_FREE_CREDIT_LIMIT", 100, 10000, 1000)
	
	// Pro tier
	validateFloatRange(result, "SUBSCRIPTION_PRO_PRICE", 1.0, 1000.0, 29.99)
	validateIntRange(result, "SUBSCRIPTION_PRO_CREDIT_LIMIT", 1000, 100000, 10000)
	
	// Enterprise tier
	validateFloatRange(result, "SUBSCRIPTION_ENTERPRISE_PRICE", 10.0, 10000.0, 99.99)
	validateIntRange(result, "SUBSCRIPTION_ENTERPRISE_CREDIT_LIMIT", 10000, 1000000, 50000)
}

// validateServiceURL validates service URL format
func validateServiceURL(result *ValidationResult, envVar, defaultURL string) {
	serviceURL := os.Getenv(envVar)
	if serviceURL == "" {
		serviceURL = defaultURL
		result.addWarning(fmt.Sprintf("%s not set, using default: %s", envVar, defaultURL))
	}
	
	if parsedURL, err := url.Parse(serviceURL); err != nil {
		result.addError(envVar, serviceURL, "invalid service URL format")
	} else if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		result.addError(envVar, serviceURL, "service URL must use http or https")
	}
}

// validateIntRange validates integer environment variable within range
func validateIntRange(result *ValidationResult, envVar string, min, max, defaultVal int) {
	valueStr := os.Getenv(envVar)
	if valueStr == "" {
		result.addWarning(fmt.Sprintf("%s not set, using default: %d", envVar, defaultVal))
		return
	}
	
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		result.addError(envVar, valueStr, "must be a valid integer")
		return
	}
	
	if value < min || value > max {
		result.addError(envVar, valueStr, fmt.Sprintf("must be between %d and %d", min, max))
	}
}

// validateFloatRange validates float environment variable within range
func validateFloatRange(result *ValidationResult, envVar string, min, max, defaultVal float64) {
	valueStr := os.Getenv(envVar)
	if valueStr == "" {
		result.addWarning(fmt.Sprintf("%s not set, using default: %.3f", envVar, defaultVal))
		return
	}
	
	value, err := strconv.ParseFloat(valueStr, 64)
	if err != nil {
		result.addError(envVar, valueStr, "must be a valid number")
		return
	}
	
	if value < min || value > max {
		result.addError(envVar, valueStr, fmt.Sprintf("must be between %.3f and %.3f", min, max))
	}
}

// validateDurationRange validates duration environment variable within range
func validateDurationRange(result *ValidationResult, envVar string, min, max, defaultVal time.Duration) {
	valueStr := os.Getenv(envVar)
	if valueStr == "" {
		result.addWarning(fmt.Sprintf("%s not set, using default: %s", envVar, defaultVal))
		return
	}
	
	// Try parsing as seconds first
	if seconds, err := strconv.Atoi(valueStr); err == nil {
		duration := time.Duration(seconds) * time.Second
		if duration < min || duration > max {
			result.addError(envVar, valueStr, fmt.Sprintf("must be between %s and %s", min, max))
		}
		return
	}
	
	// Try parsing as duration string
	duration, err := time.ParseDuration(valueStr)
	if err != nil {
		result.addError(envVar, valueStr, "must be a valid duration (e.g., '30s', '5m', '1h')")
		return
	}
	
	if duration < min || duration > max {
		result.addError(envVar, valueStr, fmt.Sprintf("must be between %s and %s", min, max))
	}
}

// addError adds a validation error
func (r *ValidationResult) addError(field, value, message string) {
	r.IsValid = false
	r.Errors = append(r.Errors, ConfigValidationError{
		Field:   field,
		Value:   value,
		Message: message,
	})
}

// addWarning adds a validation warning
func (r *ValidationResult) addWarning(message string) {
	r.Warnings = append(r.Warnings, message)
}

// logValidationResults logs the validation results
func logValidationResults(result *ValidationResult) {
	if len(result.Errors) > 0 {
		logrus.WithField("error_count", len(result.Errors)).Error("Configuration validation failed")
		for _, err := range result.Errors {
			logrus.WithFields(logrus.Fields{
				"field":   err.Field,
				"message": err.Message,
			}).Error("Configuration error")
		}
	}
	
	if len(result.Warnings) > 0 {
		logrus.WithField("warning_count", len(result.Warnings)).Warn("Configuration validation warnings")
		for _, warning := range result.Warnings {
			logrus.Warn(warning)
		}
	}
	
	if result.IsValid && len(result.Warnings) == 0 {
		logrus.Info("Configuration validation passed successfully")
	}
}

// GetConfigSummary returns a summary of current configuration
func GetConfigSummary() map[string]interface{} {
	return map[string]interface{}{
		"database": map[string]interface{}{
			"url_set":                os.Getenv("DATABASE_URL") != "",
			"max_idle_connections":   getEnvOrDefault("DB_MAX_IDLE_CONNECTIONS", "10"),
			"max_open_connections":   getEnvOrDefault("DB_MAX_OPEN_CONNECTIONS", "100"),
			"connection_max_lifetime": getEnvOrDefault("DB_CONNECTION_MAX_LIFETIME", "3600"),
		},
		"security": map[string]interface{}{
			"jwt_secret_set":         os.Getenv("JWT_SECRET") != "",
			"refresh_secret_set":     os.Getenv("JWT_REFRESH_SECRET") != "",
			"nextauth_secret_set":    os.Getenv("NEXTAUTH_SECRET") != "",
			"internal_api_keys_set":  os.Getenv("INTERNAL_API_KEYS") != "",
		},
		"external_services": map[string]interface{}{
			"google_oauth_configured": os.Getenv("GOOGLE_CLIENT_ID") != "" && os.Getenv("GOOGLE_CLIENT_SECRET") != "",
			"stripe_configured":       os.Getenv("STRIPE_SECRET_KEY") != "",
			"sso_service_url":         getEnvOrDefault("SSO_SERVICE_URL", "http://localhost:9000"),
			"subscription_service_url": getEnvOrDefault("SUBSCRIPTION_SERVICE_URL", "http://localhost:9100"),
		},
		"server": map[string]interface{}{
			"port":         getEnvOrDefault("PORT", "8400"),
			"environment":  getEnvOrDefault("ENVIRONMENT", "development"),
			"cors_origin":  getEnvOrDefault("CORS_ORIGIN", ""),
			"frontend_url": getEnvOrDefault("FRONTEND_URL", ""),
		},
	}
}

// getEnvOrDefault gets environment variable or returns default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// MustValidateConfig validates configuration and exits if validation fails
func MustValidateConfig() {
	result := ValidateConfiguration()
	if !result.IsValid {
		logrus.Fatal("Configuration validation failed - exiting")
	}
}