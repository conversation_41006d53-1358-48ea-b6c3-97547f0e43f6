package config

import (
	"time"

	"github.com/adc-credit/backend/internal/utils"
)

// DatabaseConfig holds all database-related configuration
type DatabaseConfig struct {
	MaxIdleConnections    int
	MaxOpenConnections    int
	ConnectionMaxLifetime time.Duration
}

// RateLimitConfig holds all rate limiting configuration
type RateLimitConfig struct {
	CleanupInterval time.Duration
	BucketExpiry    time.Duration

	// Free tier limits
	FreeMaxTokens  float64
	FreeRefillRate float64

	// Pro tier limits
	ProMaxTokens  float64
	ProRefillRate float64

	// Enterprise tier limits
	EnterpriseMaxTokens  float64
	EnterpriseRefillRate float64

	// Default limits
	DefaultMaxTokens     float64
	DefaultRefillRate    float64
	InvalidAPIMaxTokens  float64
	InvalidAPIRefillRate float64
	IPMaxTokens          float64
	IPRefillRate         float64
}

// MultiLangConfig holds configuration for Multi-Languages service integration
type MultiLangConfig struct {
	BaseURL      string
	InternalKey  string
	ProjectID    string
	Organization string
	Timeout      time.Duration
}

// SubscriptionConfig - REMOVED: Now handled by centralized ADC Subscription Service
// type SubscriptionConfig struct {
//     Free       SubscriptionTierConfig
//     Pro        SubscriptionTierConfig
//     Enterprise SubscriptionTierConfig
// }

// SubscriptionTierConfig - REMOVED: Now handled by centralized ADC Subscription Service
// type SubscriptionTierConfig struct {
//     Price             float64
//     CreditLimit       int
//     RateLimitMax      int
//     RateLimitRate     float64
//     MaxWebhooks       int
//     AdvancedAnalytics bool
// }

// LoadDatabaseConfig loads database configuration from environment variables
func LoadDatabaseConfig() DatabaseConfig {
	return DatabaseConfig{
		MaxIdleConnections:    utils.GetEnvAsInt("DB_MAX_IDLE_CONNECTIONS", 10),
		MaxOpenConnections:    utils.GetEnvAsInt("DB_MAX_OPEN_CONNECTIONS", 100),
		ConnectionMaxLifetime: utils.GetEnvAsDuration("DB_CONNECTION_MAX_LIFETIME", 3600),
	}
}

// LoadRateLimitConfig loads rate limiting configuration from environment variables
func LoadRateLimitConfig() RateLimitConfig {
	return RateLimitConfig{
		CleanupInterval:      utils.GetEnvAsDuration("RATE_LIMIT_CLEANUP_INTERVAL", 3600),
		BucketExpiry:         utils.GetEnvAsDuration("RATE_LIMIT_BUCKET_EXPIRY", 86400),
		FreeMaxTokens:        utils.GetEnvAsFloat("RATE_LIMIT_FREE_MAX_TOKENS", 10),
		FreeRefillRate:       utils.GetEnvAsFloat("RATE_LIMIT_FREE_REFILL_RATE", 1.0/6.0),
		ProMaxTokens:         utils.GetEnvAsFloat("RATE_LIMIT_PRO_MAX_TOKENS", 60),
		ProRefillRate:        utils.GetEnvAsFloat("RATE_LIMIT_PRO_REFILL_RATE", 1.0),
		EnterpriseMaxTokens:  utils.GetEnvAsFloat("RATE_LIMIT_ENTERPRISE_MAX_TOKENS", 600),
		EnterpriseRefillRate: utils.GetEnvAsFloat("RATE_LIMIT_ENTERPRISE_REFILL_RATE", 10.0),
		DefaultMaxTokens:     utils.GetEnvAsFloat("RATE_LIMIT_DEFAULT_MAX_TOKENS", 10),
		DefaultRefillRate:    utils.GetEnvAsFloat("RATE_LIMIT_DEFAULT_REFILL_RATE", 1.0/6.0),
		InvalidAPIMaxTokens:  utils.GetEnvAsFloat("RATE_LIMIT_INVALID_API_MAX_TOKENS", 5),
		InvalidAPIRefillRate: utils.GetEnvAsFloat("RATE_LIMIT_INVALID_API_REFILL_RATE", 1.0/12.0),
		IPMaxTokens:          utils.GetEnvAsFloat("RATE_LIMIT_IP_MAX_TOKENS", 30),
		IPRefillRate:         utils.GetEnvAsFloat("RATE_LIMIT_IP_REFILL_RATE", 0.5),
	}
}

// LoadMultiLangConfig loads Multi-Languages service configuration from environment variables
func LoadMultiLangConfig() MultiLangConfig {
	return MultiLangConfig{
		BaseURL:      utils.GetEnvOrDefault("MULTILANG_SERVICE_URL", "http://localhost:8300"),
		InternalKey:  utils.GetEnvOrDefault("MULTILANG_INTERNAL_KEY", "adc-credit-internal-2024"),
		ProjectID:    utils.GetEnvOrDefault("MULTILANG_PROJECT_ID", "b90e383d-6e7d-4881-ba9a-c31649719348"),
		Organization: utils.GetEnvOrDefault("MULTILANG_ORGANIZATION", "adc-credit"),
		Timeout:      utils.GetEnvAsDuration("MULTILANG_TIMEOUT", 30),
	}
}

// LoadSubscriptionConfig - REMOVED: Now handled by centralized ADC Subscription Service
// func LoadSubscriptionConfig() SubscriptionConfig {
//     return SubscriptionConfig{
//         Free: SubscriptionTierConfig{
//             Price:             utils.GetEnvAsFloat("SUBSCRIPTION_FREE_PRICE", 0),
//             CreditLimit:       utils.GetEnvAsInt("SUBSCRIPTION_FREE_CREDIT_LIMIT", 1000),
//             RateLimitMax:      utils.GetEnvAsInt("SUBSCRIPTION_FREE_RATE_LIMIT_MAX", 10),
//             RateLimitRate:     utils.GetEnvAsFloat("SUBSCRIPTION_FREE_RATE_LIMIT_RATE", 1.0/6.0),
//             MaxWebhooks:       utils.GetEnvAsInt("SUBSCRIPTION_FREE_MAX_WEBHOOKS", 1),
//             AdvancedAnalytics: utils.GetEnvAsBool("SUBSCRIPTION_FREE_ADVANCED_ANALYTICS", false),
//         },
//         Pro: SubscriptionTierConfig{
//             Price:             utils.GetEnvAsFloat("SUBSCRIPTION_PRO_PRICE", 29.99),
//             CreditLimit:       utils.GetEnvAsInt("SUBSCRIPTION_PRO_CREDIT_LIMIT", 10000),
//             RateLimitMax:      utils.GetEnvAsInt("SUBSCRIPTION_PRO_RATE_LIMIT_MAX", 60),
//             RateLimitRate:     utils.GetEnvAsFloat("SUBSCRIPTION_PRO_RATE_LIMIT_RATE", 1.0),
//             MaxWebhooks:       utils.GetEnvAsInt("SUBSCRIPTION_PRO_MAX_WEBHOOKS", 5),
//             AdvancedAnalytics: utils.GetEnvAsBool("SUBSCRIPTION_PRO_ADVANCED_ANALYTICS", true),
//         },
//         Enterprise: SubscriptionTierConfig{
//             Price:             utils.GetEnvAsFloat("SUBSCRIPTION_ENTERPRISE_PRICE", 99.99),
//             CreditLimit:       utils.GetEnvAsInt("SUBSCRIPTION_ENTERPRISE_CREDIT_LIMIT", 50000),
//             RateLimitMax:      utils.GetEnvAsInt("SUBSCRIPTION_ENTERPRISE_RATE_LIMIT_MAX", 600),
//             RateLimitRate:     utils.GetEnvAsFloat("SUBSCRIPTION_ENTERPRISE_RATE_LIMIT_RATE", 10.0),
//             MaxWebhooks:       utils.GetEnvAsInt("SUBSCRIPTION_ENTERPRISE_MAX_WEBHOOKS", 20),
//             AdvancedAnalytics: utils.GetEnvAsBool("SUBSCRIPTION_ENTERPRISE_ADVANCED_ANALYTICS", true),
//         },
//     }
// }

// GetTierConfig - REMOVED: Now handled by centralized ADC Subscription Service
// func (sc SubscriptionConfig) GetTierConfig(tierName string) *SubscriptionTierConfig {
//     switch tierName {
//     case "Free":
//         return &sc.Free
//     case "Pro":
//         return &sc.Pro
//     case "Enterprise":
//         return &sc.Enterprise
//     default:
//         return &sc.Free // Default to Free tier
//     }
// }

// GetRateLimitForTier - REMOVED: Now handled by centralized ADC Subscription Service
// func (rlc RateLimitConfig) GetRateLimitForTier(tierName string) (maxTokens, refillRate float64) {
//     switch tierName {
//     case "Free":
//         return rlc.FreeMaxTokens, rlc.FreeRefillRate
//     case "Pro":
//         return rlc.ProMaxTokens, rlc.ProRefillRate
//     case "Enterprise":
//         return rlc.EnterpriseMaxTokens, rlc.EnterpriseRefillRate
//     default:
//         return rlc.DefaultMaxTokens, rlc.DefaultRefillRate
//     }
// }