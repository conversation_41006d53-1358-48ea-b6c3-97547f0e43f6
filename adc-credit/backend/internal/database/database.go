package database

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"

	"github.com/adc-credit/backend/internal/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

// InitDB initializes the database connection
func InitDB() error {
	// Check if DATABASE_URL is provided (for Supabase or other PostgreSQL providers)
	var dsn string
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		dsn = dbURL
	} else {
		// Fallback to individual connection parameters
		dsn = fmt.Sprintf(
			"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
			os.<PERSON>env("DB_HOST"),
			os.Getenv("DB_USER"),
			os.<PERSON>env("DB_PASSWORD"),
			os.Getenv("DB_NAME"),
			os.<PERSON>("DB_PORT"),
		)
	}

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		PrepareStmt: true, // Caches prepared statements for better performance
	})
	if err != nil {
		return err
	}

	// Configure connection pooling
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	// Set connection pool settings from configuration
	dbConfig := config.LoadDatabaseConfig()
	sqlDB.SetMaxIdleConns(dbConfig.MaxIdleConnections)
	sqlDB.SetMaxOpenConns(dbConfig.MaxOpenConnections)
	sqlDB.SetConnMaxLifetime(dbConfig.ConnectionMaxLifetime)

	// Run migrations
	if err := RunMigrations(); err != nil {
		return err
	}

	// Seed subscription tiers if they don't exist
	if err := seedSubscriptionTiers(); err != nil {
		logrus.Infof("Error seeding subscription tiers: %v", err)
	}

	return nil
}

// seedSubscriptionTiers creates default subscription tiers if they don't exist
// DEPRECATED: Subscription tiers are now managed by ADC Subscription Service
func seedSubscriptionTiers() error {
	// No longer needed - subscription tiers are managed by ADC Subscription Service
	return nil

	/* LEGACY CODE - COMMENTED OUT
	var count int64
	DB.Model(&models.SubscriptionTier{}).Count(&count)
	if count > 0 {
		return nil
	}

	// Load subscription configuration from environment variables
	subConfig := config.LoadSubscriptionConfig()
	
	tiers := []models.SubscriptionTier{
		{
			Name:                 "Free",
			Description:          "Basic tier with limited credits",
			Price:                subConfig.Free.Price,
			CreditLimit:          subConfig.Free.CreditLimit,
			DefaultRateLimitMax:  subConfig.Free.RateLimitMax,
			DefaultRateLimitRate: subConfig.Free.RateLimitRate,
			MaxWebhooks:          subConfig.Free.MaxWebhooks,
			AdvancedAnalytics:    subConfig.Free.AdvancedAnalytics,
			Features:             []string{"1,000 credits per month", "Basic API access", "Standard support", "1 webhook", "Basic rate limiting"},
		},
		{
			Name:                 "Pro",
			Description:          "Professional tier with more credits",
			Price:                subConfig.Pro.Price,
			CreditLimit:          subConfig.Pro.CreditLimit,
			DefaultRateLimitMax:  subConfig.Pro.RateLimitMax,
			DefaultRateLimitRate: subConfig.Pro.RateLimitRate,
			MaxWebhooks:          subConfig.Pro.MaxWebhooks,
			AdvancedAnalytics:    subConfig.Pro.AdvancedAnalytics,
			Features:             []string{"10,000 credits per month", "Full API access", "Priority support", "Detailed analytics", "5 webhooks", "Enhanced rate limiting"},
		},
		{
			Name:                 "Enterprise",
			Description:          "Enterprise tier with unlimited credits",
			Price:                subConfig.Enterprise.Price,
			CreditLimit:          subConfig.Enterprise.CreditLimit,
			DefaultRateLimitMax:  subConfig.Enterprise.RateLimitMax,
			DefaultRateLimitRate: subConfig.Enterprise.RateLimitRate,
			MaxWebhooks:          subConfig.Enterprise.MaxWebhooks,
			AdvancedAnalytics:    subConfig.Enterprise.AdvancedAnalytics,
			Features:             []string{"50,000 credits per month", "Full API access", "24/7 support", "Advanced analytics", "Custom integrations", "20 webhooks", "Custom rate limiting"},
		},
	}

	return DB.Create(&tiers).Error
	*/
}
