package database

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// RunMigrations runs all database migrations
func RunMigrations() error {
	// Run custom migrations first to fix constraints
	if err := fixShopConstraints(); err != nil {
		return fmt.Errorf("failed to fix shop constraints: %w", err)
	}

	// Run auto migrations in correct order (dependencies first)
	err := DB.AutoMigrate(
		// Core models first (no dependencies)
		&models.User{},
		// &models.SubscriptionTier{}, // REMOVED: Now managed by ADC Subscription Service

		// Models that depend on User
		&models.Organization{},
		&models.Shop{},         // Re-enabled for shop subscription support
		&models.MerchantShop{}, // Depends on User

		// Models that depend on Organization/Shop
		&models.Branch{}, // Depends on Organization
		&models.ShopBranch{},            // Re-enabled for shop subscription support
		// &models.APIKey{},       // Temporarily disabled - depends on Shop, ShopBranch
		// &models.Subscription{}, // Temporarily disabled - depends on Shop
		// &models.ShopCustomer{},          // Temporarily disabled - depends on Shop
		// &models.CreditCode{},            // Temporarily disabled - depends on Shop
		// &models.ShopCreditTransaction{}, // Temporarily disabled - depends on Shop
		// &models.ShopExchangeRate{}, // Depends on Shop - using manual creation
		
		// New shop-based subscription models
		&models.ShopSubscription{},            // Depends on Shop and SubscriptionTier
		&models.ShopUser{},                    // Depends on Shop and User
		&models.ShopTransaction{},             // Depends on Shop and ShopSubscription
		&models.ShopMonthlyUsageTracking{},    // Depends on Shop

		// Other models
		&models.Usage{},
		&models.Transaction{},
		&models.Webhook{},
		&models.WebhookDelivery{},
		&models.AnalyticsData{},
		&models.MonthlyUsageTracking{},
	)
	if err != nil {
		return fmt.Errorf("failed to run auto migrations: %w", err)
	}

	if err := migrateShopSlugs(); err != nil {
		return fmt.Errorf("failed to migrate shop slugs: %w", err)
	}

	// Create shop exchange rates table manually
	if err := createShopExchangeRatesTable(); err != nil {
		return fmt.Errorf("failed to create shop exchange rates table: %w", err)
	}

	return nil
}

// migrateShopSlugs adds slugs to existing shops
func migrateShopSlugs() error {
	// Check if we need to run this migration
	var count int64
	DB.Model(&models.MerchantShop{}).Where("slug = ?", "").Or("slug IS NULL").Count(&count)

	if count == 0 {
		// No shops without slugs, no need to run migration
		return nil
	}

	// Get all shops without slugs
	var shops []models.MerchantShop
	if err := DB.Where("slug = ?", "").Or("slug IS NULL").Find(&shops).Error; err != nil {
		return err
	}

	// Update each shop with a slug
	for _, shop := range shops {
		slug := generateSlug(shop.Name)
		slug = ensureUniqueSlug(slug, shop.ID)

		if err := DB.Model(&models.MerchantShop{}).Where("id = ?", shop.ID).Update("slug", slug).Error; err != nil {
			return err
		}
	}

	// Now make the slug column NOT NULL
	if err := DB.Exec("ALTER TABLE merchant_shops ALTER COLUMN slug SET NOT NULL").Error; err != nil {
		return err
	}

	return nil
}

// generateSlug creates a URL-friendly slug from a shop name
func generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove any character that is not alphanumeric or hyphen
	reg := regexp.MustCompile("[^a-z0-9-]")
	slug = reg.ReplaceAllString(slug, "")

	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile("-+")
	slug = reg.ReplaceAllString(slug, "-")

	// Trim hyphens from beginning and end
	slug = strings.Trim(slug, "-")

	// If slug is empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

// ensureUniqueSlug makes sure the slug is unique by appending a number if necessary
func ensureUniqueSlug(slug string, shopID uuid.UUID) string {
	baseSlug := slug
	counter := 1

	for {
		// Check if slug exists for another shop
		var existingShop models.MerchantShop
		err := DB.Where("slug = ? AND id != ?", slug, shopID).First(&existingShop).Error

		// If no shop found with this slug, it's unique
		if err != nil {
			return slug
		}

		// Append counter to make it unique
		slug = fmt.Sprintf("%s-%d", baseSlug, counter)
		counter++
	}
}

// createShopExchangeRatesTable creates the shop_exchange_rates table manually
func createShopExchangeRatesTable() error {
	// Check if table already exists
	var tableExists bool
	err := DB.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shop_exchange_rates')").Scan(&tableExists).Error
	if err != nil {
		return err
	}

	if tableExists {
		return nil // Table already exists
	}

	// Create the table (execute commands separately)
	commands := []string{
		`CREATE TABLE IF NOT EXISTS shop_exchange_rates (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
			currency_code VARCHAR(3) NOT NULL,
			rate DECIMAL(15,8) NOT NULL DEFAULT 1.0,
			is_active BOOLEAN NOT NULL DEFAULT true,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			deleted_at TIMESTAMP WITH TIME ZONE,
			UNIQUE(shop_id, currency_code)
		)`,
		`CREATE INDEX IF NOT EXISTS idx_shop_exchange_rates_shop_id ON shop_exchange_rates(shop_id)`,
		`CREATE INDEX IF NOT EXISTS idx_shop_exchange_rates_deleted_at ON shop_exchange_rates(deleted_at)`,
	}

	for _, cmd := range commands {
		if err := DB.Exec(cmd).Error; err != nil {
			return err
		}
	}

	return nil
}

// fixShopConstraints handles shop constraint issues during migration
func fixShopConstraints() error {
	// List of problematic constraints that might exist and cause issues
	problemConstraints := []string{
		"uni_shops_slug",
		"idx_shops_slug",
		"shops_slug_key",
	}

	// Check if shops table exists
	var tableExists bool
	err := DB.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shops')").Scan(&tableExists).Error
	if err != nil {
		return err
	}

	if !tableExists {
		// Table doesn't exist yet, no constraints to fix
		return nil
	}

	// Drop any problematic constraints that exist
	for _, constraintName := range problemConstraints {
		var constraintExists bool
		err = DB.Raw(`SELECT EXISTS (
			SELECT FROM information_schema.table_constraints 
			WHERE table_name = 'shops' AND constraint_name = $1
		)`, constraintName).Scan(&constraintExists).Error
		if err != nil {
			continue // Skip if we can't check
		}

		if constraintExists {
			err = DB.Exec(fmt.Sprintf("ALTER TABLE shops DROP CONSTRAINT %s", constraintName)).Error
			if err != nil {
				// Log warning but continue - constraint dropping is best effort
				logrus.WithFields(logrus.Fields{
					"constraint": constraintName,
					"error":      err,
				}).Warn("Could not drop database constraint")
			} else {
				logrus.WithField("constraint", constraintName).Info("Dropped problematic database constraint")
			}
		}
	}

	return nil
}
