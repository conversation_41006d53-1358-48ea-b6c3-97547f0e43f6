package utils

import (
	"regexp"
	"strings"
)

// GenerateSlug creates a URL-friendly slug from a string
func GenerateSlug(input string) string {
	// Convert to lowercase
	slug := strings.ToLower(input)
	
	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")
	
	// Remove special characters
	reg := regexp.MustCompile("[^a-z0-9-]")
	slug = reg.ReplaceAllString(slug, "")
	
	// Replace multiple hyphens with a single one
	reg = regexp.MustCompile("-+")
	slug = reg.ReplaceAllString(slug, "-")
	
	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")
	
	// If slug is empty, return a default
	if slug == "" {
		return "organization"
	}
	
	return slug
}

// EnsureUniqueSlug makes sure the slug is unique by appending a number if needed
func EnsureUniqueSlug(slug string, checkExists func(string) bool) string {
	originalSlug := slug
	counter := 1
	
	// Keep checking and incrementing counter until we find a unique slug
	for checkExists(slug) {
		slug = originalSlug + "-" + string(rune(counter+'0'))
		counter++
	}
	
	return slug
}
