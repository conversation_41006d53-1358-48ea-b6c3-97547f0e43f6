package utils

import (
	"fmt"
	"os"

	"github.com/sirupsen/logrus"
)

// RequiredEnvVar represents a required environment variable
type RequiredEnvVar struct {
	Key         string
	Description string
	Critical    bool // If true, application exits if missing
}

// ValidateEnvironmentVariables validates required environment variables at startup
func ValidateEnvironmentVariables() error {
	requiredVars := []RequiredEnvVar{
		// Critical variables - application cannot start without these
		{Key: "DATABASE_URL", Description: "PostgreSQL connection string", Critical: true},
		{Key: "JWT_SECRET", Description: "Secret key for JWT token signing", Critical: true},
		
		// Important variables - warnings if missing
		{Key: "GOOGLE_CLIENT_ID", Description: "Google OAuth client ID", Critical: false},
		{Key: "GOOGLE_CLIENT_SECRET", Description: "Google OAuth client secret", Critical: false},
		{Key: "STRIPE_SECRET_KEY", Description: "Stripe secret key for payments", Critical: false},
		{Key: "NEXTAUTH_SECRET", Description: "NextAuth.js session secret", Critical: false},
		
		// Stripe Price IDs - important for payment functionality
		{Key: "STRIPE_PRICE_ID_1_PERSONAL", Description: "Stripe price ID for personal tier 1", Critical: false},
		{Key: "STRIPE_PRICE_ID_2_PERSONAL", Description: "Stripe price ID for personal tier 2", Critical: false},
		{Key: "STRIPE_PRICE_ID_3_PERSONAL", Description: "Stripe price ID for personal tier 3", Critical: false},
	}

	var criticalMissing []string
	var warningMissing []string

	for _, envVar := range requiredVars {
		value := os.Getenv(envVar.Key)
		if value == "" {
			if envVar.Critical {
				criticalMissing = append(criticalMissing, envVar.Key)
				logrus.Errorf("CRITICAL: Missing required environment variable %s (%s)", envVar.Key, envVar.Description)
			} else {
				warningMissing = append(warningMissing, envVar.Key)
				logrus.Warnf("WARNING: Missing optional environment variable %s (%s)", envVar.Key, envVar.Description)
			}
		} else {
			logrus.Debugf("Environment variable %s is set", envVar.Key)
		}
	}

	// Log warnings for missing optional variables
	if len(warningMissing) > 0 {
		logrus.Warnf("Found %d missing optional environment variables. Some features may not work correctly.", len(warningMissing))
	}

	// Return error if critical variables are missing
	if len(criticalMissing) > 0 {
		return fmt.Errorf("missing critical environment variables: %v", criticalMissing)
	}

	logrus.Info("Environment variable validation completed successfully")
	return nil
}

// LogConfigurationSummary logs a summary of current configuration
func LogConfigurationSummary() {
	logrus.Info("Configuration Summary:")
	logrus.Infof("  - Database: %s", getMaskedValue("DATABASE_URL"))
	logrus.Infof("  - Port: %s", GetEnvOrDefault("PORT", "8400"))
	logrus.Infof("  - Environment: %s", GetEnvOrDefault("ENVIRONMENT", "development"))
	logrus.Infof("  - Frontend URL: %s", GetEnvOrDefault("FRONTEND_URL", "not set"))
	
	// Database pool settings
	logrus.Infof("  - DB Max Idle Connections: %d", GetEnvAsInt("DB_MAX_IDLE_CONNECTIONS", 10))
	logrus.Infof("  - DB Max Open Connections: %d", GetEnvAsInt("DB_MAX_OPEN_CONNECTIONS", 100))
	logrus.Infof("  - DB Connection Max Lifetime: %v", GetEnvAsDuration("DB_CONNECTION_MAX_LIFETIME", 3600))
	
	// Rate limiting
	logrus.Infof("  - Rate Limit Cleanup Interval: %v", GetEnvAsDuration("RATE_LIMIT_CLEANUP_INTERVAL", 3600))
	logrus.Infof("  - Free Tier Max Tokens: %.0f", GetEnvAsFloat("RATE_LIMIT_FREE_MAX_TOKENS", 10))
	logrus.Infof("  - Pro Tier Max Tokens: %.0f", GetEnvAsFloat("RATE_LIMIT_PRO_MAX_TOKENS", 60))
	logrus.Infof("  - Enterprise Tier Max Tokens: %.0f", GetEnvAsFloat("RATE_LIMIT_ENTERPRISE_MAX_TOKENS", 600))
}

// getMaskedValue returns a masked version of sensitive environment variables
func getMaskedValue(key string) string {
	value := os.Getenv(key)
	if value == "" {
		return "not set"
	}
	if len(value) <= 8 {
		return "***masked***"
	}
	return value[:4] + "***" + value[len(value)-4:]
}