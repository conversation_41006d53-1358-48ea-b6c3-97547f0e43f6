package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/adc-credit/backend/internal/config"
	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
)

// Global rate limiter configuration
var rateLimitConfig config.RateLimitConfig

// RateLimiter implements a token bucket rate limiter
type RateLimiter struct {
	mu           sync.Mutex
	buckets      map[string]*TokenBucket
	cleanupTimer *time.Timer
}

// TokenBucket represents a token bucket for rate limiting
type TokenBucket struct {
	tokens         float64
	maxTokens      float64
	refillRate     float64 // tokens per second
	lastRefillTime time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter() *RateLimiter {
	// Load rate limiting configuration once
	rateLimitConfig = config.LoadRateLimitConfig()
	
	rl := &RateLimiter{
		buckets: make(map[string]*TokenBucket),
	}

	// Start cleanup routine to remove expired buckets
	rl.cleanupTimer = time.AfterFunc(rateLimitConfig.CleanupInterval, func() {
		rl.cleanup()
	})

	return rl
}

// cleanup removes buckets that haven't been used for a while
func (rl *RateLimiter) cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	for key, bucket := range rl.buckets {
		if time.Since(bucket.lastRefillTime) > rateLimitConfig.BucketExpiry {
			delete(rl.buckets, key)
		}
	}

	// Schedule next cleanup
	rl.cleanupTimer.Reset(rateLimitConfig.CleanupInterval)
}

// Allow checks if a request is allowed based on rate limits
func (rl *RateLimiter) Allow(key string, maxTokens, refillRate float64) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	bucket, exists := rl.buckets[key]
	if !exists {
		bucket = &TokenBucket{
			tokens:         maxTokens,
			maxTokens:      maxTokens,
			refillRate:     refillRate,
			lastRefillTime: time.Now(),
		}
		rl.buckets[key] = bucket
		return true
	}

	// Refill tokens based on time elapsed
	now := time.Now()
	elapsed := now.Sub(bucket.lastRefillTime).Seconds()
	bucket.tokens = min(bucket.maxTokens, bucket.tokens+elapsed*bucket.refillRate)
	bucket.lastRefillTime = now

	// Check if we have enough tokens
	if bucket.tokens >= 1.0 {
		bucket.tokens -= 1.0
		return true
	}

	return false
}

// Global rate limiter instance
var globalRateLimiter = NewRateLimiter()

// RateLimitMiddleware applies rate limiting based on API key or IP address
func RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get API key from header
		apiKey := c.GetHeader("X-API-Key")
		var key string
		var maxTokens float64
		var refillRate float64

		if apiKey != "" {
			// Rate limit based on API key
			key = "api:" + apiKey
			
			// Get rate limit settings from database
			var apiKeyObj models.APIKey
			if err := database.DB.Where("key = ?", apiKey).First(&apiKeyObj).Error; err == nil {
				// Get subscription tier for this API key's shop (new shop-based system)
				if apiKeyObj.ShopID != nil {
					var shopSubscription models.ShopSubscription
					if err := database.DB.Where("shop_id = ? AND status = ?", *apiKeyObj.ShopID, "active").
						Preload("SubscriptionTier").First(&shopSubscription).Error; err == nil {
						
						// Set rate limits based on subscription tier using configuration
						// TODO: Implement GetRateLimitForTier method in config
						maxTokens = rateLimitConfig.DefaultMaxTokens
						refillRate = rateLimitConfig.DefaultRefillRate
					} else {
						// Default rate limits if no shop subscription found
						maxTokens = rateLimitConfig.DefaultMaxTokens
						refillRate = rateLimitConfig.DefaultRefillRate
					}
				} else {
					// Default rate limits if no shop ID on API key
					maxTokens = rateLimitConfig.DefaultMaxTokens
					refillRate = rateLimitConfig.DefaultRefillRate
				}
			} else {
				// Default rate limits if API key not found
				maxTokens = rateLimitConfig.InvalidAPIMaxTokens
				refillRate = rateLimitConfig.InvalidAPIRefillRate
			}
		} else {
			// Rate limit based on IP address
			key = "ip:" + c.ClientIP()
			maxTokens = rateLimitConfig.IPMaxTokens
			refillRate = rateLimitConfig.IPRefillRate
		}

		// Check if request is allowed
		if !globalRateLimiter.Allow(key, maxTokens, refillRate) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"retry_after": int(1.0 / refillRate), // Seconds until next token
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CustomRateLimitMiddleware applies custom rate limiting based on provided parameters
func CustomRateLimitMiddleware(maxTokens, refillRate float64) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get API key from header
		apiKey := c.GetHeader("X-API-Key")
		var key string

		if apiKey != "" {
			// Rate limit based on API key
			key = "custom:api:" + apiKey
		} else {
			// Rate limit based on IP address
			key = "custom:ip:" + c.ClientIP()
		}

		// Check if request is allowed
		if !globalRateLimiter.Allow(key, maxTokens, refillRate) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"retry_after": int(1.0 / refillRate), // Seconds until next token
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
