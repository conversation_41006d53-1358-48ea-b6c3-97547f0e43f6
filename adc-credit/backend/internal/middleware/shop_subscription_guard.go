package middleware

import (
	"context"
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// ShopSubscriptionGuardMiddleware provides subscription limit checking using the shop subscription service
type ShopSubscriptionGuardMiddleware struct {
	serviceContainer   *services.ServiceContainer
	subscriptionClient *subscriptionSDK.Client // Direct access to centralized service
}

// NewShopSubscriptionGuardMiddleware creates a new shop subscription guard middleware
func NewShopSubscriptionGuardMiddleware(serviceContainer *services.ServiceContainer) *ShopSubscriptionGuardMiddleware {
	return &ShopSubscriptionGuardMiddleware{
		serviceContainer:   serviceContainer,
		subscriptionClient: serviceContainer.SubscriptionClient,
	}
}

// CheckShopLimit validates shop creation limits
func (h *ShopSubscriptionGuardMiddleware) CheckShopLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Info("Internal API key detected - bypassing shop creation limits")
			c.Next()
			return
		}

		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		// For shop creation, we need to check user's current shop count
		// Since we're mapping shops to organizations, we validate shop creation limits
		// For user-level limits, we use a simplified validation for now
		if !h.validateUserShopCreation(c, userID.(uint)) {
			logrus.Warnf("Shop limit exceeded for user %d via centralized service", userID)
			c.JSON(http.StatusForbidden, gin.H{
				"error":     "Subscription limit exceeded",
				"message":   "You have reached the maximum number of shops allowed by your subscription plan",
				"limit_type": "shops",
				"service":   "credit",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckCustomerLimit validates customer creation limits for a shop
func (h *ShopSubscriptionGuardMiddleware) CheckCustomerLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Info("Internal API key detected - bypassing customer creation limits")
			c.Next()
			return
		}

		shopIDStr := c.Param("id")
		if shopIDStr == "" {
			shopIDStr = c.Param("shopId")
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		// Use centralized subscription service for validation
		if !h.validateFeatureUsageWithCentralizedService(c, shopID, "credit", "customers", 1) {
			logrus.Warnf("Customer limit exceeded for shop %s via centralized service", shopID)
			c.JSON(http.StatusForbidden, gin.H{
				"error":     "Subscription limit exceeded",
				"message":   "You have reached the maximum number of customers allowed by your subscription plan",
				"limit_type": "customers",
				"service":   "credit",
			})
			c.Abort()
			return
		}

		// Record usage in centralized service
		h.recordFeatureUsage(c, shopID, "credit", "customers", 1, "Customer creation")

		c.Next()
	}
}

// CheckAPIKeyLimit validates API key creation limits for a shop
func (h *ShopSubscriptionGuardMiddleware) CheckAPIKeyLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Info("Internal API key detected - bypassing API key creation limits")
			c.Next()
			return
		}

		shopIDStr := c.Param("id")
		if shopIDStr == "" {
			shopIDStr = c.Param("shopId")
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		// Use centralized subscription service for validation
		if !h.validateFeatureUsageWithCentralizedService(c, shopID, "credit", "api_keys", 1) {
			logrus.Warnf("API key limit exceeded for shop %s via centralized service", shopID)
			c.JSON(http.StatusForbidden, gin.H{
				"error":     "Subscription limit exceeded",
				"message":   "You have reached the maximum number of API keys allowed by your subscription plan",
				"limit_type": "api_keys",
				"service":   "credit",
			})
			c.Abort()
			return
		}

		// Record usage in centralized service
		h.recordFeatureUsage(c, shopID, "credit", "api_keys", 1, "API key creation")

		c.Next()
	}
}

// CheckQRCodeLimit validates QR code generation limits
func (h *ShopSubscriptionGuardMiddleware) CheckQRCodeLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Info("Internal API key detected - bypassing QR code generation limits")
			c.Next()
			return
		}

		shopIDStr := c.Param("id")
		if shopIDStr == "" {
			shopIDStr = c.Param("shopId")
		}

		shopID, err := uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			c.Abort()
			return
		}

		// Use centralized subscription service for validation
		if !h.validateFeatureUsageWithCentralizedService(c, shopID, "credit", "qr_codes", 1) {
			logrus.Warnf("QR code limit exceeded for shop %s via centralized service", shopID)
			c.JSON(http.StatusForbidden, gin.H{
				"error":     "Subscription limit exceeded",
				"message":   "You have reached the monthly QR code generation limit for your subscription plan",
				"limit_type": "qr_codes",
				"service":   "credit",
			})
			c.Abort()
			return
		}

		// Record usage in centralized service
		h.recordFeatureUsage(c, shopID, "credit", "qr_codes", 1, "QR code generation")

		c.Next()
	}
}

// CheckWebhookLimit validates webhook creation limits
func (h *ShopSubscriptionGuardMiddleware) CheckWebhookLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Info("Internal API key detected - bypassing webhook creation limits")
			c.Next()
			return
		}

		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		// For webhook creation, use simplified user-level validation during transition
		if !h.validateUserWebhookCreation(c, userID.(uint)) {
			logrus.Warnf("Webhook limit exceeded for user %d via centralized service", userID)
			c.JSON(http.StatusForbidden, gin.H{
				"error":     "Subscription limit exceeded",
				"message":   "You have reached the maximum number of webhooks allowed by your subscription plan",
				"limit_type": "webhooks",
				"service":   "credit",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckFeatureAccess validates if a feature is enabled for the user's subscription
func (h *ShopSubscriptionGuardMiddleware) CheckFeatureAccess(feature string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for internal API key bypass first
		isInternal, _ := c.Get("is_internal_api_key")
		if isInternalAPIKey, ok := isInternal.(bool); ok && isInternalAPIKey {
			logrus.Infof("Internal API key detected - bypassing feature access check for %s", feature)
			c.Next()
			return
		}

		shopIDStr := c.Param("id")
		if shopIDStr == "" {
			shopIDStr = c.Param("shopId")
		}

		if shopIDStr != "" {
			shopID, err := uuid.Parse(shopIDStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
				c.Abort()
				return
			}

			shopService := h.serviceContainer.GetShopSubscriptionService()
			enabled, err := shopService.IsFeatureEnabled(shopID, feature)
			if err != nil {
				logrus.Warnf("Failed to check feature access for shop %s, feature %s: %v", shopID, feature, err)
				// Allow access if we can't determine feature status (fail open)
				c.Next()
				return
			}

			if !enabled {
				c.JSON(http.StatusForbidden, gin.H{
					"error":   "Feature not available",
					"message": "This feature is not available in your current subscription plan",
					"feature": feature,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// validateUserAction validates if a user can perform an action (used for user-level limits)
func (h *ShopSubscriptionGuardMiddleware) validateUserAction(userID uint, action string) error {
	// For user-level actions, we need to find the user's primary shop
	// or handle this differently. For now, we'll use a simplified approach
	// where we check the first shop owned by the user.
	
	// TODO: Implement proper user-to-shop mapping for user-level limits
	// This is a simplified implementation for the migration period
	
	return nil // Allow action for now during migration
}

// validateFeatureUsageWithCentralizedService validates feature usage using the centralized subscription service
func (h *ShopSubscriptionGuardMiddleware) validateFeatureUsageWithCentralizedService(c *gin.Context, shopID uuid.UUID, serviceName, featureName string, amount int64) bool {
	if h.subscriptionClient == nil {
		logrus.Warn("Subscription client not available, falling back to local validation")
		return true // Fail open if centralized service unavailable
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Use shopID as organizationID since Shop == Organization in our architecture
	canUse, err := h.subscriptionClient.CanUseFeature(ctx, shopID, serviceName, featureName, amount)
	if err != nil {
		logrus.Warnf("Failed to validate feature usage via centralized service for shop %s: %v", shopID, err)
		// Fall back to local validation if centralized service fails
		localErr := h.serviceContainer.ValidateSubscriptionAccessWithContext(c, shopID, featureName)
		return localErr == nil
	}

	return canUse
}

// recordFeatureUsage records feature usage in the centralized subscription service
func (h *ShopSubscriptionGuardMiddleware) recordFeatureUsage(c *gin.Context, shopID uuid.UUID, serviceName, featureName string, amount int64, description string) {
	if h.subscriptionClient == nil {
		logrus.Warn("Subscription client not available, skipping usage recording")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	usageReq := &subscriptionSDK.RecordUsageRequest{
		OrganizationID: shopID, // Shop == Organization in our architecture
		ServiceName:    serviceName,
		FeatureName:    featureName,
		UsageAmount:    amount,
		UsageUnit:      "count",
		Metadata: map[string]interface{}{
			"description": description,
			"user_agent":  c.GetHeader("User-Agent"),
			"ip_address":  c.ClientIP(),
			"timestamp":   time.Now().Unix(),
		},
	}

	err := h.subscriptionClient.RecordUsage(ctx, usageReq)
	if err != nil {
		logrus.Warnf("Failed to record feature usage via centralized service for shop %s: %v", shopID, err)
		// Don't fail the request if usage recording fails
	} else {
		logrus.Debugf("Successfully recorded usage for shop %s: %s.%s = %d", shopID, serviceName, featureName, amount)
	}
}

// validateUserShopCreation validates if a user can create a new shop
func (h *ShopSubscriptionGuardMiddleware) validateUserShopCreation(c *gin.Context, userID uint) bool {
	// For user-level shop creation, we need a different approach since we don't have a shopID yet
	// This is a simplified implementation during the transition period
	
	// For now, we'll use the legacy validation and eventually migrate to
	// user-based organization limits in the centralized service
	err := h.validateUserAction(userID, "shops")
	if err != nil {
		return false
	}
	
	// TODO: Implement centralized service validation for user-level shop creation limits
	// This would involve checking user's current organization count against their plan limits
	
	logrus.Debugf("Shop creation validated for user %d", userID)
	return true
}

// validateUserWebhookCreation validates if a user can create a new webhook
func (h *ShopSubscriptionGuardMiddleware) validateUserWebhookCreation(c *gin.Context, userID uint) bool {
	// For user-level webhook creation, use legacy validation during transition
	err := h.validateUserAction(userID, "webhooks")
	if err != nil {
		return false
	}
	
	// TODO: Implement centralized service validation for user-level webhook limits
	// This would involve checking user's current webhook count against their plan limits
	
	logrus.Debugf("Webhook creation validated for user %d", userID)
	return true
}