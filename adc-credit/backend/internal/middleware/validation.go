package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ValidationConfig holds validation configuration
type ValidationConfig struct {
	MaxRequestSize   int64         // Maximum request body size in bytes
	RequestTimeout   time.Duration // Request timeout
	AllowedMethods   []string      // Allowed HTTP methods
	RequiredHeaders  []string      // Headers that must be present
	SanitizeHTML     bool          // Whether to sanitize HTML in string fields
	LogValidationErrors bool       // Whether to log validation errors
}

// DefaultValidationConfig returns default validation configuration
func DefaultValidationConfig() *ValidationConfig {
	return &ValidationConfig{
		MaxRequestSize:      10 * 1024 * 1024, // 10MB
		RequestTimeout:      30 * time.Second,
		AllowedMethods:      []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"},
		RequiredHeaders:     []string{"Content-Type"},
		SanitizeHTML:        true,
		LogValidationErrors: true,
	}
}

// ValidationMiddleware creates a comprehensive input validation middleware
func ValidationMiddleware(config *ValidationConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultValidationConfig()
	}

	return gin.HandlerFunc(func(c *gin.Context) {
		startTime := time.Now()

		// Validate HTTP method
		if !isMethodAllowed(c.Request.Method, config.AllowedMethods) {
			logValidationError(c, "invalid_http_method", fmt.Sprintf("Method %s not allowed", c.Request.Method), config)
			c.JSON(http.StatusMethodNotAllowed, gin.H{
				"error": "Method not allowed",
				"code":  "INVALID_METHOD",
			})
			c.Abort()
			return
		}

		// Validate request size
		if c.Request.ContentLength > config.MaxRequestSize {
			logValidationError(c, "request_too_large", fmt.Sprintf("Request size %d exceeds limit %d", c.Request.ContentLength, config.MaxRequestSize), config)
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": "Request entity too large",
				"code":  "REQUEST_TOO_LARGE",
				"limit": config.MaxRequestSize,
			})
			c.Abort()
			return
		}

		// Validate required headers for non-GET requests
		if c.Request.Method != "GET" && c.Request.Method != "OPTIONS" {
			for _, header := range config.RequiredHeaders {
				if c.GetHeader(header) == "" {
					logValidationError(c, "missing_required_header", fmt.Sprintf("Missing required header: %s", header), config)
					c.JSON(http.StatusBadRequest, gin.H{
						"error": fmt.Sprintf("Missing required header: %s", header),
						"code":  "MISSING_HEADER",
					})
					c.Abort()
					return
				}
			}
		}

		// Validate Content-Type for POST/PUT/PATCH requests with body
		if (c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH") &&
			c.Request.ContentLength > 0 {
			contentType := c.GetHeader("Content-Type")
			if !isValidContentType(contentType) {
				logValidationError(c, "invalid_content_type", fmt.Sprintf("Invalid content type: %s", contentType), config)
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "Unsupported media type",
					"code":  "INVALID_CONTENT_TYPE",
					"supported": []string{"application/json", "application/x-www-form-urlencoded", "multipart/form-data"},
				})
				c.Abort()
				return
			}
		}

		// Validate JSON payload if present
		if strings.Contains(c.GetHeader("Content-Type"), "application/json") && c.Request.ContentLength > 0 {
			if !validateJSONPayload(c, config) {
				c.Abort()
				return
			}
		}

		// Validate query parameters
		if !validateQueryParameters(c, config) {
			c.Abort()
			return
		}

		// Log slow validation
		elapsed := time.Since(startTime)
		if elapsed > 50*time.Millisecond {
			logrus.WithFields(logrus.Fields{
				"path":               c.Request.URL.Path,
				"method":             c.Request.Method,
				"validation_duration": elapsed,
			}).Warn("Slow input validation detected")
		}

		c.Next()
	})
}

// isMethodAllowed checks if HTTP method is in allowed list
func isMethodAllowed(method string, allowedMethods []string) bool {
	for _, allowed := range allowedMethods {
		if method == allowed {
			return true
		}
	}
	return false
}

// isValidContentType checks if content type is supported
func isValidContentType(contentType string) bool {
	validTypes := []string{
		"application/json",
		"application/x-www-form-urlencoded",
		"multipart/form-data",
	}
	
	for _, validType := range validTypes {
		if strings.Contains(strings.ToLower(contentType), validType) {
			return true
		}
	}
	return false
}

// validateJSONPayload validates JSON request body
func validateJSONPayload(c *gin.Context, config *ValidationConfig) bool {
	// Read the body
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logValidationError(c, "body_read_error", fmt.Sprintf("Error reading request body: %v", err), config)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Error reading request body",
			"code":  "BODY_READ_ERROR",
		})
		return false
	}

	// Restore the body for downstream handlers
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Validate JSON syntax
	var jsonData interface{}
	if err := json.Unmarshal(bodyBytes, &jsonData); err != nil {
		logValidationError(c, "invalid_json", fmt.Sprintf("Invalid JSON: %v", err), config)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid JSON format",
			"code":  "INVALID_JSON",
		})
		return false
	}

	// Validate JSON depth (prevent deeply nested objects)
	if !validateJSONDepth(jsonData, 0, 10) {
		logValidationError(c, "json_too_deep", "JSON nesting too deep", config)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "JSON nesting too deep",
			"code":  "JSON_TOO_DEEP",
		})
		return false
	}

	// Sanitize HTML if enabled
	if config.SanitizeHTML {
		sanitizeJSONStrings(jsonData)
	}

	return true
}

// validateQueryParameters validates URL query parameters
func validateQueryParameters(c *gin.Context, config *ValidationConfig) bool {
	queryParams := c.Request.URL.Query()
	
	for key, values := range queryParams {
		// Check parameter name
		if !isValidParameterName(key) {
			logValidationError(c, "invalid_parameter_name", fmt.Sprintf("Invalid parameter name: %s", key), config)
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Invalid parameter name: %s", key),
				"code":  "INVALID_PARAMETER",
			})
			return false
		}

		// Check parameter values
		for _, value := range values {
			if len(value) > 1000 { // Limit parameter value length
				logValidationError(c, "parameter_too_long", fmt.Sprintf("Parameter %s value too long", key), config)
				c.JSON(http.StatusBadRequest, gin.H{
					"error": fmt.Sprintf("Parameter %s value too long", key),
					"code":  "PARAMETER_TOO_LONG",
				})
				return false
			}

			// Validate specific parameter types
			if err := validateParameterValue(key, value); err != nil {
				logValidationError(c, "invalid_parameter_value", err.Error(), config)
				c.JSON(http.StatusBadRequest, gin.H{
					"error": err.Error(),
					"code":  "INVALID_PARAMETER_VALUE",
				})
				return false
			}
		}
	}

	return true
}

// validateJSONDepth checks JSON nesting depth
func validateJSONDepth(data interface{}, currentDepth, maxDepth int) bool {
	if currentDepth > maxDepth {
		return false
	}

	switch v := data.(type) {
	case map[string]interface{}:
		for _, value := range v {
			if !validateJSONDepth(value, currentDepth+1, maxDepth) {
				return false
			}
		}
	case []interface{}:
		for _, item := range v {
			if !validateJSONDepth(item, currentDepth+1, maxDepth) {
				return false
			}
		}
	}

	return true
}

// isValidParameterName checks if parameter name is valid
func isValidParameterName(name string) bool {
	if len(name) == 0 || len(name) > 50 {
		return false
	}
	
	// Allow alphanumeric, underscore, hyphen, dot
	for _, r := range name {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || 
			(r >= '0' && r <= '9') || r == '_' || r == '-' || r == '.') {
			return false
		}
	}
	
	return true
}

// validateParameterValue validates specific parameter values
func validateParameterValue(key, value string) error {
	switch key {
	case "page", "limit", "offset":
		if _, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("parameter %s must be a valid integer", key)
		}
	case "user_id", "shop_id", "api_key_id", "organization_id", "subscription_id":
		if key == "api_key" {
			// API keys can be longer strings
			if len(value) > 128 {
				return fmt.Errorf("parameter %s too long", key)
			}
		} else if strings.HasSuffix(key, "_id") {
			// ID fields should be valid UUIDs
			if _, err := uuid.Parse(value); err != nil {
				return fmt.Errorf("parameter %s must be a valid UUID", key)
			}
		}
	case "sort", "order":
		validSortValues := []string{"asc", "desc", "name", "created_at", "updated_at", "email", "id"}
		if !contains(validSortValues, strings.ToLower(value)) {
			return fmt.Errorf("parameter %s has invalid value: %s", key, value)
		}
	case "format":
		validFormats := []string{"json", "csv", "xml"}
		if !contains(validFormats, strings.ToLower(value)) {
			return fmt.Errorf("parameter %s has invalid format: %s", key, value)
		}
	}

	return nil
}

// sanitizeJSONStrings recursively sanitizes HTML in JSON strings
func sanitizeJSONStrings(data interface{}) {
	switch v := data.(type) {
	case map[string]interface{}:
		for key, value := range v {
			if str, ok := value.(string); ok {
				v[key] = sanitizeHTML(str)
			} else {
				sanitizeJSONStrings(value)
			}
		}
	case []interface{}:
		for i, item := range v {
			if str, ok := item.(string); ok {
				v[i] = sanitizeHTML(str)
			} else {
				sanitizeJSONStrings(item)
			}
		}
	}
}

// sanitizeHTML removes potentially dangerous HTML
func sanitizeHTML(input string) string {
	// Basic HTML sanitization - remove script tags and javascript
	dangerous := []string{
		"<script", "</script>", "javascript:", "onload=", "onerror=", 
		"onclick=", "onmouseover=", "onfocus=", "onblur=", "onchange=",
		"onsubmit=", "onreset=", "onselect=", "onkeydown=", "onkeyup=",
	}
	
	result := input
	for _, pattern := range dangerous {
		result = strings.ReplaceAll(strings.ToLower(result), pattern, "")
	}
	
	return result
}

// contains checks if slice contains string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// logValidationError logs validation errors with context
func logValidationError(c *gin.Context, errorType, message string, config *ValidationConfig) {
	if !config.LogValidationErrors {
		return
	}

	logrus.WithFields(logrus.Fields{
		"error_type":   errorType,
		"path":         c.Request.URL.Path,
		"method":       c.Request.Method,
		"remote_addr":  c.ClientIP(),
		"user_agent":   c.GetHeader("User-Agent"),
		"content_type": c.GetHeader("Content-Type"),
		"message":      message,
	}).Warn("Input validation error")
}

// RequestSizeLimitMiddleware creates middleware for limiting request size
func RequestSizeLimitMiddleware(maxSize int64) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": "Request entity too large",
				"code":  "REQUEST_TOO_LARGE",
				"limit": maxSize,
			})
			c.Abort()
			return
		}
		c.Next()
	})
}

// SecurityHeadersMiddleware adds security headers
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Prevent XSS attacks
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// Prevent MIME type sniffing
		c.Header("X-Content-Type-Options", "nosniff")
		
		// Prevent clickjacking
		c.Header("X-Frame-Options", "DENY")
		
		// Enforce HTTPS
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		
		// Content Security Policy
		c.Header("Content-Security-Policy", "default-src 'self'")
		
		// Referrer Policy
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		
		c.Next()
	})
}