package middleware

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

const (
	// CorrelationIDHeader is the header name for correlation ID
	CorrelationIDHeader = "X-Correlation-ID"
	// CorrelationIDKey is the context key for correlation ID
	CorrelationIDKey = "correlation_id"
	// UserIDKey is the context key for user ID
	UserIDKey = "user_id"
)

// CorrelationIDMiddleware adds correlation ID to all requests and logs
func CorrelationIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get correlation ID from header or generate new one
		correlationID := c.GetHeader(CorrelationIDHeader)
		if correlationID == "" {
			correlationID = uuid.New().String()
		}

		// Set correlation ID in context and response header
		c.Set(CorrelationIDKey, correlationID)
		c.Header(CorrelationIDHeader, correlationID)

		// Create logger with correlation ID
		logger := logrus.WithField("correlation_id", correlationID)
		
		// Add request info to logger
		logger = logger.WithFields(logrus.Fields{
			"method": c.Request.Method,
			"path":   c.Request.URL.Path,
			"origin": c.Request.Header.Get("Origin"),
		})

		// Store logger in context for use in handlers
		c.Set("logger", logger)

		// Log request start
		logger.Info("Request started")

		c.Next()

		// Log request completion
		logger.WithField("status", c.Writer.Status()).Info("Request completed")
	}
}

// GetCorrelationID extracts correlation ID from gin context
func GetCorrelationID(c *gin.Context) string {
	if correlationID, exists := c.Get(CorrelationIDKey); exists {
		return correlationID.(string)
	}
	return ""
}

// GetLogger extracts the correlation-aware logger from gin context
func GetLogger(c *gin.Context) *logrus.Entry {
	if logger, exists := c.Get("logger"); exists {
		return logger.(*logrus.Entry)
	}
	// Fallback to basic logger with correlation ID if available
	if correlationID := GetCorrelationID(c); correlationID != "" {
		return logrus.WithField("correlation_id", correlationID)
	}
	return logrus.NewEntry(logrus.StandardLogger())
}

// AddUserToLogger adds user ID to the correlation-aware logger
func AddUserToLogger(c *gin.Context, userID string) {
	logger := GetLogger(c)
	enhancedLogger := logger.WithField("user_id", userID)
	c.Set("logger", enhancedLogger)
	c.Set(UserIDKey, userID)
}

// LogWithCorrelation creates a logger with correlation ID from context
func LogWithCorrelation(ctx context.Context) *logrus.Entry {
	if correlationID, ok := ctx.Value(CorrelationIDKey).(string); ok {
		return logrus.WithField("correlation_id", correlationID)
	}
	return logrus.NewEntry(logrus.StandardLogger())
}

// ContextWithCorrelationID adds correlation ID to context
func ContextWithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, CorrelationIDKey, correlationID)
}