package middleware

import (
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/verawat1234/adc-sso-service/sdk"
)

// Simple in-memory cache for user data
type userCache struct {
	cache  map[uuid.UUID]userCacheEntry
	mutex  sync.RWMutex
	hits   int
	misses int
}

type userCacheEntry struct {
	user      models.User
	expiresAt time.Time
}

var (
	// Global user cache with 15-minute expiration
	userCacheInstance = &userCache{
		cache: make(map[uuid.UUID]userCacheEntry),
	}
)

// Get user from cache
func (c *userCache) get(id uuid.UUID) (models.User, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, found := c.cache[id]
	if !found {
		c.misses++
		return models.User{}, false
	}

	// Check if entry has expired
	if time.Now().After(entry.expiresAt) {
		delete(c.cache, id)
		c.misses++
		return models.User{}, false
	}

	c.hits++
	return entry.user, true
}

// Set user in cache with 15-minute expiration (increased from 5 minutes)
func (c *userCache) set(user models.User) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[user.ID] = userCacheEntry{
		user:      user,
		expiresAt: time.Now().Add(15 * time.Minute),
	}
}

// GetCacheStats returns cache hit/miss statistics
func (c *userCache) getStats() (hits, misses int) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.hits, c.misses
}

// isInternalAPIKey checks if an API key is internal and should bypass subscription limits
// Returns true if the key should bypass limits, false otherwise
func isInternalAPIKey(apiKeyValue string, apiKey *models.APIKey) bool {
	// Method 1: Check against environment variable list
	internalKeysEnv := os.Getenv("INTERNAL_API_KEYS")
	if internalKeysEnv != "" {
		internalKeys := strings.Split(internalKeysEnv, ",")
		for _, key := range internalKeys {
			if strings.TrimSpace(key) == apiKeyValue {
				return true
			}
		}
	}
	
	// Method 2: Check API key permissions for "internal" or "system" permission
	if apiKey != nil {
		for _, permission := range apiKey.Permissions {
			if permission == "internal" || permission == "system" {
				return true
			}
		}
	}
	
	return false
}

// AuthMiddleware validates JWT tokens for protected routes
func AuthMiddleware() gin.HandlerFunc {
	// Create a local cache for tokens to reduce parsing overhead
	type tokenCacheEntry struct {
		userID    uuid.UUID
		expiresAt time.Time
	}
	tokenCache := make(map[string]tokenCacheEntry)
	var tokenCacheMutex sync.RWMutex

	return func(c *gin.Context) {
		startTime := time.Now()
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header format must be Bearer {token}"})
			c.Abort()
			return
		}

		tokenString := parts[1]
		var userID uuid.UUID
		var cacheHit bool

		// Try to get token from cache first
		tokenCacheMutex.RLock()
		entry, found := tokenCache[tokenString]
		if found && time.Now().Before(entry.expiresAt) {
			userID = entry.userID
			cacheHit = true
		}
		tokenCacheMutex.RUnlock()

		if !cacheHit {
			// Token not in cache, parse and validate
			claims := &jwt.RegisteredClaims{}
			token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}
				return []byte(os.Getenv("JWT_SECRET")), nil
			})

			if err != nil || !token.Valid {
				logger := GetLogger(c)
				logger.WithField("error", err).Warn("Invalid or expired token")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
				c.Abort()
				return
			}

			// Get user ID from token
			var parseErr error
			userID, parseErr = uuid.Parse(claims.Subject)
			if parseErr != nil {
				logger := GetLogger(c)
				logger.WithField("error", parseErr).Warn("Invalid user ID in token")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID in token"})
				c.Abort()
				return
			}

			// Store token in cache for 5 minutes
			tokenCacheMutex.Lock()
			tokenCache[tokenString] = tokenCacheEntry{
				userID:    userID,
				expiresAt: time.Now().Add(5 * time.Minute),
			}
			tokenCacheMutex.Unlock()
		}

		// Try to get user from cache first
		user, found := userCacheInstance.get(userID)
		if !found {
			// If not in cache, fetch from database with optimized query
			// Use Take() instead of First() to avoid the ORDER BY clause
			if err := database.DB.Table("users").Select("id, email, name, picture, role").
				Where("id = ?", userID).
				Where("deleted_at IS NULL").
				Take(&user).Error; err != nil {
				logger := GetLogger(c)
				logger.WithFields(map[string]interface{}{
					"user_id": userID.String(),
					"error": err.Error(),
				}).Warn("User not found in database")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
				c.Abort()
				return
			}

			// Store in cache for future requests
			userCacheInstance.set(user)
		}

		// Set user in context
		c.Set("user", user)
		
		// Add user ID to correlation logger
		AddUserToLogger(c, user.ID.String())

		// Log cache performance if request took too long
		elapsed := time.Since(startTime)
		if elapsed > 100*time.Millisecond {
			hits, misses := userCacheInstance.getStats()
			logger := GetLogger(c)
			logger.WithFields(map[string]interface{}{
				"elapsed_ms": elapsed.Milliseconds(),
				"cache_hits": hits,
				"cache_misses": misses,
			}).Warn("Slow auth validation")
		}

		c.Next()
	}
}

// AdminMiddleware ensures the user has admin role
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found in context"})
			c.Abort()
			return
		}

		u, ok := user.(models.User)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse user from context"})
			c.Abort()
			return
		}

		if u.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SSOAuthMiddleware validates tokens using SSO service first, with JWT fallback
func SSOAuthMiddleware(ssoClient *sdk.SSOClient) gin.HandlerFunc {
	// Create a local cache for tokens to reduce validation overhead
	type tokenCacheEntry struct {
		userID    uuid.UUID
		expiresAt time.Time
		authType  string
	}
	tokenCache := make(map[string]tokenCacheEntry)
	var tokenCacheMutex sync.RWMutex

	return func(c *gin.Context) {
		startTime := time.Now()
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header format must be Bearer {token}"})
			c.Abort()
			return
		}

		tokenString := parts[1]
		var userID uuid.UUID
		var cacheHit bool
		var authType string

		// Try to get token from cache first
		tokenCacheMutex.RLock()
		entry, found := tokenCache[tokenString]
		if found && time.Now().Before(entry.expiresAt) {
			userID = entry.userID
			authType = entry.authType
			cacheHit = true
		}
		tokenCacheMutex.RUnlock()

		if !cacheHit {
			// Try SSO validation first
			if ssoClient != nil {
				logger := GetLogger(c)
				logger.Debug("Attempting SSO token validation first")
				
				validationResp, ssoErr := ssoClient.ValidateToken(tokenString)
				if ssoErr == nil && validationResp.Valid {
					logger.WithField("email", validationResp.Email).Debug("SSO token validation successful")
					
					// Parse user ID from SSO response
					var parseErr error
					userID, parseErr = uuid.Parse(validationResp.UserID)
					if parseErr != nil {
						logger.WithField("user_id", validationResp.UserID).Warn("Failed to parse SSO user ID")
						c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format from SSO"})
						c.Abort()
						return
					}
					
					authType = "sso"
					
					// Cache the SSO validation result
					tokenCacheMutex.Lock()
					tokenCache[tokenString] = tokenCacheEntry{
						userID:    userID,
						expiresAt: validationResp.ExpiresAt,
						authType:  "sso",
					}
					tokenCacheMutex.Unlock()
				} else {
					// SSO validation failed, try JWT as fallback
					logger.WithField("sso_error", ssoErr).Debug("SSO validation failed, trying JWT validation")
					
					claims := &jwt.RegisteredClaims{}
					token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
						if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
							return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
						}
						return []byte(os.Getenv("JWT_SECRET")), nil
					})

					if err != nil || !token.Valid {
						logger.WithField("jwt_error", err).Warn("Both SSO and JWT validation failed")
						c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
						c.Abort()
						return
					}

					// Get user ID from JWT token
					var parseErr error
					userID, parseErr = uuid.Parse(claims.Subject)
					if parseErr != nil {
						logger.WithField("subject", claims.Subject).Warn("Failed to parse JWT user ID")
						c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format in token"})
						c.Abort()
						return
					}
					
					authType = "jwt"
					
					// Cache the JWT validation result
					tokenCacheMutex.Lock()
					tokenCache[tokenString] = tokenCacheEntry{
						userID:    userID,
						expiresAt: claims.ExpiresAt.Time,
						authType:  "jwt",
					}
					tokenCacheMutex.Unlock()
				}
			} else {
				// No SSO client available, use JWT only
				logger := GetLogger(c)
				logger.Debug("No SSO client configured, using JWT validation only")
				
				claims := &jwt.RegisteredClaims{}
				token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
					if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
						return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
					}
					return []byte(os.Getenv("JWT_SECRET")), nil
				})

				if err != nil || !token.Valid {
					logger.WithField("error", err).Warn("JWT validation failed")
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
					c.Abort()
					return
				}

				// Get user ID from token
				var parseErr error
				userID, parseErr = uuid.Parse(claims.Subject)
				if parseErr != nil {
					logger.WithField("subject", claims.Subject).Warn("Failed to parse user ID")
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format in token"})
					c.Abort()
					return
				}
				
				authType = "jwt"
			}
		}

		// Get user from cache or database
		user, found := userCacheInstance.get(userID)
		if !found {
			if err := database.DB.Where("id = ?", userID).First(&user).Error; err != nil {
				logger := GetLogger(c)
				logger.WithField("user_id", userID).Warn("User not found in database")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
				c.Abort()
				return
			}
			userCacheInstance.set(user)
		}

		// Set user in context
		c.Set("user_id", userID)
		c.Set("user", user)
		c.Set("auth_type", authType)

		// Log authentication success
		logger := GetLogger(c)
		duration := time.Since(startTime)
		logger.WithFields(map[string]interface{}{
			"user_id":   userID,
			"auth_type": authType,
			"duration":  duration,
			"cache_hit": cacheHit,
		}).Debug("Authentication successful")

		c.Next()
	}
}

// ValidateAPIKey validates API keys for external API access
func ValidateAPIKey() gin.HandlerFunc {
	// Create a local cache for API keys to reduce database lookups
	type apiKeyCacheEntry struct {
		apiKey    models.APIKey
		expiresAt time.Time
	}
	apiKeyCache := make(map[string]apiKeyCacheEntry)
	var apiKeyCacheMutex sync.RWMutex

	return func(c *gin.Context) {
		startTime := time.Now()
		apiKeyHeader := c.GetHeader("X-API-Key")
		if apiKeyHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key is required"})
			c.Abort()
			return
		}

		var apiKey models.APIKey
		var cacheHit bool

		// Try to get API key from local cache first
		apiKeyCacheMutex.RLock()
		entry, found := apiKeyCache[apiKeyHeader]
		if found && time.Now().Before(entry.expiresAt) {
			apiKey = entry.apiKey
			cacheHit = true
		}
		apiKeyCacheMutex.RUnlock()

		if !cacheHit {
			// API key not in cache, fetch from database
			if err := database.DB.Select("id, user_id, name, key, enabled, permissions, rate_limit_max, rate_limit_rate").
				Where("key = ? AND enabled = ?", apiKeyHeader, true).First(&apiKey).Error; err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
				c.Abort()
				return
			}

			// Store API key in cache for 5 minutes
			apiKeyCacheMutex.Lock()
			apiKeyCache[apiKeyHeader] = apiKeyCacheEntry{
				apiKey:    apiKey,
				expiresAt: time.Now().Add(5 * time.Minute),
			}
			apiKeyCacheMutex.Unlock()

			// Update last used timestamp in a separate goroutine to avoid blocking
			go func(apiKeyID uuid.UUID) {
				now := time.Now()
				database.DB.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Update("last_used", &now)
			}(apiKey.ID)
		}

		// Try to get user from cache first
		user, found := userCacheInstance.get(apiKey.UserID)
		if !found {
			// Get user associated with API key - optimized query to select only necessary fields
			// Remove the ORDER BY clause which is unnecessary and can slow down the query
			if err := database.DB.Table("users").Select("id, email, name, picture, role").
				Where("id = ?", apiKey.UserID).
				Where("deleted_at IS NULL").
				Take(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user associated with API key"})
				c.Abort()
				return
			}

			// Store in cache for future requests
			userCacheInstance.set(user)
		}

		// Check if this is an internal API key that should bypass subscription limits
		isInternal := isInternalAPIKey(apiKeyHeader, &apiKey)
		
		// Set API key, user, and internal flag in context
		c.Set("apiKey", apiKey)
		c.Set("user", user)
		c.Set("is_internal_api_key", isInternal)

		// Log internal API key usage for security monitoring
		if isInternal {
			logger := GetLogger(c)
			logger.WithFields(map[string]interface{}{
				"api_key_id": apiKey.ID,
				"api_key_name": apiKey.Name,
				"user_id": user.ID,
				"user_email": user.Email,
			}).Info("Internal API key used - subscription limits bypassed")
		}

		// Log cache performance if request took too long
		elapsed := time.Since(startTime)
		if elapsed > 100*time.Millisecond {
			hits, misses := userCacheInstance.getStats()
			logrus.WithFields(logrus.Fields{
				"validation_duration": elapsed,
				"cache_hits":          hits,
				"cache_misses":        misses,
			}).Warn("Slow API key validation detected")
		}

		c.Next()
	}
}
