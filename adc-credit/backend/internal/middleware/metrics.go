package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

// Prometheus metrics
var (
	// HTTP request metrics
	httpRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "adc_credit_http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status_code"},
	)

	httpRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "adc_credit_http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	httpRequestSize = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "adc_credit_http_request_size_bytes",
			Help:    "HTTP request size in bytes",
			Buckets: []float64{100, 1000, 10000, 100000, 1000000},
		},
		[]string{"method", "endpoint"},
	)

	httpResponseSize = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "adc_credit_http_response_size_bytes",
			Help:    "HTTP response size in bytes",
			Buckets: []float64{100, 1000, 10000, 100000, 1000000},
		},
		[]string{"method", "endpoint"},
	)

	// Business metrics
	creditOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "adc_credit_operations_total",
			Help: "Total number of credit operations",
		},
		[]string{"operation_type", "status"},
	)

	creditBalanceGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "adc_credit_balance_current",
			Help: "Current credit balance by shop",
		},
		[]string{"shop_id", "subscription_tier"},
	)

	apiKeyValidationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "adc_credit_api_key_validations_total",
			Help: "Total number of API key validations",
		},
		[]string{"status", "cache_hit"},
	)

	apiKeyValidationDuration = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "adc_credit_api_key_validation_duration_seconds",
			Help:    "API key validation duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		},
	)

	// Database metrics
	databaseConnectionsActive = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "adc_credit_database_connections_active",
			Help: "Number of active database connections",
		},
	)

	databaseConnectionsIdle = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "adc_credit_database_connections_idle",
			Help: "Number of idle database connections",
		},
	)

	databaseQueryDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "adc_credit_database_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0},
		},
		[]string{"operation", "table"},
	)

	// External service metrics
	externalServiceRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "adc_credit_external_service_requests_total",
			Help: "Total number of external service requests",
		},
		[]string{"service", "operation", "status_code"},
	)

	externalServiceRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "adc_credit_external_service_request_duration_seconds",
			Help:    "External service request duration in seconds",
			Buckets: []float64{0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0, 25.0, 60.0},
		},
		[]string{"service", "operation"},
	)

	// Cache metrics
	cacheOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "adc_credit_cache_operations_total",
			Help: "Total number of cache operations",
		},
		[]string{"operation", "cache_type", "result"},
	)

	cacheSize = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "adc_credit_cache_size_current",
			Help: "Current number of items in cache",
		},
		[]string{"cache_type"},
	)

	// Application metrics
	applicationInfo = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "adc_credit_application_info",
			Help: "Application information",
		},
		[]string{"version", "environment", "build_time"},
	)

	activeUsers = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "adc_credit_active_users_current",
			Help: "Current number of active users",
		},
	)

	activeShops = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "adc_credit_active_shops_current",
			Help: "Current number of active shops",
		},
	)
)

// PrometheusMiddleware creates a middleware for collecting HTTP metrics
func PrometheusMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()
		
		// Get request size
		requestSize := float64(c.Request.ContentLength)
		if requestSize < 0 {
			requestSize = 0
		}

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(start).Seconds()
		
		// Get endpoint pattern (remove dynamic parts)
		endpoint := normalizeEndpoint(c.FullPath())
		if endpoint == "" {
			endpoint = "unknown"
		}
		
		// Get response size
		responseSize := float64(c.Writer.Size())
		if responseSize < 0 {
			responseSize = 0
		}

		// Record metrics
		statusCode := strconv.Itoa(c.Writer.Status())
		
		httpRequestsTotal.WithLabelValues(c.Request.Method, endpoint, statusCode).Inc()
		httpRequestDuration.WithLabelValues(c.Request.Method, endpoint).Observe(duration)
		
		if requestSize > 0 {
			httpRequestSize.WithLabelValues(c.Request.Method, endpoint).Observe(requestSize)
		}
		
		if responseSize > 0 {
			httpResponseSize.WithLabelValues(c.Request.Method, endpoint).Observe(responseSize)
		}

		// Log slow requests
		if duration > 1.0 {
			logrus.WithFields(logrus.Fields{
				"method":       c.Request.Method,
				"endpoint":     endpoint,
				"duration":     duration,
				"status_code":  statusCode,
				"request_size": requestSize,
				"response_size": responseSize,
			}).Warn("Slow HTTP request detected")
		}
	})
}

// normalizeEndpoint normalizes endpoint paths for metrics
func normalizeEndpoint(path string) string {
	if path == "" {
		return "/"
	}
	
	// Remove dynamic parts like IDs
	// /api/v1/users/123 -> /api/v1/users/:id
	// /api/v1/shops/abc-def/customers -> /api/v1/shops/:id/customers
	
	// This is a simple implementation - in production, you might want
	// to use the actual route pattern from Gin
	return path
}

// RecordCreditOperation records a credit operation metric
func RecordCreditOperation(operationType, status string) {
	creditOperationsTotal.WithLabelValues(operationType, status).Inc()
}

// UpdateCreditBalance updates the credit balance gauge
func UpdateCreditBalance(shopID, subscriptionTier string, balance float64) {
	creditBalanceGauge.WithLabelValues(shopID, subscriptionTier).Set(balance)
}

// RecordAPIKeyValidation records an API key validation metric
func RecordAPIKeyValidation(status string, cacheHit bool, duration time.Duration) {
	cacheHitStr := "false"
	if cacheHit {
		cacheHitStr = "true"
	}
	
	apiKeyValidationsTotal.WithLabelValues(status, cacheHitStr).Inc()
	apiKeyValidationDuration.Observe(duration.Seconds())
}

// UpdateDatabaseConnectionStats updates database connection metrics
func UpdateDatabaseConnectionStats(active, idle int) {
	databaseConnectionsActive.Set(float64(active))
	databaseConnectionsIdle.Set(float64(idle))
}

// RecordDatabaseQuery records a database query metric
func RecordDatabaseQuery(operation, table string, duration time.Duration) {
	databaseQueryDuration.WithLabelValues(operation, table).Observe(duration.Seconds())
}

// RecordExternalServiceRequest records an external service request
func RecordExternalServiceRequest(service, operation, statusCode string, duration time.Duration) {
	externalServiceRequestsTotal.WithLabelValues(service, operation, statusCode).Inc()
	externalServiceRequestDuration.WithLabelValues(service, operation).Observe(duration.Seconds())
}

// RecordCacheOperation records a cache operation
func RecordCacheOperation(operation, cacheType, result string) {
	cacheOperationsTotal.WithLabelValues(operation, cacheType, result).Inc()
}

// UpdateCacheSize updates cache size metrics
func UpdateCacheSize(cacheType string, size int) {
	cacheSize.WithLabelValues(cacheType).Set(float64(size))
}

// SetApplicationInfo sets application information metrics
func SetApplicationInfo(version, environment, buildTime string) {
	applicationInfo.WithLabelValues(version, environment, buildTime).Set(1)
}

// UpdateActiveUsers updates the active users metric
func UpdateActiveUsers(count int) {
	activeUsers.Set(float64(count))
}

// UpdateActiveShops updates the active shops metric
func UpdateActiveShops(count int) {
	activeShops.Set(float64(count))
}

// MetricsCollector provides a way to collect custom metrics
type MetricsCollector struct {
	customGauges   map[string]prometheus.Gauge
	customCounters map[string]prometheus.Counter
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		customGauges:   make(map[string]prometheus.Gauge),
		customCounters: make(map[string]prometheus.Counter),
	}
}

// RegisterGauge registers a custom gauge metric
func (mc *MetricsCollector) RegisterGauge(name, help string) prometheus.Gauge {
	gauge := promauto.NewGauge(prometheus.GaugeOpts{
		Name: name,
		Help: help,
	})
	mc.customGauges[name] = gauge
	return gauge
}

// RegisterCounter registers a custom counter metric
func (mc *MetricsCollector) RegisterCounter(name, help string) prometheus.Counter {
	counter := promauto.NewCounter(prometheus.CounterOpts{
		Name: name,
		Help: help,
	})
	mc.customCounters[name] = counter
	return counter
}

// GetGauge gets a registered gauge
func (mc *MetricsCollector) GetGauge(name string) prometheus.Gauge {
	return mc.customGauges[name]
}

// GetCounter gets a registered counter
func (mc *MetricsCollector) GetCounter(name string) prometheus.Counter {
	return mc.customCounters[name]
}

// BusinessMetrics provides business-specific metrics functions
type BusinessMetrics struct{}

// NewBusinessMetrics creates a new business metrics instance
func NewBusinessMetrics() *BusinessMetrics {
	return &BusinessMetrics{}
}

// RecordShopCreation records a shop creation event
func (bm *BusinessMetrics) RecordShopCreation(shopType, subscriptionTier string) {
	creditOperationsTotal.WithLabelValues("shop_creation", "success").Inc()
	logrus.WithFields(logrus.Fields{
		"shop_type":         shopType,
		"subscription_tier": subscriptionTier,
		"metric":           "shop_creation",
	}).Info("Shop creation recorded")
}

// RecordUserRegistration records a user registration event
func (bm *BusinessMetrics) RecordUserRegistration(source string) {
	creditOperationsTotal.WithLabelValues("user_registration", "success").Inc()
	logrus.WithFields(logrus.Fields{
		"source": source,
		"metric": "user_registration",
	}).Info("User registration recorded")
}

// RecordSubscriptionChange records a subscription change event
func (bm *BusinessMetrics) RecordSubscriptionChange(fromTier, toTier string) {
	creditOperationsTotal.WithLabelValues("subscription_change", "success").Inc()
	logrus.WithFields(logrus.Fields{
		"from_tier": fromTier,
		"to_tier":   toTier,
		"metric":    "subscription_change",
	}).Info("Subscription change recorded")
}