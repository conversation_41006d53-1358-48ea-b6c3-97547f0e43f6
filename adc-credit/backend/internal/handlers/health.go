package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/middleware"
	"github.com/gin-gonic/gin"
)

// HealthCheck returns the health status of the API
func HealthCheck(c *gin.Context) {
	logger := middleware.GetLogger(c)
	logger.Info("Health check requested")

	// Check database connection
	sqlDB, err := database.DB.DB()
	if err != nil {
		logger.WithField("error", err).Error("Database connection error")
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Database connection error",
			"error":   err.Error(),
		})
		return
	}

	if err := sqlDB.Ping(); err != nil {
		logger.WithField("error", err).Error("Database ping failed")
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "Database ping failed",
			"error":   err.<PERSON><PERSON><PERSON>(),
		})
		return
	}

	logger.Info("Health check successful")
	c.<PERSON>(http.StatusOK, gin.H{
		"status":  "ok",
		"message": "API is running",
	})
}
