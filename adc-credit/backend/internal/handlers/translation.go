package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
)

// TranslationRequest represents a request to fetch translation
type TranslationRequest struct {
	ProjectID string `json:"project_id"`
	Namespace string `json:"namespace"`
	Key       string `json:"key"`
	Locale    string `json:"locale"`
}

// TranslationResponse represents the response from Multi-Languages service
type TranslationResponse struct {
	Success bool `json:"success"`
	Data    *struct {
		Key             string `json:"key"`
		Namespace       string `json:"namespace"`
		Locale          string `json:"locale"`
		Value           string `json:"value"`
		AutoTranslated  bool   `json:"auto_translated"`
		CreatedNew      bool   `json:"created_new"`
		TranslationKeyID string `json:"translation_key_id"`
		TranslationID   string `json:"translation_id,omitempty"`
		Confidence      float64 `json:"confidence,omitempty"`
		SourceLocale    string `json:"source_locale,omitempty"`
	} `json:"data,omitempty"`
	Error   string `json:"error,omitempty"`
	Message string `json:"message,omitempty"`
}

// GetTranslation handles fetching translations from Multi-Languages service
// GET /api/v1/translations/:namespace/:key/:locale
func GetTranslation(c *gin.Context) {
	namespace := c.Param("namespace")
	key := c.Param("key")
	locale := c.Param("locale")

	if namespace == "" || key == "" || locale == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing required parameters: namespace, key, or locale",
		})
		return
	}

	// Get configuration from environment
	multiLangURL := os.Getenv("MULTILANG_SERVICE_URL")
	if multiLangURL == "" {
		multiLangURL = "http://localhost:8300" // Default for development
	}

	internalKey := os.Getenv("MULTILANG_INTERNAL_KEY")
	if internalKey == "" {
		internalKey = "adc-internal-2024" // Default for development
	}

	projectID := os.Getenv("CREDIT_PROJECT_ID")
	if projectID == "" {
		projectID = "b90e383d-6e7d-4881-ba9a-c31649719348" // Default project ID
	}

	// Prepare request to Multi-Languages service
	translationReq := TranslationRequest{
		ProjectID: projectID,
		Namespace: namespace,
		Key:       key,
		Locale:    locale,
	}

	jsonData, err := json.Marshal(translationReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to prepare translation request",
		})
		return
	}

	// Make request to Multi-Languages service
	url := fmt.Sprintf("%s/api/v2/internal/translations/fetch", multiLangURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create request to translation service",
		})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", internalKey)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"error":   "Failed to connect to translation service",
		})
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to read translation service response",
		})
		return
	}

	// Parse response
	var translationResp TranslationResponse
	if err := json.Unmarshal(body, &translationResp); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to parse translation service response",
		})
		return
	}

	// Return the translation response
	if resp.StatusCode == http.StatusOK && translationResp.Success {
		c.JSON(http.StatusOK, translationResp)
	} else {
		c.JSON(resp.StatusCode, translationResp)
	}
}

// GetNamespaceTranslations handles fetching all translations for a namespace
// GET /api/v1/translations/:namespace/:locale
func GetNamespaceTranslations(c *gin.Context) {
	namespace := c.Param("namespace")
	locale := c.Param("locale")

	if namespace == "" || locale == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing required parameters: namespace or locale",
		})
		return
	}

	// Get configuration from environment
	multiLangURL := os.Getenv("MULTILANG_SERVICE_URL")
	if multiLangURL == "" {
		multiLangURL = "http://localhost:8300"
	}

	internalKey := os.Getenv("MULTILANG_INTERNAL_KEY")
	if internalKey == "" {
		internalKey = "adc-internal-2024"
	}

	projectID := os.Getenv("CREDIT_PROJECT_ID")
	if projectID == "" {
		projectID = "b90e383d-6e7d-4881-ba9a-c31649719348"
	}

	// Prepare request for namespace translations
	requestData := map[string]interface{}{
		"project_id": projectID,
		"namespace":  namespace,
		"locale":     locale,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to prepare namespace translation request",
		})
		return
	}

	// Make request to Multi-Languages service
	url := fmt.Sprintf("%s/api/v2/internal/translations/namespace", multiLangURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to create request to translation service",
		})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Internal-Key", internalKey)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"error":   "Failed to connect to translation service",
		})
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to read translation service response",
		})
		return
	}

	// Forward the response as-is
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to parse translation service response",
		})
		return
	}

	c.JSON(resp.StatusCode, result)
}