package handlers

import (
	"fmt"
	"log"
	"net/http"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AutoTranslationHandler handles auto-translation settings and processing
type AutoTranslationHandler struct {
	settingsService *services.SettingsService
	processor       *services.AutoTranslationProcessor
}

// NewAutoTranslationHandler creates a new auto-translation handler
func NewAutoTranslationHandler(settingsService *services.SettingsService, processor *services.AutoTranslationProcessor) *AutoTranslationHandler {
	return &AutoTranslationHandler{
		settingsService: settingsService,
		processor:       processor,
	}
}

// GetUserAutoTranslationSettings handles GET /api/v1/users/:user_id/auto-translation/settings
func (h *AutoTranslationHandler) GetUserAutoTranslationSettings(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	settings, err := h.settingsService.GetUserAutoTranslationSettings(userID)
	if err != nil {
		log.Printf("Failed to get user auto-translation settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get settings"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id": userID,
		"settings": settings,
	})
}

// UpdateUserAutoTranslationSettings handles PUT /api/v1/users/:user_id/auto-translation/settings
func (h *AutoTranslationHandler) UpdateUserAutoTranslationSettings(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var settings map[string]interface{}
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settings format"})
		return
	}

	// Update each setting individually
	for key, value := range settings {
		var settingKey string
		switch key {
		case "enabled":
			settingKey = services.AutoTranslationSettings.USER.ENABLED
		case "target_language":
			settingKey = services.AutoTranslationSettings.USER.TARGET_LANGUAGE
		case "confidence_threshold":
			settingKey = services.AutoTranslationSettings.USER.CONFIDENCE_THRESHOLD
		case "fallback_enabled":
			settingKey = services.AutoTranslationSettings.USER.FALLBACK_ENABLED
		default:
			continue // Skip unknown settings
		}

		if err := h.settingsService.SetUserAutoTranslationSetting(userID, settingKey, value); err != nil {
			log.Printf("Failed to set user auto-translation setting %s: %v", settingKey, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update settings"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
		"user_id": userID,
	})
}

// GetShopAutoTranslationSettings handles GET /api/v1/shops/:shop_id/auto-translation/settings
func (h *AutoTranslationHandler) GetShopAutoTranslationSettings(c *gin.Context) {
	shopIDStr := c.Param("shop_id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	settings, err := h.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		log.Printf("Failed to get shop auto-translation settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get settings"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id": shopID,
		"settings": settings,
	})
}

// UpdateShopAutoTranslationSettings handles PUT /api/v1/shops/:shop_id/auto-translation/settings
func (h *AutoTranslationHandler) UpdateShopAutoTranslationSettings(c *gin.Context) {
	shopIDStr := c.Param("shop_id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	var settings map[string]interface{}
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid settings format"})
		return
	}

	// Update each setting individually
	for key, value := range settings {
		var settingKey string
		switch key {
		case "enabled":
			settingKey = services.AutoTranslationSettings.SHOP.ENABLED
		case "batch_processing":
			settingKey = services.AutoTranslationSettings.SHOP.BATCH_PROCESSING
		case "real_time_mode":
			settingKey = services.AutoTranslationSettings.SHOP.REAL_TIME_MODE
		case "smart_triggers":
			settingKey = services.AutoTranslationSettings.SHOP.SMART_TRIGGERS
		default:
			continue // Skip unknown settings
		}

		if err := h.settingsService.SetShopAutoTranslationSetting(shopID, settingKey, value); err != nil {
			log.Printf("Failed to set shop auto-translation setting %s: %v", settingKey, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update settings"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
		"shop_id": shopID,
	})
}

// TriggerAutoTranslation handles POST /api/v1/shops/:shop_id/auto-translation/trigger
func (h *AutoTranslationHandler) TriggerAutoTranslation(c *gin.Context) {
	shopIDStr := c.Param("shop_id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Get shop auto-translation settings
	settings, err := h.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		log.Printf("Failed to get shop auto-translation settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get settings"})
		return
	}

	// Check if auto-translation is enabled
	enabled, ok := settings["enabled"].(bool)
	if !ok || !enabled {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Auto-translation is not enabled for this shop"})
		return
	}

	// Parse optional parameters
	var request struct {
		TargetLanguage      string  `json:"target_language,omitempty"`
		ConfidenceThreshold float64 `json:"confidence_threshold,omitempty"`
		Keys               []string `json:"keys,omitempty"` // Specific translation keys to process
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		// Default behavior if no body provided
		request.TargetLanguage = "en"
		request.ConfidenceThreshold = 0.8
	}

	// Get user settings if user_id is provided
	userIDHeader := c.GetHeader("X-User-ID")
	if userIDHeader != "" {
		userID, err := uuid.Parse(userIDHeader)
		if err == nil {
			userSettings, err := h.settingsService.GetUserAutoTranslationSettings(userID)
			if err == nil {
				// Override with user preferences
				if targetLang, ok := userSettings["target_language"].(string); ok && targetLang != "" {
					request.TargetLanguage = targetLang
				}
				if threshold, ok := userSettings["confidence_threshold"].(float64); ok && threshold > 0 {
					request.ConfidenceThreshold = threshold
				}
			}
		}
	}

	// Submit translation jobs to the background processor
	result, err := h.submitAutoTranslationJobs(shopID, request.TargetLanguage, request.ConfidenceThreshold, request.Keys)
	if err != nil {
		log.Printf("Failed to submit auto-translation jobs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to submit auto-translation jobs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Auto-translation completed",
		"shop_id": shopID,
		"target_language": request.TargetLanguage,
		"processed_keys": result.ProcessedKeys,
		"successful_translations": result.SuccessfulTranslations,
		"failed_translations": result.FailedTranslations,
		"confidence_threshold": request.ConfidenceThreshold,
	})
}

// AutoTranslationResult represents the result of auto-translation processing
type AutoTranslationResult struct {
	ProcessedKeys           int      `json:"processed_keys"`
	SuccessfulTranslations  int      `json:"successful_translations"`
	FailedTranslations      int      `json:"failed_translations"`
	LowConfidenceTranslations int    `json:"low_confidence_translations"`
	ProcessedKeysList       []string `json:"processed_keys_list,omitempty"`
}

// submitAutoTranslationJobs submits auto-translation jobs to the background processor
func (h *AutoTranslationHandler) submitAutoTranslationJobs(shopID uuid.UUID, targetLanguage string, confidenceThreshold float64, specificKeys []string) (*AutoTranslationResult, error) {
	result := &AutoTranslationResult{
		ProcessedKeys:           0,
		SuccessfulTranslations:  0,
		FailedTranslations:      0,
		LowConfidenceTranslations: 0,
		ProcessedKeysList:       []string{},
	}

	// If specific keys are provided, process only those
	if len(specificKeys) > 0 {
		for _, key := range specificKeys {
			err := h.processor.SubmitShopTranslationJob(shopID, key, fmt.Sprintf("Sample text for %s", key), targetLanguage)
			if err != nil {
				log.Printf("Failed to submit job for key %s: %v", key, err)
				result.FailedTranslations++
			} else {
				result.ProcessedKeys++
			}
		}
		result.ProcessedKeysList = specificKeys
	} else {
		// Submit jobs for common translation keys (mock data)
		commonKeys := []string{
			"homepage.title",
			"homepage.welcome_message",
			"navigation.home",
			"navigation.products",
			"navigation.contact",
			"buttons.save",
			"buttons.cancel",
			"forms.submit",
		}

		for _, key := range commonKeys {
			err := h.processor.SubmitShopTranslationJob(shopID, key, fmt.Sprintf("Sample text for %s", key), targetLanguage)
			if err != nil {
				log.Printf("Failed to submit job for key %s: %v", key, err)
				result.FailedTranslations++
			} else {
				result.ProcessedKeys++
			}
		}
		result.ProcessedKeysList = commonKeys
	}

	result.SuccessfulTranslations = result.ProcessedKeys - result.FailedTranslations

	log.Printf("Submitted %d auto-translation jobs for shop %s to %s with confidence threshold %.2f", 
		result.ProcessedKeys, shopID.String(), targetLanguage, confidenceThreshold)

	return result, nil
}

// GetAutoTranslationStatus handles GET /api/v1/shops/:shop_id/auto-translation/status
func (h *AutoTranslationHandler) GetAutoTranslationStatus(c *gin.Context) {
	shopIDStr := c.Param("shop_id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Get shop settings
	settings, err := h.settingsService.GetShopAutoTranslationSettings(shopID)
	if err != nil {
		log.Printf("Failed to get shop auto-translation settings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get settings"})
		return
	}

	// Check if auto-translation is enabled
	enabled, _ := settings["enabled"].(bool)
	batchProcessing, _ := settings["batch_processing"].(bool)
	realTimeMode, _ := settings["real_time_mode"].(bool)

	// Get translation statistics (mock data for now)
	stats := gin.H{
		"total_keys": 100,
		"translated_keys": 75,
		"pending_translations": 25,
		"last_auto_translation": "2025-06-29T01:20:00Z",
		"average_confidence_score": 0.85,
	}

	// Get processor status
	processorStatus := h.processor.GetStatus()
	jobStats := h.processor.GetJobStats()

	c.JSON(http.StatusOK, gin.H{
		"shop_id": shopID,
		"auto_translation_enabled": enabled,
		"batch_processing": batchProcessing,
		"real_time_mode": realTimeMode,
		"statistics": stats,
		"processor_status": processorStatus,
		"job_statistics": jobStats,
	})
}

// HealthCheck handles GET /api/v1/auto-translation/health
func (h *AutoTranslationHandler) HealthCheck(c *gin.Context) {
	// Check Settings Service health
	if err := h.settingsService.Health(); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "unhealthy",
			"settings_service": "unavailable",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "healthy",
		"settings_service": "available",
		"auto_translation": "ready",
	})
}