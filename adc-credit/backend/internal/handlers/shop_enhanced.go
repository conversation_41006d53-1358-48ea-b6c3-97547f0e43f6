package handlers

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// EnhancedShopHandler handles shop operations with SSO organization integration
type EnhancedShopHandler struct {
	serviceContainer *services.ServiceContainer
}

// NewEnhancedShopHandler creates a new enhanced shop handler
func NewEnhancedShopHandler(serviceContainer *services.ServiceContainer) *EnhancedShopHandler {
	return &EnhancedShopHandler{
		serviceContainer: serviceContainer,
	}
}

// CreateShopWithOrganization creates a new shop and corresponding ADC organization
func (h *EnhancedShopHandler) CreateShopWithOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateShopRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"` // "retail", "api_service", "enterprise"
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req CreateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate shop type
	validShopTypes := []string{"retail", "api_service", "enterprise"}
	if req.ShopType == "" {
		req.ShopType = "retail" // Default to retail
	}

	isValidType := false
	for _, validType := range validShopTypes {
		if req.ShopType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
		return
	}

	shopID := uuid.New()
	slug := h.generateShopSlug(req.Name)
	slug = h.ensureUniqueShopSlug(slug, shopID)

	shop := models.Shop{
		ID:           shopID,
		Slug:         slug,
		Name:         req.Name,
		Description:  req.Description,
		ShopType:     req.ShopType,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		OwnerUserID:  user.ID,
	}

	// Use the shop organization service to create shop with SSO integration
	if err := h.serviceContainer.ShopOrganization.CreateShopWithOrganization(&shop, user.ID); err != nil {
		logrus.Errorf("Failed to create shop with organization integration: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop"})
		return
	}

	logrus.Infof("Successfully created shop %s (%s) for user %s with organization integration", shop.ID, shop.Name, user.ID)

	c.JSON(http.StatusCreated, gin.H{
		"shop": shop,
		"message": "Shop created successfully with organization integration",
		"organization_auto_created": true,
	})
}

// AddUserToShop adds a user to a shop and corresponding SSO organization
func (h *EnhancedShopHandler) AddUserToShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	type AddUserRequest struct {
		UserID string `json:"user_id" binding:"required"`
		Role   string `json:"role" binding:"required"`
	}

	var req AddUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse shop ID
	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Verify the requesting user owns the shop
	var shop models.Shop
	if err := h.serviceContainer.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you don't have permission"})
		return
	}

	// Validate role
	validRoles := []string{"owner", "admin", "member"}
	isValidRole := false
	for _, validRole := range validRoles {
		if req.Role == validRole {
			isValidRole = true
			break
		}
	}
	if !isValidRole {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role. Must be one of: owner, admin, member"})
		return
	}

	// Add user to shop and organization
	if err := h.serviceContainer.ShopOrganization.AddUserToShopOrganization(shopUUID, userUUID, req.Role); err != nil {
		logrus.Errorf("Failed to add user to shop with organization integration: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add user to shop"})
		return
	}

	logrus.Infof("Successfully added user %s to shop %s with role %s (organization integration)", userUUID, shopUUID, req.Role)

	c.JSON(http.StatusCreated, gin.H{
		"message": "User added to shop successfully with organization integration",
		"shop_id": shopUUID,
		"user_id": userUUID,
		"role": req.Role,
		"organization_sync": true,
	})
}

// RemoveUserFromShop removes a user from a shop and corresponding SSO organization
func (h *EnhancedShopHandler) RemoveUserFromShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	userIDParam := c.Param("user_id")

	// Parse shop ID
	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Parse user ID
	userUUID, err := uuid.Parse(userIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Verify the requesting user owns the shop
	var shop models.Shop
	if err := h.serviceContainer.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you don't have permission"})
		return
	}

	// Remove user from shop and organization
	if err := h.serviceContainer.ShopOrganization.RemoveUserFromShopOrganization(shopUUID, userUUID); err != nil {
		logrus.Errorf("Failed to remove user from shop with organization integration: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove user from shop"})
		return
	}

	logrus.Infof("Successfully removed user %s from shop %s (organization integration)", userUUID, shopUUID)

	c.JSON(http.StatusOK, gin.H{
		"message": "User removed from shop successfully with organization integration",
		"shop_id": shopUUID,
		"user_id": userUUID,
		"organization_sync": true,
	})
}

// UpdateShopWithOrganization updates a shop and corresponding SSO organization
func (h *EnhancedShopHandler) UpdateShopWithOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	type UpdateShopRequest struct {
		Name         *string `json:"name"`
		Description  *string `json:"description"`
		ShopType     *string `json:"shop_type"`
		ContactEmail *string `json:"contact_email"`
		ContactPhone *string `json:"contact_phone"`
	}

	var req UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse shop ID
	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Verify the requesting user owns the shop
	var shop models.Shop
	if err := h.serviceContainer.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you don't have permission"})
		return
	}

	// Build update data
	updateData := make(map[string]interface{})
	
	if req.Name != nil {
		updateData["name"] = *req.Name
	}
	if req.Description != nil {
		updateData["description"] = *req.Description
	}
	if req.ShopType != nil {
		// Validate shop type
		validShopTypes := []string{"retail", "api_service", "enterprise"}
		isValidType := false
		for _, validType := range validShopTypes {
			if *req.ShopType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
			return
		}
		updateData["shop_type"] = *req.ShopType
	}
	if req.ContactEmail != nil {
		updateData["contact_email"] = *req.ContactEmail
	}
	if req.ContactPhone != nil {
		updateData["contact_phone"] = *req.ContactPhone
	}

	if len(updateData) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Update shop and organization
	if err := h.serviceContainer.ShopOrganization.UpdateShopOrganization(shopUUID, updateData); err != nil {
		logrus.Errorf("Failed to update shop with organization integration: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop"})
		return
	}

	// Fetch updated shop
	if err := h.serviceContainer.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch updated shop"})
		return
	}

	logrus.Infof("Successfully updated shop %s with organization integration", shopUUID)

	c.JSON(http.StatusOK, gin.H{
		"shop": shop,
		"message": "Shop updated successfully with organization integration",
		"organization_sync": true,
	})
}

// SyncShopToOrganization manually synchronizes a shop with SSO organization
func (h *EnhancedShopHandler) SyncShopToOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Parse shop ID
	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Verify the requesting user owns the shop or is admin
	var shop models.Shop
	query := h.serviceContainer.DB.Where("id = ?", shopUUID)
	if user.Role != "admin" {
		query = query.Where("owner_user_id = ?", user.ID)
	}
	
	if err := query.First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you don't have permission"})
		return
	}

	// Sync shop to organization
	if err := h.serviceContainer.ShopOrganization.SyncShopToOrganization(shopUUID); err != nil {
		logrus.Errorf("Failed to sync shop to organization: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to sync shop to organization"})
		return
	}

	logrus.Infof("Successfully synced shop %s to organization", shopUUID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Shop synchronized to organization successfully",
		"shop_id": shopUUID,
		"sync_completed": true,
	})
}

// GetShopOrganization retrieves the SSO organization for a shop
func (h *EnhancedShopHandler) GetShopOrganization(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Parse shop ID
	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Verify the requesting user has access to the shop
	var shop models.Shop
	query := h.serviceContainer.DB.Where("id = ?", shopUUID)
	if user.Role != "admin" {
		// Check if user owns the shop or is a member
		query = query.Where("owner_user_id = ? OR id IN (SELECT shop_id FROM shop_users WHERE user_id = ?)", user.ID, user.ID)
	}
	
	if err := query.First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found or you don't have permission"})
		return
	}

	// Get organization from SSO service
	organization, err := h.serviceContainer.ShopOrganization.GetOrganizationForShop(shopUUID)
	if err != nil {
		logrus.Errorf("Failed to get organization for shop %s: %v", shopUUID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found in SSO service"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop": shop,
		"organization": organization,
		"integration_status": "active",
	})
}

// Helper functions (copied from original shop.go and enhanced)

func (h *EnhancedShopHandler) generateShopSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)
	
	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")
	
	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")
	
	// Limit length to 50 characters
	if len(slug) > 50 {
		slug = slug[:50]
		// Remove trailing hyphen if created by truncation
		slug = strings.TrimRight(slug, "-")
	}
	
	return slug
}

func (h *EnhancedShopHandler) ensureUniqueShopSlug(baseSlug string, shopID uuid.UUID) string {
	slug := baseSlug
	counter := 1
	
	for {
		var existingShop models.Shop
		err := h.serviceContainer.DB.Where("slug = ? AND id != ?", slug, shopID).First(&existingShop).Error
		if err != nil {
			// Slug is unique
			break
		}
		
		// Slug exists, try with counter
		slug = baseSlug + "-" + string(rune('0'+counter))
		counter++
		
		// Prevent infinite loop
		if counter > 100 {
			slug = baseSlug + "-" + shopID.String()[:8]
			break
		}
	}
	
	return slug
}