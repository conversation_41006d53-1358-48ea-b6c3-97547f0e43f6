package handlers

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AddShopCredit adds credit to a customer's account
func AddShopCredit(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	customerID := c.Param("customerId")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	var customer models.ShopCustomer
	if err := database.DB.First(&customer, "id = ? AND shop_id = ?", customerID, shop.ID).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "Customer not found"})
		return
	}

	type AddCreditRequest struct {
		Amount      int    `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
	}

	var req AddCreditRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Update credit balance
	customer.CreditBalance += req.Amount
	if err := tx.Save(&customer).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record
	transaction := models.ShopCreditTransaction{
		ID:          uuid.New(),
		ShopID:      shop.ID,
		CustomerID:  customer.ID,
		Type:        "credit_add",
		Amount:      req.Amount,
		Description: req.Description,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits added successfully",
		"credit_balance": customer.CreditBalance,
		"transaction":    transaction,
	})
}

// GenerateCreditCode generates a unique credit code for a merchant shop
func GenerateCreditCode(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	type GenerateCodeRequest struct {
		Amount      int    `json:"amount" binding:"required,min=1"`
		Description string `json:"description"`
		ExpiresIn   int    `json:"expires_in"` // In days, 0 means no expiration
	}

	var req GenerateCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate a random code
	code, err := generateRandomCode(12)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate code"})
		return
	}

	// Set expiration date if provided
	var expiresAt *time.Time
	if req.ExpiresIn > 0 {
		expiry := time.Now().AddDate(0, 0, req.ExpiresIn)
		expiresAt = &expiry
	}

	// Create credit code
	creditCode := models.CreditCode{
		ID:          uuid.New(),
		ShopID:      shop.ID,
		Code:        code,
		Amount:      req.Amount,
		Description: req.Description,
		ExpiresAt:   expiresAt,
	}

	if err := database.DB.Create(&creditCode).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create credit code"})
		return
	}

	// Generate QR code
	qrCodeData := fmt.Sprintf("shop:%s;code:%s;amount:%d", shop.ID, code, req.Amount)
	qrCode, err := utils.GenerateQRCodeWithPrefix(qrCodeData, 256, 256)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate QR code"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"credit_code": creditCode,
		"qr_code":     qrCode,
	})
}

// GetCreditCodes returns all credit codes for a merchant shop
func GetCreditCodes(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	var codes []models.CreditCode
	if err := database.DB.Where("shop_id = ?", shop.ID).Find(&codes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch credit codes"})
		return
	}

	c.JSON(http.StatusOK, codes)
}

// GenerateQRCode generates a QR code for an existing credit code
func GenerateQRCode(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	type GenerateQRRequest struct {
		Code   string `json:"code" binding:"required"`
		Amount int    `json:"amount" binding:"required,min=1"`
	}

	var req GenerateQRRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate QR code
	qrCodeData := fmt.Sprintf("shop:%s;code:%s;amount:%d", shop.ID, req.Code, req.Amount)
	qrCode, err := utils.GenerateQRCodeWithPrefix(qrCodeData, 256, 256)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate QR code"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"qr_code": qrCode,
	})
}

// GetMerchantCreditStats returns credit statistics for all shops owned by the merchant
func GetMerchantCreditStats(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get all shops owned by the user
	var shops []models.MerchantShop
	if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&shops).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch merchant shops"})
		return
	}

	// If no shops found, return zeros
	if len(shops) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"total_credits_issued":  0,
			"total_credits_redeemed": 0,
		})
		return
	}

	// Extract shop IDs
	var shopIDs []uuid.UUID
	for _, shop := range shops {
		shopIDs = append(shopIDs, shop.ID)
	}

	// Calculate total credits issued (sum of all positive transactions)
	var totalIssued int64
	database.DB.Model(&models.ShopCreditTransaction{}).
		Where("shop_id IN ? AND amount > 0", shopIDs).
		Select("COALESCE(SUM(amount), 0)").
		Row().Scan(&totalIssued)

	// Calculate total credits redeemed (sum of all negative transactions, but return as positive number)
	var totalRedeemed int64
	database.DB.Model(&models.ShopCreditTransaction{}).
		Where("shop_id IN ? AND amount < 0", shopIDs).
		Select("COALESCE(SUM(ABS(amount)), 0)").
		Row().Scan(&totalRedeemed)

	c.JSON(http.StatusOK, gin.H{
		"total_credits_issued":   totalIssued,
		"total_credits_redeemed": totalRedeemed,
	})
}

// RedeemCreditCode redeems a credit code for a customer
func RedeemCreditCode(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type RedeemCodeRequest struct {
		Code string `json:"code" binding:"required"`
	}

	var req RedeemCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find the credit code
	var creditCode models.CreditCode
	if err := database.DB.Where("code = ? AND is_redeemed = ?", req.Code, false).First(&creditCode).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Credit code not found or already redeemed"})
		return
	}

	// Check if code has expired
	if creditCode.ExpiresAt != nil && time.Now().After(*creditCode.ExpiresAt) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Credit code has expired"})
		return
	}

	// Find the shop
	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ?", creditCode.ShopID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	// Find or create customer
	var customer models.ShopCustomer
	if err := database.DB.Where("shop_id = ? AND user_id = ?", shop.ID, user.ID).First(&customer).Error; err != nil {
		// Customer doesn't exist, create a new one
		customer = models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        shop.ID,
			UserID:        user.ID,
			CreditBalance: 0,
		}

		if err := database.DB.Create(&customer).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create customer record"})
			return
		}
	}

	// Start a transaction
	tx := database.DB.Begin()

	// Update credit code
	now := time.Now()
	creditCode.IsRedeemed = true
	creditCode.RedeemedByID = &user.ID
	creditCode.RedeemedAt = &now
	if err := tx.Save(&creditCode).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit code"})
		return
	}

	// Update customer credit balance
	customer.CreditBalance += creditCode.Amount
	if err := tx.Save(&customer).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record
	transaction := models.ShopCreditTransaction{
		ID:          uuid.New(),
		ShopID:      shop.ID,
		CustomerID:  customer.ID,
		Type:        "credit_redeem",
		Amount:      creditCode.Amount,
		Description: "Redeemed credit code",
		Reference:   creditCode.ID.String(),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credit code redeemed successfully",
		"credit_balance": customer.CreditBalance,
		"transaction":    transaction,
	})
}

// generateRandomCode generates a random string of the specified length
func generateRandomCode(length int) (string, error) {
	buffer := make([]byte, length)
	_, err := rand.Read(buffer)
	if err != nil {
		return "", err
	}

	// Convert to base64 and remove non-alphanumeric characters
	code := base64.URLEncoding.EncodeToString(buffer)
	if len(code) > length {
		code = code[:length]
	}
	return code, nil
}

// GetShopTransactions returns all credit transactions for a merchant shop
func GetShopTransactions(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.MerchantShop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Merchant shop not found"})
		return
	}

	var transactions []models.ShopCreditTransaction
	if err := database.DB.Where("shop_id = ?", shop.ID).Order("created_at DESC").Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shop transactions"})
		return
	}

	// Fetch customer details for each transaction
	for i := range transactions {
		var customer models.ShopCustomer
		if err := database.DB.Select("id, user_id").First(&customer, "id = ?", transactions[i].CustomerID).Error; err == nil {
			var user models.User
			if err := database.DB.Select("id, name, email").First(&user, "id = ?", customer.UserID).Error; err == nil {
				transactions[i].Customer.User = user
			}
		}
	}

	c.JSON(http.StatusOK, transactions)
}
