package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ProcessMonthlyQuotaReset handles the monthly quota reset process
// POST /api/v1/admin/quota/reset-monthly
func ProcessMonthlyQuotaReset(c *gin.Context) {
	// Create monthly quota reset service
	quotaService := services.NewMonthlyQuotaResetService()

	// Process the reset
	result, err := quotaService.ProcessMonthlyQuotaResets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to process monthly quota reset",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Monthly quota reset processed successfully",
		"data": result,
	})
}

// GetUserMonthlyUsage gets monthly usage for a specific user
// GET /api/v1/admin/quota/usage/:user_id?year=2024&month=12
func GetUserMonthlyUsage(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID format",
		})
		return
	}

	// Parse year and month from query parameters
	yearStr := c.DefaultQuery("year", "")
	monthStr := c.DefaultQuery("month", "")
	
	var year, month int
	if yearStr != "" && monthStr != "" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid year format",
			})
			return
		}
		
		month, err = strconv.Atoi(monthStr)
		if err != nil || month < 1 || month > 12 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid month format (must be 1-12)",
			})
			return
		}
	} else {
		// Default to current month
		now := time.Now()
		year = now.Year()
		month = int(now.Month())
	}

	// Create service and get usage
	quotaService := services.NewMonthlyQuotaResetService()
	usage, err := quotaService.GetUserMonthlyUsage(userID, year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user monthly usage",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": usage,
	})
}

// ForceResetUserQuota manually resets quota for a specific user
// POST /api/v1/admin/quota/reset-user/:user_id
func ForceResetUserQuota(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID format",
		})
		return
	}

	// Create service and force reset
	quotaService := services.NewMonthlyQuotaResetService()
	err = quotaService.ForceResetUserQuota(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to reset user quota",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User quota reset successfully",
		"user_id": userID,
	})
}

// GetMonthlyUsageStats gets monthly usage statistics for all users
// GET /api/v1/admin/quota/stats?year=2024&month=12
func GetMonthlyUsageStats(c *gin.Context) {
	// Parse year and month from query parameters
	yearStr := c.DefaultQuery("year", "")
	monthStr := c.DefaultQuery("month", "")
	
	var year, month int
	var err error
	if yearStr != "" && monthStr != "" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid year format",
			})
			return
		}
		
		month, err = strconv.Atoi(monthStr)
		if err != nil || month < 1 || month > 12 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid month format (must be 1-12)",
			})
			return
		}
	} else {
		// Default to current month
		now := time.Now()
		year = now.Year()
		month = int(now.Month())
	}

	// Get database instance
	db := database.DB

	// Query monthly usage stats
	type UsageStats struct {
		TotalUsers           int64 `json:"total_users"`
		TotalQRCodes         int64 `json:"total_qr_codes"`
		TotalAPICalls        int64 `json:"total_api_calls"`
		TotalCreditsConsumed int64 `json:"total_credits_consumed"`
		AvgQRCodesPerUser    float64 `json:"avg_qr_codes_per_user"`
		AvgAPICallsPerUser   float64 `json:"avg_api_calls_per_user"`
		AvgCreditsPerUser    float64 `json:"avg_credits_per_user"`
	}

	var stats UsageStats
	
	// Get aggregated stats
	err = db.Model(&models.MonthlyUsageTracking{}).
		Where("year = ? AND month = ?", year, month).
		Select(`
			COUNT(*) as total_users,
			SUM(qr_codes_generated) as total_qr_codes,
			SUM(api_calls_made) as total_api_calls,
			SUM(credits_consumed) as total_credits_consumed,
			AVG(qr_codes_generated) as avg_qr_codes_per_user,
			AVG(api_calls_made) as avg_api_calls_per_user,
			AVG(credits_consumed) as avg_credits_per_user
		`).
		Scan(&stats).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get monthly usage stats",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"year": year,
		"month": month,
		"data": stats,
	})
}
