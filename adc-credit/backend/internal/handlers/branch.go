package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetBranches returns all branches for an organization
func GetBranches(c *gin.Context) {
	user := c.Must<PERSON>et("user").(models.User)
	orgID := c.Query("organization_id")
	if orgID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing organization_id query parameter"})
		return
	}

	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	// Check if user has access to this organization
	var organization models.Organization
	if user.Role == "admin" {
		// Admin can access any organization
		if err := database.DB.First(&organization, "id = ?", organizationID).Error; err != nil {
			c.<PERSON>(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only access organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", organizationID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	var branches []models.Branch
	if err := database.DB.Where("organization_id = ?", organizationID).Find(&branches).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch branches"})
		return
	}

	c.JSON(http.StatusOK, branches)
}

// GetBranch returns a specific branch
func GetBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	orgID := c.Query("organization_id")
	if orgID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing organization_id query parameter"})
		return
	}
	branchID := c.Param("id")

	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this organization
	var organization models.Organization
	if user.Role == "admin" {
		// Admin can access any organization
		if err := database.DB.First(&organization, "id = ?", organizationID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only access organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", organizationID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	var branch models.Branch
	if err := database.DB.First(&branch, "id = ? AND organization_id = ?", branchUUID, organizationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// CreateBranch creates a new branch for an organization
func CreateBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	orgID := c.Query("organization_id")
	if orgID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing organization_id query parameter"})
		return
	}

	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	// Check if user has access to this organization
	var organization models.Organization
	if user.Role == "admin" {
		// Admin can access any organization
		if err := database.DB.First(&organization, "id = ?", organizationID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only access organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", organizationID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	type CreateBranchRequest struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	var req CreateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	branch := models.Branch{
		ID:             uuid.New(),
		OrganizationID: organizationID,
		Name:           req.Name,
		Description:    req.Description,
	}

	if err := database.DB.Create(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create branch"})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateBranch updates an existing branch
func UpdateBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	orgID := c.Query("organization_id")
	if orgID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing organization_id query parameter"})
		return
	}
	branchID := c.Param("id")

	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this organization
	var organization models.Organization
	if user.Role == "admin" {
		// Admin can access any organization
		if err := database.DB.First(&organization, "id = ?", organizationID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only access organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", organizationID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	var branch models.Branch
	if err := database.DB.First(&branch, "id = ? AND organization_id = ?", branchUUID, organizationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	type UpdateBranchRequest struct {
		Name        string `json:"name"`
		Description string `json:"description"`
	}

	var req UpdateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Name != "" {
		branch.Name = req.Name
	}
	branch.Description = req.Description

	if err := database.DB.Save(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update branch"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranch deletes a branch
func DeleteBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	orgID := c.Query("organization_id")
	if orgID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing organization_id query parameter"})
		return
	}
	branchID := c.Param("id")

	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this organization
	var organization models.Organization
	if user.Role == "admin" {
		// Admin can access any organization
		if err := database.DB.First(&organization, "id = ?", organizationID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	} else {
		// Regular users can only access organizations they own
		if err := database.DB.First(&organization, "id = ? AND owner_user_id = ?", organizationID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Organization not found"})
			return
		}
	}

	var branch models.Branch
	if err := database.DB.First(&branch, "id = ? AND organization_id = ?", branchUUID, organizationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if branch has users
	var userCount int64
	if err := database.DB.Model(&models.User{}).Where("branch_id = ?", branchUUID).Count(&userCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check users"})
		return
	}

	if userCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete branch with users"})
		return
	}

	if err := database.DB.Delete(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete branch"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Branch deleted successfully"})
}
