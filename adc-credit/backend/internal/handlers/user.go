package handlers

import (
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetCurrentUser returns the current authenticated user
func GetCurrentUser(c *gin.Context) {
	currentUser := c.MustGet("user").(models.User)
	
	// Get fresh user data from database
	var user models.User
	if err := database.DB.First(&user, "id = ?", currentUser.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}
	
	c.J<PERSON>(http.StatusOK, user)
}

// UpdateUser updates the current user's profile
func UpdateUser(c *gin.Context) {
	currentUser := c.MustGet("user").(models.User)
	
	// Get the user from database to ensure we have the latest version
	var user models.User
	if err := database.DB.First(&user, "id = ?", currentUser.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}

	type UpdateUserRequest struct {
		Name    *string `json:"name"`
		Picture *string `json:"picture"`
		Email   *string `json:"email"`
		Role    *string `json:"role"` // Should be rejected
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and reject role changes
	if req.Role != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Role modification not allowed through profile update"})
		return
	}

	hasUpdates := false

	// Validate and update name if provided
	if req.Name != nil {
		trimmedName := strings.TrimSpace(*req.Name)
		if trimmedName == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Name cannot be empty"})
			return
		}
		if len(trimmedName) > 255 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Name too long (maximum 255 characters)"})
			return
		}
		user.Name = trimmedName
		hasUpdates = true
	}

	// Validate email format if provided
	if req.Email != nil {
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(*req.Email) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email format"})
			return
		}
		user.Email = *req.Email
		hasUpdates = true
	}

	// Update picture if provided
	if req.Picture != nil {
		user.Picture = *req.Picture
		hasUpdates = true
	}

	// Check if any fields were actually updated
	if !hasUpdates {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No valid fields provided for update"})
		return
	}

	// Use Updates instead of Save to ensure the changes are persisted
	updateData := make(map[string]interface{})
	if req.Name != nil {
		updateData["name"] = user.Name
	}
	if req.Picture != nil {
		updateData["picture"] = user.Picture
	}
	if req.Email != nil {
		updateData["email"] = user.Email
	}
	updateData["updated_at"] = time.Now()

	if err := database.DB.Model(&user).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}
	
	// Reload the user to get the updated data
	if err := database.DB.First(&user, "id = ?", user.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reload user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// Admin handlers

// GetAllUsers returns all users (admin only)
func GetAllUsers(c *gin.Context) {
	var users []models.User
	if err := database.DB.Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetUser returns a specific user by ID (admin only)
func GetUser(c *gin.Context) {
	id := c.Param("id")

	userID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var user models.User
	if err := database.DB.First(&user, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUserByAdmin updates a user's profile (admin only)
func UpdateUserByAdmin(c *gin.Context) {
	id := c.Param("id")

	userID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var user models.User
	if err := database.DB.First(&user, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	type UpdateUserRequest struct {
		Name    string `json:"name"`
		Picture string `json:"picture"`
		Role    string `json:"role"`
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		user.Name = req.Name
	}
	if req.Picture != "" {
		user.Picture = req.Picture
	}
	if req.Role != "" {
		user.Role = req.Role
	}

	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUser deletes a user (admin only)
func DeleteUser(c *gin.Context) {
	id := c.Param("id")

	userID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if err := database.DB.Delete(&models.User{}, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}
