package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShopBranchAPIKeys returns all API keys for a specific shop branch
func GetShopBranchAPIKeys(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")

	// Parse branch UUID
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Verify branch ownership through shop
	var branch models.ShopBranch
	if err := database.DB.Preload("Shop").First(&branch, "id = ?", branchUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if user has access to the shop that owns this branch
	if user.Role != "admin" && branch.Shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	var apiKeys []models.APIKey
	if err := database.DB.Where("shop_branch_id = ?", branch.ID).Find(&apiKeys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch API keys"})
		return
	}

	c.JSON(http.StatusOK, apiKeys)
}

// CreateShopBranchAPIKey creates a new API key for a specific shop branch
func CreateShopBranchAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")

	// Parse branch UUID
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Verify branch ownership through shop
	var branch models.ShopBranch
	if err := database.DB.Preload("Shop").First(&branch, "id = ?", branchUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if user has access to the shop that owns this branch
	if user.Role != "admin" && branch.Shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	type CreateAPIKeyRequest struct {
		Name        string   `json:"name" binding:"required"`
		Permissions []string `json:"permissions"`
	}

	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate API key
	key, err := generateAPIKey()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}

	apiKey := models.APIKey{
		ID:           uuid.New(),
		UserID:       user.ID,
		ShopID:       &branch.ShopID,
		ShopBranchID: &branch.ID,
		Name:         req.Name,
		Key:          key,
		Permissions:  req.Permissions,
		Enabled:      true,
	}

	if err := database.DB.Create(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create API key"})
		return
	}

	c.JSON(http.StatusCreated, apiKey)
}

// GetShopBranchAPIKey returns a specific API key for a shop branch
func GetShopBranchAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	keyID := c.Param("keyId")

	// Parse branch UUID
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Verify branch ownership through shop
	var branch models.ShopBranch
	if err := database.DB.Preload("Shop").First(&branch, "id = ?", branchUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if user has access to the shop that owns this branch
	if user.Role != "admin" && branch.Shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_branch_id = ?", keyID, branch.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// UpdateShopBranchAPIKey updates an existing API key for a shop branch
func UpdateShopBranchAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	keyID := c.Param("keyId")

	// Parse branch UUID
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Verify branch ownership through shop
	var branch models.ShopBranch
	if err := database.DB.Preload("Shop").First(&branch, "id = ?", branchUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if user has access to the shop that owns this branch
	if user.Role != "admin" && branch.Shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_branch_id = ?", keyID, branch.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	type UpdateAPIKeyRequest struct {
		Name        string   `json:"name"`
		Permissions []string `json:"permissions"`
		Enabled     *bool    `json:"enabled"`
	}

	var req UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		apiKey.Name = req.Name
	}
	if req.Permissions != nil {
		apiKey.Permissions = req.Permissions
	}
	if req.Enabled != nil {
		apiKey.Enabled = *req.Enabled
	}

	if err := database.DB.Save(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update API key"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// DeleteShopBranchAPIKey deletes an API key for a shop branch
func DeleteShopBranchAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	keyID := c.Param("keyId")

	// Parse branch UUID
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Verify branch ownership through shop
	var branch models.ShopBranch
	if err := database.DB.Preload("Shop").First(&branch, "id = ?", branchUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Check if user has access to the shop that owns this branch
	if user.Role != "admin" && branch.Shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_branch_id = ?", keyID, branch.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	if err := database.DB.Delete(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete API key"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "API key deleted successfully"})
}
