package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetWebhooks returns all webhooks for the current user
func GetWebhooks(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var webhooks []models.Webhook
	if err := database.DB.Where("user_id = ?", user.ID).Find(&webhooks).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch webhooks"})
		return
	}

	c.<PERSON>(http.StatusOK, webhooks)
}

// GetWebhook returns a specific webhook
func GetWebhook(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	webhookID, err := uuid.Parse(id)
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid webhook ID"})
		return
	}

	var webhook models.Webhook
	if err := database.DB.Where("id = ? AND user_id = ?", webhookID, user.ID).First(&webhook).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Webhook not found"})
		return
	}

	c.JSON(http.StatusOK, webhook)
}

// CreateWebhook creates a new webhook
func CreateWebhook(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// TODO: Implement webhook limit checking with shop-based subscriptions
	// For now, allow webhook creation without limits to maintain functionality
	// The legacy user-based subscription model is no longer supported
	
	// Count existing webhooks for basic validation
	var count int64
	database.DB.Model(&models.Webhook{}).Where("user_id = ?", user.ID).Count(&count)
	
	// Set a basic limit of 10 webhooks per user as a temporary measure
	const maxWebhooksPerUser = 10
	if int(count) >= maxWebhooksPerUser {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Webhook limit reached - maximum 10 webhooks per user",
			"limit": maxWebhooksPerUser,
		})
		return
	}

	type CreateWebhookRequest struct {
		Name   string   `json:"name" binding:"required"`
		URL    string   `json:"url" binding:"required,url"`
		Secret string   `json:"secret"`
		Events []string `json:"events" binding:"required"`
	}

	var req CreateWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate events
	validEvents := map[string]bool{
		"credit.consumed":        true,
		"credit.added":           true,
		"credit.scheduled":       true,
		"api_key.created":        true,
		"api_key.updated":        true,
		"api_key.deleted":        true,
		"subscription.created":   true,
		"subscription.updated":   true,
		"subscription.cancelled": true,
	}

	for _, event := range req.Events {
		if !validEvents[event] {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event: " + event})
			return
		}
	}

	webhook := models.Webhook{
		ID:     uuid.New(),
		UserID: user.ID,
		Name:   req.Name,
		URL:    req.URL,
		Secret: req.Secret,
		Events: req.Events,
		Active: true,
	}

	if err := database.DB.Create(&webhook).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create webhook"})
		return
	}

	c.JSON(http.StatusCreated, webhook)
}

// UpdateWebhook updates an existing webhook
func UpdateWebhook(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	webhookID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook ID"})
		return
	}

	type UpdateWebhookRequest struct {
		Name   string   `json:"name"`
		URL    string   `json:"url" binding:"omitempty,url"`
		Secret string   `json:"secret"`
		Events []string `json:"events"`
		Active bool     `json:"active"`
	}

	var req UpdateWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var webhook models.Webhook
	if err := database.DB.Where("id = ? AND user_id = ?", webhookID, user.ID).First(&webhook).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Webhook not found"})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		webhook.Name = req.Name
	}
	if req.URL != "" {
		webhook.URL = req.URL
	}
	if req.Secret != "" {
		webhook.Secret = req.Secret
	}
	if req.Events != nil {
		// Validate events
		validEvents := map[string]bool{
			"credit.consumed":        true,
			"credit.added":           true,
			"credit.scheduled":       true,
			"api_key.created":        true,
			"api_key.updated":        true,
			"api_key.deleted":        true,
			"subscription.created":   true,
			"subscription.updated":   true,
			"subscription.cancelled": true,
		}

		for _, event := range req.Events {
			if !validEvents[event] {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event: " + event})
				return
			}
		}
		webhook.Events = req.Events
	}
	webhook.Active = req.Active

	if err := database.DB.Save(&webhook).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update webhook"})
		return
	}

	c.JSON(http.StatusOK, webhook)
}

// DeleteWebhook deletes a webhook
func DeleteWebhook(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	webhookID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook ID"})
		return
	}

	var webhook models.Webhook
	if err := database.DB.Where("id = ? AND user_id = ?", webhookID, user.ID).First(&webhook).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Webhook not found"})
		return
	}

	if err := database.DB.Delete(&webhook).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete webhook"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Webhook deleted successfully"})
}

// GetWebhookDeliveries returns the delivery history for a webhook
func GetWebhookDeliveries(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	id := c.Param("id")

	webhookID, err := uuid.Parse(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook ID"})
		return
	}

	// Verify webhook belongs to user
	var webhook models.Webhook
	if err := database.DB.Where("id = ? AND user_id = ?", webhookID, user.ID).First(&webhook).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Webhook not found"})
		return
	}

	// Get deliveries
	var deliveries []models.WebhookDelivery
	if err := database.DB.Where("webhook_id = ?", webhookID).
		Order("created_at DESC").
		Limit(100).
		Find(&deliveries).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch webhook deliveries"})
		return
	}

	c.JSON(http.StatusOK, deliveries)
}
