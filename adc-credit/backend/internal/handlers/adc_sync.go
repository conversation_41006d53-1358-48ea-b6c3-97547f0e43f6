package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ADCSyncHandler handles ADC synchronization operations
type ADCSyncHandler struct {
	serviceContainer *services.ServiceContainer
}

// NewADCSyncHandler creates a new ADC sync handler
func NewADCSyncHandler(serviceContainer *services.ServiceContainer) *ADCSyncHandler {
	return &ADCSyncHandler{
		serviceContainer: serviceContainer,
	}
}

// SyncShopToADC manually syncs a shop to the ADC Subscription Service
func (h *ADCSyncHandler) SyncShopToADC(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid shop ID",
			"message": "Shop ID must be a valid UUID",
		})
		return
	}

	// Get the shop subscription service
	shopSubscriptionService := h.serviceContainer.GetShopSubscriptionService()

	// Sync the shop to ADC
	err = shopSubscriptionService.SyncShopToADC(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to sync shop to ADC",
			"message": err.Error(),
			"shop_id": shopID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Shop successfully synced to ADC Subscription Service",
		"shop_id": shopID,
	})
}

// SyncAllShopsToADC syncs all shops to the ADC Subscription Service (for bulk migration)
func (h *ADCSyncHandler) SyncAllShopsToADC(c *gin.Context) {
	// Get all shops from the database
	var shops []struct {
		ID   uuid.UUID `json:"id"`
		Name string    `json:"name"`
		Slug string    `json:"slug"`
	}

	err := h.serviceContainer.DB.Table("merchant_shops").
		Select("id, name, slug").
		Find(&shops).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve shops",
			"message": err.Error(),
		})
		return
	}

	shopSubscriptionService := h.serviceContainer.GetShopSubscriptionService()
	
	syncResults := make(map[string]interface{})
	successCount := 0
	errorCount := 0

	// Sync each shop
	for _, shop := range shops {
		err := shopSubscriptionService.SyncShopToADC(shop.ID)
		if err != nil {
			syncResults[shop.ID.String()] = gin.H{
				"status":  "error",
				"message": err.Error(),
				"name":    shop.Name,
			}
			errorCount++
		} else {
			syncResults[shop.ID.String()] = gin.H{
				"status": "success",
				"name":   shop.Name,
			}
			successCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"status":        "completed",
		"message":       "Bulk shop sync completed",
		"total_shops":   len(shops),
		"success_count": successCount,
		"error_count":   errorCount,
		"results":       syncResults,
	})
}

// GetADCSubscriptionStatus checks the status of a shop's subscription in ADC
func (h *ADCSyncHandler) GetADCSubscriptionStatus(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid shop ID",
			"message": "Shop ID must be a valid UUID",
		})
		return
	}

	// Get organization mapping service
	orgMapper := h.serviceContainer.GetOrganizationMapper()

	// Get subscription from ADC
	subscription, err := orgMapper.GetSubscriptionForShop(shopID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Subscription not found in ADC",
			"message": err.Error(),
			"shop_id": shopID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":        "found",
		"shop_id":       shopID,
		"org_id":        subscription.OrganizationID,
		"plan":          subscription.Plan.Name,
		"plan_price":    subscription.Plan.MonthlyPrice,
		"subscription_status": subscription.Status,
		"period_start":  subscription.CurrentPeriodStart,
		"period_end":    subscription.CurrentPeriodEnd,
		"usage":         subscription.CurrentUsage,
	})
}