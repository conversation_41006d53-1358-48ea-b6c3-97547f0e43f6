package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/webhook"
)

// StripeWebhookV2Handler handles Stripe webhooks using the service container approach
type StripeWebhookV2Handler struct {
	serviceContainer *services.ServiceContainer
}

// NewStripeWebhookV2Handler creates a new Stripe webhook handler with service container
func NewStripeWebhookV2Handler(serviceContainer *services.ServiceContainer) *StripeWebhookV2Handler {
	return &StripeWebhookV2Handler{
		serviceContainer: serviceContainer,
	}
}

// HandleStripeWebhookV2 processes Stripe webhook events using shop-based subscriptions
func (h *StripeWebhookV2Handler) HandleStripeWebhookV2(c *gin.Context) {
	logrus.Info("Processing Stripe webhook event")

	// Read the request body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logrus.Error("Failed to read webhook request body: ", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// Get the webhook secret from environment variables
	endpointSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if endpointSecret == "" {
		logrus.Error("Stripe webhook secret not configured")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Stripe webhook secret not configured"})
		return
	}

	// Verify the webhook signature
	event, err := webhook.ConstructEvent(body, c.GetHeader("Stripe-Signature"), endpointSecret)
	if err != nil {
		logrus.Error("Webhook signature verification failed: ", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Webhook signature verification failed: %v", err)})
		return
	}

	logrus.Infof("Processing webhook event type: %s", event.Type)

	// Handle the event based on its type
	switch event.Type {
	case "checkout.session.completed":
		err = h.handleCheckoutSessionCompleted(event)
	case "customer.subscription.created":
		err = h.handleSubscriptionCreated(event)
	case "customer.subscription.updated":
		err = h.handleSubscriptionUpdated(event)
	case "customer.subscription.deleted":
		err = h.handleSubscriptionDeleted(event)
	case "invoice.payment_succeeded":
		err = h.handleInvoicePaymentSucceeded(event)
	case "invoice.payment_failed":
		err = h.handleInvoicePaymentFailed(event)
	default:
		logrus.Infof("Unhandled webhook event type: %s", event.Type)
		c.JSON(http.StatusOK, gin.H{"received": true, "handled": false})
		return
	}

	if err != nil {
		logrus.Error("Failed to process webhook event: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process event: %v", err)})
		return
	}

	logrus.Info("Successfully processed webhook event")
	c.JSON(http.StatusOK, gin.H{"received": true, "handled": true})
}

// handleCheckoutSessionCompleted processes a completed checkout session
func (h *StripeWebhookV2Handler) handleCheckoutSessionCompleted(event stripe.Event) error {
	var session stripe.CheckoutSession
	if err := json.Unmarshal(event.Data.Raw, &session); err != nil {
		return fmt.Errorf("failed to parse checkout session: %v", err)
	}

	logrus.Infof("Processing checkout session completed: %s", session.ID)

	// Extract metadata from the session
	shopIDStr := session.Metadata["shop_id"]
	if shopIDStr == "" {
		return fmt.Errorf("shop_id not found in session metadata")
	}

	tierIDStr := session.Metadata["tier_id"]
	if tierIDStr == "" {
		return fmt.Errorf("tier_id not found in session metadata")
	}

	// Parse the shop ID
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		return fmt.Errorf("invalid shop ID: %v", err)
	}

	// Parse the tier ID
	tierID, err := strconv.Atoi(tierIDStr)
	if err != nil {
		return fmt.Errorf("invalid tier ID: %v", err)
	}

	// Get the shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ?", shopID).First(&shop).Error; err != nil {
		return fmt.Errorf("shop not found: %v", err)
	}

	// Get the subscription tier
	var tier models.SubscriptionTier
	if err := h.serviceContainer.DB.First(&tier, tierID).Error; err != nil {
		return fmt.Errorf("subscription tier not found: %v", err)
	}

	// Start a database transaction
	tx := h.serviceContainer.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Check for existing active subscriptions for this shop
	var activeSubscriptions []models.ShopSubscription
	if err := tx.Where("shop_id = ? AND status = ?", shopID, "active").Find(&activeSubscriptions).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check existing subscriptions: %v", err)
	}

	// Cancel existing active subscriptions
	if len(activeSubscriptions) > 0 {
		now := time.Now()
		for _, activeSubscription := range activeSubscriptions {
			activeSubscription.EndDate = &now
			activeSubscription.Status = "cancelled"

			if err := tx.Save(&activeSubscription).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to cancel existing subscription: %v", err)
			}

			logrus.Infof("Cancelled existing subscription %s for shop %s", activeSubscription.ID, shopID)
		}
	}

	// Create a new subscription
	subscription := models.ShopSubscription{
		ID:                   uuid.New(),
		ShopID:               shopID,
		SubscriptionTierID:   uint(tierID),
		StartDate:            time.Now(),
		AutoRenew:            true,
		Status:               "active",
		CreditBalance:        tier.CreditLimit,
		StripeSubscriptionID: session.Subscription.ID,
	}

	if err := tx.Create(&subscription).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create subscription: %v", err)
	}

	// Create transaction record for initial credits
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopID,
		ShopSubscriptionID: subscription.ID,
		InitiatedByUserID:  shop.OwnerID, // Use shop owner as the initiator
		Type:               "credit_add",
		Amount:             tier.CreditLimit,
		Description:        "Initial credits for " + tier.Name + " subscription",
		Reference:          "stripe_checkout_" + session.ID,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create transaction record: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logrus.Infof("Successfully created subscription %s for shop %s", subscription.ID, shopID)

	// Sync with ADC Subscription Service if available
	if h.serviceContainer.SubscriptionSync != nil {
		go func() {
			ctx := c.Request.Context()
			if _, err := h.serviceContainer.SubscriptionSync.SyncShopSubscription(ctx, shopID); err != nil {
				logrus.Errorf("Failed to sync subscription with ADC service: %v", err)
			}
		}()
	}

	return nil
}

// handleSubscriptionCreated processes a created subscription
func (h *StripeWebhookV2Handler) handleSubscriptionCreated(event stripe.Event) error {
	// This is typically handled by the checkout.session.completed event
	logrus.Info("Subscription created event received, handled by checkout.session.completed")
	return nil
}

// handleSubscriptionUpdated processes an updated subscription
func (h *StripeWebhookV2Handler) handleSubscriptionUpdated(event stripe.Event) error {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		return fmt.Errorf("failed to parse subscription: %v", err)
	}

	logrus.Infof("Processing subscription updated: %s", subscription.ID)

	// Find the subscription by Stripe ID
	var dbSubscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("stripe_subscription_id = ?", subscription.ID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Update the subscription status based on the Stripe subscription status
	oldStatus := dbSubscription.Status
	switch subscription.Status {
	case stripe.SubscriptionStatusActive:
		dbSubscription.Status = "active"
	case stripe.SubscriptionStatusPastDue:
		dbSubscription.Status = "past_due"
	case stripe.SubscriptionStatusUnpaid:
		dbSubscription.Status = "unpaid"
	case stripe.SubscriptionStatusCanceled:
		dbSubscription.Status = "cancelled"
		now := time.Now()
		dbSubscription.EndDate = &now
		dbSubscription.AutoRenew = false
	case stripe.SubscriptionStatusIncomplete:
		dbSubscription.Status = "incomplete"
	case stripe.SubscriptionStatusIncompleteExpired:
		dbSubscription.Status = "expired"
	case stripe.SubscriptionStatusTrialing:
		dbSubscription.Status = "trialing"
	}

	// Save the updated subscription
	if err := h.serviceContainer.DB.Save(&dbSubscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	logrus.Infof("Updated subscription %s status from %s to %s", dbSubscription.ID, oldStatus, dbSubscription.Status)

	// Sync with ADC Subscription Service if available
	if h.serviceContainer.SubscriptionSync != nil {
		go func() {
			ctx := event.Request.Context()
			if _, err := h.serviceContainer.SubscriptionSync.SyncShopSubscription(ctx, dbSubscription.ShopID); err != nil {
				logrus.Errorf("Failed to sync subscription with ADC service: %v", err)
			}
		}()
	}

	return nil
}

// handleSubscriptionDeleted processes a deleted subscription
func (h *StripeWebhookV2Handler) handleSubscriptionDeleted(event stripe.Event) error {
	var subscription stripe.Subscription
	if err := json.Unmarshal(event.Data.Raw, &subscription); err != nil {
		return fmt.Errorf("failed to parse subscription: %v", err)
	}

	logrus.Infof("Processing subscription deleted: %s", subscription.ID)

	// Find the subscription by Stripe ID
	var dbSubscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("stripe_subscription_id = ?", subscription.ID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Update the subscription status to cancelled
	dbSubscription.Status = "cancelled"
	now := time.Now()
	dbSubscription.EndDate = &now
	dbSubscription.AutoRenew = false

	// Save the updated subscription
	if err := h.serviceContainer.DB.Save(&dbSubscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	logrus.Infof("Marked subscription %s as cancelled", dbSubscription.ID)

	// Sync with ADC Subscription Service if available
	if h.serviceContainer.SubscriptionSync != nil {
		go func() {
			ctx := event.Request.Context()
			if _, err := h.serviceContainer.SubscriptionSync.SyncShopSubscription(ctx, dbSubscription.ShopID); err != nil {
				logrus.Errorf("Failed to sync subscription with ADC service: %v", err)
			}
		}()
	}

	return nil
}

// handleInvoicePaymentSucceeded processes a successful invoice payment
func (h *StripeWebhookV2Handler) handleInvoicePaymentSucceeded(event stripe.Event) error {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		return fmt.Errorf("failed to parse invoice: %v", err)
	}

	logrus.Infof("Processing invoice payment succeeded: %s", invoice.ID)

	// Find the subscription by Stripe ID
	var dbSubscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("stripe_subscription_id = ?", invoice.Subscription.ID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Ensure subscription is active
	if dbSubscription.Status != "active" {
		dbSubscription.Status = "active"
		dbSubscription.EndDate = nil

		if err := h.serviceContainer.DB.Save(&dbSubscription).Error; err != nil {
			return fmt.Errorf("failed to update subscription status: %v", err)
		}

		logrus.Infof("Reactivated subscription %s after successful payment", dbSubscription.ID)
	}

	// Create transaction record for payment
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             dbSubscription.ShopID,
		ShopSubscriptionID: dbSubscription.ID,
		Type:               "payment_succeeded",
		Amount:             int(invoice.AmountPaid),
		Description:        "Subscription payment succeeded",
		Reference:          "stripe_invoice_" + invoice.ID,
	}

	if err := h.serviceContainer.DB.Create(&transaction).Error; err != nil {
		return fmt.Errorf("failed to create transaction record: %v", err)
	}

	return nil
}

// handleInvoicePaymentFailed processes a failed invoice payment
func (h *StripeWebhookV2Handler) handleInvoicePaymentFailed(event stripe.Event) error {
	var invoice stripe.Invoice
	if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
		return fmt.Errorf("failed to parse invoice: %v", err)
	}

	logrus.Infof("Processing invoice payment failed: %s", invoice.ID)

	// Find the subscription by Stripe ID
	var dbSubscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("stripe_subscription_id = ?", invoice.Subscription.ID).First(&dbSubscription).Error; err != nil {
		return fmt.Errorf("subscription not found: %v", err)
	}

	// Update subscription status to past_due
	dbSubscription.Status = "past_due"

	if err := h.serviceContainer.DB.Save(&dbSubscription).Error; err != nil {
		return fmt.Errorf("failed to update subscription status: %v", err)
	}

	// Create transaction record for failed payment
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             dbSubscription.ShopID,
		ShopSubscriptionID: dbSubscription.ID,
		Type:               "payment_failed",
		Amount:             int(invoice.AmountDue),
		Description:        "Subscription payment failed",
		Reference:          "stripe_invoice_" + invoice.ID,
	}

	if err := h.serviceContainer.DB.Create(&transaction).Error; err != nil {
		return fmt.Errorf("failed to create transaction record: %v", err)
	}

	logrus.Infof("Marked subscription %s as past_due due to payment failure", dbSubscription.ID)

	return nil
}