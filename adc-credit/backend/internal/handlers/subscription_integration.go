package handlers

import (
	"net/http"
	"strconv"

	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// SubscriptionIntegrationHandler handles ADC Subscription Service integration endpoints
type SubscriptionIntegrationHandler struct {
	serviceContainer *services.ServiceContainer
}

// NewSubscriptionIntegrationHandler creates a new subscription integration handler
func NewSubscriptionIntegrationHandler(serviceContainer *services.ServiceContainer) *SubscriptionIntegrationHandler {
	return &SubscriptionIntegrationHandler{
		serviceContainer: serviceContainer,
	}
}

// GetShopSubscription retrieves subscription information for a shop using the shop subscription service
func (h *SubscriptionIntegrationHandler) GetShopSubscription(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopService := h.serviceContainer.GetShopSubscriptionService()
	tier, err := shopService.GetShopSubscriptionInfo(shopID)
	if err != nil {
		logrus.Errorf("Failed to get subscription for shop %d: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Subscription not found",
			"message": "No subscription found for this shop",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"subscription": map[string]interface{}{
			"shop_id":    shopID,
			"tier_name":  tier.Name,
			"tier_price": tier.Price,
			"limits": map[string]interface{}{
				"max_shops":               tier.MaxShops,
				"max_customers_per_shop":  tier.MaxCustomersPerShop,
				"max_api_keys_per_shop":   tier.MaxAPIKeysPerShop,
				"max_branches_per_shop":   tier.MaxBranchesPerShop,
				"max_qr_codes_per_month":  tier.MaxQRCodesPerMonth,
				"max_webhooks":            tier.MaxWebhooks,
				"credit_limit":            tier.CreditLimit,
				"analytics_history_days":  tier.AnalyticsHistoryDays,
				"support_level":           tier.SupportLevel,
				"advanced_analytics":      tier.AdvancedAnalytics,
			},
		},
	})
}

// GetShopSubscriptionUsage retrieves current usage statistics for a shop
func (h *SubscriptionIntegrationHandler) GetShopSubscriptionUsage(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	usage, err := h.serviceContainer.GetSubscriptionUsage(shopID)
	if err != nil {
		logrus.Errorf("Failed to get usage for shop %d: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get usage statistics",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id": shopID,
		"usage":   usage,
	})
}

// MigrateShopToADC migrates a shop's subscription to the ADC Subscription Service
func (h *SubscriptionIntegrationHandler) MigrateShopToADC(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// TODO: Implement shop migration to ADC Subscription Service
	// err = h.serviceContainer.MigrateShopToADC(shopID)
	// if err != nil {
	//	logrus.Errorf("Failed to migrate shop %d to ADC: %v", shopID, err)
	//	c.JSON(http.StatusInternalServerError, gin.H{
	//		"error":   "Migration failed",
	//		"message": err.Error(),
	//	})
	//	return
	// }
	
	logrus.Infof("Shop migration to ADC requested for shop %s - not yet implemented", shopID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Shop successfully migrated to ADC Subscription Service",
		"shop_id": shopID,
	})
}

// SetMigrationMode switches between legacy-first and ADC-first modes
func (h *SubscriptionIntegrationHandler) SetMigrationMode(c *gin.Context) {
	var request struct {
		UseLegacyFirst bool `json:"use_legacy_first"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	h.serviceContainer.SetMigrationMode(request.UseLegacyFirst)

	c.JSON(http.StatusOK, gin.H{
		"message":         "Migration mode updated successfully",
		"use_legacy_first": request.UseLegacyFirst,
	})
}

// CheckSubscriptionLimit validates subscription limits for a specific action
func (h *SubscriptionIntegrationHandler) CheckSubscriptionLimit(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	limitType := c.Query("limit_type")
	if limitType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "limit_type query parameter is required"})
		return
	}

	currentUsageStr := c.Query("current_usage")
	if currentUsageStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "current_usage query parameter is required"})
		return
	}

	currentUsage, err := strconv.Atoi(currentUsageStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid current_usage value"})
		return
	}

	shopService := h.serviceContainer.GetShopSubscriptionService()
	allowed, err := shopService.CheckSubscriptionLimit(shopID, limitType, currentUsage)
	if err != nil {
		logrus.Errorf("Failed to check subscription limit for shop %d: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check subscription limit",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":       shopID,
		"limit_type":    limitType,
		"current_usage": currentUsage,
		"allowed":       allowed,
	})
}

// GetSubscriptionFeatures retrieves available features for a shop's subscription
func (h *SubscriptionIntegrationHandler) GetSubscriptionFeatures(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopService := h.serviceContainer.GetShopSubscriptionService()

	// Check various features
	features := map[string]bool{}
	featureList := []string{"advanced_analytics", "premium_support", "unlimited_customers"}

	for _, feature := range featureList {
		enabled, err := shopService.IsFeatureEnabled(shopID, feature)
		if err != nil {
			logrus.Warnf("Failed to check feature %s for shop %d: %v", feature, shopID, err)
			features[feature] = false // Default to disabled on error
		} else {
			features[feature] = enabled
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":  shopID,
		"features": features,
	})
}

// GetMigrationStatus returns the current migration status for the service
func (h *SubscriptionIntegrationHandler) GetMigrationStatus(c *gin.Context) {
	// This would return information about which shops have been migrated,
	// current migration mode, etc.
	c.JSON(http.StatusOK, gin.H{
		"migration_status": "in_progress",
		"migration_mode":   "legacy_first",
		"migrated_shops":   0, // TODO: Implement actual counting
		"total_shops":      0, // TODO: Implement actual counting
		"last_migration":   nil,
	})
}

