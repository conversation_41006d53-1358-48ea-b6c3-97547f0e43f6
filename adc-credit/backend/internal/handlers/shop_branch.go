package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShopBranches returns all branches for a shop
func GetShopBranches(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Query("shop_id")
	if shopID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing shop_id query parameter"})
		return
	}

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has access to this shop
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
			c.<PERSON>(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var branches []models.ShopBranch
	if err := database.DB.Where("shop_id = ?", shopUUID).Find(&branches).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch branches"})
		return
	}

	c.JSON(http.StatusOK, branches)
}

// GetShopBranch returns a specific branch by ID
func GetShopBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	shopID := c.Query("shop_id")

	if shopID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing shop_id query parameter"})
		return
	}

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this shop
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var branch models.ShopBranch
	if err := database.DB.First(&branch, "id = ? AND shop_id = ?", branchUUID, shopUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// CreateShopBranch creates a new branch for a shop
func CreateShopBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Query("shop_id")
	if shopID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing shop_id query parameter"})
		return
	}

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has access to this shop
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	type CreateShopBranchRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		BranchType   string `json:"branch_type"` // "location", "department", "division"
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
		Address      string `json:"address"`
	}

	var req CreateShopBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate branch type
	validBranchTypes := []string{"location", "department", "division"}
	if req.BranchType == "" {
		req.BranchType = "location" // Default to location
	}
	
	isValidType := false
	for _, validType := range validBranchTypes {
		if req.BranchType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch type. Must be one of: location, department, division"})
		return
	}

	branch := models.ShopBranch{
		ID:           uuid.New(),
		ShopID:       shopUUID,
		Name:         req.Name,
		Description:  req.Description,
		BranchType:   req.BranchType,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		Address:      req.Address,
	}

	if err := database.DB.Create(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create branch"})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateShopBranch updates an existing branch
func UpdateShopBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	shopID := c.Query("shop_id")

	if shopID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing shop_id query parameter"})
		return
	}

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this shop
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var branch models.ShopBranch
	if err := database.DB.First(&branch, "id = ? AND shop_id = ?", branchUUID, shopUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	type UpdateShopBranchRequest struct {
		Name         string `json:"name"`
		Description  string `json:"description"`
		BranchType   string `json:"branch_type"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
		Address      string `json:"address"`
	}

	var req UpdateShopBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		branch.Name = req.Name
	}
	if req.Description != "" {
		branch.Description = req.Description
	}
	if req.BranchType != "" {
		// Validate branch type
		validBranchTypes := []string{"location", "department", "division"}
		isValidType := false
		for _, validType := range validBranchTypes {
			if req.BranchType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch type. Must be one of: location, department, division"})
			return
		}
		branch.BranchType = req.BranchType
	}
	if req.ContactEmail != "" {
		branch.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		branch.ContactPhone = req.ContactPhone
	}
	if req.Address != "" {
		branch.Address = req.Address
	}

	if err := database.DB.Save(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update branch"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteShopBranch deletes a branch
func DeleteShopBranch(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	branchID := c.Param("id")
	shopID := c.Query("shop_id")

	if shopID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing shop_id query parameter"})
		return
	}

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Check if user has access to this shop
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopUUID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopUUID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var branch models.ShopBranch
	if err := database.DB.First(&branch, "id = ? AND shop_id = ?", branchUUID, shopUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	if err := database.DB.Delete(&branch).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete branch"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Branch deleted successfully"})
}
