package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"github.com/sirupsen/logrus"
)

// CentralizedStripeHandler handles Stripe operations through centralized ADC Subscription Service
type CentralizedStripeHandler struct {
	serviceContainer   *services.ServiceContainer
	subscriptionClient *subscriptionSDK.Client
}

// NewCentralizedStripeHandler creates a new centralized Stripe handler
func NewCentralizedStripeHandler(serviceContainer *services.ServiceContainer) *CentralizedStripeHandler {
	return &CentralizedStripeHandler{
		serviceContainer:   serviceContainer,
		subscriptionClient: serviceContainer.SubscriptionClient,
	}
}

// CreateCheckoutSessionCentralized creates a Stripe checkout session via centralized service
func (h *CentralizedStripeHandler) CreateCheckoutSessionCentralized(c *gin.Context) {
	// Check for internal API key bypass first
	if h.isInternalAPIKey(c) {
		logrus.Info("Internal API key detected - bypassing checkout session creation")
		c.<PERSON>(http.StatusOK, gin.H{
			"message": "Internal API key - checkout session creation bypassed",
			"bypass":  true,
		})
		return
	}

	// Get authenticated user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	userObj := user.(models.User)

	type CreateCheckoutRequest struct {
		PlanID       string `json:"plan_id" binding:"required"`
		ShopID       string `json:"shop_id" binding:"required"`
		BillingCycle string `json:"billing_cycle"`
	}

	var req CreateCheckoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse and validate shop ID
	shopID, err := uuid.Parse(req.ShopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Verify shop ownership
	if !h.verifyShopOwnership(shopID, userObj.ID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Shop not found or not owned by user"})
		return
	}

	// Parse plan ID
	planID, err := uuid.Parse(req.PlanID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid plan ID"})
		return
	}

	// Set default billing cycle
	billingCycle := "monthly"
	if req.BillingCycle != "" {
		if req.BillingCycle != "monthly" && req.BillingCycle != "yearly" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid billing cycle. Must be 'monthly' or 'yearly'"})
			return
		}
		billingCycle = req.BillingCycle
	}

	// Create subscription via centralized service
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create subscription request for centralized service
	// Note: In our architecture, Shop ID maps to Organization ID
	subscriptionReq := &subscriptionSDK.CreateSubscriptionRequest{
		OrganizationID: shopID, // Shop == Organization in our model
		PlanID:         planID,
		BillingCycle:   billingCycle,
	}

	subscription, err := h.subscriptionClient.CreateSubscription(ctx, subscriptionReq)
	if err != nil {
		logrus.Errorf("Failed to create subscription via centralized service: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create subscription",
			"message": "Unable to process subscription request through centralized service",
		})
		return
	}

	// Get checkout URL from subscription response
	// The centralized service should return a checkout URL from Stripe
	checkoutURL, ok := subscription.Metadata["checkout_url"].(string)
	if !ok || checkoutURL == "" {
		logrus.Error("No checkout URL returned from centralized subscription service")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Invalid subscription response",
			"message": "No checkout URL provided by centralized service",
		})
		return
	}

	// Log subscription creation for audit trail
	logrus.Infof("Subscription created via centralized service for shop %s, user %s, plan %s", 
		shopID, userObj.ID, planID)

	c.JSON(http.StatusOK, gin.H{
		"checkout_url":    checkoutURL,
		"subscription_id": subscription.ID,
		"plan_id":         planID,
		"billing_cycle":   billingCycle,
		"organization_id": shopID, // Return organization ID for frontend tracking
	})
}

// GetSubscriptionPlansCentralized retrieves available subscription plans from centralized service
func (h *CentralizedStripeHandler) GetSubscriptionPlansCentralized(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get service scope for credit service
	serviceScope := "credit"

	plans, err := h.subscriptionClient.GetPlans(ctx, serviceScope)
	if err != nil {
		logrus.Errorf("Failed to get subscription plans from centralized service: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve subscription plans",
			"message": "Unable to fetch plans from centralized service",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"plans":        plans,
		"service":      "credit",
		"total_plans":  len(plans),
	})
}

// GetShopSubscriptionCentralized retrieves shop subscription from centralized service
func (h *CentralizedStripeHandler) GetShopSubscriptionCentralized(c *gin.Context) {
	// Get shop ID from URL parameter
	shopIDStr := c.Param("shopId")
	if shopIDStr == "" {
		shopIDStr = c.Param("id")
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Get authenticated user for ownership verification
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	userObj := user.(models.User)

	// Verify shop ownership (unless internal API key)
	if !h.isInternalAPIKey(c) && !h.verifyShopOwnership(shopID, userObj.ID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Shop not found or not owned by user"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get subscription from centralized service
	// Note: Shop ID maps to Organization ID in centralized service
	subscription, err := h.subscriptionClient.GetOrganizationSubscription(ctx, shopID)
	if err != nil {
		logrus.Warnf("Failed to get subscription from centralized service for shop %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Subscription not found",
			"message": "No active subscription found for this shop",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"subscription":    subscription,
		"organization_id": shopID,
		"service":         "credit",
	})
}

// CancelSubscriptionCentralized cancels a subscription via centralized service
func (h *CentralizedStripeHandler) CancelSubscriptionCentralized(c *gin.Context) {
	// Check for internal API key bypass first
	if h.isInternalAPIKey(c) {
		logrus.Info("Internal API key detected - bypassing subscription cancellation")
		c.JSON(http.StatusOK, gin.H{
			"message": "Internal API key - subscription cancellation bypassed",
			"bypass":  true,
		})
		return
	}

	// Get shop ID from URL parameter
	shopIDStr := c.Param("shopId")
	if shopIDStr == "" {
		shopIDStr = c.Param("id")
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Get authenticated user for ownership verification
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	userObj := user.(models.User)

	// Verify shop ownership
	if !h.verifyShopOwnership(shopID, userObj.ID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Shop not found or not owned by user"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// Cancel subscription via centralized service
	err = h.subscriptionClient.CancelSubscription(ctx, shopID)
	if err != nil {
		logrus.Errorf("Failed to cancel subscription via centralized service for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to cancel subscription",
			"message": "Unable to cancel subscription through centralized service",
		})
		return
	}

	// Log cancellation for audit trail
	logrus.Infof("Subscription cancelled via centralized service for shop %s, user %s", shopID, userObj.ID)

	c.JSON(http.StatusOK, gin.H{
		"message":         "Subscription cancelled successfully",
		"organization_id": shopID,
		"cancelled_at":    time.Now(),
	})
}

// Helper methods

// isInternalAPIKey checks if the request is from an internal API key
func (h *CentralizedStripeHandler) isInternalAPIKey(c *gin.Context) bool {
	isInternal, exists := c.Get("is_internal_api_key")
	if !exists {
		return false
	}
	
	internal, ok := isInternal.(bool)
	return ok && internal
}

// verifyShopOwnership verifies that a user owns the specified shop
func (h *CentralizedStripeHandler) verifyShopOwnership(shopID, userID uuid.UUID) bool {
	var shopUser models.ShopUser
	err := h.serviceContainer.DB.Where("shop_id = ? AND user_id = ? AND role = ?", 
		shopID, userID, "owner").First(&shopUser).Error
	return err == nil
}