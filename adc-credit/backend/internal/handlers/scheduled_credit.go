package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// getScheduledCreditService returns a new instance of the scheduled credit service
func getScheduledCreditService() *services.ScheduledCreditService {
	return services.NewScheduledCreditService()
}

// ProcessScheduledCredits processes all scheduled credits
// This endpoint can be called manually or by a Cloud Scheduler job
func ProcessScheduledCredits(c *gin.Context) {
	// Check for API key authentication
	apiKey, exists := c.Get("apiKey")
	if !exists {
		// If no API key, check for admin user
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		// Check if user is admin
		typedUser := user.(models.User)
		if typedUser.Role != "admin" {
			c.J<PERSON>(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}
	} else {
		// If API key exists, check if it has admin permissions
		typedAPIKey := apiKey.(models.APIKey)
		hasAdminPerm := false
		for _, perm := range typedAPIKey.Permissions {
			if perm == "admin" {
				hasAdminPerm = true
				break
			}
		}

		if !hasAdminPerm {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin API key required"})
			return
		}
	}

	// Process scheduled credits
	service := getScheduledCreditService()
	processedCount, err := service.ProcessScheduledCredits()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Scheduled credits processed successfully",
		"count":   processedCount,
	})
}

// GetNextScheduledCreditDate returns the date when the next scheduled credit will be added
func GetNextScheduledCreditDate(c *gin.Context) {
	// TODO: user variable commented out until shop-based subscription logic is implemented
	// user := c.MustGet("user").(models.User)

	// TODO: Legacy subscription access commented out - need to update for shop-based subscriptions
	// Get active subscription
	// var subscription models.ShopSubscription
	// if err := database.DB.Where("user_id = ? AND status = ?", user.ID, "active").First(&subscription).Error; err != nil {
	// 	c.JSON(http.StatusOK, gin.H{
	// 		"next_scheduled_credit_date": nil,
	// 		"message":                    "No active subscription found",
	// 	})
	// 	return
	// }
	
	// For now, create a temporary subscription struct to prevent compilation errors
	var subscription struct {
		ID uuid.UUID
	}
	subscription.ID = uuid.New() // Temporary ID

	// TODO: Legacy next scheduled credit date logic commented out - need to update for shop-based subscriptions
	// Get next scheduled credit date
	// service := getScheduledCreditService()
	//
	// // Initialize the database in the service
	// service.SetDB(database.DB)
	//
	// nextDate, err := service.GetNextScheduledCreditDate(subscription.ID)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }
	//
	// if nextDate == nil {
	// 	c.JSON(http.StatusOK, gin.H{
	// 		"next_scheduled_credit_date": nil,
	// 		"message":                    "No scheduled credits found",
	// 	})
	// 	return
	// }
	//
	// c.JSON(http.StatusOK, gin.H{
	// 	"next_scheduled_credit_date": nextDate,
	// 	"next_reset_date":            nextDate, // For test compatibility
	// })
	
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"next_scheduled_credit_date": nil,
		"message":                    "Scheduled credit dates not yet implemented for shop-based subscriptions",
	})
}

// GetScheduledCreditHistory returns the history of scheduled credit additions
func GetScheduledCreditHistory(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get scheduled credit transactions
	var transactions []models.Transaction
	if err := database.DB.Where("user_id = ? AND type = ?", user.ID, "credit_scheduled").
		Order("created_at DESC").
		Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled credit history"})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// ManuallyAddScheduledCredits allows an admin to manually add scheduled credits to a specific user
func ManuallyAddScheduledCredits(c *gin.Context) {
	// Check for admin user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Check if user is admin
	typedUser := user.(models.User)
	if typedUser.Role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse request
	type ManualAddRequest struct {
		UserID string `json:"user_id" binding:"required"`
	}

	var req ManualAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: user ID parsing commented out until shop-based subscription logic is implemented
	// Parse user ID
	// userID, err := uuid.Parse(req.UserID)
	// if err != nil {
	// 	c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
	// 	return
	// }

	// TODO: Legacy subscription access commented out - need to update for shop-based subscriptions
	// Get user's active subscription
	// var subscription models.ShopSubscription
	// if err := database.DB.Where("user_id = ? AND status = ?", userID, "active").
	// 	Preload("SubscriptionTier").
	// 	First(&subscription).Error; err != nil {
	// 	c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found for user"})
	// 	return
	// }
	
	// For now, create a temporary subscription struct to prevent compilation errors
	var subscription struct {
		ID           uuid.UUID
		CreditBalance int
	}
	subscription.ID = uuid.New()
	subscription.CreditBalance = 1000 // Default value

	// TODO: Legacy scheduled credits function commented out - need to update for shop-based subscriptions
	// Add scheduled credits
	// service := getScheduledCreditService()
	// if err := service.AddScheduledCreditsToShopSubscription(subscription); err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }
	
	// For now, skip adding scheduled credits as this needs to be updated for shop-based subscriptions
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Manual scheduled credits not yet implemented for shop-based subscriptions"})
	return
}
