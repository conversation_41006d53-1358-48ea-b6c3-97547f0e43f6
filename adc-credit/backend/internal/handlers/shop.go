package handlers

import (
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShops returns all shops for the current user
func GetShops(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	var shops []models.Shop
	if user.Role == "admin" {
		// Admin can see all shops
		if err := database.DB.Find(&shops).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.Where("owner_user_id = ?", user.ID).Find(&shops).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shops"})
			return
		}
	}

	c.<PERSON>(http.StatusOK, shops)
}

// GetShop returns a specific shop by ID
func GetShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// GetShopBySlug returns a specific shop by slug
func GetShopBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopSlug := c.Param("slug")

	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "slug = ?", shopSlug).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "slug = ? AND owner_user_id = ?", shopSlug, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	c.JSON(http.StatusOK, shop)
}

// CreateShop creates a new shop
func CreateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type CreateShopRequest struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"` // "retail", "api_service", "enterprise"
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req CreateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate shop type
	validShopTypes := []string{"retail", "api_service", "enterprise"}
	if req.ShopType == "" {
		req.ShopType = "retail" // Default to retail
	}

	isValidType := false
	for _, validType := range validShopTypes {
		if req.ShopType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
		return
	}

	shopID := uuid.New()
	slug := generateShopSlug(req.Name)
	slug = ensureUniqueShopSlug(slug, shopID)

	shop := models.Shop{
		ID:           shopID,
		Slug:         slug,
		Name:         req.Name,
		Description:  req.Description,
		ShopType:     req.ShopType,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
		OwnerUserID:  user.ID,
	}

	if err := database.DB.Create(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop"})
		return
	}

	c.JSON(http.StatusCreated, shop)
}

// UpdateShop updates an existing shop
func UpdateShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	type UpdateShopRequest struct {
		Name         string `json:"name"`
		Description  string `json:"description"`
		ShopType     string `json:"shop_type"`
		ContactEmail string `json:"contact_email"`
		ContactPhone string `json:"contact_phone"`
	}

	var req UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		shop.Name = req.Name
		// Regenerate slug if name changed
		newSlug := generateShopSlug(req.Name)
		if newSlug != shop.Slug {
			shop.Slug = ensureUniqueShopSlug(newSlug, shop.ID)
		}
	}
	if req.Description != "" {
		shop.Description = req.Description
	}
	if req.ShopType != "" {
		// Validate shop type
		validShopTypes := []string{"retail", "api_service", "enterprise"}
		isValidType := false
		for _, validType := range validShopTypes {
			if req.ShopType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop type. Must be one of: retail, api_service, enterprise"})
			return
		}
		shop.ShopType = req.ShopType
	}
	if req.ContactEmail != "" {
		shop.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		shop.ContactPhone = req.ContactPhone
	}

	if err := database.DB.Save(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// DeleteShop deletes a shop
func DeleteShop(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	if err := database.DB.Delete(&shop).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete shop"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Shop deleted successfully"})
}

// GetShopStats returns statistics for a specific shop
func GetShopStats(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	// Get total customers
	var totalCustomers int64
	database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop.ID).Count(&totalCustomers)

	// Get total credit codes generated
	var totalCreditCodes int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ?", shop.ID).Count(&totalCreditCodes)

	// Get total credits issued (sum of all credit codes)
	var totalCreditsIssued int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ?", shop.ID).Select("COALESCE(SUM(amount), 0)").Scan(&totalCreditsIssued)

	// Get total credits redeemed
	var totalCreditsRedeemed int64
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ? AND is_redeemed = ?", shop.ID, true).Select("COALESCE(SUM(amount), 0)").Scan(&totalCreditsRedeemed)

	// Get active credit codes (not redeemed and not expired)
	var activeCreditCodes int64
	now := time.Now()
	database.DB.Model(&models.CreditCode{}).Where("shop_id = ? AND is_redeemed = ? AND (expires_at IS NULL OR expires_at > ?)", shop.ID, false, now).Count(&activeCreditCodes)

	// Get recent transactions (last 30 days)
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	var recentTransactions int64
	database.DB.Model(&models.ShopCreditTransaction{}).Where("shop_id = ? AND created_at >= ?", shop.ID, thirtyDaysAgo).Count(&recentTransactions)

	// Get total customer credit balance
	var totalCustomerBalance int64
	database.DB.Model(&models.ShopCustomer{}).Where("shop_id = ?", shop.ID).Select("COALESCE(SUM(credit_balance), 0)").Scan(&totalCustomerBalance)

	stats := gin.H{
		"shop_id":                shop.ID,
		"shop_name":              shop.Name,
		"shop_type":              shop.ShopType,
		"total_customers":        totalCustomers,
		"total_credit_codes":     totalCreditCodes,
		"total_credits_issued":   totalCreditsIssued,
		"total_credits_redeemed": totalCreditsRedeemed,
		"active_credit_codes":    activeCreditCodes,
		"recent_transactions":    recentTransactions,
		"total_customer_balance": totalCustomerBalance,
		"redemption_rate":        calculateRedemptionRate(totalCreditsIssued, totalCreditsRedeemed),
	}

	c.JSON(http.StatusOK, stats)
}

// Helper functions

func calculateRedemptionRate(issued, redeemed int64) float64 {
	if issued == 0 {
		return 0.0
	}
	return float64(redeemed) / float64(issued) * 100
}

func generateShopSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// If empty, use a default
	if slug == "" {
		slug = "shop"
	}

	return slug
}

func ensureUniqueShopSlug(baseSlug string, excludeID uuid.UUID) string {
	return utils.EnsureUniqueSlug(baseSlug, func(s string) bool {
		var count int64
		database.DB.Model(&models.Shop{}).Where("slug = ? AND id != ?", s, excludeID).Count(&count)
		return count > 0
	})
}

// CreateShopCustomer creates a new customer for a shop with credentials
func CreateShopCustomer(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	type CreateCustomerRequest struct {
		Name           string `json:"name" binding:"required"`
		Email          string `json:"email" binding:"required,email"`
		Phone          string `json:"phone"`
		Username       string `json:"username" binding:"required"`
		Password       string `json:"password" binding:"required,min=6"`
		InitialCredits int    `json:"initial_credits"`
		SendWelcomeEmail bool `json:"send_welcome_email"`
	}

	var req CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if username already exists
	var existingUser models.User
	if err := database.DB.Where("email = ?", req.Username+"@"+shop.Slug+".shop").First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
		return
	}

	// Start transaction
	tx := database.DB.Begin()

	// Create user account for customer
	customerUser := models.User{
		ID:       uuid.New(),
		Email:    req.Username + "@" + shop.Slug + ".shop", // Generate shop-scoped email
		Name:     req.Name,
		Password: req.Password, // TODO: Hash password
		Role:     "customer",
		ShopID:   &shop.ID,
		ExternalUserIdentifier: req.Username,
		IsExternalUser: true,
	}

	if err := tx.Create(&customerUser).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create customer user"})
		return
	}

	// Create shop customer record
	shopCustomer := models.ShopCustomer{
		ID:            uuid.New(),
		ShopID:        shop.ID,
		UserID:        customerUser.ID,
		Phone:         req.Phone,
		CreditBalance: req.InitialCredits,
	}

	if err := tx.Create(&shopCustomer).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop customer"})
		return
	}

	// Create initial credit transaction if credits provided
	if req.InitialCredits > 0 {
		transaction := models.ShopCreditTransaction{
			ID:          uuid.New(),
			ShopID:      shop.ID,
			CustomerID:  shopCustomer.ID,
			Type:        "credit_add",
			Amount:      req.InitialCredits,
			Description: "Initial credits",
		}

		if err := tx.Create(&transaction).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create initial transaction"})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create customer"})
		return
	}

	// TODO: Send welcome email if requested

	c.JSON(http.StatusCreated, gin.H{
		"id":             shopCustomer.ID,
		"user_id":        customerUser.ID,
		"name":           customerUser.Name,
		"username":       req.Username,
		"email":          req.Email,
		"phone":          req.Phone,
		"credit_balance": shopCustomer.CreditBalance,
		"shop_id":        shop.ID,
		"created_at":     shopCustomer.CreatedAt,
	})
}

// InviteShopCustomer sends an email invitation to a customer
func InviteShopCustomer(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	type InviteCustomerRequest struct {
		Name           string `json:"name" binding:"required"`
		Email          string `json:"email" binding:"required,email"`
		Phone          string `json:"phone"`
		InitialCredits int    `json:"initial_credits"`
		WelcomeMessage string `json:"welcome_message"`
	}

	var req InviteCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Generate invitation token and send email
	// For now, return success with invitation details

	c.JSON(http.StatusOK, gin.H{
		"message": "Invitation sent successfully",
		"email":   req.Email,
		"shop":    shop.Name,
		"credits": req.InitialCredits,
	})
}

// GetShopCustomers returns all customers for a shop with pagination, search, and filtering
func GetShopCustomers(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Parse query parameters
	search := c.Query("search")
	page := 1
	limit := 10
	sortBy := c.DefaultQuery("sortBy", "created_at")
	sortOrder := c.DefaultQuery("sortOrder", "desc")
	
	if p := c.Query("page"); p != "" {
		if parsedPage, err := strconv.Atoi(p); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	
	if l := c.Query("limit"); l != "" {
		if parsedLimit, err := strconv.Atoi(l); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	// Build base query
	query := database.DB.Where("shop_id = ?", shop.ID).Preload("User")

	// Add search functionality
	if search != "" {
		query = query.Joins("JOIN users ON users.id = shop_customers.user_id").
			Where("users.name ILIKE ? OR users.email ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Add credit balance filtering
	if minCredit := c.Query("minCreditBalance"); minCredit != "" {
		if min, err := strconv.ParseFloat(minCredit, 64); err == nil {
			query = query.Where("credit_balance >= ?", min)
		}
	}
	
	if maxCredit := c.Query("maxCreditBalance"); maxCredit != "" {
		if max, err := strconv.ParseFloat(maxCredit, 64); err == nil {
			query = query.Where("credit_balance <= ?", max)
		}
	}

	// Count total records before pagination
	var total int64
	if err := query.Model(&models.ShopCustomer{}).Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count customers"})
		return
	}

	// Add sorting
	validSortFields := map[string]string{
		"name":           "users.name",
		"email":          "users.email", 
		"credit_balance": "shop_customers.credit_balance",
		"created_at":     "shop_customers.created_at",
		"updated_at":     "shop_customers.updated_at",
	}
	
	if sortField, exists := validSortFields[sortBy]; exists {
		if sortOrder == "desc" {
			query = query.Order(sortField + " DESC")
		} else {
			query = query.Order(sortField + " ASC")
		}
	} else {
		// Default sort by created_at desc to show recent customers first
		query = query.Order("shop_customers.created_at DESC")
	}

	// Add pagination
	offset := (page - 1) * limit
	query = query.Offset(offset).Limit(limit)

	// Fetch customers
	var customers []models.ShopCustomer
	if err := query.Find(&customers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch customers"})
		return
	}

	// Calculate total pages
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	c.JSON(http.StatusOK, gin.H{
		"customers":   customers,
		"total":       total,
		"page":        page,
		"limit":       limit,
		"totalPages":  totalPages,
	})
}

// GetShopCustomersBySlug returns all customers for a shop by slug with pagination, search, and filtering
func GetShopCustomersBySlug(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopSlug := c.Param("slug")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can see any shop
		if err := database.DB.First(&shop, "slug = ?", shopSlug).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only see shops they own
		if err := database.DB.First(&shop, "slug = ? AND owner_user_id = ?", shopSlug, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	// Parse query parameters
	search := c.Query("search")
	page := 1
	limit := 10
	sortBy := c.DefaultQuery("sortBy", "created_at")
	sortOrder := c.DefaultQuery("sortOrder", "desc")
	
	if p := c.Query("page"); p != "" {
		if parsedPage, err := strconv.Atoi(p); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}
	
	if l := c.Query("limit"); l != "" {
		if parsedLimit, err := strconv.Atoi(l); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	// Build base query
	query := database.DB.Where("shop_id = ?", shop.ID).Preload("User")

	// Add search functionality
	if search != "" {
		query = query.Joins("JOIN users ON users.id = shop_customers.user_id").
			Where("users.name ILIKE ? OR users.email ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Add credit balance filtering
	if minCredit := c.Query("minCreditBalance"); minCredit != "" {
		if min, err := strconv.ParseFloat(minCredit, 64); err == nil {
			query = query.Where("credit_balance >= ?", min)
		}
	}
	
	if maxCredit := c.Query("maxCreditBalance"); maxCredit != "" {
		if max, err := strconv.ParseFloat(maxCredit, 64); err == nil {
			query = query.Where("credit_balance <= ?", max)
		}
	}

	// Count total records before pagination
	var total int64
	if err := query.Model(&models.ShopCustomer{}).Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count customers"})
		return
	}

	// Add sorting
	validSortFields := map[string]string{
		"name":           "users.name",
		"email":          "users.email", 
		"credit_balance": "shop_customers.credit_balance",
		"created_at":     "shop_customers.created_at",
		"updated_at":     "shop_customers.updated_at",
	}
	
	if sortField, exists := validSortFields[sortBy]; exists {
		if sortOrder == "desc" {
			query = query.Order(sortField + " DESC")
		} else {
			query = query.Order(sortField + " ASC")
		}
	} else {
		// Default sort by created_at desc to show recent customers first
		query = query.Order("shop_customers.created_at DESC")
	}

	// Add pagination
	offset := (page - 1) * limit
	query = query.Offset(offset).Limit(limit)

	// Fetch customers
	var customers []models.ShopCustomer
	if err := query.Find(&customers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch customers"})
		return
	}

	// Calculate total pages
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	c.JSON(http.StatusOK, gin.H{
		"customers":   customers,
		"total":       total,
		"page":        page,
		"limit":       limit,
		"totalPages":  totalPages,
	})
}

// GetShopCustomer returns a specific customer for a shop
func GetShopCustomer(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	customerID := c.Param("customerID")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	var customer models.ShopCustomer
	if err := database.DB.Where("id = ? AND shop_id = ?", customerID, shop.ID).
		Preload("User").
		First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Customer not found"})
		return
	}

	c.JSON(http.StatusOK, customer)
}

// AddCreditsToCustomer adds credits to a specific customer
func AddCreditsToCustomer(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	customerID := c.Param("customerID")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	var customer models.ShopCustomer
	if err := database.DB.Where("id = ? AND shop_id = ?", customerID, shop.ID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Customer not found"})
		return
	}

	type AddCreditsRequest struct {
		Amount          int    `json:"amount" binding:"required,min=1"`
		Description     string `json:"description"`
		TransactionType string `json:"transaction_type"`
	}

	var req AddCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Handle both positive (add) and negative (deduct) amounts
	isAdd := req.Amount > 0
	actualAmount := req.Amount
	if !isAdd {
		actualAmount = -req.Amount
		// Check if customer has enough credits for deduction
		if customer.CreditBalance < actualAmount {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Insufficient credit balance"})
			return
		}
	}

	// Start transaction
	tx := database.DB.Begin()

	// Update credit balance
	if isAdd {
		customer.CreditBalance += actualAmount
	} else {
		customer.CreditBalance -= actualAmount
	}

	if err := tx.Save(&customer).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record
	transactionType := req.TransactionType
	if transactionType == "" {
		if isAdd {
			transactionType = "credit_add"
		} else {
			transactionType = "credit_use"
		}
	}

	transaction := models.ShopCreditTransaction{
		ID:          uuid.New(),
		ShopID:      shop.ID,
		CustomerID:  customer.ID,
		Type:        transactionType,
		Amount:      req.Amount, // Keep original amount (positive or negative)
		Description: req.Description,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credits"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "Credits updated successfully",
		"new_balance": customer.CreditBalance,
		"transaction": transaction,
	})
}

// GetCustomerCredits returns credit balance for a specific customer
func GetCustomerCredits(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	customerID := c.Param("customerID")

	// Verify shop ownership
	var shop models.Shop
	if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	var customer models.ShopCustomer
	if err := database.DB.Where("id = ? AND shop_id = ?", customerID, shop.ID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Customer not found"})
		return
	}

	// Get transaction history
	var transactions []models.ShopCreditTransaction
	database.DB.Where("shop_id = ? AND customer_id = ?", shop.ID, customer.ID).
		Order("created_at DESC").
		Limit(50).
		Find(&transactions)

	// Calculate totals
	var totalEarned, totalSpent int
	for _, transaction := range transactions {
		if transaction.Amount > 0 {
			totalEarned += transaction.Amount
		} else {
			totalSpent += -transaction.Amount
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"customer_id":     customer.ID,
		"credit_balance":  customer.CreditBalance,
		"total_earned":    totalEarned,
		"total_spent":     totalSpent,
		"recent_transactions": transactions,
	})
}
