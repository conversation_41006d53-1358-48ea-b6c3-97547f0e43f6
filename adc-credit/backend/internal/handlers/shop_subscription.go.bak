package handlers

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/checkout/session"
	"gorm.io/gorm"
)

// GetShopSubscription returns the subscription for a specific shop
func GetShopSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has access to this shop
	if !hasShopAccess(user.ID, shopUUID) {
		c.J<PERSON>(http.StatusForbidden, gin.H{"error": "Access denied to this shop"})
		return
	}

	var shopSubscription models.ShopSubscription
	if err := database.DB.Where("shop_id = ? AND status = ?", shopUUID, "active").
		Preload("SubscriptionTier").
		Preload("Shop").
		First(&shopSubscription).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found for this shop"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch shop subscription"})
		}
		return
	}

	c.JSON(http.StatusOK, shopSubscription)
}

// CreateShopSubscription creates a new subscription for a shop
func CreateShopSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has owner/admin access to this shop
	if !hasShopOwnerAccess(user.ID, shopUUID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only shop owners can manage subscriptions"})
		return
	}

	type CreateShopSubscriptionRequest struct {
		SubscriptionTierID uint   `json:"subscription_tier_id" binding:"required"`
		AutoRenew          bool   `json:"auto_renew"`
		BillingEmail       string `json:"billing_email"`
	}

	var req CreateShopSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if subscription tier exists
	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, req.SubscriptionTierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
		return
	}

	// Check if shop exists and user has access
	var shop models.Shop
	if err := database.DB.First(&shop, shopUUID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Check for existing active subscription
	var existingSubscription models.ShopSubscription
	if err := database.DB.Where("shop_id = ? AND status = ?", shopUUID, "active").
		First(&existingSubscription).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop already has an active subscription"})
		return
	}

	// Start transaction
	tx := database.DB.Begin()

	// Create shop subscription
	shopSubscription := models.ShopSubscription{
		ID:                 uuid.New(),
		ShopID:             shopUUID,
		SubscriptionTierID: tier.ID,
		StartDate:          time.Now(),
		AutoRenew:          req.AutoRenew,
		Status:             "active",
		CreditBalance:      tier.CreditLimit,
	}

	if err := tx.Create(&shopSubscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop subscription"})
		return
	}

	// Create initial transaction record
	shopTransaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopUUID,
		ShopSubscriptionID: shopSubscription.ID,
		InitiatedByUserID:  user.ID,
		Type:               "credit_add",
		Amount:             tier.CreditLimit,
		Description:        "Initial credits for " + tier.Name + " subscription",
		Reference:          "initial_subscription",
	}

	if err := tx.Create(&shopTransaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&shopSubscription).Association("SubscriptionTier").Find(&shopSubscription.SubscriptionTier)

	c.JSON(http.StatusCreated, shopSubscription)
}

// UpdateShopSubscription updates an existing shop subscription
func UpdateShopSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has owner/admin access to this shop
	if !hasShopOwnerAccess(user.ID, shopUUID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only shop owners can manage subscriptions"})
		return
	}

	type UpdateShopSubscriptionRequest struct {
		AutoRenew    bool   `json:"auto_renew"`
		BillingEmail string `json:"billing_email"`
	}

	var req UpdateShopSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var shopSubscription models.ShopSubscription
	if err := database.DB.Where("shop_id = ? AND status = ?", shopUUID, "active").
		First(&shopSubscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Active shop subscription not found"})
		return
	}

	shopSubscription.AutoRenew = req.AutoRenew
	shopSubscription.BillingEmail = req.BillingEmail

	if err := database.DB.Save(&shopSubscription).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop subscription"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&shopSubscription).Association("SubscriptionTier").Find(&shopSubscription.SubscriptionTier)

	c.JSON(http.StatusOK, shopSubscription)
}

// CancelShopSubscription cancels an active shop subscription
func CancelShopSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has owner access to this shop
	if !hasShopOwnerAccess(user.ID, shopUUID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only shop owners can cancel subscriptions"})
		return
	}

	var shopSubscription models.ShopSubscription
	if err := database.DB.Where("shop_id = ? AND status = ?", shopUUID, "active").
		First(&shopSubscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Active shop subscription not found"})
		return
	}

	// Start transaction
	tx := database.DB.Begin()

	now := time.Now()
	shopSubscription.EndDate = &now
	shopSubscription.Status = "cancelled"
	shopSubscription.AutoRenew = false

	if err := tx.Save(&shopSubscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel shop subscription"})
		return
	}

	// Create cancellation transaction record
	shopTransaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopUUID,
		ShopSubscriptionID: shopSubscription.ID,
		InitiatedByUserID:  user.ID,
		Type:               "subscription_cancelled",
		Amount:             0,
		Description:        "Subscription cancelled",
		Reference:          "cancellation",
	}

	if err := tx.Create(&shopTransaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Shop subscription cancelled successfully"})
}

// UpgradeShopSubscription upgrades a shop subscription to a new tier
func UpgradeShopSubscription(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	shopUUID, err := uuid.Parse(shopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Check if user has owner access to this shop
	if !hasShopOwnerAccess(user.ID, shopUUID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only shop owners can upgrade subscriptions"})
		return
	}

	type UpgradeShopSubscriptionRequest struct {
		NewTierID int  `json:"new_tier_id" binding:"required"`
		Prorate   bool `json:"prorate" default:"true"`
	}

	var req UpgradeShopSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current shop subscription
	var shopSubscription models.ShopSubscription
	if err := database.DB.Where("shop_id = ? AND status = ?", shopUUID, "active").
		Preload("SubscriptionTier").
		First(&shopSubscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Active shop subscription not found"})
		return
	}

	// Get new subscription tier
	var newTier models.SubscriptionTier
	if err := database.DB.First(&newTier, req.NewTierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "New subscription tier not found"})
		return
	}

	// If subscription has Stripe ID, create checkout session for upgrade
	if shopSubscription.StripeSubscriptionID != "" {
		handleStripeUpgrade(c, shopSubscription, newTier, user)
		return
	}

	// Handle direct upgrade for free plans
	handleDirectUpgrade(c, shopSubscription, newTier, user, req.Prorate)
}

func handleStripeUpgrade(c *gin.Context, shopSub models.ShopSubscription, newTier models.SubscriptionTier, user models.User) {
	// Get Stripe price ID for new tier
	priceID, ok := GetStripePriceID(int(newTier.ID), "shop")
	if !ok || priceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Stripe price ID not configured for this subscription tier",
		})
		return
	}

	// Get frontend URL
	origin := c.GetHeader("Origin")
	if origin == "" {
		origin = os.Getenv("FRONTEND_URL")
		if origin == "" {
			origin = "https://credit.adcshop.store"
		}
	}

	successURL := fmt.Sprintf("%s/dashboard/shops/%s/subscription?success=true&upgrade=true&session_id={CHECKOUT_SESSION_ID}",
		origin, shopSub.ShopID)
	cancelURL := fmt.Sprintf("%s/dashboard/shops/%s/subscription?canceled=true", origin, shopSub.ShopID)

	// Create checkout session
	params := &stripe.CheckoutSessionParams{
		PaymentMethodTypes: stripe.StringSlice([]string{"card"}),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Price:    stripe.String(priceID),
				Quantity: stripe.Int64(1),
			},
		},
		Mode:          stripe.String(string(stripe.CheckoutSessionModeSubscription)),
		SuccessURL:    stripe.String(successURL),
		CancelURL:     stripe.String(cancelURL),
		CustomerEmail: stripe.String(shopSub.BillingEmail),
	}

	// Add metadata
	params.AddMetadata("shop_id", shopSub.ShopID.String())
	params.AddMetadata("subscription_id", shopSub.ID.String())
	params.AddMetadata("old_tier_id", strconv.Itoa(int(shopSub.SubscriptionTierID)))
	params.AddMetadata("new_tier_id", strconv.Itoa(int(newTier.ID)))
	params.AddMetadata("upgrade_type", "shop_tier_change")
	params.AddMetadata("initiated_by_user_id", user.ID.String())

	s, err := session.New(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"checkout_url": s.URL,
		"message":      "Please complete the checkout process to upgrade your shop subscription",
	})
}

func handleDirectUpgrade(c *gin.Context, shopSub models.ShopSubscription, newTier models.SubscriptionTier, user models.User, prorate bool) {
	// Start transaction
	tx := database.DB.Begin()

	// Calculate new credit balance
	var newCreditBalance int
	if prorate {
		// Calculate prorated credits
		var timeRemaining float64 = 1.0
		if shopSub.EndDate != nil {
			totalPeriod := shopSub.EndDate.Sub(shopSub.StartDate)
			elapsed := time.Since(shopSub.StartDate)
			timeRemaining = 1.0 - (elapsed.Seconds() / totalPeriod.Seconds())
			if timeRemaining < 0 {
				timeRemaining = 0
			}
		}

		usedCredits := shopSub.SubscriptionTier.CreditLimit - shopSub.CreditBalance
		usedPercentage := float64(usedCredits) / float64(shopSub.SubscriptionTier.CreditLimit)

		if newTier.CreditLimit > shopSub.SubscriptionTier.CreditLimit {
			additionalCredits := newTier.CreditLimit - shopSub.SubscriptionTier.CreditLimit
			newCreditBalance = shopSub.CreditBalance + int(float64(additionalCredits)*timeRemaining)
		} else {
			newCreditBalance = int(float64(newTier.CreditLimit) * (1.0 - usedPercentage))
		}
	} else {
		newCreditBalance = newTier.CreditLimit
	}

	// Update current subscription
	now := time.Now()
	shopSub.EndDate = &now
	shopSub.Status = "cancelled"

	if err := tx.Save(&shopSub).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update existing subscription"})
		return
	}

	// Create new subscription
	newShopSubscription := models.ShopSubscription{
		ID:                 uuid.New(),
		ShopID:             shopSub.ShopID,
		SubscriptionTierID: newTier.ID,
		StartDate:          now,
		AutoRenew:          shopSub.AutoRenew,
		Status:             "active",
		CreditBalance:      newCreditBalance,
		BillingEmail:       shopSub.BillingEmail,
	}

	if err := tx.Create(&newShopSubscription).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create new subscription"})
		return
	}

	// Create transaction record
	var description string
	var amount int

	if newTier.CreditLimit > shopSub.SubscriptionTier.CreditLimit {
		description = fmt.Sprintf("Upgrade from %s to %s tier", shopSub.SubscriptionTier.Name, newTier.Name)
		amount = newCreditBalance - shopSub.CreditBalance
	} else {
		description = fmt.Sprintf("Downgrade from %s to %s tier", shopSub.SubscriptionTier.Name, newTier.Name)
		amount = newCreditBalance - shopSub.CreditBalance
	}

	shopTransaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopSub.ShopID,
		ShopSubscriptionID: newShopSubscription.ID,
		InitiatedByUserID:  user.ID,
		Type:               "credit_add",
		Amount:             amount,
		Description:        description,
		Reference:          fmt.Sprintf("tier_change_%s_to_%s", shopSub.SubscriptionTier.Name, newTier.Name),
	}

	if err := tx.Create(&shopTransaction).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Load subscription tier for response
	database.DB.Model(&newShopSubscription).Association("SubscriptionTier").Find(&newShopSubscription.SubscriptionTier)

	c.JSON(http.StatusOK, gin.H{
		"message":      fmt.Sprintf("Successfully changed subscription from %s to %s tier", shopSub.SubscriptionTier.Name, newTier.Name),
		"subscription": newShopSubscription,
	})
}

// Helper functions for access control
func hasShopAccess(userID, shopID uuid.UUID) bool {
	var count int64
	database.DB.Model(&models.ShopUser{}).
		Where("user_id = ? AND shop_id = ?", userID, shopID).
		Count(&count)
	return count > 0
}

func hasShopOwnerAccess(userID, shopID uuid.UUID) bool {
	var count int64
	database.DB.Model(&models.ShopUser{}).
		Where("user_id = ? AND shop_id = ? AND role IN (?)", userID, shopID, []string{"owner", "admin"}).
		Count(&count)
	return count > 0
}