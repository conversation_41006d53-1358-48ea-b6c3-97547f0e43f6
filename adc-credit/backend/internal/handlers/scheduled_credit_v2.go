package handlers

import (
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ScheduledCreditV2Handler handles scheduled credit operations using the service container approach
type ScheduledCreditV2Handler struct {
	serviceContainer *services.ServiceContainer
}

// NewScheduledCreditV2Hand<PERSON> creates a new scheduled credit handler with service container
func NewScheduledCreditV2Handler(serviceContainer *services.ServiceContainer) *ScheduledCreditV2Handler {
	return &ScheduledCreditV2Handler{
		serviceContainer: serviceContainer,
	}
}

// ProcessScheduledCredits processes all scheduled credits for all shops
func (h *ScheduledCreditV2Handler) ProcessScheduledCredits(c *gin.Context) {
	// Check for API key authentication
	apiKey, exists := c.Get("apiKey")
	if !exists {
		// If no API key, check for admin user
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			return
		}

		// Check if user is admin
		typedUser := user.(models.User)
		if typedUser.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}
	} else {
		// If API key exists, check if it has admin permissions
		typedAPIKey := apiKey.(models.APIKey)
		hasAdminPerm := false
		for _, perm := range typedAPIKey.Permissions {
			if perm == "admin" || perm == "system" {
				hasAdminPerm = true
				break
			}
		}

		if !hasAdminPerm {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin API key required"})
			return
		}
	}

	logrus.Info("Processing scheduled credits for all shops")

	// Get all active subscriptions that need scheduled credits
	var subscriptions []models.ShopSubscription
	if err := h.serviceContainer.DB.Where("status = ? AND auto_renew = ?", "active", true).
		Preload("SubscriptionTier").
		Find(&subscriptions).Error; err != nil {
		logrus.Errorf("Failed to fetch active subscriptions: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscriptions"})
		return
	}

	processedCount := 0
	now := time.Now()
	
	// Process each subscription for scheduled credits
	for _, subscription := range subscriptions {
		// Check if this subscription is due for scheduled credits
		// For this example, we'll add credits monthly on the subscription start date
		if h.shouldProcessScheduledCredits(subscription, now) {
			if err := h.processScheduledCreditsForSubscription(subscription); err != nil {
				logrus.Errorf("Failed to process scheduled credits for subscription %s: %v", subscription.ID, err)
				continue
			}
			processedCount++
		}
	}

	logrus.Infof("Processed scheduled credits for %d subscriptions", processedCount)

	c.JSON(http.StatusOK, gin.H{
		"message": "Scheduled credits processed successfully",
		"count":   processedCount,
	})
}

// GetNextScheduledCreditDate returns the next scheduled credit date for a shop
func (h *ScheduledCreditV2Handler) GetNextScheduledCreditDate(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get shop ID from query parameter or use user's primary shop
	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID

	if shopIDStr != "" {
		var err error
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get the user's primary shop
		var shop models.Shop
		if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
			logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shop.ID
	}

	// Verify user has access to this shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ? AND owner_id = ?", shopID, user.ID).First(&shop).Error; err != nil {
		logrus.Errorf("User %s does not have access to shop %s: %v", user.ID, shopID, err)
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to shop"})
		return
	}

	// Get active subscription for the shop
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shopID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"shop_id":                    shopID,
			"next_scheduled_credit_date": nil,
			"message":                    "No active subscription found",
		})
		return
	}

	// Calculate next scheduled credit date
	nextDate := h.calculateNextScheduledCreditDate(subscription)

	c.JSON(http.StatusOK, gin.H{
		"shop_id":                    shopID,
		"next_scheduled_credit_date": nextDate,
		"next_reset_date":            nextDate, // For test compatibility
	})
}

// GetScheduledCreditHistory returns the history of scheduled credit additions for a shop
func (h *ScheduledCreditV2Handler) GetScheduledCreditHistory(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get shop ID from query parameter or use user's primary shop
	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID

	if shopIDStr != "" {
		var err error
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get the user's primary shop
		var shop models.Shop
		if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
			logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shop.ID
	}

	// Verify user has access to this shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ? AND owner_id = ?", shopID, user.ID).First(&shop).Error; err != nil {
		logrus.Errorf("User %s does not have access to shop %s: %v", user.ID, shopID, err)
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to shop"})
		return
	}

	// Get scheduled credit transactions for the shop
	var transactions []models.ShopTransaction
	if err := h.serviceContainer.DB.Where("shop_id = ? AND type = ?", shopID, "credit_scheduled").
		Order("created_at DESC").
		Find(&transactions).Error; err != nil {
		logrus.Errorf("Failed to fetch scheduled credit history for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled credit history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":      shopID,
		"transactions": transactions,
	})
}

// ManuallyAddScheduledCredits allows an admin to manually add scheduled credits to a specific shop
func (h *ScheduledCreditV2Handler) ManuallyAddScheduledCredits(c *gin.Context) {
	// Check for admin user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Check if user is admin
	typedUser := user.(models.User)
	if typedUser.Role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse request
	type ManualAddRequest struct {
		ShopID string `json:"shop_id" binding:"required"`
	}

	var req ManualAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse shop ID
	shopID, err := uuid.Parse(req.ShopID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Get shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ?", shopID).First(&shop).Error; err != nil {
		logrus.Errorf("Shop not found: %s", shopID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Get shop's active subscription
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shopID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		logrus.Errorf("No active subscription found for shop %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found for shop"})
		return
	}

	// Add scheduled credits
	if err := h.processScheduledCreditsForSubscription(subscription); err != nil {
		logrus.Errorf("Failed to add scheduled credits to shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	logrus.Infof("Manually added scheduled credits to shop %s", shopID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Scheduled credits added successfully",
		"shop_id": shopID,
	})
}

// Helper methods

// shouldProcessScheduledCredits determines if a subscription should receive scheduled credits
func (h *ScheduledCreditV2Handler) shouldProcessScheduledCredits(subscription models.ShopSubscription, now time.Time) bool {
	// For this example, we'll add credits monthly on the anniversary of the start date
	
	// Calculate the next scheduled date based on start date
	nextDate := h.calculateNextScheduledCreditDate(subscription)
	if nextDate == nil {
		return false
	}

	// Check if we're past the next scheduled date
	return now.After(*nextDate) || now.Equal(*nextDate)
}

// calculateNextScheduledCreditDate calculates when the next scheduled credits should be added
func (h *ScheduledCreditV2Handler) calculateNextScheduledCreditDate(subscription models.ShopSubscription) *time.Time {
	// For this example, we'll add credits monthly on the anniversary of the start date
	now := time.Now()
	startDate := subscription.StartDate
	
	// Calculate the next monthly anniversary
	_, _, day := startDate.Date()
	location := startDate.Location()
	
	// Start with the current month
	nextYear, nextMonth := now.Year(), now.Month()
	
	// If we've passed this month's anniversary, move to next month
	nextDate := time.Date(nextYear, nextMonth, day, startDate.Hour(), startDate.Minute(), startDate.Second(), 0, location)
	if nextDate.Before(now) || nextDate.Equal(now) {
		nextDate = nextDate.AddDate(0, 1, 0)
	}
	
	// Ensure we don't go beyond the subscription end date if it exists
	if subscription.EndDate != nil && nextDate.After(*subscription.EndDate) {
		return nil
	}
	
	return &nextDate
}

// processScheduledCreditsForSubscription adds scheduled credits to a specific subscription
func (h *ScheduledCreditV2Handler) processScheduledCreditsForSubscription(subscription models.ShopSubscription) error {
	// Calculate scheduled credit amount (could be based on tier, usage, etc.)
	// For this example, we'll add 10% of the credit limit monthly
	scheduledAmount := subscription.SubscriptionTier.CreditLimit / 10
	if scheduledAmount < 100 {
		scheduledAmount = 100 // Minimum scheduled credits
	}

	// Start a transaction
	tx := h.serviceContainer.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update credit balance
	subscription.CreditBalance += scheduledAmount
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create transaction record
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             subscription.ShopID,
		ShopSubscriptionID: subscription.ID,
		Type:               "credit_scheduled",
		Amount:             scheduledAmount,
		Description:        "Monthly scheduled credit addition",
		Reference:          "scheduled_" + time.Now().Format("2006-01-02"),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	logrus.Infof("Added %d scheduled credits to shop %s, new balance: %d", 
		scheduledAmount, subscription.ShopID, subscription.CreditBalance)

	// Report usage if service is available
	if h.serviceContainer.UsageReporting != nil {
		go func() {
			// TODO: Implement usage reporting with proper event types
			// For now, just log the scheduled credit addition
			logrus.Infof("Scheduled credit usage reported: shop=%s, amount=%d, tier=%s", 
				subscription.ShopID, scheduledAmount, subscription.SubscriptionTier.Name)
		}()
	}

	return nil
}