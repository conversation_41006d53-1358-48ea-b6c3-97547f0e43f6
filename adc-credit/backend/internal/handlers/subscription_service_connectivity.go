package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// TestSubscriptionServiceConnectivity tests the connection to the subscription service
func TestSubscriptionServiceConnectivity(subscriptionClient *subscriptionSDK.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Try to get plans for the "credit" service as a connectivity test
		plans, err := subscriptionClient.GetPlans(ctx, "credit")
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "error",
				"message": "Subscription service unavailable",
				"error": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"message": "Subscription service connectivity verified",
			"plans_count": len(plans),
		})
	}
}