package handlers

import (
	"net/http"
	"strconv"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SubscriptionLimitsHandler handles subscription limit-related requests
type SubscriptionLimitsHandler struct {
	serviceContainer *services.ServiceContainer
}

// NewSubscriptionLimitsHandler creates a new subscription limits handler
func NewSubscriptionLimitsHandler(serviceContainer *services.ServiceContainer) *SubscriptionLimitsHandler {
	return &SubscriptionLimitsHandler{
		serviceContainer: serviceContainer,
	}
}

// GetUserLimits returns all subscription limits for the authenticated user based on their primary shop
func (h *SubscriptionLimitsHandler) GetUserLimits(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	userUUID := userObj.ID
	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()

	// Find user's primary shop subscription
	var shopUser models.ShopUser
	err := h.serviceContainer.DB.Where("user_id = ? AND role = ?", userUUID, "owner").
		Preload("Shop").
		Preload("Shop.Subscription").
		Preload("Shop.Subscription.SubscriptionTier").
		First(&shopUser).Error

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "No shop subscription found",
			"message": "User must be owner of a shop with an active subscription",
		})
		return
	}

	if shopUser.Shop.Subscription == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "No active subscription",
			"message": "Shop has no active subscription",
		})
		return
	}

	subscription := shopUser.Shop.Subscription.SubscriptionTier
	shopID := shopUser.Shop.ID

	// Get all limit checks
	shopLimit, _ := shopGuard.CheckShopLimit(userUUID)
	customerLimit, _ := shopGuard.CheckCustomerLimit(shopID)
	apiKeyLimit, _ := shopGuard.CheckAPIKeyLimit(shopID)
	branchLimit, _ := shopGuard.CheckBranchLimit(shopID)
	qrCodeLimit, _ := shopGuard.CheckQRCodeLimit(shopID)
	webhookLimit, _ := shopGuard.CheckWebhookLimit(userUUID)
	creditBalance, _ := shopGuard.CheckCreditBalance(shopID, 0)
	analyticsHistoryDays, _ := shopGuard.GetAnalyticsHistoryLimit(shopID)
	supportLevel, _ := shopGuard.GetSupportLevel(shopID)

	// Create response that matches expected format
	response := gin.H{
		"subscription_tier": subscription,
		"shop_id": shopID,
		"shops": gin.H{
			"max": subscription.MaxShops,
			"current": shopLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedShops,
			"allowed": shopLimit.Allowed,
		},
		"customers": gin.H{
			"max": subscription.MaxCustomersPerShop,
			"current": customerLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedCustomers,
			"allowed": customerLimit.Allowed,
		},
		"api_keys": gin.H{
			"max": subscription.MaxAPIKeysPerShop,
			"current": apiKeyLimit.CurrentUsage,
			"unlimited": false,
			"allowed": apiKeyLimit.Allowed,
		},
		"branches": gin.H{
			"max": subscription.MaxBranchesPerShop,
			"current": branchLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedBranches,
			"allowed": branchLimit.Allowed,
		},
		"qr_codes": gin.H{
			"max": subscription.MaxQRCodesPerMonth,
			"current": qrCodeLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedQRCodes,
			"allowed": qrCodeLimit.Allowed,
		},
		"webhooks": gin.H{
			"max": subscription.MaxWebhooks,
			"current": webhookLimit.CurrentUsage,
			"unlimited": false,
			"allowed": webhookLimit.Allowed,
		},
		"credits": gin.H{
			"monthly_allocation": subscription.CreditLimit,
			"current_balance": creditBalance.CurrentUsage,
			"limit": subscription.CreditLimit,
			"unlimited": false,
			"allowed": creditBalance.Allowed,
		},
		"analytics": gin.H{
			"history_days": analyticsHistoryDays,
			"advanced": subscription.AdvancedAnalytics,
		},
		"support": gin.H{
			"level": supportLevel,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CheckShopLimit checks if user can create more shops
func (h *SubscriptionLimitsHandler) CheckShopLimit(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	userUUID := userObj.ID
	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()

	result, err := shopGuard.CheckShopLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check shop limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"user_id": userUUID,
		"limit_type": "shops",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckCustomerLimit checks if shop can add more customers
func (h *SubscriptionLimitsHandler) CheckCustomerLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckCustomerLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check customer limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"limit_type": "customers",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckAPIKeyLimit checks if shop can create more API keys
func (h *SubscriptionLimitsHandler) CheckAPIKeyLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckAPIKeyLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check API key limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"limit_type": "api_keys",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckBranchLimit checks if shop can create more branches
func (h *SubscriptionLimitsHandler) CheckBranchLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckBranchLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check branch limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"limit_type": "branches",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckQRCodeLimit checks if user can generate more QR codes
func (h *SubscriptionLimitsHandler) CheckQRCodeLimit(c *gin.Context) {
	// For QR codes, we need shop context - try to get it from query param or user's primary shop
	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID
	var err error

	if shopIDStr != "" {
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get user's primary shop
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		userObj, ok := user.(models.User)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
			return
		}

		// Find user's primary shop
		var shopUser models.ShopUser
		err = h.serviceContainer.DB.Where("user_id = ? AND role = ?", userObj.ID, "owner").
			First(&shopUser).Error
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shopUser.ShopID
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckQRCodeLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check QR code limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"limit_type": "qr_codes",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckWebhookLimit checks if user can create more webhooks
func (h *SubscriptionLimitsHandler) CheckWebhookLimit(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}

	userUUID := userObj.ID
	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()

	result, err := shopGuard.CheckWebhookLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check webhook limit",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"user_id": userUUID,
		"limit_type": "webhooks",
		"allowed": result.Allowed,
		"current_usage": result.CurrentUsage,
		"limit": result.Limit,
		"unlimited": result.Unlimited,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckShopTypeAllowed checks if shop type is allowed for subscription
func (h *SubscriptionLimitsHandler) CheckShopTypeAllowed(c *gin.Context) {
	shopType := c.Query("shop_type")
	if shopType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shop_type query parameter is required"})
		return
	}

	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID
	var err error

	if shopIDStr != "" {
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get user's primary shop
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		userObj, ok := user.(models.User)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
			return
		}

		// Find user's primary shop
		var shopUser models.ShopUser
		err = h.serviceContainer.DB.Where("user_id = ? AND role = ?", userObj.ID, "owner").
			First(&shopUser).Error
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shopUser.ShopID
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckShopTypeAllowed(shopID, shopType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check shop type",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"shop_type": shopType,
		"allowed": result.Allowed,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

// CheckCreditBalance checks if shop has sufficient credits
func (h *SubscriptionLimitsHandler) CheckCreditBalance(c *gin.Context) {
	requiredCreditsStr := c.Query("required_credits")
	if requiredCreditsStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "required_credits query parameter is required"})
		return
	}

	requiredCredits, err := strconv.Atoi(requiredCreditsStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid required_credits value"})
		return
	}

	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID

	if shopIDStr != "" {
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get user's primary shop
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		userObj, ok := user.(models.User)
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
			return
		}

		// Find user's primary shop
		var shopUser models.ShopUser
		err = h.serviceContainer.DB.Where("user_id = ? AND role = ?", userObj.ID, "owner").
			First(&shopUser).Error
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shopUser.ShopID
	}

	shopGuard := h.serviceContainer.GetShopSubscriptionGuard()
	result, err := shopGuard.CheckCreditBalance(shopID, requiredCredits)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to check credit balance",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"shop_id": shopID,
		"required_credits": requiredCredits,
		"allowed": result.Allowed,
		"current_balance": result.CurrentUsage,
		"message": result.Message,
	}

	c.JSON(http.StatusOK, response)
}

/* LEGACY CODE COMMENTED OUT
func (h *SubscriptionLimitsHandler) GetUserLimitsOLD(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	// Get user's active subscription
	subscription, err := h.guard.GetUserActiveSubscription(userUUID, "personal")
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Get all limit checks
	shopLimit, _ := h.guard.CheckShopLimit(userUUID)
	qrCodeLimit, _ := h.guard.CheckQRCodeLimit(userUUID)
	webhookLimit, _ := h.guard.CheckWebhookLimit(userUUID)
	creditBalance, _ := h.guard.CheckCreditBalance(userUUID, 0)
	analyticsHistoryDays, _ := h.guard.GetAnalyticsHistoryLimit(userUUID)
	supportLevel, _ := h.guard.GetSupportLevel(userUUID)

	// Create response that matches test expectations
	response := gin.H{
		"subscription_tier": subscription,
		"shops": gin.H{
			"max":       subscription.MaxShops,
			"current":   shopLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedShops,
			"allowed":   shopLimit.Allowed,
		},
		"customers": gin.H{
			"max":       subscription.MaxCustomersPerShop,
			"unlimited": subscription.UnlimitedCustomers,
		},
		"api_keys": gin.H{
			"max": subscription.MaxAPIKeysPerShop,
		},
		"branches": gin.H{
			"max":       subscription.MaxBranchesPerShop,
			"unlimited": subscription.UnlimitedBranches,
		},
		"qr_codes": gin.H{
			"max":       subscription.MaxQRCodesPerMonth,
			"current":   qrCodeLimit.CurrentUsage,
			"unlimited": subscription.UnlimitedQRCodes,
			"allowed":   qrCodeLimit.Allowed,
		},
		"webhooks": gin.H{
			"max":     subscription.MaxWebhooks,
			"current": webhookLimit.CurrentUsage,
			"allowed": webhookLimit.Allowed,
		},
		"credits": gin.H{
			"limit":   subscription.CreditLimit,
			"current": creditBalance.CurrentUsage,
		},
		"analytics_history_days": analyticsHistoryDays,
		"support_level":          supportLevel,
		"allowed_shop_types":     subscription.AllowedShopTypes,
		// Keep the nested limits structure for backwards compatibility
		"limits": gin.H{
			"shops": gin.H{
				"max":       subscription.MaxShops,
				"current":   shopLimit.CurrentUsage,
				"unlimited": subscription.UnlimitedShops,
				"allowed":   shopLimit.Allowed,
			},
			"customers_per_shop": gin.H{
				"max":       subscription.MaxCustomersPerShop,
				"unlimited": subscription.UnlimitedCustomers,
			},
			"api_keys_per_shop": gin.H{
				"max": subscription.MaxAPIKeysPerShop,
			},
			"branches_per_shop": gin.H{
				"max":       subscription.MaxBranchesPerShop,
				"unlimited": subscription.UnlimitedBranches,
			},
			"qr_codes_per_month": gin.H{
				"max":       subscription.MaxQRCodesPerMonth,
				"current":   qrCodeLimit.CurrentUsage,
				"unlimited": subscription.UnlimitedQRCodes,
				"allowed":   qrCodeLimit.Allowed,
			},
			"webhooks": gin.H{
				"max":     subscription.MaxWebhooks,
				"current": webhookLimit.CurrentUsage,
				"allowed": webhookLimit.Allowed,
			},
			"credits": gin.H{
				"limit":   subscription.CreditLimit,
				"current": creditBalance.CurrentUsage,
			},
			"analytics_history_days": analyticsHistoryDays,
			"support_level":          supportLevel,
			"allowed_shop_types":     subscription.AllowedShopTypes,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CheckShopLimit checks if user can create more shops
func (h *SubscriptionLimitsHandler) CheckShopLimit(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	result, err := h.guard.CheckShopLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckCustomerLimit checks if shop can add more customers
func (h *SubscriptionLimitsHandler) CheckCustomerLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckCustomerLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check customer limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckAPIKeyLimit checks if shop can create more API keys
func (h *SubscriptionLimitsHandler) CheckAPIKeyLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckAPIKeyLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check API key limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckBranchLimit checks if shop can create more branches
func (h *SubscriptionLimitsHandler) CheckBranchLimit(c *gin.Context) {
	shopIDStr := c.Param("shopId")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	result, err := h.guard.CheckBranchLimit(shopID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check branch limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckQRCodeLimit checks if user can generate more QR codes
func (h *SubscriptionLimitsHandler) CheckQRCodeLimit(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	result, err := h.guard.CheckQRCodeLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check QR code limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckWebhookLimit checks if user can create more webhooks
func (h *SubscriptionLimitsHandler) CheckWebhookLimit(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	result, err := h.guard.CheckWebhookLimit(userUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check webhook limit"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckShopTypeAllowed checks if user can create shop of specific type
func (h *SubscriptionLimitsHandler) CheckShopTypeAllowed(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	shopType := c.Query("shop_type")
	if shopType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop type is required"})
		return
	}

	result, err := h.guard.CheckShopTypeAllowed(userUUID, shopType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check shop type permission"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckCreditBalance checks if user has enough credits
func (h *SubscriptionLimitsHandler) CheckCreditBalance(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userObj, ok := user.(models.User)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user data"})
		return
	}
	
	userUUID := userObj.ID

	// Get required credits from query parameter (default to 0 for balance check)
	requiredCredits := 0
	if creditsStr := c.Query("required_credits"); creditsStr != "" {
		if parsed, err := uuid.Parse(creditsStr); err == nil {
			requiredCredits = int(parsed.ID())
		}
	}

	// Get user's active subscription for detailed credit info
	subscription, err := h.guard.GetUserActiveSubscription(userUUID, "personal")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get subscription"})
		return
	}

	result, err := h.guard.CheckCreditBalance(userUUID, requiredCredits)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check credit balance"})
		return
	}

	// TODO: Fix this - subscription is SubscriptionTier, not ShopSubscription
	// Need to get actual subscription to get CreditBalance
	response := gin.H{
		"allowed":            result.Allowed,
		"current_usage":      result.CurrentUsage,
		"current_balance":    0, // TODO: Get actual current balance from shop subscription
		"monthly_allocation": subscription.CreditLimit,
		"limit":              result.Limit,
		"unlimited":          result.Unlimited,
		"message":            result.Message,
	}

	c.JSON(http.StatusOK, response)
}
*/
