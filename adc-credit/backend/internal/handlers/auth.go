package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/verawat1234/adc-sso-service/sdk"
	"golang.org/x/crypto/bcrypt"
)

type GoogleAuthRequest struct {
	Token string `json:"token" binding:"required"`
}

type LoginResponse struct {
	Token        string      `json:"token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
}

// GoogleAuth handles Google OAuth authentication
func GoogleAuth(c *gin.Context) {
	var req GoogleAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}


	// For Google OAuth tokens, we need to get user info from Google
	// In a production environment, you should verify the token with Google
	// For this implementation, we'll use the token to get user info from Google's userinfo endpoint

	// Get user info from Google
	googleUserInfo, err := getGoogleUserInfo(req.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": fmt.Sprintf("Failed to verify Google token: %v", err)})
		return
	}

	// Extract user information
	googleID := googleUserInfo.Sub
	email := googleUserInfo.Email
	name := googleUserInfo.Name
	picture := googleUserInfo.Picture

	// If we couldn't get the Google ID, generate a fallback
	if googleID == "" {
		googleID = "google_" + uuid.New().String()
	}

	// Check if user exists by Google ID
	var user models.User
	result := database.DB.Where("google_id = ?", googleID).First(&user)

	// If user doesn't exist by Google ID, try to find by email
	if result.Error != nil {
		result = database.DB.Where("email = ?", email).First(&user)

		// If user exists by email but not Google ID, update the Google ID
		if result.Error == nil {
			user.GoogleID = googleID
			if err := database.DB.Save(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
				return
			}
		} else {
			// Create new user
			user = models.User{
				ID:       uuid.New(),
				Email:    email,
				Name:     name,
				Picture:  picture,
				GoogleID: googleID,
				Role:     "user",
			}

			if err := database.DB.Create(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
				return
			}

			// Create default subscription for new user
			var freeTier models.SubscriptionTier
			if err := database.DB.Where("name = ?", "Starter").First(&freeTier).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find starter tier"})
				return
			}

			// TODO: Update to use models.ShopSubscription with proper shop association
			// subscription := models.ShopSubscription{
			//	ID:                 uuid.New(),
			//	ShopID:             shopID, // Need to get shop ID from context
			//	SubscriptionTierID: freeTier.ID,
			//	StartDate:          time.Now(),
			//	AutoRenew:          true,
			//	Status:             "active",
			//	CreditBalance:      freeTier.CreditLimit,
			// }
			// 
			// if err := database.DB.Create(&subscription).Error; err != nil {
			//	c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
			//	return
			// }
		}
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// GoogleUserInfo represents the response from Google's userinfo endpoint
type GoogleUserInfo struct {
	Sub           string `json:"sub"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Profile       string `json:"profile"`
	Picture       string `json:"picture"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Locale        string `json:"locale"`
}

// getGoogleUserInfo gets user info from Google's userinfo endpoint
func getGoogleUserInfo(accessToken string) (*GoogleUserInfo, error) {
	// Make request to Google's userinfo endpoint
	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v3/userinfo", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add authorization header
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	// Send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to get user info: %s", body)
	}

	// Parse response
	var userInfo GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &userInfo, nil
}

// Login handles email/password login
func Login(c *gin.Context) {
	type LoginRequest struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by email
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	// Check if the user has a password (might be a Google-only user)
	if user.Password == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "This account doesn't have a password set"})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// RegisterUser creates a new user with email and password
func RegisterUser(c *gin.Context) {
	type RegisterRequest struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required,min=8"`
		Name     string `json:"name" binding:"required"`
	}

	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user already exists
	var existingUser models.User
	result := database.DB.Where("email = ?", req.Email).First(&existingUser)
	if result.Error == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "User with this email already exists"})
		return
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Create new user
	user := models.User{
		ID:       uuid.New(),
		Email:    req.Email,
		Name:     req.Name,
		Password: string(hashedPassword),
		Role:     "user",
		GoogleID: "email_" + uuid.New().String(), // Set a unique google_id for email registrations
	}

	if err := database.DB.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// Create default subscription for new user
	var freeTier models.SubscriptionTier
	if err := database.DB.Where("name = ?", "Starter").First(&freeTier).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find starter tier"})
		return
	}

	// TODO: Update to use models.ShopSubscription with proper shop association
	// subscription := models.ShopSubscription{
	//	ID:                 uuid.New(),
	//	ShopID:             shopID, // Need to get shop ID from context
	//	SubscriptionTierID: freeTier.ID,
	//	StartDate:          time.Now(),
	//	AutoRenew:          true,
	//	Status:             "active",
	//	CreditBalance:      freeTier.CreditLimit,
	// }
	// 
	// if err := database.DB.Create(&subscription).Error; err != nil {
	//	c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
	//	return
	// }

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusCreated, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// ForgotPassword initiates the password reset process
func ForgotPassword(c *gin.Context) {
	type ForgotPasswordRequest struct {
		Email string `json:"email" binding:"required,email"`
	}

	var req ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user exists
	var user models.User
	if err := database.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		// Don't reveal that the email doesn't exist for security reasons
		c.JSON(http.StatusOK, gin.H{"message": "If your email is registered, you will receive a password reset link"})
		return
	}

	// Generate a reset token
	resetToken := uuid.New().String()

	// Set token expiration (1 hour from now)
	expiresAt := time.Now().Add(time.Hour)

	// Store the token in the database
	user.ResetToken = resetToken
	user.ResetTokenExpiresAt = &expiresAt

	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process password reset"})
		return
	}

	// Send password reset email
	emailService := services.NewEmailService()
	if err := emailService.SendPasswordResetEmail(user.Email, resetToken); err != nil {
		// Log the error but don't fail the request for security reasons
		// Email sending failed - this is logged but request continues for security
	}

	// For development, also include debug link
	var response gin.H
	if os.Getenv("ENVIRONMENT") == "development" {
		resetLink := fmt.Sprintf("%s/auth/reset-password?token=%s", os.Getenv("FRONTEND_URL"), resetToken)
		response = gin.H{
			"message":    "Password reset link has been sent to your email",
			"debug_link": resetLink, // Only in development
		}
	} else {
		response = gin.H{
			"message": "If your email is registered, you will receive a password reset link",
		}
	}

	c.JSON(http.StatusOK, response)
}

// ResetPassword completes the password reset process
func ResetPassword(c *gin.Context) {
	type ResetPasswordRequest struct {
		Token    string `json:"token" binding:"required"`
		Password string `json:"password" binding:"required,min=8"`
	}

	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by reset token
	var user models.User
	if err := database.DB.Where("reset_token = ?", req.Token).First(&user).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired reset token"})
		return
	}

	// Check if token is expired
	if user.ResetTokenExpiresAt == nil || user.ResetTokenExpiresAt.Before(time.Now()) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Reset token has expired"})
		return
	}

	// Hash the new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Update user's password and clear reset token
	user.Password = string(hashedPassword)
	user.ResetToken = ""
	user.ResetTokenExpiresAt = nil

	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password has been reset successfully"})
}

// RefreshToken handles token refresh
func RefreshToken(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse the refresh token
	claims := &jwt.RegisteredClaims{}
	token, err := jwt.ParseWithClaims(req.RefreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(os.Getenv("JWT_REFRESH_SECRET")), nil
	})

	if err != nil || !token.Valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	}

	// Get user ID from token
	userID := claims.Subject

	// Generate new tokens
	newToken, newRefreshToken, err := generateTokens(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":         newToken,
		"refresh_token": newRefreshToken,
	})
}

// generateTokens creates a new JWT token and refresh token
func generateTokens(userID string) (string, string, error) {
	// Create access token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   userID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24)), // 24 hours
		IssuedAt:  jwt.NewNumericDate(time.Now()),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return "", "", err
	}

	// Create refresh token
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   userID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 30)), // 30 days
		IssuedAt:  jwt.NewNumericDate(time.Now()),
	})

	refreshTokenString, err := refreshToken.SignedString([]byte(os.Getenv("JWT_REFRESH_SECRET")))
	if err != nil {
		return "", "", err
	}

	return tokenString, refreshTokenString, nil
}

// CustomerLogin handles shop customer login with username/password
func CustomerLogin(c *gin.Context) {
	type CustomerLoginRequest struct {
		Username  string `json:"username" binding:"required"`
		Password  string `json:"password" binding:"required"`
		LoginType string `json:"login_type"` // "shop_customer"
	}

	var req CustomerLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user by external identifier (username) and shop context
	var user models.User
	query := database.DB.Where("external_user_identifier = ? AND is_external_user = ? AND role = ?", 
		req.Username, true, "customer")

	if err := query.First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
		return
	}

	// Check if the user has a password
	if user.Password == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "No password set for this account"})
		return
	}

	// Verify password (for now, comparing plain text - should be hashed in production)
	if user.Password != req.Password {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
		return
	}

	// Get shop information
	var shop models.Shop
	if user.ShopID != nil {
		database.DB.First(&shop, "id = ?", *user.ShopID)
	}

	// Get customer record to get credit balance
	var customer models.ShopCustomer
	if user.ShopID != nil {
		database.DB.Where("user_id = ? AND shop_id = ?", user.ID, *user.ShopID).First(&customer)
	}

	// Generate tokens
	token, refreshToken, err := generateTokens(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	// Prepare user response with shop context
	userResponse := user
	userResponse.Password = "" // Don't return password

	c.JSON(http.StatusOK, gin.H{
		"token":         token,
		"refresh_token": refreshToken,
		"user": gin.H{
			"id":             user.ID,
			"name":           user.Name,
			"username":       user.ExternalUserIdentifier,
			"role":           user.Role,
			"shop_id":        user.ShopID,
			"shop_name":      shop.Name,
			"credit_balance": customer.CreditBalance,
		},
	})
}

// ChangeCustomerPassword allows customers to change their password
func ChangeCustomerPassword(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	
	// Only allow customers to change their password
	if user.Role != "customer" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only customers can use this endpoint"})
		return
	}

	type ChangePasswordRequest struct {
		CurrentPassword string `json:"current_password" binding:"required"`
		NewPassword     string `json:"new_password" binding:"required,min=6"`
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify current password
	if user.Password != req.CurrentPassword {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Current password is incorrect"})
		return
	}

	// Update password (should be hashed in production)
	user.Password = req.NewPassword
	if err := database.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password updated successfully"})
}

// GetMyCredits returns customer's credit information
func GetMyCredits(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("shopID")

	// Only allow customers
	if user.Role != "customer" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only customers can access this endpoint"})
		return
	}

	// Get customer record
	var customer models.ShopCustomer
	if err := database.DB.Where("user_id = ? AND shop_id = ?", user.ID, shopID).First(&customer).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Customer record not found"})
		return
	}

	// Get recent transactions
	var transactions []models.ShopCreditTransaction
	database.DB.Where("shop_id = ? AND customer_id = ?", shopID, customer.ID).
		Order("created_at DESC").
		Limit(20).
		Find(&transactions)

	// Calculate totals
	var totalEarned, totalSpent int
	for _, transaction := range transactions {
		if transaction.Amount > 0 {
			totalEarned += transaction.Amount
		} else {
			totalSpent += -transaction.Amount
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"balance":       customer.CreditBalance,
		"total_earned":  totalEarned,
		"total_spent":   totalSpent,
		"transactions":  transactions,
	})
}

// SSOLogin initiates SSO authentication flow
func SSOLogin(ssoClient *sdk.SSOClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		loginResp, err := ssoClient.GetSSOLoginURL()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to initiate SSO login",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "SSO login URL generated",
			"data": gin.H{
				"redirect_url": loginResp.RedirectURL,
				"state":        loginResp.State,
			},
		})
	}
}

// SSOCallback handles SSO authentication callback
func SSOCallback(ssoClient *sdk.SSOClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Query("code")
		state := c.Query("state")

		if code == "" || state == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Missing required parameters: code and state",
			})
			return
		}

		// Handle SSO callback
		callbackResp, err := ssoClient.HandleSSOCallback(code, state)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "SSO authentication failed",
			})
			return
		}

		// Check if user exists in local database, create if not
		var user models.User
		if err := database.DB.Where("email = ?", callbackResp.Email).First(&user).Error; err != nil {
			// User doesn't exist, create new user
			userID, _ := uuid.Parse(callbackResp.UserID)
			user = models.User{
				ID:     userID,
				Email:  callbackResp.Email,
				Name:   callbackResp.FullName,  // Use Name instead of FirstName
				Role:   callbackResp.Role,
				// Note: Credit service User model doesn't have SSOUserID or IsVerified fields
				// We can use GoogleID field to store SSO user reference
				GoogleID: "sso_" + callbackResp.UserID,
			}
			
			if err := database.DB.Create(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Failed to create user",
				})
				return
			}
		} else {
			// Update existing user with SSO info using GoogleID field as SSO reference
			user.GoogleID = "sso_" + callbackResp.UserID
			if err := database.DB.Save(&user).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Failed to update user",
				})
				return
			}
		}

		// Generate local JWT tokens for backward compatibility
		token, refreshToken, err := generateTokens(user.ID.String())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate tokens",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "SSO authentication successful",
			"data": gin.H{
				"user":          user,
				"access_token":  token,
				"refresh_token": refreshToken,
				"sso_token":     callbackResp.AccessToken, // Original SSO token
			},
		})
	}
}
