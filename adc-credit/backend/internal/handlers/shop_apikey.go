package handlers

import (
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetShopAPIKeys returns all API keys for a specific shop
func GetShopAPIKeys(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.J<PERSON>N(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var apiKeys []models.APIKey
	if err := database.DB.Where("shop_id = ?", shop.ID).Find(&apiKeys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch API keys"})
		return
	}

	c.JSON(http.StatusOK, apiKeys)
}

// CreateShopAPIKey creates a new API key for a specific shop
func CreateShopAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	type CreateAPIKeyRequest struct {
		Name        string   `json:"name" binding:"required"`
		Permissions []string `json:"permissions"`
	}

	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate API key
	key, err := generateAPIKey()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}

	apiKey := models.APIKey{
		ID:          uuid.New(),
		UserID:      user.ID,
		ShopID:      &shop.ID,
		Name:        req.Name,
		Key:         key,
		Permissions: req.Permissions,
		Enabled:     true,
	}

	if err := database.DB.Create(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create API key"})
		return
	}

	c.JSON(http.StatusCreated, apiKey)
}

// GetShopAPIKey returns a specific API key for a shop
func GetShopAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	keyID := c.Param("keyId")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_id = ?", keyID, shop.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// UpdateShopAPIKey updates an existing API key for a shop
func UpdateShopAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	keyID := c.Param("keyId")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_id = ?", keyID, shop.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	type UpdateAPIKeyRequest struct {
		Name        string   `json:"name"`
		Permissions []string `json:"permissions"`
		Enabled     *bool    `json:"enabled"`
	}

	var req UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		apiKey.Name = req.Name
	}
	if req.Permissions != nil {
		apiKey.Permissions = req.Permissions
	}
	if req.Enabled != nil {
		apiKey.Enabled = *req.Enabled
	}

	if err := database.DB.Save(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update API key"})
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// DeleteShopAPIKey deletes an API key for a shop
func DeleteShopAPIKey(c *gin.Context) {
	user := c.MustGet("user").(models.User)
	shopID := c.Param("id")
	keyID := c.Param("keyId")

	// Verify shop ownership
	var shop models.Shop
	if user.Role == "admin" {
		// Admin can access any shop
		if err := database.DB.First(&shop, "id = ?", shopID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	} else {
		// Regular users can only access shops they own
		if err := database.DB.First(&shop, "id = ? AND owner_user_id = ?", shopID, user.ID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
			return
		}
	}

	var apiKey models.APIKey
	if err := database.DB.Where("id = ? AND shop_id = ?", keyID, shop.ID).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	if err := database.DB.Delete(&apiKey).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete API key"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "API key deleted successfully"})
}
