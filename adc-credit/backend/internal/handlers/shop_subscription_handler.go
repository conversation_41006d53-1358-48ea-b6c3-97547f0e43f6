package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ShopSubscriptionHandler handles subscription operations for shop-based subscriptions with ADC integration
type ShopSubscriptionHandler struct {
	serviceContainer *services.ServiceContainer
}

// NewShopSubscriptionHandler creates a new shop subscription handler
func NewShopSubscriptionHandler(serviceContainer *services.ServiceContainer) *ShopSubscriptionHandler {
	return &ShopSubscriptionHandler{
		serviceContainer: serviceContainer,
	}
}

// GetSubscriptionTiers returns all available subscription tiers from both systems
func (h *ShopSubscriptionHandler) GetSubscriptionTiers(c *gin.Context) {
	// First try to get tiers from legacy system
	var tiers []models.SubscriptionTier
	if err := database.DB.Find(&tiers).Error; err != nil {
		logrus.Errorf("Failed to fetch legacy subscription tiers: %v", err)
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription tiers"})
		return
	}

	// TODO: Enhance to also fetch tiers from ADC service and merge them
	// For now, return legacy tiers with hybrid service indication
	response := gin.H{
		"tiers":  tiers,
		"source": "legacy",
		"shop_mode": true,
	}

	c.JSON(http.StatusOK, response)
}

// GetShopSubscription gets subscription information for a shop using shop subscription service
func (h *ShopSubscriptionHandler) GetShopSubscription(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Use shop subscription service to get subscription info
	shopService := h.serviceContainer.GetShopSubscriptionService()
	tier, err := shopService.GetShopSubscriptionInfo(shopID)
	if err != nil {
		logrus.Errorf("Failed to get shop subscription for %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Subscription not found",
			"message": "No subscription found for this shop",
		})
		return
	}

	// Get usage statistics
	usage, err := shopService.GetSubscriptionUsage(shopID)
	if err != nil {
		logrus.Warnf("Failed to get usage statistics for shop %s: %v", shopID, err)
		usage = map[string]int{} // Continue with empty usage
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id": shopID,
		"subscription": gin.H{
			"tier_name":              tier.Name,
			"tier_price":             tier.Price,
			"credit_limit":           tier.CreditLimit,
			"max_shops":              tier.MaxShops,
			"max_customers_per_shop": tier.MaxCustomersPerShop,
			"max_api_keys_per_shop":  tier.MaxAPIKeysPerShop,
			"max_branches_per_shop":  tier.MaxBranchesPerShop,
			"max_qr_codes_per_month": tier.MaxQRCodesPerMonth,
			"max_webhooks":           tier.MaxWebhooks,
			"analytics_history_days": tier.AnalyticsHistoryDays,
			"support_level":          tier.SupportLevel,
			"advanced_analytics":     tier.AdvancedAnalytics,
		},
		"usage": usage,
		"limits_status": h.calculateLimitsStatus(tier, usage),
	})
}

// CreateShopSubscription creates a subscription for a shop in both systems
func (h *ShopSubscriptionHandler) CreateShopSubscription(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	type CreateShopSubscriptionRequest struct {
		SubscriptionTierID uint   `json:"subscription_tier_id" binding:"required"`
		AutoRenew          bool   `json:"auto_renew"`
		BillingCycle       string `json:"billing_cycle"` // "monthly" or "yearly"
	}

	var req CreateShopSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate billing cycle
	if req.BillingCycle == "" {
		req.BillingCycle = "monthly"
	}
	if req.BillingCycle != "monthly" && req.BillingCycle != "yearly" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid billing cycle. Must be 'monthly' or 'yearly'"})
		return
	}

	// Get the subscription tier
	var tier models.SubscriptionTier
	if err := database.DB.First(&tier, req.SubscriptionTierID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
		return
	}

	// Get the shop
	var shop models.MerchantShop
	if err := database.DB.Preload("Owner").First(&shop, shopID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Create subscription in legacy system first
	startDate := time.Now()
	subscription := models.ShopSubscription{
		ID:                 uuid.New(),
		ShopID:             shopID,
		SubscriptionTierID: req.SubscriptionTierID,
		StartDate:          startDate,
		EndDate:            nil,
		AutoRenew:          req.AutoRenew,
		Status:             "created",
		CreditBalance:      tier.CreditLimit,
	}

	if err := database.DB.Create(&subscription).Error; err != nil {
		logrus.Errorf("Failed to create shop subscription in legacy system: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
		return
	}

	// TODO: Create subscription in ADC service
	// For now, log the intention
	logrus.Infof("Would create subscription in ADC service for shop %s with tier %s", shopID, tier.Name)

	c.JSON(http.StatusCreated, gin.H{
		"subscription_id": subscription.ID,
		"shop_id":         shopID,
		"tier":            tier.Name,
		"status":          subscription.Status,
		"credit_balance":  subscription.CreditBalance,
		"auto_renew":      subscription.AutoRenew,
		"billing_cycle":   req.BillingCycle,
		"message":         "Subscription created successfully",
	})
}

// UpdateShopSubscription updates a shop subscription in both systems
func (h *ShopSubscriptionHandler) UpdateShopSubscription(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	type UpdateShopSubscriptionRequest struct {
		SubscriptionTierID *uint  `json:"subscription_tier_id"`
		AutoRenew          *bool  `json:"auto_renew"`
		Status             string `json:"status"`
	}

	var req UpdateShopSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get existing subscription
	var subscription models.ShopSubscription
	if err := database.DB.Preload("SubscriptionTier").Where("shop_id = ?", shopID).First(&subscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop subscription not found"})
		return
	}

	// Update fields if provided
	if req.SubscriptionTierID != nil {
		subscription.SubscriptionTierID = *req.SubscriptionTierID
	}
	if req.AutoRenew != nil {
		subscription.AutoRenew = *req.AutoRenew
	}
	if req.Status != "" {
		subscription.Status = req.Status
	}

	// Save updated subscription
	if err := database.DB.Save(&subscription).Error; err != nil {
		logrus.Errorf("Failed to update shop subscription: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update subscription"})
		return
	}

	// TODO: Update subscription in ADC service
	logrus.Infof("Would update subscription in ADC service for shop %s", shopID)

	c.JSON(http.StatusOK, gin.H{
		"subscription_id": subscription.ID,
		"shop_id":         shopID,
		"status":          subscription.Status,
		"auto_renew":      subscription.AutoRenew,
		"message":         "Subscription updated successfully",
	})
}

// CheckSubscriptionLimits validates if an action is allowed for a shop
func (h *ShopSubscriptionHandler) CheckSubscriptionLimits(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	limitType := c.Query("limit_type")
	if limitType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "limit_type query parameter is required"})
		return
	}

	currentUsageStr := c.Query("current_usage")
	currentUsage := 0
	if currentUsageStr != "" {
		if parsed, err := strconv.Atoi(currentUsageStr); err == nil {
			currentUsage = parsed
		}
	}

	// Use shop subscription service to check limits
	shopService := h.serviceContainer.GetShopSubscriptionService()
	allowed, err := shopService.CheckSubscriptionLimit(shopID, limitType, currentUsage)
	if err != nil {
		logrus.Errorf("Failed to check subscription limit: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check subscription limit",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":       shopID,
		"limit_type":    limitType,
		"current_usage": currentUsage,
		"allowed":       allowed,
		"checked_via":   "shop_service",
	})
}

// GetShopSubscriptionFeatures returns available features for a shop's subscription
func (h *ShopSubscriptionHandler) GetShopSubscriptionFeatures(c *gin.Context) {
	shopIDStr := c.Param("id")
	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shopService := h.serviceContainer.GetShopSubscriptionService()

	// Check various features
	features := map[string]bool{}
	featureList := []string{"advanced_analytics", "premium_support", "unlimited_customers", "api_access", "webhook_support"}

	for _, feature := range featureList {
		enabled, err := shopService.IsFeatureEnabled(shopID, feature)
		if err != nil {
			logrus.Warnf("Failed to check feature %s for shop %s: %v", feature, shopID, err)
			features[feature] = false // Default to disabled on error
		} else {
			features[feature] = enabled
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":     shopID,
		"features":    features,
		"checked_via": "shop_service",
	})
}

// calculateLimitsStatus calculates the status of each limit (OK, Warning, Critical)
func (h *ShopSubscriptionHandler) calculateLimitsStatus(tier *models.SubscriptionTier, usage map[string]int) map[string]string {
	status := make(map[string]string)

	limits := map[string]int{
		"shops":       tier.MaxShops,
		"customers":   tier.MaxCustomersPerShop,
		"api_keys":    tier.MaxAPIKeysPerShop,
		"branches":    tier.MaxBranchesPerShop,
		"qr_codes":    tier.MaxQRCodesPerMonth,
		"webhooks":    tier.MaxWebhooks,
	}

	for limitType, maxValue := range limits {
		currentUsage := usage[limitType]
		
		if maxValue <= 0 { // Unlimited
			status[limitType] = "unlimited"
		} else {
			percentage := float64(currentUsage) / float64(maxValue) * 100
			
			if percentage >= 90 {
				status[limitType] = "critical"
			} else if percentage >= 75 {
				status[limitType] = "warning"
			} else {
				status[limitType] = "ok"
			}
		}
	}

	return status
}