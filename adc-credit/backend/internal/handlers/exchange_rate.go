package handlers

import (
	"fmt"
	"net/http"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CreateExchangeRate creates a new exchange rate for a shop
func CreateExchangeRate(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	user := userInterface.(models.User)

	shopSlug := c.Param("slug")
	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Check if user owns the shop
	if shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to manage this shop"})
		return
	}

	var req struct {
		CurrencyCode string  `json:"currency_code" binding:"required"`
		Rate         float64 `json:"rate" binding:"required,gt=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if an active exchange rate for this currency already exists
	var existingRate models.ShopExchangeRate
	if err := database.DB.Where("shop_id = ? AND currency_code = ? AND is_active = true", shop.ID, req.CurrencyCode).First(&existingRate).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "An active exchange rate for this currency already exists"})
		return
	}

	// Create new exchange rate
	exchangeRate := models.ShopExchangeRate{
		ShopID:       shop.ID,
		CurrencyCode: req.CurrencyCode,
		Rate:         req.Rate,
		IsActive:     true,
	}

	if err := database.DB.Create(&exchangeRate).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create exchange rate"})
		return
	}

	c.JSON(http.StatusCreated, exchangeRate)
}

// GetShopExchangeRates gets all exchange rates for a shop
func GetShopExchangeRates(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	user := userInterface.(models.User)

	shopSlug := c.Param("slug")
	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Check if user owns the shop
	if shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to view this shop's exchange rates"})
		return
	}

	var exchangeRates []models.ShopExchangeRate
	if err := database.DB.Where("shop_id = ?", shop.ID).Order("created_at DESC").Find(&exchangeRates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch exchange rates"})
		return
	}

	c.JSON(http.StatusOK, exchangeRates)
}

// UpdateExchangeRate updates an exchange rate
func UpdateExchangeRate(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	user := userInterface.(models.User)

	shopSlug := c.Param("slug")
	rateID := c.Param("id")

	if shopSlug == "" || rateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and rate ID are required"})
		return
	}

	rateUUID, err := uuid.Parse(rateID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rate ID"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Check if user owns the shop
	if shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to manage this shop"})
		return
	}

	var req struct {
		Rate     *float64 `json:"rate"`
		IsActive *bool    `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find the exchange rate
	var exchangeRate models.ShopExchangeRate
	if err := database.DB.Where("id = ? AND shop_id = ?", rateUUID, shop.ID).First(&exchangeRate).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Exchange rate not found"})
		return
	}

	// Update fields if provided
	updateData := make(map[string]interface{})
	if req.Rate != nil {
		if *req.Rate <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Rate must be greater than 0"})
			return
		}
		updateData["rate"] = *req.Rate
	}
	if req.IsActive != nil {
		updateData["is_active"] = *req.IsActive
	}

	if len(updateData) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	if err := database.DB.Model(&exchangeRate).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update exchange rate"})
		return
	}

	// Fetch updated record
	if err := database.DB.Where("id = ?", rateUUID).First(&exchangeRate).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch updated exchange rate"})
		return
	}

	c.JSON(http.StatusOK, exchangeRate)
}

// DeleteExchangeRate deletes an exchange rate
func DeleteExchangeRate(c *gin.Context) {
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	user := userInterface.(models.User)

	shopSlug := c.Param("slug")
	rateID := c.Param("id")

	if shopSlug == "" || rateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and rate ID are required"})
		return
	}

	rateUUID, err := uuid.Parse(rateID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rate ID"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Check if user owns the shop
	if shop.OwnerUserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to manage this shop"})
		return
	}

	// Delete the exchange rate (soft delete)
	if err := database.DB.Where("id = ? AND shop_id = ?", rateUUID, shop.ID).Delete(&models.ShopExchangeRate{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete exchange rate"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Exchange rate deleted successfully"})
}

// GetActiveExchangeRate gets the active exchange rate for a specific currency
func GetActiveExchangeRate(c *gin.Context) {
	shopSlug := c.Param("slug")
	currencyCode := c.Query("currency")

	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	if currencyCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Currency code is required"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find active exchange rate for the currency
	var exchangeRate models.ShopExchangeRate
	if err := database.DB.Where("shop_id = ? AND currency_code = ? AND is_active = true", shop.ID, currencyCode).First(&exchangeRate).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active exchange rate found for this currency"})
		return
	}

	c.JSON(http.StatusOK, exchangeRate)
}

// ConvertCreditsToLocalCurrency converts credits to local currency using shop's exchange rate
func ConvertCreditsToLocalCurrency(c *gin.Context) {
	shopSlug := c.Param("slug")
	currencyCode := c.Query("currency")
	creditsParam := c.Query("credits")

	if shopSlug == "" || currencyCode == "" || creditsParam == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug, currency code, and credits are required"})
		return
	}

	// Parse credits
	var credits float64
	if _, err := fmt.Sscanf(creditsParam, "%f", &credits); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid credits value"})
		return
	}

	// Find the shop
	var shop models.Shop
	if err := database.DB.Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find active exchange rate for the currency
	var exchangeRate models.ShopExchangeRate
	if err := database.DB.Where("shop_id = ? AND currency_code = ? AND is_active = true", shop.ID, currencyCode).First(&exchangeRate).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active exchange rate found for this currency"})
		return
	}

	// Convert credits to local currency
	localCurrencyAmount := credits / exchangeRate.Rate

	c.JSON(http.StatusOK, gin.H{
		"credits":               credits,
		"currency_code":         currencyCode,
		"rate":                  exchangeRate.Rate,
		"local_currency_amount": localCurrencyAmount,
	})
}
