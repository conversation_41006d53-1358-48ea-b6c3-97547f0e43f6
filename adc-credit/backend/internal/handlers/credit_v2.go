package handlers

import (
	"net/http"
	"time"

	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// CreditV2Handler handles credit operations using the service container approach
type CreditV2Handler struct {
	serviceContainer *services.ServiceContainer
}

// NewCreditV2Handler creates a new credit handler with service container
func NewCreditV2Handler(serviceContainer *services.ServiceContainer) *CreditV2Handler {
	return &CreditV2Handler{
		serviceContainer: serviceContainer,
	}
}

// GetCreditBalance returns the credit balance for the current user's shop
func (h *CreditV2Handler) GetCreditBalance(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get the user's primary shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
		logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
		return
	}

	// Get active subscription for the shop
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shop.ID, "active").
		Preload("SubscriptionTier").
		First(&subscription).Error; err != nil {
		logrus.Errorf("Failed to find active subscription for shop %s: %v", shop.ID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"balance":        subscription.CreditBalance,
		"credit_balance": subscription.CreditBalance, // For backward compatibility
		"credit_limit":   subscription.SubscriptionTier.CreditLimit,
		"shop_id":        shop.ID,
		"subscription":   subscription,
	})
}

// AddCredits adds credits to a shop's account
func (h *CreditV2Handler) AddCredits(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	type AddCreditsRequest struct {
		ShopID      *uuid.UUID `json:"shop_id,omitempty"` // Optional - defaults to user's primary shop
		Amount      int        `json:"amount" binding:"required,min=1"`
		Description string     `json:"description"`
		Reference   string     `json:"reference"`
	}

	var req AddCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var shopID uuid.UUID
	if req.ShopID != nil {
		shopID = *req.ShopID
	} else {
		// Get the user's primary shop
		var shop models.Shop
		if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
			logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shop.ID
	}

	// Verify user has access to this shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ? AND owner_id = ?", shopID, user.ID).First(&shop).Error; err != nil {
		logrus.Errorf("User %s does not have access to shop %s: %v", user.ID, shopID, err)
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to shop"})
		return
	}

	// Get active subscription for the shop
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shopID, "active").First(&subscription).Error; err != nil {
		logrus.Errorf("Failed to find active subscription for shop %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Start a transaction
	tx := h.serviceContainer.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update credit balance
	subscription.CreditBalance += req.Amount
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		logrus.Errorf("Failed to update credit balance for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create transaction record using ShopTransaction
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopID,
		ShopSubscriptionID: subscription.ID,
		InitiatedByUserID:  user.ID,
		Type:               "credit_add",
		Amount:             req.Amount,
		Description:        req.Description,
		Reference:          req.Reference,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		logrus.Errorf("Failed to create transaction record for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		logrus.Errorf("Failed to commit transaction for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	logrus.Infof("Added %d credits to shop %s, new balance: %d", req.Amount, shopID, subscription.CreditBalance)

	// Report usage if service is available
	if h.serviceContainer.UsageReporting != nil {
		go func() {
			// TODO: Implement usage reporting with proper event types
			// For now, just log the credit addition
			logrus.Infof("Credit usage reported: shop=%s, amount=%d, user=%s, description=%s", 
				shopID, req.Amount, user.ID, req.Description)
		}()
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits added successfully",
		"credit_balance": subscription.CreditBalance,
		"new_balance":    subscription.CreditBalance, // For test compatibility
		"shop_id":        shopID,
		"transaction":    transaction,
	})
}

// ConsumeCredits consumes credits for an API operation
func (h *CreditV2Handler) ConsumeCredits(c *gin.Context) {
	apiKey := c.MustGet("apiKey").(models.APIKey)
	user := c.MustGet("user").(models.User)

	// Set credits consumed in context for usage tracking middleware
	defer func() {
		if credits, exists := c.Get("credits_consumed"); exists {
			c.Set("credits_consumed", credits)
			c.Set("consumption_reason", "api_usage")
		}
	}()

	type ConsumeCreditsRequest struct {
		ShopID    *uuid.UUID `json:"shop_id,omitempty"` // Optional - defaults to API key's shop
		Endpoint  string     `json:"endpoint" binding:"required"`
		Method    string     `json:"method" binding:"required"`
		Credits   int        `json:"credits" binding:"required,min=1"`
		IPAddress string     `json:"ip_address"`
		UserAgent string     `json:"user_agent"`
	}

	var req ConsumeCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var shopID uuid.UUID
	if req.ShopID != nil {
		shopID = *req.ShopID
	} else {
		// Get shop from API key or user's primary shop
		if apiKey.ShopID != nil {
			shopID = *apiKey.ShopID
		} else {
			// Get the user's primary shop
			var shop models.Shop
			if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
				logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
				c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
				return
			}
			shopID = shop.ID
		}
	}

	// Get active subscription for the shop
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shopID, "active").First(&subscription).Error; err != nil {
		logrus.Errorf("Failed to find active subscription for shop %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Check if enough credits
	if subscription.CreditBalance < req.Credits {
		logrus.Warnf("Insufficient credits for shop %s: need %d, have %d", shopID, req.Credits, subscription.CreditBalance)
		c.JSON(http.StatusPaymentRequired, gin.H{"error": "Insufficient credits"})
		return
	}

	// Start a transaction
	tx := h.serviceContainer.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update credit balance
	subscription.CreditBalance -= req.Credits
	if err := tx.Save(&subscription).Error; err != nil {
		tx.Rollback()
		logrus.Errorf("Failed to update credit balance for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update credit balance"})
		return
	}

	// Create usage record
	usage := models.Usage{
		ID:           uuid.New(),
		APIKeyID:     apiKey.ID,
		Endpoint:     req.Endpoint,
		Method:       req.Method,
		Credits:      req.Credits,
		Timestamp:    time.Now(),
		Success:      true,
		IPAddress:    req.IPAddress,
		UserAgent:    req.UserAgent,
		ResponseTime: 0, // Will be updated later
		StatusCode:   http.StatusOK,
	}

	if err := tx.Create(&usage).Error; err != nil {
		tx.Rollback()
		logrus.Errorf("Failed to create usage record for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create usage record"})
		return
	}

	// Create transaction record using ShopTransaction
	transaction := models.ShopTransaction{
		ID:                 uuid.New(),
		ShopID:             shopID,
		ShopSubscriptionID: subscription.ID,
		InitiatedByUserID:  user.ID,
		Type:               "credit_use",
		Amount:             -req.Credits, // Negative for consumption
		Description:        "API usage: " + req.Method + " " + req.Endpoint,
		Reference:          usage.ID.String(),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		logrus.Errorf("Failed to create transaction record for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction record"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		logrus.Errorf("Failed to commit transaction for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	logrus.Infof("Consumed %d credits from shop %s, new balance: %d", req.Credits, shopID, subscription.CreditBalance)

	// Set credits consumed for middleware tracking
	c.Set("credits_consumed", int64(req.Credits))

	c.JSON(http.StatusOK, gin.H{
		"message":        "Credits consumed successfully",
		"credit_balance": subscription.CreditBalance,
		"shop_id":        shopID,
		"usage":          usage,
	})
}

// GetTransactions returns the transaction history for a shop
func (h *CreditV2Handler) GetTransactions(c *gin.Context) {
	user := c.MustGet("user").(models.User)

	// Get shop ID from query parameter or use user's primary shop
	shopIDStr := c.Query("shop_id")
	var shopID uuid.UUID

	if shopIDStr != "" {
		var err error
		shopID, err = uuid.Parse(shopIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
			return
		}
	} else {
		// Get the user's primary shop
		var shop models.Shop
		if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
			logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shop.ID
	}

	// Verify user has access to this shop
	var shop models.Shop
	if err := h.serviceContainer.DB.Where("id = ? AND owner_id = ?", shopID, user.ID).First(&shop).Error; err != nil {
		logrus.Errorf("User %s does not have access to shop %s: %v", user.ID, shopID, err)
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to shop"})
		return
	}

	var transactions []models.ShopTransaction
	if err := h.serviceContainer.DB.Where("shop_id = ?", shopID).
		Order("created_at DESC").
		Find(&transactions).Error; err != nil {
		logrus.Errorf("Failed to fetch transactions for shop %s: %v", shopID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch transactions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"shop_id":      shopID,
		"transactions": transactions,
	})
}

// VerifyAPIKey verifies if an API key is valid and has enough credits
func (h *CreditV2Handler) VerifyAPIKey(c *gin.Context) {
	type VerifyAPIKeyRequest struct {
		APIKey  string     `json:"api_key" binding:"required"`
		ShopID  *uuid.UUID `json:"shop_id,omitempty"`
		Credits int        `json:"credits" binding:"required,min=1"`
	}

	var req VerifyAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var apiKey models.APIKey
	if err := h.serviceContainer.DB.Where("key = ? AND enabled = ?", req.APIKey, true).First(&apiKey).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
		return
	}

	// Get user associated with API key
	var user models.User
	if err := h.serviceContainer.DB.First(&user, "id = ?", apiKey.UserID).Error; err != nil {
		logrus.Errorf("Failed to find user associated with API key: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find user associated with API key"})
		return
	}

	var shopID uuid.UUID
	if req.ShopID != nil {
		shopID = *req.ShopID
	} else if apiKey.ShopID != nil {
		shopID = *apiKey.ShopID
	} else {
		// Get the user's primary shop
		var shop models.Shop
		if err := h.serviceContainer.DB.Where("owner_id = ?", user.ID).First(&shop).Error; err != nil {
			logrus.Errorf("Failed to find shop for user %s: %v", user.ID, err)
			c.JSON(http.StatusNotFound, gin.H{"error": "No shop found for user"})
			return
		}
		shopID = shop.ID
	}

	// Get active subscription for the shop
	var subscription models.ShopSubscription
	if err := h.serviceContainer.DB.Where("shop_id = ? AND status = ?", shopID, "active").First(&subscription).Error; err != nil {
		logrus.Errorf("Failed to find active subscription for shop %s: %v", shopID, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	// Check if enough credits
	if subscription.CreditBalance < req.Credits {
		c.JSON(http.StatusPaymentRequired, gin.H{
			"error":          "Insufficient credits",
			"credit_balance": subscription.CreditBalance,
			"required":       req.Credits,
			"shop_id":        shopID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":          true,
		"credit_balance": subscription.CreditBalance,
		"shop_id":        shopID,
	})
}