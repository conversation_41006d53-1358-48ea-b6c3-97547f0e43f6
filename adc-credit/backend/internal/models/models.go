package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID                     uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Email                  string         `gorm:"uniqueIndex;not null" json:"email"`
	Name                   string         `json:"name"`
	Picture                string         `json:"picture"`
	Password               string         `json:"-"` // Password is never returned in JSON
	GoogleID               string         `gorm:"uniqueIndex" json:"google_id,omitempty"`
	Role                   string         `gorm:"default:user" json:"role"`
	ResetToken             string         `json:"-"` // Password reset token
	ResetTokenExpiresAt    *time.Time     `json:"-"` // Password reset token expiration
	OrganizationID         *uuid.UUID     `gorm:"type:uuid" json:"organization_id"`
	BranchID               *uuid.UUID     `gorm:"type:uuid" json:"branch_id"`
	ShopID                 *uuid.UUID     `gorm:"type:uuid" json:"shop_id"`
	ShopBranchID           *uuid.UUID     `gorm:"type:uuid" json:"shop_branch_id"`
	MonthlyCredits         int            `gorm:"default:0" json:"monthly_credits"`
	NextCreditResetDate    *time.Time     `json:"next_credit_reset_date"`
	MonthlyUsageResetDate  *time.Time     `json:"monthly_usage_reset_date"`
	IsExternalUser         bool           `gorm:"default:false" json:"is_external_user"` // Indicates if user was created by an external service
	ExternalUserIdentifier string         `json:"external_user_identifier,omitempty"`    // External identifier for users created by external services
	APIKeys                []APIKey       `json:"api_keys,omitempty"`
	Transactions           []Transaction  `json:"transactions,omitempty"`
	Webhooks               []Webhook      `json:"webhooks,omitempty"`
	CreatedAt              time.Time      `json:"created_at"`
	UpdatedAt              time.Time      `json:"updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"index" json:"-"`
}

// APIKey represents an API key for accessing the credit system
type APIKey struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID        uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	ShopID        *uuid.UUID     `gorm:"type:uuid" json:"shop_id"`        // Optional: Shop-level API key
	ShopBranchID  *uuid.UUID     `gorm:"type:uuid" json:"shop_branch_id"` // Optional: Branch-level API key
	Name          string         `json:"name"`
	Key           string         `gorm:"uniqueIndex;not null" json:"key"`
	LastUsed      *time.Time     `json:"last_used"`
	Enabled       bool           `gorm:"default:true" json:"enabled"`
	Permissions   StringSlice    `gorm:"type:text" json:"permissions"`
	RateLimitMax  int            `gorm:"default:60" json:"rate_limit_max"`
	RateLimitRate float64        `gorm:"default:1.0" json:"rate_limit_rate"`
	Usage         []Usage        `json:"usage,omitempty"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// SubscriptionTier - Legacy compatibility stub for service integration
// Actual subscription data comes from ADC Subscription Service
type SubscriptionTier struct {
	ID                     uint    `json:"id"`
	Name                   string  `json:"name"`
	Price                  int     `json:"price"`
	CreditLimit            int     `json:"credit_limit"`
	ShopLimit              int     `json:"shop_limit"`
	CustomerLimit          int     `json:"customer_limit"`
	APIKeyLimit            int     `json:"api_key_limit"`
	RateLimitMax           int     `json:"rate_limit_max"`
	RateLimitRate          float64 `json:"rate_limit_rate"`
	MaxWebhooks            int     `json:"max_webhooks"`
	AdvancedAnalytics      bool    `json:"advanced_analytics"`
	MaxShops               int     `json:"max_shops"`
	MaxCustomersPerShop    int     `json:"max_customers_per_shop"`
	MaxAPIKeysPerShop      int     `json:"max_api_keys_per_shop"`
	MaxBranchesPerShop     int     `json:"max_branches_per_shop"`
	MaxQRCodesPerMonth     int     `json:"max_qr_codes_per_month"`
	AnalyticsHistoryDays   int      `json:"analytics_history_days"`
	SupportLevel           string   `json:"support_level"`
	UnlimitedShops         bool     `json:"unlimited_shops"`
	UnlimitedCustomers     bool     `json:"unlimited_customers"`
	UnlimitedBranches      bool     `json:"unlimited_branches"`
	UnlimitedQRCodes       bool     `json:"unlimited_qr_codes"`
	AllowedShopTypes       []string `json:"allowed_shop_types"`
}


// Usage represents API usage by an API key
type Usage struct {
	ID           uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	APIKeyID     uuid.UUID      `gorm:"type:uuid;not null" json:"api_key_id"`
	Endpoint     string         `json:"endpoint"`
	Method       string         `json:"method"`
	Credits      int            `json:"credits"`
	Timestamp    time.Time      `json:"timestamp"`
	Success      bool           `gorm:"default:true" json:"success"`
	IPAddress    string         `json:"ip_address"`
	UserAgent    string         `json:"user_agent"`
	ResponseTime int            `json:"response_time"` // in milliseconds
	StatusCode   int            `json:"status_code"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// Transaction represents a credit transaction (addition or consumption)
type Transaction struct {
	ID             uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID         uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	// SubscriptionID uuid.UUID   `gorm:"type:uuid" json:"subscription_id"` // REMOVED: Subscriptions now handled by centralized service
	OrganizationID *uuid.UUID     `gorm:"type:uuid" json:"organization_id"`
	BranchID       *uuid.UUID     `gorm:"type:uuid" json:"branch_id"`
	Type           string         `json:"type"` // "credit_add", "credit_use", "credit_scheduled", "credit_reset"
	Amount         int            `json:"amount"`
	Description    string         `json:"description"`
	Reference      string         `json:"reference"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
}

// Webhook represents a webhook configuration for event notifications
type Webhook struct {
	ID         uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID     uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	Name       string         `json:"name"`
	URL        string         `json:"url"`
	Secret     string         `json:"secret"`
	Events     StringSlice    `gorm:"type:text[]" json:"events"` // "credit.consumed", "credit.added", "api_key.created", etc.
	Active     bool           `gorm:"default:true" json:"active"`
	LastCalled *time.Time     `json:"last_called"`
	FailCount  int            `gorm:"default:0" json:"fail_count"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// WebhookDelivery represents a record of a webhook delivery attempt
type WebhookDelivery struct {
	ID         uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	WebhookID  uuid.UUID      `gorm:"type:uuid;not null" json:"webhook_id"`
	Event      string         `json:"event"`
	Payload    string         `gorm:"type:text" json:"payload"`
	StatusCode int            `json:"status_code"`
	Response   string         `gorm:"type:text" json:"response"`
	Success    bool           `gorm:"default:false" json:"success"`
	Duration   int            `json:"duration"` // in milliseconds
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// MonthlyUsageTracking represents monthly usage tracking for subscription limits
type MonthlyUsageTracking struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	Year             int            `gorm:"not null" json:"year"`
	Month            int            `gorm:"not null" json:"month"`
	
	// Monthly resettable counts
	QRCodesGenerated int            `gorm:"default:0" json:"qr_codes_generated"`
	APICallsMade     int            `gorm:"default:0" json:"api_calls_made"`
	CreditsConsumed  int            `gorm:"default:0" json:"credits_consumed"`
	
	// Persistent counts (for analytics, don't reset monthly)
	ShopsCreated     int            `gorm:"default:0" json:"shops_created"`
	CustomersAdded   int            `gorm:"default:0" json:"customers_added"`
	APIKeysCreated   int            `gorm:"default:0" json:"api_keys_created"`
	BranchesCreated  int            `gorm:"default:0" json:"branches_created"`
	WebhooksCreated  int            `gorm:"default:0" json:"webhooks_created"`
	
	// Metadata
	LastResetDate    time.Time      `gorm:"default:current_timestamp" json:"last_reset_date"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`
}

// AnalyticsData represents aggregated analytics data for advanced reporting
type AnalyticsData struct {
	ID              uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          uuid.UUID      `gorm:"type:uuid;not null" json:"user_id"`
	Date            time.Time      `json:"date"`
	APIKeyID        *uuid.UUID     `json:"api_key_id"`
	Endpoint        string         `json:"endpoint"`
	TotalRequests   int            `json:"total_requests"`
	TotalCredits    int            `json:"total_credits"`
	AvgResponseTime int            `json:"avg_response_time"` // in milliseconds
	ErrorRate       float64        `json:"error_rate"`        // percentage
	P95ResponseTime int            `json:"p95_response_time"` // 95th percentile response time
	P99ResponseTime int            `json:"p99_response_time"` // 99th percentile response time
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopSubscription represents a shop-level subscription (integrates with ADC Subscription Service)
type ShopSubscription struct {
	ID                    uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID                uuid.UUID      `gorm:"type:uuid;not null;uniqueIndex:idx_shop_active_subscription" json:"shop_id"`
	Shop                  Shop           `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	SubscriptionTierID uint            `json:"subscription_tier_id"` // Legacy compatibility - migrating to ADC Subscription Service
	SubscriptionTier   SubscriptionTier `json:"subscription_tier"`   // Legacy compatibility - migrating to ADC Subscription Service
	ADCSubscriptionID     string         `json:"adc_subscription_id"`                                              // Reference to ADC Subscription Service
	ADCOrganizationID     string         `json:"adc_organization_id"`                                              // Reference to ADC SSO Service organization
	StartDate             time.Time      `json:"start_date"`
	EndDate               *time.Time     `json:"end_date"`
	AutoRenew             bool           `gorm:"default:true" json:"auto_renew"`
	Status                string         `gorm:"default:active;index:idx_shop_active_subscription" json:"status"`
	CreditBalance         int            `gorm:"default:0" json:"credit_balance"`
	// StripeSubscriptionID string        `json:"stripe_subscription_id,omitempty"` // REMOVED: Stripe handling via ADC Subscription Service
	// PaymentMethodID     string         `json:"payment_method_id,omitempty"`      // REMOVED: Payment handling via ADC Subscription Service
	// BillingEmail        string         `json:"billing_email,omitempty"`          // REMOVED: Billing via ADC Subscription Service
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	DeletedAt             gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopUser represents the many-to-many relationship between shops and users
type ShopUser struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID    uuid.UUID      `gorm:"type:uuid;not null;uniqueIndex:idx_shop_user" json:"shop_id"`
	Shop      Shop           `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	UserID    uuid.UUID      `gorm:"type:uuid;not null;uniqueIndex:idx_shop_user" json:"user_id"`
	User      User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role      string         `gorm:"default:member" json:"role"` // "owner", "admin", "member"
	JoinedAt  time.Time      `gorm:"default:current_timestamp" json:"joined_at"`
	InvitedBy *uuid.UUID     `gorm:"type:uuid" json:"invited_by,omitempty"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ShopTransaction represents a credit transaction at the shop level
type ShopTransaction struct {
	ID                   uuid.UUID          `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID               uuid.UUID          `gorm:"type:uuid;not null" json:"shop_id"`
	ShopSubscriptionID   uuid.UUID          `gorm:"type:uuid" json:"shop_subscription_id"`
	ShopSubscription     ShopSubscription   `json:"shop_subscription,omitempty" gorm:"foreignKey:ShopSubscriptionID"`
	InitiatedByUserID    uuid.UUID          `gorm:"type:uuid;not null" json:"initiated_by_user_id"`
	InitiatedByUser      User               `json:"initiated_by_user,omitempty" gorm:"foreignKey:InitiatedByUserID"`
	Type                 string             `json:"type"` // "credit_add", "credit_use", "credit_scheduled", "credit_reset"
	Amount               int                `json:"amount"`
	Description          string             `json:"description"`
	Reference            string             `json:"reference"`
	APIKeyID             *uuid.UUID         `gorm:"type:uuid" json:"api_key_id,omitempty"`
	CreatedAt            time.Time          `json:"created_at"`
	UpdatedAt            time.Time          `json:"updated_at"`
	DeletedAt            gorm.DeletedAt     `gorm:"index" json:"-"`
}

// ShopMonthlyUsageTracking represents monthly usage tracking for shop subscription limits
type ShopMonthlyUsageTracking struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ShopID           uuid.UUID      `gorm:"type:uuid;not null" json:"shop_id"`
	Year             int            `gorm:"not null" json:"year"`
	Month            int            `gorm:"not null" json:"month"`
	
	// Monthly resettable counts
	QRCodesGenerated int            `gorm:"default:0" json:"qr_codes_generated"`
	APICallsMade     int            `gorm:"default:0" json:"api_calls_made"`
	CreditsConsumed  int            `gorm:"default:0" json:"credits_consumed"`
	
	// Persistent counts (for analytics, don't reset monthly)
	CustomersAdded   int            `gorm:"default:0" json:"customers_added"`
	APIKeysCreated   int            `gorm:"default:0" json:"api_keys_created"`
	BranchesCreated  int            `gorm:"default:0" json:"branches_created"`
	WebhooksCreated  int            `gorm:"default:0" json:"webhooks_created"`
	
	// Metadata
	LastResetDate    time.Time      `gorm:"default:current_timestamp" json:"last_reset_date"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`
}
