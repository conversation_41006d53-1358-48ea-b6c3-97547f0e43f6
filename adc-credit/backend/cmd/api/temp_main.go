package main

import (
	"log"
	"os"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/routes"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize database
	if err := database.Initialize(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Initialize Gin
	r := gin.Default()

	// CORS middleware
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// Setup routes (simplified for compilation)
	setupBasicRoutes(r)

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8400"
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupBasicRoutes(r *gin.Engine) {
	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "message": "Credit Service Backend is running"})
	})

	// Public subscription tiers endpoint for frontend
	r.GET("/api/v1/public/subscriptions/tiers", func(c *gin.Context) {
		// Return basic tier information
		tiers := []map[string]interface{}{
			{
				"id":    1,
				"name":  "Free",
				"price": 0,
				"features": []string{
					"1,000 credits per month",
					"Basic analytics",
					"Email support",
				},
			},
			{
				"id":    2,
				"name":  "Pro",
				"price": 29,
				"features": []string{
					"10,000 credits per month",
					"Advanced analytics",
					"Priority support",
					"Multiple shops",
				},
			},
			{
				"id":    3,
				"name":  "Enterprise",
				"price": 99,
				"features": []string{
					"50,000 credits per month",
					"Advanced analytics",
					"24/7 support",
					"Unlimited shops",
					"Custom integrations",
				},
			},
		}
		c.JSON(200, gin.H{"data": tiers})
	})

	log.Println("Basic routes configured")
}