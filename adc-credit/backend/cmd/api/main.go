package main

import (
	"time"
	"github.com/sirupsen/logrus"
	"os"

	// "github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/middleware"
	// "github.com/adc-credit/backend/internal/routes"
	"github.com/adc-credit/backend/internal/utils"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	// ssoSDK "github.com/adc-sso-service/sdk"
	// subscriptionSDK "github.com/adc-subscription-service/sdk"
)

func main() {
	// Load environment variables
	// Try to load from the current directory first
	if err := godotenv.Load(); err != nil {
		// Try to load from the backend directory (when running from project root)
		if err := godotenv.Load("backend/.env"); err != nil {
			// Try to load from parent directory (when running from backend/cmd/api)
			if err := godotenv.Load("../../.env"); err != nil {
				logrus.Info("No .env file found, using environment variables")
			} else {
				logrus.Info("Loaded .env file from parent directory")
			}
		} else {
			logrus.Info("Loaded .env file from backend directory")
		}
	} else {
		logrus.Info("Loaded .env file from current directory")
	}

	// Configure logrus for structured logging
	logrus.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
	})
	
	// Set log level based on environment
	if os.Getenv("GIN_MODE") == "release" {
		logrus.SetLevel(logrus.InfoLevel)
	} else {
		logrus.SetLevel(logrus.DebugLevel)
	}
	
	// Log to stdout for Cloud Run
	logrus.SetOutput(os.Stdout)

	// Validate environment variables
	if err := utils.ValidateEnvironmentVariables(); err != nil {
		logrus.Fatalf("Environment validation failed: %v", err)
	}

	// Log configuration summary
	utils.LogConfigurationSummary()

	// Initialize database connection (temporarily disabled due to connection issues)
	// if err := database.InitDB(); err != nil {
	// 	logrus.Fatalf("Failed to connect to database: %v", err)
	// }
	logrus.Info("Database initialization skipped for testing")

	// Initialize SSO client (temporarily disabled for minimal startup)
	// ssoServiceURL := os.Getenv("SSO_SERVICE_URL")
	// if ssoServiceURL == "" {
	// 	ssoServiceURL = "http://localhost:9000"
	// }
	// ssoClient := ssoSDK.NewClient(ssoServiceURL)
	// logrus.Infof("SSO client initialized with URL: %s", ssoServiceURL)

	// Initialize Subscription Service client (temporarily disabled for minimal startup)
	// subscriptionServiceURL := os.Getenv("SUBSCRIPTION_SERVICE_URL")
	// if subscriptionServiceURL == "" {
	// 	subscriptionServiceURL = "http://localhost:8500"
	// }
	// subscriptionServiceAPIKey := os.Getenv("SUBSCRIPTION_SERVICE_API_KEY")
	// 
	// var subscriptionClient *subscriptionSDK.Client
	// if subscriptionServiceAPIKey != "" {
	// 	subscriptionClient = subscriptionSDK.NewClient(subscriptionServiceURL, subscriptionSDK.WithAPIKey(subscriptionServiceAPIKey))
	// } else {
	// 	subscriptionClient = subscriptionSDK.NewClient(subscriptionServiceURL)
	// }
	// logrus.Infof("Subscription service client initialized with URL: %s", subscriptionServiceURL)

	// Analytics service integration disabled - not part of core platform
	logrus.Info("Analytics service integration disabled")

	// Set up Gin router
	r := gin.Default()

	// Add correlation ID middleware first
	r.Use(middleware.CorrelationIDMiddleware())

	// Enable CORS with proper configuration for credentials
	r.Use(func(c *gin.Context) {
		// Get the origin from the request
		origin := c.Request.Header.Get("Origin")
		if origin == "" {
			// Default to all origins for development
			origin = "*"
		}

		// Set CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key, accept, origin, Cache-Control, X-Requested-With, Cookie, X-Correlation-ID")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, Set-Cookie, X-Correlation-ID")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Add global error handling middleware
	r.Use(func(c *gin.Context) {
		c.Next()
		
		// If we reach here and there are errors, handle them
		if len(c.Errors) > 0 {
			c.JSON(500, gin.H{
				"error": "Internal Server Error",
				"message": c.Errors.Last().Error(),
			})
			return
		}
	})

	// Handle 404 errors with JSON response
	r.NoRoute(func(c *gin.Context) {
		c.JSON(404, gin.H{
			"error": "Not Found",
			"message": "The requested resource was not found",
			"path": c.Request.URL.Path,
			"method": c.Request.Method,
		})
	})

	// Handle 405 Method Not Allowed errors with JSON response
	r.NoMethod(func(c *gin.Context) {
		c.JSON(405, gin.H{
			"error": "Method Not Allowed",
			"message": "Method " + c.Request.Method + " is not allowed for this endpoint",
			"path": c.Request.URL.Path,
			"allowed_methods": []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		})
	})

	// Register basic health route
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "healthy",
			"service": "ADC Credit Service",
			"timestamp": time.Now().UTC(),
		})
	})
	
	// TODO: Re-enable full routes after fixing compilation errors
	// routes.RegisterRoutes(r, ssoClient, subscriptionClient, nil)

	// Get port from environment variable or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8400"
	}

	// Start server
	logrus.Infof("Server starting on port %s...", port)
	if err := r.Run(":" + port); err != nil {
		logrus.Fatalf("Failed to start server: %v", err)
	}
}
