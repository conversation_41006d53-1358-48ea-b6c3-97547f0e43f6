package main

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"math/rand"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		logrus.Info("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		logrus.Fatalf("Failed to connect to database: %v", err)
	}

	// Get all users
	var users []struct {
		ID    uuid.UUID
		Email string
	}

	if err := database.DB.Table("users").Select("id, email").Scan(&users).Error; err != nil {
		logrus.Fatalf("Failed to get users: %v", err)
	}

	if len(users) == 0 {
		logrus.Fatalf("No users found in the database")
	}

	// Use the first user
	userID := users[0].ID
	logrus.Infof("Using user: %s (%s)", userID, users[0].Email)

	// Create test usage data
	if err := seedUsageData(userID); err != nil {
		logrus.Fatalf("Failed to seed usage data: %v", err)
	}

	logrus.Info("Usage data seeded successfully!")
}

func seedUsageData(userID uuid.UUID) error {
	// Delete existing usage data for this user's API keys
	if err := database.DB.Exec(`
		DELETE FROM usages 
		WHERE api_key_id IN (
			SELECT id FROM api_keys WHERE user_id = ?
		)
	`, userID).Error; err != nil {
		return fmt.Errorf("failed to delete existing usage data: %w", err)
	}

	logrus.Info("Deleted existing usage data for user:", userID)

	// Get API keys for the user
	var apiKeys []struct {
		ID uuid.UUID
	}
	if err := database.DB.Table("api_keys").Where("user_id = ?", userID).Select("id").Scan(&apiKeys).Error; err != nil {
		return fmt.Errorf("failed to get API keys: %w", err)
	}

	if len(apiKeys) == 0 {
		logrus.Info("No API keys found for user, skipping usage data creation")
		return nil
	}

	// Sample endpoints
	endpoints := []string{
		"/api/v1/data",
		"/api/v1/analyze",
		"/api/v1/search",
		"/api/v1/process",
		"/api/v1/generate",
	}

	// Sample methods
	methods := []string{"GET", "POST", "PUT", "DELETE"}

	// Sample IP addresses
	ipAddresses := []string{
		"***********",
		"********",
		"**********",
		"*******",
		"*******",
	}

	// Sample user agents
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
		"PostmanRuntime/7.28.0",
		"curl/7.64.1",
	}

	// Generate usage data for the last 30 days
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	// Create between 500-1000 usage records
	numRecords := rand.Intn(500) + 500
	logrus.Infof("Creating %d usage records", numRecords)

	for i := 0; i < numRecords; i++ {
		// Choose a random API key
		apiKeyID := apiKeys[rand.Intn(len(apiKeys))].ID

		// Choose a random endpoint and method
		endpoint := endpoints[rand.Intn(len(endpoints))]
		method := methods[rand.Intn(len(methods))]

		// Choose a random timestamp between start and end date
		randomDuration := time.Duration(rand.Int63n(int64(endDate.Sub(startDate))))
		timestamp := startDate.Add(randomDuration)

		// Generate random usage data
		credits := rand.Intn(10) + 1
		success := rand.Float32() > 0.1 // 90% success rate
		ipAddress := ipAddresses[rand.Intn(len(ipAddresses))]
		userAgent := userAgents[rand.Intn(len(userAgents))]
		responseTime := rand.Intn(500) + 50 // 50-550ms
		statusCode := 200
		if !success {
			statusCodes := []int{400, 401, 403, 404, 429, 500, 503}
			statusCode = statusCodes[rand.Intn(len(statusCodes))]
		}

		// Insert usage record
		if err := database.DB.Exec(`
			INSERT INTO usages (
				id, api_key_id, endpoint, method, credits, timestamp,
				success, ip_address, user_agent, response_time, status_code,
				created_at, updated_at
			)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, uuid.New(), apiKeyID, endpoint, method, credits, timestamp,
			success, ipAddress, userAgent, responseTime, statusCode,
			time.Now(), time.Now()).Error; err != nil {
			return fmt.Errorf("failed to create usage record: %w", err)
		}

		// Log progress every 100 records
		if i > 0 && i%100 == 0 {
			logrus.Infof("Created %d/%d usage records", i, numRecords)
		}
	}

	logrus.Infof("Created %d usage records for user: %s", numRecords, userID)
	return nil
}
