# Use the official Golang image to create a build artifact.
FROM golang:1.24-alpine AS builder

# Create and change to the app directory.
WORKDIR /app

# Copy go.mod and go.sum
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy local code to the container image.
COPY . ./

# Build the binary.
RUN CGO_ENABLED=0 GOOS=linux go build -mod=readonly -v -o server

# Use a Docker multi-stage build to create a lean production image.
FROM alpine:latest

# Install ca-certificates for HTTPS calls
RUN apk --no-cache add ca-certificates

WORKDIR /root/

# Copy the binary to the production image from the builder stage.
COPY --from=builder /app/server .

# Configure the container to run as an unprivileged user.
RUN adduser -D -s /bin/sh appuser
USER appuser

# Run the web service on container startup.
CMD ["./server"]