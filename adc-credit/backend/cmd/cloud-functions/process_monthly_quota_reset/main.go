package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/sirupsen/logrus"
	"net/http"
	"os"

	"github.com/GoogleCloudPlatform/functions-framework-go/functions"
	"github.com/cloudevents/sdk-go/v2/event"
)

func init() {
	functions.CloudEvent("ProcessMonthlyQuotaReset", processMonthlyQuotaReset)
}

// processMonthlyQuotaReset is triggered by Cloud Scheduler to reset monthly quotas
func processMonthlyQuotaReset(ctx context.Context, e event.Event) error {
	logrus.Infof("Monthly quota reset Cloud Function triggered at: %s", e.Time())

	// Get the API base URL and API key from environment variables
	apiBaseURL := os.Getenv("API_BASE_URL")
	if apiBaseURL == "" {
		apiBaseURL = "https://adc-credit-backend.run.app" // Default Cloud Run URL
	}

	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		return fmt.Errorf("API_KEY environment variable is required")
	}

	// Call the monthly quota reset endpoint
	endpoint := fmt.Sprintf("%s/api/v1/tasks/process-monthly-quota-reset", apiBaseURL)
	
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer([]byte("{}")))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set required headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	// Make the HTTP request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to call monthly quota reset endpoint: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("monthly quota reset endpoint returned status %d", resp.StatusCode)
	}

	// Parse response for logging
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err == nil {
		logrus.Infof("Monthly quota reset completed successfully: %+v", result)
	} else {
		logrus.Infof("Monthly quota reset completed successfully (could not parse response)")
	}

	return nil
}

func main() {
	// Use PORT environment variable, or default to 8080.
	port := "8080"
	if envPort := os.Getenv("PORT"); envPort != "" {
		port = envPort
	}

	if err := functions.Start(port); err != nil {
		logrus.Fatalf("functions.Start: %v\n", err)
	}
}
