# Monthly Quota Reset Cloud Function

This Cloud Function automatically resets monthly usage quotas for all users in the ADC Credit system.

## Overview

The function is triggered monthly by Cloud Scheduler and calls the backend API to reset:
- QR code generation counts
- API call usage counts 
- Credit consumption tracking

## Deployment

### 1. Deploy the Cloud Function

```bash
# From this directory
gcloud functions deploy process-monthly-quota-reset \
  --runtime go124 \
  --trigger-topic monthly-quota-reset-topic \
  --entry-point ProcessMonthlyQuotaReset \
  --set-env-vars API_BASE_URL=https://your-backend-url.run.app,API_KEY=your-api-key \
  --memory 256MB \
  --timeout 300s
```

### 2. Create Cloud Scheduler Job

```bash
# Create a pub/sub topic
gcloud pubsub topics create monthly-quota-reset-topic

# Create the scheduler job (runs on 1st of every month at midnight UTC)
gcloud scheduler jobs create pubsub monthly-quota-reset-job \
  --schedule="0 0 1 * *" \
  --topic=monthly-quota-reset-topic \
  --message-body='{"action":"reset_monthly_quotas"}' \
  --time-zone="UTC"
```

### 3. Environment Variables

Set these environment variables when deploying:

- `API_BASE_URL`: Your backend API base URL (e.g., https://adc-credit-backend.run.app)
- `API_KEY`: API key for authenticating with the backend

### 4. Manual Trigger

You can manually trigger the reset for testing:

```bash
# Manually trigger the scheduler job
gcloud scheduler jobs run monthly-quota-reset-job

# Or directly publish to the topic
gcloud pubsub topics publish monthly-quota-reset-topic --message='{"action":"reset_monthly_quotas"}'
```

## Monitoring

Check the function logs:
```bash
gcloud functions logs read process-monthly-quota-reset
```

Check scheduler job status:
```bash
gcloud scheduler jobs describe monthly-quota-reset-job
```

## Testing Locally

```bash
# Set environment variables
export API_BASE_URL=http://localhost:8400
export API_KEY=your-local-api-key

# Run the function
go run main.go
```