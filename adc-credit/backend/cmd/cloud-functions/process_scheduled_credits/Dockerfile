FROM golang:1.24-alpine as builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the function
RUN CGO_ENABLED=0 GOOS=linux go build -v -o process_scheduled_credits ./cmd/cloud-functions/process_scheduled_credits

# Use a minimal alpine image for the final container
FROM alpine:latest

# Install CA certificates for HTTPS
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Copy the binary from the builder stage
COPY --from=builder /app/process_scheduled_credits .

# Copy .env file if it exists (for local testing)
COPY --from=builder /app/.env* ./

# Expose the port
EXPOSE 8080

# Run the function
CMD ["./process_scheduled_credits"]
