package main

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		logrus.Info("No .env file found, using environment variables")
	}

	// Initialize database connection
	if err := database.InitDB(); err != nil {
		logrus.Fatalf("Failed to connect to database: %v", err)
	}

	// Seed test data
	if err := seedTestData(); err != nil {
		logrus.Fatalf("Failed to seed test data: %v", err)
	}

	logrus.Info("Test data seeded successfully!")
}

func seedTestData() error {
	// Create a test user if it doesn't exist
	var testUser models.User
	if err := database.DB.Where("email = ?", "<EMAIL>").First(&testUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create a new user with a unique ID
			userID := uuid.New()

			// Execute raw SQL to insert the user with explicit NULL for google_id
			result := database.DB.Exec(`
				INSERT INTO users (id, email, name, password, role, google_id, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, NULL, ?, ?)
			`, userID, "<EMAIL>", "Test User",
				"$2a$10$yjH6RgJCHjvjI5Cv.1JTwOKKD.YmPRVuKL9Dbu5RDOTtCZ7IYJvSe", // "password"
				"user", time.Now(), time.Now())

			if result.Error != nil {
				return fmt.Errorf("failed to create test user: %w", result.Error)
			}

			// Fetch the newly created user
			if err := database.DB.Where("id = ?", userID).First(&testUser).Error; err != nil {
				return fmt.Errorf("failed to fetch created user: %w", err)
			}

			logrus.Info("Created test user:", testUser.ID)
		} else {
			return fmt.Errorf("failed to check for test user: %w", err)
		}
	} else {
		logrus.Info("Using existing test user:", testUser.ID)
	}

	// Ensure subscription tiers exist
	if err := ensureSubscriptionTiers(); err != nil {
		return fmt.Errorf("failed to ensure subscription tiers: %w", err)
	}

	// Create a subscription for the test user if it doesn't exist
	var subscription models.Subscription
	if err := database.DB.Where("user_id = ?", testUser.ID).First(&subscription).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Get the Pro tier
			var proTier models.SubscriptionTier
			if err := database.DB.Where("name = ?", "Pro").First(&proTier).Error; err != nil {
				return fmt.Errorf("failed to find Pro tier: %w", err)
			}

			subscription = models.Subscription{
				ID:                 uuid.New(),
				UserID:             testUser.ID,
				SubscriptionTierID: proTier.ID,
				StartDate:          time.Now(),
				AutoRenew:          true,
				Status:             "active",
				CreditBalance:      proTier.CreditLimit,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			}
			if err := database.DB.Create(&subscription).Error; err != nil {
				return fmt.Errorf("failed to create subscription: %w", err)
			}
			logrus.Info("Created subscription for test user:", subscription.ID)
		} else {
			return fmt.Errorf("failed to check for subscription: %w", err)
		}
	} else {
		logrus.Info("Using existing subscription:", subscription.ID)
	}

	// Create test webhooks
	return seedWebhooks(testUser.ID)
}

func seedWebhooks(userID uuid.UUID) error {
	// Delete existing webhooks for this user
	if err := database.DB.Where("user_id = ?", userID).Delete(&models.Webhook{}).Error; err != nil {
		return fmt.Errorf("failed to delete existing webhooks: %w", err)
	}
	logrus.Info("Deleted existing webhooks for user:", userID)

	// Create test webhooks using raw SQL to avoid type issues
	webhook1ID := uuid.New()
	webhook2ID := uuid.New()
	webhook3ID := uuid.New()

	// Create Credit Notifications webhook
	result1 := database.DB.Exec(`
		INSERT INTO webhooks
		(id, user_id, name, url, secret, events, active, fail_count, created_at, updated_at)
		VALUES
		(?, ?, 'Credit Notifications', 'https://webhook.site/12345678-1234-1234-1234-123456789012', 'secret123', ARRAY['credit.consumed', 'credit.added'], true, 0, NOW(), NOW())
	`, webhook1ID, userID)

	if result1.Error != nil {
		return fmt.Errorf("failed to create Credit Notifications webhook: %w", result1.Error)
	}
	logrus.Infof("Created webhook: Credit Notifications (ID: %s)", webhook1ID)

	// Create API Key Events webhook
	result2 := database.DB.Exec(`
		INSERT INTO webhooks
		(id, user_id, name, url, secret, events, active, fail_count, created_at, updated_at)
		VALUES
		(?, ?, 'API Key Events', 'https://webhook.site/*************-4321-4321-************', 'apisecret456', ARRAY['api_key.created', 'api_key.updated', 'api_key.deleted'], true, 0, NOW(), NOW())
	`, webhook2ID, userID)

	if result2.Error != nil {
		return fmt.Errorf("failed to create API Key Events webhook: %w", result2.Error)
	}
	logrus.Infof("Created webhook: API Key Events (ID: %s)", webhook2ID)

	// Create Subscription Events webhook
	result3 := database.DB.Exec(`
		INSERT INTO webhooks
		(id, user_id, name, url, secret, events, active, fail_count, created_at, updated_at)
		VALUES
		(?, ?, 'Subscription Events', 'https://webhook.site/abcdef12-3456-7890-abcd-ef1234567890', 'subsecret789', ARRAY['subscription.created', 'subscription.updated', 'subscription.cancelled'], false, 2, NOW(), NOW())
	`, webhook3ID, userID)

	if result3.Error != nil {
		return fmt.Errorf("failed to create Subscription Events webhook: %w", result3.Error)
	}
	logrus.Infof("Created webhook: Subscription Events (ID: %s)", webhook3ID)

	// Create webhook deliveries for each webhook
	if err := seedWebhookDeliveries(webhook1ID); err != nil {
		return err
	}

	if err := seedWebhookDeliveries(webhook2ID); err != nil {
		return err
	}

	if err := seedWebhookDeliveries(webhook3ID); err != nil {
		return err
	}

	return nil
}

// ensureSubscriptionTiers makes sure the subscription tiers exist in the database
func ensureSubscriptionTiers() error {
	var count int64
	database.DB.Model(&models.SubscriptionTier{}).Count(&count)
	if count > 0 {
		logrus.Info("Subscription tiers already exist")
		return nil
	}

	// Create subscription tiers using raw SQL to avoid type issues
	result := database.DB.Exec(`
		INSERT INTO subscription_tiers
		(name, description, price, credit_limit, features, default_rate_limit_max, default_rate_limit_rate, max_webhooks, advanced_analytics, created_at, updated_at)
		VALUES
		('Free', 'Basic tier with limited credits', 0, 1000, ARRAY['1,000 credits per month', 'Basic API access', 'Standard support', '1 webhook', 'Basic rate limiting'], 10, 0.16666666666666666, 1, false, NOW(), NOW()),
		('Pro', 'Professional tier with more credits', 29.99, 10000, ARRAY['10,000 credits per month', 'Full API access', 'Priority support', 'Detailed analytics', '5 webhooks', 'Enhanced rate limiting'], 60, 1, 5, true, NOW(), NOW()),
		('Enterprise', 'Enterprise tier with unlimited credits', 99.99, 50000, ARRAY['50,000 credits per month', 'Full API access', '24/7 support', 'Advanced analytics', 'Custom integrations', '20 webhooks', 'Custom rate limiting'], 600, 10, 20, true, NOW(), NOW())
	`)

	if result.Error != nil {
		return fmt.Errorf("failed to create subscription tiers: %w", result.Error)
	}

	logrus.Info("Created subscription tiers")
	return nil
}

func seedWebhookDeliveries(webhookID uuid.UUID) error {
	// Create test webhook deliveries
	deliveries := []models.WebhookDelivery{
		{
			ID:         uuid.New(),
			WebhookID:  webhookID,
			Event:      "credit.consumed",
			Payload:    `{"id":"12345","event":"credit.consumed","created_at":"2025-05-18T12:00:00Z","data":{"user_id":"user123","credits":10,"endpoint":"/api/v1/data"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   150,
			CreatedAt:  time.Now().Add(-48 * time.Hour),
			UpdatedAt:  time.Now().Add(-48 * time.Hour),
		},
		{
			ID:         uuid.New(),
			WebhookID:  webhookID,
			Event:      "credit.added",
			Payload:    `{"id":"67890","event":"credit.added","created_at":"2025-05-18T14:30:00Z","data":{"user_id":"user123","credits":100,"source":"purchase"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   120,
			CreatedAt:  time.Now().Add(-24 * time.Hour),
			UpdatedAt:  time.Now().Add(-24 * time.Hour),
		},
		{
			ID:         uuid.New(),
			WebhookID:  webhookID,
			Event:      "api_key.created",
			Payload:    `{"id":"abcde","event":"api_key.created","created_at":"2025-05-18T16:45:00Z","data":{"user_id":"user123","api_key_id":"key123","name":"New API Key"}}`,
			StatusCode: 500,
			Response:   `{"error":"Internal server error"}`,
			Success:    false,
			Duration:   350,
			CreatedAt:  time.Now().Add(-12 * time.Hour),
			UpdatedAt:  time.Now().Add(-12 * time.Hour),
		},
		{
			ID:         uuid.New(),
			WebhookID:  webhookID,
			Event:      "subscription.updated",
			Payload:    `{"id":"fghij","event":"subscription.updated","created_at":"2025-05-18T18:15:00Z","data":{"user_id":"user123","subscription_id":"sub123","tier":"pro"}}`,
			StatusCode: 404,
			Response:   `{"error":"Not found"}`,
			Success:    false,
			Duration:   200,
			CreatedAt:  time.Now().Add(-6 * time.Hour),
			UpdatedAt:  time.Now().Add(-6 * time.Hour),
		},
		{
			ID:         uuid.New(),
			WebhookID:  webhookID,
			Event:      "credit.consumed",
			Payload:    `{"id":"klmno","event":"credit.consumed","created_at":"2025-05-18T20:00:00Z","data":{"user_id":"user123","credits":5,"endpoint":"/api/v1/analyze"}}`,
			StatusCode: 200,
			Response:   `{"status":"ok"}`,
			Success:    true,
			Duration:   180,
			CreatedAt:  time.Now().Add(-1 * time.Hour),
			UpdatedAt:  time.Now().Add(-1 * time.Hour),
		},
	}

	for _, delivery := range deliveries {
		if err := database.DB.Create(&delivery).Error; err != nil {
			return fmt.Errorf("failed to create webhook delivery: %w", err)
		}
	}

	logrus.Infof("Created %d webhook deliveries for webhook ID: %s", len(deliveries), webhookID)
	return nil
}
