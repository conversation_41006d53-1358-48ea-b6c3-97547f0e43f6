package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize Gin
	r := gin.Default()

	// CORS
	r.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c<PERSON><PERSON><PERSON>("Access-Control-Allow-Headers", "*")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.J<PERSON>(200, gin.H{"status": "ok", "message": "Credit Service Backend is running"})
	})

	// Public subscription tiers endpoint for frontend
	r.GET("/api/v1/public/subscriptions/tiers", func(c *gin.Context) {
		tiers := []map[string]interface{}{
			{
				"id":                      1,
				"name":                    "Free",
				"description":             "Perfect for individuals and small projects",
				"price":                   0,
				"credit_limit":            1000,
				"features": []string{
					"1,000 credits per month",
					"Basic analytics",
					"Email support",
				},
				"default_rate_limit_max":  10,
				"default_rate_limit_rate": 0.166,
				"max_webhooks":            1,
				"advanced_analytics":      false,
				"created_at":              "2024-01-01T00:00:00Z",
				"updated_at":              "2024-01-01T00:00:00Z",
			},
			{
				"id":                      2,
				"name":                    "Pro",
				"description":             "Perfect for growing businesses",
				"price":                   29,
				"credit_limit":            10000,
				"features": []string{
					"10,000 credits per month",
					"Advanced analytics",
					"Priority support",
					"Multiple shops",
				},
				"default_rate_limit_max":  60,
				"default_rate_limit_rate": 1.0,
				"max_webhooks":            5,
				"advanced_analytics":      true,
				"created_at":              "2024-01-01T00:00:00Z",
				"updated_at":              "2024-01-01T00:00:00Z",
			},
			{
				"id":                      3,
				"name":                    "Enterprise",
				"description":             "For large organizations with custom needs",
				"price":                   99,
				"credit_limit":            50000,
				"features": []string{
					"50,000 credits per month",
					"Advanced analytics",
					"24/7 support",
					"Unlimited shops",
					"Custom integrations",
				},
				"default_rate_limit_max":  600,
				"default_rate_limit_rate": 10.0,
				"max_webhooks":            20,
				"advanced_analytics":      true,
				"created_at":              "2024-01-01T00:00:00Z",
				"updated_at":              "2024-01-01T00:00:00Z",
			},
		}
		c.JSON(200, gin.H{"data": tiers})
	})

	// Get port from environment
	port := os.Getenv("PORT")
	if port == "" {
		port = "8400"
	}

	log.Printf("Simple Credit Service Backend starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}