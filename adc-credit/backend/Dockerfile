# Stage 1: Build the Go application
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -v -o api-server ./cmd/api/main.go

# Stage 2: Create a minimal runtime image
FROM alpine:latest

# Install CA certificates for HTTPS
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Copy the binary from the builder stage
COPY --from=builder /app/api-server .

# Create an empty .env file (will be overridden by environment variables)
RUN touch .env

# Set environment variables
ENV GIN_MODE=release

# Expose the port (Cloud Run will override the PORT env var)
EXPOSE 8080

# Run the application
CMD ["./api-server"]
