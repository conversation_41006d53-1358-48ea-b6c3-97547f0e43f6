package main

import (
	"fmt"
	"os"
	"os/exec"
)

// ADC Integration Test Runner
// This script provides a convenient way to run all ADC integration tests
// with proper environment setup and service availability checks.

func main() {
	fmt.Println("🧪 ADC Platform Integration Test Runner")
	fmt.Println("========================================")

	// Check if short mode is requested
	shortMode := false
	for _, arg := range os.Args[1:] {
		if arg == "-short" || arg == "--short" {
			shortMode = true
			break
		}
	}

	if shortMode {
		fmt.Println("📝 Running in SHORT mode - skipping integration tests")
		os.Exit(0)
	}

	// Set default environment variables if not provided
	setDefaultEnvVars()

	// Check ADC service availability
	fmt.Println("\n🔍 Checking ADC Service Availability...")
	checkServiceAvailability()

	// Run test suites
	fmt.Println("\n🚀 Running ADC Integration Test Suites...")
	
	testSuites := []TestSuite{
		{
			Name:        "ADC Service Client Integration",
			Package:     "./tests/adc_integration",
			TestPattern: "TestADCClientIntegration",
			Critical:    true,
		},
		{
			Name:        "Service Container Integration", 
			Package:     "./tests/adc_integration",
			TestPattern: "TestServiceContainerIntegration",
			Critical:    true,
		},
		{
			Name:        "Credit V2 Handler Integration",
			Package:     "./tests/handlers_v2",
			TestPattern: "TestCreditV2HandlerIntegration",
			Critical:    false,
		},
		{
			Name:        "ADC Sync Handler Integration",
			Package:     "./tests/handlers_v2", 
			TestPattern: "TestADCSyncHandlerIntegration",
			Critical:    false,
		},
		{
			Name:        "Webhook V2 Integration",
			Package:     "./tests/handlers_v2",
			TestPattern: "TestWebhookV2Integration",
			Critical:    false,
		},
		{
			Name:        "Shop Organization Service Integration",
			Package:     "./tests/services",
			TestPattern: "TestShopOrganizationServiceIntegration",
			Critical:    false,
		},
		{
			Name:        "Subscription Sync Service Integration",
			Package:     "./tests/services",
			TestPattern: "TestSubscriptionSyncServiceIntegration",
			Critical:    false,
		},
		{
			Name:        "Usage Reporting Service Integration",
			Package:     "./tests/services",
			TestPattern: "TestUsageReportingServiceIntegration",
			Critical:    false,
		},
		{
			Name:        "Complete Shop Lifecycle E2E",
			Package:     "./tests/e2e",
			TestPattern: "TestCompleteShopLifecycleIntegration",
			Critical:    false,
		},
		{
			Name:        "Multi-Service Integration E2E",
			Package:     "./tests/e2e",
			TestPattern: "TestMultiServiceIntegration",
			Critical:    false,
		},
		{
			Name:        "Data Consistency E2E",
			Package:     "./tests/e2e",
			TestPattern: "TestDataConsistencyIntegration",
			Critical:    false,
		},
	}

	var failedSuites []string
	totalSuites := len(testSuites)
	passedSuites := 0

	for i, suite := range testSuites {
		fmt.Printf("\n📋 [%d/%d] Running: %s\n", i+1, totalSuites, suite.Name)
		fmt.Printf("    Package: %s\n", suite.Package)
		fmt.Printf("    Pattern: %s\n", suite.TestPattern)

		success := runTestSuite(suite)
		if success {
			fmt.Printf("✅ PASSED: %s\n", suite.Name)
			passedSuites++
		} else {
			fmt.Printf("❌ FAILED: %s\n", suite.Name)
			failedSuites = append(failedSuites, suite.Name)
			
			if suite.Critical {
				fmt.Printf("🚨 CRITICAL TEST FAILED: %s\n", suite.Name)
				fmt.Println("   This indicates a fundamental integration issue.")
			}
		}
	}

	// Print summary
	fmt.Println("\n📊 Test Summary")
	fmt.Println("================")
	fmt.Printf("Total Suites: %d\n", totalSuites)
	fmt.Printf("Passed: %d\n", passedSuites)
	fmt.Printf("Failed: %d\n", len(failedSuites))

	if len(failedSuites) > 0 {
		fmt.Println("\n❌ Failed Test Suites:")
		for _, suite := range failedSuites {
			fmt.Printf("   - %s\n", suite)
		}
		
		fmt.Println("\n🔧 Troubleshooting Tips:")
		fmt.Println("   1. Ensure ADC services are running:")
		fmt.Println("      - SSO Service: http://localhost:9000/health")
		fmt.Println("      - Subscription Service: http://localhost:9100/health")
		fmt.Println("      - Analytics Service: http://localhost:9200/health")
		fmt.Println("   2. Check database connectivity")
		fmt.Println("   3. Verify environment variables")
		fmt.Println("   4. Review test logs for specific errors")
		
		os.Exit(1)
	} else {
		fmt.Println("\n🎉 All ADC Integration Tests Passed!")
		fmt.Println("   The ADC platform integration is working correctly.")
	}
}

type TestSuite struct {
	Name        string
	Package     string
	TestPattern string
	Critical    bool // Whether failure should cause immediate exit
}

func setDefaultEnvVars() {
	envVars := map[string]string{
		"SSO_SERVICE_URL":          "http://localhost:9000",
		"SUBSCRIPTION_SERVICE_URL": "http://localhost:9100", 
		"ANALYTICS_SERVICE_URL":    "http://localhost:9200",
		"GIN_MODE":                "test",
	}
	
	// Preserve existing DATABASE_URL if set
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		envVars["DATABASE_URL"] = dbURL
	}

	fmt.Println("🔧 Setting default environment variables...")
	for key, defaultValue := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, defaultValue)
			fmt.Printf("   %s = %s\n", key, defaultValue)
		} else {
			fmt.Printf("   %s = %s (existing)\n", key, os.Getenv(key))
		}
	}
}

func checkServiceAvailability() {
	services := map[string]string{
		"SSO Service":          os.Getenv("SSO_SERVICE_URL") + "/health",
		"Subscription Service": os.Getenv("SUBSCRIPTION_SERVICE_URL") + "/health",
		"Analytics Service":    os.Getenv("ANALYTICS_SERVICE_URL") + "/health",
	}

	for name, url := range services {
		fmt.Printf("   Checking %s... ", name)
		
		cmd := exec.Command("curl", "-s", "-f", "--max-time", "3", url)
		err := cmd.Run()
		
		if err != nil {
			fmt.Printf("❌ UNAVAILABLE (%s)\n", url)
		} else {
			fmt.Printf("✅ AVAILABLE (%s)\n", url)
		}
	}
	
	fmt.Println("   Note: Tests will adapt based on service availability")
}

func runTestSuite(suite TestSuite) bool {
	// Build the go test command
	args := []string{"test", suite.Package, "-run", suite.TestPattern, "-v"}
	
	// Add timeout to prevent hanging tests
	args = append(args, "-timeout", "5m")
	
	cmd := exec.Command("go", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	
	// Set environment variables for the test
	cmd.Env = os.Environ()
	
	err := cmd.Run()
	return err == nil
}

// Additional utility functions for running specific test categories

func runAllTests() {
	fmt.Println("Running all ADC integration tests...")
	cmd := exec.Command("go", "test", "./tests/adc_integration", "./tests/handlers_v2", "-v", "-timeout", "10m")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Env = os.Environ()
	cmd.Run()
}

func runCriticalTests() {
	fmt.Println("Running critical ADC integration tests...")
	cmd := exec.Command("go", "test", "./tests/adc_integration", "-v", "-timeout", "5m")
	cmd.Stdout = os.Stdout  
	cmd.Stderr = os.Stderr
	cmd.Env = os.Environ()
	cmd.Run()
}

func runWithCoverage() {
	fmt.Println("Running ADC integration tests with coverage...")
	cmd := exec.Command("go", "test", "./tests/adc_integration", "./tests/handlers_v2", "-v", "-coverprofile=adc_integration_coverage.out", "-timeout", "10m")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Env = os.Environ()
	err := cmd.Run()
	
	if err == nil {
		fmt.Println("Generating coverage report...")
		coverCmd := exec.Command("go", "tool", "cover", "-html=adc_integration_coverage.out", "-o", "adc_integration_coverage.html")
		coverCmd.Run()
		fmt.Println("Coverage report saved to: adc_integration_coverage.html")
	}
}