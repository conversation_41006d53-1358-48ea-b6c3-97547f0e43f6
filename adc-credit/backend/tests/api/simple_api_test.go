package api

import (
	"net/http"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type SimpleAPITestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *SimpleAPITestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup a simple test router
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Add simple test routes
	api := suite.router.Group("/api/v1")
	{
		api.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":  "ok",
				"message": "API is healthy",
			})
		})

		api.GET("/test-auth", func(c *gin.Context) {
			// Simple auth check - look for Authorization header
			auth := c.<PERSON>("Authorization")
			if auth == "" {
				c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Authenticated successfully",
				"auth":    auth,
			})
		})

		api.POST("/test-data", func(c *gin.Context) {
			var data map[string]interface{}
			if err := c.ShouldBindJSON(&data); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusCreated, gin.H{
				"message": "Data received",
				"data":    data,
			})
		})
	}
}

func (suite *SimpleAPITestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *SimpleAPITestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *SimpleAPITestSuite) TestHealthEndpoint() {
	t := suite.T()

	// Test health endpoint
	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/health",
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"status":  "ok",
		"message": "API is healthy",
	})
}

func (suite *SimpleAPITestSuite) TestAuthenticationRequired() {
	t := suite.T()

	// Test without authentication
	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/test-auth",
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 401 Unauthorized
	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *SimpleAPITestSuite) TestAuthenticationSuccess() {
	t := suite.T()

	// Create test user and get auth token
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with authentication
	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/test-auth",
		Headers: map[string]string{
			"Authorization": "Bearer " + token,
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 200 OK
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)

	// Check response contains auth info
	assert.Contains(t, resp.Body, "message")
	assert.Contains(t, resp.Body, "auth")
}

func (suite *SimpleAPITestSuite) TestPostData() {
	t := suite.T()

	// Test posting JSON data
	testData := map[string]interface{}{
		"name":   "Test Item",
		"value":  42,
		"active": true,
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/test-data",
		Body:   testData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)

	// Check response structure
	assert.Contains(t, resp.Body, "message")
	assert.Contains(t, resp.Body, "data")

	// Verify the data was echoed back correctly
	data := resp.Body["data"].(map[string]interface{})
	assert.Equal(t, "Test Item", data["name"])
	assert.Equal(t, float64(42), data["value"]) // JSON numbers are float64
	assert.Equal(t, true, data["active"])
}

func (suite *SimpleAPITestSuite) TestPostInvalidData() {
	t := suite.T()

	// Test posting invalid JSON
	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/test-data",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}

	// Manually create request with invalid JSON
	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request for empty body
	assert.Equal(t, 400, resp.StatusCode)
}

func (suite *SimpleAPITestSuite) TestHelperFunctions() {
	t := suite.T()

	// Test UUID validation helper
	testutils.AssertValidUUID(t, "123e4567-e89b-12d3-a456-************")

	// Test timestamp validation helper
	testutils.AssertValidTimestamp(t, "2023-01-01T12:00:00Z")

	// Test response helpers
	successResp := &testutils.TestResponse{
		StatusCode: 200,
		Body: map[string]interface{}{
			"status": "success",
		},
	}
	testutils.AssertSuccessResponse(t, successResp)

	errorResp := &testutils.TestResponse{
		StatusCode: 400,
		Body: map[string]interface{}{
			"error": "Bad request",
		},
	}
	testutils.AssertErrorResponse(t, errorResp, 400)
}

func (suite *SimpleAPITestSuite) TestDatabaseIntegration() {
	t := suite.T()

	// Test creating and retrieving test data
	user, err := testutils.CreateTestUser("<EMAIL>", "DB Test User")
	suite.Require().NoError(err)
	defer testutils.CleanupTestUser(t, user.ID)

	// Verify user was created
	assert.NotEmpty(t, user.ID)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "DB Test User", user.Name)

	// Test creating subscription tier
	tier, err := testutils.CreateTestSubscriptionTier("Test Tier", 1000)
	suite.Require().NoError(err)
	assert.Equal(t, "Test Tier", tier.Name)
	assert.Equal(t, 1000, tier.CreditLimit)

	// Test creating subscription
	subscription, err := testutils.CreateTestSubscription(user.ID, tier.ID)
	suite.Require().NoError(err)
	assert.Equal(t, user.ID, subscription.UserID)
	assert.Equal(t, tier.ID, subscription.SubscriptionTierID)
}

// Run the test suite
func TestSimpleAPITestSuite(t *testing.T) {
	suite.Run(t, new(SimpleAPITestSuite))
}
