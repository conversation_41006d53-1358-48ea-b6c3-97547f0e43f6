package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/handlers"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type CreditsTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *CreditsTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with credit routes
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	// Setup credit routes
	creditHandler := handlers.NewCreditHandler(testutils.TestDB)
	api := suite.router.Group("/api/v1")
	{
		credits := api.Group("/credits")
		{
			credits.GET("/balance", creditHandler.GetCreditBalance)
			credits.POST("/consume", creditHandler.ConsumeCredits)
			credits.POST("/add", creditHandler.AddCredits)
			credits.GET("/history", creditHandler.GetCreditHistory)
		}
		
		// External API endpoint for credit consumption
		external := api.Group("/external")
		{
			external.POST("/consume", creditHandler.ExternalConsumeCredits)
		}
	}
}

func (suite *CreditsTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *CreditsTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *CreditsTestSuite) TestGetCreditBalance() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting credit balance
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/credits/balance",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"credit_balance": float64(subscription.CreditBalance),
		"subscription_type": subscription.SubscriptionType,
	})
	
	assert.Contains(t, resp.Body, "subscription_tier")
}

func (suite *CreditsTestSuite) TestGetCreditBalanceNoSubscription() {
	t := suite.T()

	// Create test user without subscription
	user, token := testutils.CreateTestUserWithAuth(t, "<EMAIL>", "Test User")
	defer testutils.CleanupTestUser(t, user.ID)

	// Test getting credit balance
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/credits/balance",
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return error or zero balance
	assert.True(t, resp.StatusCode == 200 || resp.StatusCode == 404)
	
	if resp.StatusCode == 200 {
		// Should show zero balance
		testutils.AssertJSONResponse(t, resp, map[string]interface{}{
			"credit_balance": float64(0),
		})
	}
}

func (suite *CreditsTestSuite) TestConsumeCredits() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test consuming credits
	consumeData := map[string]interface{}{
		"credits":     10,
		"description": "Test credit consumption",
		"endpoint":    "/api/test",
		"method":      "GET",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/credits/consume",
		Body:   consumeData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	expectedBalance := subscription.CreditBalance - 10
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"credit_balance": float64(expectedBalance),
		"credits_consumed": float64(10),
	})
}

func (suite *CreditsTestSuite) TestConsumeCreditsInsufficientBalance() {
	t := suite.T()

	// Create test user with low credit subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 5)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test consuming more credits than available
	consumeData := map[string]interface{}{
		"credits":     10,
		"description": "Test insufficient credits",
		"endpoint":    "/api/test",
		"method":      "GET",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/credits/consume",
		Body:   consumeData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 402 Payment Required or 400 Bad Request
	assert.True(t, resp.StatusCode == 402 || resp.StatusCode == 400)
	testutils.AssertErrorResponse(t, resp, resp.StatusCode)
}

func (suite *CreditsTestSuite) TestAddCredits() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test adding credits
	addData := map[string]interface{}{
		"credits":     50,
		"description": "Test credit addition",
		"reason":      "bonus",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/credits/add",
		Body:   addData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	expectedBalance := subscription.CreditBalance + 50
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"credit_balance": float64(expectedBalance),
		"credits_added": float64(50),
	})
}

func (suite *CreditsTestSuite) TestGetCreditHistory() {
	t := suite.T()

	// Create test user with subscription
	user, subscription, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Create some credit transactions first
	consumeData := map[string]interface{}{
		"credits":     10,
		"description": "Test consumption",
		"endpoint":    "/api/test",
		"method":      "GET",
	}

	// Make a consumption request to create history
	consumeReq := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/credits/consume",
		Body:   consumeData,
	}, user.ID, user.Email)
	testutils.MakeRequest(t, suite.router, consumeReq)

	// Test getting credit history
	req := testutils.WithAuth(testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/credits/history",
		QueryParams: map[string]string{
			"limit": "10",
			"page":  "1",
		},
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check pagination structure
	testutils.AssertPaginatedResponse(t, resp)
	testutils.AssertArrayResponse(t, resp, 1) // At least 1 transaction
}

func (suite *CreditsTestSuite) TestExternalConsumeCredits() {
	t := suite.T()

	// Create test user with API key and subscription
	user, apiKey, token := testutils.CreateTestUserWithAPIKey(t, "<EMAIL>", "Test User")
	_, subscription, _ := testutils.CreateTestUserWithSubscription(t, user.Email, user.Name, 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test external credit consumption with API key
	consumeData := map[string]interface{}{
		"credits":     5,
		"endpoint":    "/api/external/test",
		"method":      "POST",
		"ip_address":  "***********",
		"user_agent":  "Test Agent",
	}

	req := testutils.WithAPIKey(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/consume",
		Body:   consumeData,
	}, apiKey.Key)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Assertions
	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	
	// Check response structure
	expectedBalance := subscription.CreditBalance - 5
	testutils.AssertJSONResponse(t, resp, map[string]interface{}{
		"credit_balance": float64(expectedBalance),
		"credits_consumed": float64(5),
	})
}

func (suite *CreditsTestSuite) TestExternalConsumeCreditsInvalidAPIKey() {
	t := suite.T()

	// Test with invalid API key
	consumeData := map[string]interface{}{
		"credits":  5,
		"endpoint": "/api/test",
		"method":   "GET",
	}

	req := testutils.WithAPIKey(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/external/consume",
		Body:   consumeData,
	}, "invalid-api-key")

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 401 Unauthorized
	testutils.AssertErrorResponse(t, resp, 401)
}

func (suite *CreditsTestSuite) TestConsumeCreditsInvalidData() {
	t := suite.T()

	// Create test user with subscription
	user, _, token := testutils.CreateTestUserWithSubscription(t, "<EMAIL>", "Test User", 1000)
	defer testutils.CleanupTestUser(t, user.ID)

	// Test with negative credits
	consumeData := map[string]interface{}{
		"credits":     -10,
		"description": "Invalid negative credits",
		"endpoint":    "/api/test",
		"method":      "GET",
	}

	req := testutils.WithAuth(testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/credits/consume",
		Body:   consumeData,
	}, user.ID, user.Email)

	resp := testutils.MakeRequest(t, suite.router, req)

	// Should return 400 Bad Request
	testutils.AssertErrorResponse(t, resp, 400)
}

// Run the test suite
func TestCreditsTestSuite(t *testing.T) {
	suite.Run(t, new(CreditsTestSuite))
}
