package api

import (
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/models"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ShopBasicTestSuite tests basic shop functionality
type ShopBasicTestSuite struct {
	suite.Suite
	router   *gin.Engine
	testUser *models.User
	testShop *models.Shop
}

// SetupSuite runs once before all tests
func (suite *ShopBasicTestSuite) SetupSuite() {
	// Set up test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Set up basic router
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Set the database connection for handlers
	database.DB = testutils.TestDB
}

// SetupTest runs before each test
func (suite *ShopBasicTestSuite) SetupTest() {
	// Clean test data
	testutils.CleanTestData()

	// Create test user
	suite.testUser = &models.User{
		ID:    uuid.New(),
		Email: "<EMAIL>",
		Name:  "Test User",
		Role:  "user",
	}
	require.NoError(suite.T(), testutils.TestDB.Create(suite.testUser).Error)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:           uuid.New(),
		OwnerUserID:  suite.testUser.ID,
		Name:         "Test Shop",
		Description:  "A test shop",
		ContactEmail: "<EMAIL>",
		ShopType:     "retail",
		Slug:         "test-shop-" + uuid.New().String()[:8],
	}
	require.NoError(suite.T(), testutils.TestDB.Create(suite.testShop).Error)
}

// TearDownTest runs after each test
func (suite *ShopBasicTestSuite) TearDownTest() {
	testutils.CleanTestData()
}

// Test that the GetShopStats handler can be called without panicking
func (suite *ShopBasicTestSuite) TestGetShopStatsHandler_Basic() {
	t := suite.T()

	// Create a mock Gin context
	c, _ := gin.CreateTestContext(nil)
	c.Set("user", *suite.testUser)
	c.Params = gin.Params{
		{Key: "id", Value: suite.testShop.ID.String()},
	}

	// This should not panic
	assert.NotPanics(t, func() {
		handlers.GetShopStats(c)
	})
}

// Test that shop API key handlers can be called without panicking
func (suite *ShopBasicTestSuite) TestShopAPIKeyHandlers_Basic() {
	t := suite.T()

	// Test GetShopAPIKeys
	c1, _ := gin.CreateTestContext(nil)
	c1.Set("user", *suite.testUser)
	c1.Params = gin.Params{
		{Key: "id", Value: suite.testShop.ID.String()},
	}

	assert.NotPanics(t, func() {
		handlers.GetShopAPIKeys(c1)
	})

	// Test CreateShopAPIKey
	c2, _ := gin.CreateTestContext(nil)
	c2.Set("user", *suite.testUser)
	c2.Params = gin.Params{
		{Key: "id", Value: suite.testShop.ID.String()},
	}

	assert.NotPanics(t, func() {
		handlers.CreateShopAPIKey(c2)
	})
}

// Test that shop branch API key handlers can be called without panicking
func (suite *ShopBasicTestSuite) TestShopBranchAPIKeyHandlers_Basic() {
	t := suite.T()

	// Create a test branch
	branch := &models.ShopBranch{
		ID:     uuid.New(),
		ShopID: suite.testShop.ID,
		Name:   "Test Branch",
	}
	require.NoError(t, testutils.TestDB.Create(branch).Error)

	// Test GetShopBranchAPIKeys
	c1, _ := gin.CreateTestContext(nil)
	c1.Set("user", *suite.testUser)
	c1.Params = gin.Params{
		{Key: "id", Value: branch.ID.String()},
	}

	assert.NotPanics(t, func() {
		handlers.GetShopBranchAPIKeys(c1)
	})

	// Test CreateShopBranchAPIKey
	c2, _ := gin.CreateTestContext(nil)
	c2.Set("user", *suite.testUser)
	c2.Params = gin.Params{
		{Key: "id", Value: branch.ID.String()},
	}

	assert.NotPanics(t, func() {
		handlers.CreateShopBranchAPIKey(c2)
	})
}

// Test database operations for shop statistics
func (suite *ShopBasicTestSuite) TestShopStatsDatabase_Basic() {
	t := suite.T()

	// Create test customers
	for i := 0; i < 3; i++ {
		customer := &models.ShopCustomer{
			ID:            uuid.New(),
			ShopID:        suite.testShop.ID,
			UserID:        suite.testUser.ID,
			CreditBalance: 100,
		}
		require.NoError(t, testutils.TestDB.Create(customer).Error)
	}

	// Create test credit codes
	for i := 0; i < 5; i++ {
		code := &models.CreditCode{
			ID:         uuid.New(),
			ShopID:     suite.testShop.ID,
			Code:       "TEST" + uuid.New().String()[:8],
			Amount:     50,
			IsRedeemed: i%2 == 0,
		}
		require.NoError(t, testutils.TestDB.Create(code).Error)
	}

	// Test database queries that would be used in GetShopStats
	var totalCustomers int64
	err := testutils.TestDB.Model(&models.ShopCustomer{}).Where("shop_id = ?", suite.testShop.ID).Count(&totalCustomers).Error
	require.NoError(t, err)
	assert.Equal(t, int64(3), totalCustomers)

	var totalCreditCodes int64
	err = testutils.TestDB.Model(&models.CreditCode{}).Where("shop_id = ?", suite.testShop.ID).Count(&totalCreditCodes).Error
	require.NoError(t, err)
	assert.Equal(t, int64(5), totalCreditCodes)

	var redeemedCodes int64
	err = testutils.TestDB.Model(&models.CreditCode{}).Where("shop_id = ? AND is_redeemed = ?", suite.testShop.ID, true).Count(&redeemedCodes).Error
	require.NoError(t, err)
	assert.Equal(t, int64(3), redeemedCodes) // 3 out of 5 are redeemed (i%2 == 0 for i=0,1,2,3,4)
}

// Test API key database operations
func (suite *ShopBasicTestSuite) TestAPIKeyDatabase_Basic() {
	t := suite.T()

	// Create test API key
	apiKey := &models.APIKey{
		ID:          uuid.New(),
		UserID:      suite.testUser.ID,
		ShopID:      &suite.testShop.ID,
		Name:        "Test API Key",
		Key:         "sk_test_" + uuid.New().String(),
		Permissions: []string{"read", "write"},
		Enabled:     true,
	}
	require.NoError(t, testutils.TestDB.Create(apiKey).Error)

	// Test querying API keys
	var apiKeys []models.APIKey
	err := testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).Find(&apiKeys).Error
	require.NoError(t, err)
	assert.Len(t, apiKeys, 1)
	assert.Equal(t, "Test API Key", apiKeys[0].Name)
	assert.True(t, apiKeys[0].Enabled)
}

// Test that all new handlers are properly defined and can be referenced
func (suite *ShopBasicTestSuite) TestHandlersExist() {
	t := suite.T()

	// Test that all handlers exist and are callable
	assert.NotNil(t, handlers.GetShopStats)
	assert.NotNil(t, handlers.GetShopAPIKeys)
	assert.NotNil(t, handlers.CreateShopAPIKey)
	assert.NotNil(t, handlers.GetShopAPIKey)
	assert.NotNil(t, handlers.UpdateShopAPIKey)
	assert.NotNil(t, handlers.DeleteShopAPIKey)
	assert.NotNil(t, handlers.GetShopBranchAPIKeys)
	assert.NotNil(t, handlers.CreateShopBranchAPIKey)
	assert.NotNil(t, handlers.GetShopBranchAPIKey)
	assert.NotNil(t, handlers.UpdateShopBranchAPIKey)
	assert.NotNil(t, handlers.DeleteShopBranchAPIKey)
}

// Run the test suite
func TestShopBasicTestSuite(t *testing.T) {
	suite.Run(t, new(ShopBasicTestSuite))
}
