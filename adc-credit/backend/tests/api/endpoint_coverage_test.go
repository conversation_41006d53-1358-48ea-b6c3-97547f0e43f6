package api

import (
	"net/http"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type EndpointCoverageTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *EndpointCoverageTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	suite.Require().NoError(err)

	// Setup router with all endpoints
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	suite.setupAllEndpoints()
}

func (suite *EndpointCoverageTestSuite) TearDownSuite() {
	testutils.CleanupTestEnvironment()
}

func (suite *EndpointCoverageTestSuite) SetupTest() {
	testutils.CleanTestData()
}

func (suite *EndpointCoverageTestSuite) setupAllEndpoints() {
	// Protected routes
	protected := suite.router.Group("/api/v1")
	protected.Use(suite.mockAuthMiddleware())
	{
		// Merchant shop management
		shops := protected.Group("/merchant-shops")
		{
			shops.GET("", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "shop1", "name": "Test Shop", "slug": "test-shop"},
					},
					"total": 1,
				})
			})
			shops.POST("", func(c *gin.Context) {
				c.JSON(http.StatusCreated, gin.H{
					"id": "new-shop-id",
					"message": "Merchant shop created successfully",
				})
			})
			shops.GET("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"name": "Test Shop",
					"slug": "test-shop",
				})
			})
			shops.GET("/slug/:slug", func(c *gin.Context) {
				slug := c.Param("slug")
				c.JSON(http.StatusOK, gin.H{
					"id": "shop-id",
					"name": "Test Shop",
					"slug": slug,
				})
			})
			shops.PUT("/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"message": "Merchant shop updated successfully",
				})
			})
			shops.DELETE("/:id", func(c *gin.Context) {
				c.JSON(http.StatusNoContent, nil)
			})

			// Customer management
			shops.GET("/:id/customers", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"data": []gin.H{
						{"id": "cust1", "name": "Customer 1", "credits": 100},
					},
					"total": 1,
				})
			})
			shops.POST("/:id/customers", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusCreated, gin.H{
					"shop_id": id,
					"customer_id": "new-customer-id",
					"message": "Customer added to shop",
				})
			})
			shops.POST("/:id/customers/:customerId/credits", func(c *gin.Context) {
				id := c.Param("id")
				customerId := c.Param("customerId")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"customer_id": customerId,
					"message": "Credits added to customer",
					"new_balance": 150,
				})
			})

			// Credit code management
			shops.GET("/:id/credit-codes", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"data": []gin.H{
						{"id": "code1", "code": "CREDIT100", "amount": 100, "is_redeemed": false},
					},
					"total": 1,
				})
			})
			shops.POST("/:id/credit-codes", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusCreated, gin.H{
					"shop_id": id,
					"code_id": "new-code-id",
					"code": "CREDIT200",
					"message": "Credit code generated successfully",
				})
			})
			shops.POST("/:id/credit-codes/qr", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
					"message": "QR code generated successfully",
				})
			})

			// Transaction management
			shops.GET("/:id/transactions", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"data": []gin.H{
						{"id": "tx1", "type": "credit_add", "amount": 100, "customer_id": "cust1"},
					},
					"total": 1,
				})
			})
		}

		// Merchant credit statistics
		protected.GET("/merchant/credit-stats", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"total_credits_issued": 5000,
				"total_credits_redeemed": 3000,
				"active_customers": 150,
				"total_shops": 5,
			})
		})

		// Customer routes
		customers := protected.Group("/customer")
		{
			customers.GET("/shops", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"data": []gin.H{
						{"id": "shop1", "name": "Shop 1", "credits": 100},
					},
					"total": 1,
				})
			})
			customers.GET("/shops/:id", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"id": id,
					"name": "Test Shop",
					"credits": 100,
				})
			})
			customers.GET("/shops/:id/transactions", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"data": []gin.H{
						{"id": "tx1", "type": "credit_use", "amount": -10},
					},
					"total": 1,
				})
			})
			customers.POST("/shops/:id/use-credit", func(c *gin.Context) {
				id := c.Param("id")
				c.JSON(http.StatusOK, gin.H{
					"shop_id": id,
					"message": "Credit used successfully",
					"remaining_credits": 90,
				})
			})
			customers.POST("/redeem-code", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"message": "Credit code redeemed successfully",
					"credits_added": 100,
					"new_balance": 200,
				})
			})
		}
	}

	// Admin routes
	admin := suite.router.Group("/api/v1/admin")
	admin.Use(suite.mockAuthMiddleware(), suite.mockAdminMiddleware())
	{
		admin.GET("/users", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"data": []gin.H{
					{"id": "user1", "email": "<EMAIL>", "role": "user"},
					{"id": "user2", "email": "<EMAIL>", "role": "admin"},
				},
				"total": 2,
			})
		})
		admin.GET("/users/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"email": "<EMAIL>",
				"role": "user",
			})
		})
		admin.PUT("/users/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"message": "User updated by admin",
			})
		})
		admin.DELETE("/users/:id", func(c *gin.Context) {
			c.JSON(http.StatusNoContent, nil)
		})

		admin.GET("/subscriptions", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"data": []gin.H{
					{"id": "sub1", "user_id": "user1", "tier": "premium"},
				},
				"total": 1,
			})
		})
		admin.POST("/subscription-tiers", func(c *gin.Context) {
			c.JSON(http.StatusCreated, gin.H{
				"id": "new-tier-id",
				"message": "Subscription tier created",
			})
		})
		admin.PUT("/subscription-tiers/:id", func(c *gin.Context) {
			id := c.Param("id")
			c.JSON(http.StatusOK, gin.H{
				"id": id,
				"message": "Subscription tier updated",
			})
		})
		admin.DELETE("/subscription-tiers/:id", func(c *gin.Context) {
			c.JSON(http.StatusNoContent, nil)
		})
	}
}

// Mock authentication middleware
func (suite *EndpointCoverageTestSuite) mockAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		auth := c.GetHeader("Authorization")
		if auth == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
			c.Abort()
			return
		}

		mockUser := gin.H{
			"id":    "user-123",
			"email": "<EMAIL>",
			"name":  "Test User",
			"role":  "admin", // Set as admin for admin tests
		}
		c.Set("user", mockUser)
		c.Next()
	}
}

// Mock admin middleware
func (suite *EndpointCoverageTestSuite) mockAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		user := c.MustGet("user").(gin.H)
		if user["role"] != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}
		c.Next()
	}
}

// ============================================================================
// MERCHANT SHOP ENDPOINT TESTS
// ============================================================================

func (suite *EndpointCoverageTestSuite) TestMerchantShopsGetAll() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops",
		Headers: map[string]string{
			"Authorization": "Bearer test-token",
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "data")
	assert.Contains(t, resp.Body, "total")
}

func (suite *EndpointCoverageTestSuite) TestMerchantShopsCreate() {
	t := suite.T()

	shopData := map[string]interface{}{
		"name":          "New Test Shop",
		"description":   "A test shop for testing",
		"contact_email": "<EMAIL>",
		"contact_phone": "+1234567890",
	}

	req := testutils.TestRequest{
		Method: "POST",
		URL:    "/api/v1/merchant-shops",
		Headers: map[string]string{
			"Authorization": "Bearer test-token",
		},
		Body: shopData,
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 201, resp.StatusCode)
	assert.Contains(t, resp.Body, "message")
}

func (suite *EndpointCoverageTestSuite) TestMerchantShopsGetByID() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops/shop-123",
		Headers: map[string]string{
			"Authorization": "Bearer test-token",
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "id")
	assert.Contains(t, resp.Body, "name")
}

func (suite *EndpointCoverageTestSuite) TestMerchantShopsGetBySlug() {
	t := suite.T()

	req := testutils.TestRequest{
		Method: "GET",
		URL:    "/api/v1/merchant-shops/slug/test-shop",
		Headers: map[string]string{
			"Authorization": "Bearer test-token",
		},
	}

	resp := testutils.MakeRequest(t, suite.router, req)

	testutils.AssertSuccessResponse(t, resp)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Contains(t, resp.Body, "slug")
}

// Run the test suite
func TestEndpointCoverageTestSuite(t *testing.T) {
	suite.Run(t, new(EndpointCoverageTestSuite))
}
