package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// APIKeyLiveIntegrationTestSuite tests the live API key provided by the user
type APIKeyLiveIntegrationTestSuite struct {
	suite.Suite
	baseURL string
	apiKey  string
}

func (suite *APIKeyLiveIntegrationTestSuite) SetupSuite() {
	// Use the actual running server on port 8100
	suite.baseURL = "http://localhost:8100"
	// The actual API key provided by the user
	suite.apiKey = "52Hp1T6D4FM5jDOWvxtfXAgKGHoHkkw4pQ6uozOmRnk="
}

// Helper function to make HTTP requests
func (suite *APIKeyLiveIntegrationTestSuite) makeRequest(method, endpoint string, body interface{}, headers map[string]string) (*http.Response, []byte, error) {
	var bodyBytes []byte
	var err error
	
	if body != nil {
		bodyBytes, err = json.Marshal(body)
		if err != nil {
			return nil, nil, err
		}
	}

	url := suite.baseURL + endpoint
	req, err := http.NewRequest(method, url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, nil, err
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")
	
	// Set additional headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()

	respBody := make([]byte, 0)
	buf := make([]byte, 1024)
	for {
		n, err := resp.Body.Read(buf)
		if n > 0 {
			respBody = append(respBody, buf[:n]...)
		}
		if err != nil {
			break
		}
	}

	return resp, respBody, nil
}

// ============================================================================
// API KEY VERIFICATION TESTS
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestAPIKeyVerification() {
	t := suite.T()

	// Test API key verification endpoint
	body := map[string]interface{}{
		"api_key": suite.apiKey,
		"credits": 1,
	}

	resp, respBody, err := suite.makeRequest("POST", "/api/v1/external/verify", body, nil)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var result map[string]interface{}
	err = json.Unmarshal(respBody, &result)
	assert.NoError(t, err)

	// Verify response structure
	assert.Equal(t, true, result["valid"])
	assert.NotNil(t, result["credit_balance"])
	
	// Log the current credit balance
	t.Logf("API Key Verification - Credit Balance: %.0f", result["credit_balance"])
}

func (suite *APIKeyLiveIntegrationTestSuite) TestAPIKeyVerificationInvalidKey() {
	t := suite.T()

	body := map[string]interface{}{
		"api_key": "invalid-key-test",
		"credits": 1,
	}

	resp, _, err := suite.makeRequest("POST", "/api/v1/external/verify", body, nil)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
}

// ============================================================================
// CREDIT CONSUMPTION TESTS
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestCreditConsumption() {
	t := suite.T()

	// Test credit consumption with various scenarios
	testCases := []struct {
		name      string
		credits   int
		endpoint  string
		method    string
		ipAddress string
		userAgent string
	}{
		{
			name:      "Basic GET request",
			credits:   1,
			endpoint:  "/api/test/get",
			method:    "GET",
			ipAddress: "127.0.0.1",
			userAgent: "Test-Client/1.0",
		},
		{
			name:      "POST request with write permission",
			credits:   2,
			endpoint:  "/api/test/post",
			method:    "POST",
			ipAddress: "*************",
			userAgent: "Integration-Test/1.0",
		},
		{
			name:      "DELETE request with delete permission",
			credits:   3,
			endpoint:  "/api/test/delete",
			method:    "DELETE",
			ipAddress: "********",
			userAgent: "API-Test-Suite/2.0",
		},
		{
			name:      "PUT request with write permission",
			credits:   2,
			endpoint:  "/api/test/put",
			method:    "PUT",
			ipAddress: "**********",
			userAgent: "Live-Test/1.5",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			body := map[string]interface{}{
				"endpoint":   tc.endpoint,
				"method":     tc.method,
				"credits":    tc.credits,
				"ip_address": tc.ipAddress,
				"user_agent": tc.userAgent,
			}

			headers := map[string]string{
				"X-API-Key": suite.apiKey,
			}

			resp, respBody, err := suite.makeRequest("POST", "/api/v1/external/consume", body, headers)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, resp.StatusCode)

			var result map[string]interface{}
			err = json.Unmarshal(respBody, &result)
			assert.NoError(t, err)

			// Verify response structure
			assert.Equal(t, "Credits consumed successfully", result["message"])
			assert.NotNil(t, result["credit_balance"])
			assert.NotNil(t, result["usage"])

			// Verify usage tracking
			usage, ok := result["usage"].(map[string]interface{})
			assert.True(t, ok)
			assert.Equal(t, tc.endpoint, usage["endpoint"])
			assert.Equal(t, tc.method, usage["method"])
			assert.Equal(t, float64(tc.credits), usage["credits"])
			assert.Equal(t, tc.ipAddress, usage["ip_address"])
			assert.Equal(t, tc.userAgent, usage["user_agent"])

			t.Logf("%s - Consumed %d credits, Remaining: %.0f", tc.name, tc.credits, result["credit_balance"])
		})
	}
}

func (suite *APIKeyLiveIntegrationTestSuite) TestCreditConsumptionInvalidAPIKey() {
	t := suite.T()

	body := map[string]interface{}{
		"endpoint": "/api/test",
		"method":   "GET",
		"credits":  1,
	}

	headers := map[string]string{
		"X-API-Key": "invalid-api-key-12345",
	}

	resp, _, err := suite.makeRequest("POST", "/api/v1/external/consume", body, headers)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
}

func (suite *APIKeyLiveIntegrationTestSuite) TestCreditConsumptionMissingAPIKey() {
	t := suite.T()

	body := map[string]interface{}{
		"endpoint": "/api/test",
		"method":   "GET",
		"credits":  1,
	}

	resp, _, err := suite.makeRequest("POST", "/api/v1/external/consume", body, nil)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
}

func (suite *APIKeyLiveIntegrationTestSuite) TestCreditConsumptionInvalidData() {
	t := suite.T()

	testCases := []struct {
		name string
		body map[string]interface{}
	}{
		{
			name: "Negative credits",
			body: map[string]interface{}{
				"endpoint": "/api/test",
				"method":   "GET",
				"credits":  -1,
			},
		},
		{
			name: "Zero credits",
			body: map[string]interface{}{
				"endpoint": "/api/test",
				"method":   "GET",
				"credits":  0,
			},
		},
		{
			name: "Missing endpoint",
			body: map[string]interface{}{
				"method":  "GET",
				"credits": 1,
			},
		},
		{
			name: "Missing method",
			body: map[string]interface{}{
				"endpoint": "/api/test",
				"credits":  1,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			headers := map[string]string{
				"X-API-Key": suite.apiKey,
			}

			resp, _, err := suite.makeRequest("POST", "/api/v1/external/consume", tc.body, headers)
			assert.NoError(t, err)
			assert.True(t, resp.StatusCode >= 400, "Expected error status code for %s", tc.name)
		})
	}
}

// ============================================================================
// RATE LIMITING TESTS
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestRateLimiting() {
	t := suite.T()

	// Test rate limiting by making multiple rapid requests
	var successCount, rateLimitCount int
	maxRequests := 15 // Try to exceed potential rate limits

	for i := 0; i < maxRequests; i++ {
		body := map[string]interface{}{
			"endpoint": fmt.Sprintf("/api/test/rate-limit-%d", i),
			"method":   "GET",
			"credits":  1,
		}

		headers := map[string]string{
			"X-API-Key": suite.apiKey,
		}

		resp, _, err := suite.makeRequest("POST", "/api/v1/external/consume", body, headers)
		assert.NoError(t, err)

		if resp.StatusCode == http.StatusOK {
			successCount++
		} else if resp.StatusCode == http.StatusTooManyRequests {
			rateLimitCount++
		}

		// Small delay to prevent overwhelming the server
		time.Sleep(50 * time.Millisecond)
	}

	t.Logf("Rate Limiting Test - Success: %d, Rate Limited: %d, Total: %d", successCount, rateLimitCount, maxRequests)
	
	// At least some requests should succeed
	assert.Greater(t, successCount, 0, "At least some requests should succeed")
}

// ============================================================================
// PERMISSION TESTS
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestPermissionBasedAccess() {
	t := suite.T()

	// Test different HTTP methods to verify permissions
	methods := []string{"GET", "POST", "PUT", "DELETE"}
	
	for _, method := range methods {
		t.Run(fmt.Sprintf("Permission_test_%s", method), func(t *testing.T) {
			body := map[string]interface{}{
				"endpoint": fmt.Sprintf("/api/test/permission/%s", method),
				"method":   method,
				"credits":  1,
			}

			headers := map[string]string{
				"X-API-Key": suite.apiKey,
			}

			resp, respBody, err := suite.makeRequest("POST", "/api/v1/external/consume", body, headers)
			assert.NoError(t, err)
			
			// Since the user mentioned the key has read, write, and delete permissions,
			// all these methods should work
			if resp.StatusCode == http.StatusOK {
				var result map[string]interface{}
				err = json.Unmarshal(respBody, &result)
				assert.NoError(t, err)
				t.Logf("Method %s - Success: %v", method, result["message"])
			} else {
				t.Logf("Method %s - Status: %d", method, resp.StatusCode)
			}
		})
	}
}

// ============================================================================
// BULK OPERATIONS TEST
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestBulkOperations() {
	t := suite.T()

	// Test bulk credit consumption to verify consistency
	initialBalance := suite.getInitialBalance(t)
	
	var totalCreditsConsumed float64
	bulkSize := 5

	for i := 0; i < bulkSize; i++ {
		credits := float64(i + 1) // 1, 2, 3, 4, 5 credits
		totalCreditsConsumed += credits

		body := map[string]interface{}{
			"endpoint": fmt.Sprintf("/api/test/bulk-%d", i),
			"method":   "POST",
			"credits":  credits,
		}

		headers := map[string]string{
			"X-API-Key": suite.apiKey,
		}

		resp, respBody, err := suite.makeRequest("POST", "/api/v1/external/consume", body, headers)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.Unmarshal(respBody, &result)
		assert.NoError(t, err)

		currentBalance := result["credit_balance"].(float64)
		expectedBalance := initialBalance - totalCreditsConsumed
		
		// Allow for some floating point precision differences
		assert.InDelta(t, expectedBalance, currentBalance, 0.1, 
			"Credit balance mismatch after consuming %.0f credits", totalCreditsConsumed)

		t.Logf("Bulk Operation %d - Consumed %.0f credits, Balance: %.0f", i+1, credits, currentBalance)
	}

	t.Logf("Bulk Operations Complete - Total Credits Consumed: %.0f", totalCreditsConsumed)
}

// Helper function to get initial balance
func (suite *APIKeyLiveIntegrationTestSuite) getInitialBalance(t *testing.T) float64 {
	body := map[string]interface{}{
		"api_key": suite.apiKey,
		"credits": 0, // Just checking balance, not consuming
	}

	resp, respBody, err := suite.makeRequest("POST", "/api/v1/external/verify", body, nil)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var result map[string]interface{}
	err = json.Unmarshal(respBody, &result)
	assert.NoError(t, err)

	return result["credit_balance"].(float64)
}

// ============================================================================
// ERROR HANDLING TESTS
// ============================================================================

func (suite *APIKeyLiveIntegrationTestSuite) TestErrorHandling() {
	t := suite.T()

	testCases := []struct {
		name           string
		endpoint       string
		method         string
		headers        map[string]string
		body           map[string]interface{}
		expectedStatus int
	}{
		{
			name:     "Malformed JSON",
			endpoint: "/api/v1/external/consume",
			method:   "POST",
			headers: map[string]string{
				"X-API-Key":    suite.apiKey,
				"Content-Type": "application/json",
			},
			body:           nil, // Will send malformed JSON manually
			expectedStatus: 400,
		},
		{
			name:     "Wrong HTTP method",
			endpoint: "/api/v1/external/consume",
			method:   "GET", // Should be POST
			headers: map[string]string{
				"X-API-Key": suite.apiKey,
			},
			body: map[string]interface{}{
				"endpoint": "/api/test",
				"method":   "GET",
				"credits":  1,
			},
			expectedStatus: 405, // Method Not Allowed
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			resp, _, err := suite.makeRequest(tc.method, tc.endpoint, tc.body, tc.headers)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedStatus, resp.StatusCode, "Test case: %s", tc.name)
		})
	}
}

// Run the test suite
func TestAPIKeyLiveIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(APIKeyLiveIntegrationTestSuite))
}
