package integration_basic

import (
	"context"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/models"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ADCBasicIntegrationTestSuite tests basic ADC integration functionality
type ADCBasicIntegrationTestSuite struct {
	suite.Suite
	testUser         *models.User
	testShop         *models.Shop
	testSubscription *models.ShopSubscription
	ctx              context.Context
}

// SetupSuite initializes the test environment
func (suite *ADCBasicIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Create test user with unique identifiers
	uniqueID := uuid.New().String()[:8]
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "adcbasic-" + uniqueID + "@example.com",
		Name:     "ADC Basic Test User",
		GoogleID: "adc-basic-google-" + uniqueID,
		Role:     "user",
	}
	err = testutils.TestDB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:           uuid.New(),
		OwnerUserID:  suite.testUser.ID,
		Name:         "ADC Basic Test Shop",
		Slug:         "adc-basic-test-shop-" + uniqueID,
		ShopType:     "api_service",
		ContactEmail: "shop-" + uniqueID + "@test.com",
	}
	err = testutils.TestDB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Create test shop subscription using raw SQL to match actual database schema
	subscriptionID := uuid.New()
	suite.testSubscription = &models.ShopSubscription{
		ID:            subscriptionID,
		ShopID:        suite.testShop.ID,
		Status:        "active",
		CreditBalance: 1000,
		StartDate:     time.Now(),
	}
	
	// Use raw SQL to insert subscription with existing schema
	err = testutils.TestDB.Exec(`
		INSERT INTO shop_subscriptions (id, shop_id, status, credit_balance, start_date, auto_renew, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, subscriptionID, suite.testShop.ID, "active", 1000, time.Now(), true).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleans up after tests
func (suite *ADCBasicIntegrationTestSuite) TearDownSuite() {
	if testutils.TestDB != nil {
		// Clean up test data
		testutils.TestDB.Where("id = ?", suite.testSubscription.ID).Delete(&models.ShopSubscription{})
		testutils.TestDB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		testutils.TestDB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test local database consistency
func (suite *ADCBasicIntegrationTestSuite) TestLocalDatabaseConsistency() {
	suite.T().Log("Testing local database consistency...")

	// Test shop and subscription relationship
	var shop models.Shop
	err := testutils.TestDB.Where("id = ?", suite.testShop.ID).First(&shop).Error
	require.NoError(suite.T(), err, "Shop should exist in database")

	var subscription models.ShopSubscription
	err = testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).First(&subscription).Error
	require.NoError(suite.T(), err, "Subscription should exist for shop")

	// Verify relationships
	assert.Equal(suite.T(), suite.testShop.ID, subscription.ShopID, "Subscription should reference correct shop")
	assert.Equal(suite.T(), "active", subscription.Status, "Subscription should be active")
	assert.Equal(suite.T(), 1000, subscription.CreditBalance, "Credit balance should match initial value")

	suite.T().Logf("Local database consistency verified: Shop=%s, Subscription=%s", 
		shop.ID, subscription.ID)
}

// Test transaction event creation and retrieval
func (suite *ADCBasicIntegrationTestSuite) TestTransactionEventManagement() {
	suite.T().Log("Testing transaction event management...")

	// Create transaction events
	transactions := []*models.Transaction{
		{
			ID:          uuid.New(),
			UserID:      suite.testUser.ID,
			Amount:      50,
			Type:        "credit_use",
			Description: "API call consumption",
			Reference:   "shop:" + suite.testShop.ID.String(),
		},
		{
			ID:          uuid.New(),
			UserID:      suite.testUser.ID,
			Amount:      25,
			Type:        "credit_use",
			Description: "QR code generation",
			Reference:   "shop:" + suite.testShop.ID.String(),
		},
	}

	// Store transaction events
	for _, transaction := range transactions {
		err := testutils.TestDB.Create(transaction).Error
		require.NoError(suite.T(), err, "Transaction should be created successfully")
		suite.T().Logf("Created transaction: %s (%d)", transaction.Type, transaction.Amount)
	}

	// Retrieve and verify transactions
	var storedTransactions []models.Transaction
	err := testutils.TestDB.Where("reference LIKE ?", "shop:"+suite.testShop.ID.String()+"%").Find(&storedTransactions).Error
	require.NoError(suite.T(), err, "Should be able to retrieve transactions")

	assert.GreaterOrEqual(suite.T(), len(storedTransactions), 2, "Should have at least 2 transactions")

	// Verify transaction details
	totalAmount := 0
	for _, transaction := range storedTransactions {
		assert.Contains(suite.T(), transaction.Reference, suite.testShop.ID.String(), "Transaction should belong to correct shop")
		assert.Greater(suite.T(), transaction.Amount, 0, "Amount should be positive")
		totalAmount += transaction.Amount
	}

	suite.T().Logf("Total transaction amount: %d", totalAmount)
	assert.Equal(suite.T(), 75, totalAmount, "Total amount should match expected sum")
}

// Test credit balance management
func (suite *ADCBasicIntegrationTestSuite) TestCreditBalanceManagement() {
	suite.T().Log("Testing credit balance management...")

	// Get initial balance
	var subscription models.ShopSubscription
	err := testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).First(&subscription).Error
	require.NoError(suite.T(), err)
	initialBalance := subscription.CreditBalance

	suite.T().Logf("Initial credit balance: %d", initialBalance)

	// Simulate credit consumption
	consumptionAmount := 100
	newBalance := initialBalance - consumptionAmount

	err = testutils.TestDB.Model(&subscription).Update("credit_balance", newBalance).Error
	require.NoError(suite.T(), err, "Should be able to update credit balance")

	// Verify balance update
	var updatedSubscription models.ShopSubscription
	err = testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), newBalance, updatedSubscription.CreditBalance, "Credit balance should be updated correctly")
	suite.T().Logf("Updated credit balance: %d (consumed: %d)", newBalance, consumptionAmount)

	// Restore balance for cleanup
	err = testutils.TestDB.Model(&subscription).Update("credit_balance", initialBalance).Error
	require.NoError(suite.T(), err)
}

// Test shop-to-organization mapping consistency
func (suite *ADCBasicIntegrationTestSuite) TestShopOrganizationMapping() {
	suite.T().Log("Testing shop-to-organization mapping consistency...")

	// Verify shop exists and has correct structure
	var subscription models.ShopSubscription
	err := testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).First(&subscription).Error
	require.NoError(suite.T(), err)

	// Shop ID should be used as basis for organization mapping in ADC system
	suite.T().Logf("Shop-Organization mapping verified: Shop=%s would map to Organization External ID=%s", 
		suite.testShop.ID, suite.testShop.ID.String())

	// Test organization data structure that would be sent to ADC services
	organizationData := map[string]interface{}{
		"id":           "org-" + uuid.New().String(),
		"external_id":  suite.testShop.ID.String(),
		"name":         suite.testShop.Name,
		"shop_type":    suite.testShop.ShopType,
		"created_from": "adc_credit_service",
	}

	suite.T().Logf("Simulated organization data for ADC integration: %+v", organizationData)

	// Verify organization data structure
	assert.Equal(suite.T(), suite.testShop.ID.String(), organizationData["external_id"], 
		"Organization external ID should match shop ID")
	assert.Equal(suite.T(), suite.testShop.Name, organizationData["name"], 
		"Organization name should match shop name")
}

// Test concurrent operations
func (suite *ADCBasicIntegrationTestSuite) TestConcurrentOperations() {
	suite.T().Log("Testing concurrent operations...")

	const numOperations = 3
	results := make(chan error, numOperations)

	// Start concurrent database operations
	for i := 0; i < numOperations; i++ {
		go func(index int) {
			// Create transaction
			transaction := &models.Transaction{
				ID:          uuid.New(),
				UserID:      suite.testUser.ID,
				Amount:      index + 1,
				Type:        "credit_use",
				Description: "Concurrent operation test",
				Reference:   "concurrent_test:shop:" + suite.testShop.ID.String(),
			}

			err := testutils.TestDB.Create(transaction).Error
			results <- err
		}(i)
	}

	// Collect results
	var errors []error
	for i := 0; i < numOperations; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	suite.T().Logf("Concurrent operations completed: %d errors out of %d operations", 
		len(errors), numOperations)
	assert.Equal(suite.T(), 0, len(errors), "All concurrent operations should succeed")

	// Verify all transactions were created
	var transactionCount int64
	err := testutils.TestDB.Model(&models.Transaction{}).
		Where("reference LIKE ?", "concurrent_test:shop:"+suite.testShop.ID.String()+"%").
		Count(&transactionCount).Error
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), int64(numOperations), transactionCount, 
		"Should have created all concurrent transactions")
}

// Test data validation and constraints
func (suite *ADCBasicIntegrationTestSuite) TestDataValidationAndConstraints() {
	suite.T().Log("Testing data validation and constraints...")

	// Test shop subscription constraints
	var subscription models.ShopSubscription
	err := testutils.TestDB.Where("shop_id = ?", suite.testShop.ID).First(&subscription).Error
	require.NoError(suite.T(), err)

	// Verify required fields
	assert.NotEmpty(suite.T(), subscription.Status, "Status should not be empty")
	assert.GreaterOrEqual(suite.T(), subscription.CreditBalance, 0, "Credit balance should be non-negative")
	assert.Equal(suite.T(), suite.testShop.ID, subscription.ShopID, "Subscription should reference correct shop")

	// Test shop constraints
	var shop models.Shop
	err = testutils.TestDB.Where("id = ?", suite.testShop.ID).First(&shop).Error
	require.NoError(suite.T(), err)

	assert.NotEmpty(suite.T(), shop.Name, "Shop name should not be empty")
	assert.NotEmpty(suite.T(), shop.Slug, "Shop slug should not be empty")
	assert.NotEmpty(suite.T(), shop.ShopType, "Shop type should not be empty")
	assert.Equal(suite.T(), suite.testUser.ID, shop.OwnerUserID, "Shop should belong to correct user")

	suite.T().Log("Data validation and constraints verified")
}

// Test API key functionality
func (suite *ADCBasicIntegrationTestSuite) TestAPIKeyFunctionality() {
	suite.T().Log("Testing API key functionality...")

	// Create API key for the shop
	apiKey := &models.APIKey{
		ID:         uuid.New(),
		UserID:     suite.testUser.ID,
		ShopID:     &suite.testShop.ID,
		Name:       "Test API Key",
		Key:        "test-api-key-" + uuid.New().String(),
		Enabled:    true,
		Permissions: []string{"read", "write"},
	}

	err := testutils.TestDB.Create(apiKey).Error
	require.NoError(suite.T(), err, "API key should be created successfully")

	suite.T().Logf("Created API key: %s", apiKey.Name)

	// Retrieve API key
	var storedAPIKey models.APIKey
	err = testutils.TestDB.Where("shop_id = ? AND key = ?", suite.testShop.ID, apiKey.Key).First(&storedAPIKey).Error
	require.NoError(suite.T(), err, "Should be able to retrieve API key")

	// Verify API key details
	assert.Equal(suite.T(), apiKey.Name, storedAPIKey.Name, "API key name should match")
	assert.Equal(suite.T(), apiKey.Key, storedAPIKey.Key, "API key should match")
	assert.Equal(suite.T(), &suite.testShop.ID, storedAPIKey.ShopID, "API key should belong to correct shop")
	assert.True(suite.T(), storedAPIKey.Enabled, "API key should be enabled")

	suite.T().Log("API key functionality verified")

	// Cleanup
	testutils.TestDB.Where("id = ?", apiKey.ID).Delete(&models.APIKey{})
}

// Test webhook functionality
func (suite *ADCBasicIntegrationTestSuite) TestWebhookFunctionality() {
	suite.T().Log("Testing webhook functionality...")

	// Create webhook for the shop using raw SQL to handle PostgreSQL array properly
	webhookID := uuid.New()
	webhook := &models.Webhook{
		ID:     webhookID,
		UserID: suite.testUser.ID,
		Name:   "Test Webhook",
		URL:    "https://example.com/webhook-" + uuid.New().String()[:8],
		Events: []string{"credit.consumed", "credit.added"},
		Active: true,
	}

	// Use raw SQL to insert webhook with proper array format
	err := testutils.TestDB.Exec(`
		INSERT INTO webhooks (id, user_id, name, url, events, active, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, webhookID, suite.testUser.ID, "Test Webhook", webhook.URL, `{"credit.consumed","credit.added"}`, true).Error
	require.NoError(suite.T(), err, "Webhook should be created successfully")

	suite.T().Logf("Created webhook: %s", webhook.URL)

	// Retrieve webhook
	var storedWebhook models.Webhook
	err = testutils.TestDB.Where("user_id = ? AND url = ?", suite.testUser.ID, webhook.URL).First(&storedWebhook).Error
	require.NoError(suite.T(), err, "Should be able to retrieve webhook")

	// Verify webhook details
	assert.Equal(suite.T(), webhook.URL, storedWebhook.URL, "Webhook URL should match")
	assert.Equal(suite.T(), suite.testUser.ID, storedWebhook.UserID, "Webhook should belong to correct user")
	assert.True(suite.T(), storedWebhook.Active, "Webhook should be active")
	// Check that events field contains our event data (handling PostgreSQL array format)
	eventsString := string(storedWebhook.Events[0])
	assert.Contains(suite.T(), eventsString, "credit.consumed", "Webhook should have correct events")

	suite.T().Log("Webhook functionality verified")

	// Cleanup
	testutils.TestDB.Where("id = ?", webhook.ID).Delete(&models.Webhook{})
}

// TestADCBasicIntegration runs the basic ADC integration test suite
func TestADCBasicIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ADC basic integration tests in short mode")
	}

	suite.Run(t, new(ADCBasicIntegrationTestSuite))
}