package integration

import (
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMonthlyQuotaResetService(t *testing.T) {
	// Setup test database
	SetupTestDB()
	defer CleanupTestDB()

	t.Run("ProcessMonthlyQuotaResets", func(t *testing.T) {
		// Create test users
		user1 := createTestUser("<EMAIL>")
		user2 := createTestUser("<EMAIL>")

		// Create monthly usage records for previous month
		lastMonth := time.Now().AddDate(0, -1, 0)
		usage1 := models.MonthlyUsageTracking{
			UserID:           user1.ID,
			Year:             lastMonth.Year(),
			Month:            int(lastMonth.Month()),
			QRCodesGenerated: 50,
			APICallsMade:     200,
			CreditsConsumed:  100,
		}
		usage2 := models.MonthlyUsageTracking{
			UserID:           user2.ID,
			Year:             lastMonth.Year(),
			Month:            int(lastMonth.Month()),
			QRCodesGenerated: 30,
			APICallsMade:     150,
			CreditsConsumed:  75,
		}

		err := database.DB.Create(&usage1).Error
		require.NoError(t, err)
		err = database.DB.Create(&usage2).Error
		require.NoError(t, err)

		// Set users to need reset (reset date in the past)
		pastDate := time.Now().AddDate(0, -1, 0)
		err = database.DB.Model(&user1).Update("monthly_usage_reset_date", pastDate).Error
		require.NoError(t, err)
		err = database.DB.Model(&user2).Update("monthly_usage_reset_date", pastDate).Error
		require.NoError(t, err)

		// Create service and process resets
		service := services.NewMonthlyQuotaResetService()
		result, err := service.ProcessMonthlyQuotaResets()

		// Verify results
		require.NoError(t, err)
		assert.Equal(t, 2, result.UsersProcessed)
		assert.Equal(t, 2, result.RecordsReset)
		assert.Empty(t, result.Errors)

		// Verify users have updated reset dates
		var updatedUser1, updatedUser2 models.User
		err = database.DB.First(&updatedUser1, user1.ID).Error
		require.NoError(t, err)
		err = database.DB.First(&updatedUser2, user2.ID).Error
		require.NoError(t, err)

		now := time.Now()
		assert.True(t, updatedUser1.MonthlyUsageResetDate.After(now))
		assert.True(t, updatedUser2.MonthlyUsageResetDate.After(now))

		// Verify new monthly usage records were created for current month
		currentMonth := time.Now()
		var currentUsage1, currentUsage2 models.MonthlyUsageTracking
		err = database.DB.Where("user_id = ? AND year = ? AND month = ?", 
			user1.ID, currentMonth.Year(), int(currentMonth.Month())).
			First(&currentUsage1).Error
		require.NoError(t, err)

		err = database.DB.Where("user_id = ? AND year = ? AND month = ?", 
			user2.ID, currentMonth.Year(), int(currentMonth.Month())).
			First(&currentUsage2).Error
		require.NoError(t, err)

		// Verify counters are reset
		assert.Equal(t, 0, currentUsage1.QRCodesGenerated)
		assert.Equal(t, 0, currentUsage1.APICallsMade)
		assert.Equal(t, 0, currentUsage1.CreditsConsumed)
		assert.Equal(t, 0, currentUsage2.QRCodesGenerated)
		assert.Equal(t, 0, currentUsage2.APICallsMade)
		assert.Equal(t, 0, currentUsage2.CreditsConsumed)
	})

	t.Run("GetUserMonthlyUsage", func(t *testing.T) {
		user := createTestUser("<EMAIL>")
		service := services.NewMonthlyQuotaResetService()

		now := time.Now()
		year := now.Year()
		month := int(now.Month())

		// Test getting usage for non-existent record (should create one)
		usage, err := service.GetUserMonthlyUsage(user.ID, year, month)
		require.NoError(t, err)
		assert.Equal(t, user.ID, usage.UserID)
		assert.Equal(t, year, usage.Year)
		assert.Equal(t, month, usage.Month)
		assert.Equal(t, 0, usage.QRCodesGenerated)
		assert.Equal(t, 0, usage.APICallsMade)
		assert.Equal(t, 0, usage.CreditsConsumed)

		// Test getting existing usage
		usage2, err := service.GetUserMonthlyUsage(user.ID, year, month)
		require.NoError(t, err)
		assert.Equal(t, usage.ID, usage2.ID)
	})

	t.Run("IncrementUsageMethods", func(t *testing.T) {
		user := createTestUser("<EMAIL>")
		service := services.NewMonthlyQuotaResetService()

		now := time.Now()
		year := now.Year()
		month := int(now.Month())

		// Create initial usage record
		_, err := service.GetUserMonthlyUsage(user.ID, year, month)
		require.NoError(t, err)

		// Test incrementing QR codes
		err = service.IncrementQRCodeUsage(user.ID)
		require.NoError(t, err)

		// Test incrementing API calls
		err = service.IncrementAPICallUsage(user.ID)
		require.NoError(t, err)

		// Test incrementing credits
		err = service.IncrementCreditUsage(user.ID, 10)
		require.NoError(t, err)

		// Verify increments
		usage, err := service.GetUserMonthlyUsage(user.ID, year, month)
		require.NoError(t, err)
		assert.Equal(t, 1, usage.QRCodesGenerated)
		assert.Equal(t, 1, usage.APICallsMade)
		assert.Equal(t, 10, usage.CreditsConsumed)

		// Test multiple increments
		err = service.IncrementQRCodeUsage(user.ID)
		require.NoError(t, err)
		err = service.IncrementCreditUsage(user.ID, 5)
		require.NoError(t, err)

		usage, err = service.GetUserMonthlyUsage(user.ID, year, month)
		require.NoError(t, err)
		assert.Equal(t, 2, usage.QRCodesGenerated)
		assert.Equal(t, 1, usage.APICallsMade)
		assert.Equal(t, 15, usage.CreditsConsumed)
	})

	t.Run("ForceResetUserQuota", func(t *testing.T) {
		user := createTestUser("<EMAIL>")
		service := services.NewMonthlyQuotaResetService()

		now := time.Now()
		year := now.Year()
		month := int(now.Month())

		// Create usage record with some data
		usage := models.MonthlyUsageTracking{
			UserID:           user.ID,
			Year:             year,
			Month:            month,
			QRCodesGenerated: 25,
			APICallsMade:     100,
			CreditsConsumed:  50,
		}
		err := database.DB.Create(&usage).Error
		require.NoError(t, err)

		// Force reset the user quota
		err = service.ForceResetUserQuota(user.ID)
		require.NoError(t, err)

		// Verify the usage was reset
		var resetUsage models.MonthlyUsageTracking
		err = database.DB.Where("user_id = ? AND year = ? AND month = ?", 
			user.ID, year, month).First(&resetUsage).Error
		require.NoError(t, err)

		assert.Equal(t, 0, resetUsage.QRCodesGenerated)
		assert.Equal(t, 0, resetUsage.APICallsMade)
		assert.Equal(t, 0, resetUsage.CreditsConsumed)

		// Verify user's reset date was updated
		var updatedUser models.User
		err = database.DB.First(&updatedUser, user.ID).Error
		require.NoError(t, err)
		assert.True(t, updatedUser.MonthlyUsageResetDate.After(now))
	})
}

func TestUpdatedSubscriptionGuard(t *testing.T) {
	// Setup test database
	SetupTestDB()
	defer CleanupTestDB()

	t.Run("CheckQRCodeLimitWithMonthlyTracking", func(t *testing.T) {
		user := createTestUser("<EMAIL>")
		
		// Create subscription tier with QR code limit
		tier := models.SubscriptionTier{
			Name:               "Test Tier",
			MaxQRCodesPerMonth: 100,
			UnlimitedQRCodes:   false,
		}
		err := database.DB.Create(&tier).Error
		require.NoError(t, err)

		// Create active subscription
		subscription := models.Subscription{
			UserID:             user.ID,
			SubscriptionTierID: tier.ID,
			Status:             "active",
			SubscriptionType:   "personal",
		}
		err = database.DB.Create(&subscription).Error
		require.NoError(t, err)

		guard := services.NewSubscriptionGuard()

		// Test with no existing usage record
		result, err := guard.CheckQRCodeLimit(user.ID)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 0, result.CurrentUsage)
		assert.Equal(t, 100, result.Limit)

		// Create usage record with some QR codes
		now := time.Now()
		usage := models.MonthlyUsageTracking{
			UserID:           user.ID,
			Year:             now.Year(),
			Month:            int(now.Month()),
			QRCodesGenerated: 95,
		}
		err = database.DB.Create(&usage).Error
		require.NoError(t, err)

		// Test near limit
		result, err = guard.CheckQRCodeLimit(user.ID)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 95, result.CurrentUsage)
		assert.Equal(t, 100, result.Limit)

		// Test at limit
		err = database.DB.Model(&usage).Update("qr_codes_generated", 100).Error
		require.NoError(t, err)

		result, err = guard.CheckQRCodeLimit(user.ID)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, 100, result.CurrentUsage)
		assert.Equal(t, 100, result.Limit)
	})

	t.Run("CheckAPICallLimit", func(t *testing.T) {
		user := createTestUser("<EMAIL>")
		
		// Create subscription tier with API call limit
		tier := models.SubscriptionTier{
			Name:                "API Test Tier",
			MaxAPICallsPerMonth: 1000,
		}
		err := database.DB.Create(&tier).Error
		require.NoError(t, err)

		// Create active subscription
		subscription := models.Subscription{
			UserID:             user.ID,
			SubscriptionTierID: tier.ID,
			Status:             "active",
			SubscriptionType:   "personal",
		}
		err = database.DB.Create(&subscription).Error
		require.NoError(t, err)

		guard := services.NewSubscriptionGuard()

		// Test with no existing usage
		result, err := guard.CheckAPICallLimit(user.ID)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 0, result.CurrentUsage)
		assert.Equal(t, 1000, result.Limit)

		// Test with unlimited API calls (0 means unlimited)
		err = database.DB.Model(&tier).Update("max_api_calls_per_month", 0).Error
		require.NoError(t, err)

		result, err = guard.CheckAPICallLimit(user.ID)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.True(t, result.Unlimited)
	})
}

// Helper functions for testing
func createTestUser(email string) models.User {
	user := models.User{
		Email: email,
		Name:  "Test User",
		Role:  "user",
	}
	err := database.DB.Create(&user).Error
	if err != nil {
		panic(err)
	}
	return user
}
