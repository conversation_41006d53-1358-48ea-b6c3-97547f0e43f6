package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-credit/backend/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestExchangeRateManagement(t *testing.T) {
	// Set up test environment
	SetupTestDB()
	defer TeardownTestDB()

	gin.SetMode(gin.TestMode)

	// Create test user and shop
	user := models.User{
		Email: "<EMAIL>",
		Name:  "Test User",
		Role:  "user",
	}
	assert.NoError(t, db.Create(&user).Error)

	shop := models.Shop{
		Slug:        "test-shop",
		Name:        "Test Shop",
		Description: "A test shop",
		ShopType:    "retail",
		OwnerUserID: user.ID,
	}
	assert.NoError(t, db.Create(&shop).Error)

	// Test creating exchange rate
	t.Run("Create Exchange Rate", func(t *testing.T) {
		createData := map[string]interface{}{
			"currency_code": "USD",
			"rate":          100.0,
		}
		body, _ := json.Marshal(createData)

		req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/shops/slug/%s/exchange-rates", shop.Slug), bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		// Add user ID to context (simulating authentication middleware)
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("user_id", user.ID)
		c.Params = []gin.Param{{Key: "slug", Value: shop.Slug}}

		// Note: This would normally be called through the router, but we're testing the concept
		// handlers.CreateExchangeRate(c)

		// For now, let's test the model creation directly
		exchangeRate := models.ShopExchangeRate{
			ShopID:       shop.ID,
			CurrencyCode: "USD",
			Rate:         100.0,
			IsActive:     true,
		}
		err := db.Create(&exchangeRate).Error
		assert.NoError(t, err)
		assert.Equal(t, "USD", exchangeRate.CurrencyCode)
		assert.Equal(t, 100.0, exchangeRate.Rate)
		assert.True(t, exchangeRate.IsActive)
	})

	// Test getting exchange rates
	t.Run("Get Exchange Rates", func(t *testing.T) {
		var exchangeRates []models.ShopExchangeRate
		err := db.Where("shop_id = ?", shop.ID).Find(&exchangeRates).Error
		assert.NoError(t, err)
		assert.Greater(t, len(exchangeRates), 0)
	})

	// Test currency conversion logic
	t.Run("Currency Conversion Logic", func(t *testing.T) {
		// Create an exchange rate: 1 USD = 100 credits
		exchangeRate := models.ShopExchangeRate{
			ShopID:       shop.ID,
			CurrencyCode: "USD",
			Rate:         100.0, // 100 credits per 1 USD
			IsActive:     true,
		}
		assert.NoError(t, db.Create(&exchangeRate).Error)

		// Test conversion: 500 credits should equal 5 USD
		credits := 500.0
		expectedUSD := credits / exchangeRate.Rate // 500 / 100 = 5
		assert.Equal(t, 5.0, expectedUSD)

		// Test another rate: 1 THB = 3 credits
		thbRate := models.ShopExchangeRate{
			ShopID:       shop.ID,
			CurrencyCode: "THB",
			Rate:         3.0, // 3 credits per 1 THB
			IsActive:     true,
		}
		assert.NoError(t, db.Create(&thbRate).Error)

		// Test conversion: 150 credits should equal 50 THB
		credits = 150.0
		expectedTHB := credits / thbRate.Rate // 150 / 3 = 50
		assert.Equal(t, 50.0, expectedTHB)
	})

	// Test multiple exchange rates for same shop
	t.Run("Multiple Exchange Rates", func(t *testing.T) {
		currencies := []struct {
			code string
			rate float64
		}{
			{"EUR", 85.0},  // 1 EUR = 85 credits
			{"GBP", 120.0}, // 1 GBP = 120 credits
			{"JPY", 0.8},   // 1 JPY = 0.8 credits
		}

		for _, currency := range currencies {
			rate := models.ShopExchangeRate{
				ShopID:       shop.ID,
				CurrencyCode: currency.code,
				Rate:         currency.rate,
				IsActive:     true,
			}
			assert.NoError(t, db.Create(&rate).Error)
		}

		// Verify all rates were created
		var allRates []models.ShopExchangeRate
		err := db.Where("shop_id = ?", shop.ID).Find(&allRates).Error
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(allRates), 3) // Should have at least EUR, GBP, JPY + previous ones
	})

	// Test updating exchange rate
	t.Run("Update Exchange Rate", func(t *testing.T) {
		// Find an existing rate
		var rate models.ShopExchangeRate
		err := db.Where("shop_id = ? AND currency_code = ?", shop.ID, "USD").First(&rate).Error
		assert.NoError(t, err)

		// Update the rate
		originalRate := rate.Rate
		newRate := 110.0
		rate.Rate = newRate
		err = db.Save(&rate).Error
		assert.NoError(t, err)

		// Verify the update
		var updatedRate models.ShopExchangeRate
		err = db.Where("id = ?", rate.ID).First(&updatedRate).Error
		assert.NoError(t, err)
		assert.Equal(t, newRate, updatedRate.Rate)
		assert.NotEqual(t, originalRate, updatedRate.Rate)
	})

	// Test deactivating exchange rate
	t.Run("Deactivate Exchange Rate", func(t *testing.T) {
		var rate models.ShopExchangeRate
		err := db.Where("shop_id = ? AND currency_code = ?", shop.ID, "EUR").First(&rate).Error
		assert.NoError(t, err)

		// Deactivate the rate
		rate.IsActive = false
		err = db.Save(&rate).Error
		assert.NoError(t, err)

		// Verify deactivation
		var updatedRate models.ShopExchangeRate
		err = db.Where("id = ?", rate.ID).First(&updatedRate).Error
		assert.NoError(t, err)
		assert.False(t, updatedRate.IsActive)
	})
}
