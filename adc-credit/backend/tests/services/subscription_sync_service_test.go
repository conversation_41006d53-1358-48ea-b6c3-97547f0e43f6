package services

import (
	"context"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// SubscriptionSyncServiceTestSuite tests subscription synchronization service
type SubscriptionSyncServiceTestSuite struct {
	suite.Suite
	subscriptionSyncService *services.SubscriptionSyncService
	shopOrgService          *services.ShopOrganizationService
	subscriptionClient      *subscriptionSDK.Client
	ssoClient               *ssoSDK.Client
	testUser                *models.User
	testShop                *models.Shop
	testSubscription        *models.ShopSubscription
	ctx                     context.Context
}

// SetupSuite initializes the test environment
func (suite *SubscriptionSyncServiceTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)
	suite.ssoClient = ssoClient

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)
	suite.subscriptionClient = subscriptionClient

	// Initialize services
	suite.shopOrgService = services.NewShopOrganizationService(database.DB, suite.ssoClient)
	suite.subscriptionSyncService = services.NewSubscriptionSyncService(
		database.DB,
		suite.subscriptionClient,
		suite.shopOrgService,
	)

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Subscription Sync Test User",
		GoogleID: "subsync-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Subscription Sync Shop",
		Slug:    "test-subscription-sync-shop",
		Type:    "api_service",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Create test shop subscription
	suite.testSubscription = &models.ShopSubscription{
		ID:                uuid.New(),
		ShopID:            suite.testShop.ID,
		ADCSubscriptionID: "adc-sub-sync-123",
		ADCOrganizationID: suite.testShop.ID.String(),
		Status:            "active",
		CreditBalance:     1000,
		StartDate:         time.Now(),
	}
	err = database.DB.Create(suite.testSubscription).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleans up after tests
func (suite *SubscriptionSyncServiceTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testSubscription.ID).Delete(&models.ShopSubscription{})
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test individual shop subscription synchronization
func (suite *SubscriptionSyncServiceTestSuite) TestSyncShopSubscription() {
	suite.T().Log("Testing individual shop subscription synchronization...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Test subscription synchronization
	result, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, suite.testShop.ID)

	if err != nil {
		// Service may not be available or no organization exists
		suite.T().Logf("Subscription sync failed (may be expected): %v", err)
		
		// Check if it's a specific type of error we expect
		if strings.Contains(err.Error(), "organization not found") {
			suite.T().Log("Expected error: organization not found")
			return
		}
		
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Service not available for testing")
		}
		
		return
	}

	if result != nil {
		// Verify sync result
		assert.NotNil(suite.T(), result, "Sync result should not be nil")
		assert.Equal(suite.T(), suite.testShop.ID, result.ShopID, "Shop ID should match")
		
		suite.T().Logf("Sync result: %+v", result)

		// Check if local subscription was updated
		if result.LocalUpdated {
			suite.T().Log("Local subscription was updated during sync")
			
			// Verify local subscription changes
			var updatedSubscription models.ShopSubscription
			err = database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
			require.NoError(suite.T(), err)
			
			suite.T().Logf("Updated local subscription: %+v", updatedSubscription)
		}

		// Check if remote subscription was created/updated
		if result.RemoteUpdated {
			suite.T().Log("Remote subscription was updated during sync")
		}

		// Check for conflicts
		if result.ConflictDetected {
			suite.T().Logf("Conflict detected during sync: %s", result.ConflictResolution)
		}
	}
}

// Test bulk subscription synchronization
func (suite *SubscriptionSyncServiceTestSuite) TestSyncAllSubscriptions() {
	suite.T().Log("Testing bulk subscription synchronization...")

	// Create additional test subscriptions
	testShop2 := &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Sync Shop 2",
		Slug:    "test-sync-shop-2",
		Type:    "retail",
	}
	err := database.DB.Create(testShop2).Error
	require.NoError(suite.T(), err)
	defer database.DB.Delete(testShop2)

	testSubscription2 := &models.ShopSubscription{
		ID:                uuid.New(),
		ShopID:            testShop2.ID,
		ADCSubscriptionID: "adc-sub-sync-456",
		ADCOrganizationID: testShop2.ID.String(),
		Status:            "active",
		CreditBalance:     500,
		StartDate:         time.Now(),
	}
	err = database.DB.Create(testSubscription2).Error
	require.NoError(suite.T(), err)
	defer database.DB.Delete(testSubscription2)

	ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
	defer cancel()

	// Test bulk synchronization
	result, err := suite.subscriptionSyncService.SyncAllSubscriptions(ctx)

	if err != nil {
		suite.T().Logf("Bulk sync failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Service not available for bulk sync testing")
		}
		return
	}

	if result != nil {
		// Verify bulk sync result
		assert.NotNil(suite.T(), result, "Bulk sync result should not be nil")
		assert.GreaterOrEqual(suite.T(), result.TotalShops, 2, "Should have at least 2 test shops")
		
		suite.T().Logf("Bulk sync result: %+v", result)

		// Check individual shop results
		if len(result.Results) > 0 {
			for i, shopResult := range result.Results {
				suite.T().Logf("Shop %d sync result: %+v", i+1, shopResult)
				assert.NotEmpty(suite.T(), shopResult.ShopID, "Each shop result should have a shop ID")
			}
		}

		// Verify statistics
		assert.Equal(suite.T(), result.SyncedCount+result.FailedCount+result.SkippedCount, result.TotalShops, 
			"Total counts should add up")
		
		suite.T().Logf("Sync statistics - Total: %d, Synced: %d, Failed: %d, Skipped: %d",
			result.TotalShops, result.SyncedCount, result.FailedCount, result.SkippedCount)
	}
}

// Test incremental synchronization
func (suite *SubscriptionSyncServiceTestSuite) TestIncrementalSync() {
	suite.T().Log("Testing incremental synchronization...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Mark subscription as needing sync (simulate data change)
	now := time.Now()
	err := database.DB.Model(suite.testSubscription).Updates(map[string]interface{}{
		"updated_at": now.Add(-5 * time.Minute), // Set updated time to 5 minutes ago
	}).Error
	require.NoError(suite.T(), err)

	// Test incremental sync (only sync items modified since last sync)
	lastSyncTime := now.Add(-10 * time.Minute) // Sync from 10 minutes ago
	result, err := suite.subscriptionSyncService.SyncSubscriptionsSince(ctx, lastSyncTime)

	if err != nil {
		suite.T().Logf("Incremental sync failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Service not available for incremental sync testing")
		}
		return
	}

	if result != nil {
		suite.T().Logf("Incremental sync result: %+v", result)
		
		// Should have found at least our test subscription that was updated
		assert.GreaterOrEqual(suite.T(), result.TotalShops, 1, "Should have found updated subscriptions")
		
		// Check that the sync targeted the right time period
		if len(result.Results) > 0 {
			foundTestShop := false
			for _, shopResult := range result.Results {
				if shopResult.ShopID == suite.testShop.ID {
					foundTestShop = true
					break
				}
			}
			assert.True(suite.T(), foundTestShop, "Should have found test shop in incremental sync")
		}
	}
}

// Test sync conflict resolution
func (suite *SubscriptionSyncServiceTestSuite) TestSyncConflictResolution() {
	suite.T().Log("Testing sync conflict resolution...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Simulate a conflict by modifying local subscription
	originalStatus := suite.testSubscription.Status
	err := database.DB.Model(suite.testSubscription).Update("status", "pending").Error
	require.NoError(suite.T(), err)

	// Attempt sync which may detect conflict with remote state
	result, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, suite.testShop.ID)

	if err != nil {
		suite.T().Logf("Conflict resolution test failed (may be expected): %v", err)
		// Restore original status
		database.DB.Model(suite.testSubscription).Update("status", originalStatus)
		return
	}

	if result != nil && result.ConflictDetected {
		suite.T().Logf("Conflict detected and resolved: %s", result.ConflictResolution)
		
		// Verify conflict was resolved
		assert.NotEmpty(suite.T(), result.ConflictResolution, "Conflict resolution should be documented")
		
		// Check final state of local subscription
		var finalSubscription models.ShopSubscription
		err = database.DB.Where("shop_id = ?", suite.testShop.ID).First(&finalSubscription).Error
		require.NoError(suite.T(), err)
		
		suite.T().Logf("Final subscription state after conflict resolution: %+v", finalSubscription)
	} else {
		suite.T().Log("No conflict detected or sync failed")
		// Restore original status
		database.DB.Model(suite.testSubscription).Update("status", originalStatus)
	}
}

// Test sync with non-existent shop
func (suite *SubscriptionSyncServiceTestSuite) TestSyncNonExistentShop() {
	suite.T().Log("Testing sync with non-existent shop...")

	ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
	defer cancel()

	nonExistentShopID := uuid.New()
	result, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, nonExistentShopID)

	// Should handle non-existent shop gracefully
	assert.Error(suite.T(), err, "Should error for non-existent shop")
	assert.Nil(suite.T(), result, "Should not return result for non-existent shop")
	
	suite.T().Logf("Expected error for non-existent shop: %v", err)
}

// Test sync with service failures
func (suite *SubscriptionSyncServiceTestSuite) TestSyncWithServiceFailures() {
	suite.T().Log("Testing sync with service failures...")

	// Test 1: Timeout scenario
	suite.T().Run("Timeout_Scenario", func(t *testing.T) {
		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(suite.ctx, 100*time.Millisecond)
		defer cancel()

		result, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, suite.testShop.ID)
		
		if err != nil {
			t.Logf("Expected timeout or quick completion: %v", err)
			if strings.Contains(err.Error(), "context deadline exceeded") {
				t.Log("Timeout handled correctly")
			}
		}
		
		if result != nil {
			t.Log("Sync completed quickly")
		}
	})

	// Test 2: Service unavailable scenario
	suite.T().Run("Service_Unavailable", func(t *testing.T) {
		// Create service with invalid URL
		invalidClient, err := subscriptionSDK.NewClient("http://invalid-service:9999")
		require.NoError(t, err)

		invalidSyncService := services.NewSubscriptionSyncService(
			database.DB,
			invalidClient,
			suite.shopOrgService,
		)

		ctx, cancel := context.WithTimeout(suite.ctx, 5*time.Second)
		defer cancel()

		result, err := invalidSyncService.SyncShopSubscription(ctx, suite.testShop.ID)
		
		assert.Error(t, err, "Should error with invalid service")
		assert.Nil(t, result, "Should not return result with invalid service")
		t.Logf("Expected service error: %v", err)
	})
}

// Test concurrent sync operations
func (suite *SubscriptionSyncServiceTestSuite) TestConcurrentSyncOperations() {
	suite.T().Log("Testing concurrent sync operations...")

	const numOperations = 3
	results := make(chan error, numOperations)

	// Start concurrent sync operations
	for i := 0; i < numOperations; i++ {
		go func() {
			ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
			defer cancel()

			_, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, suite.testShop.ID)
			results <- err
		}()
	}

	// Collect results
	var errors []error
	for i := 0; i < numOperations; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		suite.T().Logf("Some concurrent syncs failed (may be expected): %v", errors)
	} else {
		suite.T().Log("All concurrent syncs succeeded")
	}

	// At least some should succeed if service is available
	successRate := float64(numOperations-len(errors)) / float64(numOperations)
	suite.T().Logf("Concurrent sync success rate: %.2f%% (%d/%d)", 
		successRate*100, numOperations-len(errors), numOperations)
}

// Test sync metrics and monitoring
func (suite *SubscriptionSyncServiceTestSuite) TestSyncMetrics() {
	suite.T().Log("Testing sync metrics and monitoring...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Get sync metrics before operation
	beforeMetrics, err := suite.subscriptionSyncService.GetSyncMetrics(ctx)
	if err != nil {
		suite.T().Logf("Failed to get metrics (feature may not be implemented): %v", err)
		return
	}

	suite.T().Logf("Metrics before sync: %+v", beforeMetrics)

	// Perform a sync operation
	_, err = suite.subscriptionSyncService.SyncShopSubscription(ctx, suite.testShop.ID)
	if err != nil {
		suite.T().Logf("Sync failed: %v", err)
		return
	}

	// Get sync metrics after operation
	afterMetrics, err := suite.subscriptionSyncService.GetSyncMetrics(ctx)
	if err != nil {
		suite.T().Logf("Failed to get metrics after sync: %v", err)
		return
	}

	suite.T().Logf("Metrics after sync: %+v", afterMetrics)

	// Verify metrics were updated
	if beforeMetrics != nil && afterMetrics != nil {
		// Check that sync count increased
		if beforeCount, ok := beforeMetrics["total_syncs"].(int64); ok {
			if afterCount, ok := afterMetrics["total_syncs"].(int64); ok {
				assert.GreaterOrEqual(suite.T(), afterCount, beforeCount, "Total sync count should increase")
			}
		}
	}
}

// TestSubscriptionSyncServiceIntegration runs the subscription sync service integration test suite
func TestSubscriptionSyncServiceIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping subscription sync service integration tests in short mode")
	}

	suite.Run(t, new(SubscriptionSyncServiceTestSuite))
}