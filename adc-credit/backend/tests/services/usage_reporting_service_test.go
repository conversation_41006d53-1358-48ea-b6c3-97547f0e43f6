package services

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// UsageReportingServiceTestSuite tests usage reporting service integration
type UsageReportingServiceTestSuite struct {
	suite.Suite
	usageReportingService *services.UsageReportingService
	subscriptionClient    *subscriptionSDK.Client
	analyticsClient       *analyticsSDK.Client
	testUser              *models.User
	testShop              *models.Shop
	testSubscription      *models.ShopSubscription
	ctx                   context.Context
}

// SetupSuite initializes the test environment
func (suite *UsageReportingServiceTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize service clients
	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)
	suite.subscriptionClient = subscriptionClient

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)
	suite.analyticsClient = analyticsClient

	// Initialize usage reporting service
	suite.usageReportingService = services.NewUsageReportingService(
		database.DB,
		suite.subscriptionClient,
		suite.analyticsClient,
	)

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Usage Test User",
		GoogleID: "usage-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Usage Shop",
		Slug:    "test-usage-shop",
		Type:    "api_service",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Create test shop subscription
	suite.testSubscription = &models.ShopSubscription{
		ID:                uuid.New(),
		ShopID:            suite.testShop.ID,
		ADCSubscriptionID: "adc-sub-usage-123",
		ADCOrganizationID: suite.testShop.ID.String(),
		Status:            "active",
		CreditBalance:     1000,
		StartDate:         time.Now(),
	}
	err = database.DB.Create(suite.testSubscription).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleans up after tests
func (suite *UsageReportingServiceTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testSubscription.ID).Delete(&models.ShopSubscription{})
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test usage event tracking
func (suite *UsageReportingServiceTestSuite) TestTrackUsageEvent() {
	suite.T().Log("Testing usage event tracking...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Create usage event
	usageEvent := &models.UsageEvent{
		ShopID:        suite.testShop.ID,
		EventType:     "api_call",
		EventCategory: "credit_consumption",
		Quantity:      10,
		Metadata: map[string]interface{}{
			"endpoint":    "/api/v1/credits/consume",
			"user_agent": "Test Client",
			"ip_address": "127.0.0.1",
		},
	}

	// Track usage event
	err := suite.usageReportingService.TrackUsageEvent(ctx, usageEvent)

	if err != nil {
		suite.T().Logf("Usage event tracking failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Analytics service not available for testing")
		}
		return
	}

	suite.T().Log("Usage event tracked successfully")

	// Verify event was stored locally
	var storedEvent models.UsageEvent
	err = database.DB.Where("shop_id = ? AND event_type = ?", suite.testShop.ID, "api_call").First(&storedEvent).Error
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), usageEvent.ShopID, storedEvent.ShopID)
	assert.Equal(suite.T(), usageEvent.EventType, storedEvent.EventType)
	assert.Equal(suite.T(), usageEvent.Quantity, storedEvent.Quantity)

	suite.T().Logf("Stored usage event: %+v", storedEvent)
}

// Test batch usage event reporting
func (suite *UsageReportingServiceTestSuite) TestBatchUsageReporting() {
	suite.T().Log("Testing batch usage event reporting...")

	ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
	defer cancel()

	// Create multiple usage events
	usageEvents := []*models.UsageEvent{
		{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "credit_consumption",
			Quantity:      5,
			Metadata: map[string]interface{}{
				"endpoint": "/api/v1/credits/balance",
			},
		},
		{
			ShopID:        suite.testShop.ID,
			EventType:     "qr_generation",
			EventCategory: "credit_generation",
			Quantity:      1,
			Metadata: map[string]interface{}{
				"code_type": "merchant",
			},
		},
		{
			ShopID:        suite.testShop.ID,
			EventType:     "customer_creation",
			EventCategory: "shop_management",
			Quantity:      1,
			Metadata: map[string]interface{}{
				"customer_type": "regular",
			},
		},
	}

	// Track events in batch
	for _, event := range usageEvents {
		err := database.DB.Create(event).Error
		require.NoError(suite.T(), err)
	}

	// Report batch to ADC services
	result, err := suite.usageReportingService.ReportUsageBatch(ctx, suite.testShop.ID, time.Now().Add(-1*time.Hour), time.Now())

	if err != nil {
		suite.T().Logf("Batch usage reporting failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Service not available for batch reporting testing")
		}
		return
	}

	if result != nil {
		suite.T().Logf("Batch usage reporting result: %+v", result)
		assert.GreaterOrEqual(suite.T(), result.EventsReported, 3, "Should have reported at least 3 events")
		assert.Equal(suite.T(), suite.testShop.ID, result.ShopID, "Shop ID should match")
	}
}

// Test usage analytics retrieval
func (suite *UsageReportingServiceTestSuite) TestGetUsageAnalytics() {
	suite.T().Log("Testing usage analytics retrieval...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Create some test usage data
	testEvents := []*models.UsageEvent{
		{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "credit_consumption",
			Quantity:      25,
			CreatedAt:     time.Now().Add(-2 * time.Hour),
		},
		{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "credit_consumption",
			Quantity:      15,
			CreatedAt:     time.Now().Add(-1 * time.Hour),
		},
	}

	for _, event := range testEvents {
		err := database.DB.Create(event).Error
		require.NoError(suite.T(), err)
	}

	// Get usage analytics
	analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, suite.testShop.ID, time.Now().Add(-24*time.Hour), time.Now())

	if err != nil {
		suite.T().Logf("Usage analytics retrieval failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Analytics service not available for testing")
		}
		return
	}

	if analytics != nil {
		suite.T().Logf("Usage analytics: %+v", analytics)
		
		// Verify analytics data
		assert.Equal(suite.T(), suite.testShop.ID, analytics.ShopID, "Shop ID should match")
		assert.GreaterOrEqual(suite.T(), analytics.TotalEvents, int64(2), "Should have at least 2 events")
		assert.GreaterOrEqual(suite.T(), analytics.TotalQuantity, int64(40), "Should have total quantity of at least 40")
		
		// Check event type breakdown
		if len(analytics.EventTypeBreakdown) > 0 {
			suite.T().Log("Event type breakdown:")
			for eventType, count := range analytics.EventTypeBreakdown {
				suite.T().Logf("  %s: %d", eventType, count)
			}
		}
	}
}

// Test usage metrics aggregation
func (suite *UsageReportingServiceTestSuite) TestUsageMetricsAggregation() {
	suite.T().Log("Testing usage metrics aggregation...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Create test data with different time periods
	now := time.Now()
	testEvents := []*models.UsageEvent{
		// Today's events
		{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "credit_consumption",
			Quantity:      10,
			CreatedAt:     now.Add(-2 * time.Hour),
		},
		{
			ShopID:        suite.testShop.ID,
			EventType:     "qr_generation",
			EventCategory: "credit_generation",
			Quantity:      2,
			CreatedAt:     now.Add(-1 * time.Hour),
		},
		// Yesterday's events
		{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "credit_consumption",
			Quantity:      20,
			CreatedAt:     now.Add(-25 * time.Hour),
		},
	}

	for _, event := range testEvents {
		err := database.DB.Create(event).Error
		require.NoError(suite.T(), err)
	}

	// Test daily aggregation
	dailyMetrics, err := suite.usageReportingService.GetDailyUsageMetrics(ctx, suite.testShop.ID, now.Add(-48*time.Hour), now)

	if err != nil {
		suite.T().Logf("Daily metrics aggregation failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Service not available for metrics aggregation testing")
		}
		return
	}

	if dailyMetrics != nil {
		suite.T().Logf("Daily usage metrics: %+v", dailyMetrics)
		
		// Should have metrics for at least 2 days
		assert.GreaterOrEqual(suite.T(), len(dailyMetrics.DailyBreakdown), 1, "Should have daily breakdown")
		
		// Verify total metrics
		assert.GreaterOrEqual(suite.T(), dailyMetrics.TotalEvents, int64(3), "Should have total events")
		assert.GreaterOrEqual(suite.T(), dailyMetrics.TotalQuantity, int64(32), "Should have total quantity")
	}
}

// Test usage limit monitoring
func (suite *UsageReportingServiceTestSuite) TestUsageLimitMonitoring() {
	suite.T().Log("Testing usage limit monitoring...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Check current usage against subscription limits
	limitStatus, err := suite.usageReportingService.CheckUsageLimits(ctx, suite.testShop.ID)

	if err != nil {
		suite.T().Logf("Usage limit monitoring failed (may be expected): %v", err)
		if strings.Contains(err.Error(), "connection") || strings.Contains(err.Error(), "timeout") {
			suite.T().Skip("Subscription service not available for limit monitoring")
		}
		return
	}

	if limitStatus != nil {
		suite.T().Logf("Usage limit status: %+v", limitStatus)
		
		// Verify limit status structure
		assert.Equal(suite.T(), suite.testShop.ID, limitStatus.ShopID, "Shop ID should match")
		assert.NotNil(suite.T(), limitStatus.Limits, "Should have limits information")
		assert.NotNil(suite.T(), limitStatus.CurrentUsage, "Should have current usage information")
		
		// Check if we're within limits
		if limitStatus.WithinLimits {
			suite.T().Log("Shop is within usage limits")
		} else {
			suite.T().Log("Shop has exceeded usage limits")
			suite.T().Logf("Exceeded limits: %+v", limitStatus.ExceededLimits)
		}
	}
}

// Test usage event validation
func (suite *UsageReportingServiceTestSuite) TestUsageEventValidation() {
	suite.T().Log("Testing usage event validation...")

	ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
	defer cancel()

	// Test 1: Invalid event type
	suite.T().Run("Invalid_Event_Type", func(t *testing.T) {
		invalidEvent := &models.UsageEvent{
			ShopID:        suite.testShop.ID,
			EventType:     "", // Empty event type
			EventCategory: "test",
			Quantity:      1,
		}

		err := suite.usageReportingService.TrackUsageEvent(ctx, invalidEvent)
		assert.Error(t, err, "Should error with invalid event type")
		t.Logf("Expected validation error: %v", err)
	})

	// Test 2: Negative quantity
	suite.T().Run("Negative_Quantity", func(t *testing.T) {
		invalidEvent := &models.UsageEvent{
			ShopID:        suite.testShop.ID,
			EventType:     "api_call",
			EventCategory: "test",
			Quantity:      -5, // Negative quantity
		}

		err := suite.usageReportingService.TrackUsageEvent(ctx, invalidEvent)
		assert.Error(t, err, "Should error with negative quantity")
		t.Logf("Expected validation error: %v", err)
	})

	// Test 3: Missing shop ID
	suite.T().Run("Missing_Shop_ID", func(t *testing.T) {
		invalidEvent := &models.UsageEvent{
			ShopID:        uuid.Nil, // Empty shop ID
			EventType:     "api_call",
			EventCategory: "test",
			Quantity:      1,
		}

		err := suite.usageReportingService.TrackUsageEvent(ctx, invalidEvent)
		assert.Error(t, err, "Should error with missing shop ID")
		t.Logf("Expected validation error: %v", err)
	})
}

// Test concurrent usage tracking
func (suite *UsageReportingServiceTestSuite) TestConcurrentUsageTracking() {
	suite.T().Log("Testing concurrent usage tracking...")

	const numConcurrentEvents = 5
	results := make(chan error, numConcurrentEvents)

	// Start concurrent usage tracking operations
	for i := 0; i < numConcurrentEvents; i++ {
		go func(index int) {
			ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
			defer cancel()

			usageEvent := &models.UsageEvent{
				ShopID:        suite.testShop.ID,
				EventType:     "concurrent_api_call",
				EventCategory: "test",
				Quantity:      1,
				Metadata: map[string]interface{}{
					"test_index": index,
				},
			}

			err := suite.usageReportingService.TrackUsageEvent(ctx, usageEvent)
			results <- err
		}(i)
	}

	// Collect results
	var errors []error
	for i := 0; i < numConcurrentEvents; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		suite.T().Logf("Some concurrent tracking failed (may be expected): %v", errors)
	} else {
		suite.T().Log("All concurrent tracking succeeded")
	}

	// Verify that events were tracked
	var eventCount int64
	err := database.DB.Model(&models.UsageEvent{}).
		Where("shop_id = ? AND event_type = ?", suite.testShop.ID, "concurrent_api_call").
		Count(&eventCount).Error
	require.NoError(suite.T(), err)

	suite.T().Logf("Tracked %d concurrent events", eventCount)
	assert.GreaterOrEqual(suite.T(), eventCount, int64(numConcurrentEvents-len(errors)), 
		"Should have tracked most concurrent events")
}

// Test usage reporting with different time ranges
func (suite *UsageReportingServiceTestSuite) TestUsageReportingTimeRanges() {
	suite.T().Log("Testing usage reporting with different time ranges...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Create events across different time periods
	now := time.Now()
	timeRanges := []struct {
		name     string
		time     time.Time
		quantity int
	}{
		{"1_hour_ago", now.Add(-1 * time.Hour), 10},
		{"6_hours_ago", now.Add(-6 * time.Hour), 15},
		{"1_day_ago", now.Add(-24 * time.Hour), 20},
		{"1_week_ago", now.Add(-7 * 24 * time.Hour), 25},
	}

	for _, tr := range timeRanges {
		event := &models.UsageEvent{
			ShopID:        suite.testShop.ID,
			EventType:     "time_range_test",
			EventCategory: "test",
			Quantity:      tr.quantity,
			CreatedAt:     tr.time,
		}
		err := database.DB.Create(event).Error
		require.NoError(suite.T(), err)
	}

	// Test different time range retrievals
	testCases := []struct {
		name      string
		startTime time.Time
		endTime   time.Time
		expected  int
	}{
		{"Last_2_Hours", now.Add(-2 * time.Hour), now, 1},
		{"Last_12_Hours", now.Add(-12 * time.Hour), now, 2},
		{"Last_2_Days", now.Add(-48 * time.Hour), now, 3},
		{"Last_Week", now.Add(-8 * 24 * time.Hour), now, 4},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, suite.testShop.ID, tc.startTime, tc.endTime)
			
			if err != nil {
				t.Logf("Analytics failed for %s: %v", tc.name, err)
				return
			}

			if analytics != nil {
				t.Logf("%s analytics: Events=%d, Quantity=%d", tc.name, analytics.TotalEvents, analytics.TotalQuantity)
				// Note: We don't assert exact counts because other tests may have created events
				// We just verify the service is working
			}
		})
	}
}

// Test usage reporting error scenarios
func (suite *UsageReportingServiceTestSuite) TestUsageReportingErrorScenarios() {
	suite.T().Log("Testing usage reporting error scenarios...")

	// Test 1: Non-existent shop
	suite.T().Run("NonExistent_Shop", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		nonExistentShopID := uuid.New()
		analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, nonExistentShopID, time.Now().Add(-1*time.Hour), time.Now())

		assert.Error(t, err, "Should error for non-existent shop")
		assert.Nil(t, analytics, "Should not return analytics for non-existent shop")
		t.Logf("Expected error for non-existent shop: %v", err)
	})

	// Test 2: Invalid time range
	suite.T().Run("Invalid_Time_Range", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// End time before start time
		startTime := time.Now()
		endTime := startTime.Add(-1 * time.Hour)

		analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, suite.testShop.ID, startTime, endTime)

		assert.Error(t, err, "Should error with invalid time range")
		assert.Nil(t, analytics, "Should not return analytics with invalid time range")
		t.Logf("Expected error for invalid time range: %v", err)
	})

	// Test 3: Service timeout
	suite.T().Run("Service_Timeout", func(t *testing.T) {
		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(suite.ctx, 1*time.Millisecond)
		defer cancel()

		analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, suite.testShop.ID, time.Now().Add(-1*time.Hour), time.Now())

		if err != nil {
			t.Logf("Expected timeout or quick completion: %v", err)
			if strings.Contains(err.Error(), "context deadline exceeded") {
				t.Log("Timeout handled correctly")
			}
		}

		if analytics != nil {
			t.Log("Analytics completed quickly")
		}
	})
}

// Test usage reporting metrics and monitoring
func (suite *UsageReportingServiceTestSuite) TestUsageReportingMetrics() {
	suite.T().Log("Testing usage reporting metrics and monitoring...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Get reporting metrics
	metrics, err := suite.usageReportingService.GetReportingMetrics(ctx)
	if err != nil {
		suite.T().Logf("Failed to get reporting metrics (feature may not be implemented): %v", err)
		return
	}

	if metrics != nil {
		suite.T().Logf("Usage reporting metrics: %+v", metrics)

		// Verify metrics structure
		if totalReports, ok := metrics["total_reports"]; ok {
			suite.T().Logf("Total reports: %v", totalReports)
		}

		if failedReports, ok := metrics["failed_reports"]; ok {
			suite.T().Logf("Failed reports: %v", failedReports)
		}

		if avgResponseTime, ok := metrics["avg_response_time_ms"]; ok {
			suite.T().Logf("Average response time: %v ms", avgResponseTime)
		}
	}
}

// TestUsageReportingServiceIntegration runs the usage reporting service integration test suite
func TestUsageReportingServiceIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping usage reporting service integration tests in short mode")
	}

	suite.Run(t, new(UsageReportingServiceTestSuite))
}