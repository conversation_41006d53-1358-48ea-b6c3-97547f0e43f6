package services

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	ssoSDK "github.com/adc-sso-service/sdk"
)

// ShopOrganizationServiceTestSuite tests shop-to-organization mapping service
type ShopOrganizationServiceTestSuite struct {
	suite.Suite
	shopOrgService *services.ShopOrganizationService
	ssoClient      *ssoSDK.Client
	testUser       *models.User
	testShop       *models.Shop
	ctx            context.Context
}

// SetupSuite initializes the test environment
func (suite *ShopOrganizationServiceTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize SSO client
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)
	suite.ssoClient = ssoClient

	// Initialize shop organization service
	suite.shopOrgService = services.NewShopOrganizationService(database.DB, suite.ssoClient)

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Shop Org Test User",
		GoogleID: "shoporg-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Shop Organization",
		Slug:    "test-shop-organization",
		Type:    "api_service",
		Website: "https://testshop.example.com",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleans up after tests
func (suite *ShopOrganizationServiceTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test organization creation for shop
func (suite *ShopOrganizationServiceTestSuite) TestCreateOrganizationForShop() {
	suite.T().Log("Testing organization creation for shop...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Test organization creation
	org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, suite.testShop, suite.testUser.ID)

	if err != nil {
		// Service may not be available or authentication required
		suite.T().Logf("Organization creation failed (may be expected): %v", err)
		suite.T().Skip("SSO service not available or authentication required")
	}

	if org != nil {
		// Verify organization was created correctly
		assert.NotEmpty(suite.T(), org.ID, "Organization should have an ID")
		assert.Equal(suite.T(), suite.testShop.Name, org.Name, "Organization name should match shop name")
		assert.Equal(suite.T(), suite.testShop.ID.String(), org.ExternalID, "External ID should match shop ID")
		assert.NotNil(suite.T(), org.Settings, "Organization should have settings")

		suite.T().Logf("Created organization: %+v", org)

		// Verify settings are properly set
		if settings, ok := org.Settings.(map[string]interface{}); ok {
			assert.Equal(suite.T(), "credit_service", settings["shop_type"])
			assert.Equal(suite.T(), "adc_credit_service", settings["created_from"])
		}

		// Cleanup: Delete the created organization
		err = suite.ssoClient.DeleteOrganization(ctx, org.ID)
		if err != nil {
			suite.T().Logf("Warning: Failed to cleanup test organization: %v", err)
		}
	}
}

// Test retrieving organization for shop
func (suite *ShopOrganizationServiceTestSuite) TestGetOrganizationForShop() {
	suite.T().Log("Testing get organization for shop...")

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// First, try to create an organization
	createdOrg, err := suite.shopOrgService.CreateOrganizationForShop(ctx, suite.testShop, suite.testUser.ID)
	if err != nil {
		suite.T().Logf("Organization creation failed: %v", err)
		suite.T().Skip("Cannot test retrieval without creation")
	}

	if createdOrg != nil {
		defer func() {
			// Cleanup
			err := suite.ssoClient.DeleteOrganization(ctx, createdOrg.ID)
			if err != nil {
				suite.T().Logf("Warning: Failed to cleanup test organization: %v", err)
			}
		}()

		// Now test retrieval
		retrievedOrg, err := suite.shopOrgService.GetOrganizationForShop(ctx, suite.testShop.ID)
		
		if err != nil {
			suite.T().Logf("Organization retrieval failed: %v", err)
			return
		}

		if retrievedOrg != nil {
			// Verify retrieved organization matches created one
			assert.Equal(suite.T(), createdOrg.ID, retrievedOrg.ID, "Retrieved organization ID should match")
			assert.Equal(suite.T(), createdOrg.ExternalID, retrievedOrg.ExternalID, "External ID should match")
			assert.Equal(suite.T(), suite.testShop.ID.String(), retrievedOrg.ExternalID, "External ID should be shop ID")

			suite.T().Logf("Retrieved organization: %+v", retrievedOrg)
		}
	}
}

// Test organization creation with different shop types
func (suite *ShopOrganizationServiceTestSuite) TestCreateOrganizationWithDifferentShopTypes() {
	suite.T().Log("Testing organization creation with different shop types...")

	shopTypes := []string{"retail", "api_service", "enterprise"}

	for _, shopType := range shopTypes {
		suite.T().Run("ShopType_"+shopType, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
			defer cancel()

			// Create shop of specific type
			testShop := &models.Shop{
				ID:      uuid.New(),
				OwnerID: suite.testUser.ID,
				Name:    "Test " + shopType + " Shop",
				Slug:    "test-" + shopType + "-shop",
				Type:    shopType,
			}
			err := database.DB.Create(testShop).Error
			require.NoError(t, err)
			defer database.DB.Delete(testShop)

			// Create organization for this shop type
			org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, testShop, suite.testUser.ID)
			if err != nil {
				t.Logf("Organization creation failed for %s shop: %v", shopType, err)
				t.Skip("SSO service not available")
			}

			if org != nil {
				// Verify organization settings reflect shop type
				assert.Equal(t, testShop.Name, org.Name)
				assert.Equal(t, testShop.ID.String(), org.ExternalID)

				if settings, ok := org.Settings.(map[string]interface{}); ok {
					assert.Equal(t, "credit_service", settings["shop_type"])
				}

				t.Logf("Created %s organization: %+v", shopType, org)

				// Cleanup
				err = suite.ssoClient.DeleteOrganization(ctx, org.ID)
				if err != nil {
					t.Logf("Warning: Failed to cleanup %s organization: %v", shopType, err)
				}
			}
		})
	}
}

// Test organization creation error scenarios
func (suite *ShopOrganizationServiceTestSuite) TestOrganizationCreationErrorScenarios() {
	suite.T().Log("Testing organization creation error scenarios...")

	// Test 1: Nil shop
	suite.T().Run("Nil_Shop", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, nil, suite.testUser.ID)
		assert.Error(t, err, "Should error with nil shop")
		assert.Nil(t, org, "Should not return organization")
	})

	// Test 2: Empty user ID
	suite.T().Run("Empty_User_ID", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, suite.testShop, uuid.Nil)
		assert.Error(t, err, "Should error with empty user ID")
		assert.Nil(t, org, "Should not return organization")
	})

	// Test 3: Context timeout
	suite.T().Run("Context_Timeout", func(t *testing.T) {
		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(suite.ctx, 1*time.Millisecond)
		defer cancel()

		org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, suite.testShop, suite.testUser.ID)
		
		// Should either timeout or complete quickly
		if err != nil {
			t.Logf("Expected timeout or quick completion: %v", err)
		}
		
		if org != nil {
			// If it succeeded quickly, clean up
			cleanupCtx, cleanupCancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cleanupCancel()
			suite.ssoClient.DeleteOrganization(cleanupCtx, org.ID)
		}
	})
}

// Test organization retrieval with non-existent shop
func (suite *ShopOrganizationServiceTestSuite) TestGetOrganizationForNonExistentShop() {
	suite.T().Log("Testing get organization for non-existent shop...")

	ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
	defer cancel()

	nonExistentShopID := uuid.New()
	org, err := suite.shopOrgService.GetOrganizationForShop(ctx, nonExistentShopID)

	// Should handle non-existent shop gracefully
	if err != nil {
		suite.T().Logf("Expected error for non-existent shop: %v", err)
		assert.Error(suite.T(), err, "Should error for non-existent shop")
	}
	
	assert.Nil(suite.T(), org, "Should not return organization for non-existent shop")
}

// Test concurrent organization operations
func (suite *ShopOrganizationServiceTestSuite) TestConcurrentOrganizationOperations() {
	suite.T().Log("Testing concurrent organization operations...")

	const numOperations = 3
	results := make(chan error, numOperations)

	// Create multiple shops for concurrent testing
	testShops := make([]*models.Shop, numOperations)
	for i := 0; i < numOperations; i++ {
		testShops[i] = &models.Shop{
			ID:      uuid.New(),
			OwnerID: suite.testUser.ID,
			Name:    fmt.Sprintf("Concurrent Test Shop %d", i+1),
			Slug:    fmt.Sprintf("concurrent-test-shop-%d", i+1),
			Type:    "api_service",
		}
		err := database.DB.Create(testShops[i]).Error
		require.NoError(suite.T(), err)
		defer database.DB.Delete(testShops[i])
	}

	// Start concurrent organization creation operations
	for i := 0; i < numOperations; i++ {
		go func(shop *models.Shop) {
			ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
			defer cancel()

			org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, shop, suite.testUser.ID)
			if err == nil && org != nil {
				// Cleanup
				suite.ssoClient.DeleteOrganization(ctx, org.ID)
			}
			results <- err
		}(testShops[i])
	}

	// Collect results
	var errors []error
	for i := 0; i < numOperations; i++ {
		if err := <-results; err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		suite.T().Logf("Some concurrent operations failed (may be expected): %v", errors)
	} else {
		suite.T().Log("All concurrent operations succeeded")
	}

	// At least some should succeed if service is available
	successRate := float64(numOperations-len(errors)) / float64(numOperations)
	suite.T().Logf("Success rate: %.2f%% (%d/%d)", successRate*100, numOperations-len(errors), numOperations)
}

// Test service integration with different authentication states
func (suite *ShopOrganizationServiceTestSuite) TestServiceIntegrationWithAuth() {
	suite.T().Log("Testing service integration with different authentication states...")

	// Test 1: Service available but unauthenticated
	suite.T().Run("Service_Available_Unauthenticated", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// This test verifies that the service handles authentication errors gracefully
		org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, suite.testShop, suite.testUser.ID)
		
		if err != nil {
			// Check if it's an authentication error
			if strings.Contains(err.Error(), "auth") || strings.Contains(err.Error(), "401") || strings.Contains(err.Error(), "403") {
				t.Log("Service correctly returned authentication error")
			} else {
				t.Logf("Service returned other error (may be connection issue): %v", err)
			}
		} else if org != nil {
			t.Log("Service allowed organization creation (has authentication)")
			// Cleanup
			suite.ssoClient.DeleteOrganization(ctx, org.ID)
		}
	})

	// Test 2: Service health check
	suite.T().Run("Service_Health_Check", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		health, err := suite.ssoClient.HealthCheck(ctx)
		if err != nil {
			t.Logf("Health check failed (service may not be available): %v", err)
		} else {
			t.Logf("Service health: %+v", health)
			assert.True(t, health.Healthy, "Service should be healthy")
		}
	})
}

// TestShopOrganizationServiceIntegration runs the shop organization service integration test suite
func TestShopOrganizationServiceIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping shop organization service integration tests in short mode")
	}

	suite.Run(t, new(ShopOrganizationServiceTestSuite))
}