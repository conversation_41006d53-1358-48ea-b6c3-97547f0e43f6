package tests

import (
	"os"
	"testing"

	testutils "github.com/adc-credit/backend/internal/testing"
)

// TestMain sets up and tears down the test environment for all tests
func TestMain(m *testing.M) {
	// Setup test environment
	if err := testutils.SetupTestEnvironment(); err != nil {
		panic("Failed to setup test environment: " + err.Error())
	}

	// Run all tests
	code := m.Run()

	// Cleanup test environment
	testutils.CleanupTestEnvironment()

	// Exit with the test result code
	os.Exit(code)
}
