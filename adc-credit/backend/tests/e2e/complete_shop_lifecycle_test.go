package e2e

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// CompleteShopLifecycleTestSuite tests the complete shop lifecycle with ADC integration
type CompleteShopLifecycleTestSuite struct {
	suite.Suite
	router           *gin.Engine
	serviceContainer *services.ServiceContainer
	ssoClient        *ssoSDK.Client
	subscriptionClient *subscriptionSDK.Client
	analyticsClient  *analyticsSDK.Client
	testUser         *models.User
	testShop         *models.Shop
	authToken        string
	ctx              context.Context
}

// SetupSuite initializes the complete test environment
func (suite *CompleteShopLifecycleTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize ADC service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)
	suite.ssoClient = ssoClient

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)
	suite.subscriptionClient = subscriptionClient

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)
	suite.analyticsClient = analyticsClient

	// Initialize service container
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		suite.subscriptionClient,
		suite.analyticsClient,
		suite.ssoClient,
	)

	// Setup router with all handlers
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())
	
	// Setup authentication middleware (mock for testing)
	suite.router.Use(func(c *gin.Context) {
		if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			// Mock authentication - in real scenario this would validate JWT
			c.Set("user_id", suite.testUser.ID.String())
			c.Set("authenticated", true)
		}
		c.Next()
	})

	// Initialize all handlers
	shopHandler := handlers.NewShopHandler(suite.serviceContainer)
	creditV2Handler := handlers.NewCreditV2Handler(suite.serviceContainer)
	adcSyncHandler := handlers.NewADCSyncHandler(suite.serviceContainer)
	stripeWebhookV2Handler := handlers.NewStripeWebhookV2Handler(suite.serviceContainer)

	// Shop management routes
	shopRoutes := suite.router.Group("/api/v1/shops")
	{
		shopRoutes.POST("/", shopHandler.CreateShop)
		shopRoutes.GET("/:slug", shopHandler.GetShopBySlug)
		shopRoutes.PUT("/:slug", shopHandler.UpdateShop)
		shopRoutes.DELETE("/:slug", shopHandler.DeleteShop)
		shopRoutes.GET("/:id/customers", shopHandler.GetShopCustomers)
		shopRoutes.POST("/:id/customers", shopHandler.CreateCustomer)
	}

	// Credit management routes
	creditRoutes := suite.router.Group("/api/v2/credits")
	{
		creditRoutes.GET("/balance", creditV2Handler.GetCreditBalance)
		creditRoutes.POST("/consume", creditV2Handler.ConsumeCredits)
		creditRoutes.GET("/usage", creditV2Handler.GetUsageHistory)
		creditRoutes.GET("/analytics", creditV2Handler.GetUsageAnalytics)
	}

	// ADC synchronization routes
	adcRoutes := suite.router.Group("/api/v1/adc-sync")
	{
		adcRoutes.POST("/shops/:shop_id", adcSyncHandler.SyncShopToADC)
		adcRoutes.POST("/subscriptions/bulk", adcSyncHandler.BulkSyncSubscriptions)
		adcRoutes.GET("/status", adcSyncHandler.GetSyncStatus)
	}

	// Webhook routes
	suite.router.POST("/webhook/stripe/v2", stripeWebhookV2Handler.HandleStripeWebhookV2)

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "E2E Test User",
		GoogleID: "e2e-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Generate auth token for testing
	suite.authToken = "test-jwt-token-12345"
}

// TearDownSuite cleans up after tests
func (suite *CompleteShopLifecycleTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		if suite.testShop != nil {
			database.DB.Where("shop_id = ?", suite.testShop.ID).Delete(&models.ShopSubscription{})
			database.DB.Where("shop_id = ?", suite.testShop.ID).Delete(&models.UsageEvent{})
			database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		}
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test complete shop lifecycle: Create → Subscribe → Use → Monitor → Cleanup
func (suite *CompleteShopLifecycleTestSuite) TestCompleteShopLifecycle() {
	suite.T().Log("Testing complete shop lifecycle with ADC integration...")

	// Phase 1: Shop Creation
	suite.T().Run("Phase_1_Shop_Creation", func(t *testing.T) {
		t.Log("Creating shop and checking ADC organization creation...")

		shopData := map[string]interface{}{
			"name":        "E2E Test Shop",
			"slug":        "e2e-test-shop",
			"type":        "api_service",
			"description": "End-to-end test shop for complete lifecycle testing",
			"website":     "https://e2e-test-shop.example.com",
		}

		jsonBody, _ := json.Marshal(shopData)
		req, _ := http.NewRequest("POST", "/api/v1/shops/", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+suite.authToken)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusCreated {
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			// Extract shop data
			shopID := response["id"].(string)
			suite.testShop = &models.Shop{}
			err = database.DB.Where("id = ?", shopID).First(suite.testShop).Error
			require.NoError(t, err)

			t.Logf("Shop created successfully: %s", shopID)
			assert.Equal(t, shopData["name"], response["name"])
			assert.Equal(t, shopData["slug"], response["slug"])

			// Verify ADC organization creation (if services are available)
			suite.verifyADCOrganizationCreation(t, suite.testShop.ID)

		} else {
			t.Logf("Shop creation failed (may be expected): Status %d, Response: %s", w.Code, w.Body.String())
			t.Skip("Cannot continue without shop creation")
		}
	})

	// Phase 2: Subscription Setup
	suite.T().Run("Phase_2_Subscription_Setup", func(t *testing.T) {
		if suite.testShop == nil {
			t.Skip("Shop not created, skipping subscription setup")
		}

		t.Log("Setting up subscription and checking ADC integration...")

		// Simulate subscription creation via webhook
		suite.simulateStripeSubscriptionWebhook(t, "checkout.session.completed")

		// Verify local subscription was created
		var shopSubscription models.ShopSubscription
		err := database.DB.Where("shop_id = ?", suite.testShop.ID).First(&shopSubscription).Error
		if err == nil {
			t.Logf("Shop subscription created: %+v", shopSubscription)
			assert.Equal(t, suite.testShop.ID, shopSubscription.ShopID)
			assert.Equal(t, suite.testShop.ID.String(), shopSubscription.ADCOrganizationID)
		} else {
			t.Logf("Shop subscription not found (services may be unavailable): %v", err)
		}

		// Sync with ADC subscription service
		suite.syncShopToADC(t, suite.testShop.ID)
	})

	// Phase 3: Usage Simulation
	suite.T().Run("Phase_3_Usage_Simulation", func(t *testing.T) {
		if suite.testShop == nil {
			t.Skip("Shop not created, skipping usage simulation")
		}

		t.Log("Simulating credit usage and monitoring...")

		// Test 1: Check initial credit balance
		suite.checkCreditBalance(t, 1000) // Default balance

		// Test 2: Consume credits for various operations
		usageScenarios := []struct {
			name     string
			amount   int
			operation string
		}{
			{"API_Call_Batch_1", 50, "api_batch_processing"},
			{"QR_Code_Generation", 25, "qr_code_creation"},
			{"Customer_Management", 15, "customer_operations"},
			{"Analytics_Query", 10, "analytics_processing"},
		}

		for _, scenario := range usageScenarios {
			t.Run(scenario.name, func(t *testing.T) {
				suite.consumeCredits(t, scenario.amount, scenario.operation)
			})
		}

		// Test 3: Check updated balance
		expectedBalance := 1000 - 50 - 25 - 15 - 10 // 900
		suite.checkCreditBalance(t, expectedBalance)

		// Test 4: Get usage analytics
		suite.getUsageAnalytics(t)
	})

	// Phase 4: Subscription Management
	suite.T().Run("Phase_4_Subscription_Management", func(t *testing.T) {
		if suite.testShop == nil {
			t.Skip("Shop not created, skipping subscription management")
		}

		t.Log("Testing subscription management and plan changes...")

		// Test 1: Simulate plan upgrade
		suite.simulateStripeSubscriptionWebhook(t, "customer.subscription.updated")

		// Test 2: Check subscription limits
		suite.checkSubscriptionLimits(t)

		// Test 3: Test subscription sync status
		suite.checkSyncStatus(t)
	})

	// Phase 5: Advanced Operations
	suite.T().Run("Phase_5_Advanced_Operations", func(t *testing.T) {
		if suite.testShop == nil {
			t.Skip("Shop not created, skipping advanced operations")
		}

		t.Log("Testing advanced operations and integrations...")

		// Test 1: Customer management
		suite.testCustomerManagement(t)

		// Test 2: Bulk operations
		suite.testBulkSyncOperations(t)

		// Test 3: Usage reporting to analytics
		suite.testUsageReporting(t)
	})

	// Phase 6: Error Scenarios
	suite.T().Run("Phase_6_Error_Scenarios", func(t *testing.T) {
		if suite.testShop == nil {
			t.Skip("Shop not created, skipping error scenarios")
		}

		t.Log("Testing error scenarios and recovery...")

		// Test 1: Insufficient credits
		suite.testInsufficientCredits(t)

		// Test 2: Invalid operations
		suite.testInvalidOperations(t)

		// Test 3: Service failures
		suite.testServiceFailureScenarios(t)
	})
}

// Helper methods for specific test operations

func (suite *CompleteShopLifecycleTestSuite) verifyADCOrganizationCreation(t *testing.T, shopID uuid.UUID) {
	// Check if organization was created in ADC SSO service
	ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
	defer cancel()

	org, err := suite.ssoClient.GetOrganizationByExternalID(ctx, shopID.String())
	if err != nil {
		t.Logf("ADC organization check failed (service may be unavailable): %v", err)
		return
	}

	if org != nil {
		t.Logf("ADC organization created successfully: %+v", org)
		assert.Equal(t, shopID.String(), org.ExternalID)
	}
}

func (suite *CompleteShopLifecycleTestSuite) simulateStripeSubscriptionWebhook(t *testing.T, eventType string) {
	webhookData := map[string]interface{}{
		"id":      fmt.Sprintf("evt_test_%d", time.Now().Unix()),
		"object":  "event",
		"type":    eventType,
		"created": time.Now().Unix(),
		"data": map[string]interface{}{
			"object": map[string]interface{}{
				"id":           fmt.Sprintf("cs_test_%d", time.Now().Unix()),
				"object":       "checkout_session",
				"mode":         "subscription",
				"status":       "complete",
				"subscription": fmt.Sprintf("sub_test_%d", time.Now().Unix()),
				"metadata": map[string]interface{}{
					"shop_id":   suite.testShop.ID.String(),
					"user_id":   suite.testUser.ID.String(),
					"plan_type": "pro",
				},
			},
		},
	}

	jsonBody, _ := json.Marshal(webhookData)
	req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Stripe-Signature", "t="+fmt.Sprintf("%d", time.Now().Unix())+",v1=test-signature")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Stripe webhook simulation (%s): Status %d", eventType, w.Code)
	if w.Code != http.StatusOK {
		t.Logf("Webhook response: %s", w.Body.String())
	}
}

func (suite *CompleteShopLifecycleTestSuite) syncShopToADC(t *testing.T, shopID uuid.UUID) {
	url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", shopID.String())
	req, _ := http.NewRequest("POST", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("ADC sync request: Status %d", w.Code)
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		t.Logf("Sync response: %+v", response)
	}
}

func (suite *CompleteShopLifecycleTestSuite) checkCreditBalance(t *testing.T, expectedBalance int) {
	req, _ := http.NewRequest("GET", "/api/v2/credits/balance", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		if balance, ok := response["balance"].(float64); ok {
			t.Logf("Current credit balance: %.0f (expected: %d)", balance, expectedBalance)
			// Note: We don't assert exact balance as other operations may affect it
			assert.GreaterOrEqual(t, balance, 0.0, "Balance should be non-negative")
		}
	} else {
		t.Logf("Credit balance check failed: Status %d, Response: %s", w.Code, w.Body.String())
	}
}

func (suite *CompleteShopLifecycleTestSuite) consumeCredits(t *testing.T, amount int, operation string) {
	consumeData := map[string]interface{}{
		"amount":      amount,
		"operation":   operation,
		"description": fmt.Sprintf("E2E test credit consumption for %s", operation),
	}

	jsonBody, _ := json.Marshal(consumeData)
	req, _ := http.NewRequest("POST", "/api/v2/credits/consume", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Credit consumption (%s, %d credits): Status %d", operation, amount, w.Code)
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		t.Logf("Consumption response: %+v", response)
	}
}

func (suite *CompleteShopLifecycleTestSuite) getUsageAnalytics(t *testing.T) {
	req, _ := http.NewRequest("GET", "/api/v2/credits/analytics", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		t.Logf("Usage analytics: %+v", response)
	} else {
		t.Logf("Usage analytics failed: Status %d", w.Code)
	}
}

func (suite *CompleteShopLifecycleTestSuite) checkSubscriptionLimits(t *testing.T) {
	// This would typically check against subscription service
	t.Log("Checking subscription limits via ADC integration...")
	// Implementation would call subscription service to get current limits
	// and compare against usage
}

func (suite *CompleteShopLifecycleTestSuite) checkSyncStatus(t *testing.T) {
	req, _ := http.NewRequest("GET", "/api/v1/adc-sync/status", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Sync status check: Status %d", w.Code)
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		t.Logf("Sync status: %+v", response)
	}
}

func (suite *CompleteShopLifecycleTestSuite) testCustomerManagement(t *testing.T) {
	// Create a customer
	customerData := map[string]interface{}{
		"name":  "E2E Test Customer",
		"email": "<EMAIL>",
		"phone": "+1234567890",
	}

	jsonBody, _ := json.Marshal(customerData)
	url := fmt.Sprintf("/api/v1/shops/%s/customers", suite.testShop.ID.String())
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Customer creation: Status %d", w.Code)
	if w.Code == http.StatusCreated {
		t.Log("Customer created successfully")
	}

	// Get customers
	url = fmt.Sprintf("/api/v1/shops/%s/customers", suite.testShop.ID.String())
	req, _ = http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Customer list: Status %d", w.Code)
}

func (suite *CompleteShopLifecycleTestSuite) testBulkSyncOperations(t *testing.T) {
	req, _ := http.NewRequest("POST", "/api/v1/adc-sync/subscriptions/bulk", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Bulk sync operation: Status %d", w.Code)
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		t.Logf("Bulk sync response: %+v", response)
	}
}

func (suite *CompleteShopLifecycleTestSuite) testUsageReporting(t *testing.T) {
	req, _ := http.NewRequest("GET", "/api/v2/credits/usage", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Usage history: Status %d", w.Code)
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		t.Logf("Usage history: %+v", response)
	}
}

func (suite *CompleteShopLifecycleTestSuite) testInsufficientCredits(t *testing.T) {
	// Try to consume more credits than available
	consumeData := map[string]interface{}{
		"amount":    10000, // Large amount
		"operation": "test_insufficient_credits",
	}

	jsonBody, _ := json.Marshal(consumeData)
	req, _ := http.NewRequest("POST", "/api/v2/credits/consume", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Insufficient credits test: Status %d", w.Code)
	// Should return an error status
	assert.NotEqual(t, http.StatusOK, w.Code, "Should fail with insufficient credits")
}

func (suite *CompleteShopLifecycleTestSuite) testInvalidOperations(t *testing.T) {
	// Test invalid credit consumption
	invalidData := map[string]interface{}{
		"amount": -50, // Negative amount
	}

	jsonBody, _ := json.Marshal(invalidData)
	req, _ := http.NewRequest("POST", "/api/v2/credits/consume", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	t.Logf("Invalid operation test: Status %d", w.Code)
	assert.Equal(t, http.StatusBadRequest, w.Code, "Should reject negative amounts")
}

func (suite *CompleteShopLifecycleTestSuite) testServiceFailureScenarios(t *testing.T) {
	// Test operations when ADC services are unavailable
	// This is mainly for demonstrating graceful degradation
	t.Log("Testing graceful degradation when ADC services are unavailable...")
	
	// Even if ADC services are down, local operations should still work
	suite.checkCreditBalance(t, 0) // Just check that the endpoint responds
}

// TestCompleteShopLifecycleIntegration runs the complete shop lifecycle test suite
func TestCompleteShopLifecycleIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping complete shop lifecycle integration tests in short mode")
	}

	suite.Run(t, new(CompleteShopLifecycleTestSuite))
}