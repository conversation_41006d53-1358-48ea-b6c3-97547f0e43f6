package e2e

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// DataConsistencyTestSuite tests data consistency across ADC services and local database
type DataConsistencyTestSuite struct {
	suite.Suite
	serviceContainer        *services.ServiceContainer
	shopOrgService          *services.ShopOrganizationService
	subscriptionSyncService *services.SubscriptionSyncService
	usageReportingService   *services.UsageReportingService
	ssoClient               *ssoSDK.Client
	subscriptionClient      *subscriptionSDK.Client
	analyticsClient         *analyticsSDK.Client
	testUser                *models.User
	testShops               []*models.Shop
	testSubscriptions       []*models.ShopSubscription
	ctx                     context.Context
}

// SetupSuite initializes the test environment
func (suite *DataConsistencyTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize ADC service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)
	suite.ssoClient = ssoClient

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)
	suite.subscriptionClient = subscriptionClient

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)
	suite.analyticsClient = analyticsClient

	// Initialize service container and services
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		suite.subscriptionClient,
		suite.analyticsClient,
		suite.ssoClient,
	)

	suite.shopOrgService = services.NewShopOrganizationService(database.DB, suite.ssoClient)
	suite.subscriptionSyncService = services.NewSubscriptionSyncService(
		database.DB,
		suite.subscriptionClient,
		suite.shopOrgService,
	)
	suite.usageReportingService = services.NewUsageReportingService(
		database.DB,
		suite.subscriptionClient,
		suite.analyticsClient,
	)

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Data Consistency Test User",
		GoogleID: "dataconsistency-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shops for consistency testing
	for i := 0; i < 3; i++ {
		shop := &models.Shop{
			ID:      uuid.New(),
			OwnerID: suite.testUser.ID,
			Name:    fmt.Sprintf("Data Consistency Test Shop %d", i+1),
			Slug:    fmt.Sprintf("data-consistency-shop-%d", i+1),
			Type:    "api_service",
		}
		err = database.DB.Create(shop).Error
		require.NoError(suite.T(), err)
		suite.testShops = append(suite.testShops, shop)

		// Create corresponding subscription
		subscription := &models.ShopSubscription{
			ID:                uuid.New(),
			ShopID:            shop.ID,
			ADCSubscriptionID: fmt.Sprintf("adc-sub-consistency-%d", i+1),
			ADCOrganizationID: shop.ID.String(),
			Status:            "active",
			CreditBalance:     1000,
			StartDate:         time.Now(),
		}
		err = database.DB.Create(subscription).Error
		require.NoError(suite.T(), err)
		suite.testSubscriptions = append(suite.testSubscriptions, subscription)
	}
}

// TearDownSuite cleans up after tests
func (suite *DataConsistencyTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		for _, subscription := range suite.testSubscriptions {
			database.DB.Where("id = ?", subscription.ID).Delete(&models.ShopSubscription{})
		}
		for _, shop := range suite.testShops {
			database.DB.Where("shop_id = ?", shop.ID).Delete(&models.UsageEvent{})
			database.DB.Where("id = ?", shop.ID).Delete(&models.Shop{})
		}
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test eventual consistency between local and remote data
func (suite *DataConsistencyTestSuite) TestEventualConsistency() {
	suite.T().Log("Testing eventual consistency between local and remote data...")

	// Test subscription data consistency
	suite.T().Run("Subscription_Data_Consistency", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
		defer cancel()

		shop := suite.testShops[0]
		originalSubscription := suite.testSubscriptions[0]

		t.Logf("Testing consistency for shop: %s", shop.ID)

		// Step 1: Modify local subscription
		updatedBalance := 750
		err := database.DB.Model(originalSubscription).Update("credit_balance", updatedBalance).Error
		require.NoError(t, err)

		t.Logf("Updated local credit balance to: %d", updatedBalance)

		// Step 2: Sync with remote service
		syncResult, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, shop.ID)
		if err != nil {
			t.Logf("Sync failed (service may be unavailable): %v", err)
			t.Skip("Cannot test consistency without service sync")
		}

		if syncResult != nil {
			t.Logf("Sync result: %+v", syncResult)

			// Step 3: Verify consistency after sync
			var syncedSubscription models.ShopSubscription
			err = database.DB.Where("shop_id = ?", shop.ID).First(&syncedSubscription).Error
			require.NoError(t, err)

			t.Logf("Post-sync subscription: Balance=%d, Status=%s", 
				syncedSubscription.CreditBalance, syncedSubscription.Status)

			// Check if conflict was detected and resolved
			if syncResult.ConflictDetected {
				t.Logf("Conflict detected and resolved: %s", syncResult.ConflictResolution)
				assert.NotEmpty(t, syncResult.ConflictResolution, "Conflict resolution should be documented")
			}

			// Verify data integrity
			assert.NotEmpty(t, syncedSubscription.ADCSubscriptionID, "ADC Subscription ID should be preserved")
			assert.Equal(t, shop.ID.String(), syncedSubscription.ADCOrganizationID, "Organization ID should match shop ID")
		}
	})
}

// Test data consistency under concurrent operations
func (suite *DataConsistencyTestSuite) TestConcurrentDataConsistency() {
	suite.T().Log("Testing data consistency under concurrent operations...")

	// Test concurrent subscription updates
	suite.T().Run("Concurrent_Subscription_Updates", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 90*time.Second)
		defer cancel()

		shop := suite.testShops[1]
		subscription := suite.testSubscriptions[1]

		const numConcurrentOps = 5
		var wg sync.WaitGroup
		results := make(chan error, numConcurrentOps)

		// Start concurrent sync operations
		for i := 0; i < numConcurrentOps; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()
				
				opCtx, opCancel := context.WithTimeout(ctx, 30*time.Second)
				defer opCancel()

				// Modify subscription locally
				newBalance := 1000 - (index * 10)
				err := database.DB.Model(subscription).Update("credit_balance", newBalance).Error
				if err != nil {
					results <- err
					return
				}

				// Attempt sync
				_, err = suite.subscriptionSyncService.SyncShopSubscription(opCtx, shop.ID)
				results <- err
			}(i)
		}

		// Wait for all operations to complete
		wg.Wait()
		close(results)

		// Collect results
		var errors []error
		for err := range results {
			if err != nil {
				errors = append(errors, err)
			}
		}

		t.Logf("Concurrent operations completed: %d errors out of %d operations", len(errors), numConcurrentOps)

		// Verify final state consistency
		var finalSubscription models.ShopSubscription
		err := database.DB.Where("shop_id = ?", shop.ID).First(&finalSubscription).Error
		require.NoError(t, err)

		t.Logf("Final subscription state: Balance=%d, Status=%s", 
			finalSubscription.CreditBalance, finalSubscription.Status)

		// Data should be in a consistent state even after concurrent operations
		assert.NotEmpty(t, finalSubscription.ADCSubscriptionID, "ADC Subscription ID should be preserved")
		assert.GreaterOrEqual(t, finalSubscription.CreditBalance, 0, "Credit balance should be non-negative")
	})
}

// Test data consistency during service failures
func (suite *DataConsistencyTestSuite) TestConsistencyDuringServiceFailures() {
	suite.T().Log("Testing data consistency during service failures...")

	// Test consistency when sync fails
	suite.T().Run("Consistency_During_Sync_Failures", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 45*time.Second)
		defer cancel()

		shop := suite.testShops[2]
		subscription := suite.testSubscriptions[2]

		// Record initial state
		initialBalance := subscription.CreditBalance
		initialStatus := subscription.Status

		t.Logf("Initial state: Balance=%d, Status=%s", initialBalance, initialStatus)

		// Modify local data
		newBalance := initialBalance + 100
		newStatus := "updated_locally"
		
		err := database.DB.Model(subscription).Updates(map[string]interface{}{
			"credit_balance": newBalance,
			"status":        newStatus,
		}).Error
		require.NoError(t, err)

		t.Logf("Modified local state: Balance=%d, Status=%s", newBalance, newStatus)

		// Attempt sync with very short timeout (likely to fail)
		shortCtx, shortCancel := context.WithTimeout(ctx, 10*time.Millisecond)
		defer shortCancel()

		_, err = suite.subscriptionSyncService.SyncShopSubscription(shortCtx, shop.ID)
		if err != nil {
			t.Logf("Sync failed as expected (short timeout): %v", err)
		}

		// Verify local data is still consistent after failed sync
		var postFailSubscription models.ShopSubscription
		err = database.DB.Where("shop_id = ?", shop.ID).First(&postFailSubscription).Error
		require.NoError(t, err)

		t.Logf("Post-failure state: Balance=%d, Status=%s", 
			postFailSubscription.CreditBalance, postFailSubscription.Status)

		// Local modifications should be preserved
		assert.Equal(t, newBalance, postFailSubscription.CreditBalance, "Local balance should be preserved")
		assert.Equal(t, newStatus, postFailSubscription.Status, "Local status should be preserved")
		assert.Equal(t, shop.ID, postFailSubscription.ShopID, "Shop ID should be unchanged")

		// Try sync again with longer timeout
		longerCtx, longerCancel := context.WithTimeout(ctx, 30*time.Second)
		defer longerCancel()

		syncResult, err := suite.subscriptionSyncService.SyncShopSubscription(longerCtx, shop.ID)
		if err != nil {
			t.Logf("Second sync also failed (services may be unavailable): %v", err)
		} else if syncResult != nil {
			t.Logf("Second sync succeeded: %+v", syncResult)
		}
	})
}

// Test usage data consistency across analytics and subscription services
func (suite *DataConsistencyTestSuite) TestUsageDataConsistency() {
	suite.T().Log("Testing usage data consistency across analytics and subscription services...")

	// Test usage tracking and credit consumption consistency
	suite.T().Run("Usage_Credit_Consistency", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
		defer cancel()

		shop := suite.testShops[0]

		// Get initial credit balance
		var initialSubscription models.ShopSubscription
		err := database.DB.Where("shop_id = ?", shop.ID).First(&initialSubscription).Error
		require.NoError(t, err)
		initialBalance := initialSubscription.CreditBalance

		t.Logf("Initial credit balance: %d", initialBalance)

		// Create usage events that should affect credit balance
		usageEvents := []struct {
			eventType string
			quantity  int
		}{
			{"api_call", 50},
			{"qr_generation", 25},
			{"customer_creation", 15},
		}

		totalUsage := 0
		for _, usage := range usageEvents {
			totalUsage += usage.quantity

			// Track usage event
			usageEvent := &models.UsageEvent{
				ShopID:        shop.ID,
				EventType:     usage.eventType,
				EventCategory: "credit_consumption",
				Quantity:      usage.quantity,
				Metadata: map[string]interface{}{
					"consistency_test": true,
				},
			}

			err = suite.usageReportingService.TrackUsageEvent(ctx, usageEvent)
			if err != nil {
				t.Logf("Usage tracking failed for %s (service may be unavailable): %v", usage.eventType, err)
				continue
			}

			t.Logf("Tracked usage: %s (%d credits)", usage.eventType, usage.quantity)

			// Simulate credit consumption (in real scenario this would be automatic)
			newBalance := initialBalance - totalUsage
			err = database.DB.Model(&initialSubscription).Update("credit_balance", newBalance).Error
			require.NoError(t, err)
		}

		// Verify usage events were recorded
		var recordedEvents []models.UsageEvent
		err = database.DB.Where("shop_id = ? AND metadata->>'consistency_test' = 'true'", shop.ID).Find(&recordedEvents).Error
		require.NoError(t, err)

		t.Logf("Recorded %d usage events", len(recordedEvents))
		assert.GreaterOrEqual(t, len(recordedEvents), 1, "At least some usage events should be recorded")

		// Calculate total recorded usage
		totalRecordedUsage := 0
		for _, event := range recordedEvents {
			totalRecordedUsage += event.Quantity
		}

		t.Logf("Total recorded usage: %d", totalRecordedUsage)

		// Get final credit balance
		var finalSubscription models.ShopSubscription
		err = database.DB.Where("shop_id = ?", shop.ID).First(&finalSubscription).Error
		require.NoError(t, err)

		t.Logf("Final credit balance: %d", finalSubscription.CreditBalance)

		// Verify consistency between usage and credit balance
		expectedBalance := initialBalance - totalRecordedUsage
		assert.Equal(t, expectedBalance, finalSubscription.CreditBalance, 
			"Credit balance should match initial balance minus recorded usage")

		// Get usage analytics and verify consistency
		analytics, err := suite.usageReportingService.GetUsageAnalytics(ctx, shop.ID, 
			time.Now().Add(-1*time.Hour), time.Now())
		
		if err != nil {
			t.Logf("Analytics retrieval failed (service may be unavailable): %v", err)
		} else if analytics != nil {
			t.Logf("Analytics total quantity: %d", analytics.TotalQuantity)
			assert.Equal(t, int64(totalRecordedUsage), analytics.TotalQuantity, 
				"Analytics should match recorded usage")
		}
	})
}

// Test incremental sync consistency
func (suite *DataConsistencyTestSuite) TestIncrementalSyncConsistency() {
	suite.T().Log("Testing incremental sync consistency...")

	// Test incremental sync maintains data integrity
	suite.T().Run("Incremental_Sync_Integrity", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 90*time.Second)
		defer cancel()

		// Create usage events across different time periods
		now := time.Now()
		timeRanges := []struct {
			name string
			time time.Time
		}{
			{"1_hour_ago", now.Add(-1 * time.Hour)},
			{"30_min_ago", now.Add(-30 * time.Minute)},
			{"15_min_ago", now.Add(-15 * time.Minute)},
			{"5_min_ago", now.Add(-5 * time.Minute)},
		}

		eventsCreated := 0
		for i, shop := range suite.testShops {
			for j, timeRange := range timeRanges {
				event := &models.UsageEvent{
					ShopID:        shop.ID,
					EventType:     "incremental_test",
					EventCategory: "sync_test",
					Quantity:      (i + 1) * (j + 1),
					CreatedAt:     timeRange.time,
				}
				
				err := database.DB.Create(event).Error
				require.NoError(t, err)
				eventsCreated++
			}
		}

		t.Logf("Created %d usage events across different time periods", eventsCreated)

		// Perform incremental sync from 45 minutes ago
		syncTime := now.Add(-45 * time.Minute)
		result, err := suite.subscriptionSyncService.SyncSubscriptionsSince(ctx, syncTime)
		
		if err != nil {
			t.Logf("Incremental sync failed (service may be unavailable): %v", err)
		} else if result != nil {
			t.Logf("Incremental sync result: Total=%d, Synced=%d, Failed=%d", 
				result.TotalShops, result.SyncedCount, result.FailedCount)

			// Should have synced at least the test shops
			assert.GreaterOrEqual(t, result.TotalShops, len(suite.testShops), 
				"Should include test shops in incremental sync")
		}

		// Verify data integrity after incremental sync
		for _, shop := range suite.testShops {
			var subscription models.ShopSubscription
			err = database.DB.Where("shop_id = ?", shop.ID).First(&subscription).Error
			require.NoError(t, err)

			// Basic integrity checks
			assert.NotEmpty(t, subscription.ADCSubscriptionID, "ADC Subscription ID should be preserved")
			assert.Equal(t, shop.ID.String(), subscription.ADCOrganizationID, "Organization ID should match")
			assert.GreaterOrEqual(t, subscription.CreditBalance, 0, "Credit balance should be non-negative")

			t.Logf("Shop %s post-sync: Balance=%d, Status=%s", 
				shop.ID, subscription.CreditBalance, subscription.Status)
		}
	})
}

// Test cross-service data validation
func (suite *DataConsistencyTestSuite) TestCrossServiceDataValidation() {
	suite.T().Log("Testing cross-service data validation...")

	// Test data validation across SSO and Subscription services
	suite.T().Run("SSO_Subscription_Data_Validation", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
		defer cancel()

		shop := suite.testShops[0]

		// Step 1: Create organization via SSO service
		org, err := suite.shopOrgService.CreateOrganizationForShop(ctx, shop, suite.testUser.ID)
		
		var orgID string
		if err != nil {
			t.Logf("Organization creation failed (SSO service may be unavailable): %v", err)
		} else if org != nil {
			orgID = org.ID
			t.Logf("Organization created: %s", orgID)
			defer suite.ssoClient.DeleteOrganization(ctx, orgID)

			// Verify organization data consistency
			assert.Equal(t, shop.ID.String(), org.ExternalID, "External ID should match shop ID")
			assert.Equal(t, shop.Name, org.Name, "Organization name should match shop name")
		}

		// Step 2: Sync subscription data
		syncResult, err := suite.subscriptionSyncService.SyncShopSubscription(ctx, shop.ID)
		
		if err != nil {
			t.Logf("Subscription sync failed (Subscription service may be unavailable): %v", err)
		} else if syncResult != nil {
			t.Logf("Subscription synced: %+v", syncResult)

			// Cross-validate organization and subscription data
			var localSubscription models.ShopSubscription
			err = database.DB.Where("shop_id = ?", shop.ID).First(&localSubscription).Error
			require.NoError(t, err)

			// The organization external ID should match the subscription organization ID
			if orgID != "" {
				assert.Equal(t, shop.ID.String(), localSubscription.ADCOrganizationID, 
					"Subscription organization ID should match shop ID")
			}

			// Subscription should reference the correct shop
			assert.Equal(t, shop.ID, localSubscription.ShopID, "Subscription should reference correct shop")
		}
	})
}

// Test data consistency metrics and monitoring
func (suite *DataConsistencyTestSuite) TestDataConsistencyMetrics() {
	suite.T().Log("Testing data consistency metrics and monitoring...")

	// Test consistency monitoring
	suite.T().Run("Consistency_Monitoring", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
		defer cancel()

		// Check sync metrics
		metrics, err := suite.subscriptionSyncService.GetSyncMetrics(ctx)
		if err != nil {
			t.Logf("Sync metrics unavailable (feature may not be implemented): %v", err)
		} else if metrics != nil {
			t.Logf("Sync metrics: %+v", metrics)

			// Verify metrics structure
			if totalSyncs, ok := metrics["total_syncs"]; ok {
				t.Logf("Total syncs: %v", totalSyncs)
			}
			if failedSyncs, ok := metrics["failed_syncs"]; ok {
				t.Logf("Failed syncs: %v", failedSyncs)
			}
		}

		// Check usage reporting metrics
		reportingMetrics, err := suite.usageReportingService.GetReportingMetrics(ctx)
		if err != nil {
			t.Logf("Reporting metrics unavailable (feature may not be implemented): %v", err)
		} else if reportingMetrics != nil {
			t.Logf("Reporting metrics: %+v", reportingMetrics)
		}

		// Verify local database consistency
		suite.verifyLocalDatabaseConsistency(t)
	})
}

// Helper method to verify local database consistency
func (suite *DataConsistencyTestSuite) verifyLocalDatabaseConsistency(t *testing.T) {
	t.Log("Verifying local database consistency...")

	// Check that all shops have corresponding subscriptions
	for _, shop := range suite.testShops {
		var subscription models.ShopSubscription
		err := database.DB.Where("shop_id = ?", shop.ID).First(&subscription).Error
		assert.NoError(t, err, "Each shop should have a subscription")

		if err == nil {
			// Verify foreign key relationships
			assert.Equal(t, shop.ID, subscription.ShopID, "Subscription should reference correct shop")
			assert.Equal(t, shop.ID.String(), subscription.ADCOrganizationID, 
				"Organization ID should match shop ID")

			// Verify data integrity
			assert.NotEmpty(t, subscription.ADCSubscriptionID, "ADC Subscription ID should not be empty")
			assert.GreaterOrEqual(t, subscription.CreditBalance, 0, "Credit balance should be non-negative")
			assert.NotEmpty(t, subscription.Status, "Status should not be empty")
		}
	}

	// Check usage events referential integrity
	var orphanedEvents int64
	err := database.DB.Model(&models.UsageEvent{}).
		Joins("LEFT JOIN shops ON usage_events.shop_id = shops.id").
		Where("shops.id IS NULL").
		Count(&orphanedEvents).Error
	
	assert.NoError(t, err, "Should be able to check orphaned events")
	assert.Equal(t, int64(0), orphanedEvents, "Should not have orphaned usage events")

	t.Logf("Local database consistency verified: %d shops, %d subscriptions", 
		len(suite.testShops), len(suite.testSubscriptions))
}

// TestDataConsistencyIntegration runs the data consistency test suite
func TestDataConsistencyIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping data consistency integration tests in short mode")
	}

	suite.Run(t, new(DataConsistencyTestSuite))
}