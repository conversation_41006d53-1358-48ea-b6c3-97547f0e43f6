package handlers_v2

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// WebhookV2TestSuite tests webhook processing integration with ADC services
type WebhookV2TestSuite struct {
	suite.Suite
	router            *gin.Engine
	serviceContainer  *services.ServiceContainer
	testUser          *models.User
	testShop          *models.Shop
	testSubscription  *models.ShopSubscription
	webhookSecret     string
}

// SetupSuite initializes the test environment
func (suite *WebhookV2TestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize ADC service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)

	// Initialize service container
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		subscriptionClient,
		analyticsClient,
		ssoClient,
	)

	// Setup router
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Set webhook secret for testing
	suite.webhookSecret = "test-webhook-secret-12345"

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Webhook Test User",
		GoogleID: "webhook-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Webhook Shop",
		Slug:    "test-webhook-shop",
		Type:    "api_service",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Create test shop subscription
	suite.testSubscription = &models.ShopSubscription{
		ID:                uuid.New(),
		ShopID:            suite.testShop.ID,
		ADCSubscriptionID: "adc-sub-webhook-123",
		ADCOrganizationID: suite.testShop.ID.String(),
		Status:            "active",
		CreditBalance:     1000,
	}
	err = database.DB.Create(suite.testSubscription).Error
	require.NoError(suite.T(), err)

	// Setup webhook routes
	stripeWebhookV2Handler := handlers.NewStripeWebhookV2Handler(suite.serviceContainer)
	subscriptionWebhookHandler := handlers.NewSubscriptionWebhookHandler(suite.serviceContainer)

	// Stripe webhook V2 routes
	suite.router.POST("/webhook/stripe/v2", stripeWebhookV2Handler.HandleStripeWebhookV2)

	// ADC Subscription service webhook routes  
	adcWebhooks := suite.router.Group("/webhook/adc")
	{
		adcWebhooks.POST("/subscription", subscriptionWebhookHandler.HandleSubscriptionWebhook)
	}
}

// TearDownSuite cleans up after tests
func (suite *WebhookV2TestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testSubscription.ID).Delete(&models.ShopSubscription{})
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// Test Stripe webhook V2 processing
func (suite *WebhookV2TestSuite) TestStripeWebhookV2Processing() {
	suite.T().Log("Testing Stripe webhook V2 processing...")

	// Test 1: Checkout session completed event
	suite.T().Run("Checkout_Session_Completed", func(t *testing.T) {
		event := map[string]interface{}{
			"id":      "evt_test_webhook",
			"object":  "event",
			"type":    "checkout.session.completed",
			"created": time.Now().Unix(),
			"data": map[string]interface{}{
				"object": map[string]interface{}{
					"id":           "cs_test_12345",
					"object":       "checkout_session",
					"mode":         "subscription",
					"status":       "complete",
					"subscription": "sub_test_67890",
					"metadata": map[string]interface{}{
						"shop_id":   suite.testShop.ID.String(),
						"user_id":   suite.testUser.ID.String(),
						"plan_type": "pro",
					},
				},
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("Checkout session webhook processed successfully")
			
			// Verify that shop subscription was updated
			var updatedSubscription models.ShopSubscription
			err := database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
			if err == nil {
				t.Logf("Updated subscription: %+v", updatedSubscription)
			}
		} else {
			t.Logf("Webhook processing failed (may be expected): Status %d, Response: %s", w.Code, w.Body.String())
		}
	})

	// Test 2: Subscription updated event
	suite.T().Run("Subscription_Updated", func(t *testing.T) {
		event := map[string]interface{}{
			"id":      "evt_test_webhook_2",
			"object":  "event", 
			"type":    "customer.subscription.updated",
			"created": time.Now().Unix(),
			"data": map[string]interface{}{
				"object": map[string]interface{}{
					"id":     "sub_test_67890",
					"object": "subscription",
					"status": "active",
					"items": map[string]interface{}{
						"data": []interface{}{
							map[string]interface{}{
								"price": map[string]interface{}{
									"id": "price_test_pro",
								},
							},
						},
					},
					"metadata": map[string]interface{}{
						"shop_id": suite.testShop.ID.String(),
					},
				},
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("Subscription update webhook processed successfully")
		} else {
			t.Logf("Subscription update webhook failed: Status %d, Response: %s", w.Code, w.Body.String())
		}
	})

	// Test 3: Subscription cancelled event
	suite.T().Run("Subscription_Cancelled", func(t *testing.T) {
		event := map[string]interface{}{
			"id":      "evt_test_webhook_3",
			"object":  "event",
			"type":    "customer.subscription.deleted",
			"created": time.Now().Unix(),
			"data": map[string]interface{}{
				"object": map[string]interface{}{
					"id":     "sub_test_67890",
					"object": "subscription",
					"status": "canceled",
					"metadata": map[string]interface{}{
						"shop_id": suite.testShop.ID.String(),
					},
				},
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("Subscription cancellation webhook processed successfully")
			
			// Verify subscription status was updated
			var updatedSubscription models.ShopSubscription
			err := database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
			if err == nil && updatedSubscription.Status == "cancelled" {
				t.Log("Subscription status correctly updated to cancelled")
			}
		} else {
			t.Logf("Subscription cancellation webhook failed: Status %d, Response: %s", w.Code, w.Body.String())
		}
	})
}

// Test ADC Subscription service webhook processing
func (suite *WebhookV2TestSuite) TestADCSubscriptionWebhookProcessing() {
	suite.T().Log("Testing ADC Subscription webhook processing...")

	// Test 1: Subscription created event from ADC service
	suite.T().Run("ADC_Subscription_Created", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type":      "subscription.created",
			"event_id":        "adc_evt_12345",
			"timestamp":       time.Now().Format(time.RFC3339),
			"organization_id": suite.testShop.ID.String(), // External ID = Shop ID
			"subscription": map[string]interface{}{
				"id":              "adc-sub-new-123",
				"organization_id": suite.testShop.ID.String(),
				"plan_id":         "plan_pro",
				"status":          "active",
				"current_period_start": time.Now().Format(time.RFC3339),
				"current_period_end":   time.Now().Add(30 * 24 * time.Hour).Format(time.RFC3339),
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("ADC subscription created webhook processed successfully")
			
			// Verify local subscription was updated
			var updatedSubscription models.ShopSubscription
			err := database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
			if err == nil {
				assert.Equal(t, "adc-sub-new-123", updatedSubscription.ADCSubscriptionID)
				t.Logf("Local subscription synced: %+v", updatedSubscription)
			}
		} else {
			t.Logf("ADC subscription webhook failed (may be expected): Status %d, Response: %s", w.Code, w.Body.String())
		}
	})

	// Test 2: Subscription updated event from ADC service
	suite.T().Run("ADC_Subscription_Updated", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type":      "subscription.updated",
			"event_id":        "adc_evt_12346",
			"timestamp":       time.Now().Format(time.RFC3339),
			"organization_id": suite.testShop.ID.String(),
			"subscription": map[string]interface{}{
				"id":              suite.testSubscription.ADCSubscriptionID,
				"organization_id": suite.testShop.ID.String(),
				"plan_id":         "plan_enterprise",
				"status":          "active",
				"current_period_start": time.Now().Format(time.RFC3339),
				"current_period_end":   time.Now().Add(30 * 24 * time.Hour).Format(time.RFC3339),
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("ADC subscription updated webhook processed successfully")
		} else {
			t.Logf("ADC subscription update webhook failed: Status %d, Response: %s", w.Code, w.Body.String())
		}
	})

	// Test 3: Subscription cancelled event from ADC service
	suite.T().Run("ADC_Subscription_Cancelled", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type":      "subscription.cancelled",
			"event_id":        "adc_evt_12347",
			"timestamp":       time.Now().Format(time.RFC3339),
			"organization_id": suite.testShop.ID.String(),
			"subscription": map[string]interface{}{
				"id":              suite.testSubscription.ADCSubscriptionID,
				"organization_id": suite.testShop.ID.String(),
				"plan_id":         "plan_pro",
				"status":          "cancelled",
				"cancelled_at":    time.Now().Format(time.RFC3339),
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("ADC subscription cancelled webhook processed successfully")
			
			// Verify local subscription status was updated
			var updatedSubscription models.ShopSubscription
			err := database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
			if err == nil && updatedSubscription.Status == "cancelled" {
				t.Log("Local subscription status correctly updated to cancelled")
			}
		} else {
			t.Logf("ADC subscription cancellation webhook failed: Status %d, Response: %s", w.Code, w.Body.String())
		}
	})
}

// Test webhook signature validation
func (suite *WebhookV2TestSuite) TestWebhookSignatureValidation() {
	suite.T().Log("Testing webhook signature validation...")

	// Test 1: Valid Stripe signature
	suite.T().Run("Valid_Stripe_Signature", func(t *testing.T) {
		event := map[string]interface{}{
			"id":     "evt_test_valid",
			"object": "event",
			"type":   "ping",
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// Should not reject based on signature alone
		assert.NotEqual(t, http.StatusUnauthorized, w.Code, "Valid signature should not be rejected")
	})

	// Test 2: Invalid Stripe signature
	suite.T().Run("Invalid_Stripe_Signature", func(t *testing.T) {
		event := map[string]interface{}{
			"id":     "evt_test_invalid",
			"object": "event",
			"type":   "ping",
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", "invalid-signature")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code, "Invalid signature should be rejected")
		t.Logf("Invalid signature correctly rejected: %s", w.Body.String())
	})

	// Test 3: Valid ADC signature
	suite.T().Run("Valid_ADC_Signature", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type": "ping",
			"event_id":   "adc_ping_123",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// Should not reject based on signature alone
		assert.NotEqual(t, http.StatusUnauthorized, w.Code, "Valid ADC signature should not be rejected")
	})

	// Test 4: Invalid ADC signature
	suite.T().Run("Invalid_ADC_Signature", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type": "ping",
			"event_id":   "adc_ping_124",
			"timestamp":  time.Now().Format(time.RFC3339),
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", "invalid-adc-signature")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code, "Invalid ADC signature should be rejected")
		t.Logf("Invalid ADC signature correctly rejected: %s", w.Body.String())
	})
}

// Test webhook error scenarios
func (suite *WebhookV2TestSuite) TestWebhookErrorScenarios() {
	suite.T().Log("Testing webhook error scenarios...")

	// Test 1: Malformed JSON
	suite.T().Run("Malformed_JSON", func(t *testing.T) {
		malformedJSON := []byte(`{"invalid": json}`)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(malformedJSON))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(malformedJSON))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code, "Malformed JSON should be rejected")
	})

	// Test 2: Missing required fields
	suite.T().Run("Missing_Required_Fields", func(t *testing.T) {
		event := map[string]interface{}{
			"id": "evt_missing_fields",
			// Missing "type" and "data" fields
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code, "Missing required fields should be rejected")
	})

	// Test 3: Non-existent shop ID in metadata
	suite.T().Run("NonExistent_Shop_ID", func(t *testing.T) {
		event := map[string]interface{}{
			"id":      "evt_test_nonexistent",
			"object":  "event",
			"type":    "checkout.session.completed",
			"created": time.Now().Unix(),
			"data": map[string]interface{}{
				"object": map[string]interface{}{
					"id":           "cs_test_nonexistent",
					"object":       "checkout_session",
					"mode":         "subscription",
					"status":       "complete",
					"subscription": "sub_test_nonexistent",
					"metadata": map[string]interface{}{
						"shop_id": uuid.New().String(), // Non-existent shop ID
						"user_id": suite.testUser.ID.String(),
					},
				},
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code, "Non-existent shop should return 404")
	})

	// Test 4: Duplicate webhook processing (idempotency)
	suite.T().Run("Duplicate_Webhook_Processing", func(t *testing.T) {
		event := map[string]interface{}{
			"id":      "evt_test_duplicate",
			"object":  "event",
			"type":    "ping",
			"created": time.Now().Unix(),
		}

		jsonBody, _ := json.Marshal(event)

		// Process webhook first time
		req1, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req1.Header.Set("Content-Type", "application/json")
		req1.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w1 := httptest.NewRecorder()
		suite.router.ServeHTTP(w1, req1)

		// Process same webhook second time
		req2, _ := http.NewRequest("POST", "/webhook/stripe/v2", bytes.NewBuffer(jsonBody))
		req2.Header.Set("Content-Type", "application/json")
		req2.Header.Set("Stripe-Signature", suite.generateStripeSignature(jsonBody))

		w2 := httptest.NewRecorder()
		suite.router.ServeHTTP(w2, req2)

		// Both should succeed (idempotent processing)
		assert.Equal(t, w1.Code, w2.Code, "Duplicate webhook processing should be idempotent")
		t.Logf("First response: %d, Second response: %d", w1.Code, w2.Code)
	})
}

// Test webhook integration with ADC services
func (suite *WebhookV2TestSuite) TestWebhookADCIntegration() {
	suite.T().Log("Testing webhook integration with ADC services...")

	// Test 1: Webhook triggers usage reporting
	suite.T().Run("Webhook_Triggers_Usage_Reporting", func(t *testing.T) {
		event := map[string]interface{}{
			"event_type":      "subscription.updated",
			"event_id":        "adc_evt_usage_123",
			"timestamp":       time.Now().Format(time.RFC3339),
			"organization_id": suite.testShop.ID.String(),
			"subscription": map[string]interface{}{
				"id":              suite.testSubscription.ADCSubscriptionID,
				"organization_id": suite.testShop.ID.String(),
				"plan_id":         "plan_pro",
				"status":          "active",
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("Webhook processed and should have triggered usage reporting")
			// In a real implementation, this would verify that usage events were sent to Analytics
		} else {
			t.Logf("Webhook processing failed: %s", w.Body.String())
		}
	})

	// Test 2: Webhook triggers organization sync
	suite.T().Run("Webhook_Triggers_Organization_Sync", func(t *testing.T) {
		// Test that subscription webhooks can trigger organization data sync
		// This ensures that changes in ADC services are reflected locally
		
		event := map[string]interface{}{
			"event_type":      "organization.updated",
			"event_id":        "adc_evt_org_123",
			"timestamp":       time.Now().Format(time.RFC3339),
			"organization_id": suite.testShop.ID.String(),
			"organization": map[string]interface{}{
				"id":          "org_adc_123",
				"external_id": suite.testShop.ID.String(),
				"name":        "Updated Shop Name",
				"status":      "active",
			},
		}

		jsonBody, _ := json.Marshal(event)
		req, _ := http.NewRequest("POST", "/webhook/adc/subscription", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-ADC-Signature", suite.generateADCSignature(jsonBody))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		if w.Code == http.StatusOK {
			t.Log("Organization update webhook processed successfully")
		} else {
			t.Logf("Organization update webhook failed: %s", w.Body.String())
		}
	})
}

// Helper function to generate Stripe signature for testing
func (suite *WebhookV2TestSuite) generateStripeSignature(payload []byte) string {
	timestamp := time.Now().Unix()
	mac := hmac.New(sha256.New, []byte(suite.webhookSecret))
	mac.Write([]byte(string(rune(timestamp))))
	mac.Write([]byte("."))
	mac.Write(payload)
	signature := hex.EncodeToString(mac.Sum(nil))
	
	return "t=" + string(rune(timestamp)) + ",v1=" + signature
}

// Helper function to generate ADC signature for testing
func (suite *WebhookV2TestSuite) generateADCSignature(payload []byte) string {
	mac := hmac.New(sha256.New, []byte(suite.webhookSecret))
	mac.Write(payload)
	signature := hex.EncodeToString(mac.Sum(nil))
	
	return "sha256=" + signature
}

// TestWebhookV2Integration runs the webhook V2 integration test suite
func TestWebhookV2Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping webhook V2 integration tests in short mode")
	}

	suite.Run(t, new(WebhookV2TestSuite))
}