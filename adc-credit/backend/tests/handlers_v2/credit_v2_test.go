package handlers_v2

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// CreditV2HandlerTestSuite tests the new credit V2 handler
type CreditV2HandlerTestSuite struct {
	suite.Suite
	router           *gin.Engine
	serviceContainer *services.ServiceContainer
	testUser         *models.User
	testShop         *models.Shop
	testSubscription *models.ShopSubscription
	authToken        string
}

// SetupSuite initializes the test environment
func (suite *CreditV2HandlerTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize ADC service clients (mock or real)
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)

	// Initialize service container
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		subscriptionClient,
		analyticsClient,
		ssoClient,
	)

	// Setup router
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "Credit V2 Test User",
		GoogleID: "creditv2-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test Credit Shop V2",
		Slug:    "test-credit-shop-v2",
		Type:    "api_service",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Create test subscription tier (we still need this for local ShopSubscription)
	testTier := &models.SubscriptionTier{
		Name:        "Test Pro",
		Description: "Test Pro tier for V2 testing",
		Price:       29.99,
		CreditLimit: 10000,
	}
	err = database.DB.Create(testTier).Error
	if err != nil {
		// Tier might not exist in new schema, skip this part
		suite.T().Logf("Warning: Could not create subscription tier: %v", err)
	}

	// Create test shop subscription
	suite.testSubscription = &models.ShopSubscription{
		ID:                uuid.New(),
		ShopID:            suite.testShop.ID,
		ADCSubscriptionID: "adc-sub-12345",
		ADCOrganizationID: suite.testShop.ID.String(), // Shop ID = Organization External ID
		Status:            "active",
		CreditBalance:     1000,
	}
	err = database.DB.Create(suite.testSubscription).Error
	require.NoError(suite.T(), err)

	// Setup routes with V2 handler
	creditV2Handler := handlers.NewCreditV2Handler(suite.serviceContainer)
	v2 := suite.router.Group("/api/v2")
	{
		v2.GET("/credits/balance", suite.authMiddleware(), creditV2Handler.GetCreditBalance)
		v2.POST("/credits/add", suite.authMiddleware(), creditV2Handler.AddCredits)
		v2.GET("/credits/transactions", suite.authMiddleware(), creditV2Handler.GetTransactions)
		v2.POST("/external/verify", creditV2Handler.VerifyAPIKey)
		v2.POST("/external/consume", creditV2Handler.ConsumeCredits)
	}

	// Generate auth token for tests
	suite.authToken = testutils.GenerateTestJWT(suite.testUser.ID)
}

// TearDownSuite cleans up after tests
func (suite *CreditV2HandlerTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testSubscription.ID).Delete(&models.ShopSubscription{})
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// authMiddleware simulates authentication middleware for testing
func (suite *CreditV2HandlerTestSuite) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set authenticated user in context
		c.Set("user", *suite.testUser)
		c.Next()
	}
}

// Test credit balance retrieval with shop-based system
func (suite *CreditV2HandlerTestSuite) TestGetCreditBalance() {
	suite.T().Log("Testing V2 credit balance retrieval...")

	req, _ := http.NewRequest("GET", "/api/v2/credits/balance", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	// Verify response structure
	assert.Contains(suite.T(), response, "balance")
	assert.Contains(suite.T(), response, "shop_id")
	assert.Equal(suite.T(), float64(1000), response["balance"])
	assert.Equal(suite.T(), suite.testShop.ID.String(), response["shop_id"])

	suite.T().Logf("Credit balance response: %+v", response)
}

// Test credit addition with shop-based system
func (suite *CreditV2HandlerTestSuite) TestAddCredits() {
	suite.T().Log("Testing V2 credit addition...")

	requestBody := map[string]interface{}{
		"amount":      500,
		"description": "Test credit addition via V2",
		"reference":   "test-ref-v2-123",
	}

	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v2/credits/add", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Contains(suite.T(), response, "transaction_id")
	assert.Contains(suite.T(), response, "new_balance")
	assert.Equal(suite.T(), float64(1500), response["new_balance"]) // 1000 + 500

	suite.T().Logf("Add credits response: %+v", response)

	// Verify database was updated
	var updatedSubscription models.ShopSubscription
	err = database.DB.Where("id = ?", suite.testSubscription.ID).First(&updatedSubscription).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1500, updatedSubscription.CreditBalance)
}

// Test transaction history retrieval
func (suite *CreditV2HandlerTestSuite) TestGetTransactions() {
	suite.T().Log("Testing V2 transaction history retrieval...")

	req, _ := http.NewRequest("GET", "/api/v2/credits/transactions", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	// Verify response structure
	assert.Contains(suite.T(), response, "transactions")
	assert.Contains(suite.T(), response, "shop_id")

	transactions, ok := response["transactions"].([]interface{})
	assert.True(suite.T(), ok, "Transactions should be an array")

	suite.T().Logf("Transactions response: %+v", response)
	suite.T().Logf("Number of transactions: %d", len(transactions))
}

// Test API key verification for external usage
func (suite *CreditV2HandlerTestSuite) TestVerifyAPIKey() {
	suite.T().Log("Testing V2 API key verification...")

	// Create test API key
	testAPIKey := &models.APIKey{
		ID:     uuid.New(),
		UserID: suite.testUser.ID,
		Name:   "Test API Key V2",
		Key:    "test-api-key-v2-123",
		Active: true,
	}
	err := database.DB.Create(testAPIKey).Error
	require.NoError(suite.T(), err)
	defer database.DB.Delete(testAPIKey)

	requestBody := map[string]interface{}{
		"api_key": testAPIKey.Key,
	}

	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v2/external/verify", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Contains(suite.T(), response, "valid")
	assert.Contains(suite.T(), response, "user_id")
	assert.Contains(suite.T(), response, "credit_balance")
	assert.Equal(suite.T(), true, response["valid"])
	assert.Equal(suite.T(), suite.testUser.ID.String(), response["user_id"])

	suite.T().Logf("API key verification response: %+v", response)
}

// Test credit consumption through external API
func (suite *CreditV2HandlerTestSuite) TestConsumeCredits() {
	suite.T().Log("Testing V2 credit consumption...")

	// Create test API key
	testAPIKey := &models.APIKey{
		ID:     uuid.New(),
		UserID: suite.testUser.ID,
		Name:   "Test API Key V2 Consume",
		Key:    "test-api-key-consume-v2-456",
		Active: true,
	}
	err := database.DB.Create(testAPIKey).Error
	require.NoError(suite.T(), err)
	defer database.DB.Delete(testAPIKey)

	requestBody := map[string]interface{}{
		"api_key":     testAPIKey.Key,
		"amount":      100,
		"description": "Test credit consumption via V2 API",
		"reference":   "ext-consume-test-v2",
	}

	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v2/external/consume", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should succeed if user has sufficient credits
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(suite.T(), err)

		assert.Contains(suite.T(), response, "success")
		assert.Contains(suite.T(), response, "remaining_balance")
		assert.Equal(suite.T(), true, response["success"])

		suite.T().Logf("Credit consumption response: %+v", response)

		// Verify balance was reduced
		var updatedSubscription models.ShopSubscription
		err = database.DB.Where("shop_id = ?", suite.testShop.ID).First(&updatedSubscription).Error
		require.NoError(suite.T(), err)
		assert.LessOrEqual(suite.T(), updatedSubscription.CreditBalance, 1500) // Should be less than or equal to previous balance
	} else {
		suite.T().Logf("Credit consumption failed (may be expected): Status %d, Response: %s", w.Code, w.Body.String())
	}
}

// Test error scenarios
func (suite *CreditV2HandlerTestSuite) TestErrorScenarios() {
	suite.T().Log("Testing V2 error scenarios...")

	// Test 1: Invalid API key
	suite.T().Run("Invalid_API_Key", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"api_key": "invalid-api-key-12345",
		}

		jsonBody, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v2/external/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		t.Logf("Invalid API key response: %s", w.Body.String())
	})

	// Test 2: Insufficient credits for consumption
	suite.T().Run("Insufficient_Credits", func(t *testing.T) {
		// Create API key for user with no credits
		testAPIKey := &models.APIKey{
			ID:     uuid.New(),
			UserID: suite.testUser.ID,
			Name:   "Test API Key No Credits",
			Key:    "test-api-key-no-credits-789",
			Active: true,
		}
		err := database.DB.Create(testAPIKey).Error
		require.NoError(t, err)
		defer database.DB.Delete(testAPIKey)

		// Try to consume more credits than available
		requestBody := map[string]interface{}{
			"api_key":     testAPIKey.Key,
			"amount":      999999, // Much more than available
			"description": "Test insufficient credits",
		}

		jsonBody, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v2/external/consume", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// Should fail with insufficient credits
		assert.Equal(t, http.StatusBadRequest, w.Code)
		t.Logf("Insufficient credits response: %s", w.Body.String())
	})

	// Test 3: Missing authentication
	suite.T().Run("Missing_Authentication", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v2/credits/balance", nil)
		// No Authorization header

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		t.Logf("Missing auth response: %s", w.Body.String())
	})
}

// Test integration with ADC services (if available)
func (suite *CreditV2HandlerTestSuite) TestADCIntegration() {
	suite.T().Log("Testing V2 ADC service integration...")

	// Test that V2 handlers can work with ADC service integration
	suite.T().Run("Service_Container_Integration", func(t *testing.T) {
		// Verify service container is properly initialized
		assert.NotNil(t, suite.serviceContainer, "Service container should be available")
		assert.NotNil(t, suite.serviceContainer.GetSSOClient(), "SSO client should be available")
		assert.NotNil(t, suite.serviceContainer.GetSubscriptionClient(), "Subscription client should be available")
		assert.NotNil(t, suite.serviceContainer.GetAnalyticsClient(), "Analytics client should be available")

		t.Log("All ADC service clients are properly initialized in V2 handlers")
	})

	// Test usage tracking integration
	suite.T().Run("Usage_Tracking_Integration", func(t *testing.T) {
		// This would test that credit operations trigger usage reporting
		// For now, we verify the infrastructure is in place
		usageService := suite.serviceContainer.GetUsageReportingService()
		if usageService != nil {
			t.Log("Usage reporting service is available for V2 handlers")
		} else {
			t.Log("Usage reporting service not yet implemented")
		}
	})
}

// TestCreditV2HandlerIntegration runs the Credit V2 handler integration test suite
func TestCreditV2HandlerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Credit V2 handler integration tests in short mode")
	}

	suite.Run(t, new(CreditV2HandlerTestSuite))
}