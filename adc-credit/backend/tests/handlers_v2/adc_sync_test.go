package handlers_v2

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/handlers"
	"github.com/adc-credit/backend/internal/models"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// ADCSyncHandlerTestSuite tests the ADC synchronization handler
type ADCSyncHandlerTestSuite struct {
	suite.Suite
	router           *gin.Engine
	serviceContainer *services.ServiceContainer
	testUser         *models.User
	testShop         *models.Shop
	authToken        string
}

// SetupSuite initializes the test environment
func (suite *ADCSyncHandlerTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err)

	// Initialize ADC service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err)

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err)

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err)

	// Initialize service container
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		subscriptionClient,
		analyticsClient,
		ssoClient,
	)

	// Setup router
	suite.router = gin.New()
	suite.router.Use(gin.Recovery())

	// Create test user
	suite.testUser = &models.User{
		ID:       uuid.New(),
		Email:    "<EMAIL>",
		Name:     "ADC Sync Test User",
		GoogleID: "adcsync-google-123",
	}
	err = database.DB.Create(suite.testUser).Error
	require.NoError(suite.T(), err)

	// Create test shop
	suite.testShop = &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test ADC Sync Shop",
		Slug:    "test-adc-sync-shop",
		Type:    "api_service",
	}
	err = database.DB.Create(suite.testShop).Error
	require.NoError(suite.T(), err)

	// Setup routes with ADC sync handler
	adcSyncHandler := handlers.NewADCSyncHandler(suite.serviceContainer)
	sync := suite.router.Group("/api/v1/adc-sync")
	sync.Use(suite.authMiddleware())
	{
		sync.POST("/shops/:shopId", adcSyncHandler.SyncShopToADC)
		sync.POST("/shops/all", adcSyncHandler.SyncAllShopsToADC)
		sync.GET("/shops/:shopId/status", adcSyncHandler.GetADCSubscriptionStatus)
	}

	// Generate auth token for tests
	suite.authToken = testutils.GenerateTestJWT(suite.testUser.ID)
}

// TearDownSuite cleans up after tests
func (suite *ADCSyncHandlerTestSuite) TearDownSuite() {
	if database.DB != nil {
		// Clean up test data
		database.DB.Where("id = ?", suite.testShop.ID).Delete(&models.Shop{})
		database.DB.Where("id = ?", suite.testUser.ID).Delete(&models.User{})
		
		testutils.CleanupTestEnvironment()
	}
}

// authMiddleware simulates authentication middleware for testing
func (suite *ADCSyncHandlerTestSuite) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set authenticated user in context
		c.Set("user", *suite.testUser)
		c.Next()
	}
}

// Test individual shop synchronization to ADC
func (suite *ADCSyncHandlerTestSuite) TestSyncShopToADC() {
	suite.T().Log("Testing individual shop sync to ADC...")

	url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", suite.testShop.ID)
	req, _ := http.NewRequest("POST", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return OK or an informative error if ADC services are not available
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(suite.T(), err)

		// Verify response structure
		assert.Contains(suite.T(), response, "shop_id")
		assert.Contains(suite.T(), response, "status")
		assert.Equal(suite.T(), suite.testShop.ID.String(), response["shop_id"])

		suite.T().Logf("Shop sync response: %+v", response)

		// Check if organization was created in response
		if orgData, exists := response["organization"]; exists {
			orgMap, ok := orgData.(map[string]interface{})
			assert.True(suite.T(), ok, "Organization data should be a map")
			assert.Contains(suite.T(), orgMap, "id")
			assert.Contains(suite.T(), orgMap, "external_id")
			suite.T().Logf("Created organization: %+v", orgMap)
		}

		// Check if subscription was created/synced
		if subData, exists := response["subscription"]; exists {
			subMap, ok := subData.(map[string]interface{})
			assert.True(suite.T(), ok, "Subscription data should be a map")
			suite.T().Logf("Synced subscription: %+v", subMap)
		}

	} else {
		suite.T().Logf("Shop sync failed (may be expected if ADC services unavailable): Status %d, Response: %s", w.Code, w.Body.String())
		
		// Parse error response
		var errorResponse map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
		if err == nil {
			assert.Contains(suite.T(), errorResponse, "error")
			suite.T().Logf("Error details: %s", errorResponse["error"])
		}
	}
}

// Test bulk shop synchronization to ADC
func (suite *ADCSyncHandlerTestSuite) TestSyncAllShopsToADC() {
	suite.T().Log("Testing bulk shop sync to ADC...")

	// Create additional test shop for bulk sync
	testShop2 := &models.Shop{
		ID:      uuid.New(),
		OwnerID: suite.testUser.ID,
		Name:    "Test ADC Sync Shop 2",
		Slug:    "test-adc-sync-shop-2",
		Type:    "retail",
	}
	err := database.DB.Create(testShop2).Error
	require.NoError(suite.T(), err)
	defer database.DB.Delete(testShop2)

	req, _ := http.NewRequest("POST", "/api/v1/adc-sync/shops/all", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return OK or an informative error
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(suite.T(), err)

		// Verify response structure
		assert.Contains(suite.T(), response, "total_shops")
		assert.Contains(suite.T(), response, "synced_count")
		assert.Contains(suite.T(), response, "failed_count")

		totalShops, ok := response["total_shops"].(float64)
		assert.True(suite.T(), ok, "Total shops should be a number")
		assert.GreaterOrEqual(suite.T(), int(totalShops), 2, "Should have at least 2 test shops")

		suite.T().Logf("Bulk sync response: %+v", response)

		// Check individual shop results if available
		if results, exists := response["results"]; exists {
			resultsArray, ok := results.([]interface{})
			assert.True(suite.T(), ok, "Results should be an array")
			suite.T().Logf("Number of sync results: %d", len(resultsArray))

			for i, result := range resultsArray {
				resultMap, ok := result.(map[string]interface{})
				assert.True(suite.T(), ok, "Each result should be a map")
				assert.Contains(suite.T(), resultMap, "shop_id")
				assert.Contains(suite.T(), resultMap, "status")
				suite.T().Logf("Shop %d sync result: %+v", i+1, resultMap)
			}
		}

	} else {
		suite.T().Logf("Bulk sync failed (may be expected if ADC services unavailable): Status %d, Response: %s", w.Code, w.Body.String())
	}
}

// Test ADC subscription status retrieval
func (suite *ADCSyncHandlerTestSuite) TestGetADCSubscriptionStatus() {
	suite.T().Log("Testing ADC subscription status retrieval...")

	url := fmt.Sprintf("/api/v1/adc-sync/shops/%s/status", suite.testShop.ID)
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return OK or an informative error
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(suite.T(), err)

		// Verify response structure
		assert.Contains(suite.T(), response, "shop_id")
		assert.Contains(suite.T(), response, "local_subscription")
		assert.Contains(suite.T(), response, "adc_organization")
		assert.Contains(suite.T(), response, "adc_subscription")
		assert.Equal(suite.T(), suite.testShop.ID.String(), response["shop_id"])

		suite.T().Logf("Subscription status response: %+v", response)

		// Check local subscription status
		if localSub, exists := response["local_subscription"]; exists {
			localSubMap, ok := localSub.(map[string]interface{})
			assert.True(suite.T(), ok, "Local subscription should be a map")
			suite.T().Logf("Local subscription: %+v", localSubMap)
		}

		// Check ADC organization status
		if adcOrg, exists := response["adc_organization"]; exists {
			if adcOrg != nil {
				adcOrgMap, ok := adcOrg.(map[string]interface{})
				assert.True(suite.T(), ok, "ADC organization should be a map")
				assert.Contains(suite.T(), adcOrgMap, "id")
				suite.T().Logf("ADC organization: %+v", adcOrgMap)
			} else {
				suite.T().Log("ADC organization not found (not yet synced)")
			}
		}

		// Check ADC subscription status
		if adcSub, exists := response["adc_subscription"]; exists {
			if adcSub != nil {
				adcSubMap, ok := adcSub.(map[string]interface{})
				assert.True(suite.T(), ok, "ADC subscription should be a map")
				suite.T().Logf("ADC subscription: %+v", adcSubMap)
			} else {
				suite.T().Log("ADC subscription not found (not yet synced)")
			}
		}

	} else {
		suite.T().Logf("Status check failed (may be expected): Status %d, Response: %s", w.Code, w.Body.String())
	}
}

// Test error scenarios
func (suite *ADCSyncHandlerTestSuite) TestErrorScenarios() {
	suite.T().Log("Testing ADC sync error scenarios...")

	// Test 1: Sync non-existent shop
	suite.T().Run("Sync_NonExistent_Shop", func(t *testing.T) {
		nonExistentID := uuid.New()
		url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", nonExistentID)
		req, _ := http.NewRequest("POST", url, nil)
		req.Header.Set("Authorization", "Bearer "+suite.authToken)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
		t.Logf("Non-existent shop sync response: %s", w.Body.String())
	})

	// Test 2: Invalid shop ID format
	suite.T().Run("Invalid_Shop_ID", func(t *testing.T) {
		url := "/api/v1/adc-sync/shops/invalid-uuid-format"
		req, _ := http.NewRequest("POST", url, nil)
		req.Header.Set("Authorization", "Bearer "+suite.authToken)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		t.Logf("Invalid shop ID response: %s", w.Body.String())
	})

	// Test 3: Missing authentication
	suite.T().Run("Missing_Authentication", func(t *testing.T) {
		url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", suite.testShop.ID)
		req, _ := http.NewRequest("POST", url, nil)
		// No Authorization header

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		t.Logf("Missing auth response: %s", w.Body.String())
	})

	// Test 4: Shop not owned by user
	suite.T().Run("Shop_Not_Owned", func(t *testing.T) {
		// Create shop owned by different user
		otherUser := &models.User{
			ID:       uuid.New(),
			Email:    "<EMAIL>",
			Name:     "Other User",
			GoogleID: "other-google-123",
		}
		err := database.DB.Create(otherUser).Error
		require.NoError(t, err)
		defer database.DB.Delete(otherUser)

		otherShop := &models.Shop{
			ID:      uuid.New(),
			OwnerID: otherUser.ID,
			Name:    "Other User Shop",
			Slug:    "other-user-shop",
			Type:    "retail",
		}
		err = database.DB.Create(otherShop).Error
		require.NoError(t, err)
		defer database.DB.Delete(otherShop)

		// Try to sync shop not owned by authenticated user
		url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", otherShop.ID)
		req, _ := http.NewRequest("POST", url, nil)
		req.Header.Set("Authorization", "Bearer "+suite.authToken)

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
		t.Logf("Shop not owned response: %s", w.Body.String())
	})
}

// Test sync with different shop types
func (suite *ADCSyncHandlerTestSuite) TestSyncDifferentShopTypes() {
	suite.T().Log("Testing sync with different shop types...")

	shopTypes := []string{"retail", "api_service", "enterprise"}

	for _, shopType := range shopTypes {
		suite.T().Run(fmt.Sprintf("Sync_%s_Shop", shopType), func(t *testing.T) {
			// Create shop of specific type
			testShop := &models.Shop{
				ID:      uuid.New(),
				OwnerID: suite.testUser.ID,
				Name:    fmt.Sprintf("Test %s Shop", shopType),
				Slug:    fmt.Sprintf("test-%s-shop", shopType),
				Type:    shopType,
			}
			err := database.DB.Create(testShop).Error
			require.NoError(t, err)
			defer database.DB.Delete(testShop)

			// Sync shop to ADC
			url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", testShop.ID)
			req, _ := http.NewRequest("POST", url, nil)
			req.Header.Set("Authorization", "Bearer "+suite.authToken)

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			// Should handle different shop types appropriately
			if w.Code == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(t, err)

				assert.Equal(t, testShop.ID.String(), response["shop_id"])
				t.Logf("%s shop sync response: %+v", shopType, response)
			} else {
				t.Logf("%s shop sync failed (may be expected): %s", shopType, w.Body.String())
			}
		})
	}
}

// Test sync state consistency
func (suite *ADCSyncHandlerTestSuite) TestSyncStateConsistency() {
	suite.T().Log("Testing sync state consistency...")

	// Test that multiple syncs of the same shop maintain consistency
	suite.T().Run("Multiple_Sync_Consistency", func(t *testing.T) {
		url := fmt.Sprintf("/api/v1/adc-sync/shops/%s", suite.testShop.ID)

		// Perform first sync
		req1, _ := http.NewRequest("POST", url, nil)
		req1.Header.Set("Authorization", "Bearer "+suite.authToken)

		w1 := httptest.NewRecorder()
		suite.router.ServeHTTP(w1, req1)

		// Perform second sync
		req2, _ := http.NewRequest("POST", url, nil)
		req2.Header.Set("Authorization", "Bearer "+suite.authToken)

		w2 := httptest.NewRecorder()
		suite.router.ServeHTTP(w2, req2)

		// Both should succeed or fail consistently
		assert.Equal(t, w1.Code, w2.Code, "Multiple syncs should have consistent results")

		if w1.Code == http.StatusOK && w2.Code == http.StatusOK {
			var response1, response2 map[string]interface{}
			err1 := json.Unmarshal(w1.Body.Bytes(), &response1)
			err2 := json.Unmarshal(w2.Body.Bytes(), &response2)
			require.NoError(t, err1)
			require.NoError(t, err2)

			// Organization ID should be consistent across syncs
			if org1, exists1 := response1["organization"]; exists1 {
				if org2, exists2 := response2["organization"]; exists2 {
					org1Map := org1.(map[string]interface{})
					org2Map := org2.(map[string]interface{})
					assert.Equal(t, org1Map["id"], org2Map["id"], "Organization ID should be consistent")
				}
			}

			t.Log("Multiple sync consistency verified")
		}
	})
}

// TestADCSyncHandlerIntegration runs the ADC sync handler integration test suite
func TestADCSyncHandlerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ADC sync handler integration tests in short mode")
	}

	suite.Run(t, new(ADCSyncHandlerTestSuite))
}