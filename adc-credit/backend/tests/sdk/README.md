# ADC Credit SDK Integration Tests (Go)

This directory contains comprehensive Go integration tests for the ADC Credit SDK, testing all SDK functionality against the real backend API.

## 🎯 Overview

The Go SDK integration tests verify:
- **Real API Integration** - Direct HTTP calls to backend endpoints
- **Authentication Workflows** - JWT tokens and API key authentication
- **Complete CRUD Operations** - Create, Read, Update, Delete for all resources
- **End-to-End Workflows** - Complete user journeys from registration to API usage
- **Error Handling** - Network failures, authentication errors, validation errors
- **Performance Testing** - Response times and throughput benchmarks

## 📁 Test Structure

```
backend/tests/sdk/
├── README.md                    # This file
├── client.go                    # Go SDK client implementation
├── integration_test.go          # Main integration test suite
├── run_tests.sh                 # Test runner script
└── coverage.html               # Generated coverage report
```

## 🧪 Test Categories

### 1. Health and Connectivity Tests
- ✅ API server health check
- ✅ SDK client initialization
- ✅ Basic HTTP connectivity

### 2. Authentication Tests
- ✅ User registration workflow
- ✅ Email/password login
- ✅ JWT token validation
- ✅ Authentication required endpoints
- ✅ Invalid credentials handling

### 3. Credits Integration Tests
- ✅ Credit balance retrieval
- ✅ Transaction history
- ✅ Credit consumption (external API)
- ✅ Subscription information
- ✅ Error handling for insufficient credits

### 4. Shops Integration Tests
- ✅ Shop CRUD operations (Create, Read, Update, Delete)
- ✅ Shop customer management
- ✅ Shop credit management
- ✅ Credit code generation
- ✅ Shop statistics and analytics

### 5. API Key Management Tests
- ✅ API key CRUD operations
- ✅ Permission management
- ✅ Key enabling/disabling
- ✅ Authentication with API keys

### 6. End-to-End Workflow Tests
- ✅ Complete user journey: Registration → Shop creation → Customer management → Credit operations → API key usage
- ✅ Cross-module integration testing
- ✅ Real-world usage scenarios

## 🚀 Running Tests

### Prerequisites

1. **Go Environment** - Go 1.21+ installed
2. **Database** - PostgreSQL test database running
3. **Environment** - `.env.test` file configured
4. **Backend Directory** - Run from `backend/` directory

### Quick Start

```bash
# Navigate to backend directory
cd backend

# Run all SDK integration tests
./tests/sdk/run_tests.sh

# Or use Make command
make test-sdk-go
```

### Test Runner Options

```bash
# Run all tests
./tests/sdk/run_tests.sh all

# Run with coverage report
./tests/sdk/run_tests.sh coverage

# Run performance benchmarks
./tests/sdk/run_tests.sh benchmarks

# Run specific test categories
./tests/sdk/run_tests.sh health
./tests/sdk/run_tests.sh auth
./tests/sdk/run_tests.sh credits
./tests/sdk/run_tests.sh shops
./tests/sdk/run_tests.sh apikeys
./tests/sdk/run_tests.sh journey

# Setup test environment only
./tests/sdk/run_tests.sh setup

# Clean test data only
./tests/sdk/run_tests.sh clean

# Generate test report
./tests/sdk/run_tests.sh report
```

### Using Make Commands

```bash
# Run Go SDK tests
make test-sdk-go

# Run with coverage
make test-sdk-go-coverage

# Run specific test
make test-sdk-go-specific TEST=auth

# Run all tests (includes SDK tests)
make test
```

### Manual Test Execution

```bash
# Run from tests/sdk directory
cd backend/tests/sdk

# Run all tests with verbose output
go test -v -timeout=300s ./...

# Run specific test
go test -v -run "TestSDKIntegrationSuite/TestHealthEndpoint" ./...

# Run with coverage
go test -v -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

# Run benchmarks
go test -v -bench=. -benchmem ./...
```

## 🔧 Configuration

### Environment Variables

Create `.env.test` in the backend directory:

```bash
# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=test_user
DB_PASSWORD=test_password
DB_NAME=adc_credit_test

# JWT configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only

# API configuration
API_PORT=8400
API_HOST=localhost

# Test environment
GO_ENV=test
GIN_MODE=test
```

### Test Database Setup

```sql
-- Create test database
CREATE DATABASE adc_credit_test;

-- Create test user
CREATE USER test_user WITH PASSWORD 'test_password';
GRANT ALL PRIVILEGES ON DATABASE adc_credit_test TO test_user;
```

## 🏗️ SDK Client Architecture

### HTTP Client (`client.go`)
- **Authentication Support** - API keys and JWT tokens
- **Request/Response Handling** - JSON serialization/deserialization
- **Error Handling** - HTTP status codes and API errors
- **Debug Logging** - Request/response logging for debugging

### API Methods
- **Authentication** - Login, register, token refresh
- **Credits** - Balance, transactions, consumption
- **Shops** - CRUD operations, customers, API keys
- **API Keys** - Management and permissions

### Response Handling
```go
type APIResponse struct {
    Data    interface{} `json:"data,omitempty"`
    Error   string      `json:"error,omitempty"`
    Message string      `json:"message,omitempty"`
    Success bool        `json:"success,omitempty"`
}
```

## 🧪 Test Structure

### Test Suite Setup
```go
type SDKIntegrationTestSuite struct {
    suite.Suite
    server *httptest.Server
    client *SDKClient
    router *gin.Engine
}
```

### Test Lifecycle
1. **SetupSuite** - Initialize test server and routes
2. **SetupTest** - Clean test data before each test
3. **Test Execution** - Run individual test cases
4. **TearDownTest** - Clean test data after each test
5. **TearDownSuite** - Cleanup test server

### Test Data Management
- **Unique Data Generation** - Timestamp-based unique identifiers
- **Automatic Cleanup** - Test data removed after each test
- **Isolation** - No data pollution between tests

## 📊 Test Data

### Test Users
- **Email Pattern**: `test-{timestamp}@example.com`
- **Password**: `testpassword123` or `password123`
- **Name**: Descriptive names like "Credits User", "Shops User"

### Test Shops
- **Name Pattern**: `Test Shop {timestamp}`
- **Types**: `retail`, `api_service`, `enterprise`
- **Contact**: Generated emails and phone numbers

### Test Customers
- **Name Pattern**: `Test Customer`, `Journey Customer`
- **Email Pattern**: `customer-{timestamp}@example.com`
- **Phone**: `+1234567890`

## 🔍 Debugging Tests

### Enable Debug Mode
```bash
# Run with debug output
DEBUG=1 ./tests/sdk/run_tests.sh

# Run specific test with verbose output
go test -v -run "TestSpecificTest" ./...
```

### Check Test Database
```bash
# Connect to test database
psql -h localhost -U test_user -d adc_credit_test

# Check test data
SELECT * FROM users WHERE email LIKE '%test%';
SELECT * FROM shops WHERE name LIKE '%Test%';
```

### Common Issues

1. **Database Connection** - Check `.env.test` configuration
2. **Port Conflicts** - Ensure test ports are available
3. **Authentication Errors** - Verify JWT secret configuration
4. **Test Data Conflicts** - Run `./tests/sdk/run_tests.sh clean`

## 📈 Performance Testing

### Benchmarks
```bash
# Run performance benchmarks
./tests/sdk/run_tests.sh benchmarks

# Or manually
go test -v -bench=. -benchmem ./...
```

### Coverage Reports
```bash
# Generate coverage report
./tests/sdk/run_tests.sh coverage

# View coverage report
open coverage.html
```

### Performance Metrics
- **Response Time** - Average API response times
- **Throughput** - Requests per second
- **Memory Usage** - Memory allocation patterns
- **Concurrency** - Concurrent request handling

## 🚀 Continuous Integration

### GitHub Actions
```yaml
- name: Run Go SDK Integration Tests
  run: |
    cd backend
    ./tests/sdk/run_tests.sh coverage

- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: backend/tests/sdk/coverage.out
```

### Docker Testing
```bash
# Run tests in Docker
docker run --rm -v $(pwd):/app -w /app/backend golang:1.21 \
  ./tests/sdk/run_tests.sh
```

## 🤝 Contributing

When adding new SDK functionality:

1. **Add corresponding tests** in `integration_test.go`
2. **Update SDK client** in `client.go` with new methods
3. **Test both success and error scenarios**
4. **Update this README** with new test descriptions
5. **Run full test suite** to ensure no regressions

### Test Naming Convention
- Use descriptive test names: `TestShopsWorkflow`
- Group related tests in test suites
- Include expected behavior in test names
- Follow Go testing conventions

## 📋 Test Checklist

Before submitting changes:

- [ ] All tests pass locally
- [ ] Coverage report generated
- [ ] No test data pollution
- [ ] Error scenarios tested
- [ ] Documentation updated
- [ ] Performance benchmarks run

## 🎯 Next Steps

- Add more edge case testing
- Implement load testing scenarios
- Add integration with monitoring
- Enhance error scenario coverage
- Add API versioning tests
