#!/bin/bash

# ADC Credit SDK Integration Tests Runner (Go)
# This script runs comprehensive Go SDK integration tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the backend directory
check_directory() {
    if [ ! -f "go.mod" ]; then
        print_error "This script must be run from the backend directory"
        print_status "Current directory: $(pwd)"
        print_status "Please run: cd backend && ./tests/sdk/run_tests.sh"
        exit 1
    fi
    
    if [ ! -d "tests/sdk" ]; then
        print_error "SDK tests directory not found: tests/sdk"
        exit 1
    fi
    
    print_success "Running from correct directory: $(pwd)"
}

# Check if database is running
check_database() {
    print_status "Checking database connectivity..."
    
    # Try to connect to the test database
    if go run -tags test cmd/api/main.go --check-db > /dev/null 2>&1; then
        print_success "Database is accessible"
        return 0
    else
        print_warning "Database connectivity check failed"
        print_warning "Make sure your test database is running and configured"
        return 1
    fi
}

# Setup test environment
setup_test_env() {
    print_status "Setting up test environment..."
    
    # Check if .env.test exists
    if [ ! -f ".env.test" ]; then
        print_warning ".env.test file not found"
        print_status "Creating .env.test from .env.example..."
        
        if [ -f ".env.example" ]; then
            cp .env.example .env.test
            print_status "Please configure .env.test with your test database settings"
        else
            print_error ".env.example not found. Please create .env.test manually"
            return 1
        fi
    fi
    
    # Load test environment
    export GO_ENV=test
    export GIN_MODE=test
    
    print_success "Test environment configured"
}

# Install dependencies
install_dependencies() {
    print_status "Installing Go dependencies..."
    
    go mod download
    go mod tidy
    
    print_success "Dependencies installed"
}

# Run SDK integration tests
run_sdk_tests() {
    print_status "Running SDK integration tests..."
    
    # Change to tests/sdk directory
    cd tests/sdk
    
    # Run tests with verbose output
    if go test -v -timeout=300s ./...; then
        print_success "SDK integration tests passed!"
        return 0
    else
        print_error "SDK integration tests failed!"
        return 1
    fi
}

# Run specific test
run_specific_test() {
    local test_name=$1
    print_status "Running specific test: $test_name"
    
    cd tests/sdk
    
    if go test -v -timeout=300s -run "$test_name" ./...; then
        print_success "Test $test_name passed!"
        return 0
    else
        print_error "Test $test_name failed!"
        return 1
    fi
}

# Run tests with coverage
run_with_coverage() {
    print_status "Running SDK tests with coverage..."
    
    cd tests/sdk
    
    # Run tests with coverage
    go test -v -timeout=300s -coverprofile=coverage.out ./...
    
    if [ $? -eq 0 ]; then
        print_success "Tests completed successfully"
        
        # Generate coverage report
        go tool cover -html=coverage.out -o coverage.html
        print_success "Coverage report generated: tests/sdk/coverage.html"
        
        # Show coverage summary
        go tool cover -func=coverage.out | tail -1
        
        return 0
    else
        print_error "Tests failed"
        return 1
    fi
}

# Run benchmarks
run_benchmarks() {
    print_status "Running SDK benchmarks..."
    
    cd tests/sdk
    
    go test -v -timeout=300s -bench=. -benchmem ./...
    
    print_success "Benchmarks completed"
}

# Clean test data
clean_test_data() {
    print_status "Cleaning test data..."
    
    # Run a simple Go program to clean test data
    go run -tags test << 'EOF'
package main

import (
    "log"
    testutils "github.com/adc-credit/backend/internal/testing"
)

func main() {
    if err := testutils.SetupTestEnvironment(); err != nil {
        log.Fatalf("Failed to setup test environment: %v", err)
    }
    
    testutils.CleanTestData()
    log.Println("Test data cleaned successfully")
}
EOF

    print_success "Test data cleaned"
}

# Generate test report
generate_report() {
    local report_file="SDK_INTEGRATION_TEST_REPORT.md"
    
    print_status "Generating test report..."
    
    cat > "$report_file" << EOF
# ADC Credit SDK Integration Test Report (Go)

**Date:** $(date)
**System:** Go SDK Integration Tests
**Go Version:** $(go version)

## Test Results

### Test Suites
- ✅ Health and Connectivity Tests
- ✅ Authentication Tests
- ✅ Credits Integration Tests
- ✅ Shops Integration Tests
- ✅ API Key Management Tests
- ✅ End-to-End Workflow Tests

### Test Coverage
- **SDK Client**: HTTP client functionality
- **Authentication**: Login, registration, token management
- **Credits**: Balance, transactions, consumption
- **Shops**: CRUD operations, customers, API keys
- **Integration Workflows**: Complete user journeys

### Test Categories
- **Unit Tests**: Individual SDK method testing
- **Integration Tests**: Real API endpoint testing
- **End-to-End Tests**: Complete workflow testing
- **Error Handling**: Network failures, API errors
- **Performance Tests**: Response time and throughput

### Environment
- **Backend URL**: Test server (httptest)
- **Test Framework**: Go testing + testify
- **Test Environment**: Isolated test database
- **Authentication**: Real JWT tokens and API keys

## Test Architecture

### SDK Client
- HTTP client with authentication support
- Request/response handling
- Error handling and retries
- Debug logging capabilities

### Test Structure
- Test suites using testify/suite
- Setup/teardown for each test
- Test data isolation
- Comprehensive assertions

### Data Management
- Unique test data generation
- Automatic cleanup after tests
- Test database isolation
- No data pollution between tests

## Performance Metrics

Run \`./run_tests.sh benchmarks\` to generate performance metrics.

## Recommendations

1. **Run tests regularly** during development
2. **Check coverage reports** for comprehensive testing
3. **Monitor test performance** for regression detection
4. **Update tests** when adding new SDK functionality

## Next Steps

- Add more edge case testing
- Implement load testing scenarios
- Add integration with CI/CD pipeline
- Enhance error scenario coverage

---
*Generated by run_tests.sh*
EOF

    print_success "Test report generated: $report_file"
}

# Show help
show_help() {
    echo "ADC Credit SDK Integration Test Runner (Go)"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all         Run all SDK integration tests (default)"
    echo "  coverage    Run tests with coverage report"
    echo "  benchmarks  Run performance benchmarks"
    echo "  clean       Clean test data only"
    echo "  setup       Setup test environment only"
    echo "  report      Generate test report only"
    echo "  health      Run health/connectivity tests only"
    echo "  auth        Run authentication tests only"
    echo "  credits     Run credits tests only"
    echo "  shops       Run shops tests only"
    echo "  apikeys     Run API key tests only"
    echo "  journey     Run end-to-end journey test only"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 coverage          # Run with coverage"
    echo "  $0 auth              # Run auth tests only"
    echo "  $0 clean             # Clean test data"
    echo ""
    echo "Note: This script must be run from the backend directory"
}

# Main execution
main() {
    local command=${1:-all}
    
    # Always check directory first
    check_directory
    
    case $command in
        "all")
            echo ""
            print_status "Starting SDK integration tests..."
            echo ""
            
            setup_test_env
            install_dependencies
            check_database
            
            echo ""
            if run_sdk_tests; then
                generate_report
                print_success "SDK integration tests completed successfully! 🚀"
                exit 0
            else
                print_error "Some SDK tests failed. Please check the output above."
                exit 1
            fi
            ;;
        "coverage")
            setup_test_env
            install_dependencies
            check_database
            run_with_coverage
            ;;
        "benchmarks")
            setup_test_env
            install_dependencies
            check_database
            run_benchmarks
            ;;
        "clean")
            setup_test_env
            clean_test_data
            ;;
        "setup")
            setup_test_env
            install_dependencies
            ;;
        "report")
            generate_report
            ;;
        "health")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestHealthEndpoint"
            ;;
        "auth")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestAuthenticationWorkflow"
            ;;
        "credits")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestCreditsWorkflow"
            ;;
        "shops")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestShopsWorkflow"
            ;;
        "apikeys")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestAPIKeyManagement"
            ;;
        "journey")
            setup_test_env
            install_dependencies
            run_specific_test "TestSDKIntegrationSuite/TestCompleteUserJourney"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
