package sdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// SDKClient represents the ADC Credit SDK client for Go integration tests
type SDKClient struct {
	BaseURL    string
	APIKey     string
	Token      string
	HTTPClient *http.Client
	Debug      bool
}

// APIResponse represents a standard API response
type APIResponse struct {
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
	Success bool        `json:"success,omitempty"`
}

// NewSDKClient creates a new SDK client instance
func NewSDKClient(baseURL string) *SDKClient {
	return &SDKClient{
		BaseURL: baseURL,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		Debug: false,
	}
}

// WithAPIKey sets the API key for authentication
func (c *SDKClient) WithAPIKey(apiKey string) *SDKClient {
	c.APIKey = apiKey
	return c
}

// WithToken sets the JWT token for authentication
func (c *SDKClient) WithToken(token string) *SDKClient {
	c.Token = token
	return c
}

// WithDebug enables debug logging
func (c *SDKClient) WithDebug(debug bool) *SDKClient {
	c.Debug = debug
	return c
}

// makeRequest makes an HTTP request to the API
func (c *SDKClient) makeRequest(method, endpoint string, body interface{}) (*APIResponse, error) {
	url := c.BaseURL + endpoint

	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)

		if c.Debug {
			fmt.Printf("Request Body: %s\n", string(jsonBody))
		}
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "ADC-Credit-SDK-Go/1.0")

	// Add authentication
	if c.APIKey != "" {
		req.Header.Set("X-API-Key", c.APIKey)
	} else if c.Token != "" {
		req.Header.Set("Authorization", "Bearer "+c.Token)
	}

	if c.Debug {
		fmt.Printf("Making %s request to %s\n", method, url)
		for key, values := range req.Header {
			for _, value := range values {
				if key == "Authorization" || key == "X-API-Key" {
					fmt.Printf("Header %s: [REDACTED]\n", key)
				} else {
					fmt.Printf("Header %s: %s\n", key, value)
				}
			}
		}
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if c.Debug {
		fmt.Printf("Response Status: %d\n", resp.StatusCode)
		fmt.Printf("Response Body: %s\n", string(respBody))
	}

	var apiResp APIResponse
	if len(respBody) > 0 {
		if err := json.Unmarshal(respBody, &apiResp); err != nil {
			// If JSON parsing fails, treat as error response
			return &APIResponse{
				Error:   string(respBody),
				Success: false,
			}, nil
		}
	}

	// Check HTTP status code
	if resp.StatusCode >= 400 {
		if apiResp.Error == "" {
			apiResp.Error = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, http.StatusText(resp.StatusCode))
		}
		apiResp.Success = false
	} else {
		apiResp.Success = true
	}

	return &apiResp, nil
}

// GET makes a GET request
func (c *SDKClient) GET(endpoint string) (*APIResponse, error) {
	return c.makeRequest("GET", endpoint, nil)
}

// POST makes a POST request
func (c *SDKClient) POST(endpoint string, body interface{}) (*APIResponse, error) {
	return c.makeRequest("POST", endpoint, body)
}

// PUT makes a PUT request
func (c *SDKClient) PUT(endpoint string, body interface{}) (*APIResponse, error) {
	return c.makeRequest("PUT", endpoint, body)
}

// DELETE makes a DELETE request
func (c *SDKClient) DELETE(endpoint string) (*APIResponse, error) {
	return c.makeRequest("DELETE", endpoint, nil)
}

// Health checks the API health endpoint
func (c *SDKClient) Health() (*APIResponse, error) {
	return c.GET("/api/v1/health")
}

// ============================================================================
// Authentication Methods
// ============================================================================

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token string `json:"token"`
	User  struct {
		ID      string `json:"id"`
		Email   string `json:"email"`
		Name    string `json:"name"`
		Picture string `json:"picture"`
		Role    string `json:"role"`
	} `json:"user"`
}

// Login authenticates with email and password
func (c *SDKClient) Login(email, password string) (*LoginResponse, error) {
	req := LoginRequest{
		Email:    email,
		Password: password,
	}

	resp, err := c.POST("/api/v1/auth/login", req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("login failed: %s", resp.Error)
	}

	var loginResp LoginResponse
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &loginResp)
	}

	return &loginResp, nil
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	Name     string `json:"name"`
}

// Register creates a new user account
func (c *SDKClient) Register(email, password, name string) (*LoginResponse, error) {
	req := RegisterRequest{
		Email:    email,
		Password: password,
		Name:     name,
	}

	resp, err := c.POST("/api/v1/auth/register", req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("registration failed: %s", resp.Error)
	}

	var loginResp LoginResponse
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &loginResp)
	}

	return &loginResp, nil
}

// ============================================================================
// Credits Methods
// ============================================================================

// CreditBalance represents credit balance information
type CreditBalance struct {
	CreditBalance int `json:"credit_balance"`
	CreditLimit   int `json:"credit_limit"`
	Subscription  struct {
		ID              string `json:"id"`
		Tier            string `json:"tier"`
		Status          string `json:"status"`
		CreditsPerMonth int    `json:"credits_per_month"`
	} `json:"subscription"`
}

// GetCreditBalance retrieves the current credit balance
func (c *SDKClient) GetCreditBalance() (*CreditBalance, error) {
	resp, err := c.GET("/api/v1/credits")
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get credit balance: %s", resp.Error)
	}

	var balance CreditBalance
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &balance)
	}

	return &balance, nil
}

// Transaction represents a credit transaction
type Transaction struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Amount      int       `json:"amount"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetTransactions retrieves credit transaction history
func (c *SDKClient) GetTransactions() ([]Transaction, error) {
	resp, err := c.GET("/api/v1/credits/transactions")
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get transactions: %s", resp.Error)
	}

	var transactions []Transaction
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &transactions)
	}

	return transactions, nil
}

// ConsumeCreditsRequest represents a credit consumption request
type ConsumeCreditsRequest struct {
	Endpoint  string `json:"endpoint"`
	Method    string `json:"method"`
	Credits   int    `json:"credits"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
}

// ConsumeCreditsResponse represents a credit consumption response
type ConsumeCreditsResponse struct {
	Success         bool `json:"success"`
	CreditBalance   int  `json:"credit_balance"`
	CreditsConsumed int  `json:"credits_consumed"`
	Usage           struct {
		ID        string    `json:"id"`
		Endpoint  string    `json:"endpoint"`
		Method    string    `json:"method"`
		Credits   int       `json:"credits"`
		Timestamp time.Time `json:"timestamp"`
	} `json:"usage"`
}

// ConsumeCredits consumes credits for API usage
func (c *SDKClient) ConsumeCredits(req ConsumeCreditsRequest) (*ConsumeCreditsResponse, error) {
	resp, err := c.POST("/api/v1/external/consume", req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to consume credits: %s", resp.Error)
	}

	var consumeResp ConsumeCreditsResponse
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &consumeResp)
	}

	return &consumeResp, nil
}

// ============================================================================
// Shops Methods
// ============================================================================

// Shop represents a shop
type Shop struct {
	ID           string    `json:"id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	ShopType     string    `json:"shop_type"`
	Slug         string    `json:"slug"`
	ContactEmail string    `json:"contact_email"`
	ContactPhone string    `json:"contact_phone"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CreateShopRequest represents a shop creation request
type CreateShopRequest struct {
	Name         string `json:"name"`
	Description  string `json:"description,omitempty"`
	ShopType     string `json:"shop_type"`
	ContactEmail string `json:"contact_email,omitempty"`
	ContactPhone string `json:"contact_phone,omitempty"`
}

// GetShops retrieves all shops for the current user
func (c *SDKClient) GetShops() ([]Shop, error) {
	resp, err := c.GET("/api/v1/shops")
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get shops: %s", resp.Error)
	}

	var shops []Shop
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &shops)
	}

	return shops, nil
}

// CreateShop creates a new shop
func (c *SDKClient) CreateShop(req CreateShopRequest) (*Shop, error) {
	resp, err := c.POST("/api/v1/shops", req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to create shop: %s", resp.Error)
	}

	var shop Shop
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &shop)
	}

	return &shop, nil
}

// GetShop retrieves a specific shop by ID
func (c *SDKClient) GetShop(shopID string) (*Shop, error) {
	resp, err := c.GET(fmt.Sprintf("/api/v1/shops/%s", shopID))
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get shop: %s", resp.Error)
	}

	var shop Shop
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &shop)
	}

	return &shop, nil
}

// UpdateShopRequest represents a shop update request
type UpdateShopRequest struct {
	Name         string `json:"name,omitempty"`
	Description  string `json:"description,omitempty"`
	ContactEmail string `json:"contact_email,omitempty"`
	ContactPhone string `json:"contact_phone,omitempty"`
}

// UpdateShop updates a shop
func (c *SDKClient) UpdateShop(shopID string, req UpdateShopRequest) (*Shop, error) {
	resp, err := c.PUT(fmt.Sprintf("/api/v1/shops/%s", shopID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to update shop: %s", resp.Error)
	}

	var shop Shop
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &shop)
	}

	return &shop, nil
}

// DeleteShop deletes a shop
func (c *SDKClient) DeleteShop(shopID string) error {
	resp, err := c.DELETE(fmt.Sprintf("/api/v1/shops/%s", shopID))
	if err != nil {
		return err
	}

	if !resp.Success {
		return fmt.Errorf("failed to delete shop: %s", resp.Error)
	}

	return nil
}

// ShopCustomer represents a shop customer
type ShopCustomer struct {
	ID            string    `json:"id"`
	Name          string    `json:"name"`
	Email         string    `json:"email"`
	Phone         string    `json:"phone"`
	CreditBalance int       `json:"credit_balance"`
	CreatedAt     time.Time `json:"created_at"`
}

// AddCustomerRequest represents a request to add a customer to a shop
type AddCustomerRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

// GetShopCustomers retrieves customers for a shop
func (c *SDKClient) GetShopCustomers(shopID string) ([]ShopCustomer, error) {
	resp, err := c.GET(fmt.Sprintf("/api/v1/shops/%s/customers", shopID))
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get shop customers: %s", resp.Error)
	}

	var customers []ShopCustomer
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &customers)
	}

	return customers, nil
}

// AddShopCustomer adds a customer to a shop
func (c *SDKClient) AddShopCustomer(shopID string, req AddCustomerRequest) (*ShopCustomer, error) {
	resp, err := c.POST(fmt.Sprintf("/api/v1/shops/%s/customers", shopID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to add shop customer: %s", resp.Error)
	}

	var customer ShopCustomer
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &customer)
	}

	return &customer, nil
}

// AddCreditRequest represents a request to add credit to a shop customer
type AddCreditRequest struct {
	CustomerID  string `json:"customer_id"`
	Amount      int    `json:"amount"`
	Description string `json:"description"`
}

// CreditTransaction represents a credit transaction
type CreditTransaction struct {
	ID          string    `json:"id"`
	CustomerID  string    `json:"customer_id"`
	Amount      int       `json:"amount"`
	Description string    `json:"description"`
	NewBalance  int       `json:"new_balance"`
	CreatedAt   time.Time `json:"created_at"`
}

// AddShopCredit adds credit to a shop customer
func (c *SDKClient) AddShopCredit(shopID string, req AddCreditRequest) (*CreditTransaction, error) {
	resp, err := c.POST(fmt.Sprintf("/api/v1/shops/%s/credits", shopID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to add shop credit: %s", resp.Error)
	}

	var transaction CreditTransaction
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &transaction)
	}

	return &transaction, nil
}

// GenerateCreditCodeRequest represents a request to generate a credit code
type GenerateCreditCodeRequest struct {
	Amount      int    `json:"amount"`
	Description string `json:"description"`
	ExpiresAt   string `json:"expires_at,omitempty"`
}

// CreditCode represents a credit code
type CreditCode struct {
	ID          string    `json:"id"`
	Code        string    `json:"code"`
	Amount      int       `json:"amount"`
	Description string    `json:"description"`
	ExpiresAt   time.Time `json:"expires_at"`
	IsUsed      bool      `json:"is_used"`
	CreatedAt   time.Time `json:"created_at"`
}

// GenerateCreditCode generates a credit code for a shop
func (c *SDKClient) GenerateCreditCode(shopID string, req GenerateCreditCodeRequest) (*CreditCode, error) {
	resp, err := c.POST(fmt.Sprintf("/api/v1/shops/%s/credit-codes", shopID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to generate credit code: %s", resp.Error)
	}

	var code CreditCode
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &code)
	}

	return &code, nil
}

// GetShopCreditTransactions retrieves credit transactions for a shop
func (c *SDKClient) GetShopCreditTransactions(shopID string) ([]CreditTransaction, error) {
	resp, err := c.GET(fmt.Sprintf("/api/v1/shops/%s/credit-transactions", shopID))
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get shop credit transactions: %s", resp.Error)
	}

	var transactions []CreditTransaction
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &transactions)
	}

	return transactions, nil
}

// ============================================================================
// API Key Management Methods
// ============================================================================

// APIKey represents an API key
type APIKey struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Key         string    `json:"key"`
	Enabled     bool      `json:"enabled"`
	Permissions []string  `json:"permissions"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateAPIKeyRequest represents a request to create an API key
type CreateAPIKeyRequest struct {
	Name        string   `json:"name"`
	Permissions []string `json:"permissions"`
}

// GetShopAPIKeys retrieves API keys for a shop
func (c *SDKClient) GetShopAPIKeys(shopID string) ([]APIKey, error) {
	resp, err := c.GET(fmt.Sprintf("/api/v1/shops/%s/api-keys", shopID))
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to get shop API keys: %s", resp.Error)
	}

	var apiKeys []APIKey
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &apiKeys)
	}

	return apiKeys, nil
}

// CreateShopAPIKey creates an API key for a shop
func (c *SDKClient) CreateShopAPIKey(shopID string, req CreateAPIKeyRequest) (*APIKey, error) {
	resp, err := c.POST(fmt.Sprintf("/api/v1/shops/%s/api-keys", shopID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to create shop API key: %s", resp.Error)
	}

	var apiKey APIKey
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &apiKey)
	}

	return &apiKey, nil
}

// UpdateAPIKeyRequest represents a request to update an API key
type UpdateAPIKeyRequest struct {
	Name    string `json:"name,omitempty"`
	Enabled *bool  `json:"enabled,omitempty"`
}

// UpdateShopAPIKey updates an API key for a shop
func (c *SDKClient) UpdateShopAPIKey(shopID, keyID string, req UpdateAPIKeyRequest) (*APIKey, error) {
	resp, err := c.PUT(fmt.Sprintf("/api/v1/shops/%s/api-keys/%s", shopID, keyID), req)
	if err != nil {
		return nil, err
	}

	if !resp.Success {
		return nil, fmt.Errorf("failed to update shop API key: %s", resp.Error)
	}

	var apiKey APIKey
	if resp.Data != nil {
		dataBytes, _ := json.Marshal(resp.Data)
		json.Unmarshal(dataBytes, &apiKey)
	}

	return &apiKey, nil
}

// DeleteShopAPIKey deletes an API key for a shop
func (c *SDKClient) DeleteShopAPIKey(shopID, keyID string) error {
	resp, err := c.DELETE(fmt.Sprintf("/api/v1/shops/%s/api-keys/%s", shopID, keyID))
	if err != nil {
		return err
	}

	if !resp.Success {
		return fmt.Errorf("failed to delete shop API key: %s", resp.Error)
	}

	return nil
}
