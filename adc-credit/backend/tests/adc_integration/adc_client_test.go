package adc_integration

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// ADCClientTestSuite tests integration with ADC core services
type ADCClientTestSuite struct {
	suite.Suite
	ssoClient          *ssoSDK.Client
	subscriptionClient *subscriptionSDK.Client
	analyticsClient    *analyticsSDK.Client
	ctx                context.Context
}

// SetupSuite initializes ADC service clients for testing
func (suite *ADCClientTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Initialize SSO Service Client
	ssoURL := os.Getenv("SSO_SERVICE_URL")
	if ssoURL == "" {
		ssoURL = "http://localhost:9000" // Default for testing
	}
	
	ssoClient, err := ssoSDK.NewClient(ssoURL)
	require.NoError(suite.T(), err, "Failed to create SSO client")
	suite.ssoClient = ssoClient

	// Initialize Subscription Service Client
	subscriptionURL := os.Getenv("SUBSCRIPTION_SERVICE_URL")
	if subscriptionURL == "" {
		subscriptionURL = "http://localhost:9100" // Default for testing
	}
	
	subscriptionClient, err := subscriptionSDK.NewClient(subscriptionURL)
	require.NoError(suite.T(), err, "Failed to create Subscription client")
	suite.subscriptionClient = subscriptionClient

	// Initialize Analytics Service Client
	analyticsURL := os.Getenv("ANALYTICS_SERVICE_URL")
	if analyticsURL == "" {
		analyticsURL = "http://localhost:9200" // Default for testing
	}
	
	analyticsClient, err := analyticsSDK.NewClient(analyticsURL)
	require.NoError(suite.T(), err, "Failed to create Analytics client")
	suite.analyticsClient = analyticsClient
}

// Test SSO Service connectivity and basic operations
func (suite *ADCClientTestSuite) TestSSOServiceIntegration() {
	suite.T().Log("Testing SSO Service integration...")

	// Test 1: Health check
	suite.T().Run("SSO_Health_Check", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		health, err := suite.ssoClient.HealthCheck(ctx)
		if err != nil {
			t.Logf("SSO service may not be running: %v", err)
			t.Skip("SSO service not available for integration testing")
		}
		assert.True(t, health.Healthy, "SSO service should be healthy")
		t.Logf("SSO service status: %v", health)
	})

	// Test 2: Organization management (if SSO service is available)
	suite.T().Run("SSO_Organization_Operations", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// Test creating organization with external ID (shop-based)
		createOrgReq := &ssoSDK.CreateOrganizationRequest{
			Name:        "Test Credit Shop Organization",
			Description: "Test organization for credit service integration",
			ExternalID:  "test-shop-12345",
			Settings: map[string]interface{}{
				"shop_type":    "credit_service",
				"created_from": "integration_test",
			},
		}

		org, err := suite.ssoClient.CreateOrganization(ctx, createOrgReq)
		if err != nil {
			t.Logf("Failed to create organization (may require auth): %v", err)
			t.Skip("Organization creation requires authentication")
		}

		if org != nil {
			assert.NotEmpty(t, org.ID, "Organization should have an ID")
			assert.Equal(t, createOrgReq.Name, org.Name, "Organization name should match")
			assert.Equal(t, createOrgReq.ExternalID, org.ExternalID, "External ID should match")
			t.Logf("Created organization: %+v", org)

			// Test retrieving organization by external ID
			retrievedOrg, err := suite.ssoClient.GetOrganizationByExternalID(ctx, createOrgReq.ExternalID)
			if err == nil {
				assert.Equal(t, org.ID, retrievedOrg.ID, "Retrieved organization should match created one")
				t.Logf("Retrieved organization by external ID: %+v", retrievedOrg)
			}

			// Cleanup: Delete test organization
			err = suite.ssoClient.DeleteOrganization(ctx, org.ID)
			if err != nil {
				t.Logf("Warning: Failed to cleanup test organization: %v", err)
			}
		}
	})
}

// Test Subscription Service connectivity and basic operations
func (suite *ADCClientTestSuite) TestSubscriptionServiceIntegration() {
	suite.T().Log("Testing Subscription Service integration...")

	// Test 1: Health check
	suite.T().Run("Subscription_Health_Check", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		health, err := suite.subscriptionClient.HealthCheck(ctx)
		if err != nil {
			t.Logf("Subscription service may not be running: %v", err)
			t.Skip("Subscription service not available for integration testing")
		}
		assert.True(t, health.Healthy, "Subscription service should be healthy")
		t.Logf("Subscription service status: %v", health)
	})

	// Test 2: Subscription plans retrieval
	suite.T().Run("Subscription_Plans_Retrieval", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		plans, err := suite.subscriptionClient.GetSubscriptionPlans(ctx)
		if err != nil {
			t.Logf("Failed to get subscription plans: %v", err)
			t.Skip("Subscription plans retrieval failed")
		}

		if plans != nil && len(plans.Plans) > 0 {
			assert.NotEmpty(t, plans.Plans, "Should have subscription plans")
			
			// Verify common plan structure
			for _, plan := range plans.Plans {
				assert.NotEmpty(t, plan.ID, "Plan should have an ID")
				assert.NotEmpty(t, plan.Name, "Plan should have a name")
				assert.GreaterOrEqual(t, plan.Price, float64(0), "Plan price should be non-negative")
				t.Logf("Found plan: %s (ID: %s, Price: %.2f)", plan.Name, plan.ID, plan.Price)
			}
		}
	})

	// Test 3: Organization subscription management
	suite.T().Run("Organization_Subscription_Operations", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// Test organization ID that would map to a shop
		testOrgID := "test-organization-123"

		// Try to get subscription for organization
		subscription, err := suite.subscriptionClient.GetSubscriptionByOrganizationID(ctx, testOrgID)
		if err != nil {
			t.Logf("No subscription found for test organization (expected): %v", err)
			// This is expected for a test organization that doesn't exist
		} else if subscription != nil {
			t.Logf("Found subscription for organization: %+v", subscription)
			assert.NotEmpty(t, subscription.ID, "Subscription should have an ID")
			assert.Equal(t, testOrgID, subscription.OrganizationID, "Organization ID should match")
		}
	})
}

// Test Analytics Service connectivity and basic operations
func (suite *ADCClientTestSuite) TestAnalyticsServiceIntegration() {
	suite.T().Log("Testing Analytics Service integration...")

	// Test 1: Health check
	suite.T().Run("Analytics_Health_Check", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		health, err := suite.analyticsClient.HealthCheck(ctx)
		if err != nil {
			t.Logf("Analytics service may not be running: %v", err)
			t.Skip("Analytics service not available for integration testing")
		}
		assert.True(t, health.Healthy, "Analytics service should be healthy")
		t.Logf("Analytics service status: %v", health)
	})

	// Test 2: Usage event reporting
	suite.T().Run("Analytics_Usage_Reporting", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// Test usage event
		usageEvent := &analyticsSDK.UsageEvent{
			ServiceName:    "adc-credit",
			OrganizationID: "test-org-123",
			UserID:         "test-user-456",
			EventType:      "credit_consumption",
			EventData: map[string]interface{}{
				"credits_consumed": 10,
				"shop_id":         "test-shop-789",
				"operation_type":  "api_call",
			},
			Timestamp: time.Now(),
		}

		err := suite.analyticsClient.RecordUsageEvent(ctx, usageEvent)
		if err != nil {
			t.Logf("Failed to record usage event (may require auth): %v", err)
			t.Skip("Usage event recording requires authentication")
		} else {
			t.Logf("Successfully recorded usage event: %+v", usageEvent)
		}
	})

	// Test 3: Metrics retrieval
	suite.T().Run("Analytics_Metrics_Retrieval", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
		defer cancel()

		// Test metrics request
		metricsReq := &analyticsSDK.MetricsRequest{
			ServiceName:    "adc-credit",
			OrganizationID: "test-org-123",
			StartTime:      time.Now().Add(-24 * time.Hour),
			EndTime:        time.Now(),
			MetricTypes:    []string{"credit_consumption", "api_calls"},
		}

		metrics, err := suite.analyticsClient.GetMetrics(ctx, metricsReq)
		if err != nil {
			t.Logf("Failed to get metrics (may require auth or no data): %v", err)
			t.Skip("Metrics retrieval failed")
		} else if metrics != nil {
			t.Logf("Retrieved metrics: %+v", metrics)
			assert.NotNil(t, metrics.Data, "Metrics should have data field")
		}
	})
}

// Test service failure scenarios and error handling
func (suite *ADCClientTestSuite) TestServiceFailureHandling() {
	suite.T().Log("Testing service failure scenarios...")

	// Test 1: Connection timeout handling
	suite.T().Run("Connection_Timeout_Handling", func(t *testing.T) {
		// Create client with very short timeout
		ctx, cancel := context.WithTimeout(suite.ctx, 100*time.Millisecond)
		defer cancel()

		// This should timeout quickly if service is slow or unavailable
		_, err := suite.ssoClient.HealthCheck(ctx)
		if err != nil {
			t.Logf("Expected timeout error: %v", err)
			assert.Contains(t, err.Error(), "context deadline exceeded", "Should get timeout error")
		}
	})

	// Test 2: Invalid service URL handling
	suite.T().Run("Invalid_Service_URL_Handling", func(t *testing.T) {
		invalidClient, err := ssoSDK.NewClient("http://invalid-service-url:9999")
		require.NoError(t, err, "Client creation should succeed even with invalid URL")

		ctx, cancel := context.WithTimeout(suite.ctx, 5*time.Second)
		defer cancel()

		_, err = invalidClient.HealthCheck(ctx)
		assert.Error(t, err, "Should get error with invalid service URL")
		t.Logf("Expected connection error: %v", err)
	})
}

// Test concurrent service calls
func (suite *ADCClientTestSuite) TestConcurrentServiceCalls() {
	suite.T().Log("Testing concurrent service calls...")

	suite.T().Run("Concurrent_Health_Checks", func(t *testing.T) {
		const numConcurrent = 5
		results := make(chan error, numConcurrent)

		// Start concurrent health checks
		for i := 0; i < numConcurrent; i++ {
			go func() {
				ctx, cancel := context.WithTimeout(suite.ctx, 10*time.Second)
				defer cancel()

				_, err := suite.ssoClient.HealthCheck(ctx)
				results <- err
			}()
		}

		// Collect results
		var errors []error
		for i := 0; i < numConcurrent; i++ {
			if err := <-results; err != nil {
				errors = append(errors, err)
			}
		}

		if len(errors) > 0 {
			t.Logf("Some concurrent calls failed (service may not be available): %v", errors)
		} else {
			t.Log("All concurrent health checks succeeded")
		}

		// At least some should succeed if services are available
		successRate := float64(numConcurrent-len(errors)) / float64(numConcurrent)
		t.Logf("Success rate: %.2f%% (%d/%d)", successRate*100, numConcurrent-len(errors), numConcurrent)
	})
}

// TestADCClientIntegration runs the full ADC client integration test suite
func TestADCClientIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ADC integration tests in short mode")
	}

	suite.Run(t, new(ADCClientTestSuite))
}