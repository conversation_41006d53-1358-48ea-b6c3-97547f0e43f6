package adc_integration

import (
	"testing"

	"github.com/adc-credit/backend/internal/database"
	"github.com/adc-credit/backend/internal/services"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	analyticsSDK "github.com/adc-analytics-service/sdk"
	ssoSDK "github.com/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
)

// ServiceContainerTestSuite tests the service container integration
type ServiceContainerTestSuite struct {
	suite.Suite
	serviceContainer *services.ServiceContainer
	ssoClient        *ssoSDK.Client
	subscriptionClient *subscriptionSDK.Client
	analyticsClient    *analyticsSDK.Client
}

// SetupSuite initializes the service container for testing
func (suite *ServiceContainerTestSuite) SetupSuite() {
	// Setup test environment
	err := testutils.SetupTestEnvironment()
	require.NoError(suite.T(), err, "Failed to setup test environment")

	// Initialize ADC service clients
	ssoClient, err := ssoSDK.NewClient("http://localhost:9000")
	require.NoError(suite.T(), err, "Failed to create SSO client")
	suite.ssoClient = ssoClient

	subscriptionClient, err := subscriptionSDK.NewClient("http://localhost:9100")
	require.NoError(suite.T(), err, "Failed to create Subscription client")
	suite.subscriptionClient = subscriptionClient

	analyticsClient, err := analyticsSDK.NewClient("http://localhost:9200")
	require.NoError(suite.T(), err, "Failed to create Analytics client")
	suite.analyticsClient = analyticsClient

	// Initialize service container with all clients
	suite.serviceContainer = services.NewServiceContainer(
		database.DB,
		suite.subscriptionClient,
		suite.analyticsClient,
		suite.ssoClient,
	)
}

// TearDownSuite cleans up after tests
func (suite *ServiceContainerTestSuite) TearDownSuite() {
	if database.DB != nil {
		testutils.CleanupTestEnvironment()
	}
}

// Test service container initialization
func (suite *ServiceContainerTestSuite) TestServiceContainerInitialization() {
	suite.T().Log("Testing service container initialization...")

	// Test that service container is properly initialized
	assert.NotNil(suite.T(), suite.serviceContainer, "Service container should be initialized")
	assert.NotNil(suite.T(), suite.serviceContainer.DB, "Database should be available")
}

// Test service retrieval from container
func (suite *ServiceContainerTestSuite) TestServiceRetrieval() {
	suite.T().Log("Testing service retrieval from container...")

	// Test SSO client retrieval
	suite.T().Run("SSO_Client_Retrieval", func(t *testing.T) {
		ssoClient := suite.serviceContainer.GetSSOClient()
		assert.NotNil(t, ssoClient, "SSO client should be available")
		assert.Equal(t, suite.ssoClient, ssoClient, "SSO client should match the one provided")
	})

	// Test Subscription client retrieval
	suite.T().Run("Subscription_Client_Retrieval", func(t *testing.T) {
		subscriptionClient := suite.serviceContainer.GetSubscriptionClient()
		assert.NotNil(t, subscriptionClient, "Subscription client should be available")
		assert.Equal(t, suite.subscriptionClient, subscriptionClient, "Subscription client should match")
	})

	// Test Analytics client retrieval
	suite.T().Run("Analytics_Client_Retrieval", func(t *testing.T) {
		analyticsClient := suite.serviceContainer.GetAnalyticsClient()
		assert.NotNil(t, analyticsClient, "Analytics client should be available")
		assert.Equal(t, suite.analyticsClient, analyticsClient, "Analytics client should match")
	})

	// Test Database retrieval
	suite.T().Run("Database_Retrieval", func(t *testing.T) {
		db := suite.serviceContainer.GetDB()
		assert.NotNil(t, db, "Database should be available")
		assert.Equal(t, database.DB, db, "Database should match the global instance")
	})
}

// Test business service initialization through container
func (suite *ServiceContainerTestSuite) TestBusinessServiceInitialization() {
	suite.T().Log("Testing business service initialization...")

	// Test Shop Organization Service
	suite.T().Run("ShopOrganizationService_Initialization", func(t *testing.T) {
		shopOrgService := suite.serviceContainer.GetShopOrganizationService()
		assert.NotNil(t, shopOrgService, "Shop Organization Service should be available")

		// Test service has proper dependencies
		// Note: These are internal checks that would require reflection or public methods
		// For now, we test that the service can be retrieved and is not nil
	})

	// Test Organization Mapping Service
	suite.T().Run("OrganizationMappingService_Initialization", func(t *testing.T) {
		orgMappingService := suite.serviceContainer.GetOrganizationMappingService()
		assert.NotNil(t, orgMappingService, "Organization Mapping Service should be available")
	})

	// Test Shop Subscription Service
	suite.T().Run("ShopSubscriptionService_Initialization", func(t *testing.T) {
		shopSubService := suite.serviceContainer.GetShopSubscriptionService()
		assert.NotNil(t, shopSubService, "Shop Subscription Service should be available")
	})

	// Test Shop Subscription Guard
	suite.T().Run("ShopSubscriptionGuard_Initialization", func(t *testing.T) {
		shopSubGuard := suite.serviceContainer.GetShopSubscriptionGuard()
		assert.NotNil(t, shopSubGuard, "Shop Subscription Guard should be available")
	})

	// Test Usage Reporting Service
	suite.T().Run("UsageReportingService_Initialization", func(t *testing.T) {
		usageService := suite.serviceContainer.GetUsageReportingService()
		if usageService != nil {
			assert.NotNil(t, usageService, "Usage Reporting Service should be available")
		} else {
			t.Log("Usage Reporting Service not yet implemented")
		}
	})
}

// Test service container dependency injection
func (suite *ServiceContainerTestSuite) TestDependencyInjection() {
	suite.T().Log("Testing dependency injection...")

	// Test that services can access their dependencies through the container
	suite.T().Run("Service_Dependencies_Available", func(t *testing.T) {
		// Get a service that depends on ADC clients
		shopOrgService := suite.serviceContainer.GetShopOrganizationService()
		require.NotNil(t, shopOrgService, "Shop Organization Service should be available")

		// Test that the service can perform operations (this tests dependency injection)
		// Note: This would require the service to have testable methods
		// For now, we verify the service exists and dependencies are injected
		
		// Verify that all required clients are available for the service
		assert.NotNil(t, suite.serviceContainer.GetSSOClient(), "SSO client should be available for services")
		assert.NotNil(t, suite.serviceContainer.GetSubscriptionClient(), "Subscription client should be available for services")
		assert.NotNil(t, suite.serviceContainer.GetDB(), "Database should be available for services")
	})
}

// Test service container with missing dependencies
func (suite *ServiceContainerTestSuite) TestMissingDependencies() {
	suite.T().Log("Testing service container with missing dependencies...")

	// Test container with nil clients (error scenarios)
	suite.T().Run("Nil_Dependencies_Handling", func(t *testing.T) {
		// Create container with nil dependencies
		containerWithNilDeps := services.NewServiceContainer(
			database.DB,
			nil, // nil subscription client
			nil, // nil analytics client
			nil, // nil SSO client
		)

		assert.NotNil(t, containerWithNilDeps, "Container should be created even with nil dependencies")
		assert.NotNil(t, containerWithNilDeps.GetDB(), "Database should still be available")

		// Services should handle nil dependencies gracefully
		ssoClient := containerWithNilDeps.GetSSOClient()
		subscriptionClient := containerWithNilDeps.GetSubscriptionClient()
		analyticsClient := containerWithNilDeps.GetAnalyticsClient()

		// These may be nil and services should handle it gracefully
		t.Logf("SSO client: %v", ssoClient)
		t.Logf("Subscription client: %v", subscriptionClient)
		t.Logf("Analytics client: %v", analyticsClient)
	})
}

// Test service container thread safety
func (suite *ServiceContainerTestSuite) TestThreadSafety() {
	suite.T().Log("Testing service container thread safety...")

	suite.T().Run("Concurrent_Service_Access", func(t *testing.T) {
		const numGoroutines = 10
		const numAccesses = 100

		done := make(chan bool, numGoroutines)

		// Start multiple goroutines accessing services concurrently
		for i := 0; i < numGoroutines; i++ {
			go func() {
				defer func() { done <- true }()

				for j := 0; j < numAccesses; j++ {
					// Access different services concurrently
					_ = suite.serviceContainer.GetSSOClient()
					_ = suite.serviceContainer.GetSubscriptionClient()
					_ = suite.serviceContainer.GetAnalyticsClient()
					_ = suite.serviceContainer.GetDB()
					_ = suite.serviceContainer.GetShopOrganizationService()
					_ = suite.serviceContainer.GetOrganizationMappingService()
				}
			}()
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}

		t.Log("Concurrent access test completed successfully")
	})
}

// Test service container validation
func (suite *ServiceContainerTestSuite) TestServiceContainerValidation() {
	suite.T().Log("Testing service container validation...")

	// Test subscription access validation with internal keys
	suite.T().Run("Subscription_Access_Validation", func(t *testing.T) {
		// Test internal API key bypass
		internalKey := "adc-internal-test-key"
		
		// This would test the ValidateSubscriptionAccess method if it exists
		hasAccess := suite.serviceContainer.ValidateSubscriptionAccess(internalKey, "any-shop-id")
		if hasAccess {
			t.Log("Internal API key bypass working correctly")
		} else {
			t.Log("Internal API key bypass not configured or internal key not recognized")
		}
	})

	// Test limit checking with service integration
	suite.T().Run("Limit_Checking_Integration", func(t *testing.T) {
		shopSubGuard := suite.serviceContainer.GetShopSubscriptionGuard()
		if shopSubGuard != nil {
			// Test that limit checking integrates with ADC services
			// This would require actual limit checking methods
			t.Log("Shop subscription guard available for limit checking")
		} else {
			t.Log("Shop subscription guard not available")
		}
	})
}

// TestServiceContainerIntegration runs the service container integration test suite
func TestServiceContainerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping service container integration tests in short mode")
	}

	suite.Run(t, new(ServiceContainerTestSuite))
}