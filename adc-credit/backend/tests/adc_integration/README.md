# ADC Integration Tests

This directory contains comprehensive integration tests for the ADC Platform core services integration.

## Test Structure

### Phase 1: Core Service Integration Tests
- **`adc_client_test.go`** - Tests ADC service client connectivity and basic operations
- **`service_container_test.go`** - Tests service container initialization and dependency injection

### Phase 2: Handler Integration Tests (in `/tests/handlers_v2/`)
- **`credit_v2_test.go`** - Tests new V2 credit handlers with ADC integration
- **`adc_sync_test.go`** - Tests ADC synchronization endpoints
- **`webhook_v2_test.go`** - Tests V2 webhook processing (to be implemented)

### Phase 3: Service Layer Tests (to be implemented)
- **`shop_organization_service_test.go`** - Tests shop-to-organization mapping
- **`subscription_sync_service_test.go`** - Tests subscription synchronization
- **`usage_reporting_service_test.go`** - Tests usage reporting to ADC Analytics

## Running Tests

### Individual Test Suites
```bash
# Run ADC client integration tests
go test ./tests/adc_integration -run TestADCClientIntegration -v

# Run service container tests
go test ./tests/adc_integration -run TestServiceContainerIntegration -v

# Run V2 handler tests
go test ./tests/handlers_v2 -run TestCreditV2HandlerIntegration -v
go test ./tests/handlers_v2 -run TestADCSyncHandlerIntegration -v
```

### All ADC Integration Tests
```bash
# Run all ADC integration tests
go test ./tests/adc_integration ./tests/handlers_v2 -v

# Run with real ADC services (if available)
SSO_SERVICE_URL=http://localhost:9000 \
SUBSCRIPTION_SERVICE_URL=http://localhost:9100 \
ANALYTICS_SERVICE_URL=http://localhost:9200 \
go test ./tests/adc_integration ./tests/handlers_v2 -v
```

### Skip Integration Tests
```bash
# Skip integration tests in short mode
go test ./tests/adc_integration ./tests/handlers_v2 -short
```

## Test Environment Setup

### Prerequisites
1. **Test Database**: PostgreSQL database for testing
2. **ADC Services** (optional, tests will skip if unavailable):
   - ADC SSO Service (port 9000)
   - ADC Subscription Service (port 9100) 
   - ADC Analytics Service (port 9200)

### Environment Variables
```bash
# ADC Service URLs (optional, defaults to localhost)
export SSO_SERVICE_URL=http://localhost:9000
export SUBSCRIPTION_SERVICE_URL=http://localhost:9100
export ANALYTICS_SERVICE_URL=http://localhost:9200

# Test Database
export DATABASE_URL=postgresql://user:password@localhost/adc_credit_test
```

## Test Approach

### Real Services First
- Tests attempt to connect to real ADC services when available
- Gracefully skip or adapt when services are unavailable
- Minimal mocking - only for failure scenarios

### Integration Focus
- End-to-end workflows rather than isolated unit tests
- Service-to-service communication
- Data consistency across systems
- Error handling and graceful degradation

### Comprehensive Coverage
- **Happy Path**: Normal operations with all services available
- **Failure Scenarios**: Service unavailability, timeouts, invalid data
- **Concurrent Operations**: Multiple simultaneous requests
- **Data Consistency**: Cross-service synchronization

## Test Data Management

### Test Isolation
- Each test suite creates its own test data
- Cleanup performed after test completion
- No shared state between test suites

### Test Users and Shops
- Dynamically created with unique IDs
- Named with test suite prefixes for identification
- Automatically cleaned up on teardown

### ADC Service Data
- Test organizations created with `external_id` mapping to shops
- Test subscriptions created for integration testing
- Cleanup performed when possible (requires service availability)

## Expected Test Outcomes

### With ADC Services Available
- All integration tests should pass
- Real organization/subscription creation and retrieval
- Actual synchronization between local and ADC data
- Usage events successfully reported to Analytics

### Without ADC Services
- Tests should skip gracefully with informative messages
- Service container should still initialize with nil clients
- Local functionality should continue to work
- Error handling scenarios should be tested with mock failures

## Test Coverage Areas

### ✅ Currently Implemented
- ADC service client connectivity
- Service container dependency injection
- V2 credit handler operations
- ADC sync endpoint functionality
- Error scenario handling

### 🚧 In Progress
- Webhook processing integration tests
- Service layer integration tests

### 📋 Planned
- End-to-end workflow tests
- Data consistency validation tests
- Performance and load testing
- Service failure recovery tests

## Debugging Tips

### Test Failures
1. Check ADC service availability: `curl http://localhost:9000/health`
2. Verify test database connection
3. Check environment variables
4. Review test logs for specific error messages

### Service Integration Issues
1. Verify service URLs and ports
2. Check authentication configuration
3. Validate request/response formats
4. Test individual service endpoints manually

### Data Consistency Issues
1. Check database state before/after tests
2. Verify organization and subscription mappings
3. Review synchronization logs
4. Test individual sync operations

This test suite provides comprehensive coverage of the ADC platform integration while maintaining flexibility for different development and testing environments.