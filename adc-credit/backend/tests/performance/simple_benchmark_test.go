package performance

import (
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/adc-credit/backend/internal/models"
	testutils "github.com/adc-credit/backend/internal/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

// Performance test configuration
const (
	testUsers       = 50
	testOperations  = 1000
	concurrency     = 10
	maxLatency      = 500 * time.Millisecond
)

// PerformanceMetrics holds performance test results
type PerformanceMetrics struct {
	TestName          string
	TotalOperations   int
	SuccessCount      int
	ErrorCount        int
	TotalDuration     time.Duration
	AverageLatency    time.Duration
	MinLatency        time.Duration
	MaxLatency        time.Duration
	P95Latency        time.Duration
	OperationsPerSec  float64
	ErrorRate         float64
	MemoryUsageMB     float64
}

// BenchmarkDatabaseConnections tests database connection performance
func BenchmarkDatabaseConnections(b *testing.B) {
	err := testutils.SetupTestEnvironment()
	require.NoError(b, err)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			var count int64
			testutils.TestDB.Model(&models.User{}).Count(&count)
		}
	})
}

// BenchmarkUserCreation tests user creation performance
func BenchmarkUserCreation(b *testing.B) {
	err := testutils.SetupTestEnvironment()
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		userID := uuid.New()
		err := testutils.TestDB.Exec(`
			INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
			VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
		`, userID, fmt.Sprintf("bench%d", i), 
			fmt.Sprintf("<EMAIL>", i), 
			fmt.Sprintf("Benchmark User %d", i), 
			fmt.Sprintf("bench-google-%d", i), "user").Error
		if err != nil {
			b.Fatal(err)
		}
		
		// Cleanup
		testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
	}
}

// BenchmarkShopCreation tests shop creation performance
func BenchmarkShopCreation(b *testing.B) {
	err := testutils.SetupTestEnvironment()
	require.NoError(b, err)

	// Create a test user first
	userID := uuid.New()
	err = testutils.TestDB.Exec(`
		INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, userID, "benchuser", "<EMAIL>", "Benchmark User", "bench-google", "user").Error
	require.NoError(b, err)

	defer func() {
		testutils.TestDB.Where("owner_user_id = ?", userID).Delete(&models.Shop{})
		testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
	}()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		shop := &models.Shop{
			ID:           uuid.New(),
			OwnerUserID:  userID,
			Name:         fmt.Sprintf("Benchmark Shop %d", i),
			Slug:         fmt.Sprintf("bench-shop-%d", i),
			ShopType:     "api_service",
			ContactEmail: fmt.Sprintf("<EMAIL>", i),
		}
		err := testutils.TestDB.Create(shop).Error
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkAPIKeyValidation tests API key lookup performance
func BenchmarkAPIKeyValidation(b *testing.B) {
	err := testutils.SetupTestEnvironment()
	require.NoError(b, err)

	// Create test API keys
	apiKeys := make([]string, 100)
	userID := uuid.New()
	
	err = testutils.TestDB.Exec(`
		INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, userID, "apikeyuser", "<EMAIL>", "API Key User", "apikey-google", "user").Error
	require.NoError(b, err)

	for i := 0; i < 100; i++ {
		apiKey := &models.APIKey{
			ID:     uuid.New(),
			UserID: userID,
			Name:   fmt.Sprintf("Benchmark API Key %d", i),
			Key:    fmt.Sprintf("bench-key-%d-%s", i, uuid.New().String()),
		}
		err := testutils.TestDB.Create(apiKey).Error
		require.NoError(b, err)
		apiKeys[i] = apiKey.Key
	}

	defer func() {
		testutils.TestDB.Where("user_id = ?", userID).Delete(&models.APIKey{})
		testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
	}()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			apiKey := apiKeys[rand.Intn(len(apiKeys))]
			var key models.APIKey
			testutils.TestDB.Where("key = ?", apiKey).First(&key)
		}
	})
}

// BenchmarkTransactionCreation tests transaction creation performance
func BenchmarkTransactionCreation(b *testing.B) {
	err := testutils.SetupTestEnvironment()
	require.NoError(b, err)

	// Create test user
	userID := uuid.New()
	err = testutils.TestDB.Exec(`
		INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, userID, "transuser", "<EMAIL>", "Transaction User", "trans-google", "user").Error
	require.NoError(b, err)

	defer func() {
		testutils.TestDB.Where("user_id = ?", userID).Delete(&models.Transaction{})
		testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
	}()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		transaction := &models.Transaction{
			ID:          uuid.New(),
			UserID:      userID,
			Type:        "credit_use",
			Amount:      rand.Intn(100) + 1,
			Description: fmt.Sprintf("Benchmark transaction %d", i),
			Reference:   fmt.Sprintf("bench-ref-%d", i),
		}
		err := testutils.TestDB.Create(transaction).Error
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestDatabasePerformanceLoad performs load testing on database operations
func TestDatabasePerformanceLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping database load test in short mode")
	}

	err := testutils.SetupTestEnvironment()
	require.NoError(t, err)

	// Test concurrent user operations
	result := performDatabaseLoadTest(t, "Concurrent User Operations", 
		testUsers, concurrency, func() error {
			userID := uuid.New()
			return testutils.TestDB.Exec(`
				INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
				VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
			`, userID, fmt.Sprintf("load%s", userID.String()[:8]), 
				fmt.Sprintf("<EMAIL>", userID.String()[:8]), 
				"Load Test User", fmt.Sprintf("load-google-%s", userID.String()[:8]), "user").Error
		})

	logPerformanceMetrics(t, result)
	require.Less(t, result.AverageLatency, maxLatency, "Database operations too slow")
	require.Less(t, result.ErrorRate, 5.0, "Too many database errors")
}

// TestConcurrentTransactionCreation tests concurrent transaction creation
func TestConcurrentTransactionCreation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrent transaction test in short mode")
	}

	err := testutils.SetupTestEnvironment()
	require.NoError(t, err)

	// Create test user
	userID := uuid.New()
	err = testutils.TestDB.Exec(`
		INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`, userID, "concuruser", "<EMAIL>", "Concurrent User", "concur-google", "user").Error
	require.NoError(t, err)

	defer func() {
		testutils.TestDB.Where("user_id = ?", userID).Delete(&models.Transaction{})
		testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
	}()

	result := performDatabaseLoadTest(t, "Concurrent Transaction Creation", 
		testOperations, concurrency, func() error {
			transaction := &models.Transaction{
				ID:          uuid.New(),
				UserID:      userID,
				Type:        "credit_use",
				Amount:      rand.Intn(100) + 1,
				Description: "Load test transaction",
				Reference:   fmt.Sprintf("load-ref-%s", uuid.New().String()[:8]),
			}
			return testutils.TestDB.Create(transaction).Error
		})

	logPerformanceMetrics(t, result)
	require.Less(t, result.AverageLatency, maxLatency, "Transaction creation too slow")
}

// TestMemoryUsageUnderLoad tests memory usage under sustained load
func TestMemoryUsageUnderLoad(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping memory usage test in short mode")
	}

	err := testutils.SetupTestEnvironment()
	require.NoError(t, err)

	// Record initial memory
	var initialMem runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&initialMem)

	// Perform sustained operations
	result := performDatabaseLoadTest(t, "Memory Usage Test", 
		500, 5, func() error {
			// Create and immediately clean up data to test memory management
			userID := uuid.New()
			err := testutils.TestDB.Exec(`
				INSERT INTO users (id, username, email, name, google_id, role, created_at, updated_at) 
				VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
			`, userID, fmt.Sprintf("mem%s", userID.String()[:8]), 
				fmt.Sprintf("<EMAIL>", userID.String()[:8]), 
				"Memory Test User", fmt.Sprintf("mem-google-%s", userID.String()[:8]), "user").Error
			if err != nil {
				return err
			}
			
			// Immediate cleanup
			testutils.TestDB.Where("id = ?", userID).Delete(&models.User{})
			return nil
		})

	// Record final memory
	var finalMem runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&finalMem)

	result.MemoryUsageMB = float64(finalMem.Alloc-initialMem.Alloc) / 1024 / 1024

	logPerformanceMetrics(t, result)
	t.Logf("Memory usage change: %.2f MB", result.MemoryUsageMB)
}

// TestDatabaseConnectionPooling tests connection pool performance
func TestDatabaseConnectionPooling(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping connection pool test in short mode")
	}

	err := testutils.SetupTestEnvironment()
	require.NoError(t, err)

	sqlDB, err := testutils.TestDB.DB()
	require.NoError(t, err)

	// Get initial connection stats
	initialStats := sqlDB.Stats()
	t.Logf("Initial connection stats - Open: %d, InUse: %d, Idle: %d", 
		initialStats.OpenConnections, initialStats.InUse, initialStats.Idle)

	// Perform concurrent database operations
	result := performDatabaseLoadTest(t, "Connection Pool Test", 
		200, 20, func() error {
			var count int64
			return testutils.TestDB.Model(&models.User{}).Count(&count).Error
		})

	// Get final connection stats
	finalStats := sqlDB.Stats()
	t.Logf("Final connection stats - Open: %d, InUse: %d, Idle: %d", 
		finalStats.OpenConnections, finalStats.InUse, finalStats.Idle)

	logPerformanceMetrics(t, result)
	
	// Assert connection pool is working efficiently
	require.LessOrEqual(t, finalStats.OpenConnections, 100, "Too many connections opened")
	require.GreaterOrEqual(t, result.OperationsPerSec, 50.0, "Database operations too slow")
}

// performDatabaseLoadTest performs a load test with the given operation
func performDatabaseLoadTest(t *testing.T, testName string, totalOps, concurrency int, operation func() error) *PerformanceMetrics {
	var wg sync.WaitGroup
	latencies := make(chan time.Duration, totalOps)
	errors := make(chan error, totalOps)
	
	startTime := time.Now()
	opsPerWorker := totalOps / concurrency
	
	// Start concurrent workers
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for j := 0; j < opsPerWorker; j++ {
				opStart := time.Now()
				err := operation()
				latency := time.Since(opStart)
				
				latencies <- latency
				if err != nil {
					errors <- err
				}
			}
		}()
	}
	
	wg.Wait()
	close(latencies)
	close(errors)
	
	totalDuration := time.Since(startTime)
	
	// Calculate metrics
	return calculateMetrics(testName, latencies, errors, totalDuration)
}

// calculateMetrics calculates performance metrics from test results
func calculateMetrics(testName string, latencies <-chan time.Duration, errors <-chan error, totalDuration time.Duration) *PerformanceMetrics {
	var latencySlice []time.Duration
	var totalLatency time.Duration
	errorCount := 0
	
	// Collect latencies
	for latency := range latencies {
		latencySlice = append(latencySlice, latency)
		totalLatency += latency
	}
	
	// Count errors
	for range errors {
		errorCount++
	}
	
	// Sort latencies for percentiles
	for i := range latencySlice {
		for j := i + 1; j < len(latencySlice); j++ {
			if latencySlice[i] > latencySlice[j] {
				latencySlice[i], latencySlice[j] = latencySlice[j], latencySlice[i]
			}
		}
	}
	
	totalOps := len(latencySlice) + errorCount
	successCount := len(latencySlice)
	
	metrics := &PerformanceMetrics{
		TestName:        testName,
		TotalOperations: totalOps,
		SuccessCount:    successCount,
		ErrorCount:      errorCount,
		TotalDuration:   totalDuration,
		ErrorRate:       float64(errorCount) / float64(totalOps) * 100,
	}
	
	if len(latencySlice) > 0 {
		metrics.AverageLatency = totalLatency / time.Duration(len(latencySlice))
		metrics.MinLatency = latencySlice[0]
		metrics.MaxLatency = latencySlice[len(latencySlice)-1]
		if len(latencySlice) >= 20 {
			metrics.P95Latency = latencySlice[len(latencySlice)*95/100]
		}
	}
	
	metrics.OperationsPerSec = float64(totalOps) / totalDuration.Seconds()
	
	return metrics
}

// logPerformanceMetrics logs performance test results
func logPerformanceMetrics(t *testing.T, metrics *PerformanceMetrics) {
	t.Logf("=== Performance Results: %s ===", metrics.TestName)
	t.Logf("Total Operations: %d", metrics.TotalOperations)
	t.Logf("Successful: %d, Failed: %d", metrics.SuccessCount, metrics.ErrorCount)
	t.Logf("Error Rate: %.2f%%", metrics.ErrorRate)
	t.Logf("Total Duration: %v", metrics.TotalDuration)
	t.Logf("Operations/Second: %.2f", metrics.OperationsPerSec)
	t.Logf("Average Latency: %v", metrics.AverageLatency)
	t.Logf("Min Latency: %v", metrics.MinLatency)
	t.Logf("Max Latency: %v", metrics.MaxLatency)
	if metrics.P95Latency > 0 {
		t.Logf("P95 Latency: %v", metrics.P95Latency)
	}
	if metrics.MemoryUsageMB != 0 {
		t.Logf("Memory Usage: %.2f MB", metrics.MemoryUsageMB)
	}
	t.Logf("==========================================")
}