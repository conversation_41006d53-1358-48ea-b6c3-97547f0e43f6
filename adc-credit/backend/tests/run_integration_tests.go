package main

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
)

func main() {
	// Load test environment variables
	if err := godotenv.Load(".env.test"); err != nil {
		if err := godotenv.Load(".env"); err != nil {
			logrus.Info("No .env file found, using environment variables")
		}
	}

	// Set test environment
	os.Setenv("GIN_MODE", "test")
	os.Setenv("DB_NAME", "adc_credit_test")

	fmt.Println("🧪 Running Subscription Guard Integration Tests")
	fmt.Println(strings.Repeat("=", 50))

	// Get the current directory
	currentDir, err := os.Getwd()
	if err != nil {
		logrus.Fatalf("Failed to get current directory: %v", err)
	}

	// Change to the backend directory  
	backendDir := filepath.Join(currentDir, "..")
	if err := os.Chdir(backendDir); err != nil {
		logrus.Fatalf("Failed to change to backend directory: %v", err)
	}

	// List of test packages to run
	testPackages := []string{
		"./tests/integration/subscription_guard_test.go",
		"./tests/integration/subscription_limits_api_test.go",
		"./tests/integration/qr_code_limits_test.go",
	}

	// Run each test package
	for _, testPkg := range testPackages {
		fmt.Printf("\n🔍 Running tests in %s\n", testPkg)
		fmt.Println(strings.Repeat("-", 40))

		cmd := exec.Command("go", "test", "-v", testPkg)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr

		if err := cmd.Run(); err != nil {
			fmt.Printf("❌ Tests failed in %s: %v\n", testPkg, err)
			os.Exit(1)
		}

		fmt.Printf("✅ Tests passed in %s\n", testPkg)
	}

	// Run all integration tests together
	fmt.Printf("\n🚀 Running all integration tests together\n")
	fmt.Println(strings.Repeat("-", 40))

	cmd := exec.Command("go", "test", "-v", "./tests/integration/...")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		fmt.Printf("❌ Integration tests failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n🎉 All integration tests passed!")

	// Run coverage report
	fmt.Printf("\n📊 Generating coverage report\n")
	fmt.Println(strings.Repeat("-", 40))

	coverageCmd := exec.Command("go", "test", "-coverprofile=coverage.out", "./tests/integration/...")
	coverageCmd.Stdout = os.Stdout
	coverageCmd.Stderr = os.Stderr

	if err := coverageCmd.Run(); err != nil {
		fmt.Printf("⚠️  Failed to generate coverage report: %v\n", err)
	} else {
		// Show coverage report
		htmlCmd := exec.Command("go", "tool", "cover", "-html=coverage.out", "-o", "coverage.html")
		if err := htmlCmd.Run(); err != nil {
			fmt.Printf("⚠️  Failed to generate HTML coverage report: %v\n", err)
		} else {
			fmt.Println("📈 Coverage report generated: coverage.html")
		}

		// Show coverage summary
		funcCmd := exec.Command("go", "tool", "cover", "-func=coverage.out")
		funcCmd.Stdout = os.Stdout
		funcCmd.Stderr = os.Stderr
		funcCmd.Run()
	}

	fmt.Println("\n✨ Integration test suite completed successfully!")
}
