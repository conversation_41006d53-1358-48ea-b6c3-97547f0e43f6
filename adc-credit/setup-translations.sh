#!/bin/bash

# Multi-language setup script for ADC Credit Service
set -e

PROJECT_ID="7265ab3a-4305-4fb5-95fe-eb6df015e6c5"
API_KEY="adc_development_7ad37376349339fc45467e9d98978591efca1fe6732f35f046b1157b18f75126"
BASE_URL="http://localhost:8300/api/v2"

echo "🚀 Setting up translations for ADC Credit Service..."
echo "Project ID: $PROJECT_ID"
echo "API Endpoint: $BASE_URL"

# Test API connectivity first
echo "📡 Testing API connectivity..."
response=$(curl -s "$BASE_URL/external/test" -H "X-API-Key: $API_KEY")
if echo "$response" | grep -q "External API working"; then
    echo "✅ API connectivity confirmed"
else
    echo "❌ API connectivity failed"
    echo "Response: $response"
    exit 1
fi

# Function to create translation
create_translation() {
    local namespace=$1
    local key=$2
    local locale=$3
    local content=$4
    
    echo "📝 Creating: $namespace.$key ($locale) = '$content'"
    
    response=$(curl -s -X POST "$BASE_URL/external/translations" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "{
            \"project_id\": \"$PROJECT_ID\",
            \"namespace\": \"$namespace\",
            \"key_name\": \"$key\",
            \"locale_code\": \"$locale\",
            \"content\": \"$content\"
        }")
    
    if echo "$response" | grep -q '"success".*true'; then
        echo "  ✅ Created successfully"
    else
        echo "  ⚠️  Response: $response"
    fi
}

# Create basic UI translations
echo ""
echo "🌍 Creating UI translations..."
create_translation "ui" "welcome_message" "en" "Welcome to ADC Credit Platform"
create_translation "ui" "welcome_message" "ja" "ADCクレジットプラットフォームへようこそ"
create_translation "ui" "welcome_message" "es" "Bienvenido a la Plataforma de Crédito ADC"

create_translation "ui" "login_button" "en" "Sign In"
create_translation "ui" "login_button" "ja" "ログイン"
create_translation "ui" "login_button" "es" "Iniciar Sesión"

create_translation "ui" "dashboard" "en" "Dashboard"
create_translation "ui" "dashboard" "ja" "ダッシュボード"
create_translation "ui" "dashboard" "es" "Panel de Control"

# Create credit-specific translations
echo ""
echo "💳 Creating credit translations..."
create_translation "credits" "balance" "en" "Credit Balance"
create_translation "credits" "balance" "ja" "クレジット残高"
create_translation "credits" "balance" "es" "Saldo de Crédito"

create_translation "credits" "add" "en" "Add Credits"
create_translation "credits" "add" "ja" "クレジット追加"
create_translation "credits" "add" "es" "Agregar Créditos"

# Create error message translations
echo ""
echo "⚠️  Creating error message translations..."
create_translation "errors" "not_found" "en" "Not found"
create_translation "errors" "not_found" "ja" "見つかりません"
create_translation "errors" "not_found" "es" "No encontrado"

echo ""
echo "🎉 Translation setup complete!"
echo ""
echo "🧪 Testing translations..."

# Function to test translation retrieval
test_translation() {
    local namespace=$1
    local key=$2
    local locale=$3
    
    echo "🔍 Testing: $namespace.$key ($locale)"
    response=$(curl -s "$BASE_URL/external/translations?project_id=$PROJECT_ID&namespace=$namespace&key=$key&language=$locale" \
        -H "X-API-Key: $API_KEY")
    
    if echo "$response" | grep -q '"success".*true'; then
        content=$(echo "$response" | jq -r '.data.translations[0].content // "No content"' 2>/dev/null || echo "Could not parse content")
        echo "  ✅ Result: '$content'"
    else
        echo "  ❌ Failed: $response"
    fi
}

test_translation "ui" "welcome_message" "ja"
test_translation "credits" "balance" "en"
test_translation "errors" "not_found" "es"

echo ""
echo "✨ Setup complete! You can now test the Credit Service translations at:"
echo "   https://localhost:3400/api/translations/ui/ja"
echo "   https://localhost:3400/api/translations/credits/en"
echo ""
echo "🔗 Test the translations now:"
echo "   curl -k https://localhost:3400/api/translations/ui/ja"