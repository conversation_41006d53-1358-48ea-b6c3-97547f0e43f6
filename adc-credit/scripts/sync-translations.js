#!/usr/bin/env node
/**
 * Translation sync utility
 * Syncs generated locale files with the Multi-Languages service
 */

const { readdir, readFile } = require('fs/promises');
const { join } = require('path');
const { existsSync } = require('fs');

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400';
const DEFAULT_LOCALE = 'en';
const LOCALES_DIR = join(process.cwd(), 'src', 'locales', DEFAULT_LOCALE);

async function loadNamespaceFile(namespace) {
  try {
    const filePath = join(LOCALES_DIR, `${namespace}.json`);
    if (!existsSync(filePath)) {
      return null;
    }

    const content = await readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${namespace}.json:`, error.message);
    return null;
  }
}

async function syncNamespace(namespace, translations) {
  try {
    console.log(`🔄 Syncing ${namespace} (${Object.keys(translations).length} keys)...`);

    const response = await fetch(`${BACKEND_URL}/api/translations/sync-namespace`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        namespace,
        locale: DEFAULT_LOCALE,
        translations,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      const { created_keys, updated_keys, total_keys } = result.data;
      console.log(`✅ ${namespace}: ${created_keys.length} created, ${updated_keys.length} updated (${total_keys} total)`);
      
      if (result.errors && result.errors.length > 0) {
        console.warn(`⚠️  ${namespace}: ${result.errors.length} errors:`);
        result.errors.forEach(error => console.warn(`   - ${error}`));
      }
    } else {
      console.error(`❌ ${namespace}: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.error(`❌ ${namespace}: ${error.message}`);
    return false;
  }
}

async function getAvailableNamespaces() {
  try {
    if (!existsSync(LOCALES_DIR)) {
      console.log(`📁 Locales directory not found: ${LOCALES_DIR}`);
      console.log('💡 Run your app with GENERATE_LOCALES=true to generate locale files');
      return [];
    }

    const files = await readdir(LOCALES_DIR);
    return files
      .filter(file => file.endsWith('.json'))
      .map(file => file.replace('.json', ''));
  } catch (error) {
    console.error('Failed to read locales directory:', error.message);
    return [];
  }
}

async function main() {
  const args = process.argv.slice(2);
  const targetNamespace = args[0];

  console.log('🚀 ADC Translation Sync Utility');
  console.log(`📍 Backend URL: ${BACKEND_URL}`);
  console.log(`📁 Locales Directory: ${LOCALES_DIR}`);
  console.log('');

  // Check if backend is available
  try {
    const healthResponse = await fetch(`${BACKEND_URL}/health`);
    if (!healthResponse.ok) {
      throw new Error(`Backend health check failed: ${healthResponse.status}`);
    }
    console.log('✅ Backend is available');
  } catch (error) {
    console.error('❌ Backend is not available:', error.message);
    console.log('💡 Make sure your backend is running on', BACKEND_URL);
    process.exit(1);
  }

  const namespaces = await getAvailableNamespaces();
  
  if (namespaces.length === 0) {
    console.log('📭 No locale files found');
    console.log('💡 Run your app with GENERATE_LOCALES=true to generate locale files');
    process.exit(0);
  }

  console.log(`📄 Found ${namespaces.length} namespace(s): ${namespaces.join(', ')}`);
  console.log('');

  const namespacesToSync = targetNamespace ? [targetNamespace] : namespaces;
  const results = [];

  for (const namespace of namespacesToSync) {
    if (!namespaces.includes(namespace)) {
      console.error(`❌ Namespace '${namespace}' not found`);
      continue;
    }

    const translations = await loadNamespaceFile(namespace);
    if (!translations) {
      console.error(`❌ Failed to load translations for '${namespace}'`);
      continue;
    }

    if (Object.keys(translations).length === 0) {
      console.log(`⏭️  Skipping '${namespace}' (empty)`);
      continue;
    }

    const success = await syncNamespace(namespace, translations);
    results.push({ namespace, success });
  }

  console.log('');
  console.log('📊 Sync Summary:');
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;
  
  console.log(`✅ Successful: ${successful}`);
  if (failed > 0) {
    console.log(`❌ Failed: ${failed}`);
    const failedNamespaces = results.filter(r => !r.success).map(r => r.namespace);
    console.log(`   ${failedNamespaces.join(', ')}`);
  }

  if (successful > 0) {
    console.log('');
    console.log('🎉 Sync completed! Your translations are now available in the Multi-Languages service.');
    console.log('💡 Visit your app with ?locale=fr to see translation status.');
  }

  process.exit(failed > 0 ? 1 : 0);
}

// Handle script errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error.message);
  process.exit(1);
});

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
});