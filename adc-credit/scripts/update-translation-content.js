#!/usr/bin/env node

/**
 * Simple Translation Content Update Script
 * 
 * This script directly updates translation content in the Multi-Languages service
 * using the internal API endpoints.
 */

import fetch from 'node-fetch';

const MULTILANG_SERVICE_URL = 'http://localhost:8300';
const API_KEY = 'adc_development_7ad37376349339fc45467e9d98978591efca1fe6732f35f046b1157b18f75126';
const PROJECT_ID = 'b90e383d-6e7d-4881-ba9a-c31649719348';

// Translation content to populate
const translations = {
  hero_title: {
    en: 'Shop Credit Management Platform',
    th: 'แพลตฟอร์มจัดการเครดิตร้านค้า'
  },
  hero_description: {
    en: 'Revolutionize your business with QR code-based credit transactions. Manage shops, customers, and payments seamlessly with our comprehensive platform.',
    th: 'ปฏิวัติธุรกิจของคุณด้วยธุรกรรมเครดิตที่ใช้ QR Code จัดการร้านค้า ลูกค้า และการชำระเงินได้อย่างราบรื่นด้วยแพลตฟอร์มที่ครอบคลุมของเรา'
  },
  button_start_shop: {
    en: 'Start Your Shop',
    th: 'เริ่มต้นร้านค้า'
  },
  button_view_docs: {
    en: 'View Documentation',
    th: 'ดูเอกสารประกอบ'
  },
  nav_dashboard: {
    en: 'Dashboard',
    th: 'แดชบอร์ด'
  },
  nav_shops: {
    en: 'Shops',
    th: 'ร้านค้า'
  },
  nav_customers: {
    en: 'Customers',
    th: 'ลูกค้า'
  },
  nav_documentation: {
    en: 'Documentation',
    th: 'เอกสารประกอบ'
  },
  button_sign_in: {
    en: 'Sign In',
    th: 'เข้าสู่ระบบ'
  }
};

async function getTranslationsForKey(keyName, language) {
  try {
    const url = `${MULTILANG_SERVICE_URL}/api/v2/external/translations?project_id=${PROJECT_ID}&namespace=ui&key=${keyName}&language=${language}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch translations: ${response.status}`);
    }

    const data = await response.json();
    return data.data.translations || [];
  } catch (error) {
    console.error(`Error fetching translations for ${keyName} (${language}):`, error.message);
    return [];
  }
}

async function createTranslationWithContent(keyName, language, content) {
  try {
    // Use the internal fetch endpoint to create translation with content
    const response = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/internal/translations/fetch`, {
      method: 'POST',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: PROJECT_ID,
        namespace: 'ui',
        key: keyName,
        locale: language,
        force_create: true
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to create translation: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`✅ Created/fetched translation for ${keyName} (${language})`);
    return result;
  } catch (error) {
    console.error(`❌ Error creating translation for ${keyName} (${language}):`, error.message);
    return null;
  }
}

async function updateAllTranslations() {
  console.log('🚀 Starting translation content update...\n');

  let successCount = 0;
  let errorCount = 0;

  for (const [keyName, content] of Object.entries(translations)) {
    console.log(`\n📝 Processing ${keyName}...`);

    // Process English
    try {
      const enResult = await createTranslationWithContent(keyName, 'en', content.en);
      if (enResult) {
        successCount++;
        console.log(`   ✅ English: "${content.en.substring(0, 50)}..."`);
      } else {
        errorCount++;
      }
    } catch (error) {
      console.error(`   ❌ English failed: ${error.message}`);
      errorCount++;
    }

    // Small delay
    await new Promise(resolve => setTimeout(resolve, 200));

    // Process Thai
    try {
      const thResult = await createTranslationWithContent(keyName, 'th', content.th);
      if (thResult) {
        successCount++;
        console.log(`   ✅ Thai: "${content.th.substring(0, 50)}..."`);
      } else {
        errorCount++;
      }
    } catch (error) {
      console.error(`   ❌ Thai failed: ${error.message}`);
      errorCount++;
    }

    // Delay between keys
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  console.log('\n📊 Update Summary:');
  console.log(`✅ Successful: ${successCount}`);
  console.log(`❌ Failed: ${errorCount}`);
  console.log(`📈 Success rate: ${((successCount / (successCount + errorCount)) * 100).toFixed(1)}%`);

  if (errorCount === 0) {
    console.log('\n🎉 All translations updated successfully!');
    console.log('🌍 Visit https://localhost:3400/ to see the translations in action.');
  } else {
    console.log('\n⚠️  Some translations failed. Check the Multi-Languages service logs for details.');
  }
}

// Test connection first
async function testConnection() {
  try {
    const response = await fetch(`${MULTILANG_SERVICE_URL}/health`);
    if (response.ok) {
      console.log('✅ Multi-Languages service is running');
      return true;
    } else {
      console.error('❌ Multi-Languages service health check failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Cannot connect to Multi-Languages service:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔧 Credit Service Translation Content Updater\n');

  // Test connection
  const isConnected = await testConnection();
  if (!isConnected) {
    console.error('💥 Cannot proceed without Multi-Languages service connection');
    process.exit(1);
  }

  // Update translations
  await updateAllTranslations();
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error.message);
  process.exit(1);
});

export { translations, updateAllTranslations };