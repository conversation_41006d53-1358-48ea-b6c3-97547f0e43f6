#!/usr/bin/env node
/**
 * Accessibility Audit Script
 * ADC Credit Service - WCAG 2.1 AA Compliance Auditing
 * 
 * Runs comprehensive accessibility audits and generates detailed reports
 * for WCAG compliance monitoring and continuous accessibility improvement.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  resultsDir: 'test-results/accessibility',
  reportFile: 'accessibility-report.html',
  baselineFile: 'accessibility-baseline.json',
  complianceThresholds: {
    levelAA: {
      maxViolations: 0,
      maxCritical: 0,
      maxSerious: 0,
    },
    levelA: {
      maxViolations: 0,
      maxCritical: 0,
      maxSerious: 0,
    }
  }
};

class AccessibilityAuditor {
  constructor() {
    this.resultsDir = path.join(process.cwd(), CONFIG.resultsDir);
    this.ensureDirectoryExists();
  }

  /**
   * Ensure results directory exists
   */
  ensureDirectoryExists() {
    if (!fs.existsSync(this.resultsDir)) {
      fs.mkdirSync(this.resultsDir, { recursive: true });
    }
  }

  /**
   * Run accessibility tests
   */
  async runAccessibilityTests() {
    console.log('♿ Starting accessibility audit...');
    
    try {
      // Run Playwright accessibility tests
      const testCommand = 'npx playwright test --config=tests/accessibility/accessibility.config.ts';
      console.log(`Running: ${testCommand}`);
      
      execSync(testCommand, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('✅ Accessibility tests completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Accessibility tests failed:', error.message);
      return false;
    }
  }

  /**
   * Parse accessibility test results
   */
  parseAccessibilityResults() {
    const resultsFile = path.join(this.resultsDir, 'accessibility-results.json');
    
    if (!fs.existsSync(resultsFile)) {
      console.warn('⚠️  No accessibility results file found');
      return null;
    }

    try {
      const rawResults = fs.readFileSync(resultsFile, 'utf8');
      const results = JSON.parse(rawResults);
      
      console.log('♿ Parsing accessibility results...');
      return this.extractAccessibilityMetrics(results);
    } catch (error) {
      console.error('❌ Failed to parse accessibility results:', error.message);
      return null;
    }
  }

  /**
   * Extract accessibility metrics from test results
   */
  extractAccessibilityMetrics(results) {
    const metrics = {
      timestamp: new Date().toISOString(),
      testSummary: {
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        duration: results.stats?.duration || 0,
      },
      violations: {
        total: 0,
        critical: 0,
        serious: 0,
        moderate: 0,
        minor: 0,
        byPage: {},
        byRule: {}
      },
      wcagCompliance: {
        levelA: true,
        levelAA: true,
        levelAAA: true,
        failedCriteria: []
      },
      coverage: {
        pagesAudited: 0,
        componentsAudited: 0,
        totalElements: 0
      }
    };

    // Extract violations from test results
    if (results.suites) {
      results.suites.forEach(suite => {
        if (suite.specs) {
          suite.specs.forEach(spec => {
            this.extractSpecAccessibilityMetrics(spec, metrics);
          });
        }
      });
    }

    // Calculate compliance levels
    this.calculateWCAGCompliance(metrics);
    
    return metrics;
  }

  /**
   * Extract accessibility metrics from individual test spec
   */
  extractSpecAccessibilityMetrics(spec, metrics) {
    const specName = spec.title;
    
    // Parse console output for accessibility violations
    if (spec.tests && spec.tests.length > 0) {
      spec.tests.forEach(test => {
        if (test.results && test.results.length > 0) {
          test.results.forEach(result => {
            if (result.stdout) {
              this.parseAccessibilityOutput(result.stdout, specName, metrics);
            }
            if (result.status === 'failed' && result.error?.message) {
              this.parseErrorForViolations(result.error.message, specName, metrics);
            }
          });
        }
      });
    }
  }

  /**
   * Parse console output for accessibility metrics
   */
  parseAccessibilityOutput(stdout, testName, metrics) {
    const lines = stdout.split('\n');
    
    lines.forEach(line => {
      // Parse axe-core violations
      if (line.includes('violations:') || line.includes('Accessibility violations')) {
        const match = line.match(/(\d+)\s+violations?/);
        if (match) {
          const violationCount = parseInt(match[1]);
          metrics.violations.total += violationCount;
          
          if (!metrics.violations.byPage[testName]) {
            metrics.violations.byPage[testName] = { total: 0, critical: 0, serious: 0, moderate: 0, minor: 0 };
          }
          metrics.violations.byPage[testName].total += violationCount;
        }
      }
      
      // Parse violation severity
      if (line.includes('critical:')) {
        const match = line.match(/critical:\s*(\d+)/);
        if (match) {
          const count = parseInt(match[1]);
          metrics.violations.critical += count;
          if (metrics.violations.byPage[testName]) {
            metrics.violations.byPage[testName].critical += count;
          }
        }
      }
      
      if (line.includes('serious:')) {
        const match = line.match(/serious:\s*(\d+)/);
        if (match) {
          const count = parseInt(match[1]);
          metrics.violations.serious += count;
          if (metrics.violations.byPage[testName]) {
            metrics.violations.byPage[testName].serious += count;
          }
        }
      }
    });
  }

  /**
   * Parse test errors for accessibility violations
   */
  parseErrorForViolations(errorMessage, testName, metrics) {
    // Parse axe-core violation details from error messages
    if (errorMessage.includes('Expected: []') && errorMessage.includes('Received:')) {
      // This indicates axe violations were found
      const violationMatch = errorMessage.match(/(\d+)\s+violations?/);
      if (violationMatch) {
        const violationCount = parseInt(violationMatch[1]);
        metrics.violations.total += violationCount;
        
        if (!metrics.violations.byPage[testName]) {
          metrics.violations.byPage[testName] = { total: 0, critical: 0, serious: 0, moderate: 0, minor: 0 };
        }
        metrics.violations.byPage[testName].total += violationCount;
      }
    }
  }

  /**
   * Calculate WCAG compliance levels
   */
  calculateWCAGCompliance(metrics) {
    // Level A compliance
    metrics.wcagCompliance.levelA = metrics.violations.critical === 0 && metrics.violations.serious === 0;
    
    // Level AA compliance
    metrics.wcagCompliance.levelAA = metrics.violations.critical === 0 && metrics.violations.serious === 0 && metrics.violations.moderate === 0;
    
    // Level AAA compliance
    metrics.wcagCompliance.levelAAA = metrics.violations.total === 0;
    
    // Failed criteria tracking
    if (metrics.violations.critical > 0) {
      metrics.wcagCompliance.failedCriteria.push('Critical accessibility issues');
    }
    if (metrics.violations.serious > 0) {
      metrics.wcagCompliance.failedCriteria.push('Serious accessibility issues');
    }
    if (metrics.violations.moderate > 0) {
      metrics.wcagCompliance.failedCriteria.push('Moderate accessibility issues');
    }
  }

  /**
   * Load accessibility baseline
   */
  loadBaseline() {
    const baselineFile = path.join(this.resultsDir, CONFIG.baselineFile);
    
    if (!fs.existsSync(baselineFile)) {
      console.log('📝 No accessibility baseline found, current results will be used as baseline');
      return null;
    }

    try {
      const baseline = JSON.parse(fs.readFileSync(baselineFile, 'utf8'));
      console.log(`♿ Loaded accessibility baseline from ${baseline.timestamp}`);
      return baseline;
    } catch (error) {
      console.error('❌ Failed to load accessibility baseline:', error.message);
      return null;
    }
  }

  /**
   * Save accessibility baseline
   */
  saveBaseline(metrics) {
    const baselineFile = path.join(this.resultsDir, CONFIG.baselineFile);
    
    try {
      fs.writeFileSync(baselineFile, JSON.stringify(metrics, null, 2));
      console.log('💾 Accessibility baseline saved');
    } catch (error) {
      console.error('❌ Failed to save accessibility baseline:', error.message);
    }
  }

  /**
   * Compare current accessibility metrics against baseline
   */
  compareToBaseline(currentMetrics, baseline) {
    if (!baseline) return null;

    const comparison = {
      timestamp: new Date().toISOString(),
      improvements: [],
      regressions: [],
      summary: {
        hasRegressions: false,
        hasImprovements: false,
        overallChange: 'stable'
      }
    };

    // Compare violation counts
    const currentTotal = currentMetrics.violations.total;
    const baselineTotal = baseline.violations.total;
    
    if (currentTotal < baselineTotal) {
      comparison.improvements.push({
        type: 'total_violations',
        current: currentTotal,
        baseline: baselineTotal,
        improvement: baselineTotal - currentTotal
      });
      comparison.summary.hasImprovements = true;
    } else if (currentTotal > baselineTotal) {
      comparison.regressions.push({
        type: 'total_violations',
        current: currentTotal,
        baseline: baselineTotal,
        regression: currentTotal - baselineTotal
      });
      comparison.summary.hasRegressions = true;
    }

    // Compare compliance levels
    if (baseline.wcagCompliance.levelAA && !currentMetrics.wcagCompliance.levelAA) {
      comparison.regressions.push({
        type: 'wcag_aa_compliance',
        current: 'Non-compliant',
        baseline: 'Compliant',
        regression: 'Lost WCAG AA compliance'
      });
      comparison.summary.hasRegressions = true;
    } else if (!baseline.wcagCompliance.levelAA && currentMetrics.wcagCompliance.levelAA) {
      comparison.improvements.push({
        type: 'wcag_aa_compliance',
        current: 'Compliant',
        baseline: 'Non-compliant',
        improvement: 'Achieved WCAG AA compliance'
      });
      comparison.summary.hasImprovements = true;
    }

    // Determine overall change
    if (comparison.summary.hasRegressions) {
      comparison.summary.overallChange = 'regression';
    } else if (comparison.summary.hasImprovements) {
      comparison.summary.overallChange = 'improvement';
    }

    return comparison;
  }

  /**
   * Generate comprehensive accessibility report
   */
  generateAccessibilityReport(metrics, comparison) {
    const reportHtml = this.createAccessibilityReportHTML(metrics, comparison);
    const reportFile = path.join(this.resultsDir, CONFIG.reportFile);
    
    try {
      fs.writeFileSync(reportFile, reportHtml);
      console.log(`📄 Accessibility report generated: ${reportFile}`);
    } catch (error) {
      console.error('❌ Failed to generate accessibility report:', error.message);
    }
  }

  /**
   * Create HTML accessibility report
   */
  createAccessibilityReportHTML(metrics, comparison) {
    const complianceBadge = (isCompliant) => 
      isCompliant ? '<span style="color: #4CAF50; font-weight: bold;">✅ COMPLIANT</span>' : 
                   '<span style="color: #F44336; font-weight: bold;">❌ NON-COMPLIANT</span>';

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADC Credit Service - Accessibility Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 30px; }
        .compliance-section { background: #f4f4f4; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .metric { display: inline-block; margin: 10px 15px 10px 0; padding: 15px; background: #fff; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .critical { border-left: 4px solid #F44336; }
        .serious { border-left: 4px solid #FF9800; }
        .moderate { border-left: 4px solid #FFC107; }
        .minor { border-left: 4px solid #4CAF50; }
        .compliant { background: #e8f5e8; border-left: 4px solid #4CAF50; }
        .non-compliant { background: #ffebee; border-left: 4px solid #f44336; }
        .violation-item { margin: 10px 0; padding: 10px; background: #fff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .improvement { background: #e8f5e8; border-left: 4px solid #4caf50; padding: 10px; margin: 5px 0; }
        .regression { background: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 5px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; background: #fff; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .page-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fff; }
        .recommendations { background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196f3; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 2px solid #ddd; color: #666; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>♿ ADC Credit Service - Accessibility Report</h1>
        <p><strong>Generated:</strong> ${metrics.timestamp}</p>
        <p><strong>Test Summary:</strong> ${metrics.testSummary.passed}/${metrics.testSummary.total} tests passed</p>
        <p><strong>Total Violations:</strong> ${metrics.violations.total}</p>
    </div>

    <div class="compliance-section">
        <h2>🏆 WCAG Compliance Status</h2>
        <table>
            <tr>
                <th>WCAG Level</th>
                <th>Status</th>
                <th>Requirements</th>
                <th>Notes</th>
            </tr>
            <tr class="${metrics.wcagCompliance.levelA ? 'compliant' : 'non-compliant'}">
                <td><strong>Level A</strong></td>
                <td>${complianceBadge(metrics.wcagCompliance.levelA)}</td>
                <td>No critical or serious violations</td>
                <td>Minimum compliance level</td>
            </tr>
            <tr class="${metrics.wcagCompliance.levelAA ? 'compliant' : 'non-compliant'}">
                <td><strong>Level AA</strong></td>
                <td>${complianceBadge(metrics.wcagCompliance.levelAA)}</td>
                <td>No critical, serious, or moderate violations</td>
                <td>Target compliance level</td>
            </tr>
            <tr class="${metrics.wcagCompliance.levelAAA ? 'compliant' : 'non-compliant'}">
                <td><strong>Level AAA</strong></td>
                <td>${complianceBadge(metrics.wcagCompliance.levelAAA)}</td>
                <td>No violations of any level</td>
                <td>Highest compliance level</td>
            </tr>
        </table>
    </div>

    <h2>📊 Violation Summary</h2>
    <div class="metric critical">
        <strong>Critical Violations:</strong> ${metrics.violations.critical}<br>
        <small>Issues that prevent access for users with disabilities</small>
    </div>
    <div class="metric serious">
        <strong>Serious Violations:</strong> ${metrics.violations.serious}<br>
        <small>Significant barriers to accessibility</small>
    </div>
    <div class="metric moderate">
        <strong>Moderate Violations:</strong> ${metrics.violations.moderate}<br>
        <small>Some barriers to accessibility</small>
    </div>
    <div class="metric minor">
        <strong>Minor Violations:</strong> ${metrics.violations.minor}<br>
        <small>Minor accessibility improvements needed</small>
    </div>

    <h2>📄 Page-by-Page Analysis</h2>
    ${Object.entries(metrics.violations.byPage).map(([pageName, pageViolations]) => `
    <div class="page-section">
        <h3>${pageName.charAt(0).toUpperCase() + pageName.slice(1)} Page</h3>
        <table>
            <tr>
                <th>Violation Type</th>
                <th>Count</th>
                <th>Impact</th>
            </tr>
            <tr class="critical">
                <td>Critical</td>
                <td>${pageViolations.critical}</td>
                <td>Prevents access</td>
            </tr>
            <tr class="serious">
                <td>Serious</td>
                <td>${pageViolations.serious}</td>
                <td>Significant barrier</td>
            </tr>
            <tr class="moderate">
                <td>Moderate</td>
                <td>${pageViolations.moderate}</td>
                <td>Some barrier</td>
            </tr>
            <tr class="minor">
                <td>Minor</td>
                <td>${pageViolations.minor}</td>
                <td>Minor improvement</td>
            </tr>
        </table>
        <p><strong>Total Violations:</strong> ${pageViolations.total}</p>
    </div>
    `).join('')}

    ${comparison ? `
    <h2>📈 Accessibility Progress</h2>
    ${comparison.regressions.length > 0 ? `
    <h3>❌ Accessibility Regressions</h3>
    ${comparison.regressions.map(regression => `
    <div class="regression">
        <strong>${regression.type.replace(/_/g, ' ').toUpperCase()}:</strong> 
        ${regression.regression || regression.current} 
        (was: ${regression.baseline})
    </div>
    `).join('')}
    ` : ''}
    
    ${comparison.improvements.length > 0 ? `
    <h3>✅ Accessibility Improvements</h3>
    ${comparison.improvements.map(improvement => `
    <div class="improvement">
        <strong>${improvement.type.replace(/_/g, ' ').toUpperCase()}:</strong> 
        ${improvement.improvement || improvement.current}
        (was: ${improvement.baseline})
    </div>
    `).join('')}
    ` : ''}
    
    ${comparison.regressions.length === 0 && comparison.improvements.length === 0 ? `
    <p>✅ No significant accessibility changes detected compared to baseline.</p>
    ` : ''}
    ` : ''}

    <div class="recommendations">
        <h2>💡 Accessibility Recommendations</h2>
        ${metrics.violations.critical > 0 ? `
        <h3>🚨 URGENT: Fix Critical Issues</h3>
        <ul>
            <li>Review all form controls for proper labeling</li>
            <li>Ensure all interactive elements are keyboard accessible</li>
            <li>Fix color contrast issues immediately</li>
            <li>Add alternative text for all images</li>
        </ul>
        ` : ''}
        
        ${metrics.violations.serious > 0 ? `
        <h3>⚠️ HIGH PRIORITY: Address Serious Issues</h3>
        <ul>
            <li>Improve focus management and visible focus indicators</li>
            <li>Ensure proper heading hierarchy</li>
            <li>Add ARIA landmarks and roles</li>
            <li>Fix form validation and error messaging</li>
        </ul>
        ` : ''}
        
        ${metrics.violations.moderate > 0 ? `
        <h3>📋 MEDIUM PRIORITY: Resolve Moderate Issues</h3>
        <ul>
            <li>Enhance screen reader support with better ARIA descriptions</li>
            <li>Improve error identification and suggestions</li>
            <li>Ensure consistent navigation and interaction patterns</li>
        </ul>
        ` : ''}
        
        ${metrics.violations.total === 0 ? `
        <h3>🎉 Excellent Accessibility!</h3>
        <p>Your application meets WCAG 2.1 AA accessibility standards. Consider:</p>
        <ul>
            <li>Regular accessibility testing as part of your development workflow</li>
            <li>User testing with people who use assistive technologies</li>
            <li>Monitoring for accessibility regressions in CI/CD pipeline</li>
        </ul>
        ` : ''}
    </div>

    <div class="footer">
        <p>Generated by ADC Credit Service Accessibility Auditor</p>
        <p>For more information about WCAG guidelines, visit <a href="https://www.w3.org/WAI/WCAG21/quickref/">WCAG 2.1 Quick Reference</a></p>
    </div>
</body>
</html>
    `;
  }

  /**
   * Run complete accessibility audit workflow
   */
  async run() {
    console.log('♿ Starting ADC Credit Service Accessibility Audit\n');

    // Run accessibility tests
    const testSuccess = await this.runAccessibilityTests();
    if (!testSuccess) {
      process.exit(1);
    }

    // Parse results
    const metrics = this.parseAccessibilityResults();
    if (!metrics) {
      console.error('❌ No accessibility metrics available');
      process.exit(1);
    }

    // Load baseline and compare
    const baseline = this.loadBaseline();
    const comparison = this.compareToBaseline(metrics, baseline);

    // Save baseline if none exists
    if (!baseline) {
      this.saveBaseline(metrics);
    }

    // Generate report
    this.generateAccessibilityReport(metrics, comparison);

    // Print summary
    this.printAccessibilitySummary(metrics, comparison);

    // Exit with appropriate code based on compliance
    if (!metrics.wcagCompliance.levelAA) {
      console.log('\n❌ WCAG AA compliance not achieved!');
      process.exit(1);
    } else {
      console.log('\n✅ Accessibility audit completed successfully - WCAG AA compliant');
      process.exit(0);
    }
  }

  /**
   * Print accessibility summary to console
   */
  printAccessibilitySummary(metrics, comparison) {
    console.log('\n♿ Accessibility Summary:');
    console.log(`   Tests: ${metrics.testSummary.passed}/${metrics.testSummary.total} passed`);
    console.log(`   Total Violations: ${metrics.violations.total}`);
    console.log(`   Critical: ${metrics.violations.critical}`);
    console.log(`   Serious: ${metrics.violations.serious}`);
    console.log(`   Moderate: ${metrics.violations.moderate}`);
    console.log(`   Minor: ${metrics.violations.minor}`);
    console.log(`   WCAG Level A: ${metrics.wcagCompliance.levelA ? '✅' : '❌'}`);
    console.log(`   WCAG Level AA: ${metrics.wcagCompliance.levelAA ? '✅' : '❌'}`);
    console.log(`   WCAG Level AAA: ${metrics.wcagCompliance.levelAAA ? '✅' : '❌'}`);
    
    if (comparison) {
      console.log(`   Regressions: ${comparison.regressions.length}`);
      console.log(`   Improvements: ${comparison.improvements.length}`);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const auditor = new AccessibilityAuditor();
  auditor.run().catch(error => {
    console.error('❌ Accessibility audit failed:', error);
    process.exit(1);
  });
}

module.exports = AccessibilityAuditor;