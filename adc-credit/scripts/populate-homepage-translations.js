#!/usr/bin/env node

/**
 * Bulk Translation Population Script for Credit Service Homepage
 * 
 * This script populates all homepage translation keys with their default values
 * in both English and Thai languages.
 */

import fetch from 'node-fetch';

const BASE_URL = 'https://localhost:3400';
const PROJECT_ID = 'b90e383d-6e7d-4881-ba9a-c31649719348';

// Translation data structure
const homepageTranslations = {
  // Hero Section
  'hero_title': { 
    en: 'Shop Credit Management Platform', 
    th: 'แพลตฟอร์มจัดการเครดิตร้านค้า' 
  },
  'hero_subtitle': { 
    en: 'Revolutionize Your Business', 
    th: 'ปฏิวัติธุรกิจของคุณ' 
  },
  'hero_description': { 
    en: 'Revolutionize your business with QR code-based credit transactions. Manage shops, customers, and payments seamlessly with our comprehensive platform.',
    th: 'ปฏิวัติธุรกิจของคุณด้วยธุรกรรมเครดิตที่ใช้ QR Code จัดการร้านค้า ลูกค้า และการชำระเงินได้อย่างราบรื่นด้วยแพลตฟอร์มที่ครอบคลุมของเรา'
  },
  'button_start_shop': { 
    en: 'Start Your Shop', 
    th: 'เริ่มต้นร้านค้า' 
  },
  'button_view_docs': { 
    en: 'View Documentation', 
    th: 'ดูเอกสารประกอบ' 
  },

  // Navigation
  'nav_dashboard': { 
    en: 'Dashboard', 
    th: 'แดชบอร์ด' 
  },
  'nav_shops': { 
    en: 'Shops', 
    th: 'ร้านค้า' 
  },
  'nav_customers': { 
    en: 'Customers', 
    th: 'ลูกค้า' 
  },
  'nav_documentation': { 
    en: 'Documentation', 
    th: 'เอกสารประกอบ' 
  },
  'button_sign_in': { 
    en: 'Sign In', 
    th: 'เข้าสู่ระบบ' 
  },

  // Features Section
  'features_title': { 
    en: 'Powerful Features', 
    th: 'ฟีเจอร์ที่ทรงพลัง' 
  },
  'features_subtitle': { 
    en: 'Everything you need to manage your business', 
    th: 'ทุกสิ่งที่คุณต้องการในการจัดการธุรกิจ' 
  },
  
  // Feature Cards
  'feature_qr_title': { 
    en: 'QR Code Payments', 
    th: 'การชำระเงินด้วย QR Code' 
  },
  'feature_qr_description': { 
    en: 'Generate and manage QR codes for seamless customer transactions',
    th: 'สร้างและจัดการ QR Code สำหรับธุรกรรมลูกค้าที่ราบรื่น'
  },
  
  'feature_analytics_title': { 
    en: 'Real-time Analytics', 
    th: 'การวิเคราะห์แบบเรียลไทม์' 
  },
  'feature_analytics_description': {
    en: 'Track your business performance with detailed analytics and insights',
    th: 'ติดตามประสิทธิภาพธุรกิจด้วยการวิเคราะห์และข้อมูลเชิงลึกที่ละเอียด'
  },

  'feature_security_title': { 
    en: 'Enterprise Security', 
    th: 'ความปลอดภัยระดับองค์กร' 
  },
  'feature_security_description': {
    en: 'Bank-level security with encrypted transactions and secure data storage',
    th: 'ความปลอดภัยระดับธนาคารด้วยธุรกรรมที่เข้ารหัสและการจัดเก็บข้อมูลที่ปลอดภัย'
  },

  // Common UI Elements
  'loading': { 
    en: 'Loading...', 
    th: 'กำลังโหลด...' 
  },
  'save': { 
    en: 'Save', 
    th: 'บันทึก' 
  },
  'cancel': { 
    en: 'Cancel', 
    th: 'ยกเลิก' 
  },
  'edit': { 
    en: 'Edit', 
    th: 'แก้ไข' 
  },
  'delete': { 
    en: 'Delete', 
    th: 'ลบ' 
  },
  'confirm': { 
    en: 'Confirm', 
    th: 'ยืนยัน' 
  },
  'success': { 
    en: 'Success', 
    th: 'สำเร็จ' 
  },
  'error': { 
    en: 'Error', 
    th: 'ข้อผิดพลาด' 
  },
};

// Disable SSL verification for localhost development
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

async function updateTranslation(key, namespace, language, content) {
  try {
    console.log(`Updating ${key} (${language}): ${content.substring(0, 50)}...`);
    
    const response = await fetch(`${BASE_URL}/api/admin/translations/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        key,
        namespace,
        language,
        content,
        project_id: PROJECT_ID
      }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      console.error(`❌ Failed to update ${key} (${language}):`, data.error);
      return false;
    }
    
    console.log(`✅ Updated ${key} (${language})`);
    return true;
  } catch (error) {
    console.error(`❌ Error updating ${key} (${language}):`, error.message);
    return false;
  }
}

async function populateAllTranslations() {
  console.log('🚀 Starting homepage translation population...\n');
  
  let successCount = 0;
  let errorCount = 0;
  const totalKeys = Object.keys(homepageTranslations).length * 2; // 2 languages per key

  for (const [key, translations] of Object.entries(homepageTranslations)) {
    // Update English translation
    const enSuccess = await updateTranslation(key, 'ui', 'en', translations.en);
    if (enSuccess) successCount++;
    else errorCount++;

    // Small delay to prevent overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 100));

    // Update Thai translation
    const thSuccess = await updateTranslation(key, 'ui', 'th', translations.th);
    if (thSuccess) successCount++;
    else errorCount++;

    // Small delay between keys
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('\n📊 Translation Population Summary:');
  console.log(`✅ Successful updates: ${successCount}/${totalKeys}`);
  console.log(`❌ Failed updates: ${errorCount}/${totalKeys}`);
  console.log(`📈 Success rate: ${((successCount / totalKeys) * 100).toFixed(1)}%`);

  if (errorCount === 0) {
    console.log('\n🎉 All translations populated successfully!');
    console.log(`🌍 Visit ${BASE_URL}/admin/translations to manage them.`);
  } else {
    console.log('\n⚠️  Some translations failed to update. Check the logs above for details.');
  }
}

// Error handling for the script
async function main() {
  try {
    await populateAllTranslations();
  } catch (error) {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();

export { homepageTranslations, updateTranslation };