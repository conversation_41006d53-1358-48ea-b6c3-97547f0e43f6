#!/bin/bash

# ADC Credit Service - i18n Integration Testing Script
# This script runs E2E tests with real Multi-Languages service integration

set -e

echo "🚀 Starting ADC Credit i18n Integration Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MULTI_LANG_PATH="/Users/<USER>/Desktop/adc-platform/services/core/adc-muti-languages"
CREDIT_SERVICE_PATH="/Users/<USER>/Desktop/adc-platform/services/core/adc-credit"
MULTI_LANG_PORT=8300
CREDIT_SERVICE_PORT=3800

# Function to check if service is running
check_service() {
    local port=$1
    local service_name=$2
    
    if curl -s "http://localhost:$port" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name is running on port $port${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is not running on port $port${NC}"
        return 1
    fi
}

# Function to start Multi-Languages service
start_multi_lang_service() {
    echo -e "${BLUE}📡 Starting Multi-Languages service...${NC}"
    
    if [ ! -d "$MULTI_LANG_PATH" ]; then
        echo -e "${RED}❌ Multi-Languages service directory not found: $MULTI_LANG_PATH${NC}"
        exit 1
    fi
    
    cd "$MULTI_LANG_PATH"
    
    # Check if already running
    if check_service $MULTI_LANG_PORT "Multi-Languages service"; then
        echo -e "${GREEN}✅ Multi-Languages service already running${NC}"
        return 0
    fi
    
    # Start the backend
    echo -e "${YELLOW}⏳ Starting Multi-Languages backend...${NC}"
    make backend > /tmp/multi-lang.log 2>&1 &
    MULTI_LANG_PID=$!
    
    # Wait for service to start
    echo -e "${YELLOW}⏳ Waiting for Multi-Languages service to start...${NC}"
    for i in {1..30}; do
        if check_service $MULTI_LANG_PORT "Multi-Languages service" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Multi-Languages service started successfully${NC}"
            return 0
        fi
        sleep 2
    done
    
    echo -e "${RED}❌ Multi-Languages service failed to start${NC}"
    echo -e "${YELLOW}📋 Multi-Languages service logs:${NC}"
    tail -20 /tmp/multi-lang.log
    exit 1
}

# Function to check Credit Service
check_credit_service() {
    echo -e "${BLUE}🏪 Checking Credit Service...${NC}"
    
    if check_service $CREDIT_SERVICE_PORT "Credit Service"; then
        echo -e "${GREEN}✅ Credit Service is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Credit Service is not running${NC}"
        echo -e "${YELLOW}💡 Please start Credit Service with: npm run dev${NC}"
        exit 1
    fi
}

# Function to verify service communication
verify_service_communication() {
    echo -e "${BLUE}🔗 Verifying service communication...${NC}"
    
    # Test Multi-Languages service health
    if curl -s "http://localhost:$MULTI_LANG_PORT/health" > /dev/null; then
        echo -e "${GREEN}✅ Multi-Languages service health endpoint accessible${NC}"
    else
        echo -e "${YELLOW}⚠️ Multi-Languages service health endpoint not accessible${NC}"
    fi
    
    # Test translation API endpoint
    response=$(curl -s -w "%{http_code}" -X POST \
        "http://localhost:$MULTI_LANG_PORT/api/v2/internal/translations/fetch" \
        -H "Content-Type: application/json" \
        -H "X-Internal-Key: adc-internal-2024" \
        -d '{
            "project_id": "adc-credit-project",
            "namespace": "ui",
            "key": "test",
            "locale": "en"
        }' -o /dev/null)
    
    if [ "$response" = "200" ] || [ "$response" = "404" ]; then
        echo -e "${GREEN}✅ Translation API endpoint accessible${NC}"
    else
        echo -e "${YELLOW}⚠️ Translation API endpoint returned: $response${NC}"
    fi
}

# Function to run i18n tests
run_i18n_tests() {
    echo -e "${BLUE}🧪 Running i18n E2E tests...${NC}"
    
    cd "$CREDIT_SERVICE_PATH"
    
    # Add i18n tests to playwright config temporarily
    echo -e "${YELLOW}📝 Updating Playwright config for i18n tests...${NC}"
    
    # Create a temporary config for i18n tests
    cat > playwright-i18n.config.js << EOF
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  testMatch: [
    '**/i18n-*.spec.ts'
  ],
  fullyParallel: false, // Run i18n tests sequentially for service management
  forbidOnly: !!process.env.CI,
  retries: 0, // No retries for integration tests
  workers: 1, // Single worker for service coordination
  reporter: [
    ['html', { outputFolder: 'playwright-report-i18n' }],
    ['json', { outputFile: 'test-results/i18n-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:$CREDIT_SERVICE_PORT',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 15000,
    navigationTimeout: 30000
  },
  projects: [
    {
      name: 'chromium-i18n',
      use: { ...devices['Desktop Chrome'] }
    }
  ],
  expect: {
    timeout: 10000
  },
  outputDir: 'test-results/i18n/',
  metadata: {
    'test-suite': 'ADC Credit i18n Integration Tests',
    'multi-lang-service': 'http://localhost:$MULTI_LANG_PORT',
    'credit-service': 'http://localhost:$CREDIT_SERVICE_PORT'
  }
});
EOF
    
    # Run the i18n tests
    echo -e "${YELLOW}🎯 Executing i18n integration tests...${NC}"
    
    if npx playwright test --config=playwright-i18n.config.js; then
        echo -e "${GREEN}✅ i18n E2E tests completed successfully${NC}"
        
        # Show test results
        echo -e "${BLUE}📊 Test Results:${NC}"
        if [ -f "test-results/i18n-results.json" ]; then
            echo -e "${YELLOW}📋 Detailed results saved to: test-results/i18n-results.json${NC}"
        fi
        
        if [ -d "playwright-report-i18n" ]; then
            echo -e "${YELLOW}📋 HTML report available at: playwright-report-i18n/index.html${NC}"
            echo -e "${BLUE}🌐 Open report with: npx playwright show-report playwright-report-i18n${NC}"
        fi
        
    else
        echo -e "${RED}❌ i18n E2E tests failed${NC}"
        
        # Show recent logs
        echo -e "${YELLOW}📋 Recent Multi-Languages service logs:${NC}"
        tail -20 /tmp/multi-lang.log 2>/dev/null || echo "No logs available"
        
        exit 1
    fi
    
    # Cleanup temporary config
    rm -f playwright-i18n.config.js
}

# Function to cleanup services
cleanup_services() {
    echo -e "${BLUE}🧹 Cleaning up services...${NC}"
    
    if [ ! -z "$MULTI_LANG_PID" ]; then
        echo -e "${YELLOW}🛑 Stopping Multi-Languages service (PID: $MULTI_LANG_PID)...${NC}"
        kill $MULTI_LANG_PID 2>/dev/null || true
        
        # Wait for graceful shutdown
        sleep 3
        
        # Force kill if still running
        kill -9 $MULTI_LANG_PID 2>/dev/null || true
    fi
    
    # Clean up log files
    rm -f /tmp/multi-lang.log
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Trap to cleanup on exit
trap cleanup_services EXIT

# Main execution
main() {
    echo -e "${BLUE}🎯 ADC Credit i18n Integration Testing Pipeline${NC}"
    echo -e "${BLUE}================================================${NC}"
    
    # Step 1: Check Credit Service
    check_credit_service
    
    # Step 2: Start Multi-Languages service
    start_multi_lang_service
    
    # Step 3: Verify communication
    verify_service_communication
    
    # Step 4: Run tests
    run_i18n_tests
    
    echo -e "${GREEN}🎉 i18n integration testing completed successfully!${NC}"
}

# Handle command line arguments
case "${1:-}" in
    "--help"|"-h")
        echo "ADC Credit i18n Integration Testing Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --cleanup      Cleanup services and exit"
        echo ""
        echo "Requirements:"
        echo "  - Multi-Languages service at: $MULTI_LANG_PATH"
        echo "  - Credit Service running on port: $CREDIT_SERVICE_PORT"
        echo ""
        exit 0
        ;;
    "--cleanup")
        cleanup_services
        exit 0
        ;;
    *)
        main
        ;;
esac