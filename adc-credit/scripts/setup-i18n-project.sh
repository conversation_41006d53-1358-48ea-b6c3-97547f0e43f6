#!/bin/bash

# ADC Credit i18n Project Setup Script
# This script creates the ADC Credit project in the Multi-Languages service

set -e

echo "🌍 Setting up ADC Credit project in Multi-Languages service..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MULTI_LANG_URL="http://localhost:8300"
SSO_URL="http://localhost:9000"
PROJECT_SLUG="adc-credit-project"
PROJECT_NAME="ADC Credit Project"
ORG_NAME="ADC Platform"

# Temporary storage for IDs
AUTH_TOKEN=""
ORG_ID=""
PROJECT_ID=""

# Function to check if Multi-Languages service is running
check_service() {
    echo -e "${BLUE}📡 Checking Multi-Languages service...${NC}"
    
    if curl -s "$MULTI_LANG_URL/health" > /dev/null; then
        echo -e "${GREEN}✅ Multi-Languages service is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Multi-Languages service is not running${NC}"
        echo -e "${YELLOW}💡 Please start it with: cd adc-muti-languages && make backend${NC}"
        exit 1
    fi
}

# Function to get authentication token
get_auth_token() {
    echo -e "${BLUE}🔑 Getting authentication token...${NC}"
    
    # For testing, we'll use a demo user or create one
    # First, try to sign in with a test user
    local signin_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/auth/signin" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "admin123"
        }' 2>/dev/null || echo '{"success":false}')
    
    # Check if signin was successful
    if echo "$signin_response" | grep -q '"success":true'; then
        AUTH_TOKEN=$(echo "$signin_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ Authentication successful with existing user${NC}"
        return 0
    fi
    
    # If signin failed, try to create a test user
    echo -e "${YELLOW}⏳ Creating test user for project setup...${NC}"
    
    local signup_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/auth/signup" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "admin123",
            "name": "ADC Admin",
            "role": "admin"
        }' 2>/dev/null || echo '{"success":false}')
    
    if echo "$signup_response" | grep -q '"success":true'; then
        AUTH_TOKEN=$(echo "$signup_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ Test user created and authenticated${NC}"
        return 0
    fi
    
    # If both failed, try with SSO service
    echo -e "${YELLOW}⏳ Attempting SSO authentication...${NC}"
    
    # This is a simplified approach - in real implementation you'd use proper SSO flow
    echo -e "${YELLOW}⚠️ Direct SSO authentication not implemented in setup script${NC}"
    echo -e "${YELLOW}💡 Please manually authenticate and provide token${NC}"
    
    read -p "Please provide authentication token (or press Enter to skip): " manual_token
    if [ ! -z "$manual_token" ]; then
        AUTH_TOKEN="$manual_token"
        echo -e "${GREEN}✅ Using provided authentication token${NC}"
        return 0
    fi
    
    echo -e "${RED}❌ Could not obtain authentication token${NC}"
    return 1
}

# Function to get or create organization
get_or_create_organization() {
    echo -e "${BLUE}🏢 Getting or creating organization...${NC}"
    
    if [ -z "$AUTH_TOKEN" ]; then
        echo -e "${YELLOW}⚠️ No authentication token, skipping organization setup${NC}"
        return 1
    fi
    
    # Get existing organizations
    local orgs_response=$(curl -s -X GET "$MULTI_LANG_URL/api/v2/organizations" \
        -H "Authorization: Bearer $AUTH_TOKEN" 2>/dev/null || echo '{"success":false}')
    
    # Check if ADC Platform organization already exists
    if echo "$orgs_response" | grep -q "ADC Platform"; then
        ORG_ID=$(echo "$orgs_response" | grep -A 5 -B 5 "ADC Platform" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
        if [ ! -z "$ORG_ID" ]; then
            echo -e "${GREEN}✅ Found existing ADC Platform organization: $ORG_ID${NC}"
            return 0
        fi
    fi
    
    # Create new organization
    echo -e "${YELLOW}⏳ Creating ADC Platform organization...${NC}"
    
    local create_org_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/organizations" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "ADC Platform",
            "description": "Main organization for ADC platform services",
            "slug": "adc-platform"
        }' 2>/dev/null || echo '{"success":false}')
    
    if echo "$create_org_response" | grep -q '"success":true'; then
        ORG_ID=$(echo "$create_org_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ Created ADC Platform organization: $ORG_ID${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to create organization${NC}"
        echo "Response: $create_org_response"
        return 1
    fi
}

# Function to get or create project
get_or_create_project() {
    echo -e "${BLUE}📦 Getting or creating ADC Credit project...${NC}"
    
    if [ -z "$AUTH_TOKEN" ] || [ -z "$ORG_ID" ]; then
        echo -e "${YELLOW}⚠️ Missing authentication or organization, skipping project setup${NC}"
        return 1
    fi
    
    # Check if project already exists
    local project_response=$(curl -s -X GET "$MULTI_LANG_URL/api/v2/projects/slug/$PROJECT_SLUG?org_id=$ORG_ID" \
        -H "Authorization: Bearer $AUTH_TOKEN" 2>/dev/null || echo '{"success":false}')
    
    if echo "$project_response" | grep -q '"success":true'; then
        PROJECT_ID=$(echo "$project_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ Found existing ADC Credit project: $PROJECT_ID${NC}"
        return 0
    fi
    
    # Create new project
    echo -e "${YELLOW}⏳ Creating ADC Credit project...${NC}"
    
    local create_project_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/projects" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$PROJECT_NAME\",
            \"slug\": \"$PROJECT_SLUG\",
            \"organization_id\": \"$ORG_ID\",
            \"description\": \"Translation management for ADC Credit service with multi-language support\",
            \"default_locale\": \"en\",
            \"is_public\": false
        }" 2>/dev/null || echo '{"success":false}')
    
    if echo "$create_project_response" | grep -q '"success":true'; then
        PROJECT_ID=$(echo "$create_project_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ Created ADC Credit project: $PROJECT_ID${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to create project${NC}"
        echo "Response: $create_project_response"
        return 1
    fi
}

# Function to setup project locales
setup_project_locales() {
    echo -e "${BLUE}🌐 Setting up project locales...${NC}"
    
    if [ -z "$AUTH_TOKEN" ] || [ -z "$PROJECT_ID" ]; then
        echo -e "${YELLOW}⚠️ Missing authentication or project, skipping locale setup${NC}"
        return 1
    fi
    
    # List of locales to support
    local locales=("en" "es" "fr" "de" "pt" "ja" "ko" "zh" "th" "ar" "he")
    
    for locale in "${locales[@]}"; do
        echo -e "${YELLOW}⏳ Adding locale: $locale${NC}"
        
        local locale_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/project-locales" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"project_id\": \"$PROJECT_ID\",
                \"locale\": \"$locale\"
            }" 2>/dev/null || echo '{"success":false}')
        
        if echo "$locale_response" | grep -q '"success":true'; then
            echo -e "${GREEN}  ✅ Added locale: $locale${NC}"
        else
            echo -e "${YELLOW}  ⚠️ Locale $locale might already exist or failed to add${NC}"
        fi
    done
}

# Function to create sample translation keys
create_sample_translations() {
    echo -e "${BLUE}📝 Creating sample translation keys...${NC}"
    
    if [ -z "$AUTH_TOKEN" ] || [ -z "$PROJECT_ID" ]; then
        echo -e "${YELLOW}⚠️ Missing authentication or project, skipping translations setup${NC}"
        return 1
    fi
    
    # Sample translation keys for ADC Credit
    declare -A sample_keys=(
        ["dashboard.title"]="Dashboard"
        ["dashboard.description"]="Overview of your API usage and credits"
        ["dashboard.credit_balance"]="Credit Balance"
        ["dashboard.api_keys"]="API Keys"
        ["dashboard.quick_actions"]="Quick Actions"
        ["shops.title"]="Shops"
        ["shops.create_shop"]="Create Shop"
        ["shops.description"]="Manage your retail stores, API services, and enterprise locations"
        ["ui.loading"]="Loading..."
        ["ui.error"]="Error"
        ["ui.save"]="Save"
        ["ui.cancel"]="Cancel"
    )
    
    for key in "${!sample_keys[@]}"; do
        local value="${sample_keys[$key]}"
        echo -e "${YELLOW}⏳ Creating translation key: $key${NC}"
        
        # Create translation key
        local key_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/translation-keys" \
            -H "Authorization: Bearer $AUTH_TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"project_id\": \"$PROJECT_ID\",
                \"key\": \"$key\",
                \"description\": \"Translation for $key\",
                \"namespace\": \"$(echo $key | cut -d. -f1)\"
            }" 2>/dev/null || echo '{"success":false}')
        
        if echo "$key_response" | grep -q '"success":true'; then
            local key_id=$(echo "$key_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
            
            # Create English translation
            local translation_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/translations" \
                -H "Authorization: Bearer $AUTH_TOKEN" \
                -H "Content-Type: application/json" \
                -d "{
                    \"translation_key_id\": \"$key_id\",
                    \"locale\": \"en\",
                    \"value\": \"$value\"
                }" 2>/dev/null || echo '{"success":false}')
            
            if echo "$translation_response" | grep -q '"success":true'; then
                echo -e "${GREEN}  ✅ Created: $key = \"$value\"${NC}"
            else
                echo -e "${YELLOW}  ⚠️ Failed to create translation for: $key${NC}"
            fi
        else
            echo -e "${YELLOW}  ⚠️ Failed to create key: $key${NC}"
        fi
    done
}

# Function to update environment configuration
update_environment_config() {
    echo -e "${BLUE}⚙️ Updating environment configuration...${NC}"
    
    if [ ! -z "$PROJECT_ID" ]; then
        # Update .env.local.example if it exists
        if [ -f ".env.local.example" ]; then
            echo -e "${YELLOW}⏳ Updating .env.local.example...${NC}"
            sed -i.bak "s/NEXT_PUBLIC_CREDIT_PROJECT_ID=.*/NEXT_PUBLIC_CREDIT_PROJECT_ID=$PROJECT_ID/" .env.local.example
            echo -e "${GREEN}✅ Updated NEXT_PUBLIC_CREDIT_PROJECT_ID in .env.local.example${NC}"
        fi
        
        # Update .env.example if it exists
        if [ -f ".env.example" ]; then
            echo -e "${YELLOW}⏳ Updating .env.example...${NC}"
            sed -i.bak "s/NEXT_PUBLIC_CREDIT_PROJECT_ID=.*/NEXT_PUBLIC_CREDIT_PROJECT_ID=$PROJECT_ID/" .env.example
            echo -e "${GREEN}✅ Updated NEXT_PUBLIC_CREDIT_PROJECT_ID in .env.example${NC}"
        fi
        
        echo -e "${BLUE}📋 Project Configuration Summary:${NC}"
        echo -e "${GREEN}Project ID: $PROJECT_ID${NC}"
        echo -e "${GREEN}Project Slug: $PROJECT_SLUG${NC}"
        echo -e "${GREEN}Organization ID: $ORG_ID${NC}"
        echo ""
        echo -e "${YELLOW}💡 Add this to your .env.local file:${NC}"
        echo "NEXT_PUBLIC_CREDIT_PROJECT_ID=$PROJECT_ID"
        echo "NEXT_PUBLIC_MULTILANG_URL=http://localhost:8300"
    else
        echo -e "${YELLOW}⚠️ No project ID available for environment update${NC}"
    fi
}

# Function to test the integration
test_integration() {
    echo -e "${BLUE}🧪 Testing integration...${NC}"
    
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${YELLOW}⚠️ No project ID available for integration test${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}⏳ Testing translation fetch...${NC}"
    
    # Test internal translation fetch
    local test_response=$(curl -s -X POST "$MULTI_LANG_URL/api/v2/internal/translations/fetch" \
        -H "Content-Type: application/json" \
        -H "X-Internal-Key: adc-internal-2024" \
        -d "{
            \"project_id\": \"$PROJECT_ID\",
            \"namespace\": \"dashboard\",
            \"key\": \"title\",
            \"locale\": \"en\"
        }" 2>/dev/null || echo '{"success":false}')
    
    if echo "$test_response" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ Integration test successful!${NC}"
        echo "Response: $test_response"
    else
        echo -e "${YELLOW}⚠️ Integration test response:${NC}"
        echo "$test_response"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}🎯 ADC Credit i18n Project Setup${NC}"
    echo -e "${BLUE}=================================${NC}"
    
    # Step 1: Check service
    check_service
    
    # Step 2: Get authentication
    if get_auth_token; then
        echo -e "${GREEN}✅ Authentication obtained${NC}"
        
        # Step 3: Get or create organization
        if get_or_create_organization; then
            echo -e "${GREEN}✅ Organization ready${NC}"
            
            # Step 4: Get or create project
            if get_or_create_project; then
                echo -e "${GREEN}✅ Project ready${NC}"
                
                # Step 5: Setup locales
                setup_project_locales
                
                # Step 6: Create sample translations
                create_sample_translations
                
                # Step 7: Update environment
                update_environment_config
                
                # Step 8: Test integration
                test_integration
                
                echo -e "${GREEN}🎉 ADC Credit i18n project setup completed!${NC}"
            else
                echo -e "${RED}❌ Project setup failed${NC}"
            fi
        else
            echo -e "${RED}❌ Organization setup failed${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Authentication failed, creating minimal setup...${NC}"
        
        # Even without auth, we can provide instructions
        echo -e "${YELLOW}💡 Manual setup instructions:${NC}"
        echo "1. Authenticate with Multi-Languages service"
        echo "2. Create organization: ADC Platform"
        echo "3. Create project: ADC Credit Project (slug: adc-credit-project)"
        echo "4. Add locales: en, es, fr, de, pt, ja, ko, zh, th, ar, he"
        echo "5. Update NEXT_PUBLIC_CREDIT_PROJECT_ID in environment"
    fi
}

# Handle command line arguments
case "${1:-}" in
    "--help"|"-h")
        echo "ADC Credit i18n Project Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --test         Test existing integration"
        echo ""
        echo "This script will:"
        echo "  1. Check Multi-Languages service availability"
        echo "  2. Authenticate with the service"
        echo "  3. Create/find ADC Platform organization"
        echo "  4. Create/find ADC Credit project"
        echo "  5. Setup supported locales"
        echo "  6. Create sample translation keys"
        echo "  7. Update environment configuration"
        echo ""
        exit 0
        ;;
    "--test")
        check_service
        if [ ! -z "${NEXT_PUBLIC_CREDIT_PROJECT_ID:-}" ]; then
            PROJECT_ID="$NEXT_PUBLIC_CREDIT_PROJECT_ID"
            test_integration
        else
            echo -e "${YELLOW}⚠️ NEXT_PUBLIC_CREDIT_PROJECT_ID not set${NC}"
        fi
        exit 0
        ;;
    *)
        main
        ;;
esac