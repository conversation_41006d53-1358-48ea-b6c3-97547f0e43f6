#!/usr/bin/env node
/**
 * Performance Monitoring Script
 * ADC Credit Service - Continuous Performance Monitoring
 * 
 * Runs performance tests, generates reports, and monitors for regressions.
 * Can be used in CI/CD pipelines for automated performance monitoring.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  resultsDir: 'test-results/performance',
  baselineFile: 'performance-baseline.json',
  reportFile: 'performance-report.html',
  thresholds: {
    lcp: 2500,        // Largest Contentful Paint (ms)
    fid: 100,         // First Input Delay (ms)
    cls: 0.1,         // Cumulative Layout Shift
    fcp: 1800,        // First Contentful Paint (ms)
    loadTime: 5000,   // Page load time (ms)
    memoryUsage: 50,  // Memory usage (MB)
  },
  regressionThreshold: 10, // 10% regression threshold
};

class PerformanceMonitor {
  constructor() {
    this.resultsDir = path.join(process.cwd(), CONFIG.resultsDir);
    this.ensureDirectoryExists();
  }

  /**
   * Ensure results directory exists
   */
  ensureDirectoryExists() {
    if (!fs.existsSync(this.resultsDir)) {
      fs.mkdirSync(this.resultsDir, { recursive: true });
    }
  }

  /**
   * Run performance tests
   */
  async runPerformanceTests() {
    console.log('🚀 Starting performance tests...');
    
    try {
      // Run Playwright performance tests
      const testCommand = 'npx playwright test --config=tests/performance/performance.config.ts';
      console.log(`Running: ${testCommand}`);
      
      execSync(testCommand, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('✅ Performance tests completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Performance tests failed:', error.message);
      return false;
    }
  }

  /**
   * Parse test results
   */
  parseTestResults() {
    const resultsFile = path.join(this.resultsDir, 'performance-results.json');
    
    if (!fs.existsSync(resultsFile)) {
      console.warn('⚠️  No performance results file found');
      return null;
    }

    try {
      const rawResults = fs.readFileSync(resultsFile, 'utf8');
      const results = JSON.parse(rawResults);
      
      console.log('📊 Parsing performance results...');
      return this.extractMetrics(results);
    } catch (error) {
      console.error('❌ Failed to parse results:', error.message);
      return null;
    }
  }

  /**
   * Extract performance metrics from test results
   */
  extractMetrics(results) {
    const metrics = {
      timestamp: new Date().toISOString(),
      testSummary: {
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        duration: results.stats?.duration || 0,
      },
      pages: {},
      overall: {
        avgLoadTime: 0,
        maxLoadTime: 0,
        totalTests: 0,
        passedTests: 0,
      }
    };

    // Extract metrics from test results
    if (results.suites) {
      results.suites.forEach(suite => {
        if (suite.specs) {
          suite.specs.forEach(spec => {
            this.extractSpecMetrics(spec, metrics);
          });
        }
      });
    }

    // Calculate overall metrics
    this.calculateOverallMetrics(metrics);
    
    return metrics;
  }

  /**
   * Extract metrics from individual test spec
   */
  extractSpecMetrics(spec, metrics) {
    const specName = spec.title;
    
    // Parse console output for performance metrics
    if (spec.tests && spec.tests.length > 0) {
      spec.tests.forEach(test => {
        if (test.results && test.results.length > 0) {
          test.results.forEach(result => {
            if (result.stdout) {
              this.parseConsoleOutput(result.stdout, specName, metrics);
            }
          });
        }
      });
    }
  }

  /**
   * Parse console output for performance metrics
   */
  parseConsoleOutput(stdout, testName, metrics) {
    const lines = stdout.split('\n');
    
    lines.forEach(line => {
      // Parse different metric types
      if (line.includes('load time:')) {
        const match = line.match(/(\w+)\s+load time:\s*(\d+)ms/);
        if (match) {
          const [, page, time] = match;
          if (!metrics.pages[page]) metrics.pages[page] = {};
          metrics.pages[page].loadTime = parseInt(time);
        }
      }
      
      if (line.includes('LCP:')) {
        const match = line.match(/LCP:\s*(\d+\.?\d*)ms/);
        if (match) {
          const lcp = parseFloat(match[1]);
          this.addPageMetric(testName, 'lcp', lcp, metrics);
        }
      }
      
      if (line.includes('FCP:')) {
        const match = line.match(/FCP:\s*(\d+\.?\d*)ms/);
        if (match) {
          const fcp = parseFloat(match[1]);
          this.addPageMetric(testName, 'fcp', fcp, metrics);
        }
      }
      
      if (line.includes('CLS:')) {
        const match = line.match(/CLS:\s*(\d+\.?\d*)/);
        if (match) {
          const cls = parseFloat(match[1]);
          this.addPageMetric(testName, 'cls', cls, metrics);
        }
      }
      
      if (line.includes('Memory usage:')) {
        const match = line.match(/Memory usage:\s*(\d+\.?\d*)MB/);
        if (match) {
          const memory = parseFloat(match[1]);
          this.addPageMetric(testName, 'memory', memory, metrics);
        }
      }
    });
  }

  /**
   * Add metric to page metrics
   */
  addPageMetric(testName, metricName, value, metrics) {
    const pageName = this.extractPageName(testName);
    if (!metrics.pages[pageName]) {
      metrics.pages[pageName] = {};
    }
    metrics.pages[pageName][metricName] = value;
  }

  /**
   * Extract page name from test name
   */
  extractPageName(testName) {
    if (testName.includes('dashboard')) return 'dashboard';
    if (testName.includes('shops')) return 'shops';
    if (testName.includes('api-keys')) return 'apiKeys';
    if (testName.includes('customer')) return 'customer';
    if (testName.includes('subscriptions')) return 'subscriptions';
    return 'unknown';
  }

  /**
   * Calculate overall performance metrics
   */
  calculateOverallMetrics(metrics) {
    const pages = Object.values(metrics.pages);
    
    if (pages.length > 0) {
      const loadTimes = pages.map(p => p.loadTime).filter(Boolean);
      metrics.overall.avgLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
      metrics.overall.maxLoadTime = Math.max(...loadTimes);
    }
    
    metrics.overall.totalTests = metrics.testSummary.total;
    metrics.overall.passedTests = metrics.testSummary.passed;
    metrics.overall.successRate = (metrics.overall.passedTests / metrics.overall.totalTests * 100).toFixed(2);
  }

  /**
   * Load performance baseline
   */
  loadBaseline() {
    const baselineFile = path.join(this.resultsDir, CONFIG.baselineFile);
    
    if (!fs.existsSync(baselineFile)) {
      console.log('📝 No baseline found, current results will be used as baseline');
      return null;
    }

    try {
      const baseline = JSON.parse(fs.readFileSync(baselineFile, 'utf8'));
      console.log(`📊 Loaded baseline from ${baseline.timestamp}`);
      return baseline;
    } catch (error) {
      console.error('❌ Failed to load baseline:', error.message);
      return null;
    }
  }

  /**
   * Save performance baseline
   */
  saveBaseline(metrics) {
    const baselineFile = path.join(this.resultsDir, CONFIG.baselineFile);
    
    try {
      fs.writeFileSync(baselineFile, JSON.stringify(metrics, null, 2));
      console.log('💾 Performance baseline saved');
    } catch (error) {
      console.error('❌ Failed to save baseline:', error.message);
    }
  }

  /**
   * Compare metrics against baseline
   */
  compareToBaseline(currentMetrics, baseline) {
    if (!baseline) return null;

    const comparison = {
      timestamp: new Date().toISOString(),
      regressions: [],
      improvements: [],
      summary: {
        hasRegressions: false,
        totalRegressions: 0,
        totalImprovements: 0,
      }
    };

    // Compare page metrics
    Object.keys(currentMetrics.pages).forEach(pageName => {
      const current = currentMetrics.pages[pageName];
      const baselinePage = baseline.pages[pageName];
      
      if (baselinePage) {
        this.comparePageMetrics(pageName, current, baselinePage, comparison);
      }
    });

    // Update summary
    comparison.summary.hasRegressions = comparison.regressions.length > 0;
    comparison.summary.totalRegressions = comparison.regressions.length;
    comparison.summary.totalImprovements = comparison.improvements.length;

    return comparison;
  }

  /**
   * Compare individual page metrics
   */
  comparePageMetrics(pageName, current, baseline, comparison) {
    const metrics = ['loadTime', 'lcp', 'fcp', 'cls', 'memory'];
    
    metrics.forEach(metric => {
      if (current[metric] && baseline[metric]) {
        const currentValue = current[metric];
        const baselineValue = baseline[metric];
        const changePercent = ((currentValue - baselineValue) / baselineValue) * 100;
        
        if (Math.abs(changePercent) > CONFIG.regressionThreshold) {
          const change = {
            page: pageName,
            metric,
            current: currentValue,
            baseline: baselineValue,
            changePercent: changePercent.toFixed(2),
            isRegression: changePercent > 0,
          };
          
          if (changePercent > 0) {
            comparison.regressions.push(change);
          } else {
            comparison.improvements.push(change);
          }
        }
      }
    });
  }

  /**
   * Generate HTML performance report
   */
  generateReport(metrics, comparison) {
    const reportHtml = this.createReportHTML(metrics, comparison);
    const reportFile = path.join(this.resultsDir, CONFIG.reportFile);
    
    try {
      fs.writeFileSync(reportFile, reportHtml);
      console.log(`📄 Performance report generated: ${reportFile}`);
    } catch (error) {
      console.error('❌ Failed to generate report:', error.message);
    }
  }

  /**
   * Create HTML report content
   */
  createReportHTML(metrics, comparison) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADC Credit Service - Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .metric { display: inline-block; margin: 10px 15px 10px 0; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        .good { border-left: 4px solid #4CAF50; }
        .warning { border-left: 4px solid #FF9800; }
        .poor { border-left: 4px solid #F44336; }
        .page-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .regression { background: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 5px 0; }
        .improvement { background: #e8f5e8; border-left: 4px solid #4caf50; padding: 10px; margin: 5px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>ADC Credit Service - Performance Report</h1>
        <p><strong>Generated:</strong> ${metrics.timestamp}</p>
        <p><strong>Test Summary:</strong> ${metrics.testSummary.passed}/${metrics.testSummary.total} tests passed</p>
        <p><strong>Success Rate:</strong> ${metrics.overall.successRate}%</p>
    </div>

    <h2>📊 Overall Performance Metrics</h2>
    <div class="metric ${metrics.overall.avgLoadTime < 3000 ? 'good' : metrics.overall.avgLoadTime < 5000 ? 'warning' : 'poor'}">
        <strong>Average Load Time:</strong> ${metrics.overall.avgLoadTime?.toFixed(0) || 'N/A'}ms
    </div>
    <div class="metric ${metrics.overall.maxLoadTime < 5000 ? 'good' : metrics.overall.maxLoadTime < 8000 ? 'warning' : 'poor'}">
        <strong>Max Load Time:</strong> ${metrics.overall.maxLoadTime?.toFixed(0) || 'N/A'}ms
    </div>

    <h2>📄 Page Performance Details</h2>
    ${Object.entries(metrics.pages).map(([pageName, pageMetrics]) => `
    <div class="page-section">
        <h3>${pageName.charAt(0).toUpperCase() + pageName.slice(1)} Page</h3>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Threshold</th>
                <th>Status</th>
            </tr>
            ${pageMetrics.loadTime ? `
            <tr>
                <td>Load Time</td>
                <td>${pageMetrics.loadTime}ms</td>
                <td>${CONFIG.thresholds.loadTime}ms</td>
                <td>${pageMetrics.loadTime < CONFIG.thresholds.loadTime ? '✅ Good' : '⚠️ Needs Improvement'}</td>
            </tr>
            ` : ''}
            ${pageMetrics.lcp ? `
            <tr>
                <td>LCP</td>
                <td>${pageMetrics.lcp}ms</td>
                <td>${CONFIG.thresholds.lcp}ms</td>
                <td>${pageMetrics.lcp < CONFIG.thresholds.lcp ? '✅ Good' : '⚠️ Needs Improvement'}</td>
            </tr>
            ` : ''}
            ${pageMetrics.fcp ? `
            <tr>
                <td>FCP</td>
                <td>${pageMetrics.fcp}ms</td>
                <td>${CONFIG.thresholds.fcp}ms</td>
                <td>${pageMetrics.fcp < CONFIG.thresholds.fcp ? '✅ Good' : '⚠️ Needs Improvement'}</td>
            </tr>
            ` : ''}
            ${pageMetrics.cls ? `
            <tr>
                <td>CLS</td>
                <td>${pageMetrics.cls}</td>
                <td>${CONFIG.thresholds.cls}</td>
                <td>${pageMetrics.cls < CONFIG.thresholds.cls ? '✅ Good' : '⚠️ Needs Improvement'}</td>
            </tr>
            ` : ''}
            ${pageMetrics.memory ? `
            <tr>
                <td>Memory Usage</td>
                <td>${pageMetrics.memory}MB</td>
                <td>${CONFIG.thresholds.memoryUsage}MB</td>
                <td>${pageMetrics.memory < CONFIG.thresholds.memoryUsage ? '✅ Good' : '⚠️ High Usage'}</td>
            </tr>
            ` : ''}
        </table>
    </div>
    `).join('')}

    ${comparison ? `
    <h2>📈 Performance Comparison</h2>
    ${comparison.regressions.length > 0 ? `
    <h3>❌ Performance Regressions</h3>
    ${comparison.regressions.map(reg => `
    <div class="regression">
        <strong>${reg.page} - ${reg.metric}:</strong> 
        ${reg.current} (${reg.changePercent}% worse than baseline: ${reg.baseline})
    </div>
    `).join('')}
    ` : ''}
    
    ${comparison.improvements.length > 0 ? `
    <h3>✅ Performance Improvements</h3>
    ${comparison.improvements.map(imp => `
    <div class="improvement">
        <strong>${imp.page} - ${imp.metric}:</strong> 
        ${imp.current} (${Math.abs(parseFloat(imp.changePercent))}% better than baseline: ${imp.baseline})
    </div>
    `).join('')}
    ` : ''}
    
    ${comparison.regressions.length === 0 && comparison.improvements.length === 0 ? `
    <p>✅ No significant performance changes detected compared to baseline.</p>
    ` : ''}
    ` : ''}

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
        <p>Generated by ADC Credit Service Performance Monitor</p>
    </footer>
</body>
</html>
    `;
  }

  /**
   * Run complete performance monitoring workflow
   */
  async run() {
    console.log('🎯 Starting ADC Credit Service Performance Monitor\n');

    // Run performance tests
    const testSuccess = await this.runPerformanceTests();
    if (!testSuccess) {
      process.exit(1);
    }

    // Parse results
    const metrics = this.parseTestResults();
    if (!metrics) {
      console.error('❌ No performance metrics available');
      process.exit(1);
    }

    // Load baseline and compare
    const baseline = this.loadBaseline();
    const comparison = this.compareToBaseline(metrics, baseline);

    // Save baseline if none exists
    if (!baseline) {
      this.saveBaseline(metrics);
    }

    // Generate report
    this.generateReport(metrics, comparison);

    // Print summary
    this.printSummary(metrics, comparison);

    // Exit with appropriate code
    if (comparison && comparison.summary.hasRegressions) {
      console.log('\n❌ Performance regressions detected!');
      process.exit(1);
    } else {
      console.log('\n✅ Performance monitoring completed successfully');
      process.exit(0);
    }
  }

  /**
   * Print performance summary to console
   */
  printSummary(metrics, comparison) {
    console.log('\n📊 Performance Summary:');
    console.log(`   Tests: ${metrics.testSummary.passed}/${metrics.testSummary.total} passed`);
    console.log(`   Average Load Time: ${metrics.overall.avgLoadTime?.toFixed(0) || 'N/A'}ms`);
    console.log(`   Max Load Time: ${metrics.overall.maxLoadTime?.toFixed(0) || 'N/A'}ms`);
    
    if (comparison) {
      console.log(`   Regressions: ${comparison.summary.totalRegressions}`);
      console.log(`   Improvements: ${comparison.summary.totalImprovements}`);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const monitor = new PerformanceMonitor();
  monitor.run().catch(error => {
    console.error('❌ Performance monitor failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceMonitor;