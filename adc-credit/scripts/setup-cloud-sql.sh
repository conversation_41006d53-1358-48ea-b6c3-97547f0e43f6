#!/bin/bash

# ADC Credit System - Cloud SQL Setup Script
# This script sets up the production Cloud SQL database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
INSTANCE_NAME="adc-credit-prod"
DATABASE_NAME="adc_credit_prod"
USERNAME="adc_prod_user"

# Generate secure password
GENERATED_PASSWORD=$(openssl rand -base64 32)

print_status "Setting up Cloud SQL database for production..."

# Check if instance is running
print_status "Checking Cloud SQL instance status..."
INSTANCE_STATE=$(gcloud sql instances describe $INSTANCE_NAME --format="value(state)" 2>/dev/null || echo "NOT_FOUND")

if [ "$INSTANCE_STATE" == "NOT_FOUND" ]; then
    print_error "Cloud SQL instance '$INSTANCE_NAME' not found. Please create it first."
    exit 1
elif [ "$INSTANCE_STATE" == "PENDING_CREATE" ]; then
    print_warning "Cloud SQL instance is still being created. Please wait and try again."
    exit 1
elif [ "$INSTANCE_STATE" != "RUNNABLE" ]; then
    print_error "Cloud SQL instance is not in RUNNABLE state. Current state: $INSTANCE_STATE"
    exit 1
fi

print_status "Cloud SQL instance is ready!"

# Create database
print_status "Creating production database '$DATABASE_NAME'..."
if gcloud sql databases create $DATABASE_NAME --instance=$INSTANCE_NAME 2>/dev/null; then
    print_status "Database '$DATABASE_NAME' created successfully!"
else
    print_warning "Database '$DATABASE_NAME' may already exist, continuing..."
fi

# Create user
print_status "Creating production user '$USERNAME'..."
if gcloud sql users create $USERNAME --instance=$INSTANCE_NAME --password="$GENERATED_PASSWORD" 2>/dev/null; then
    print_status "User '$USERNAME' created successfully!"
else
    print_warning "User '$USERNAME' may already exist, updating password..."
    gcloud sql users set-password $USERNAME --instance=$INSTANCE_NAME --password="$GENERATED_PASSWORD"
fi

# Get connection details
CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME --format="value(connectionName)")
PUBLIC_IP=$(gcloud sql instances describe $INSTANCE_NAME --format="value(ipAddresses[0].ipAddress)")

print_status "Cloud SQL setup completed!"
echo ""
echo "=== Connection Details ==="
echo "Instance Connection Name: $CONNECTION_NAME"
echo "Public IP: $PUBLIC_IP"
echo "Database: $DATABASE_NAME"
echo "Username: $USERNAME"
echo "Password: $GENERATED_PASSWORD"
echo ""
echo "=== Database URL for .env.prod ==="
echo "DATABASE_URL=******************************************************************************************"
echo ""
echo "=== Next Steps ==="
echo "1. Update backend/.env.prod with the DATABASE_URL above"
echo "2. Update .env.prod with your production domain URLs"
echo "3. Generate secure JWT secrets for production"
echo "4. Configure production Google OAuth credentials"
echo "5. Set up production Stripe keys"
echo "6. Run 'make deploy-prod' to deploy to production"
echo ""
print_warning "Please save the password securely - it will not be shown again!"