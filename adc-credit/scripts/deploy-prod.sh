#!/bin/bash

# ADC Credit System - Production Deployment Script
# This script deploys the application to production using GCP Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required environment variables are set
check_env_vars() {
    print_status "Checking required environment variables..."
    
    local required_vars=(
        "PROJECT_ID"
        "DATABASE_URL" 
        "JWT_SECRET"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
        "FRONTEND_URL"
        "NEXTAUTH_SECRET"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "The following required environment variables are not set:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "Please set these variables before running the deployment script."
        exit 1
    fi
    
    print_status "All required environment variables are set."
}

# Deploy backend to Cloud Run
deploy_backend() {
    print_status "Deploying backend to Cloud Run..."
    
    gcloud run deploy adc-credit-backend-prod \
        --source . \
        --platform managed \
        --region us-central1 \
        --allow-unauthenticated \
        --port 8400 \
        --memory 512Mi \
        --cpu 1 \
        --max-instances 10 \
        --set-env-vars "DATABASE_URL=$DATABASE_URL,JWT_SECRET=$JWT_SECRET,GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET,FRONTEND_URL=$FRONTEND_URL,ENVIRONMENT=production,GIN_MODE=release" \
        --project $PROJECT_ID
        
    print_status "Backend deployed successfully!"
}

# Deploy frontend to Cloud Run
deploy_frontend() {
    print_status "Deploying frontend to Cloud Run..."
    
    # Get the backend URL from the deployed service
    BACKEND_URL=$(gcloud run services describe adc-credit-backend-prod --region=us-central1 --format="value(status.url)" --project=$PROJECT_ID)
    
    gcloud run deploy adc-credit-frontend-prod \
        --source . \
        --platform managed \
        --region us-central1 \
        --allow-unauthenticated \
        --port 3800 \
        --memory 512Mi \
        --cpu 1 \
        --max-instances 10 \
        --set-env-vars "NEXT_PUBLIC_BACKEND_URL=$BACKEND_URL,BACKEND_URL=$BACKEND_URL,NEXTAUTH_URL=$FRONTEND_URL,NEXTAUTH_SECRET=$NEXTAUTH_SECRET,GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET,NODE_ENV=production" \
        --project $PROJECT_ID
        
    print_status "Frontend deployed successfully!"
}

# Deploy scheduled credits function
deploy_scheduled_credits() {
    print_status "Deploying scheduled credits function..."
    
    if [ -z "$SCHEDULER_API_KEY" ]; then
        print_warning "SCHEDULER_API_KEY not set, skipping scheduled credits deployment"
        return
    fi
    
    cd backend && ./scripts/deploy_scheduled_credits.sh
    cd ..
    
    print_status "Scheduled credits function deployed successfully!"
}

# Main deployment function
main() {
    print_status "Starting production deployment..."
    
    # Load environment variables from .env.prod if it exists
    if [ -f ".env.prod" ]; then
        print_status "Loading environment variables from .env.prod..."
        set -a
        source .env.prod
        set +a
    fi
    
    # Check environment variables
    check_env_vars
    
    # Deploy components
    deploy_backend
    deploy_frontend
    deploy_scheduled_credits
    
    print_status "Production deployment completed successfully!"
    print_status "Backend URL: $(gcloud run services describe adc-credit-backend-prod --region=us-central1 --format="value(status.url)" --project=$PROJECT_ID)"
    print_status "Frontend URL: $(gcloud run services describe adc-credit-frontend-prod --region=us-central1 --format="value(status.url)" --project=$PROJECT_ID)"
}

# Run main function
main "$@"