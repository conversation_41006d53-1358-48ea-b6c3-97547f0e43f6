# Adding Test IDs to ADC Credit Components

Your application has comprehensive test IDs defined in `/src/lib/test-ids.ts` but they're not actually used in your components yet. Here's how to add them:

## ✅ Verified Issues

**Current State:**
- ✅ Test IDs are properly defined in `test-ids.ts`
- ❌ Components don't use `data-testid` attributes
- ❌ E2E tests rely on HTML structure instead of stable selectors

## 🔧 How to Add Test IDs

### 1. Cookies Page (`/src/app/cookies/page.tsx`)

Add these test IDs to your cookies page:

```tsx
// Add import
import { CREDIT_SERVICE_TEST_IDS } from '@/lib/test-ids';

// Update your switches with test IDs
<Switch
  checked={preferences.essential}
  disabled={true}
  data-testid="cookie-essential-toggle"
  aria-label="Essential cookies (always enabled)"
/>

<Switch
  checked={preferences.functional}
  onCheckedChange={(checked) => updatePreference("functional", checked)}
  data-testid="cookie-functional-toggle"
  aria-label="Functional cookies"
/>

<Switch
  checked={preferences.analytics}
  onCheckedChange={(checked) => updatePreference("analytics", checked)}
  data-testid="cookie-analytics-toggle"
  aria-label="Analytics cookies"
/>

<Switch
  checked={preferences.marketing}
  onCheckedChange={(checked) => updatePreference("marketing", checked)}
  data-testid="cookie-marketing-toggle"
  aria-label="Marketing cookies"
/>

// Update your buttons
<Button onClick={savePreferences} data-testid="cookie-save-button">
  Save Preferences
</Button>

<Button variant="outline" onClick={acceptAll} data-testid="cookie-accept-all-button">
  Accept All Cookies
</Button>

<Button variant="outline" onClick={resetToEssential} data-testid="cookie-essential-only-button">
  Essential Only
</Button>
```

### 2. Dashboard Page

```tsx
// Import test IDs
import { CREDIT_SERVICE_TEST_IDS } from '@/lib/test-ids';

// Add to main container
<div data-testid={CREDIT_SERVICE_TEST_IDS.DASHBOARD.CONTAINER}>
  
  // Add to balance card
  <Card data-testid={CREDIT_SERVICE_TEST_IDS.DASHBOARD.BALANCE_CARD}>
    <CardContent>
      <div data-testid="credit-balance-amount">{balance}</div>
    </CardContent>
  </Card>
  
  // Add to shops card
  <Card data-testid={CREDIT_SERVICE_TEST_IDS.DASHBOARD.SHOPS_CARD}>
    // Shop content
  </Card>
  
</div>
```

### 3. Shops Page

```tsx
import { CREDIT_SERVICE_TEST_IDS } from '@/lib/test-ids';

<div data-testid={CREDIT_SERVICE_TEST_IDS.SHOPS.LIST_CONTAINER}>
  <Button data-testid={CREDIT_SERVICE_TEST_IDS.SHOPS.CREATE_BUTTON}>
    Create Shop
  </Button>
  
  {shops.map(shop => (
    <div key={shop.id} data-testid={CREDIT_SERVICE_TEST_IDS.SHOPS.SHOP_CARD(shop.slug)}>
      <Button data-testid={CREDIT_SERVICE_TEST_IDS.SHOPS.SHOP_SETTINGS_BUTTON(shop.slug)}>
        Settings
      </Button>
    </div>
  ))}
</div>
```

### 4. QR Code Components

```tsx
import { CREDIT_SERVICE_TEST_IDS } from '@/lib/test-ids';

<div data-testid={CREDIT_SERVICE_TEST_IDS.QR_CODES.GENERATOR_CONTAINER}>
  <input 
    data-testid={CREDIT_SERVICE_TEST_IDS.QR_CODES.AMOUNT_INPUT}
    type="number"
    placeholder="Credit amount"
  />
  
  <Button data-testid={CREDIT_SERVICE_TEST_IDS.QR_CODES.GENERATE_BUTTON}>
    Generate QR Code
  </Button>
  
  <div data-testid={CREDIT_SERVICE_TEST_IDS.QR_CODES.QR_CODE_DISPLAY}>
    {/* QR Code display */}
  </div>
  
  <Button data-testid={CREDIT_SERVICE_TEST_IDS.QR_CODES.DOWNLOAD_BUTTON}>
    Download
  </Button>
</div>
```

## 🧪 Updated E2E Tests

Once you add test IDs, you can use more reliable selectors:

```typescript
// Instead of this (fragile):
await page.locator('button:has-text("Save Preferences")').click();

// Use this (stable):
await page.locator('[data-testid="cookie-save-button"]').click();

// Instead of this (fragile):
const switches = page.locator('[role="switch"]');
await switches.nth(1).click();

// Use this (stable):
await page.locator('[data-testid="cookie-functional-toggle"]').click();
```

## 📋 Component Checklist

Add test IDs to these components in priority order:

### High Priority (Core Functionality)
- [ ] `/src/app/cookies/page.tsx` - Cookie preferences
- [ ] `/src/app/dashboard/page.tsx` - Main dashboard
- [ ] Shop management components
- [ ] Credit balance components
- [ ] QR code generator

### Medium Priority (User Features)
- [ ] Settings pages
- [ ] API key management
- [ ] Transaction history
- [ ] Analytics components

### Low Priority (Admin Features)
- [ ] Admin settings
- [ ] Global configuration
- [ ] System monitoring

## 🔄 Migration Strategy

### Phase 1: Critical Components
1. Add test IDs to cookies page
2. Add test IDs to dashboard
3. Update E2E tests to use stable selectors

### Phase 2: Core Features
1. Add test IDs to shop management
2. Add test IDs to credit components
3. Add test IDs to QR code features

### Phase 3: Complete Coverage
1. Add remaining test IDs
2. Update all E2E tests
3. Add component testing

## 🎯 Benefits of Adding Test IDs

**Current Problems:**
- Tests break when UI text changes
- Tests break when DOM structure changes
- Tests are fragile and hard to maintain
- Difficult to target specific elements

**After Adding Test IDs:**
- ✅ Stable test selectors that don't break
- ✅ Faster test execution (precise targeting)
- ✅ Better test maintainability
- ✅ Clear intent in test code
- ✅ Easier debugging when tests fail

## 🚀 Quick Start

**For immediate testing**, use the verified E2E tests that work with your current HTML structure:

```bash
# Run the verified tests (work with current structure)
npx playwright test pages-verified.spec.ts
npx playwright test authenticated-verified.spec.ts

# These tests adapt to your actual implementation
# and don't rely on non-existent test IDs
```

**For long-term stability**, gradually add test IDs to your components using the examples above.