steps:
  # Build the frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--target=frontend-prod'
      - '-t'
      - 'gcr.io/${PROJECT_ID}/adc-credit-frontend-prod:${BUILD_ID}'
      - '.'

  # Push the image to GCR
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/${PROJECT_ID}/adc-credit-frontend-prod:${BUILD_ID}'

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'adc-credit-frontend-prod'
      - '--image'
      - 'gcr.io/${PROJECT_ID}/adc-credit-frontend-prod:${BUILD_ID}'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '3800'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--timeout'
      - '300'
      - '--set-env-vars'
      - 'NEXT_PUBLIC_BACKEND_URL=https://api.credit.adcshop.store,BACKEND_URL=https://api.credit.adcshop.store,NEXTAUTH_URL=https://credit.adcshop.store,NEXTAUTH_SECRET=jNDICn5z32P6MVIEF3ZJWvLukmi3RErThHSausOqnAo=,GOOGLE_CLIENT_ID=457647006078-urjli7nlssmv9tjd9bo5uddmi4hdn0ej.apps.googleusercontent.com,GOOGLE_CLIENT_SECRET=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,NODE_ENV=production'

images:
  - 'gcr.io/${PROJECT_ID}/adc-credit-frontend-prod:${BUILD_ID}'