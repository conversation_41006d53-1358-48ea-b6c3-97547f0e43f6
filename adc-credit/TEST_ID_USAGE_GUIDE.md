# Test ID Usage Guide
## ADC Credit Service - Developer Quick Reference

### 🚀 Quick Start

#### 1. Import Test IDs
```typescript
import { 
  CUSTOMER_SCAN_TEST_IDS,
  SHOPS_TEST_IDS,
  WEBHOOKS_TEST_IDS 
} from '@/lib/test-ids';
```

#### 2. Add to Components
```typescript
export function ShopCard({ shop }: { shop: Shop }) {
  return (
    <div data-testid={SHOPS_TEST_IDS.SHOP_CARD(shop.id)}>
      <h3 data-testid={SHOPS_TEST_IDS.SHOP_NAME(shop.id)}>
        {shop.name}
      </h3>
      <button data-testid={SHOPS_TEST_IDS.SHOP_EDIT_BUTTON(shop.id)}>
        Edit Shop
      </button>
    </div>
  );
}
```

#### 3. Write Tests
```typescript
test('should display shop information', async ({ page }) => {
  await expect(page.locator('[data-testid="shops-shop-card-shop-123"]')).toBeVisible();
  await expect(page.locator('[data-testid="shops-shop-name-shop-123"]')).toHaveText('Coffee Shop');
  await page.locator('[data-testid="shops-shop-edit-button-shop-123"]').click();
});
```

### 📚 Available Test ID Sections

#### Core Layout & Navigation
```typescript
ROOT_LAYOUT_TEST_IDS         // Main app layout, navigation
DASHBOARD_LAYOUT_TEST_IDS    // Dashboard structure
AUTH_TEST_IDS               // Login, registration flows
LANDING_TEST_IDS            // Landing page components
```

#### Business Features
```typescript
SETTINGS_TEST_IDS           // User/app settings
WEBHOOKS_TEST_IDS          // Webhook management
MERCHANT_DASHBOARD_TEST_IDS // Business dashboard
CREDIT_SERVICE_TEST_IDS    // Credit management
CREDIT_DYNAMIC_TEST_IDS    // Dynamic credit content
```

#### Customer Experience
```typescript
CUSTOMER_SCAN_TEST_IDS     // QR code scanning
CUSTOMER_SHOPS_TEST_IDS    // Shop browsing
CUSTOMER_REDEEM_TEST_IDS   // Manual code redemption
```

#### Static Pages
```typescript
TERMS_TEST_IDS            // Terms of service
PRIVACY_TEST_IDS          // Privacy policy
PRICING_TEST_IDS          // Pricing plans
```

#### Landing Components
```typescript
HERO_SECTION_TEST_IDS     // Hero section
FEATURES_SECTION_TEST_IDS // Features grid
CTA_SECTION_TEST_IDS      // Call-to-action
```

### 🎯 Naming Conventions

#### Standard Pattern
```
{FEATURE}_{COMPONENT}_{ACTION}
```

Examples:
- `customer-scan-start-button`
- `shops-create-modal-title`
- `webhooks-test-result-success`

#### Dynamic Content Pattern
```typescript
// Function-based for data-driven content
SHOPS_TEST_IDS.SHOP_CARD(shopId)
// Generates: "shops-shop-card-{shopId}"

CUSTOMERS_TEST_IDS.CUSTOMER_ROW(customerId)
// Generates: "customers-table-row-{customerId}"
```

### 🔧 Common Patterns

#### Forms and Inputs
```typescript
// Form structure
FORM_CONTAINER: 'feature-form-container',
FORM_TITLE: 'feature-form-title',
FORM_FIELDS: 'feature-form-fields',

// Input fields
EMAIL_INPUT: 'feature-email-input',
EMAIL_LABEL: 'feature-email-label',
EMAIL_ERROR: 'feature-email-error',

// Actions
SUBMIT_BUTTON: 'feature-submit-button',
CANCEL_BUTTON: 'feature-cancel-button',
RESET_BUTTON: 'feature-reset-button'
```

#### Tables and Lists
```typescript
// Table structure
TABLE: 'feature-table',
TABLE_HEADER: 'feature-table-header',
TABLE_BODY: 'feature-table-body',

// Dynamic rows
TABLE_ROW: (id: string) => `feature-table-row-${id}`,
TABLE_CELL: (id: string, field: string) => `feature-table-${field}-${id}`,

// Actions
VIEW_BUTTON: (id: string) => `feature-table-view-button-${id}`,
EDIT_BUTTON: (id: string) => `feature-table-edit-button-${id}`,
DELETE_BUTTON: (id: string) => `feature-table-delete-button-${id}`
```

#### Modals and Dialogs
```typescript
// Modal structure
MODAL: 'feature-modal',
MODAL_OVERLAY: 'feature-modal-overlay',
MODAL_CONTENT: 'feature-modal-content',
MODAL_TITLE: 'feature-modal-title',
MODAL_CLOSE_BUTTON: 'feature-modal-close-button',

// Modal-specific content
MODAL_FORM: 'feature-modal-form',
MODAL_CONFIRM_BUTTON: 'feature-modal-confirm-button',
MODAL_CANCEL_BUTTON: 'feature-modal-cancel-button'
```

#### Loading and Error States
```typescript
// States
LOADING_STATE: 'feature-loading-state',
LOADING_SPINNER: 'feature-loading-spinner',
EMPTY_STATE: 'feature-empty-state',
ERROR_STATE: 'feature-error-state',

// Error handling
ERROR_MESSAGE: 'feature-error-message',
RETRY_BUTTON: 'feature-retry-button',
ERROR_DETAILS: 'feature-error-details'
```

### 📱 Responsive Design Testing

#### Viewport-Specific Elements
```typescript
// Mobile-specific elements
MOBILE_NAV: 'feature-mobile-nav',
MOBILE_MENU_BUTTON: 'feature-mobile-menu-button',
MOBILE_DRAWER: 'feature-mobile-drawer',

// Desktop-specific elements
DESKTOP_NAV: 'feature-desktop-nav',
SIDEBAR: 'feature-desktop-sidebar'
```

#### Testing Pattern
```typescript
test('should work on mobile', async ({ page }) => {
  await page.setViewportSize({ width: 375, height: 667 });
  await expect(page.locator('[data-testid="feature-mobile-nav"]')).toBeVisible();
  await expect(page.locator('[data-testid="feature-desktop-nav"]')).not.toBeVisible();
});
```

### 🧪 Test Writing Best Practices

#### Test Structure
```typescript
test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/feature-url');
  });

  test('should perform main action', async ({ page }) => {
    // Verify initial state
    await expect(page.locator('[data-testid="feature-container"]')).toBeVisible();
    
    // Perform action
    await page.locator('[data-testid="feature-action-button"]').click();
    
    // Verify result
    await expect(page.locator('[data-testid="feature-success-message"]')).toBeVisible();
  });
});
```

#### Error Handling Tests
```typescript
test('should handle API errors', async ({ page }) => {
  // Mock error response
  await page.route('**/api/endpoint', async (route) => {
    await route.fulfill({ 
      status: 500, 
      json: { error: 'Server error' } 
    });
  });
  
  // Trigger action that calls API
  await page.locator('[data-testid="feature-submit-button"]').click();
  
  // Verify error handling
  await expect(page.locator('[data-testid="feature-error-state"]')).toBeVisible();
  await expect(page.locator('[data-testid="feature-retry-button"]')).toBeVisible();
});
```

#### Dynamic Content Testing
```typescript
test('should handle dynamic shop list', async ({ page }) => {
  // Mock data
  const shops = [
    { id: 'shop-1', name: 'Coffee Shop' },
    { id: 'shop-2', name: 'Pizza Place' }
  ];
  
  await page.route('**/api/shops', async (route) => {
    await route.fulfill({ json: shops });
  });
  
  await page.reload();
  
  // Test each shop card
  for (const shop of shops) {
    await expect(
      page.locator(`[data-testid="${SHOPS_TEST_IDS.SHOP_CARD(shop.id)}"]`)
    ).toBeVisible();
    
    await expect(
      page.locator(`[data-testid="${SHOPS_TEST_IDS.SHOP_NAME(shop.id)}"]`)
    ).toHaveText(shop.name);
  }
});
```

### 🔍 Debugging Tips

#### Finding Test IDs
```bash
# Search for specific test IDs
grep -r "data-testid" src/
grep -r "SHOP_CARD" src/lib/test-ids.ts

# List all test IDs in a section
grep -A 50 "SHOPS_TEST_IDS" src/lib/test-ids.ts
```

#### Test Debugging
```typescript
// Log current test IDs for debugging
test('debug test IDs', async ({ page }) => {
  const elements = await page.locator('[data-testid]').all();
  for (const element of elements) {
    const testId = await element.getAttribute('data-testid');
    console.log('Found test ID:', testId);
  }
});
```

#### Playwright Inspector
```bash
# Debug tests interactively
npx playwright test --debug
npx playwright test --headed --slow-mo=1000
```

### 📊 Performance Guidelines

#### Efficient Selectors
```typescript
// Good - specific test ID
page.locator('[data-testid="shops-create-button"]')

// Avoid - generic selectors
page.locator('button').filter({ hasText: 'Create' })

// Good - dynamic with template
page.locator(`[data-testid="${SHOPS_TEST_IDS.SHOP_CARD(shopId)}"]`)
```

#### Batch Operations
```typescript
// Efficient - batch expectations
const container = page.locator('[data-testid="shops-container"]');
await expect(container).toBeVisible();
await expect(container.locator('[data-testid="shops-title"]')).toHaveText('My Shops');
await expect(container.locator('[data-testid="shops-create-button"]')).toBeVisible();
```

### 🚨 Common Mistakes to Avoid

#### ❌ Don't Use Dynamic Strings Directly
```typescript
// Bad
<div data-testid={`shop-card-${shop.id}`}>

// Good
<div data-testid={SHOPS_TEST_IDS.SHOP_CARD(shop.id)}>
```

#### ❌ Don't Mix Selectors
```typescript
// Bad - mixing test IDs with other selectors
page.locator('[data-testid="shops-container"] .shop-card')

// Good - use test IDs consistently
page.locator('[data-testid="shops-shop-card-shop-123"]')
```

#### ❌ Don't Skip Loading States
```typescript
// Bad - not waiting for content
await page.locator('[data-testid="shops-shop-card-shop-123"]').click();

// Good - ensure element is ready
await expect(page.locator('[data-testid="shops-shop-card-shop-123"]')).toBeVisible();
await page.locator('[data-testid="shops-shop-card-shop-123"]').click();
```

### 📝 Adding New Test IDs

#### 1. Add to Constants File
```typescript
// In src/lib/test-ids.ts
export const NEW_FEATURE_TEST_IDS = {
  CONTAINER: 'new-feature-container',
  TITLE: 'new-feature-title',
  ACTION_BUTTON: 'new-feature-action-button',
  // Dynamic content
  ITEM_CARD: (id: string) => `new-feature-item-card-${id}`,
} as const;
```

#### 2. Update Components
```typescript
import { NEW_FEATURE_TEST_IDS } from '@/lib/test-ids';

export function NewFeature() {
  return (
    <div data-testid={NEW_FEATURE_TEST_IDS.CONTAINER}>
      <h1 data-testid={NEW_FEATURE_TEST_IDS.TITLE}>
        New Feature
      </h1>
      <button data-testid={NEW_FEATURE_TEST_IDS.ACTION_BUTTON}>
        Take Action
      </button>
    </div>
  );
}
```

#### 3. Write Tests
```typescript
import { NEW_FEATURE_TEST_IDS } from '@/lib/test-ids';

test.describe('New Feature', () => {
  test('should render correctly', async ({ page }) => {
    await page.goto('/new-feature');
    
    await expect(page.locator(`[data-testid="${NEW_FEATURE_TEST_IDS.CONTAINER}"]`)).toBeVisible();
    await expect(page.locator(`[data-testid="${NEW_FEATURE_TEST_IDS.TITLE}"]`)).toHaveText('New Feature');
    await expect(page.locator(`[data-testid="${NEW_FEATURE_TEST_IDS.ACTION_BUTTON}"]`)).toBeEnabled();
  });
});
```

### 🎯 Test Execution

#### Run Specific Tests
```bash
# Run all enhanced tests
npx playwright test tests/e2e/*-enhanced.spec.ts

# Run specific test file
npx playwright test customer-workflows-enhanced.spec.ts

# Run specific test case
npx playwright test -g "should complete QR scanning workflow"

# Run with specific browser
npx playwright test --project=chromium
```

#### Test Configuration
```bash
# Headed mode (visible browser)
npx playwright test --headed

# Debug mode
npx playwright test --debug

# Generate test report
npx playwright test --reporter=html
```

---

**📚 Further Reading:**
- [Playwright Documentation](https://playwright.dev/)
- [Test ID Implementation Summary](./test-id-implementation-summary.md)
- [Enhanced E2E Test Suites](./tests/e2e/)

**🆘 Support:**
For questions about test ID implementation or E2E testing, refer to the comprehensive test files in `/tests/e2e/` or the central constants in `/src/lib/test-ids.ts`.