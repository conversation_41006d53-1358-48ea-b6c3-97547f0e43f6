// Jest setup file for SDK integration tests

import fetch from 'node-fetch';

// Mock fetch globally for tests
global.fetch = fetch;

// Mock console methods to reduce noise in tests (but keep log for debugging)
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
  // Keep console.log for debugging
});

// Global test timeout
jest.setTimeout(30000);

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_BACKEND_URL = 'http://localhost:8100';
