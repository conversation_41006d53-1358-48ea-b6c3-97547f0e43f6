/**
 * ADC Credit SDK API Client
 */
import { ADCCreditConfig, APIResponse } from './types';
import { buildUrl, debugLog, formatApiUrl, getUserAgent, getClientIp } from './utils';

/**
 * Core API client for making requests to the ADC Credit API
 */
export class ApiClient {
  private apiUrl: string;
  private apiKey?: string;
  private token?: string;
  private debug: boolean;

  /**
   * Creates a new API client
   * @param config SDK configuration
   */
  constructor(config: ADCCreditConfig = {}) {
    this.apiUrl = formatApiUrl(config.apiUrl);
    this.apiKey = config.apiKey;
    this.token = config.token;
    this.debug = config.debug || false;
    
    debugLog(this.debug, 'API Client initialized', {
      apiUrl: this.apiUrl,
      hasApiKey: !!this.apiKey,
      hasToken: !!this.token,
    });
  }

  /**
   * Updates the client configuration
   * @param config New configuration options
   */
  public updateConfig(config: Partial<ADCCreditConfig>): void {
    if (config.apiUrl) {
      this.apiUrl = formatApiUrl(config.apiUrl);
    }
    if (config.apiKey !== undefined) {
      this.apiKey = config.apiKey;
    }
    if (config.token !== undefined) {
      this.token = config.token;
    }
    if (config.debug !== undefined) {
      this.debug = config.debug;
    }
    
    debugLog(this.debug, 'API Client configuration updated', {
      apiUrl: this.apiUrl,
      hasApiKey: !!this.apiKey,
      hasToken: !!this.token,
    });
  }

  /**
   * Makes a request to the API
   * @param endpoint API endpoint
   * @param options Request options
   * @returns API response
   */
  public async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    try {
      const url = buildUrl(this.apiUrl, endpoint);
      
      // Prepare headers
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };
      
      // Add authentication headers
      if (this.apiKey) {
        headers['X-API-Key'] = this.apiKey;
      } else if (this.token) {
        headers['Authorization'] = `Bearer ${this.token}`;
      }
      
      // Add client information if not provided
      const body = options.body ? JSON.parse(options.body as string) : undefined;
      if (body && (body.ip_address === undefined || body.user_agent === undefined)) {
        const enhancedBody = { ...body };
        
        if (body.ip_address === undefined) {
          const clientIp = getClientIp();
          if (clientIp) {
            enhancedBody.ip_address = clientIp;
          }
        }
        
        if (body.user_agent === undefined) {
          enhancedBody.user_agent = getUserAgent();
        }
        
        options.body = JSON.stringify(enhancedBody);
      }
      
      // Log request details in debug mode
      debugLog(this.debug, `Making ${options.method || 'GET'} request to ${url}`);
      
      // Make the request
      const response = await fetch(url, {
        ...options,
        headers,
      });
      
      // Parse the response
      let data: T | undefined;
      let error: string | undefined;
      
      if (response.ok) {
        if (response.status !== 204) { // No content
          data = await response.json() as T;
        }
      } else {
        try {
          const errorData = await response.json();
          error = errorData.error || `Request failed with status ${response.status}`;
        } catch (e) {
          error = `Request failed with status ${response.status}`;
        }
      }
      
      // Log response in debug mode
      debugLog(this.debug, `Response from ${url}`, { status: response.status, data, error });
      
      return {
        data,
        error,
        status: response.status,
      };
    } catch (error) {
      debugLog(this.debug, 'Request error', error);
      
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 0,
      };
    }
  }

  /**
   * Makes a GET request to the API
   * @param endpoint API endpoint
   * @param options Request options
   * @returns API response
   */
  public async get<T>(endpoint: string, options: RequestInit = {}): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'GET',
    });
  }

  /**
   * Makes a POST request to the API
   * @param endpoint API endpoint
   * @param data Request body
   * @param options Request options
   * @returns API response
   */
  public async post<T>(
    endpoint: string,
    data?: any,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Makes a PUT request to the API
   * @param endpoint API endpoint
   * @param data Request body
   * @param options Request options
   * @returns API response
   */
  public async put<T>(
    endpoint: string,
    data?: any,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Makes a DELETE request to the API
   * @param endpoint API endpoint
   * @param options Request options
   * @returns API response
   */
  public async delete<T>(endpoint: string, options: RequestInit = {}): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'DELETE',
    });
  }
}
