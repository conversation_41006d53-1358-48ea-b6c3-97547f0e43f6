/**
 * ADC Credit SDK API Keys Module
 */
import { ApiClient } from './client';
import { APIKey, APIResponse, CreateAPIKeyRequest, UpdateAPIKeyRequest } from './types';

/**
 * API Keys module for the ADC Credit SDK
 */
export class ApiKeys {
  private client: ApiClient;

  /**
   * Creates a new ApiKeys instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets all API keys for the current user
   * @returns List of API keys
   */
  public async getAll(): Promise<APIResponse<APIKey[]>> {
    return this.client.get<APIKey[]>('/apikeys');
  }

  /**
   * Gets a specific API key
   * @param id API key ID
   * @returns API key
   */
  public async get(id: string): Promise<APIResponse<APIKey>> {
    return this.client.get<APIKey>(`/apikeys/${id}`);
  }

  /**
   * Creates a new API key
   * @param data API key data
   * @returns Created API key
   */
  public async create(data: CreateAPIKeyRequest): Promise<APIResponse<APIKey>> {
    return this.client.post<APIKey>('/apikeys', data);
  }

  /**
   * Updates an API key
   * @param id API key ID
   * @param data API key data to update
   * @returns Updated API key
   */
  public async update(id: string, data: UpdateAPIKeyRequest): Promise<APIResponse<APIKey>> {
    return this.client.put<APIKey>(`/apikeys/${id}`, data);
  }

  /**
   * Deletes an API key
   * @param id API key ID
   * @returns Success message
   */
  public async delete(id: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/apikeys/${id}`);
  }

  /**
   * Verifies an API key (external API)
   * @param apiKey API key to verify
   * @param credits Number of credits to verify
   * @returns Verification result
   */
  public async verify(
    apiKey: string,
    credits: number
  ): Promise<APIResponse<{ valid: boolean; credit_balance: number }>> {
    return this.client.post<{ valid: boolean; credit_balance: number }>('/external/verify', {
      api_key: apiKey,
      credits,
    });
  }
}
