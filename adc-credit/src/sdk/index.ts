/**
 * ADC Credit SDK
 * A JavaScript/TypeScript SDK for interacting with the ADC Credit API
 */
import { ApiClient } from './client';
import { Auth } from './auth';
import { ApiKeys } from './apiKeys';
import { Credits } from './credits';
import { UsageStats } from './usage';
import { Webhooks } from './webhooks';
import { Users } from './users';
import { Organizations } from './organizations';
import { Merchant } from './merchant';
import { Customer } from './customer';
import { Subscriptions } from './subscription';
import { Shops } from './shops';
import { SettingsClient } from './settings';
import { ADCCreditConfig } from './types';

/**
 * Main SDK class
 */
export class ADCCreditSDK {
  /**
   * API client
   */
  public readonly client: ApiClient;

  /**
   * Authentication module
   */
  public readonly auth: Auth;

  /**
   * API keys module
   */
  public readonly apiKeys: ApiKeys;

  /**
   * Credits module
   */
  public readonly credits: Credits;

  /**
   * Usage statistics module
   */
  public readonly usage: UsageStats;

  /**
   * Webhooks module
   */
  public readonly webhooks: Webhooks;

  /**
   * Users module
   */
  public readonly users: Users;

  /**
   * Organizations module
   */
  public readonly organizations: Organizations;

  /**
   * Merchant module
   */
  public readonly merchant: Merchant;

  /**
   * Customer module
   */
  public readonly customer: Customer;

  /**
   * Subscriptions module
   */
  public readonly subscriptions: Subscriptions;

  /**
   * Unified Shops module
   */
  public readonly shops: Shops;

  /**
   * Settings module for auto-translation and user preferences
   */
  public readonly settings: SettingsClient;

  /**
   * Creates a new SDK instance
   * @param config SDK configuration
   */
  constructor(config: ADCCreditConfig = {}) {
    this.client = new ApiClient(config);
    this.auth = new Auth(this.client);
    this.apiKeys = new ApiKeys(this.client);
    this.credits = new Credits(this.client);
    this.usage = new UsageStats(this.client);
    this.webhooks = new Webhooks(this.client);
    this.users = new Users(this.client);
    this.organizations = new Organizations(this.client);
    this.merchant = new Merchant(this.client);
    this.customer = new Customer(this.client);
    this.subscriptions = new Subscriptions(this.client);
    this.shops = new Shops(this.client);
    this.settings = new SettingsClient(this.client);
  }

  /**
   * Updates the SDK configuration
   * @param config New configuration options
   */
  public updateConfig(config: Partial<ADCCreditConfig>): void {
    this.client.updateConfig(config);
  }
}

// Export types
export * from './types';

// Export default instance
export default ADCCreditSDK;
