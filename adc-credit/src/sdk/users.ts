/**
 * ADC Credit SDK Users Module
 */
import { ApiClient } from './client';
import { APIResponse } from './types';

/**
 * User object returned from the API
 */
export interface User {
  id: string;
  email: string;
  name: string;
  picture: string;
  google_id?: string;
  role: string;
  organization_id?: string;
  branch_id?: string;
  monthly_credits: number;
  next_credit_reset_date?: string;
  is_external_user: boolean;
  external_user_identifier?: string;
}

/**
 * External user object
 */
export interface ExternalUser extends User {
  organization_id: string;
  branch_id: string;
  is_external_user: true;
}

/**
 * Update user request
 */
export interface UpdateUserRequest {
  name?: string;
  picture?: string;
}

/**
 * Update user by admin request
 */
export interface UpdateUserByAdminRequest extends UpdateUserRequest {
  role?: string;
}

/**
 * Create external user request
 */
export interface CreateExternalUserRequest {
  organization_id: string;
  branch_id: string;
  name: string;
  email: string;
  monthly_credits?: number;
  external_user_identifier?: string;
}

/**
 * Update external user request
 */
export interface UpdateExternalUserRequest {
  id: string;
  organization_id: string;
  branch_id: string;
  name?: string;
  monthly_credits?: number;
}

/**
 * Users module for the ADC Credit SDK
 */
export class Users {
  private client: ApiClient;

  /**
   * Creates a new Users instance
   * @param client API client
   */
  constructor(client: ApiClient) {
    this.client = client;
  }

  /**
   * Gets the current user
   * @returns Current user
   */
  public async getCurrentUser(): Promise<APIResponse<User>> {
    return this.client.get<User>('/users/me');
  }

  /**
   * Updates the current user
   * @param data User data to update
   * @returns Updated user
   */
  public async updateCurrentUser(data: UpdateUserRequest): Promise<APIResponse<User>> {
    return this.client.put<User>('/users/me', data);
  }

  /**
   * Gets all users (admin only)
   * @returns List of users
   */
  public async getAllUsers(): Promise<APIResponse<User[]>> {
    return this.client.get<User[]>('/admin/users');
  }

  /**
   * Gets a specific user by ID (admin only)
   * @param id User ID
   * @returns User
   */
  public async getUser(id: string): Promise<APIResponse<User>> {
    return this.client.get<User>(`/admin/users/${id}`);
  }

  /**
   * Updates a user (admin only)
   * @param id User ID
   * @param data User data to update
   * @returns Updated user
   */
  public async updateUser(id: string, data: UpdateUserByAdminRequest): Promise<APIResponse<User>> {
    return this.client.put<User>(`/admin/users/${id}`, data);
  }

  /**
   * Deletes a user (admin only)
   * @param id User ID
   * @returns Success message
   */
  public async deleteUser(id: string): Promise<APIResponse<{ message: string }>> {
    return this.client.delete<{ message: string }>(`/admin/users/${id}`);
  }

  /**
   * Gets all external users
   * @param params Query parameters
   * @returns List of external users
   */
  public async getExternalUsers(params?: {
    organization_id?: string;
    branch_id?: string;
  }): Promise<APIResponse<ExternalUser[]>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value);
        }
      });
    }
    
    const queryString = queryParams.toString();
    const endpoint = queryString ? `/org-users?${queryString}` : '/org-users';
    
    return this.client.get<ExternalUser[]>(endpoint);
  }

  /**
   * Gets a specific external user
   * @param id External user ID
   * @param params Query parameters
   * @returns External user
   */
  public async getExternalUser(
    id: string,
    params: {
      organization_id: string;
      branch_id: string;
    }
  ): Promise<APIResponse<ExternalUser>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    
    const endpoint = `/org-users/${id}?${queryParams.toString()}`;
    
    return this.client.get<ExternalUser>(endpoint);
  }

  /**
   * Creates a new external user
   * @param data External user data
   * @returns Created external user
   */
  public async createExternalUser(data: CreateExternalUserRequest): Promise<APIResponse<ExternalUser>> {
    return this.client.post<ExternalUser>('/org-users', data);
  }

  /**
   * Updates an external user
   * @param data External user data to update
   * @returns Updated external user
   */
  public async updateExternalUser(data: UpdateExternalUserRequest): Promise<APIResponse<ExternalUser>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    queryParams.append('organization_id', data.organization_id);
    queryParams.append('branch_id', data.branch_id);
    
    const endpoint = `/org-users/${data.id}?${queryParams.toString()}`;
    
    // Remove the ID, organization_id, and branch_id from the data
    const { id, organization_id, branch_id, ...updateData } = data;
    
    return this.client.put<ExternalUser>(endpoint, updateData);
  }

  /**
   * Deletes an external user
   * @param id External user ID
   * @param params Query parameters
   * @returns Success message
   */
  public async deleteExternalUser(
    id: string,
    params: {
      organization_id: string;
      branch_id: string;
    }
  ): Promise<APIResponse<{ message: string }>> {
    // Convert params to query string
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    
    const endpoint = `/org-users/${id}?${queryParams.toString()}`;
    
    return this.client.delete<{ message: string }>(endpoint);
  }

  /**
   * Adds credits to an external user
   * @param id External user ID
   * @param amount Amount of credits to add
   * @param description Description of the transaction
   * @returns Result of adding credits
   */
  public async addCreditsToExternalUser(
    id: string,
    amount: number,
    description?: string
  ): Promise<APIResponse<{ message: string; credit_balance: number }>> {
    return this.client.post<{ message: string; credit_balance: number }>(
      `/org-users/${id}/credits/add`,
      { amount, description }
    );
  }

  /**
   * Reduces credits from an external user
   * @param id External user ID
   * @param amount Amount of credits to reduce
   * @param description Description of the transaction
   * @returns Result of reducing credits
   */
  public async reduceCreditsFromExternalUser(
    id: string,
    amount: number,
    description?: string
  ): Promise<APIResponse<{ message: string; credit_balance: number }>> {
    return this.client.post<{ message: string; credit_balance: number }>(
      `/org-users/${id}/credits/reduce`,
      { amount, description }
    );
  }
}
