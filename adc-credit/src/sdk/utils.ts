/**
 * ADC Credit SDK Utilities
 */

/**
 * Formats the API URL to ensure it has the correct format
 * @param apiUrl The base API URL
 * @returns Properly formatted API URL
 */
export function formatApiUrl(apiUrl?: string): string {
  // Default API URL
  const BASE_URL = apiUrl || process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400';
  
  // Ensure API_URL always has the /api/v1 prefix
  return BASE_URL.endsWith('/api/v1') ? BASE_URL : `${BASE_URL}/api/v1`;
}

/**
 * Formats an endpoint to ensure it starts with a slash
 * @param endpoint The API endpoint
 * @returns Properly formatted endpoint
 */
export function formatEndpoint(endpoint: string): string {
  if (!endpoint.startsWith('/')) {
    return `/${endpoint}`;
  }
  return endpoint;
}

/**
 * Combines the API URL and endpoint
 * @param apiUrl The base API URL
 * @param endpoint The API endpoint
 * @returns Full URL
 */
export function buildUrl(apiUrl: string, endpoint: string): string {
  const formattedEndpoint = formatEndpoint(endpoint);
  
  // If the endpoint already starts with /api/v1, don't add the API_URL prefix
  if (formattedEndpoint.startsWith('/api/v1')) {
    const baseWithoutApiV1 = apiUrl.endsWith('/api/v1') 
      ? apiUrl.substring(0, apiUrl.length - 7) 
      : apiUrl;
    return `${baseWithoutApiV1}${formattedEndpoint}`;
  }
  
  return `${apiUrl}${formattedEndpoint}`;
}

/**
 * Logs debug messages if debug mode is enabled
 * @param debug Whether debug mode is enabled
 * @param message The message to log
 * @param data Additional data to log
 */
export function debugLog(debug: boolean, message: string, data?: any): void {
  if (debug) {
    console.log(`[ADC Credit SDK] ${message}`);
    if (data) {
      console.log(data);
    }
  }
}

/**
 * Checks if running in a browser environment
 * @returns True if running in a browser
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Gets the user agent string
 * @returns User agent string or 'unknown' if not available
 */
export function getUserAgent(): string {
  if (isBrowser()) {
    return window.navigator.userAgent;
  }
  return 'Node.js';
}

/**
 * Gets the client IP address (only works in browser)
 * @returns IP address or undefined if not available
 */
export function getClientIp(): string | undefined {
  // This is a placeholder - in a real implementation, you might use a service
  // to determine the client IP, as it's not directly accessible in the browser
  return undefined;
}
