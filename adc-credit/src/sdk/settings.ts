import { ApiClient } from './client';
import { APIResponse } from './types';

// Types for Settings Service integration
export interface Setting {
  id: string;
  key: string;
  name: string;
  description?: string;
  value: any;
  data_type: 'string' | 'integer' | 'float' | 'boolean' | 'json' | 'array' | 'object';
  default_value?: any;
  scope: 'global' | 'organization' | 'user' | 'shop';
  visibility: 'public' | 'private' | 'internal' | 'secret';
  organization_id?: string;
  user_id?: string;
  shop_id?: string;
  service_name?: string;
  environment?: string;
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  validation_schema?: Record<string, any>;
  is_required: boolean;
  is_readonly: boolean;
  is_encrypted: boolean;
  is_active: boolean;
  is_system: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface CreateSettingRequest {
  key: string;
  name: string;
  description?: string;
  value: any;
  data_type: 'string' | 'integer' | 'float' | 'boolean' | 'json' | 'array' | 'object';
  default_value?: any;
  scope: 'global' | 'organization' | 'user' | 'shop';
  visibility?: 'public' | 'private' | 'internal' | 'secret';
  organization_id?: string;
  user_id?: string;
  shop_id?: string;
  service_name?: string;
  environment?: string;
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  validation_schema?: Record<string, any>;
  is_required?: boolean;
  is_readonly?: boolean;
  is_encrypted?: boolean;
}

export interface UpdateSettingRequest {
  name?: string;
  description?: string;
  value?: any;
  default_value?: any;
  visibility?: 'public' | 'private' | 'internal' | 'secret';
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  validation_schema?: Record<string, any>;
  is_required?: boolean;
  is_readonly?: boolean;
  is_encrypted?: boolean;
  is_active?: boolean;
}

export interface SettingsQuery {
  keys?: string[];
  scope?: 'global' | 'organization' | 'user' | 'shop';
  visibility?: 'public' | 'private' | 'internal' | 'secret';
  organization_id?: string;
  user_id?: string;
  shop_id?: string;
  service_name?: string;
  environment?: string;
  category?: string;
  tags?: string[];
  is_active?: boolean;
  is_system?: boolean;
  limit?: number;
  offset?: number;
}

export interface SettingsResult {
  settings: Setting[];
  total: number;
  offset: number;
  limit: number;
  has_more: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors?: string[];
}

// Auto-translation specific setting keys
export const AUTO_TRANSLATION_SETTINGS = {
  USER: {
    ENABLED: 'user.auto_translation.enabled',
    TARGET_LANGUAGE: 'user.auto_translation.target_language',
    CONFIDENCE_THRESHOLD: 'user.auto_translation.confidence_threshold',
    FALLBACK_ENABLED: 'user.auto_translation.fallback_enabled',
  },
  SHOP: {
    ENABLED: 'shop.auto_translation.enabled',
    BATCH_PROCESSING: 'shop.auto_translation.batch_processing',
    REAL_TIME_MODE: 'shop.auto_translation.real_time_mode',
    SMART_TRIGGERS: 'shop.auto_translation.smart_triggers',
  },
  GLOBAL: {
    DEFAULT_CONFIDENCE_THRESHOLD: 'global.auto_translation.default_confidence_threshold',
    SUPPORTED_LANGUAGES: 'global.auto_translation.supported_languages',
    MAX_RETRIES: 'global.auto_translation.max_retries',
  },
} as const;

export class SettingsClient {
  constructor(private client: ApiClient) {}

  // Generic setting operations
  async createSetting(request: CreateSettingRequest): Promise<APIResponse<Setting>> {
    return this.client.post('/api/v1/settings', request);
  }

  async getSetting(settingId: string): Promise<APIResponse<Setting>> {
    return this.client.get(`/api/v1/settings/${settingId}`);
  }

  async updateSetting(settingId: string, request: UpdateSettingRequest): Promise<APIResponse<Setting>> {
    return this.client.put(`/api/v1/settings/${settingId}`, request);
  }

  async deleteSetting(settingId: string): Promise<APIResponse<void>> {
    return this.client.delete(`/api/v1/settings/${settingId}`);
  }

  async getSettings(query: SettingsQuery): Promise<APIResponse<SettingsResult>> {
    return this.client.post('/api/v1/settings/search', query);
  }

  async validateSetting(settingId: string, value: any): Promise<APIResponse<ValidationResult>> {
    return this.client.post(`/api/v1/settings/${settingId}/validate`, { value });
  }

  // Helper methods for specific scopes
  async getGlobalSetting(key: string): Promise<Setting | null> {
    try {
      const response = await this.getSettings({
        keys: [key],
        scope: 'global',
        limit: 1,
      });
      return response.data?.settings[0] || null;
    } catch (error) {
      return null;
    }
  }

  async getUserSetting(key: string, userId: string): Promise<Setting | null> {
    try {
      const response = await this.getSettings({
        keys: [key],
        scope: 'user',
        user_id: userId,
        limit: 1,
      });
      return response.data?.settings[0] || null;
    } catch (error) {
      return null;
    }
  }

  async getShopSetting(key: string, shopId: string): Promise<Setting | null> {
    try {
      const response = await this.getSettings({
        keys: [key],
        scope: 'shop',
        shop_id: shopId,
        limit: 1,
      });
      return response.data?.settings[0] || null;
    } catch (error) {
      return null;
    }
  }

  async setGlobalSetting(key: string, value: any, options?: Partial<CreateSettingRequest>): Promise<APIResponse<Setting>> {
    return this.createSetting({
      key,
      name: options?.name || key.replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value,
      data_type: this.inferDataType(value),
      scope: 'global',
      visibility: 'public',
      ...options,
    });
  }

  async setUserSetting(key: string, value: any, userId: string, options?: Partial<CreateSettingRequest>): Promise<APIResponse<Setting>> {
    return this.createSetting({
      key,
      name: options?.name || key.replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value,
      data_type: this.inferDataType(value),
      scope: 'user',
      user_id: userId,
      visibility: 'private',
      ...options,
    });
  }

  async setShopSetting(key: string, value: any, shopId: string, options?: Partial<CreateSettingRequest>): Promise<APIResponse<Setting>> {
    return this.createSetting({
      key,
      name: options?.name || key.replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value,
      data_type: this.inferDataType(value),
      scope: 'shop',
      shop_id: shopId,
      visibility: 'private',
      ...options,
    });
  }

  // Auto-translation specific methods
  async getUserAutoTranslationSettings(userId: string): Promise<{
    enabled: boolean;
    targetLanguage: string;
    confidenceThreshold: number;
    fallbackEnabled: boolean;
  }> {
    const [enabled, targetLanguage, confidenceThreshold, fallbackEnabled] = await Promise.all([
      this.getUserSetting(AUTO_TRANSLATION_SETTINGS.USER.ENABLED, userId),
      this.getUserSetting(AUTO_TRANSLATION_SETTINGS.USER.TARGET_LANGUAGE, userId),
      this.getUserSetting(AUTO_TRANSLATION_SETTINGS.USER.CONFIDENCE_THRESHOLD, userId),
      this.getUserSetting(AUTO_TRANSLATION_SETTINGS.USER.FALLBACK_ENABLED, userId),
    ]);

    return {
      enabled: enabled?.value ?? false,
      targetLanguage: targetLanguage?.value ?? 'en',
      confidenceThreshold: confidenceThreshold?.value ?? 0.8,
      fallbackEnabled: fallbackEnabled?.value ?? true,
    };
  }

  async getShopAutoTranslationSettings(shopId: string): Promise<{
    enabled: boolean;
    batchProcessing: boolean;
    realTimeMode: boolean;
    smartTriggers: boolean;
  }> {
    const [enabled, batchProcessing, realTimeMode, smartTriggers] = await Promise.all([
      this.getShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.ENABLED, shopId),
      this.getShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.BATCH_PROCESSING, shopId),
      this.getShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.REAL_TIME_MODE, shopId),
      this.getShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.SMART_TRIGGERS, shopId),
    ]);

    return {
      enabled: enabled?.value ?? false,
      batchProcessing: batchProcessing?.value ?? true,
      realTimeMode: realTimeMode?.value ?? false,
      smartTriggers: smartTriggers?.value ?? true,
    };
  }

  async updateUserAutoTranslationSettings(
    userId: string,
    settings: {
      enabled?: boolean;
      targetLanguage?: string;
      confidenceThreshold?: number;
      fallbackEnabled?: boolean;
    }
  ): Promise<void> {
    const updates: Promise<any>[] = [];

    if (settings.enabled !== undefined) {
      updates.push(this.setUserSetting(AUTO_TRANSLATION_SETTINGS.USER.ENABLED, settings.enabled, userId, {
        name: 'Auto Translation Enabled',
        description: 'Whether auto-translation is enabled for the user',
        category: 'translation',
      }));
    }

    if (settings.targetLanguage !== undefined) {
      updates.push(this.setUserSetting(AUTO_TRANSLATION_SETTINGS.USER.TARGET_LANGUAGE, settings.targetLanguage, userId, {
        name: 'Auto Translation Target Language',
        description: 'Preferred target language for auto-translation',
        category: 'translation',
        validation_schema: {
          type: 'string',
          enum: ['en', 'es', 'fr', 'de', 'ja', 'zh', 'ko', 'pt', 'it', 'ru', 'ar'],
        },
      }));
    }

    if (settings.confidenceThreshold !== undefined) {
      updates.push(this.setUserSetting(AUTO_TRANSLATION_SETTINGS.USER.CONFIDENCE_THRESHOLD, settings.confidenceThreshold, userId, {
        name: 'Auto Translation Confidence Threshold',
        description: 'Minimum confidence score required for auto-translation',
        category: 'translation',
        validation_schema: {
          type: 'number',
          minimum: 0.0,
          maximum: 1.0,
        },
      }));
    }

    if (settings.fallbackEnabled !== undefined) {
      updates.push(this.setUserSetting(AUTO_TRANSLATION_SETTINGS.USER.FALLBACK_ENABLED, settings.fallbackEnabled, userId, {
        name: 'Auto Translation Fallback Enabled',
        description: 'Whether to use fallback translations when auto-translation fails',
        category: 'translation',
      }));
    }

    await Promise.all(updates);
  }

  async updateShopAutoTranslationSettings(
    shopId: string,
    settings: {
      enabled?: boolean;
      batchProcessing?: boolean;
      realTimeMode?: boolean;
      smartTriggers?: boolean;
    }
  ): Promise<void> {
    const updates: Promise<any>[] = [];

    if (settings.enabled !== undefined) {
      updates.push(this.setShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.ENABLED, settings.enabled, shopId, {
        name: 'Shop Auto Translation Enabled',
        description: 'Whether auto-translation is enabled for the shop',
        category: 'translation',
      }));
    }

    if (settings.batchProcessing !== undefined) {
      updates.push(this.setShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.BATCH_PROCESSING, settings.batchProcessing, shopId, {
        name: 'Shop Auto Translation Batch Processing',
        description: 'Whether to use batch processing for auto-translation',
        category: 'translation',
      }));
    }

    if (settings.realTimeMode !== undefined) {
      updates.push(this.setShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.REAL_TIME_MODE, settings.realTimeMode, shopId, {
        name: 'Shop Auto Translation Real-time Mode',
        description: 'Whether to enable real-time auto-translation',
        category: 'translation',
      }));
    }

    if (settings.smartTriggers !== undefined) {
      updates.push(this.setShopSetting(AUTO_TRANSLATION_SETTINGS.SHOP.SMART_TRIGGERS, settings.smartTriggers, shopId, {
        name: 'Shop Auto Translation Smart Triggers',
        description: 'Whether to use smart triggers for auto-translation',
        category: 'translation',
      }));
    }

    await Promise.all(updates);
  }

  // Health check for Settings Service
  async healthCheck(): Promise<APIResponse<{ status: string }>> {
    return this.client.get('/health');
  }

  // Private helper method to infer data type from value
  private inferDataType(value: any): CreateSettingRequest['data_type'] {
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'integer' : 'float';
    }
    if (typeof value === 'boolean') return 'boolean';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object' && value !== null) return 'object';
    return 'string';
  }
}