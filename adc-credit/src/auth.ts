import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { getServerSession } from "next-auth";

export const authOptions: NextAuthOptions = {
  // Enable debug mode in development
  debug: process.env.NODE_ENV === "development",
  
  // Configure session strategy
  session: {
    strategy: "jwt",
  },

  // Configure providers (SSO first)
  providers: [
    CredentialsProvider({
      id: "sso",
      name: "<PERSON><PERSON>",
      credentials: {
        code: { label: "Code", type: "text" },
        state: { label: "State", type: "text" }
      },
      async authorize(credentials) {
        if (!credentials?.code || !credentials?.state) {
          return null;
        }

        try {
          // Handle SSO callback through backend
          const response = await fetch(`/api/auth/sso/callback?code=${credentials.code}&state=${credentials.state}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const data = await response.json();

          if (response.ok && data.success) {
            return {
              id: data.data.user.id,
              name: data.data.user.username || data.data.user.first_name,
              email: data.data.user.email,
              accessToken: data.data.access_token,
              refreshToken: data.data.refresh_token,
              ssoToken: data.data.sso_token,
            };
          }

          return null;
        } catch (error) {
          console.error("SSO authentication error:", error);
          return null;
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "Email and Password",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
          if (!backendUrl) {
            throw new Error("Backend URL not configured");
          }

          const response = await fetch(`/api/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: "Authentication failed" }));
            throw new Error(errorData.error || "Authentication failed");
          }

          const data = await response.json();
          const accessToken = data.token || data.access_token;

          if (!accessToken || !data.user) {
            throw new Error("Invalid response from authentication server");
          }

          return {
            id: data.user.id,
            name: data.user.name,
            email: data.user.email,
            image: data.user.picture,
            picture: data.user.picture,
            role: data.user.role,
            accessToken: accessToken,
          };
        } catch (error) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user) {
        // For SSO provider, handle SSO tokens
        if (account?.provider === "credentials" && account?.type === "credentials" && user.ssoToken) {
          return {
            ...token,
            accessToken: user.ssoToken, // Use SSO token as primary
            refreshToken: user.refreshToken,
            provider: "sso",
            userId: user.id,
            backendToken: user.accessToken, // Local JWT token for backward compatibility
          };
        }
        
        // For credentials provider, the accessToken is directly on the user object
        if (user.accessToken) {
          return {
            ...token,
            accessToken: user.accessToken,
            provider: "credentials",
            userId: user.id,
          };
        }
        
        // For OAuth providers, let backend handle user creation/login and issue single JWT
        if (account) {
          if (account.provider === "google" && account.access_token) {
            try {
              const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
              if (!backendUrl) {
                throw new Error("Backend URL not configured");
              }

              const response = await fetch(`/api/auth/google`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ 
                  token: account.access_token,
                  user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    image: user.image
                  }
                }),
              });

              if (!response.ok) {
                throw new Error(`Failed to authenticate with backend: ${response.status}`);
              }

              const data = await response.json();

              // Use only the backend JWT token
              return {
                ...token,
                accessToken: data.token, // Single JWT token from backend
                provider: account.provider,
                userId: data.user?.id || user.id,
                backendToken: data.token, // Keep for clarity, but same as accessToken
              };
            } catch (error) {
              return {
                ...token,
                provider: account.provider,
                userId: user.id,
                exchangeError: error instanceof Error ? error.message : "Authentication failed",
              };
            }
          }
        }
      }

      return token;
    },

    async session({ session, token }) {
      // Add the access token to the session
      if (token.accessToken) {
        session.accessToken = token.accessToken as string;
        session.user.accessToken = token.accessToken as string;
      }

      // Add provider information if available
      if (token.provider) {
        session.user.provider = token.provider as string;
      }

      // Add user ID if available
      if (token.userId) {
        session.user.id = token.userId as string;
      }

      // Add exchange error if available
      if (token.exchangeError) {
        session.error = token.exchangeError as string;
      }

      return session;
    },

    async redirect({ url, baseUrl }) {
      // Allow same-origin URLs
      if (url.startsWith(baseUrl)) {
        return url;
      }
      
      // Allow relative URLs
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`;
      }
      
      // Default to dashboard after sign in
      return `${baseUrl}/dashboard`;
    },
  },
  
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  
  secret: process.env.NEXTAUTH_SECRET,
  
  // Security settings
  useSecureCookies: process.env.NODE_ENV === "production",
  
  // Cookie settings
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production" ? "__Secure-next-auth.session-token" : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
};

// Helper function to get the session on the server side
export const getAuthSession = () => getServerSession(authOptions);
