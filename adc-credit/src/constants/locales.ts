/**
 * Supported locales configuration for Credit Service
 */

export interface SupportedLocale {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl?: boolean;
}

export const SUPPORTED_LOCALES: SupportedLocale[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    flag: '🇹🇭',
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
  },
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
  },
  {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
  },
  {
    code: 'zh-TW',
    name: 'Chinese (Traditional)',
    nativeName: '繁體中文',
    flag: '🇹🇼',
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true,
  },
  {
    code: 'he',
    name: 'Hebrew',
    nativeName: 'עברית',
    flag: '🇮🇱',
    rtl: true,
  },
];

export const DEFAULT_LOCALE = 'en';

export const FALLBACK_LOCALE = 'en';

/**
 * Get locale by code
 */
export function getLocaleByCode(code: string): SupportedLocale | undefined {
  return SUPPORTED_LOCALES.find(locale => locale.code === code);
}

/**
 * Check if locale is RTL
 */
export function isRTLLocale(code: string): boolean {
  const locale = getLocaleByCode(code);
  return locale?.rtl === true;
}

/**
 * Get locale display name
 */
export function getLocaleDisplayName(code: string): string {
  const locale = getLocaleByCode(code);
  return locale ? `${locale.flag} ${locale.nativeName}` : code;
}