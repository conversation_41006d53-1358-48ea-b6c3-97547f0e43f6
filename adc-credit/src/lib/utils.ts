import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Validates if a string is a valid slug format
 */
export function isValidSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') return false;
  
  // Check if slug matches the expected pattern: lowercase letters, numbers, and hyphens
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugPattern.test(slug) && slug.length >= 2 && slug.length <= 100;
}

/**
 * Checks if a string looks like a UUID (for detecting old ID-based URLs)
 */
export function isUUID(str: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidPattern.test(str);
}

/**
 * Generates a user-friendly error message for invalid slugs
 */
export function getSlugErrorMessage(slug: string): string {
  if (!slug) return "Shop identifier is missing";
  if (isUUID(slug)) return "This URL format is no longer supported. Please use the updated shop link.";
  if (!isValidSlug(slug)) return `"${slug}" is not a valid shop identifier`;
  return "Shop not found";
}

/**
 * Sanitizes and normalizes a slug input
 */
export function sanitizeSlug(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove invalid characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Generates a slug from a name with collision handling
 */
export function generateSlugFromName(name: string, existingSlugs: string[] = []): string {
  const baseSlug = sanitizeSlug(name);
  if (!baseSlug) return 'shop';
  
  let slug = baseSlug;
  let counter = 1;
  
  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
}
