import { store } from '@/lib/store';
import { 
  shopApi,
  customerApi
} from '@/lib/api/apiSlice';

/**
 * Prefetch shop data by slug for better performance
 */
export function prefetchShopBySlug(slug: string) {
  if (!slug || typeof slug !== 'string') return;
  
  store.dispatch(
    shopApi.util.prefetch('getShopBySlug', slug, {
      force: false, // Don't force if already cached
    })
  );
}

/**
 * Prefetch customer shop data for a specific shop
 */
export function prefetchCustomerShopData(shopId: string) {
  if (!shopId) return;
  
  // Prefetch customer shop data
  store.dispatch(
    customerApi.util.prefetch('getCustomerShop', shopId, {
      force: false,
    })
  );
  
  // Prefetch customer transactions
  store.dispatch(
    customerApi.util.prefetch('getCustomerTransactions', shopId, {
      force: false,
    })
  );
}

/**
 * Prefetch all customer shops for faster navigation
 */
export function prefetchCustomerShops() {
  store.dispatch(
    customerApi.util.prefetch('getCustomerShops', undefined, {
      force: false,
    })
  );
}

/**
 * Batch prefetch for customer shop detail page
 */
export function prefetchCustomerShopDetailPage(slug: string) {
  prefetchShopBySlug(slug);
  
  // We'll prefetch customer data after we get the shop ID
  // This is handled in the useCustomerShopBySlug hook
}

/**
 * Clear old cache entries to prevent memory bloat
 */
export function clearOldShopCache() {
  // Clear entries older than 10 minutes
  const maxAge = 10 * 60 * 1000; // 10 minutes
  const cutoffTime = Date.now() - maxAge;
  
  // This would need to be implemented with custom cache management
  // For now, we rely on RTK Query's built-in cache management
}

/**
 * Prefetch related shops based on user activity
 */
export function prefetchRelatedShops(currentShopSlug: string, recentShops: string[]) {
  // Prefetch recently visited shops
  recentShops
    .filter(slug => slug !== currentShopSlug)
    .slice(0, 3) // Limit to 3 most recent
    .forEach(prefetchShopBySlug);
}

/**
 * Smart prefetching based on route predictions
 */
export function smartPrefetch(currentPath: string, userRole: 'customer' | 'merchant') {
  if (userRole === 'customer') {
    if (currentPath === '/dashboard/customer/shops') {
      // User is on shops list, likely to visit a shop detail
      prefetchCustomerShops();
    }
  }
  
  // Add more smart prefetching logic as needed
}