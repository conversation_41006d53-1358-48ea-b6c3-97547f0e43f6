/**
 * Correlation ID utilities for request tracing
 * Provides consistent correlation ID generation and management across the frontend
 */

import { v4 as uuidv4 } from 'uuid';

export const CORRELATION_ID_HEADER = 'X-Correlation-ID';

/**
 * Generate a new correlation ID (UUID v4)
 */
export function generateCorrelationId(): string {
  return uuidv4();
}

/**
 * Get correlation ID from various sources (headers, storage, generate new)
 */
export function getCorrelationId(request?: Request): string {
  // Try to get from request headers first
  if (request) {
    const headerValue = request.headers.get(CORRELATION_ID_HEADER);
    if (headerValue) {
      return headerValue;
    }
  }

  // Try to get from browser storage (for client-side continuity)
  if (typeof window !== 'undefined') {
    const stored = sessionStorage.getItem('correlation-id');
    if (stored) {
      return stored;
    }
  }

  // Generate new one
  const newId = generateCorrelationId();
  
  // Store for session continuity (client-side only)
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('correlation-id', newId);
  }

  return newId;
}

/**
 * Store correlation ID for session continuity
 */
export function storeCorrelationId(correlationId: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('correlation-id', correlationId);
  }
}

/**
 * Clear stored correlation ID (useful for new sessions)
 */
export function clearCorrelationId(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('correlation-id');
  }
}

/**
 * Create headers with correlation ID
 */
export function createCorrelationHeaders(existingHeaders?: HeadersInit): HeadersInit {
  const correlationId = getCorrelationId();
  
  return {
    ...existingHeaders,
    [CORRELATION_ID_HEADER]: correlationId,
  };
}

/**
 * Add correlation ID to fetch options
 */
export function withCorrelationId(options: RequestInit = {}): RequestInit {
  const correlationId = getCorrelationId();
  
  return {
    ...options,
    headers: {
      ...options.headers,
      [CORRELATION_ID_HEADER]: correlationId,
    },
  };
}

/**
 * Extract correlation ID from response headers
 */
export function extractCorrelationId(response: Response): string | null {
  return response.headers.get(CORRELATION_ID_HEADER);
}

/**
 * Log with correlation ID context
 */
export function logWithCorrelation(
  message: string, 
  data?: any, 
  correlationId?: string
): void {
  const id = correlationId || getCorrelationId();
  console.log(`[${id}] ${message}`, data ? data : '');
}

/**
 * Error logging with correlation ID context
 */
export function logErrorWithCorrelation(
  error: Error | string, 
  correlationId?: string
): void {
  const id = correlationId || getCorrelationId();
  console.error(`[${id}] Error:`, error);
}