/**
 * Translation API client for communicating with Multi-Languages service
 * Uses smart auto-translation endpoints with internal key authentication
 */

export interface TranslationRequest {
  project_id: string;
  namespace: string;
  key: string;
  locale: string;
  force_create?: boolean;
}

export interface TranslationResponse {
  success: boolean;
  data?: {
    key: string;
    namespace: string;
    locale: string;
    value: string;
    auto_translated: boolean;
    created_new: boolean;
    translation_key_id: string;
    translation_id?: string;
    confidence?: number;
    source_locale?: string;
  };
  error?: string;
  message?: string;
}

export interface NamespaceTranslationsResponse {
  success: boolean;
  data?: {
    translations: Record<string, string>;
    namespace: string;
    locale: string;
    project_id: string;
    count: number;
  };
  error?: string;
  message?: string;
}

// Credit Service project configuration
export const CREDIT_PROJECT_CONFIG = {
  // Use Credit Service backend API instead of direct Multi-Languages service calls
  API_URL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400',
  PROJECT_ID: process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'b90e383d-6e7d-4881-ba9a-c31649719348',
} as const;

export class TranslationAPI {
  private apiURL: string;

  constructor() {
    this.apiURL = CREDIT_PROJECT_CONFIG.API_URL;
  }

  /**
   * Fetch a single translation with smart auto-translation
   */
  async fetchTranslation(
    key: string, 
    locale: string, 
    namespace: string = 'ui'
  ): Promise<TranslationResponse> {
    try {
      // Use Credit Service API proxy instead of direct Multi-Languages service call
      const response = await fetch(`/api/translations/${namespace}/${key}/${locale}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Translation API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to fetch translation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Fetch all translations for a namespace and locale
   */
  async fetchNamespaceTranslations(
    namespace: string,
    locale: string
  ): Promise<NamespaceTranslationsResponse> {
    try {
      // Use Credit Service API proxy instead of direct Multi-Languages service call
      const response = await fetch(`/api/translations/${namespace}/${locale}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Translation API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to fetch namespace translations:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Batch fetch multiple translations
   */
  async fetchMultipleTranslations(
    translations: Array<{ key: string; namespace?: string }>,
    locale: string
  ): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    // Group by namespace for efficient batch fetching
    const byNamespace = translations.reduce((acc, { key, namespace = 'ui' }) => {
      if (!acc[namespace]) acc[namespace] = [];
      acc[namespace].push(key);
      return acc;
    }, {} as Record<string, string[]>);

    // Fetch each namespace
    await Promise.all(
      Object.entries(byNamespace).map(async ([namespace, keys]) => {
        const response = await this.fetchNamespaceTranslations(namespace, locale);
        
        if (response.success && response.data) {
          keys.forEach(key => {
            const fullKey = `${namespace}.${key}`;
            results[fullKey] = response.data!.translations[key] || key;
          });
        } else {
          // Fallback to individual fetches if namespace fetch fails
          await Promise.all(
            keys.map(async (key) => {
              const response = await this.fetchTranslation(key, locale, namespace);
              const fullKey = `${namespace}.${key}`;
              results[fullKey] = response.data?.value || key;
            })
          );
        }
      })
    );

    return results;
  }
}

// Singleton instance
export const translationAPI = new TranslationAPI();