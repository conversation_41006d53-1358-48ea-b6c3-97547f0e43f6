/**
 * Translation hooks for Credit Service i18n
 */

'use client';

import { useCallback, useEffect, useState, useContext } from 'react';
import { translationAPI } from './api';
import { interpolate, getStoredLocale, setStoredLocale, getBrowserLocale } from './utils';
import { DEFAULT_LOCALE, FALLBACK_LOCALE, getLocaleByCode } from '../../constants/locales';
import { TranslationContext } from './provider';

export interface UseTranslationReturn {
  t: (key: string, variables?: Record<string, string | number>, fallback?: string) => string;
  locale: string;
  setLocale: (locale: string) => void;
  loading: boolean;
  error: string | null;
  isReady: boolean;
}

/**
 * Main translation hook
 */
export function useTranslation(namespace: string = 'ui'): UseTranslationReturn {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }

  const { 
    locale, 
    setLocale: setGlobalLocale, 
    translations, 
    loading, 
    error,
    loadNamespace 
  } = context;

  // Load namespace translations when hook is used
  useEffect(() => {
    loadNamespace(namespace);
  }, [namespace, locale, loadNamespace]);

  // Translation function
  const t = useCallback((
    key: string, 
    variables?: Record<string, string | number>,
    fallback?: string
  ): string => {
    const fullKey = `${namespace}.${key}`;
    let translation = translations[fullKey];

    // Check if translation exists and has content
    if (!translation || (typeof translation === 'string' && translation.trim().length === 0)) {
      // Try fallback locale
      if (locale !== FALLBACK_LOCALE) {
        const fallbackKey = `${FALLBACK_LOCALE}.${namespace}.${key}`;
        translation = translations[fallbackKey];
      }

      // If still no translation, use the provided fallback
      if (!translation || (typeof translation === 'string' && translation.trim().length === 0)) {
        translation = fallback;
      }
    }

    // Final fallback: use a clean version of the key
    const finalText = translation || key.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    // Interpolate variables if provided
    return variables ? interpolate(finalText, variables) : finalText;
  }, [namespace, translations, locale]);

  const isReady = !loading && !error;

  return {
    t,
    locale,
    setLocale: setGlobalLocale,
    loading,
    error,
    isReady,
  };
}

/**
 * Hook for locale management only (without translation loading)
 */
export function useLocale() {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useLocale must be used within a TranslationProvider');
  }

  return {
    locale: context.locale,
    setLocale: context.setLocale,
    supportedLocales: context.supportedLocales,
  };
}

/**
 * Hook for preloading multiple namespaces
 */
export function useTranslationPreload(namespaces: string[]) {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslationPreload must be used within a TranslationProvider');
  }

  const { loadNamespace, locale } = context;

  useEffect(() => {
    // Preload all namespaces
    namespaces.forEach(namespace => {
      loadNamespace(namespace);
    });
  }, [namespaces, locale, loadNamespace]);
}

/**
 * Hook for checking if translations are loaded for specific namespaces
 */
export function useTranslationStatus(namespaces: string[]) {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslationStatus must be used within a TranslationProvider');
  }

  const { translations, locale, loading } = context;

  const isLoaded = namespaces.every(namespace => {
    // Check if we have at least one translation for this namespace
    const namespaceKey = `${namespace}.`;
    return Object.keys(translations).some(key => 
      key.startsWith(`${locale}.${namespaceKey}`) || 
      key.startsWith(namespaceKey)
    );
  });

  return {
    isLoaded: isLoaded && !loading,
    loading,
  };
}

/**
 * Simple hook for getting current locale without full translation context
 */
export function useCurrentLocale(): string {
  const [locale, setLocaleState] = useState(() => {
    // Try stored locale first, then browser locale, then default
    return getStoredLocale() || getBrowserLocale() || DEFAULT_LOCALE;
  });

  const setLocale = useCallback((newLocale: string) => {
    const supportedLocale = getLocaleByCode(newLocale);
    if (supportedLocale) {
      setLocaleState(newLocale);
      setStoredLocale(newLocale);
    }
  }, []);

  return locale;
}