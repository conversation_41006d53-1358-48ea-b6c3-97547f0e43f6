/**
 * Internationalization utilities for formatting and interpolation
 */

import { isRTLLocale } from '@/constants/locales';

/**
 * Interpolate variables into translation strings
 * Supports {{variable}} syntax
 */
export function interpolate(
  template: string, 
  variables: Record<string, string | number> = {}
): string {
  if (!template) return '';
  
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    const value = variables[key];
    return value !== undefined ? String(value) : match;
  });
}

/**
 * Format numbers according to locale
 */
export function formatNumber(
  value: number, 
  locale: string = 'en',
  options: Intl.NumberFormatOptions = {}
): string {
  try {
    return new Intl.NumberFormat(locale, options).format(value);
  } catch {
    // Fallback to English if locale is not supported
    return new Intl.NumberFormat('en', options).format(value);
  }
}

/**
 * Format currency according to locale
 */
export function formatCurrency(
  value: number,
  locale: string = 'en',
  currency: string = 'USD'
): string {
  return formatNumber(value, locale, {
    style: 'currency',
    currency,
  });
}

/**
 * Format dates according to locale
 */
export function formatDate(
  date: Date | string | number,
  locale: string = 'en',
  options: Intl.DateTimeFormatOptions = {}
): string {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' 
      ? new Date(date) 
      : date;
    
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...options,
    }).format(dateObj);
  } catch {
    // Fallback to English if locale is not supported
    return new Intl.DateTimeFormat('en', options).format(new Date(date));
  }
}

/**
 * Format relative time (e.g., "2 days ago")
 */
export function formatRelativeTime(
  date: Date | string | number,
  locale: string = 'en'
): string {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' 
      ? new Date(date) 
      : date;
    
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  } catch {
    // Fallback to simple format
    return formatDate(date, locale);
  }
}

/**
 * Get direction class for RTL support
 */
export function getDirectionClass(locale: string): string {
  return isRTLLocale(locale) ? 'rtl' : 'ltr';
}

/**
 * Get text alignment class for locale
 */
export function getTextAlignClass(locale: string): string {
  return isRTLLocale(locale) ? 'text-right' : 'text-left';
}

/**
 * Pluralization helper
 */
export function pluralize(
  count: number,
  singular: string,
  plural?: string,
  locale: string = 'en'
): string {
  const rules = new Intl.PluralRules(locale);
  const rule = rules.select(count);
  
  if (rule === 'one') {
    return singular;
  }
  
  return plural || `${singular}s`;
}

/**
 * Safe locale storage key
 */
export const LOCALE_STORAGE_KEY = 'adc-credit-locale';

/**
 * Get stored locale from localStorage
 */
export function getStoredLocale(): string | null {
  if (typeof window === 'undefined') return null;
  
  try {
    return localStorage.getItem(LOCALE_STORAGE_KEY);
  } catch {
    return null;
  }
}

/**
 * Store locale in localStorage
 */
export function setStoredLocale(locale: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(LOCALE_STORAGE_KEY, locale);
  } catch {
    // Ignore storage errors
  }
}

/**
 * Get browser locale preference
 */
export function getBrowserLocale(): string {
  if (typeof window === 'undefined') return 'en';
  
  const browserLang = navigator.language || navigator.languages?.[0] || 'en';
  
  // Extract just the language code (e.g., 'en' from 'en-US')
  return browserLang.split('-')[0];
}