/**
 * i18n module exports for Credit Service
 */

// Core API and configuration
export { translationAPI, CREDIT_PROJECT_CONFIG } from './api';
export type { 
  TranslationRequest, 
  TranslationResponse, 
  NamespaceTranslationsResponse 
} from './api';

// Hooks
export { 
  useTranslation, 
  useLocale, 
  useTranslationPreload, 
  useTranslationStatus,
  useCurrentLocale 
} from './hooks';
export type { UseTranslationReturn } from './hooks';

// Provider
export { TranslationProvider, TranslationContext, withTranslation } from './provider';
export type { TranslationContextValue } from './provider';

// Components
export { 
  T, 
  Translate, 
  Trans, 
  Plural, 
  Number, 
  DateComponent 
} from './components';
export type { 
  TProps, 
  TranslateProps, 
  TransProps, 
  PluralProps, 
  NumberProps, 
  DateProps 
} from './components';

// Utilities
export {
  interpolate,
  formatNumber,
  formatCurrency,
  formatDate,
  formatRelativeTime,
  getDirectionClass,
  getTextAlignClass,
  pluralize,
  getStoredLocale,
  setStoredLocale,
  getBrowserLocale,
  LOCALE_STORAGE_KEY
} from './utils';

// Locale constants
export {
  SUPPORTED_LOCALES,
  DEFAULT_LOCALE,
  FALLBACK_LOCALE,
  getLocaleByCode,
  isRTLLocale,
  getLocaleDisplayName
} from '../../constants/locales';
export type { SupportedLocale } from '../../constants/locales';