/**
 * Translation context provider for Credit Service i18n
 */

'use client';

import React, { createContext, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { translationAPI } from './api';
import { getStoredLocale, setStoredLocale, getBrowserLocale } from './utils';
import { DEFAULT_LOCALE, SUPPORTED_LOCALES, getLocaleByCode } from '@/constants/locales';
import { fallbackTranslations } from './fallback-translations';

export interface TranslationContextValue {
  locale: string;
  setLocale: (locale: string) => void;
  translations: Record<string, string>;
  loading: boolean;
  error: string | null;
  loadNamespace: (namespace: string) => Promise<void>;
  supportedLocales: typeof SUPPORTED_LOCALES;
}

export const TranslationContext = createContext<TranslationContextValue | null>(null);

interface TranslationProviderProps {
  children: React.ReactNode;
  defaultLocale?: string;
  fallbackLocale?: string;
}

export function TranslationProvider({ 
  children, 
  defaultLocale = DEFAULT_LOCALE,
  fallbackLocale = DEFAULT_LOCALE 
}: TranslationProviderProps) {
  // State management
  const [locale, setLocaleState] = useState(() => {
    // Try stored locale first, then browser locale, then default
    const stored = getStoredLocale();
    const browser = getBrowserLocale();
    const candidate = stored || browser || defaultLocale;
    
    // Validate that the locale is supported
    return getLocaleByCode(candidate) ? candidate : defaultLocale;
  });

  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track which namespaces have been loaded for each locale
  const loadedNamespaces = useRef<Set<string>>(new Set());
  
  // Track ongoing fetch requests to prevent duplicates
  const fetchingNamespaces = useRef<Set<string>>(new Set());

  // Set locale with validation and persistence
  const setLocale = useCallback((newLocale: string) => {
    const supportedLocale = getLocaleByCode(newLocale);
    if (!supportedLocale) {
      console.warn(`Locale '${newLocale}' is not supported. Available locales:`, 
        SUPPORTED_LOCALES.map(l => l.code));
      return;
    }

    setLocaleState(newLocale);
    setStoredLocale(newLocale);
    
    // Clear loaded namespaces when locale changes
    loadedNamespaces.current.clear();
    fetchingNamespaces.current.clear();
  }, []);

  // Load translations for a specific namespace
  const loadNamespace = useCallback(async (namespace: string) => {
    const cacheKey = `${locale}-${namespace}`;
    
    // Skip if already loaded or currently fetching
    if (loadedNamespaces.current.has(cacheKey) || fetchingNamespaces.current.has(cacheKey)) {
      return;
    }

    fetchingNamespaces.current.add(cacheKey);
    setLoading(true);
    setError(null);

    try {
      // Try to fetch namespace translations from API
      const response = await translationAPI.fetchNamespaceTranslations(namespace, locale);
      
      if (response.success && response.data) {
        // Check if we have meaningful content (not just empty strings)
        const hasContent = Object.values(response.data.translations).some(content => 
          typeof content === 'string' && content.trim().length > 0
        );

        if (hasContent) {
          // Update translations state with API data
          setTranslations(prev => {
            const newTranslations = { ...prev };
            
            // Add translations with locale-namespace prefix for easy lookup
            Object.entries(response.data!.translations).forEach(([key, value]) => {
              // Only use non-empty content
              if (typeof value === 'string' && value.trim().length > 0) {
                newTranslations[`${namespace}.${key}`] = value;
                newTranslations[`${locale}.${namespace}.${key}`] = value;
              }
            });
            
            return newTranslations;
          });

          // Mark namespace as loaded
          loadedNamespaces.current.add(cacheKey);
          console.log(`✅ Loaded translations from API for ${namespace} (${locale})`);
        } else {
          throw new Error('API returned empty content, using fallbacks');
        }
      } else {
        throw new Error(response.error || 'API response unsuccessful');
      }
    } catch (err) {
      // API failed, use fallback translations
      console.warn(`⚠️ API failed for ${namespace} (${locale}), using fallback translations:`, err instanceof Error ? err.message : err);
      
      // Load fallback translations
      const fallbackData = fallbackTranslations[namespace as keyof typeof fallbackTranslations]?.[locale as keyof typeof fallbackTranslations.ui];
      
      if (fallbackData) {
        setTranslations(prev => {
          const newTranslations = { ...prev };
          
          // Add fallback translations
          Object.entries(fallbackData).forEach(([key, value]) => {
            newTranslations[`${namespace}.${key}`] = value;
            newTranslations[`${locale}.${namespace}.${key}`] = value;
          });
          
          return newTranslations;
        });

        // Mark namespace as loaded (with fallback)
        loadedNamespaces.current.add(cacheKey);
        console.log(`✅ Loaded fallback translations for ${namespace} (${locale}) - ${Object.keys(fallbackData).length} keys`);
        
        // Clear error since we have fallback data
        setError(null);
      } else {
        const errorMsg = `No fallback translations available for ${namespace} (${locale})`;
        setError(errorMsg);
        console.error('Translation loading failed:', errorMsg);
      }
    } finally {
      fetchingNamespaces.current.delete(cacheKey);
      setLoading(false);
    }
  }, [locale]);

  // Load default namespace on mount
  useEffect(() => {
    loadNamespace('ui'); // Load default UI namespace
  }, [loadNamespace]);

  // Reload UI namespace when locale changes
  useEffect(() => {
    loadNamespace('ui'); // Reload UI namespace for new locale
  }, [locale, loadNamespace]);

  // Context value
  const contextValue = useMemo((): TranslationContextValue => ({
    locale,
    setLocale,
    translations,
    loading,
    error,
    loadNamespace,
    supportedLocales: SUPPORTED_LOCALES,
  }), [locale, setLocale, translations, loading, error, loadNamespace]);

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

/**
 * HOC for wrapping components with translation provider
 */
export function withTranslation<P extends object>(
  Component: React.ComponentType<P>,
  options?: { defaultLocale?: string; fallbackLocale?: string }
) {
  return function WrappedComponent(props: P) {
    return (
      <TranslationProvider {...options}>
        <Component {...props} />
      </TranslationProvider>
    );
  };
}