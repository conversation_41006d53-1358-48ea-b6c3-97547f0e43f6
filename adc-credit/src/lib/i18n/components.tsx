/**
 * Translation components for Credit Service i18n
 */

'use client';

import React from 'react';
import { useTranslation } from './hooks';

export interface TProps {
  children: string;
  namespace?: string;
  variables?: Record<string, string | number>;
  fallback?: string;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Simple translation component for inline text
 * Usage: <T>welcome_message</T> or <T namespace="dashboard">credit_balance</T>
 */
export function T({ 
  children, 
  namespace = 'ui', 
  variables, 
  fallback,
  className,
  as: Component = 'span',
  ...props 
}: TProps) {
  const { t } = useTranslation(namespace);
  
  const translatedText = t(children, variables, fallback);
  
  return (
    <Component className={className} {...props}>
      {translatedText}
    </Component>
  );
}

export interface TranslateProps {
  i18nKey: string;
  namespace?: string;
  variables?: Record<string, string | number>;
  fallback?: string;
  children?: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

/**
 * More verbose translation component with explicit props
 * Usage: <Translate i18nKey="welcome_message" namespace="ui" variables={{name: "<PERSON>"}} />
 */
export function Translate({ 
  i18nKey,
  namespace = 'ui',
  variables,
  fallback,
  children,
  className,
  as: Component = 'span',
  ...props
}: TranslateProps) {
  const { t } = useTranslation(namespace);
  
  const translatedText = t(i18nKey, variables, fallback);
  
  return (
    <Component className={className} {...props}>
      {children || translatedText}
    </Component>
  );
}

export interface TransProps {
  i18nKey: string;
  namespace?: string;
  variables?: Record<string, string | number>;
  fallback?: string;
  components?: Record<string, React.ComponentType<any>>;
  className?: string;
}

/**
 * Advanced translation component with component interpolation
 * Usage: <Trans i18nKey="click_here_link" components={{link: <a href="/signup" />}} />
 * Template: "Please {{link}}click here{{/link}} to continue"
 */
export function Trans({ 
  i18nKey,
  namespace = 'ui',
  variables,
  fallback,
  components = {},
  className,
  ...props
}: TransProps) {
  const { t } = useTranslation(namespace);
  
  let translatedText = t(i18nKey, variables, fallback);
  
  // Handle component interpolation
  if (components && Object.keys(components).length > 0) {
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    
    // Find component tags like {{link}}text{{/link}}
    const componentRegex = /\{\{(\w+)\}\}(.*?)\{\{\/\1\}\}/g;
    let match;
    
    while ((match = componentRegex.exec(translatedText)) !== null) {
      const [fullMatch, componentName, content] = match;
      const Component = components[componentName];
      
      // Add text before this component
      if (match.index > lastIndex) {
        parts.push(translatedText.slice(lastIndex, match.index));
      }
      
      // Add the component
      if (Component) {
        parts.push(
          <Component key={`${componentName}-${match.index}`}>
            {content}
          </Component>
        );
      } else {
        // If component not found, just add the content
        parts.push(content);
      }
      
      lastIndex = match.index + fullMatch.length;
    }
    
    // Add remaining text
    if (lastIndex < translatedText.length) {
      parts.push(translatedText.slice(lastIndex));
    }
    
    // If we found components, return the parts
    if (parts.length > 0) {
      return (
        <span className={className} {...props}>
          {parts}
        </span>
      );
    }
  }
  
  // No component interpolation, return simple text
  return (
    <span className={className} {...props}>
      {translatedText}
    </span>
  );
}

export interface PluralProps {
  count: number;
  i18nKey: string;
  namespace?: string;
  variables?: Record<string, string | number>;
  fallback?: string;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Pluralization component
 * Usage: <Plural count={items.length} i18nKey="item" />
 * Keys: "item_one", "item_other"
 */
export function Plural({ 
  count,
  i18nKey,
  namespace = 'ui',
  variables,
  fallback,
  className,
  as: Component = 'span',
  ...props
}: PluralProps) {
  const { t, locale } = useTranslation(namespace);
  
  // Determine plural rule
  const pluralRules = new Intl.PluralRules(locale);
  const rule = pluralRules.select(count);
  
  // Try specific plural key first (e.g., "item_one", "item_other")
  const pluralKey = `${i18nKey}_${rule}`;
  let translatedText = t(pluralKey, { count, ...variables });
  
  // If plural key doesn't exist, try base key
  if (translatedText === pluralKey) {
    translatedText = t(i18nKey, { count, ...variables }, fallback);
  }
  
  return (
    <Component className={className} {...props}>
      {translatedText}
    </Component>
  );
}

export interface NumberProps {
  value: number;
  locale?: string;
  format?: 'decimal' | 'currency' | 'percent';
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  className?: string;
}

/**
 * Localized number formatting component
 * Usage: <Number value={1234.56} format="currency" currency="USD" />
 */
export function Number({ 
  value,
  locale: propLocale,
  format = 'decimal',
  currency = 'USD',
  minimumFractionDigits,
  maximumFractionDigits,
  className,
  ...props
}: NumberProps) {
  const { locale: contextLocale } = useTranslation();
  const locale = propLocale || contextLocale;
  
  const formatOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits,
    maximumFractionDigits,
  };
  
  if (format === 'currency') {
    formatOptions.style = 'currency';
    formatOptions.currency = currency;
  } else if (format === 'percent') {
    formatOptions.style = 'percent';
  }
  
  const formattedValue = new Intl.NumberFormat(locale, formatOptions).format(value);
  
  return (
    <span className={className} {...props}>
      {formattedValue}
    </span>
  );
}

export interface DateProps {
  value: Date | string | number;
  locale?: string;
  format?: 'short' | 'medium' | 'long' | 'full' | 'relative';
  className?: string;
}

/**
 * Localized date formatting component
 * Usage: <DateComponent value={new Date()} format="medium" />
 */
export function DateComponent({ 
  value,
  locale: propLocale,
  format = 'medium',
  className,
  ...props
}: DateProps) {
  const { locale: contextLocale } = useTranslation();
  const locale = propLocale || contextLocale;
  
  const date = typeof value === 'string' || typeof value === 'number' 
    ? new Date(value) 
    : value;
  
  let formattedValue: string;
  
  if (format === 'relative') {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (Math.abs(diffInDays) < 1) {
      const diffInHours = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60));
      formattedValue = rtf.format(diffInHours, 'hour');
    } else {
      formattedValue = rtf.format(diffInDays, 'day');
    }
  } else {
    const formatOptions: Intl.DateTimeFormatOptions = {};
    
    switch (format) {
      case 'short':
        formatOptions.dateStyle = 'short';
        break;
      case 'medium':
        formatOptions.dateStyle = 'medium';
        break;
      case 'long':
        formatOptions.dateStyle = 'long';
        break;
      case 'full':
        formatOptions.dateStyle = 'full';
        break;
    }
    
    formattedValue = new Intl.DateTimeFormat(locale, formatOptions).format(date);
  }
  
  return (
    <time className={className} dateTime={date.toISOString()} {...props}>
      {formattedValue}
    </time>
  );
}