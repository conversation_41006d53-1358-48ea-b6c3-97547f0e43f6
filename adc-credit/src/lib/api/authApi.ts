import { User } from 'next-auth';
import { baseApi } from './baseApi';

export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Authentication endpoints
    authenticateWithGoogle: builder.mutation<{ token: string; refresh_token: string; user: User }, { token: string }>({
      query: (data) => ({
        url: '/auth/google',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Auth', 'User'],
    }),

    refreshToken: builder.mutation<{ token: string; refresh_token: string }, { refresh_token: string }>({
      query: (data) => ({
        url: '/auth/refresh',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Auth'],
    }),
  }),
});

export const {
  useAuthenticateWithGoogleMutation,
  useRefreshTokenMutation,
} = authApi;