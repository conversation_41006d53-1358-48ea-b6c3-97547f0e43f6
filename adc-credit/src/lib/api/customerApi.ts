import { Shop, Transaction } from '@/types';
import { baseApi } from './baseApi';

export const customerApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Customer endpoints
    getCustomerShops: builder.query<Shop[], void>({
      query: () => '/customer/shops',
      providesTags: ['Shops'],
    }),

    getCustomerShop: builder.query<{ shop: Shop; credit_balance: number }, string>({
      query: (shopId) => `/customer/shops/${shopId}`,
      providesTags: (result, error, id) => [{ type: 'Shops', id }],
    }),

    getCustomerShopBySlug: builder.query<{ shop: Shop; credit_balance: number }, string>({
      query: (shopSlug) => `/customer/shops/slug/${shopSlug}`,
      providesTags: (result, error, slug) => [{ type: 'Shops', id: result?.shop?.id }],
    }),

    getCustomerTransactions: builder.query<Transaction[], string>({
      query: (shopId) => `/customer/shops/${shopId}/transactions`,
      providesTags: ['ShopCreditTransactions'],
    }),

    getCustomerTransactionsBySlug: builder.query<Transaction[], string>({
      query: (shopSlug) => `/customer/shops/slug/${shopSlug}/transactions`,
      providesTags: ['ShopCreditTransactions'],
    }),

    useShopCredit: builder.mutation<
      { message: string; credit_balance: number; transaction: Transaction },
      { shopId: string; amount: number; description?: string }
    >({
      query: ({ shopId, ...data }) => ({
        url: `/customer/shops/${shopId}/use-credit`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ShopCreditTransactions', 'Shops'],
    }),

    useShopCreditBySlug: builder.mutation<
      { message: string; credit_balance: number; transaction: Transaction },
      { shopSlug: string; amount: number; description?: string }
    >({
      query: ({ shopSlug, ...data }) => ({
        url: `/customer/shops/slug/${shopSlug}/use-credit`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ShopCreditTransactions', 'Shops'],
    }),

    redeemCreditCode: builder.mutation<
      { message: string; credit_balance: number; shop_name: string; transaction: Transaction },
      { code: string }
    >({
      query: (data) => ({
        url: '/customer/redeem-code',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ShopCreditTransactions', 'Shops'],
    }),
  }),
});

export const {
  // Customer hooks
  useGetCustomerShopsQuery,
  useGetCustomerShopQuery,
  useGetCustomerShopBySlugQuery,
  useGetCustomerTransactionsQuery,
  useGetCustomerTransactionsBySlugQuery,
  useUseShopCreditMutation,
  useUseShopCreditBySlugMutation,
  useRedeemCreditCodeMutation,
} = customerApi;