import { baseApi } from './baseApi';
import { Organization, Branch, ExternalUser, ExternalUserCreditResponse } from '@/types';

export const organizationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Organization endpoints
    getOrganizations: builder.query<Organization[], void>({
      query: () => `/organizations`,
      providesTags: ['Organizations'],
    }),

    getOrganization: builder.query<Organization, string>({
      query: (slug) => `/organizations/${slug}`,
      providesTags: (result) => [{ type: 'Organizations', id: result?.id }],
    }),

    // Legacy endpoint for backward compatibility
    getOrganizationById: builder.query<Organization, string>({
      query: (id) => `/organizations/id/${id}`,
      providesTags: (_, __, id) => [{ type: 'Organizations', id }],
    }),

    createOrganization: builder.mutation<Organization, { name: string; description?: string }>({
      query: (data) => ({
        url: `/organizations`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Organizations'],
    }),

    updateOrganization: builder.mutation<Organization, { slug: string; name?: string; description?: string }>({
      query: ({ slug, ...data }) => ({
        url: `/organizations/${slug}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result) => [
        'Organizations',
        { type: 'Organizations', id: result?.id }
      ],
    }),

    deleteOrganization: builder.mutation<{ message: string }, string>({
      query: (slug) => ({
        url: `/organizations/${slug}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Organizations'],
    }),

    // Branch endpoints
    getBranches: builder.query<Branch[], string>({
      query: (organizationId) => `/org-branches?organization_id=${organizationId}`,
      providesTags: ['Branches'],
    }),

    getBranch: builder.query<Branch, { id: string; organizationId: string }>({
      query: ({ id, organizationId }) => `/org-branches/${id}?organization_id=${organizationId}`,
      providesTags: (_, __, { id }) => [{ type: 'Branches', id }],
    }),

    createBranch: builder.mutation<Branch, { organizationId: string; name: string; description?: string }>({
      query: ({ organizationId, ...data }) => ({
        url: `/org-branches?organization_id=${organizationId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Branches'],
    }),

    updateBranch: builder.mutation<Branch, { id: string; organizationId: string; name?: string; description?: string }>({
      query: ({ id, organizationId, ...data }) => ({
        url: `/org-branches/${id}?organization_id=${organizationId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [
        'Branches',
        { type: 'Branches', id }
      ],
    }),

    deleteBranch: builder.mutation<{ message: string }, { id: string; organizationId: string }>({
      query: ({ id, organizationId }) => ({
        url: `/org-branches/${id}?organization_id=${organizationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Branches'],
    }),

    // External User endpoints
    getExternalUsers: builder.query<ExternalUser[], { organizationId: string; branchId: string }>({
      query: ({ organizationId, branchId }) =>
        `/org-users?organization_id=${organizationId}&branch_id=${branchId}`,
      providesTags: ['ExternalUsers'],
    }),

    getExternalUser: builder.query<ExternalUser, { id: string; organizationId: string; branchId: string }>({
      query: ({ id, organizationId, branchId }) =>
        `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
      providesTags: (_, __, { id }) => [{ type: 'ExternalUsers', id }],
    }),

    createExternalUser: builder.mutation<ExternalUser, {
      organizationId: string;
      branchId: string;
      name: string;
      email: string;
      external_user_identifier: string;
      monthly_credits: number;
    }>({
      query: ({ organizationId, branchId, ...data }) => ({
        url: `/org-users?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ExternalUsers'],
    }),

    updateExternalUser: builder.mutation<ExternalUser, {
      id: string;
      organizationId: string;
      branchId: string;
      name?: string;
      monthly_credits?: number;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),

    deleteExternalUser: builder.mutation<{ message: string }, { id: string; organizationId: string; branchId: string }>({
      query: ({ id, organizationId, branchId }) => ({
        url: `/org-users/${id}?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ExternalUsers'],
    }),

    addCreditsToExternalUser: builder.mutation<ExternalUserCreditResponse, {
      id: string;
      organizationId: string;
      branchId: string;
      amount: number;
      description?: string;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}/credits/add?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),

    reduceCreditsFromExternalUser: builder.mutation<ExternalUserCreditResponse, {
      id: string;
      organizationId: string;
      branchId: string;
      amount: number;
      description?: string;
    }>({
      query: ({ id, organizationId, branchId, ...data }) => ({
        url: `/org-users/${id}/credits/reduce?organization_id=${organizationId}&branch_id=${branchId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [
        'ExternalUsers',
        { type: 'ExternalUsers', id }
      ],
    }),
  }),
});

export const {
  // Organization hooks
  useGetOrganizationsQuery,
  useGetOrganizationQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  // Branch hooks
  useGetBranchesQuery,
  useGetBranchQuery,
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
  // External User hooks
  useGetExternalUsersQuery,
  useGetExternalUserQuery,
  useCreateExternalUserMutation,
  useUpdateExternalUserMutation,
  useDeleteExternalUserMutation,
  useAddCreditsToExternalUserMutation,
  useReduceCreditsFromExternalUserMutation,
} = organizationApi;