import { APIKey, Subscription, Transaction, Usage, UsageSummary, Webhook, WebhookDelivery } from '@/types';
import { baseApi } from './baseApi';

export const creditsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Credit endpoints
    getCreditBalance: builder.query<{
      credit_balance: number;
      credit_limit: number;
      subscription: Subscription;
    }, void>({
      query: () => '/credits',
      providesTags: ['Credits'],
    }),

    getTransactions: builder.query<Transaction[], void>({
      query: () => '/credits/transactions',
      providesTags: ['Credits'],
    }),

    getNextScheduledCreditDate: builder.query<{ next_scheduled_credit_date: string }, void>({
      query: () => '/credits/scheduled/next',
      providesTags: ['Credits'],
    }),

    getScheduledCreditHistory: builder.query<Transaction[], void>({
      query: () => '/credits/scheduled/history',
      providesTags: ['Credits'],
    }),

    processScheduledCredits: builder.mutation<{ message: string; count: number }, void>({
      query: () => ({
        url: '/admin/credits/scheduled/process',
        method: 'POST',
      }),
      invalidatesTags: ['Credits'],
    }),

    manuallyAddScheduledCredits: builder.mutation<
      { message: string; user_id: string; credit_balance: number },
      string
    >({
      query: (userId) => ({
        url: '/admin/credits/scheduled/manual',
        method: 'POST',
        body: { user_id: userId },
      }),
      invalidatesTags: ['Credits'],
    }),

    // API Key endpoints
    getAPIKeys: builder.query<APIKey[], void>({
      query: () => '/apikeys',
      providesTags: ['APIKeys'],
    }),

    createAPIKey: builder.mutation<APIKey, { name: string; permissions?: string[] }>({
      query: (data) => ({
        url: '/apikeys',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['APIKeys'],
    }),

    updateAPIKey: builder.mutation<APIKey, { id: string; name?: string; enabled?: boolean; permissions?: string[] }>({
      query: ({ id, ...data }) => ({
        url: `/apikeys/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['APIKeys'],
    }),

    deleteAPIKey: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/apikeys/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['APIKeys'],
    }),

    // Usage endpoints
    getUsageSummary: builder.query<UsageSummary, { period?: string; start_date?: string; end_date?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.period) queryParams.append('period', params.period);
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/usage/summary${query}`;
      },
      providesTags: ['Usage'],
    }),

    getUsage: builder.query<Usage[], { start_date?: string; end_date?: string; api_key_id?: string }>({
      query: (params) => {
        const queryParams = new URLSearchParams();
        if (params?.start_date) queryParams.append('start_date', params.start_date);
        if (params?.end_date) queryParams.append('end_date', params.end_date);
        if (params?.api_key_id) queryParams.append('api_key_id', params.api_key_id);

        const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
        return `/usage${query}`;
      },
      providesTags: ['Usage'],
    }),

    // Webhook endpoints
    getWebhooks: builder.query<Webhook[], void>({
      query: () => '/webhooks',
      providesTags: ['Webhooks'],
    }),

    getWebhook: builder.query<Webhook, string>({
      query: (id) => `/webhooks/${id}`,
      providesTags: (result, error, id) => [{ type: 'Webhooks', id }],
    }),

    createWebhook: builder.mutation<
      Webhook,
      { name: string; url: string; secret?: string; events: string[] }
    >({
      query: (data) => ({
        url: '/webhooks',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Webhooks'],
    }),

    updateWebhook: builder.mutation<
      Webhook,
      { id: string; data: { name?: string; url?: string; secret?: string; events?: string[]; active?: boolean } }
    >({
      query: ({ id, data }) => ({
        url: `/webhooks/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Webhooks',
        { type: 'Webhooks', id }
      ],
    }),

    deleteWebhook: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/webhooks/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Webhooks'],
    }),

    getWebhookDeliveries: builder.query<WebhookDelivery[], string>({
      query: (id) => `/webhooks/${id}/deliveries`,
      providesTags: (result, error, id) => [{ type: 'WebhookDeliveries', id }],
    }),
  }),
});

export const {
  // Credit hooks
  useGetCreditBalanceQuery,
  useGetTransactionsQuery,
  useGetNextScheduledCreditDateQuery,
  useGetScheduledCreditHistoryQuery,
  useProcessScheduledCreditsMutation,
  useManuallyAddScheduledCreditsMutation,
  // API Key hooks
  useGetAPIKeysQuery,
  useCreateAPIKeyMutation,
  useUpdateAPIKeyMutation,
  useDeleteAPIKeyMutation,
  // Usage hooks
  useGetUsageSummaryQuery,
  useGetUsageQuery,
  // Webhook hooks
  useGetWebhooksQuery,
  useGetWebhookQuery,
  useCreateWebhookMutation,
  useUpdateWebhookMutation,
  useDeleteWebhookMutation,
  useGetWebhookDeliveriesQuery,
} = creditsApi;