import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { signOut } from 'next-auth/react';
import { createCorrelationHeaders, extractCorrelationId, logWithCorrelation, logErrorWithCorrelation } from '@/lib/correlation';

// Use the Next.js proxy instead of direct backend calls
const API_URL = '/api';

// Custom fetch function that relies on NextAuth session management
const customFetch = async (url: RequestInfo, options: RequestInit = {}) => {
  // Create correlation headers for request tracing
  const correlationHeaders = createCorrelationHeaders(options.headers);
  
  // Ensure credentials are included
  const fetchOptions: RequestInit = {
    ...options,
    credentials: 'include', // Always include cookies for NextAuth session
    headers: {
      'Content-Type': 'application/json',
      ...correlationHeaders,
    },
  };

  try {
    // Log the request with correlation ID
    const correlationId = correlationHeaders['X-Correlation-ID'] as string;
    logWithCorrelation(`API Request: ${options.method || 'GET'} ${url}`, undefined, correlationId);
    
    // Use the native fetch with our enhanced options
    const response = await fetch(url, fetchOptions);

    // Extract correlation ID from response for logging
    const responseCorrelationId = extractCorrelationId(response);
    
    // Log response with correlation ID
    logWithCorrelation(`API Response: ${response.status} ${response.statusText}`, undefined, responseCorrelationId || correlationId);

    // Handle 401 Unauthorized responses - automatically sign out user
    if (response.status === 401) {
      logWithCorrelation('401 Unauthorized - signing out user', undefined, responseCorrelationId || correlationId);
      try {
        await signOut({ redirect: true, callbackUrl: '/auth/signin' });
      } catch {
        // Fallback: redirect manually
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/signin';
        }
      }
      
      return response;
    }

    return response;
  } catch (error) {
    const correlationId = correlationHeaders['X-Correlation-ID'] as string;
    logErrorWithCorrelation(error instanceof Error ? error : String(error), correlationId);
    throw error;
  }
};

// Base API configuration
export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    fetchFn: customFetch, // Use our custom fetch function
    credentials: 'include', // Belt and suspenders - ensure cookies are included
  }),
  tagTypes: [
    'Auth',
    'User',
    'Credits',
    'APIKeys',
    'Usage',
    'Webhooks',
    'WebhookDeliveries',
    'Analytics',
    'AnalyticsSummary',
    'AnalyticsTrends',
    'EndpointAnalytics',
    'PerformanceMetrics',
    'Organizations',
    'Branches',
    'ExternalUsers',
    'Subscriptions',
    'Shops',
    'ShopSlugs',
    'ShopCustomers',
    'CreditCodes',
    'ShopCreditTransactions',
    'ShopStats',
    'ShopAPIKeys',
    'ShopBranchAPIKeys',
    'ShopExchangeRates'
  ],
  endpoints: () => ({}),
});