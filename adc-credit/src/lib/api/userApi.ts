import { User } from 'next-auth';
import { baseApi } from './baseApi';

export const userApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // User endpoints
    getCurrentUser: builder.query<User, void>({
      query: () => `/users/me`,
      providesTags: ['User'],
    }),

    updateUser: builder.mutation<User, { name?: string; picture?: string }>({
      query: (data) => ({
        url: `/users/me`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),
  }),
});

export const {
  useGetCurrentUserQuery,
  useUpdateUserMutation,
} = userApi;