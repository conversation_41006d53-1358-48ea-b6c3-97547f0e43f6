import { APIKey, Shop, ShopCustomer, CreditCode, Transaction, ShopExchangeRate, CreateExchangeRate, UpdateExchangeRate, CurrencyConversion } from '@/types';
import { baseApi } from './baseApi';

export const shopApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Shop endpoints
    getShops: builder.query<Shop[], void>({
      query: () => '/shops',
      providesTags: ['Shops'],
    }),

    getShop: builder.query<Shop, string>({
      query: (id) => `/shops/${id}`,
      providesTags: (_, __, id) => [{ type: 'Shops', id }],
    }),

    getShopBySlug: builder.query<Shop, string>({
      query: (slug) => `/shops/slug/${slug}`,
      providesTags: (result, _, slug) => [
        { type: 'Shops', id: result?.id },
        { type: 'ShopSlugs', id: slug }
      ],
      // Enable keepUnusedDataFor for better caching
      keepUnusedDataFor: 300, // 5 minutes
    }),

    createShop: builder.mutation<Shop, {
      name: string;
      description?: string;
      shop_type?: 'retail' | 'api_service' | 'enterprise';
      contact_email?: string;
      contact_phone?: string;
    }>({
      query: (data) => ({
        url: '/shops',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Shops'],
    }),

    updateShop: builder.mutation<Shop, {
      id: string;
      name?: string;
      description?: string;
      shop_type?: 'retail' | 'api_service' | 'enterprise';
      contact_email?: string;
      contact_phone?: string;
    }>({
      query: ({ id, ...data }) => ({
        url: `/shops/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_, __, { id }) => [
        'Shops',
        { type: 'Shops', id }
      ],
    }),

    deleteShop: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Shops'],
    }),

    // Shop Customer endpoints
    getShopCustomers: builder.query<{
      customers: ShopCustomer[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }, {
      shopId: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      minCreditBalance?: number;
      maxCreditBalance?: number;
    }>({
      query: ({ shopId, search, page = 1, limit = 10, sortBy, sortOrder, minCreditBalance, maxCreditBalance }) => {
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (page) params.append('page', page.toString());
        if (limit) params.append('limit', limit.toString());
        if (sortBy) params.append('sortBy', sortBy);
        if (sortOrder) params.append('sortOrder', sortOrder);
        if (minCreditBalance !== undefined) params.append('minCreditBalance', minCreditBalance.toString());
        if (maxCreditBalance !== undefined) params.append('maxCreditBalance', maxCreditBalance.toString());

        const queryString = params.toString();
        return `/shops/${shopId}/customers${queryString ? `?${queryString}` : ''}`;
      },
      providesTags: (result, error, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    getShopCustomersBySlug: builder.query<{
      customers: ShopCustomer[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }, {
      shopSlug: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      minCreditBalance?: number;
      maxCreditBalance?: number;
    }>({
      query: ({ shopSlug, search, page = 1, limit = 10, sortBy, sortOrder, minCreditBalance, maxCreditBalance }) => {
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (page) params.append('page', page.toString());
        if (limit) params.append('limit', limit.toString());
        if (sortBy) params.append('sortBy', sortBy);
        if (sortOrder) params.append('sortOrder', sortOrder);
        if (minCreditBalance !== undefined) params.append('minCreditBalance', minCreditBalance.toString());
        if (maxCreditBalance !== undefined) params.append('maxCreditBalance', maxCreditBalance.toString());

        const queryString = params.toString();
        return `/shops/slug/${shopSlug}/customers${queryString ? `?${queryString}` : ''}`;
      },
      providesTags: (result, error, { shopSlug }) => [{ type: 'ShopCustomers', id: shopSlug }],
    }),

    addShopCustomer: builder.mutation<ShopCustomer, {
      shopId: string;
      email: string;
      name: string;
      phone?: string;
    }>({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/customers`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_, __, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    addShopCredit: builder.mutation<
      { message: string; credit_balance: number; transaction: Transaction },
      { shopId: string; customerId: string; amount: number; description?: string }
    >({
      query: ({ shopId, customerId, ...data }) => ({
        url: `/shops/${shopId}/customers/${customerId}/credits`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_, __, { shopId }) => [{ type: 'ShopCustomers', id: shopId }],
    }),

    // Generate QR code for an existing credit code
    generateQRCode: builder.mutation<{ qr_code: string }, { shopId: string; code: string; amount: number }>({
      query: ({ shopId, code, amount }) => ({
        url: `/shops/${shopId}/credit-codes/qr`,
        method: 'POST',
        body: { code, amount },
      }),
    }),

    // Credit Code endpoints
    getShopTransactions: builder.query<Transaction[], string>({
      query: (shopId) => `/shops/${shopId}/transactions`,
      providesTags: ['ShopCreditTransactions'],
    }),

    getCreditCodes: builder.query<CreditCode[], string>({
      query: (shopId) => `/shops/${shopId}/credit-codes`,
      providesTags: ['CreditCodes'],
    }),

    generateCreditCode: builder.mutation<
      { credit_code: CreditCode; qr_code: string },
      { shopId: string; amount: number; description?: string; expiresIn?: number }
    >({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/credit-codes`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditCodes'],
    }),

    // Shop Statistics endpoints
    getShopStats: builder.query<{
      shop_id: string;
      shop_name: string;
      shop_type: string;
      total_customers: number;
      total_credit_codes: number;
      total_credits_issued: number;
      total_credits_redeemed: number;
      active_credit_codes: number;
      recent_transactions: number;
      total_customer_balance: number;
      redemption_rate: number;
    }, string>({
      query: (shopId) => `/shops/${shopId}/stats`,
      providesTags: (_, __, shopId) => [
        { type: 'ShopStats', id: shopId },
        'ShopStats'
      ],
    }),

    // Shop API Key Management endpoints
    getShopAPIKeys: builder.query<APIKey[], string>({
      query: (shopId) => `/shops/${shopId}/apikeys`,
      transformResponse: (response: APIKey[]) => {
        // Sort by creation date (newest first) to maintain consistent order
        return response.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      },
      providesTags: (_, __, shopId) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    createShopAPIKey: builder.mutation<APIKey, {
      shopId: string;
      name: string;
      permissions?: string[];
    }>({
      query: ({ shopId, ...data }) => ({
        url: `/shops/${shopId}/apikeys`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { shopId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    getShopAPIKey: builder.query<APIKey, { shopId: string; keyId: string }>({
      query: ({ shopId, keyId }) => `/shops/${shopId}/apikeys/${keyId}`,
      providesTags: (result, error, { shopId, keyId }) => [
        { type: 'ShopAPIKeys', id: `${shopId}-${keyId}` }
      ],
    }),

    updateShopAPIKey: builder.mutation<APIKey, {
      shopId: string;
      keyId: string;
      name?: string;
      permissions?: string[];
      enabled?: boolean;
    }>({
      query: ({ shopId, keyId, ...data }) => ({
        url: `/shops/${shopId}/apikeys/${keyId}`,
        method: 'PUT',
        body: data,
      }),
      async onQueryStarted({ shopId, keyId, ...patch }, { dispatch, queryFulfilled }) {
        // Optimistic update
        const patchResult = dispatch(
          shopApi.util.updateQueryData('getShopAPIKeys', shopId, (draft) => {
            const apiKey = draft.find((key) => key.id === keyId);
            if (apiKey) {
              Object.assign(apiKey, patch);
            }
            // Maintain consistent sorting after update
            draft.sort((a, b) => 
              new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );
          })
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      invalidatesTags: (result, error, { shopId, keyId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        { type: 'ShopAPIKeys', id: `${shopId}-${keyId}` },
        'ShopAPIKeys'
      ],
    }),

    deleteShopAPIKey: builder.mutation<{ message: string }, {
      shopId: string;
      keyId: string;
    }>({
      query: ({ shopId, keyId }) => ({
        url: `/shops/${shopId}/apikeys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { shopId }) => [
        { type: 'ShopAPIKeys', id: shopId },
        'ShopAPIKeys'
      ],
    }),

    // Shop Branch API Key Management endpoints
    getShopBranchAPIKeys: builder.query<APIKey[], string>({
      query: (branchId) => `/shops/branches/${branchId}/apikeys`,
      providesTags: (result, error, branchId) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

    createShopBranchAPIKey: builder.mutation<APIKey, {
      branchId: string;
      name: string;
      permissions?: string[];
    }>({
      query: ({ branchId, ...data }) => ({
        url: `/shops/branches/${branchId}/apikeys`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

    getShopBranchAPIKey: builder.query<APIKey, { branchId: string; keyId: string }>({
      query: ({ branchId, keyId }) => `/shops/branches/${branchId}/apikeys/${keyId}`,
      providesTags: (result, error, { branchId, keyId }) => [
        { type: 'ShopBranchAPIKeys', id: `${branchId}-${keyId}` }
      ],
    }),

    updateShopBranchAPIKey: builder.mutation<APIKey, {
      branchId: string;
      keyId: string;
      name?: string;
      permissions?: string[];
      enabled?: boolean;
    }>({
      query: ({ branchId, keyId, ...data }) => ({
        url: `/shops/branches/${branchId}/apikeys/${keyId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId, keyId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        { type: 'ShopBranchAPIKeys', id: `${branchId}-${keyId}` },
        'ShopBranchAPIKeys'
      ],
    }),

    deleteShopBranchAPIKey: builder.mutation<{ message: string }, {
      branchId: string;
      keyId: string;
    }>({
      query: ({ branchId, keyId }) => ({
        url: `/shops/branches/${branchId}/apikeys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { branchId }) => [
        { type: 'ShopBranchAPIKeys', id: branchId },
        'ShopBranchAPIKeys'
      ],
    }),

    // Exchange Rate Management endpoints
    getShopExchangeRates: builder.query<ShopExchangeRate[], string>({
      query: (shopSlug) => `/shops/slug/${shopSlug}/exchange-rates`,
      providesTags: (result, error, shopSlug) => [
        { type: 'ShopExchangeRates', id: shopSlug },
        'ShopExchangeRates'
      ],
    }),

    createExchangeRate: builder.mutation<ShopExchangeRate, {
      shopSlug: string;
      data: CreateExchangeRate;
    }>({
      query: ({ shopSlug, data }) => ({
        url: `/shops/slug/${shopSlug}/exchange-rates`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { shopSlug }) => [
        { type: 'ShopExchangeRates', id: shopSlug },
        'ShopExchangeRates'
      ],
    }),

    updateExchangeRate: builder.mutation<ShopExchangeRate, {
      shopSlug: string;
      rateId: string;
      data: UpdateExchangeRate;
    }>({
      query: ({ shopSlug, rateId, data }) => ({
        url: `/shops/slug/${shopSlug}/exchange-rates/${rateId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { shopSlug }) => [
        { type: 'ShopExchangeRates', id: shopSlug },
        'ShopExchangeRates'
      ],
    }),

    deleteExchangeRate: builder.mutation<{ message: string }, {
      shopSlug: string;
      rateId: string;
    }>({
      query: ({ shopSlug, rateId }) => ({
        url: `/shops/slug/${shopSlug}/exchange-rates/${rateId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { shopSlug }) => [
        { type: 'ShopExchangeRates', id: shopSlug },
        'ShopExchangeRates'
      ],
    }),

    getActiveExchangeRate: builder.query<ShopExchangeRate, {
      shopSlug: string;
      currency: string;
    }>({
      query: ({ shopSlug, currency }) => `/shops/slug/${shopSlug}/exchange-rates/active?currency=${currency}`,
      providesTags: (result, error, { shopSlug, currency }) => [
        { type: 'ActiveExchangeRate', id: `${shopSlug}-${currency}` }
      ],
    }),

    convertCreditsToLocalCurrency: builder.query<CurrencyConversion, {
      shopSlug: string;
      currency: string;
      credits: number;
    }>({
      query: ({ shopSlug, currency, credits }) => 
        `/shops/slug/${shopSlug}/convert?currency=${currency}&credits=${credits}`,
    }),
  }),
});

export const {
  // Shop hooks
  useGetShopsQuery,
  useGetShopQuery,
  useGetShopBySlugQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useDeleteShopMutation,
  useGetShopCustomersQuery,
  useGetShopCustomersBySlugQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation,
  useGetCreditCodesQuery,
  useGetShopTransactionsQuery,
  useGenerateCreditCodeMutation,
  useGenerateQRCodeMutation,

  // Shop Statistics hooks
  useGetShopStatsQuery,

  // Shop API Key Management hooks
  useGetShopAPIKeysQuery,
  useCreateShopAPIKeyMutation,
  useGetShopAPIKeyQuery,
  useUpdateShopAPIKeyMutation,
  useDeleteShopAPIKeyMutation,

  // Shop Branch API Key Management hooks
  useGetShopBranchAPIKeysQuery,
  useCreateShopBranchAPIKeyMutation,
  useGetShopBranchAPIKeyQuery,
  useUpdateShopBranchAPIKeyMutation,
  useDeleteShopBranchAPIKeyMutation,

  // Exchange Rate Management hooks
  useGetShopExchangeRatesQuery,
  useCreateExchangeRateMutation,
  useUpdateExchangeRateMutation,
  useDeleteExchangeRateMutation,
  useGetActiveExchangeRateQuery,
  useConvertCreditsToLocalCurrencyQuery,
} = shopApi;