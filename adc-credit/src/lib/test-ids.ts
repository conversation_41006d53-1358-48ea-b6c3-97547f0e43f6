/**
 * Centralized Test ID Constants for ADC Credit Service
 * 
 * This file contains all test-id constants used throughout the Credit Service application.
 * Test IDs follow the naming convention: feature-component-action
 * Based on the Multi-Languages Service test ID system.
 * 
 * Usage:
 * - Import specific sections: import { SETTINGS_TEST_IDS } from '@/lib/test-ids'
 * - Use in components: <button data-testid={SETTINGS_TEST_IDS.USER.SAVE_BUTTON} />
 */

// Authentication Error Boundary Test IDs
export const AUTH_ERROR_BOUNDARY_TEST_IDS = {
  CONTAINER: 'auth-error-boundary-container',
  ERROR_TITLE: 'auth-error-boundary-error-title',
  ERROR_MESSAGE: 'auth-error-boundary-error-message',
  ERROR_DETAILS: 'auth-error-boundary-error-details',
  SIGN_OUT_BUTTON: 'auth-error-boundary-sign-out-button',
  RELOAD_BUTTON: 'auth-error-boundary-reload-button',
  CLOSE_BUTTON: 'auth-error-boundary-close-button',
} as const;

// Dashboard Test IDs (Generic Dashboard Components)
export const DASHBOARD_TEST_IDS = {
  CONTAINER: 'dashboard-container',
  HEADER: 'dashboard-header',
  TITLE: 'dashboard-title',
  LOADING_SPINNER: 'dashboard-loading-spinner',
  ERROR_MESSAGE: 'dashboard-error-message',
  REFRESH_BUTTON: 'dashboard-refresh-button',
  
  // Quick Actions
  QUICK_ACTIONS: {
    CONTAINER: 'dashboard-quick-actions-container',
    BUTTON: (action: string) => `dashboard-quick-actions-button-${action}`,
    CREATE_SHOP: 'dashboard-quick-actions-create-shop',
    GENERATE_QR: 'dashboard-quick-actions-generate-qr',
    VIEW_ANALYTICS: 'dashboard-quick-actions-view-analytics',
  },
  
  // Real Usage Card
  REAL_USAGE_CARD: {
    CONTAINER: 'dashboard-real-usage-card-container',
    TITLE: 'dashboard-real-usage-card-title',
    PERIOD_SELECTOR: 'dashboard-real-usage-card-period-selector',
    USAGE_CHART: 'dashboard-real-usage-card-usage-chart',
    VIEW_DETAILS_BUTTON: 'dashboard-real-usage-card-view-details-button',
  },
  
  // Scheduled Credits
  SCHEDULED_CREDITS: {
    CONTAINER: 'dashboard-scheduled-credits-container',
    TITLE: 'dashboard-scheduled-credits-title',
    NEXT_DATE: 'dashboard-scheduled-credits-next-date',
    AMOUNT: 'dashboard-scheduled-credits-amount',
    MANAGE_BUTTON: 'dashboard-scheduled-credits-manage-button',
  },
} as const;

// Settings Test IDs for ADC Settings Service Integration
export const SETTINGS_TEST_IDS = {
  // User Settings Page
  USER: {
    CONTAINER: 'settings-user-container',
    FORM: 'settings-user-form',
    THEME_SELECT: 'settings-user-theme-select',
    LANGUAGE_SELECT: 'settings-user-language-select',
    CURRENCY_SELECT: 'settings-user-currency-select',
    TIMEZONE_SELECT: 'settings-user-timezone-select',
    EMAIL_NOTIFICATIONS_TOGGLE: 'settings-user-email-notifications-toggle',
    PUSH_NOTIFICATIONS_TOGGLE: 'settings-user-push-notifications-toggle',
    SAVE_BUTTON: 'settings-user-save-button',
    RESET_BUTTON: 'settings-user-reset-button',
    SUCCESS_MESSAGE: 'settings-user-success-message',
    ERROR_MESSAGE: 'settings-user-error-message',
    LOADING_SPINNER: 'settings-user-loading-spinner',
  },
  
  // Shop Settings Page
  SHOP: {
    CONTAINER: 'settings-shop-container',
    FORM: 'settings-shop-form',
    
    // Shop General Settings
    GENERAL: {
      SECTION: 'settings-shop-general-section',
      NAME_INPUT: 'settings-shop-general-name-input',
      DESCRIPTION_INPUT: 'settings-shop-general-description-input',
      LOGO_UPLOAD: 'settings-shop-general-logo-upload',
      ADDRESS_INPUT: 'settings-shop-general-address-input',
      PHONE_INPUT: 'settings-shop-general-phone-input',
      EMAIL_INPUT: 'settings-shop-general-email-input',
      WEBSITE_INPUT: 'settings-shop-general-website-input',
    },
    
    // Shop QR Code Settings
    QR_CODE: {
      SECTION: 'settings-shop-qr-section',
      TEMPLATE_SELECT: 'settings-shop-qr-template-select',
      STYLE_SELECT: 'settings-shop-qr-style-select',
      COLOR_PICKER: 'settings-shop-qr-color-picker',
      BACKGROUND_COLOR: 'settings-shop-qr-background-color',
      FOREGROUND_COLOR: 'settings-shop-qr-foreground-color',
      SIZE_SLIDER: 'settings-shop-qr-size-slider',
      PREVIEW: 'settings-shop-qr-preview',
      GENERATE_BUTTON: 'settings-shop-qr-generate-button',
    },
    
    // Shop Payment Settings
    PAYMENT: {
      SECTION: 'settings-shop-payment-section',
      PAYMENT_METHODS_LIST: 'settings-shop-payment-methods-list',
      ADD_PAYMENT_BUTTON: 'settings-shop-payment-add-button',
      STRIPE_CONNECT_BUTTON: 'settings-shop-payment-stripe-connect-button',
      PAYPAL_CONNECT_BUTTON: 'settings-shop-payment-paypal-connect-button',
      CURRENCY_SELECT: 'settings-shop-payment-currency-select',
      TAX_RATE_INPUT: 'settings-shop-payment-tax-rate-input',
    },
    
    // Shop Analytics Settings
    ANALYTICS: {
      SECTION: 'settings-shop-analytics-section',
      TRACKING_ENABLED_TOGGLE: 'settings-shop-analytics-tracking-enabled-toggle',
      GOOGLE_ANALYTICS_INPUT: 'settings-shop-analytics-google-analytics-input',
      DATA_RETENTION_SELECT: 'settings-shop-analytics-data-retention-select',
      EXPORT_BUTTON: 'settings-shop-analytics-export-button',
    },
    
    // Shop Notification Settings
    NOTIFICATIONS: {
      SECTION: 'settings-shop-notifications-section',
      EMAIL_NOTIFICATIONS_TOGGLE: 'settings-shop-notifications-email-toggle',
      SMS_NOTIFICATIONS_TOGGLE: 'settings-shop-notifications-sms-toggle',
      WEBHOOK_URL_INPUT: 'settings-shop-notifications-webhook-url-input',
      WEBHOOK_SECRET_INPUT: 'settings-shop-notifications-webhook-secret-input',
      TEST_WEBHOOK_BUTTON: 'settings-shop-notifications-test-webhook-button',
    },
    
    // Action Buttons
    SAVE_BUTTON: 'settings-shop-save-button',
    RESET_BUTTON: 'settings-shop-reset-button',
    DELETE_SHOP_BUTTON: 'settings-shop-delete-button',
    SUCCESS_MESSAGE: 'settings-shop-success-message',
    ERROR_MESSAGE: 'settings-shop-error-message',
    LOADING_SPINNER: 'settings-shop-loading-spinner',
  },
  
  // Organization Settings Page
  ORGANIZATION: {
    CONTAINER: 'settings-organization-container',
    FORM: 'settings-organization-form',
    
    // Organization General Settings
    GENERAL: {
      SECTION: 'settings-organization-general-section',
      NAME_INPUT: 'settings-organization-general-name-input',
      DESCRIPTION_INPUT: 'settings-organization-general-description-input',
      LOGO_UPLOAD: 'settings-organization-general-logo-upload',
      WEBSITE_INPUT: 'settings-organization-general-website-input',
      INDUSTRY_SELECT: 'settings-organization-general-industry-select',
      SIZE_SELECT: 'settings-organization-general-size-select',
    },
    
    // Organization Subscription Settings
    SUBSCRIPTION: {
      SECTION: 'settings-organization-subscription-section',
      CURRENT_PLAN: 'settings-organization-subscription-current-plan',
      USAGE_DISPLAY: 'settings-organization-subscription-usage-display',
      UPGRADE_BUTTON: 'settings-organization-subscription-upgrade-button',
      DOWNGRADE_BUTTON: 'settings-organization-subscription-downgrade-button',
      BILLING_HISTORY_LINK: 'settings-organization-subscription-billing-history-link',
      PAYMENT_METHOD_EDIT: 'settings-organization-subscription-payment-method-edit',
    },
    
    // Organization API Settings
    API: {
      SECTION: 'settings-organization-api-section',
      API_KEYS_LIST: 'settings-organization-api-keys-list',
      CREATE_API_KEY_BUTTON: 'settings-organization-api-create-key-button',
      RATE_LIMIT_DISPLAY: 'settings-organization-api-rate-limit-display',
      WEBHOOK_SETTINGS: 'settings-organization-api-webhook-settings',
    },
    
    // Action Buttons
    SAVE_BUTTON: 'settings-organization-save-button',
    RESET_BUTTON: 'settings-organization-reset-button',
    SUCCESS_MESSAGE: 'settings-organization-success-message',
    ERROR_MESSAGE: 'settings-organization-error-message',
    LOADING_SPINNER: 'settings-organization-loading-spinner',
  },
  
  // Global Settings (Admin)
  GLOBAL: {
    CONTAINER: 'settings-global-container',
    FORM: 'settings-global-form',
    
    // System Settings
    SYSTEM: {
      SECTION: 'settings-global-system-section',
      MAINTENANCE_MODE_TOGGLE: 'settings-global-system-maintenance-mode-toggle',
      MAX_UPLOAD_SIZE_INPUT: 'settings-global-system-max-upload-size-input',
      SESSION_TIMEOUT_INPUT: 'settings-global-system-session-timeout-input',
      API_RATE_LIMIT_INPUT: 'settings-global-system-api-rate-limit-input',
    },
    
    // Security Settings
    SECURITY: {
      SECTION: 'settings-global-security-section',
      PASSWORD_POLICY_FORM: 'settings-global-security-password-policy-form',
      MIN_LENGTH_INPUT: 'settings-global-security-min-length-input',
      REQUIRE_UPPERCASE_TOGGLE: 'settings-global-security-require-uppercase-toggle',
      REQUIRE_LOWERCASE_TOGGLE: 'settings-global-security-require-lowercase-toggle',
      REQUIRE_NUMBERS_TOGGLE: 'settings-global-security-require-numbers-toggle',
      REQUIRE_SYMBOLS_TOGGLE: 'settings-global-security-require-symbols-toggle',
    },
    
    // Action Buttons
    SAVE_BUTTON: 'settings-global-save-button',
    RESET_BUTTON: 'settings-global-reset-button',
    SUCCESS_MESSAGE: 'settings-global-success-message',
    ERROR_MESSAGE: 'settings-global-error-message',
    LOADING_SPINNER: 'settings-global-loading-spinner',
  },
} as const;

// Dynamic Test ID Functions for Credit Service
export const CREDIT_DYNAMIC_TEST_IDS = {
  // Shop-specific settings
  SHOP_SETTING: (shopId: string, settingKey: string) => `settings-shop-${shopId}-${settingKey}`,
  SHOP_QR_CODE: (shopId: string) => `settings-shop-qr-code-${shopId}`,
  SHOP_PAYMENT_METHOD: (shopId: string, methodId: string) => `settings-shop-payment-method-${shopId}-${methodId}`,
  
  // API Key specific
  API_KEY_ROW: (keyId: string) => `settings-api-key-row-${keyId}`,
  API_KEY_REVOKE_BUTTON: (keyId: string) => `settings-api-key-revoke-${keyId}`,
  API_KEY_REGENERATE_BUTTON: (keyId: string) => `settings-api-key-regenerate-${keyId}`,
  
  // Customer specific (for shop settings)
  CUSTOMER_SETTING: (customerId: string, settingKey: string) => `settings-customer-${customerId}-${settingKey}`,
  
  // Exchange rate specific
  EXCHANGE_RATE_ROW: (currencyPair: string) => `settings-exchange-rate-${currencyPair}`,
  EXCHANGE_RATE_UPDATE: (currencyPair: string) => `settings-exchange-rate-update-${currencyPair}`,
} as const;

// Credit Service specific Test IDs (existing features)
export const CREDIT_SERVICE_TEST_IDS = {
  // Dashboard
  DASHBOARD: {
    CONTAINER: 'credit-dashboard-container',
    BALANCE_CARD: 'credit-dashboard-balance-card',
    SHOPS_CARD: 'credit-dashboard-shops-card',
    TRANSACTIONS_CARD: 'credit-dashboard-transactions-card',
    ANALYTICS_CARD: 'credit-dashboard-analytics-card',
  },
  
  // Shop Management
  SHOPS: {
    LIST_CONTAINER: 'credit-shops-list-container',
    CREATE_BUTTON: 'credit-shops-create-button',
    SHOP_CARD: (shopSlug: string) => `credit-shop-card-${shopSlug}`,
    SHOP_SETTINGS_BUTTON: (shopSlug: string) => `credit-shop-settings-button-${shopSlug}`,
    
    // Shop Creation Form
    CREATE_FORM: {
      CONTAINER: 'shop-create-form-container',
      FORM: 'shop-create-form',
      NAME_INPUT: 'shop-create-name-input',
      DESCRIPTION_INPUT: 'shop-create-description-input', 
      TYPE_SELECT: 'shop-create-type-select',
      EMAIL_INPUT: 'shop-create-email-input',
      PHONE_INPUT: 'shop-create-phone-input',
      SUBMIT_BUTTON: 'shop-create-submit-button',
      CANCEL_BUTTON: 'shop-create-cancel-button',
      BACK_BUTTON: 'shop-create-back-button',
      ERROR_MESSAGE: 'shop-create-error-message',
      LOADING_SPINNER: 'shop-create-loading-spinner',
    },
  },
  
  // Merchant Dashboard Components
  MERCHANT: {
    // Usage Tab
    USAGE_TAB: {
      CONTAINER: 'merchant-usage-tab-container',
      TITLE: 'merchant-usage-title',
      DESCRIPTION: 'merchant-usage-description',
      ERROR_ALERT: 'merchant-usage-error-alert',
      
      // API Usage Summary Card
      API_USAGE_CARD: 'merchant-usage-api-card',
      API_USAGE_LOADING: 'merchant-usage-api-loading',
      API_TOTAL_CALLS: 'merchant-usage-api-total-calls',
      API_CREDITS_USED: 'merchant-usage-api-credits-used',
      API_QR_CODES_USED: 'merchant-usage-api-qr-codes-used',
      API_RESPONSE_TIME: 'merchant-usage-api-response-time',
      
      // Subscription Limits Card
      SUBSCRIPTION_CARD: 'merchant-usage-subscription-card',
      SUBSCRIPTION_LOADING: 'merchant-usage-subscription-loading',
      SHOPS_LIMIT: 'merchant-usage-shops-limit',
      SHOPS_PROGRESS: 'merchant-usage-shops-progress',
      QR_LIMIT: 'merchant-usage-qr-limit',
      QR_PROGRESS: 'merchant-usage-qr-progress',
      WEBHOOKS_LIMIT: 'merchant-usage-webhooks-limit',
      WEBHOOKS_PROGRESS: 'merchant-usage-webhooks-progress',
      CREDITS_LIMIT: 'merchant-usage-credits-limit',
      CREDITS_PROGRESS: 'merchant-usage-credits-progress',
      
      // Recent API Activity Card
      ACTIVITY_CARD: 'merchant-usage-activity-card',
      ACTIVITY_LOADING: 'merchant-usage-activity-loading',
      ACTIVITY_LIST: 'merchant-usage-activity-list',
      ACTIVITY_ITEM: (id: string) => `merchant-usage-activity-item-${id}`,
      ACTIVITY_EMPTY_STATE: 'merchant-usage-activity-empty-state',
      
      // Performance Metrics Card
      PERFORMANCE_CARD: 'merchant-usage-performance-card',
      PERFORMANCE_GRID: 'merchant-usage-performance-grid',
      METRIC_AVG_RESPONSE: 'merchant-usage-metric-avg-response',
      METRIC_P95_RESPONSE: 'merchant-usage-metric-p95-response',
      METRIC_ERROR_RATE: 'merchant-usage-metric-error-rate',
      METRIC_TOTAL_REQUESTS: 'merchant-usage-metric-total-requests',
    },
    
    // Analytics Tab
    ANALYTICS_TAB: {
      CONTAINER: 'merchant-analytics-tab-container',
      TITLE: 'merchant-analytics-title',
      DESCRIPTION: 'merchant-analytics-description',
      CREDIT_PERFORMANCE_CARD: 'merchant-analytics-credit-performance-card',
      SHOP_PERFORMANCE_CARD: 'merchant-analytics-shop-performance-card',
      GROWTH_METRICS_CARD: 'merchant-analytics-growth-metrics-card',
    },
    
    // My Shops Tab
    MY_SHOPS_TAB: {
      CONTAINER: 'merchant-my-shops-tab-container',
      CARD: 'merchant-my-shops-tab-card',
      HEADER: 'merchant-my-shops-tab-header',
      TITLE: 'merchant-my-shops-tab-title',
      DESCRIPTION: 'merchant-my-shops-tab-description',
      CONTENT: 'merchant-my-shops-tab-content',
      
      // Loading State
      LOADING_STATE: 'merchant-my-shops-tab-loading-state',
      LOADING_CARD: (index: string) => `merchant-my-shops-tab-loading-card-${index}`,
      
      // Shops Grid
      SHOPS_GRID: 'merchant-my-shops-tab-shops-grid',
      SHOP_LINK: (slug: string) => `merchant-my-shops-tab-shop-link-${slug}`,
      SHOP_CARD: (slug: string) => `merchant-my-shops-tab-shop-card-${slug}`,
      SHOP_CONTENT: (slug: string) => `merchant-my-shops-tab-shop-content-${slug}`,
      SHOP_ICON: (slug: string) => `merchant-my-shops-tab-shop-icon-${slug}`,
      SHOP_NAME: (slug: string) => `merchant-my-shops-tab-shop-name-${slug}`,
      SHOP_DESCRIPTION: (slug: string) => `merchant-my-shops-tab-shop-description-${slug}`,
      SHOP_ARROW: (slug: string) => `merchant-my-shops-tab-shop-arrow-${slug}`,
      
      // Empty State
      EMPTY_STATE: 'merchant-my-shops-tab-empty-state',
      EMPTY_ICON: 'merchant-my-shops-tab-empty-icon',
      EMPTY_MESSAGE: 'merchant-my-shops-tab-empty-message',
      CREATE_FIRST_BUTTON: 'merchant-my-shops-tab-create-first-button',
    },
    
    // Quick Actions Tab
    QUICK_ACTIONS_TAB: {
      CONTAINER: 'merchant-quick-actions-tab-container',
      TITLE: 'merchant-quick-actions-title',
      DESCRIPTION: 'merchant-quick-actions-description',
      ACTION_CARD: (action: string) => `merchant-quick-action-${action}`,
      MANAGE_CREDITS_BUTTON: 'merchant-quick-action-manage-credits',
      CREDIT_CODES_BUTTON: 'merchant-quick-action-credit-codes',
      CREATE_SHOP_BUTTON: 'merchant-quick-action-create-shop',
      PROFILE_BUTTON: 'merchant-quick-action-profile',
    },
    
    // Payment QR Code Component
    PAYMENT_QR: {
      TRIGGER_BUTTON: 'merchant-payment-qr-trigger',
      DIALOG_CONTAINER: 'merchant-payment-qr-dialog',
      AMOUNT_INPUT: 'merchant-payment-qr-amount-input',
      DESCRIPTION_INPUT: 'merchant-payment-qr-description-input',
      GENERATE_BUTTON: 'merchant-payment-qr-generate-button',
      QR_DISPLAY: 'merchant-payment-qr-display',
      DOWNLOAD_BUTTON: 'merchant-payment-qr-download-button',
      SHARE_BUTTON: 'merchant-payment-qr-share-button',
      CANCEL_BUTTON: 'merchant-payment-qr-cancel-button',
      ERROR_MESSAGE: 'merchant-payment-qr-error-message',
    },
    
    // Credit Code Detail Page
    CREDIT_CODE_DETAIL: {
      CONTAINER: 'merchant-credit-code-detail-container',
      HEADER: 'merchant-credit-code-detail-header',
      BACK_BUTTON: 'merchant-credit-code-detail-back-button',
      TITLE: 'merchant-credit-code-detail-title',
      
      // Loading State
      LOADING_CONTAINER: 'merchant-credit-code-detail-loading-container',
      LOADING_CARD: 'merchant-credit-code-detail-loading-card',
      LOADING_HEADER: 'merchant-credit-code-detail-loading-header',
      LOADING_QR_PLACEHOLDER: 'merchant-credit-code-detail-loading-qr-placeholder',
      LOADING_CODE_PLACEHOLDER: 'merchant-credit-code-detail-loading-code-placeholder',
      LOADING_BUTTONS: 'merchant-credit-code-detail-loading-buttons',
      
      // Error State
      ERROR_CONTAINER: 'merchant-credit-code-detail-error-container',
      ERROR_CARD: 'merchant-credit-code-detail-error-card',
      ERROR_TITLE: 'merchant-credit-code-detail-error-title',
      ERROR_MESSAGE: 'merchant-credit-code-detail-error-message',
      ERROR_RETURN_BUTTON: 'merchant-credit-code-detail-error-return-button',
      
      // Credit Code Card
      CARD: 'merchant-credit-code-detail-card',
      CARD_HEADER: 'merchant-credit-code-detail-card-header',
      CARD_TITLE: 'merchant-credit-code-detail-card-title',
      CARD_CONTENT: 'merchant-credit-code-detail-card-content',
      
      // QR Code Section
      QR_CONTAINER: 'merchant-credit-code-detail-qr-container',
      QR_IMAGE: 'merchant-credit-code-detail-qr-image',
      QR_LOADING_PLACEHOLDER: 'merchant-credit-code-detail-qr-loading-placeholder',
      
      // Code Section
      CODE_CONTAINER: 'merchant-credit-code-detail-code-container',
      CODE_TEXT: 'merchant-credit-code-detail-code-text',
      
      // Details Section
      DESCRIPTION: 'merchant-credit-code-detail-description',
      EXPIRES_AT: 'merchant-credit-code-detail-expires-at',
      USED_BADGE: 'merchant-credit-code-detail-used-badge',
      
      // Action Buttons
      CARD_FOOTER: 'merchant-credit-code-detail-card-footer',
      COPY_BUTTON: 'merchant-credit-code-detail-copy-button',
      DOWNLOAD_BUTTON: 'merchant-credit-code-detail-download-button',
      SHARE_BUTTON: 'merchant-credit-code-detail-share-button',
      
      // Footer Navigation
      FOOTER: 'merchant-credit-code-detail-footer',
      FOOTER_ACTIONS: 'merchant-credit-code-detail-footer-actions',
      FOOTER_RETURN_BUTTON: 'merchant-credit-code-detail-footer-return-button',
    },
  },
  
  // Customer-Facing Components
  CUSTOMER: {
    // Customer Dashboard
    DASHBOARD: {
      CONTAINER: 'customer-dashboard-container',
      HEADER: 'customer-dashboard-header',
      TITLE: 'customer-dashboard-title',
      SCAN_BUTTON: 'customer-dashboard-scan-button',
      REDEEM_BUTTON: 'customer-dashboard-redeem-button',
      TOTAL_CREDITS_CARD: 'customer-dashboard-total-credits-card',
      TOTAL_CREDITS_VALUE: 'customer-dashboard-total-credits-value',
      SHOPS_COUNT_CARD: 'customer-dashboard-shops-count-card',
      SHOPS_COUNT_VALUE: 'customer-dashboard-shops-count-value',
      SHOPS_SECTION: 'customer-dashboard-shops-section',
      SHOPS_TITLE: 'customer-dashboard-shops-title',
      REDEEM_CODE_BUTTON: 'customer-dashboard-redeem-code-button',
      SHOPS_LIST: 'customer-dashboard-shops-list',
      SHOP_ITEM: (shopId: string) => `customer-dashboard-shop-item-${shopId}`,
      SHOP_ICON: (shopId: string) => `customer-dashboard-shop-icon-${shopId}`,
      SHOP_NAME: (shopId: string) => `customer-dashboard-shop-name-${shopId}`,
      SHOP_CREDITS: (shopId: string) => `customer-dashboard-shop-credits-${shopId}`,
      SHOP_ARROW: (shopId: string) => `customer-dashboard-shop-arrow-${shopId}`,
      EMPTY_STATE: 'customer-dashboard-empty-state',
      EMPTY_STATE_MESSAGE: 'customer-dashboard-empty-state-message',
      EMPTY_STATE_BUTTON: 'customer-dashboard-empty-state-button',
      LOADING_STATE: 'customer-dashboard-loading-state',
    },
    
    // QR Scanner
    QR_SCANNER: {
      CONTAINER: 'customer-qr-scanner-container',
      VIEWPORT: 'customer-qr-scanner-viewport',
      START_BUTTON: 'customer-qr-scanner-start-button',
      STOP_BUTTON: 'customer-qr-scanner-stop-button',
      PERMISSION_ERROR: 'customer-qr-scanner-permission-error',
      CAMERA_ERROR: 'customer-qr-scanner-camera-error',
      SCAN_SUCCESS_ALERT: 'customer-qr-scanner-success-alert',
      SCAN_ERROR_ALERT: 'customer-qr-scanner-error-alert',
      SCAN_AGAIN_BUTTON: 'customer-qr-scanner-scan-again-button',
      CONTINUE_BUTTON: 'customer-qr-scanner-continue-button',
      MANUAL_ENTRY_BUTTON: 'customer-qr-scanner-manual-entry-button',
      TROUBLESHOOTING_SECTION: 'customer-qr-scanner-troubleshooting-section',
    },
    
    // Credit Redemption
    REDEEM: {
      CONTAINER: 'customer-redeem-container',
      FORM: 'customer-redeem-form',
      CODE_INPUT: 'customer-redeem-code-input',
      CAMERA_BUTTON: 'customer-redeem-camera-button',
      SUBMIT_BUTTON: 'customer-redeem-submit-button',
      RESULT_CONTAINER: 'customer-redeem-result-container',
      SUCCESS_MESSAGE: 'customer-redeem-success-message',
      ERROR_MESSAGE: 'customer-redeem-error-message',
      BREADCRUMB: 'customer-redeem-breadcrumb',
    },
    
    // Shop Details
    SHOP_DETAIL: {
      CONTAINER: 'customer-shop-detail-container',
      PROFILE_CARD: 'customer-shop-profile-card',
      BALANCE_CARD: 'customer-shop-balance-card',
      BALANCE_VALUE: 'customer-shop-balance-value',
      SCAN_QR_BUTTON: 'customer-shop-scan-qr-button',
      USE_MANUALLY_BUTTON: 'customer-shop-use-manually-button',
      TRANSACTION_HISTORY: 'customer-shop-transaction-history',
      TRANSACTION_ITEM: (transactionId: string) => `customer-shop-transaction-item-${transactionId}`,
      RECENT_ACTIVITY: 'customer-shop-recent-activity',
      LOADING_STATE: 'customer-shop-loading-state',
      ERROR_STATE: 'customer-shop-error-state',
    },
    
    // Shop Listing
    SHOPS: {
      CONTAINER: 'customer-shops-container',
      SEARCH_INPUT: 'customer-shops-search-input',
      SHOP_CARD: (shopId: string) => `customer-shops-shop-card-${shopId}`,
      SHOP_IMAGE: (shopId: string) => `customer-shops-shop-image-${shopId}`,
      SHOP_NAME: (shopId: string) => `customer-shops-shop-name-${shopId}`,
      SHOP_BALANCE: (shopId: string) => `customer-shops-shop-balance-${shopId}`,
      VIEW_SHOP_BUTTON: (shopId: string) => `customer-shops-view-shop-button-${shopId}`,
      EMPTY_STATE: 'customer-shops-empty-state',
      LOADING_STATE: 'customer-shops-loading-state',
    },
    
    // Authentication
    LOGIN: {
      CONTAINER: 'customer-login-container',
      FORM: 'customer-login-form',
      TITLE: 'customer-login-title',
      USERNAME_INPUT: 'customer-login-username-input',
      PASSWORD_INPUT: 'customer-login-password-input',
      SHOW_PASSWORD_BUTTON: 'customer-login-show-password-button',
      SUBMIT_BUTTON: 'customer-login-submit-button',
      LOADING_SPINNER: 'customer-login-loading-spinner',
      ERROR_MESSAGE: 'customer-login-error-message',
      BUSINESS_LINK: 'customer-login-business-link',
      INFO_PANEL: 'customer-login-info-panel',
    },
    
    // Credit Components
    CREDITS: {
      DISPLAY_CONTAINER: 'customer-credits-display-container',
      BALANCE_OVERVIEW: 'customer-credits-balance-overview',
      CURRENT_BALANCE_CARD: 'customer-credits-current-balance-card',
      EARNED_CARD: 'customer-credits-earned-card',
      SPENT_CARD: 'customer-credits-spent-card',
      TRANSACTION_LIST: 'customer-credits-transaction-list',
      TRANSACTION_ITEM: (transactionId: string) => `customer-credits-transaction-item-${transactionId}`,
      REFRESH_BUTTON: 'customer-credits-refresh-button',
      EMPTY_STATE: 'customer-credits-empty-state',
      LOADING_STATE: 'customer-credits-loading-state',
    },
    
    // Dialogs
    DIALOGS: {
      USE_CREDIT: {
        CONTAINER: 'customer-use-credit-dialog-container',
        BALANCE_DISPLAY: 'customer-use-credit-dialog-balance-display',
        AMOUNT_INPUT: 'customer-use-credit-dialog-amount-input',
        DESCRIPTION_INPUT: 'customer-use-credit-dialog-description-input',
        SUBMIT_BUTTON: 'customer-use-credit-dialog-submit-button',
        CANCEL_BUTTON: 'customer-use-credit-dialog-cancel-button',
        ERROR_MESSAGE: 'customer-use-credit-dialog-error-message',
      },
      QR_SCANNER: {
        CONTAINER: 'customer-qr-scanner-dialog-container',
        OVERLAY: 'customer-qr-scanner-dialog-overlay',
        VIEWPORT: 'customer-qr-scanner-dialog-viewport',
        START_BUTTON: 'customer-qr-scanner-dialog-start-button',
        SUCCESS_ALERT: 'customer-qr-scanner-dialog-success-alert',
        ERROR_ALERT: 'customer-qr-scanner-dialog-error-alert',
        SCAN_AGAIN_BUTTON: 'customer-qr-scanner-dialog-scan-again-button',
        CONTINUE_BUTTON: 'customer-qr-scanner-dialog-continue-button',
        CANCEL_BUTTON: 'customer-qr-scanner-dialog-cancel-button',
      },
    },
  },
  
  // Customer Management
  CUSTOMERS: {
    LIST_CONTAINER: 'credit-customers-list-container',
    ADD_CUSTOMER_BUTTON: 'credit-customers-add-button',
    CUSTOMER_ROW: (customerId: string) => `credit-customer-row-${customerId}`,
    CUSTOMER_EDIT_BUTTON: (customerId: string) => `credit-customer-edit-${customerId}`,
  },
  
  // QR Code Generation and Display
  QR_CODES: {
    // Generator
    GENERATOR_CONTAINER: 'credit-qr-generator-container',
    AMOUNT_INPUT: 'credit-qr-amount-input',
    GENERATE_BUTTON: 'credit-qr-generate-button',
    
    // QR Code Display Component
    DISPLAY_CARD: 'qr-code-display-card',
    DISPLAY_HEADER: 'qr-code-display-header',
    DISPLAY_TITLE: 'qr-code-display-title',
    DISPLAY_CONTENT: 'qr-code-display-content',
    QR_IMAGE_CONTAINER: 'qr-code-image-container',
    QR_IMAGE: 'qr-code-image',
    CODE_TEXT_CONTAINER: 'qr-code-text-container',
    CODE_TEXT: 'qr-code-text',
    DESCRIPTION: 'qr-code-description',
    EXPIRES_TEXT: 'qr-code-expires-text',
    
    // Action Buttons
    ACTIONS_FOOTER: 'qr-code-actions-footer',
    COPY_BUTTON: 'qr-code-copy-button',
    DOWNLOAD_BUTTON: 'qr-code-download-button',
    SHARE_BUTTON: 'qr-code-share-button',
  },
  
  // Transactions
  TRANSACTIONS: {
    LIST_CONTAINER: 'credit-transactions-list-container',
    FILTER_FORM: 'credit-transactions-filter-form',
    DATE_FILTER: 'credit-transactions-date-filter',
    AMOUNT_FILTER: 'credit-transactions-amount-filter',
    TRANSACTION_ROW: (transactionId: string) => `credit-transaction-row-${transactionId}`,
  },
  
  // Analytics
  ANALYTICS: {
    CONTAINER: 'credit-analytics-container',
    CHART_CONTAINER: 'credit-analytics-chart-container',
    METRICS_GRID: 'credit-analytics-metrics-grid',
    EXPORT_BUTTON: 'credit-analytics-export-button',
    DATE_RANGE_PICKER: 'credit-analytics-date-range-picker',
  },
  
  // API Keys Management
  API_KEYS: {
    CONTAINER: 'api-keys-container',
    TITLE: 'api-keys-title',
    CREATE_BUTTON: 'api-keys-create-button',
    SUCCESS_ALERT: 'api-keys-success-alert',
    NEW_KEY_ALERT: 'api-keys-new-key-alert',
    NEW_KEY_VALUE: 'api-keys-new-key-value',
    COPY_BUTTON: 'api-keys-copy-button',
    DISMISS_BUTTON: 'api-keys-dismiss-button',
    
    // Create Dialog
    CREATE_DIALOG: {
      CONTAINER: 'api-keys-create-dialog',
      TITLE: 'api-keys-create-dialog-title',
      NAME_INPUT: 'api-keys-create-name-input',
      PERMISSIONS_SECTION: 'api-keys-create-permissions-section',
      PERMISSION_CHECKBOX: (permission: string) => `api-keys-permission-${permission}`,
      CREATE_BUTTON: 'api-keys-create-dialog-create-button',
    },
    
    // Keys Table
    TABLE: {
      CONTAINER: 'api-keys-table-container',
      HEADER: 'api-keys-table-header',
      BODY: 'api-keys-table-body',
      ROW: (keyId: string) => `api-keys-table-row-${keyId}`,
      NAME_CELL: (keyId: string) => `api-keys-table-name-${keyId}`,
      CREATED_CELL: (keyId: string) => `api-keys-table-created-${keyId}`,
      LAST_USED_CELL: (keyId: string) => `api-keys-table-last-used-${keyId}`,
      STATUS_CELL: (keyId: string) => `api-keys-table-status-${keyId}`,
      ACTIONS_CELL: (keyId: string) => `api-keys-table-actions-${keyId}`,
      VIEW_BUTTON: (keyId: string) => `api-keys-table-view-${keyId}`,
      TOGGLE_BUTTON: (keyId: string) => `api-keys-table-toggle-${keyId}`,
      DELETE_BUTTON: (keyId: string) => `api-keys-table-delete-${keyId}`,
      EMPTY_STATE: 'api-keys-table-empty-state',
    },
    
    // Loading States
    LOADING_SPINNER: 'api-keys-loading-spinner',
  },
  
  // Subscription Management
  SUBSCRIPTIONS: {
    CONTAINER: 'subscriptions-container',
    TITLE: 'subscriptions-title',
    REFRESH_BUTTON: 'subscriptions-refresh-button',
    LOADING_SPINNER: 'subscriptions-loading-spinner',
    AUTH_REQUIRED: 'subscriptions-auth-required',
    
    // Tabs
    TABS: {
      CONTAINER: 'subscriptions-tabs-container',
      LIST: 'subscriptions-tabs-list',
      PERSONAL_TAB: 'subscriptions-tab-personal',
      SHOP_TAB: 'subscriptions-tab-shop',
      CUSTOMER_TAB: 'subscriptions-tab-customer',
    },
    
    // Current Subscription Card
    CURRENT: {
      CARD: 'subscriptions-current-card',
      TITLE: 'subscriptions-current-title',
      PLAN_NAME: 'subscriptions-current-plan-name',
      PLAN_DESCRIPTION: 'subscriptions-current-plan-description',
      CREDIT_BALANCE: 'subscriptions-current-credit-balance',
      CREDIT_PROGRESS: 'subscriptions-current-credit-progress',
      START_DATE: 'subscriptions-current-start-date',
      AUTO_RENEW: 'subscriptions-current-auto-renew',
      PRICE: 'subscriptions-current-price',
      USAGE: 'subscriptions-current-usage',
      AUTO_RENEW_BUTTON: 'subscriptions-current-auto-renew-button',
      CANCEL_BUTTON: 'subscriptions-current-cancel-button',
    },
    
    // Available Plans
    PLANS: {
      SECTION_TITLE: 'subscriptions-plans-section-title',
      GRID: 'subscriptions-plans-grid',
      CARD: (tierId: string) => `subscriptions-plan-card-${tierId}`,
      NAME: (tierId: string) => `subscriptions-plan-name-${tierId}`,
      DESCRIPTION: (tierId: string) => `subscriptions-plan-description-${tierId}`,
      PRICE: (tierId: string) => `subscriptions-plan-price-${tierId}`,
      CREDITS: (tierId: string) => `subscriptions-plan-credits-${tierId}`,
      FEATURES: (tierId: string) => `subscriptions-plan-features-${tierId}`,
      SELECT_BUTTON: (tierId: string) => `subscriptions-plan-select-${tierId}`,
    },
    
    // Confirm Dialog
    CONFIRM_DIALOG: {
      CONTAINER: 'subscriptions-confirm-dialog',
      TITLE: 'subscriptions-confirm-dialog-title',
      DESCRIPTION: 'subscriptions-confirm-dialog-description',
      PLAN_DETAILS: 'subscriptions-confirm-plan-details',
      PLAN_NAME: 'subscriptions-confirm-plan-name',
      PLAN_PRICE: 'subscriptions-confirm-plan-price',
      PLAN_CREDITS: 'subscriptions-confirm-plan-credits',
      CANCEL_BUTTON: 'subscriptions-confirm-cancel-button',
      CONFIRM_BUTTON: 'subscriptions-confirm-confirm-button',
      REPLACEMENT_ALERT: 'subscriptions-confirm-replacement-alert',
    },
    
    // Cancel Dialog
    CANCEL_DIALOG: {
      CONTAINER: 'subscriptions-cancel-dialog',
      TITLE: 'subscriptions-cancel-dialog-title',
      DESCRIPTION: 'subscriptions-cancel-dialog-description',
      WARNING_ALERT: 'subscriptions-cancel-warning-alert',
      KEEP_BUTTON: 'subscriptions-cancel-keep-button',
      CANCEL_BUTTON: 'subscriptions-cancel-cancel-button',
    },
  },
} as const;

// Subscription Page Wrapper Test IDs
export const SUBSCRIPTION_PAGE_WRAPPER_TEST_IDS = {
  CONTAINER: 'subscription-page-wrapper-container',
  LOADING_CONTAINER: 'subscription-page-wrapper-loading-container',
  LOADING_SPINNER: 'subscription-page-wrapper-loading-spinner',
} as const;

// My Subscriptions Panel Test IDs
export const MY_SUBSCRIPTIONS_PANEL_TEST_IDS = {
  CONTAINER: 'my-subscriptions-panel-container',
  HEADER: 'my-subscriptions-panel-header',
  TITLE: 'my-subscriptions-panel-title',
  DESCRIPTION: 'my-subscriptions-panel-description',
  CONTENT: 'my-subscriptions-panel-content',
  
  // Tabs
  TABS_CONTAINER: 'my-subscriptions-panel-tabs-container',
  TABS_LIST: 'my-subscriptions-panel-tabs-list',
  ALL_TAB: 'my-subscriptions-panel-all-tab',
  PERSONAL_TAB: 'my-subscriptions-panel-personal-tab',
  SHOP_TAB: 'my-subscriptions-panel-shop-tab',
  CUSTOMER_TAB: 'my-subscriptions-panel-customer-tab',
  
  // Tab Content
  ALL_TAB_CONTENT: 'my-subscriptions-panel-all-tab-content',
  PERSONAL_TAB_CONTENT: 'my-subscriptions-panel-personal-tab-content',
  SHOP_TAB_CONTENT: 'my-subscriptions-panel-shop-tab-content',
  CUSTOMER_TAB_CONTENT: 'my-subscriptions-panel-customer-tab-content',
  
  // Empty State
  EMPTY_ALERT: 'my-subscriptions-panel-empty-alert',
  EMPTY_DESCRIPTION: 'my-subscriptions-panel-empty-description',
  
  // Subscription Sections
  PERSONAL_SECTION: 'my-subscriptions-panel-personal-section',
  PERSONAL_TITLE: 'my-subscriptions-panel-personal-title',
  PERSONAL_GRID: 'my-subscriptions-panel-personal-grid',
  
  SHOP_SECTION: 'my-subscriptions-panel-shop-section',
  SHOP_TITLE: 'my-subscriptions-panel-shop-title',
  SHOP_GRID: 'my-subscriptions-panel-shop-grid',
  
  CUSTOMER_SECTION: 'my-subscriptions-panel-customer-section',
  CUSTOMER_TITLE: 'my-subscriptions-panel-customer-title',
  CUSTOMER_GRID: 'my-subscriptions-panel-customer-grid',
  
  // Subscription Cards
  SUBSCRIPTION_CARD: (subscriptionId: string) => `my-subscriptions-panel-card-${subscriptionId}`,
  SUBSCRIPTION_HEADER: (subscriptionId: string) => `my-subscriptions-panel-header-${subscriptionId}`,
  SUBSCRIPTION_TITLE: (subscriptionId: string) => `my-subscriptions-panel-title-${subscriptionId}`,
  SUBSCRIPTION_BADGE: (subscriptionId: string) => `my-subscriptions-panel-badge-${subscriptionId}`,
  SUBSCRIPTION_DESCRIPTION: (subscriptionId: string) => `my-subscriptions-panel-description-${subscriptionId}`,
  SUBSCRIPTION_CONTENT: (subscriptionId: string) => `my-subscriptions-panel-content-${subscriptionId}`,
  SUBSCRIPTION_CREDIT_BALANCE: (subscriptionId: string) => `my-subscriptions-panel-credit-balance-${subscriptionId}`,
  SUBSCRIPTION_PRICE: (subscriptionId: string) => `my-subscriptions-panel-price-${subscriptionId}`,
  SUBSCRIPTION_START_DATE: (subscriptionId: string) => `my-subscriptions-panel-start-date-${subscriptionId}`,
  SUBSCRIPTION_AUTO_RENEW: (subscriptionId: string) => `my-subscriptions-panel-auto-renew-${subscriptionId}`,
  SUBSCRIPTION_FOOTER: (subscriptionId: string) => `my-subscriptions-panel-footer-${subscriptionId}`,
  SUBSCRIPTION_MANAGE_BUTTON: (subscriptionId: string) => `my-subscriptions-panel-manage-${subscriptionId}`,
  SUBSCRIPTION_CHANGE_PLAN_BUTTON: (subscriptionId: string) => `my-subscriptions-panel-change-plan-${subscriptionId}`,
  SUBSCRIPTION_CANCEL_BUTTON: (subscriptionId: string) => `my-subscriptions-panel-cancel-${subscriptionId}`,
  
  // Cancel Dialog
  CANCEL_DIALOG: 'my-subscriptions-panel-cancel-dialog',
  CANCEL_DIALOG_HEADER: 'my-subscriptions-panel-cancel-dialog-header',
  CANCEL_DIALOG_TITLE: 'my-subscriptions-panel-cancel-dialog-title',
  CANCEL_DIALOG_DESCRIPTION: 'my-subscriptions-panel-cancel-dialog-description',
  CANCEL_DIALOG_ALERT: 'my-subscriptions-panel-cancel-dialog-alert',
  CANCEL_DIALOG_FOOTER: 'my-subscriptions-panel-cancel-dialog-footer',
  CANCEL_DIALOG_CANCEL_BUTTON: 'my-subscriptions-panel-cancel-dialog-cancel-button',
  CANCEL_DIALOG_CONFIRM_BUTTON: 'my-subscriptions-panel-cancel-dialog-confirm-button',
} as const;

// Shop Subscription Panel Test IDs
export const SHOP_SUBSCRIPTION_PANEL_TEST_IDS = {
  CONTAINER: 'shop-subscription-panel-container',
  HEADER: 'shop-subscription-panel-header',
  TITLE: 'shop-subscription-panel-title',
  DESCRIPTION: 'shop-subscription-panel-description',
  CONTENT: 'shop-subscription-panel-content',
  
  // Shop Selection
  SHOP_SELECTION: 'shop-subscription-panel-shop-selection',
  SHOP_SELECT: 'shop-subscription-panel-shop-select',
  SHOP_SELECT_TRIGGER: 'shop-subscription-panel-shop-select-trigger',
  SHOP_SELECT_CONTENT: 'shop-subscription-panel-shop-select-content',
  SHOP_SELECT_ITEM: (shopId: string) => `shop-subscription-panel-shop-select-item-${shopId}`,
  
  // Tiers Grid
  TIERS_GRID: 'shop-subscription-panel-tiers-grid',
  TIER_CARD: (tierId: string) => `shop-subscription-panel-tier-card-${tierId}`,
  TIER_HEADER: (tierId: string) => `shop-subscription-panel-tier-header-${tierId}`,
  TIER_TITLE: (tierId: string) => `shop-subscription-panel-tier-title-${tierId}`,
  TIER_PRICE: (tierId: string) => `shop-subscription-panel-tier-price-${tierId}`,
  TIER_CONTENT: (tierId: string) => `shop-subscription-panel-tier-content-${tierId}`,
  TIER_FEATURES: (tierId: string) => `shop-subscription-panel-tier-features-${tierId}`,
  TIER_FEATURE_ITEM: (tierId: string, index: number) => `shop-subscription-panel-tier-feature-${tierId}-${index}`,
  TIER_FOOTER: (tierId: string) => `shop-subscription-panel-tier-footer-${tierId}`,
  TIER_SELECT_BUTTON: (tierId: string) => `shop-subscription-panel-tier-select-${tierId}`,
  
  // Confirm Dialog
  CONFIRM_DIALOG: 'shop-subscription-panel-confirm-dialog',
  CONFIRM_DIALOG_HEADER: 'shop-subscription-panel-confirm-dialog-header',
  CONFIRM_DIALOG_TITLE: 'shop-subscription-panel-confirm-dialog-title',
  CONFIRM_DIALOG_DESCRIPTION: 'shop-subscription-panel-confirm-dialog-description',
  CONFIRM_DIALOG_CONTENT: 'shop-subscription-panel-confirm-dialog-content',
  CONFIRM_DIALOG_FOOTER: 'shop-subscription-panel-confirm-dialog-footer',
  CONFIRM_DIALOG_CANCEL_BUTTON: 'shop-subscription-panel-confirm-dialog-cancel-button',
  CONFIRM_DIALOG_CONFIRM_BUTTON: 'shop-subscription-panel-confirm-dialog-confirm-button',
} as const;

// Customer Subscription Panel Test IDs
export const CUSTOMER_SUBSCRIPTION_PANEL_TEST_IDS = {
  CONTAINER: 'customer-subscription-panel-container',
  HEADER: 'customer-subscription-panel-header',
  TITLE: 'customer-subscription-panel-title',
  DESCRIPTION: 'customer-subscription-panel-description',
  CONTENT: 'customer-subscription-panel-content',
  
  // Shop Selection Section
  SHOP_SELECTION_SECTION: 'customer-subscription-panel-shop-selection-section',
  SHOP_SELECTION_TITLE: 'customer-subscription-panel-shop-selection-title',
  SHOP_SELECTION_DESCRIPTION: 'customer-subscription-panel-shop-selection-description',
  SHOPS_GRID: 'customer-subscription-panel-shops-grid',
  SHOP_CARD: (shopId: string) => `customer-subscription-panel-shop-card-${shopId}`,
  SHOP_HEADER: (shopId: string) => `customer-subscription-panel-shop-header-${shopId}`,
  SHOP_TITLE: (shopId: string) => `customer-subscription-panel-shop-title-${shopId}`,
  SHOP_DESCRIPTION: (shopId: string) => `customer-subscription-panel-shop-description-${shopId}`,
  SHOP_CONTENT: (shopId: string) => `customer-subscription-panel-shop-content-${shopId}`,
  SHOP_CREDITS: (shopId: string) => `customer-subscription-panel-shop-credits-${shopId}`,
  SHOP_FOOTER: (shopId: string) => `customer-subscription-panel-shop-footer-${shopId}`,
  SHOP_SELECT_BUTTON: (shopId: string) => `customer-subscription-panel-shop-select-${shopId}`,
  
  // Tiers Section
  TIERS_SECTION: 'customer-subscription-panel-tiers-section',
  TIERS_TITLE: 'customer-subscription-panel-tiers-title',
  TIERS_GRID: 'customer-subscription-panel-tiers-grid',
  TIER_CARD: (tierId: string) => `customer-subscription-panel-tier-card-${tierId}`,
  TIER_HEADER: (tierId: string) => `customer-subscription-panel-tier-header-${tierId}`,
  TIER_TITLE: (tierId: string) => `customer-subscription-panel-tier-title-${tierId}`,
  TIER_PRICE: (tierId: string) => `customer-subscription-panel-tier-price-${tierId}`,
  TIER_CONTENT: (tierId: string) => `customer-subscription-panel-tier-content-${tierId}`,
  TIER_FEATURES: (tierId: string) => `customer-subscription-panel-tier-features-${tierId}`,
  TIER_FEATURE_ITEM: (tierId: string, index: number) => `customer-subscription-panel-tier-feature-${tierId}-${index}`,
  TIER_FOOTER: (tierId: string) => `customer-subscription-panel-tier-footer-${tierId}`,
  TIER_SELECT_BUTTON: (tierId: string) => `customer-subscription-panel-tier-select-${tierId}`,
  
  // Confirm Dialog
  CONFIRM_DIALOG: 'customer-subscription-panel-confirm-dialog',
  CONFIRM_DIALOG_HEADER: 'customer-subscription-panel-confirm-dialog-header',
  CONFIRM_DIALOG_TITLE: 'customer-subscription-panel-confirm-dialog-title',
  CONFIRM_DIALOG_DESCRIPTION: 'customer-subscription-panel-confirm-dialog-description',
  CONFIRM_DIALOG_CONTENT: 'customer-subscription-panel-confirm-dialog-content',
  CONFIRM_DIALOG_FOOTER: 'customer-subscription-panel-confirm-dialog-footer',
  CONFIRM_DIALOG_CANCEL_BUTTON: 'customer-subscription-panel-confirm-dialog-cancel-button',
  CONFIRM_DIALOG_CONFIRM_BUTTON: 'customer-subscription-panel-confirm-dialog-confirm-button',
} as const;

// Settings Service Integration Test IDs
export const SETTINGS_INTEGRATION_TEST_IDS = {
  // Settings Service Connection Status
  CONNECTION: {
    STATUS_INDICATOR: 'settings-service-connection-status',
    HEALTH_CHECK_BUTTON: 'settings-service-health-check-button',
    RECONNECT_BUTTON: 'settings-service-reconnect-button',
    ERROR_MESSAGE: 'settings-service-connection-error',
  },
  
  // Settings Sync Status
  SYNC: {
    STATUS_INDICATOR: 'settings-service-sync-status',
    LAST_SYNC_TIME: 'settings-service-last-sync-time',
    SYNC_NOW_BUTTON: 'settings-service-sync-now-button',
    SYNC_ERROR_MESSAGE: 'settings-service-sync-error',
    SYNC_SUCCESS_MESSAGE: 'settings-service-sync-success',
  },
  
  // Settings Migration Status
  MIGRATION: {
    STATUS_CONTAINER: 'settings-migration-status-container',
    PROGRESS_BAR: 'settings-migration-progress-bar',
    MIGRATED_COUNT: 'settings-migration-migrated-count',
    TOTAL_COUNT: 'settings-migration-total-count',
    MIGRATION_LOG: 'settings-migration-log',
    RETRY_MIGRATION_BUTTON: 'settings-migration-retry-button',
  },
} as const;

// ==========================================
// PHASE 1: CORE APPLICATION TEST IDS
// ==========================================

// Landing Page Test IDs
export const LANDING_TEST_IDS = {
  CONTAINER: 'landing-container',
  
  // Hero Section
  HERO: {
    SECTION: 'landing-hero-section',
    TITLE: 'landing-hero-title',
    SUBTITLE: 'landing-hero-subtitle',
    CTA_BUTTON: 'landing-hero-cta-button',
    DEMO_BUTTON: 'landing-hero-demo-button',
    VIDEO_BUTTON: 'landing-hero-video-button',
    HERO_IMAGE: 'landing-hero-image',
  },
  
  // Features Section
  FEATURES: {
    SECTION: 'landing-features-section',
    TITLE: 'landing-features-title',
    FEATURE_CARD: (index: number) => `landing-feature-card-${index}`,
    FEATURE_ICON: (index: number) => `landing-feature-icon-${index}`,
    FEATURE_TITLE: (index: number) => `landing-feature-title-${index}`,
    FEATURE_DESCRIPTION: (index: number) => `landing-feature-description-${index}`,
  },
  
  // Pricing Section
  PRICING: {
    SECTION: 'landing-pricing-section',
    HEADER: 'landing-pricing-header',
    TITLE: 'landing-pricing-title',
    SUBTITLE: 'landing-pricing-subtitle',
    LOADING_SPINNER: 'landing-pricing-loading-spinner',
    PLANS_GRID: 'landing-pricing-plans-grid',
    PLAN_CARD: (planId: string | number) => `landing-pricing-plan-card-${planId}`,
    PLAN_POPULAR_BADGE: (planId: string | number) => `landing-pricing-popular-badge-${planId}`,
    PLAN_HEADER: (planId: string | number) => `landing-pricing-plan-header-${planId}`,
    PLAN_TITLE: (planId: string | number) => `landing-pricing-plan-title-${planId}`,
    PLAN_PRICE: (planId: string | number) => `landing-pricing-plan-price-${planId}`,
    PLAN_PRICE_AMOUNT: (planId: string | number) => `landing-pricing-plan-price-amount-${planId}`,
    PLAN_PRICE_PERIOD: (planId: string | number) => `landing-pricing-plan-price-period-${planId}`,
    PLAN_DESCRIPTION: (planId: string | number) => `landing-pricing-plan-description-${planId}`,
    PLAN_CTA_BUTTON: (planId: string | number) => `landing-pricing-plan-cta-button-${planId}`,
    PLAN_FEATURES_LIST: (planId: string | number) => `landing-pricing-plan-features-list-${planId}`,
    PLAN_CREDIT_FEATURE: (planId: string | number) => `landing-pricing-plan-credit-feature-${planId}`,
    PLAN_FEATURE: (planId: string | number, featureIndex: number) => `landing-pricing-plan-feature-${planId}-${featureIndex}`,
    PLAN_FEATURE_ICON: (planId: string | number, featureIndex: number) => `landing-pricing-plan-feature-icon-${planId}-${featureIndex}`,
    
    // Confirm Dialog
    CONFIRM_DIALOG: 'landing-pricing-confirm-dialog',
    CONFIRM_DIALOG_HEADER: 'landing-pricing-confirm-dialog-header',
    CONFIRM_DIALOG_TITLE: 'landing-pricing-confirm-dialog-title',
    CONFIRM_DIALOG_DESCRIPTION: 'landing-pricing-confirm-dialog-description',
    CONFIRM_DIALOG_CONTENT: 'landing-pricing-confirm-dialog-content',
    CONFIRM_DIALOG_PLAN_NAME: 'landing-pricing-confirm-dialog-plan-name',
    CONFIRM_DIALOG_PLAN_PRICE: 'landing-pricing-confirm-dialog-plan-price',
    CONFIRM_DIALOG_PLAN_CREDITS: 'landing-pricing-confirm-dialog-plan-credits',
    CONFIRM_DIALOG_FOOTER: 'landing-pricing-confirm-dialog-footer',
    CONFIRM_DIALOG_CANCEL_BUTTON: 'landing-pricing-confirm-dialog-cancel-button',
    CONFIRM_DIALOG_CONFIRM_BUTTON: 'landing-pricing-confirm-dialog-confirm-button',
  },
  
  // Call to Action Section
  CTA: {
    SECTION: 'landing-cta-section',
    TITLE: 'landing-cta-title',
    DESCRIPTION: 'landing-cta-description',
    PRIMARY_BUTTON: 'landing-cta-primary-button',
    SECONDARY_BUTTON: 'landing-cta-secondary-button',
  },
  
  // Integration Section
  INTEGRATION: {
    SECTION: 'landing-integration-section',
    HEADER: 'landing-integration-header',
    TITLE: 'landing-integration-title',
    DESCRIPTION: 'landing-integration-description',
    CODE_DEMO: 'landing-integration-code-demo',
    CODE_HEADER: 'landing-integration-code-header',
    CODE_LANGUAGE: 'landing-integration-code-language',
    CODE_ACTIONS: 'landing-integration-code-actions',
    COPY_BUTTON: 'landing-integration-copy-button',
    CODE_BLOCK: 'landing-integration-code-block',
    CODE_CONTENT: 'landing-integration-code-content',
  },
  
  // Testimonials Section
  TESTIMONIALS: {
    SECTION: 'landing-testimonials-section',
    CONTAINER: 'landing-testimonials-container',
    HEADER: 'landing-testimonials-header',
    TITLE: 'landing-testimonials-title',
    DESCRIPTION: 'landing-testimonials-description',
    TESTIMONIALS_WRAPPER: 'landing-testimonials-wrapper',
    CIRCULAR_TESTIMONIALS: 'landing-testimonials-circular',
  },
} as const;

// Authentication Test IDs
export const AUTH_TEST_IDS = {
  // Login Page
  LOGIN: {
    CONTAINER: 'auth-login-container',
    FORM: 'auth-login-form',
    TITLE: 'auth-login-title',
    EMAIL_INPUT: 'auth-login-email-input',
    PASSWORD_INPUT: 'auth-login-password-input',
    SUBMIT_BUTTON: 'auth-login-submit-button',
    FORGOT_PASSWORD_LINK: 'auth-login-forgot-password-link',
    REGISTER_LINK: 'auth-login-register-link',
    SSO_BUTTON: 'auth-login-sso-button',
    GOOGLE_BUTTON: 'auth-login-google-button',
    ERROR_MESSAGE: 'auth-login-error-message',
    SUCCESS_MESSAGE: 'auth-login-success-message',
    LOADING_SPINNER: 'auth-login-loading-spinner',
  },
  
  // Register Page
  REGISTER: {
    CONTAINER: 'auth-register-container',
    FORM: 'auth-register-form',
    TITLE: 'auth-register-title',
    FIRST_NAME_INPUT: 'auth-register-first-name-input',
    LAST_NAME_INPUT: 'auth-register-last-name-input',
    EMAIL_INPUT: 'auth-register-email-input',
    PASSWORD_INPUT: 'auth-register-password-input',
    CONFIRM_PASSWORD_INPUT: 'auth-register-confirm-password-input',
    TERMS_CHECKBOX: 'auth-register-terms-checkbox',
    NEWSLETTER_CHECKBOX: 'auth-register-newsletter-checkbox',
    SUBMIT_BUTTON: 'auth-register-submit-button',
    LOGIN_LINK: 'auth-register-login-link',
    ERROR_MESSAGE: 'auth-register-error-message',
    SUCCESS_MESSAGE: 'auth-register-success-message',
    LOADING_SPINNER: 'auth-register-loading-spinner',
  },
  
  // SSO Signin
  SIGNIN: {
    CONTAINER: 'auth-signin-container',
    TITLE: 'auth-signin-title',
    SSO_BUTTON: 'auth-signin-sso-button',
    GOOGLE_BUTTON: 'auth-signin-google-button',
    FACEBOOK_BUTTON: 'auth-signin-facebook-button',
    GITHUB_BUTTON: 'auth-signin-github-button',
    DIVIDER: 'auth-signin-divider',
    EMAIL_LOGIN_LINK: 'auth-signin-email-login-link',
    ERROR_MESSAGE: 'auth-signin-error-message',
    LOADING_SPINNER: 'auth-signin-loading-spinner',
  },
  
  // Customer Login
  CUSTOMER_LOGIN: {
    CONTAINER: 'auth-customer-login-container',
    FORM: 'auth-customer-login-form',
    TITLE: 'auth-customer-login-title',
    PHONE_INPUT: 'auth-customer-login-phone-input',
    CODE_INPUT: 'auth-customer-login-code-input',
    SEND_CODE_BUTTON: 'auth-customer-login-send-code-button',
    VERIFY_BUTTON: 'auth-customer-login-verify-button',
    RESEND_LINK: 'auth-customer-login-resend-link',
    BACK_BUTTON: 'auth-customer-login-back-button',
    ERROR_MESSAGE: 'auth-customer-login-error-message',
    SUCCESS_MESSAGE: 'auth-customer-login-success-message',
  },
  
  // Password Reset
  FORGOT_PASSWORD: {
    CONTAINER: 'auth-forgot-password-container',
    FORM: 'auth-forgot-password-form',
    TITLE: 'auth-forgot-password-title',
    EMAIL_INPUT: 'auth-forgot-password-email-input',
    SUBMIT_BUTTON: 'auth-forgot-password-submit-button',
    BACK_LINK: 'auth-forgot-password-back-link',
    ERROR_MESSAGE: 'auth-forgot-password-error-message',
    SUCCESS_MESSAGE: 'auth-forgot-password-success-message',
  },
  
  // Reset Password
  RESET_PASSWORD: {
    CONTAINER: 'auth-reset-password-container',
    FORM: 'auth-reset-password-form',
    TITLE: 'auth-reset-password-title',
    PASSWORD_INPUT: 'auth-reset-password-password-input',
    CONFIRM_PASSWORD_INPUT: 'auth-reset-password-confirm-password-input',
    SUBMIT_BUTTON: 'auth-reset-password-submit-button',
    ERROR_MESSAGE: 'auth-reset-password-error-message',
    SUCCESS_MESSAGE: 'auth-reset-password-success-message',
  },
  
  // Auth Error
  ERROR: {
    CONTAINER: 'auth-error-container',
    TITLE: 'auth-error-title',
    MESSAGE: 'auth-error-message',
    RETRY_BUTTON: 'auth-error-retry-button',
    HOME_BUTTON: 'auth-error-home-button',
    SUPPORT_LINK: 'auth-error-support-link',
  },
} as const;

// Navigation & Layout Test IDs
export const LAYOUT_TEST_IDS = {
  // Main Navigation
  NAVBAR: {
    CONTAINER: 'layout-navbar-container',
    LOGO: 'layout-navbar-logo',
    MENU_TOGGLE: 'layout-navbar-menu-toggle',
    NAVIGATION_MENU: 'layout-navbar-navigation-menu',
    USER_MENU: 'layout-navbar-user-menu',
    USER_AVATAR: 'layout-navbar-user-avatar',
    LOGOUT_BUTTON: 'layout-navbar-logout-button',
    SETTINGS_LINK: 'layout-navbar-settings-link',
    PROFILE_LINK: 'layout-navbar-profile-link',
    NOTIFICATIONS_BUTTON: 'layout-navbar-notifications-button',
    NOTIFICATIONS_COUNT: 'layout-navbar-notifications-count',
    MOBILE_MENU: 'layout-navbar-mobile-menu',
    MOBILE_CLOSE: 'layout-navbar-mobile-close',
    MOBILE_NAV_SECTION: 'layout-navbar-mobile-nav-section',
  },
  
  // Page Header
  PAGE_HEADER: {
    CONTAINER: 'layout-page-header-container',
    TITLE: 'layout-page-header-title',
    SUBTITLE: 'layout-page-header-subtitle',
    BREADCRUMBS: 'layout-page-header-breadcrumbs',
    ACTION_BUTTON: 'layout-page-header-action-button',
    BACK_BUTTON: 'layout-page-header-back-button',
  },
  
  // Dashboard Drawer/Sidebar
  DRAWER: {
    CONTAINER: 'layout-drawer-container',
    OVERLAY: 'layout-drawer-overlay',
    CLOSE_BUTTON: 'layout-drawer-close-button',
    MENU_SECTION: 'layout-drawer-menu-section',
    MENU_ITEM: (itemName: string) => `layout-drawer-menu-item-${itemName}`,
    USER_SECTION: 'layout-drawer-user-section',
    FOOTER_SECTION: 'layout-drawer-footer-section',
  },
  
  // Breadcrumbs
  BREADCRUMBS: {
    CONTAINER: 'layout-breadcrumbs-container',
    ITEM: (index: number) => `layout-breadcrumbs-item-${index}`,
    SEPARATOR: (index: number) => `layout-breadcrumbs-separator-${index}`,
    CURRENT: 'layout-breadcrumbs-current',
  },
} as const;

// Enhanced Dashboard Test IDs  
export const ENHANCED_DASHBOARD_TEST_IDS = {
  // Main Dashboard
  MAIN: {
    CONTAINER: 'dashboard-main-container',
    HEADER: 'dashboard-main-header',
    GREETING: 'dashboard-main-greeting',
    QUICK_STATS: 'dashboard-main-quick-stats',
    ACTION_BUTTONS: 'dashboard-main-action-buttons',
  },
  
  // Analytics Widgets
  ANALYTICS: {
    CONTAINER: 'dashboard-analytics-container',
    REVENUE_CHART: 'dashboard-analytics-revenue-chart',
    USAGE_CHART: 'dashboard-analytics-usage-chart',
    CONVERSION_CHART: 'dashboard-analytics-conversion-chart',
    TRENDS_CHART: 'dashboard-analytics-trends-chart',
    EXPORT_BUTTON: 'dashboard-analytics-export-button',
    FILTER_DROPDOWN: 'dashboard-analytics-filter-dropdown',
    DATE_RANGE_PICKER: 'dashboard-analytics-date-range-picker',
  },
  
  // Real Usage Monitoring
  REAL_USAGE: {
    CONTAINER: 'dashboard-real-usage-container',
    CURRENT_USAGE: 'dashboard-real-usage-current',
    USAGE_GRAPH: 'dashboard-real-usage-graph',
    LIMIT_INDICATOR: 'dashboard-real-usage-limit-indicator',
    UPGRADE_PROMPT: 'dashboard-real-usage-upgrade-prompt',
    REFRESH_BUTTON: 'dashboard-real-usage-refresh-button',
  },
  
  // Scheduled Credits
  SCHEDULED_CREDITS: {
    CONTAINER: 'dashboard-scheduled-credits-container',
    NEXT_SCHEDULE: 'dashboard-scheduled-credits-next-schedule',
    SCHEDULE_LIST: 'dashboard-scheduled-credits-schedule-list',
    ADD_SCHEDULE_BUTTON: 'dashboard-scheduled-credits-add-schedule-button',
    EDIT_SCHEDULE_BUTTON: (scheduleId: string) => `dashboard-scheduled-credits-edit-${scheduleId}`,
    DELETE_SCHEDULE_BUTTON: (scheduleId: string) => `dashboard-scheduled-credits-delete-${scheduleId}`,
  },
} as const;

// ==========================================
// PHASE 1: ROOT LAYOUT & CORE PAGES
// ==========================================

// Root Layout Test IDs
export const ROOT_LAYOUT_TEST_IDS = {
  CONTAINER: 'root-layout-container',
  HTML: 'root-layout-html',
  BODY: 'root-layout-body',
  PROVIDERS: 'root-layout-providers',
  REDUX_PROVIDER: 'root-layout-redux-provider',
  AUTH_ERROR_BOUNDARY: 'root-layout-auth-error-boundary',
  TRIAL_ALERT_PROVIDER: 'root-layout-trial-alert-provider',
  MAIN_WRAPPER: 'root-layout-main-wrapper',
  NAVBAR: 'root-layout-navbar',
  MAIN_CONTENT: 'root-layout-main-content',
  FOOTER: 'root-layout-footer',
  COOKIE_BANNER: 'root-layout-cookie-banner',
  TOASTER: 'root-layout-toaster',
  SONNER_TOASTER: 'root-layout-sonner-toaster',
} as const;

// Dashboard Layout Test IDs
export const DASHBOARD_LAYOUT_TEST_IDS = {
  CONTAINER: 'dashboard-layout-container',
  MAIN_WRAPPER: 'dashboard-layout-main-wrapper',
  MODE_SELECTOR_SECTION: 'dashboard-layout-mode-selector-section',
  MODE_SELECTOR_TABS: 'dashboard-layout-mode-selector-tabs',
  MODE_SELECTOR_TABS_LIST: 'dashboard-layout-mode-selector-tabs-list',
  MERCHANT_TAB: 'dashboard-layout-merchant-tab',
  CUSTOMER_TAB: 'dashboard-layout-customer-tab',
  MERCHANT_TAB_ICON: 'dashboard-layout-merchant-tab-icon',
  CUSTOMER_TAB_ICON: 'dashboard-layout-customer-tab-icon',
  MERCHANT_TAB_LABEL: 'dashboard-layout-merchant-tab-label',
  CUSTOMER_TAB_LABEL: 'dashboard-layout-customer-tab-label',
  CONTENT_CONTAINER: 'dashboard-layout-content-container',
} as const;

// ==========================================
// PHASE 2: MERCHANT FEATURE PAGES
// ==========================================

// Merchant Dashboard Test IDs
export const MERCHANT_DASHBOARD_TEST_IDS = {
  CONTAINER: 'merchant-dashboard-container',
  HEADER: 'merchant-dashboard-header',
  TITLE: 'merchant-dashboard-title',
  ADD_SHOP_BUTTON: 'merchant-dashboard-add-shop-button',
  
  // Stats Cards
  STATS_GRID: 'merchant-dashboard-stats-grid',
  CREDITS_ISSUED_CARD: 'merchant-dashboard-credits-issued-card',
  CREDITS_ISSUED_ICON: 'merchant-dashboard-credits-issued-icon',
  CREDITS_ISSUED_TITLE: 'merchant-dashboard-credits-issued-title',
  CREDITS_ISSUED_VALUE: 'merchant-dashboard-credits-issued-value',
  CREDITS_ISSUED_BADGE: 'merchant-dashboard-credits-issued-badge',
  
  CREDITS_REDEEMED_CARD: 'merchant-dashboard-credits-redeemed-card',
  CREDITS_REDEEMED_ICON: 'merchant-dashboard-credits-redeemed-icon',
  CREDITS_REDEEMED_TITLE: 'merchant-dashboard-credits-redeemed-title',
  CREDITS_REDEEMED_VALUE: 'merchant-dashboard-credits-redeemed-value',
  CREDITS_REDEEMED_BADGE: 'merchant-dashboard-credits-redeemed-badge',
  
  // Main Content Tabs
  CONTENT_TABS: 'merchant-dashboard-content-tabs',
  TABS_LIST: 'merchant-dashboard-tabs-list',
  MY_SHOPS_TAB: 'merchant-dashboard-my-shops-tab',
  ANALYTICS_TAB: 'merchant-dashboard-analytics-tab',
  USAGE_TAB: 'merchant-dashboard-usage-tab',
  QUICK_ACTIONS_TAB: 'merchant-dashboard-quick-actions-tab',
  
  // Tab Content
  TAB_CONTENT: 'merchant-dashboard-tab-content',
  
  // Quick Actions Tab
  QUICK_ACTIONS: {
    TAB_CONTENT: 'merchant-quick-actions-tab-content',
    CARD: 'merchant-quick-actions-card',
    HEADER: 'merchant-quick-actions-header',
    TITLE: 'merchant-quick-actions-title',
    DESCRIPTION: 'merchant-quick-actions-description',
    CONTENT: 'merchant-quick-actions-content',
    GRID: 'merchant-quick-actions-grid',
    
    // Action Cards
    MANAGE_CREDITS_CARD: 'merchant-quick-action-manage-credits-card',
    MANAGE_CREDITS_LINK: 'merchant-quick-action-manage-credits-link',
    MANAGE_CREDITS_ICON: 'merchant-quick-action-manage-credits-icon',
    MANAGE_CREDITS_TITLE: 'merchant-quick-action-manage-credits-title',
    MANAGE_CREDITS_DESCRIPTION: 'merchant-quick-action-manage-credits-description',
    
    CREDIT_CODES_CARD: 'merchant-quick-action-credit-codes-card',
    CREDIT_CODES_LINK: 'merchant-quick-action-credit-codes-link',
    CREDIT_CODES_ICON: 'merchant-quick-action-credit-codes-icon',
    CREDIT_CODES_TITLE: 'merchant-quick-action-credit-codes-title',
    CREDIT_CODES_DESCRIPTION: 'merchant-quick-action-credit-codes-description',
    
    CREATE_SHOP_CARD: 'merchant-quick-action-create-shop-card',
    CREATE_SHOP_LINK: 'merchant-quick-action-create-shop-link',
    CREATE_SHOP_ICON: 'merchant-quick-action-create-shop-icon',
    CREATE_SHOP_TITLE: 'merchant-quick-action-create-shop-title',
    CREATE_SHOP_DESCRIPTION: 'merchant-quick-action-create-shop-description',
    
    PROFILE_CARD: 'merchant-quick-action-profile-card',
    PROFILE_LINK: 'merchant-quick-action-profile-link',
    PROFILE_ICON: 'merchant-quick-action-profile-icon',
    PROFILE_TITLE: 'merchant-quick-action-profile-title',
    PROFILE_DESCRIPTION: 'merchant-quick-action-profile-description',
  },
  
  // Mobile Navigation
  MOBILE_HEADER: 'merchant-dashboard-mobile-header',
  MOBILE_MENU_BUTTON: 'merchant-dashboard-mobile-menu-button',
  MOBILE_DRAWER: 'merchant-dashboard-mobile-drawer',
} as const;

// Webhooks Management Test IDs
export const WEBHOOKS_TEST_IDS = {
  // Page Structure
  CONTAINER: 'webhooks-container',
  HEADER: 'webhooks-header',
  TITLE: 'webhooks-title',
  CREATE_BUTTON: 'webhooks-create-button',
  LOADING_SPINNER: 'webhooks-loading-spinner',
  
  // Create Dialog
  CREATE_DIALOG: {
    CONTAINER: 'webhooks-create-dialog-container',
    TITLE: 'webhooks-create-dialog-title',
    DESCRIPTION: 'webhooks-create-dialog-description',
    FORM: 'webhooks-create-dialog-form',
    NAME_INPUT: 'webhooks-create-dialog-name-input',
    URL_INPUT: 'webhooks-create-dialog-url-input',
    SECRET_INPUT: 'webhooks-create-dialog-secret-input',
    EVENTS_SECTION: 'webhooks-create-dialog-events-section',
    EVENT_CHECKBOX: (eventId: string) => `webhooks-create-dialog-event-checkbox-${eventId}`,
    CREATE_BUTTON: 'webhooks-create-dialog-create-button',
  },
  
  // Table
  TABLE: {
    CARD: 'webhooks-table-card',
    TITLE: 'webhooks-table-title',
    DESCRIPTION: 'webhooks-table-description',
    CONTAINER: 'webhooks-table-container',
    TABLE: 'webhooks-table-table',
    HEADER: 'webhooks-table-header',
    BODY: 'webhooks-table-body',
    ROW: (webhookId: string) => `webhooks-table-row-${webhookId}`,
    NAME_CELL: (webhookId: string) => `webhooks-table-name-cell-${webhookId}`,
    URL_CELL: (webhookId: string) => `webhooks-table-url-cell-${webhookId}`,
    EVENTS_CELL: (webhookId: string) => `webhooks-table-events-cell-${webhookId}`,
    STATUS_CELL: (webhookId: string) => `webhooks-table-status-cell-${webhookId}`,
    ACTIONS_CELL: (webhookId: string) => `webhooks-table-actions-cell-${webhookId}`,
    DELIVERIES_BUTTON: (webhookId: string) => `webhooks-table-deliveries-button-${webhookId}`,
    TOGGLE_BUTTON: (webhookId: string) => `webhooks-table-toggle-button-${webhookId}`,
    DELETE_BUTTON: (webhookId: string) => `webhooks-table-delete-button-${webhookId}`,
    EMPTY_STATE: 'webhooks-table-empty-state',
  },
  
  // Deliveries Dialog
  DELIVERIES_DIALOG: {
    CONTAINER: 'webhooks-deliveries-dialog-container',
    TITLE: 'webhooks-deliveries-dialog-title',
    DESCRIPTION: 'webhooks-deliveries-dialog-description',
    LOADING: 'webhooks-deliveries-dialog-loading',
    TABLE_CONTAINER: 'webhooks-deliveries-dialog-table-container',
    TABLE: 'webhooks-deliveries-dialog-table',
    TABLE_HEADER: 'webhooks-deliveries-dialog-table-header',
    TABLE_BODY: 'webhooks-deliveries-dialog-table-body',
    TABLE_ROW: (deliveryId: string) => `webhooks-deliveries-dialog-table-row-${deliveryId}`,
    EVENT_CELL: (deliveryId: string) => `webhooks-deliveries-dialog-event-cell-${deliveryId}`,
    STATUS_CELL: (deliveryId: string) => `webhooks-deliveries-dialog-status-cell-${deliveryId}`,
    TIME_CELL: (deliveryId: string) => `webhooks-deliveries-dialog-time-cell-${deliveryId}`,
    DURATION_CELL: (deliveryId: string) => `webhooks-deliveries-dialog-duration-cell-${deliveryId}`,
    EMPTY_STATE: 'webhooks-deliveries-dialog-empty-state',
  },
} as const;

// Documentation Pages Test IDs
export const DOCS_TEST_IDS = {
  // Main Docs Page
  CONTAINER: 'docs-container',
  TITLE: 'docs-title',
  TABS_LIST: 'docs-tabs-list',
  TAB_TRIGGER: (tabValue: string) => `docs-tab-trigger-${tabValue}`,
  TAB_CONTENT: (tabValue: string) => `docs-tab-content-${tabValue}`,
  
  // Getting Started Tab
  GETTING_STARTED: {
    CARD: 'docs-getting-started-card',
    TITLE: 'docs-getting-started-title',
    DESCRIPTION: 'docs-getting-started-description',
    INTRODUCTION_TITLE: 'docs-getting-started-introduction-title',
    FEATURES_LIST: 'docs-getting-started-features-list',
    QUICK_START_TITLE: 'docs-getting-started-quick-start-title',
    QUICK_START_LIST: 'docs-getting-started-quick-start-list',
    BASE_URL_TITLE: 'docs-getting-started-base-url-title',
    BASE_URL_CODE: 'docs-getting-started-base-url-code',
    API_EXAMPLES_BANNER: 'docs-getting-started-api-examples-banner',
    API_EXAMPLES_BUTTON: 'docs-getting-started-api-examples-button',
    AUTHENTICATION_TITLE: 'docs-getting-started-authentication-title',
    AUTHENTICATION_CODE_BLOCK: 'docs-getting-started-authentication-code-block',
  },
  
  // API Keys Tab
  API_KEYS: {
    CARD: 'docs-api-keys-card',
    TITLE: 'docs-api-keys-title',
    DESCRIPTION: 'docs-api-keys-description',
    CREATING_TITLE: 'docs-api-keys-creating-title',
    PERMISSIONS_TITLE: 'docs-api-keys-permissions-title',
    PERMISSIONS_LIST: 'docs-api-keys-permissions-list',
    USING_TITLE: 'docs-api-keys-using-title',
    USING_CODE_BLOCK: 'docs-api-keys-using-code-block',
    ENDPOINTS_TITLE: 'docs-api-keys-endpoints-title',
    GET_ALL_TITLE: 'docs-api-keys-get-all-title',
    GET_ALL_CODE_BLOCK: 'docs-api-keys-get-all-code-block',
    CREATE_TITLE: 'docs-api-keys-create-title',
    CREATE_CODE_BLOCK: 'docs-api-keys-create-code-block',
    UPDATE_TITLE: 'docs-api-keys-update-title',
    UPDATE_CODE_BLOCK: 'docs-api-keys-update-code-block',
    DELETE_TITLE: 'docs-api-keys-delete-title',
    DELETE_CODE_BLOCK: 'docs-api-keys-delete-code-block',
    TOGGLE_TITLE: 'docs-api-keys-toggle-title',
    TOGGLE_CODE_BLOCK: 'docs-api-keys-toggle-code-block',
    SECURITY_TITLE: 'docs-api-keys-security-title',
  },
  
  // Credits Tab
  CREDITS: {
    CARD: 'docs-credits-card',
    TITLE: 'docs-credits-title',
    DESCRIPTION: 'docs-credits-description',
    HOW_WORKS_TITLE: 'docs-credits-how-works-title',
    ALLOCATION_TITLE: 'docs-credits-allocation-title',
    ALLOCATION_LIST: 'docs-credits-allocation-list',
    CHECKING_BALANCE_TITLE: 'docs-credits-checking-balance-title',
    BALANCE_CODE_BLOCK: 'docs-credits-balance-code-block',
    USAGE_TITLE: 'docs-credits-usage-title',
  },
  
  // Webhooks Tab
  WEBHOOKS: {
    CARD: 'docs-webhooks-card',
    TITLE: 'docs-webhooks-title',
    DESCRIPTION: 'docs-webhooks-description',
    WHAT_ARE_TITLE: 'docs-webhooks-what-are-title',
    EVENTS_TITLE: 'docs-webhooks-events-title',
    EVENTS_LIST: 'docs-webhooks-events-list',
    CREATING_TITLE: 'docs-webhooks-creating-title',
    CREATING_CODE_BLOCK: 'docs-webhooks-creating-code-block',
    SECURITY_TITLE: 'docs-webhooks-security-title',
  },
  
  // Code Block Component
  CODE_BLOCK: {
    CONTAINER: 'docs-code-block-container',
    COPY_BUTTON: 'docs-code-block-copy-button',
    COPY_ICON: 'docs-code-block-copy-icon',
    CHECK_ICON: 'docs-code-block-check-icon',
    LANGUAGE_LABEL: 'docs-code-block-language-label',
    PRE_ELEMENT: 'docs-code-block-pre',
    CODE_ELEMENT: 'docs-code-block-code',
  },
} as const;

// API Reference Page Test IDs
export const API_REFERENCE_TEST_IDS = {
  CONTAINER: 'api-reference-container',
  TITLE: 'api-reference-title',
  DESCRIPTION: 'api-reference-description',
  
  // Endpoint Groups
  ENDPOINT_GROUP: (groupName: string) => `api-reference-endpoint-group-${groupName}`,
  ENDPOINT_TITLE: (groupName: string) => `api-reference-endpoint-title-${groupName}`,
  ENDPOINT_DESCRIPTION: (groupName: string) => `api-reference-endpoint-description-${groupName}`,
  
  // Individual Endpoints
  ENDPOINT: (endpointId: string) => `api-reference-endpoint-${endpointId}`,
  METHOD_BADGE: (endpointId: string) => `api-reference-method-badge-${endpointId}`,
  URL_PATH: (endpointId: string) => `api-reference-url-path-${endpointId}`,
  REQUEST_SECTION: (endpointId: string) => `api-reference-request-section-${endpointId}`,
  RESPONSE_SECTION: (endpointId: string) => `api-reference-response-section-${endpointId}`,
  EXAMPLE_REQUEST: (endpointId: string) => `api-reference-example-request-${endpointId}`,
  EXAMPLE_RESPONSE: (endpointId: string) => `api-reference-example-response-${endpointId}`,
  
  // Interactive Elements
  TRY_BUTTON: (endpointId: string) => `api-reference-try-button-${endpointId}`,
  COPY_CURL_BUTTON: (endpointId: string) => `api-reference-copy-curl-button-${endpointId}`,
} as const;

// API Examples Page Test IDs  
export const API_EXAMPLES_TEST_IDS = {
  CONTAINER: 'api-examples-container',
  TITLE: 'api-examples-title',
  DESCRIPTION: 'api-examples-description',
  
  // Example Categories
  CATEGORY_TABS: 'api-examples-category-tabs',
  CATEGORY_TAB: (category: string) => `api-examples-category-tab-${category}`,
  CATEGORY_CONTENT: (category: string) => `api-examples-category-content-${category}`,
  
  // Individual Examples
  EXAMPLE_CARD: (exampleId: string) => `api-examples-example-card-${exampleId}`,
  EXAMPLE_TITLE: (exampleId: string) => `api-examples-example-title-${exampleId}`,
  EXAMPLE_DESCRIPTION: (exampleId: string) => `api-examples-example-description-${exampleId}`,
  EXAMPLE_CODE: (exampleId: string) => `api-examples-example-code-${exampleId}`,
  EXAMPLE_COPY_BUTTON: (exampleId: string) => `api-examples-example-copy-button-${exampleId}`,
  EXAMPLE_TRY_BUTTON: (exampleId: string) => `api-examples-example-try-button-${exampleId}`,
  
  // Code Examples
  CODE_SNIPPET: (snippetId: string) => `api-examples-code-snippet-${snippetId}`,
  RESPONSE_PREVIEW: (snippetId: string) => `api-examples-response-preview-${snippetId}`,
} as const;

// Shop Detail Page Test IDs
export const SHOP_DETAIL_TEST_IDS = {
  // Page Structure
  CONTAINER: 'shop-detail-container',
  BREADCRUMBS: 'shop-detail-breadcrumbs',
  HEADER: 'shop-detail-header',
  TITLE: 'shop-detail-title',
  
  // Loading States
  LOADING_CONTAINER: 'shop-detail-loading-container',
  LOADING_SPINNER: 'shop-detail-loading-spinner',
  LOADING_SKELETON: 'shop-detail-loading-skeleton',
  
  // Error States
  ERROR_CONTAINER: 'shop-detail-error-container',
  ERROR_TITLE: 'shop-detail-error-title',
  ERROR_MESSAGE: 'shop-detail-error-message',
  RETURN_BUTTON: 'shop-detail-return-button',
  
  // Shop Profile Section
  PROFILE_CARD: 'shop-detail-profile-card',
  SHOP_AVATAR: 'shop-detail-shop-avatar',
  SHOP_NAME: 'shop-detail-shop-name',
  SHOP_DESCRIPTION: 'shop-detail-shop-description',
  
  // Tabs Navigation
  TABS_CONTAINER: 'shop-detail-tabs-container',
  TABS_LIST: 'shop-detail-tabs-list',
  TAB_TRIGGER: (tabValue: string) => `shop-detail-tab-trigger-${tabValue}`,
  TAB_CONTENT: (tabValue: string) => `shop-detail-tab-content-${tabValue}`,
  
  // Overview Tab
  OVERVIEW: {
    CONTAINER: 'shop-detail-overview-container',
    ACTION_BUTTONS: 'shop-detail-overview-action-buttons',
    MANAGE_CREDITS_BUTTON: 'shop-detail-overview-manage-credits-button',
    GENERATE_CODE_BUTTON: 'shop-detail-overview-generate-code-button',
    SHOP_OVERVIEW_COMPONENT: 'shop-detail-overview-component',
  },
  
  // Customers Tab
  CUSTOMERS: {
    CONTAINER: 'shop-detail-customers-container',
    FILTERS: 'shop-detail-customers-filters',
    SEARCH_INPUT: 'shop-detail-customers-search-input',
    SORT_SELECT: 'shop-detail-customers-sort-select',
    MIN_CREDIT_INPUT: 'shop-detail-customers-min-credit-input',
    MAX_CREDIT_INPUT: 'shop-detail-customers-max-credit-input',
    CLEAR_FILTERS_BUTTON: 'shop-detail-customers-clear-filters-button',
    ADD_CUSTOMER_BUTTON: 'shop-detail-customers-add-customer-button',
    
    // Customer List
    LIST_CONTAINER: 'shop-detail-customers-list-container',
    CUSTOMER_ITEM: (customerId: string) => `shop-detail-customers-item-${customerId}`,
    CUSTOMER_NAME: (customerId: string) => `shop-detail-customers-name-${customerId}`,
    CUSTOMER_EMAIL: (customerId: string) => `shop-detail-customers-email-${customerId}`,
    CUSTOMER_CREDITS: (customerId: string) => `shop-detail-customers-credits-${customerId}`,
    CUSTOMER_ACTIONS: (customerId: string) => `shop-detail-customers-actions-${customerId}`,
    ADD_CREDIT_BUTTON: (customerId: string) => `shop-detail-customers-add-credit-button-${customerId}`,
    
    // Pagination
    PAGINATION: 'shop-detail-customers-pagination',
    PAGINATION_INFO: 'shop-detail-customers-pagination-info',
    PREVIOUS_BUTTON: 'shop-detail-customers-previous-button',
    NEXT_BUTTON: 'shop-detail-customers-next-button',
    PAGE_BUTTON: (pageNumber: number) => `shop-detail-customers-page-button-${pageNumber}`,
    
    // Empty State
    EMPTY_STATE: 'shop-detail-customers-empty-state',
    EMPTY_STATE_ICON: 'shop-detail-customers-empty-state-icon',
    EMPTY_STATE_MESSAGE: 'shop-detail-customers-empty-state-message',
    EMPTY_STATE_ADD_BUTTON: 'shop-detail-customers-empty-state-add-button',
  },
  
  // Statistics Tab
  STATISTICS: {
    CONTAINER: 'shop-detail-statistics-container',
    STATS_GRID: 'shop-detail-statistics-stats-grid',
    TOTAL_CUSTOMERS: 'shop-detail-statistics-total-customers',
    TOTAL_CREDITS_DISTRIBUTED: 'shop-detail-statistics-total-credits-distributed',
    TOTAL_TRANSACTIONS: 'shop-detail-statistics-total-transactions',
    AVERAGE_TRANSACTION: 'shop-detail-statistics-average-transaction',
    
    // Charts
    CHARTS_SECTION: 'shop-detail-statistics-charts-section',
    CREDITS_CHART: 'shop-detail-statistics-credits-chart',
    CUSTOMERS_CHART: 'shop-detail-statistics-customers-chart',
    TRANSACTIONS_CHART: 'shop-detail-statistics-transactions-chart',
    
    // Date Range Picker
    DATE_RANGE_PICKER: 'shop-detail-statistics-date-range-picker',
    DATE_FROM_INPUT: 'shop-detail-statistics-date-from-input',
    DATE_TO_INPUT: 'shop-detail-statistics-date-to-input',
    APPLY_FILTER_BUTTON: 'shop-detail-statistics-apply-filter-button',
  },
  
  // API Keys Tab
  API_KEYS: {
    CONTAINER: 'shop-detail-api-keys-container',
    MANAGER_COMPONENT: 'shop-detail-api-keys-manager-component',
    CREATE_BUTTON: 'shop-detail-api-keys-create-button',
    
    // API Keys List
    LIST: 'shop-detail-api-keys-list',
    KEY_ITEM: (keyId: string) => `shop-detail-api-keys-item-${keyId}`,
    KEY_NAME: (keyId: string) => `shop-detail-api-keys-name-${keyId}`,
    KEY_PREVIEW: (keyId: string) => `shop-detail-api-keys-preview-${keyId}`,
    KEY_STATUS: (keyId: string) => `shop-detail-api-keys-status-${keyId}`,
    KEY_ACTIONS: (keyId: string) => `shop-detail-api-keys-actions-${keyId}`,
    COPY_BUTTON: (keyId: string) => `shop-detail-api-keys-copy-button-${keyId}`,
    EDIT_BUTTON: (keyId: string) => `shop-detail-api-keys-edit-button-${keyId}`,
    DELETE_BUTTON: (keyId: string) => `shop-detail-api-keys-delete-button-${keyId}`,
    TOGGLE_BUTTON: (keyId: string) => `shop-detail-api-keys-toggle-button-${keyId}`,
    
    // Empty State
    EMPTY_STATE: 'shop-detail-api-keys-empty-state',
    EMPTY_STATE_MESSAGE: 'shop-detail-api-keys-empty-state-message',
    EMPTY_STATE_CREATE_BUTTON: 'shop-detail-api-keys-empty-state-create-button',
  },
  
  // Settings Tab
  SETTINGS: {
    CONTAINER: 'shop-detail-settings-container',
    
    // Shop Information
    INFO_SECTION: 'shop-detail-settings-info-section',
    INFO_TITLE: 'shop-detail-settings-info-title',
    NAME_INPUT: 'shop-detail-settings-name-input',
    DESCRIPTION_INPUT: 'shop-detail-settings-description-input',
    SLUG_INPUT: 'shop-detail-settings-slug-input',
    
    // Contact Information
    CONTACT_SECTION: 'shop-detail-settings-contact-section',
    CONTACT_TITLE: 'shop-detail-settings-contact-title',
    EMAIL_INPUT: 'shop-detail-settings-email-input',
    PHONE_INPUT: 'shop-detail-settings-phone-input',
    ADDRESS_INPUT: 'shop-detail-settings-address-input',
    
    // Exchange Rates
    EXCHANGE_RATE_SECTION: 'shop-detail-settings-exchange-rate-section',
    EXCHANGE_RATE_MANAGER: 'shop-detail-settings-exchange-rate-manager',
    
    // Danger Zone
    DANGER_SECTION: 'shop-detail-settings-danger-section',
    DANGER_TITLE: 'shop-detail-settings-danger-title',
    DELETE_SHOP_BUTTON: 'shop-detail-settings-delete-shop-button',
    
    // Actions
    SAVE_BUTTON: 'shop-detail-settings-save-button',
    CANCEL_BUTTON: 'shop-detail-settings-cancel-button',
    SUCCESS_MESSAGE: 'shop-detail-settings-success-message',
    ERROR_MESSAGE: 'shop-detail-settings-error-message',
  },
  
  // Dialogs
  ADD_CUSTOMER_DIALOG: {
    CONTAINER: 'shop-detail-add-customer-dialog-container',
    TITLE: 'shop-detail-add-customer-dialog-title',
    NAME_INPUT: 'shop-detail-add-customer-dialog-name-input',
    EMAIL_INPUT: 'shop-detail-add-customer-dialog-email-input',
    CANCEL_BUTTON: 'shop-detail-add-customer-dialog-cancel-button',
    SUBMIT_BUTTON: 'shop-detail-add-customer-dialog-submit-button',
  },
  
  ADD_CREDIT_DIALOG: {
    CONTAINER: 'shop-detail-add-credit-dialog-container',
    TITLE: 'shop-detail-add-credit-dialog-title',
    CUSTOMER_NAME: 'shop-detail-add-credit-dialog-customer-name',
    AMOUNT_INPUT: 'shop-detail-add-credit-dialog-amount-input',
    DESCRIPTION_INPUT: 'shop-detail-add-credit-dialog-description-input',
    CANCEL_BUTTON: 'shop-detail-add-credit-dialog-cancel-button',
    SUBMIT_BUTTON: 'shop-detail-add-credit-dialog-submit-button',
  },
  
  GENERATE_CODE_DIALOG: {
    CONTAINER: 'shop-detail-generate-code-dialog-container',
    TITLE: 'shop-detail-generate-code-dialog-title',
    AMOUNT_INPUT: 'shop-detail-generate-code-dialog-amount-input',
    DESCRIPTION_INPUT: 'shop-detail-generate-code-dialog-description-input',
    EXPIRES_IN_SELECT: 'shop-detail-generate-code-dialog-expires-in-select',
    CANCEL_BUTTON: 'shop-detail-generate-code-dialog-cancel-button',
    SUBMIT_BUTTON: 'shop-detail-generate-code-dialog-submit-button',
  },

  // Flat constants for shop-detail.tsx compatibility
  TAB_OVERVIEW: 'shop-detail-tab-overview',
  TAB_CUSTOMERS: 'shop-detail-tab-customers',
  TAB_STATISTICS: 'shop-detail-tab-statistics',
  TAB_API_KEYS: 'shop-detail-tab-api-keys',
  TAB_SETTINGS: 'shop-detail-tab-settings',
  
  OVERVIEW_TAB_CONTENT: 'shop-detail-overview-tab-content',
  OVERVIEW_ACTION_BUTTONS: 'shop-detail-overview-action-buttons',
  OVERVIEW_SHOP_OVERVIEW: 'shop-detail-overview-shop-overview',
  
  CUSTOMERS_TAB_CONTENT: 'shop-detail-customers-tab-content',
  CUSTOMERS_HEADER: 'shop-detail-customers-header',
  CUSTOMERS_TITLE: 'shop-detail-customers-title',
  CUSTOMERS_COUNT: 'shop-detail-customers-count',
  CUSTOMERS_ADD_BUTTON: 'shop-detail-customers-add-button',
  CUSTOMERS_FILTERS: 'shop-detail-customers-filters',
  CUSTOMERS_LIST: 'shop-detail-customers-list',
  CUSTOMERS_PAGINATION: 'shop-detail-customers-pagination',
  
  STATISTICS_TAB_CONTENT: 'shop-detail-statistics-tab-content',
  STATISTICS_COMPONENT: 'shop-detail-statistics-component',
  
  API_KEYS_TAB_CONTENT: 'shop-detail-api-keys-tab-content',
  API_KEYS_COMPONENT: 'shop-detail-api-keys-component',
  
  SETTINGS_TAB_CONTENT: 'shop-detail-settings-tab-content',
  SETTINGS_COMPONENT: 'shop-detail-settings-component',
  
  SHOP_PROFILE: 'shop-detail-shop-profile',
  CONTENT: 'shop-detail-content',
  DIALOGS_CONTAINER: 'shop-detail-dialogs-container',
  ADD_CUSTOMER_DIALOG: 'shop-detail-add-customer-dialog',
  ADD_CREDIT_DIALOG: 'shop-detail-add-credit-dialog',
  GENERATE_CODE_DIALOG: 'shop-detail-generate-code-dialog',
  
  // Loading state test IDs
  LOADING_AVATAR: 'shop-detail-loading-avatar',
  LOADING_TITLE: 'shop-detail-loading-title',
  LOADING_DESCRIPTION: 'shop-detail-loading-description',
  LOADING_SECTION_TITLE: 'shop-detail-loading-section-title',
  LOADING_LIST: 'shop-detail-loading-list',
  LOADING_LIST_ITEM: (index: string) => `shop-detail-loading-list-item-${index}`,
  
  // Error state test IDs
  ERROR_RETURN_BUTTON: 'shop-detail-error-return-button',
} as const;

// Merchant Profile Page Test IDs
export const MERCHANT_PROFILE_TEST_IDS = {
  CONTAINER: 'merchant-profile-container',
  HEADER: 'merchant-profile-header',
  TITLE: 'merchant-profile-title',
  
  // Profile Form
  FORM: 'merchant-profile-form',
  AVATAR_SECTION: 'merchant-profile-avatar-section',
  AVATAR_IMAGE: 'merchant-profile-avatar-image',
  AVATAR_UPLOAD_BUTTON: 'merchant-profile-avatar-upload-button',
  
  // Personal Information
  PERSONAL_SECTION: 'merchant-profile-personal-section',
  FIRST_NAME_INPUT: 'merchant-profile-first-name-input',
  LAST_NAME_INPUT: 'merchant-profile-last-name-input',
  EMAIL_INPUT: 'merchant-profile-email-input',
  PHONE_INPUT: 'merchant-profile-phone-input',
  
  // Business Information
  BUSINESS_SECTION: 'merchant-profile-business-section',
  BUSINESS_NAME_INPUT: 'merchant-profile-business-name-input',
  BUSINESS_TYPE_SELECT: 'merchant-profile-business-type-select',
  BUSINESS_ADDRESS_INPUT: 'merchant-profile-business-address-input',
  
  // Preferences
  PREFERENCES_SECTION: 'merchant-profile-preferences-section',
  TIMEZONE_SELECT: 'merchant-profile-timezone-select',
  LANGUAGE_SELECT: 'merchant-profile-language-select',
  CURRENCY_SELECT: 'merchant-profile-currency-select',
  
  // Actions
  SAVE_BUTTON: 'merchant-profile-save-button',
  CANCEL_BUTTON: 'merchant-profile-cancel-button',
  SUCCESS_MESSAGE: 'merchant-profile-success-message',
  ERROR_MESSAGE: 'merchant-profile-error-message',
} as const;

// Merchant Codes Page Test IDs
export const MERCHANT_CODES_TEST_IDS = {
  CONTAINER: 'merchant-codes-container',
  HEADER: 'merchant-codes-header',
  TITLE: 'merchant-codes-title',
  GENERATE_BUTTON: 'merchant-codes-generate-button',
  
  // Filters
  FILTERS_SECTION: 'merchant-codes-filters-section',
  SHOP_FILTER: 'merchant-codes-shop-filter',
  STATUS_FILTER: 'merchant-codes-status-filter',
  DATE_RANGE_FILTER: 'merchant-codes-date-range-filter',
  
  // Codes Table
  TABLE: 'merchant-codes-table',
  TABLE_HEADER: 'merchant-codes-table-header',
  TABLE_BODY: 'merchant-codes-table-body',
  CODE_ROW: (codeId: string) => `merchant-codes-table-row-${codeId}`,
  CODE_VALUE: (codeId: string) => `merchant-codes-table-value-${codeId}`,
  CODE_AMOUNT: (codeId: string) => `merchant-codes-table-amount-${codeId}`,
  CODE_STATUS: (codeId: string) => `merchant-codes-table-status-${codeId}`,
  CODE_SHOP: (codeId: string) => `merchant-codes-table-shop-${codeId}`,
  CODE_CREATED: (codeId: string) => `merchant-codes-table-created-${codeId}`,
  CODE_ACTIONS: (codeId: string) => `merchant-codes-table-actions-${codeId}`,
  VIEW_BUTTON: (codeId: string) => `merchant-codes-table-view-button-${codeId}`,
  COPY_BUTTON: (codeId: string) => `merchant-codes-table-copy-button-${codeId}`,
  DELETE_BUTTON: (codeId: string) => `merchant-codes-table-delete-button-${codeId}`,
  
  // Empty State
  EMPTY_STATE: 'merchant-codes-empty-state',
  EMPTY_STATE_MESSAGE: 'merchant-codes-empty-state-message',
  EMPTY_STATE_BUTTON: 'merchant-codes-empty-state-button',
  
  // Credit Code Detail Page
  DETAIL: {
    CONTAINER: 'credit-code-detail-container',
    HEADER: 'credit-code-detail-header',
    BACK_LINK: 'credit-code-detail-back-link',
    TITLE: 'credit-code-detail-title',
    
    // Loading State
    LOADING_CONTAINER: 'credit-code-detail-loading-container',
    LOADING_CARD: 'credit-code-detail-loading-card',
    LOADING_HEADER: 'credit-code-detail-loading-header',
    LOADING_QR: 'credit-code-detail-loading-qr',
    LOADING_CODE: 'credit-code-detail-loading-code',
    LOADING_BUTTONS: 'credit-code-detail-loading-buttons',
    
    // Error State
    ERROR_CONTAINER: 'credit-code-detail-error-container',
    ERROR_CARD: 'credit-code-detail-error-card',
    ERROR_MESSAGE: 'credit-code-detail-error-message',
    ERROR_DESCRIPTION: 'credit-code-detail-error-description',
    ERROR_RETURN_BUTTON: 'credit-code-detail-error-return-button',
    
    // Content
    CONTENT_CARD: 'credit-code-detail-content-card',
    CONTENT_HEADER: 'credit-code-detail-content-header',
    CONTENT_TITLE: 'credit-code-detail-content-title',
    CONTENT_BODY: 'credit-code-detail-content-body',
    
    // QR Code Section
    QR_CONTAINER: 'credit-code-detail-qr-container',
    QR_IMAGE: 'credit-code-detail-qr-image',
    QR_LOADING: 'credit-code-detail-qr-loading',
    
    // Code Section
    CODE_CONTAINER: 'credit-code-detail-code-container',
    CODE_TEXT: 'credit-code-detail-code-text',
    DESCRIPTION: 'credit-code-detail-description',
    EXPIRES_TEXT: 'credit-code-detail-expires-text',
    USED_BADGE: 'credit-code-detail-used-badge',
    
    // Action Buttons
    ACTIONS_FOOTER: 'credit-code-detail-actions-footer',
    COPY_BUTTON: 'credit-code-detail-copy-button',
    DOWNLOAD_BUTTON: 'credit-code-detail-download-button',
    SHARE_BUTTON: 'credit-code-detail-share-button',
    
    // Footer
    FOOTER: 'credit-code-detail-footer',
    BACK_TO_SHOP_BUTTON: 'credit-code-detail-back-to-shop-button',
  },
  
  // Pagination
  PAGINATION: 'merchant-codes-pagination',
  PAGINATION_INFO: 'merchant-codes-pagination-info',
} as const;

// Customer Pages Test IDs
export const CUSTOMER_SCAN_TEST_IDS = {
  CONTAINER: 'customer-scan-container',
  MAIN_CONTENT: 'customer-scan-main-content',
  BREADCRUMBS: 'customer-scan-breadcrumbs',
  HEADER: 'customer-scan-header',
  SCAN_CARD: 'customer-scan-card',
  CARD_HEADER: 'customer-scan-card-header',
  CARD_TITLE: 'customer-scan-card-title',
  CARD_DESCRIPTION: 'customer-scan-card-description',
  CARD_CONTENT: 'customer-scan-card-content',
  CARD_FOOTER: 'customer-scan-card-footer',
  
  // Scanner States
  SCANNER_ACTIVE: 'customer-scan-scanner-active',
  CAMERA_PERMISSION_STATE: 'customer-scan-camera-permission-state',
  CAMERA_ICON: 'customer-scan-camera-icon',
  PERMISSION_MESSAGE: 'customer-scan-permission-message',
  PERMISSION_DENIED_ACTIONS: 'customer-scan-permission-denied-actions',
  HELP_BUTTON: 'customer-scan-help-button',
  REFRESH_BUTTON: 'customer-scan-refresh-button',
  START_SCANNER_BUTTON: 'customer-scan-start-scanner-button',
  
  // Results
  REDEMPTION_RESULT: 'customer-scan-redemption-result',
  SUCCESS_ALERT: 'customer-scan-success-alert',
  SUCCESS_ICON: 'customer-scan-success-icon',
  SUCCESS_TITLE: 'customer-scan-success-title',
  SUCCESS_MESSAGE: 'customer-scan-success-message',
  ERROR_ALERT: 'customer-scan-error-alert',
  ERROR_ICON: 'customer-scan-error-icon',
  ERROR_TITLE: 'customer-scan-error-title',
  ERROR_MESSAGE: 'customer-scan-error-message',
  
  // Processing State
  PROCESSING_STATE: 'customer-scan-processing-state',
  PROCESSING_ALERT: 'customer-scan-processing-alert',
  PROCESSING_ICON: 'customer-scan-processing-icon',
  PROCESSING_TITLE: 'customer-scan-processing-title',
  PROCESSING_MESSAGE: 'customer-scan-processing-message',
  
  // Actions
  RESULT_ACTIONS: 'customer-scan-result-actions',
  SCAN_ANOTHER_BUTTON: 'customer-scan-scan-another-button',
  VIEW_CREDITS_BUTTON: 'customer-scan-view-credits-button',
  SCAN_ACTIONS: 'customer-scan-scan-actions',
  MANUAL_ENTRY_BUTTON: 'customer-scan-manual-entry-button',
  TROUBLESHOOTING_SECTION: 'customer-scan-troubleshooting-section',
  TROUBLESHOOTING_TOGGLE: 'customer-scan-troubleshooting-toggle',
  TROUBLESHOOTING_CONTENT: 'customer-scan-troubleshooting-content',
  
  // Footer
  FOOTER: 'customer-scan-footer',
  MY_CREDITS_FOOTER_BUTTON: 'customer-scan-my-credits-footer-button',
  CREDIT_ICON: 'customer-scan-credit-icon',
} as const;

export const CUSTOMER_SHOPS_TEST_IDS = {
  CONTAINER: 'customer-shops-container',
  BREADCRUMBS: 'customer-shops-breadcrumbs',
  HEADER: 'customer-shops-header',
  SEARCH_SECTION: 'customer-shops-search-section',
  SEARCH_CONTAINER: 'customer-shops-search-container',
  SEARCH_ICON: 'customer-shops-search-icon',
  SEARCH_INPUT: 'customer-shops-search-input',
  
  // Shops List
  SHOPS_LIST: 'customer-shops-list',
  LOADING_STATE: 'customer-shops-loading-state',
  SHOP_CARD: (shopId: string) => `customer-shops-shop-card-${shopId}`,
  SHOP_LINK: (shopId: string) => `customer-shops-shop-link-${shopId}`,
  SHOP_IMAGE_CONTAINER: (shopId: string) => `customer-shops-shop-image-container-${shopId}`,
  SHOP_IMAGE: (shopId: string) => `customer-shops-shop-image-${shopId}`,
  SHOP_PLACEHOLDER: (shopId: string) => `customer-shops-shop-placeholder-${shopId}`,
  SHOP_PLACEHOLDER_ICON: (shopId: string) => `customer-shops-shop-placeholder-icon-${shopId}`,
  SHOP_CONTENT: (shopId: string) => `customer-shops-shop-content-${shopId}`,
  SHOP_NAME: (shopId: string) => `customer-shops-shop-name-${shopId}`,
  SHOP_DESCRIPTION: (shopId: string) => `customer-shops-shop-description-${shopId}`,
  SHOP_FOOTER: (shopId: string) => `customer-shops-shop-footer-${shopId}`,
  SHOP_CREDITS_INFO: (shopId: string) => `customer-shops-shop-credits-info-${shopId}`,
  SHOP_CREDITS_VALUE: (shopId: string) => `customer-shops-shop-credits-value-${shopId}`,
  SHOP_NO_CREDITS: (shopId: string) => `customer-shops-shop-no-credits-${shopId}`,
  SHOP_VIEW_BUTTON: (shopId: string) => `customer-shops-shop-view-button-${shopId}`,
  
  // Empty State
  EMPTY_STATE: 'customer-shops-empty-state',
  EMPTY_STATE_ICON: 'customer-shops-empty-state-icon',
  EMPTY_STATE_TITLE: 'customer-shops-empty-state-title',
  EMPTY_STATE_MESSAGE: 'customer-shops-empty-state-message',
  CLEAR_SEARCH_BUTTON: 'customer-shops-clear-search-button',
  
  // Footer Navigation
  FOOTER: 'customer-shops-footer',
  NAVIGATION_BAR: 'customer-shops-navigation-bar',
  HOME_LINK: 'customer-shops-home-link',
  HOME_ICON_CONTAINER: 'customer-shops-home-icon-container',
  HOME_ICON: 'customer-shops-home-icon',
  HOME_LABEL: 'customer-shops-home-label',
  SHOPS_TAB_ACTIVE: 'customer-shops-shops-tab-active',
  SHOPS_ICON_CONTAINER_ACTIVE: 'customer-shops-shops-icon-container-active',
  SHOPS_ICON_ACTIVE: 'customer-shops-shops-icon-active',
  SHOPS_LABEL_ACTIVE: 'customer-shops-shops-label-active',
} as const;

export const CUSTOMER_SHOP_DETAIL_TEST_IDS = {
  CONTAINER: 'customer-shop-detail-container',
  LOADING_CONTAINER: 'customer-shop-detail-loading-container',
  ERROR_CONTAINER: 'customer-shop-detail-error-container',
  ERROR_TITLE: 'customer-shop-detail-error-title',
  ERROR_MESSAGE: 'customer-shop-detail-error-message',
  ERROR_BACK_BUTTON: 'customer-shop-detail-error-back-button',
  
  // Header Section
  HEADER: 'customer-shop-detail-header',
  SHOP_NAME: 'customer-shop-detail-shop-name',
  SHOP_DESCRIPTION: 'customer-shop-detail-shop-description',
  
  // Main Content
  MAIN_CONTENT: 'customer-shop-detail-main-content',
  CREDIT_ACTIVITY_SECTION: 'customer-shop-detail-credit-activity-section',
} as const;

export const CUSTOMER_USE_CREDIT_DIALOG_TEST_IDS = {
  CONTAINER: 'customer-use-credit-dialog-container',
  TITLE: 'customer-use-credit-dialog-title',
  DESCRIPTION: 'customer-use-credit-dialog-description',
  BALANCE_CARD: 'customer-use-credit-dialog-balance-card',
  BALANCE_AMOUNT: 'customer-use-credit-dialog-balance-amount',
  AMOUNT_INPUT: 'customer-use-credit-dialog-amount-input',
  DESCRIPTION_INPUT: 'customer-use-credit-dialog-description-input',
  CANCEL_BUTTON: 'customer-use-credit-dialog-cancel-button',
  SUBMIT_BUTTON: 'customer-use-credit-dialog-submit-button',
} as const;

export const CUSTOMER_TRANSACTION_HISTORY_TEST_IDS = {
  CONTAINER: 'customer-transaction-history-container',
  LOADING_CONTAINER: 'customer-transaction-history-loading-container',
  LOADING_SKELETON: 'customer-transaction-history-loading-skeleton',
  TABLE: 'customer-transaction-history-table',
  TABLE_BODY: 'customer-transaction-history-table-body',
  TRANSACTION_ROW: (transactionId: string) => `customer-transaction-history-row-${transactionId}`,
  EMPTY_STATE: 'customer-transaction-history-empty-state',
} as const;

export const CUSTOMER_CREDIT_BALANCE_CARD_TEST_IDS = {
  CONTAINER: 'customer-credit-balance-card-container',
  HEADER: 'customer-credit-balance-card-header',
  TITLE: 'customer-credit-balance-card-title',
  DESCRIPTION: 'customer-credit-balance-card-description',
  CONTENT: 'customer-credit-balance-card-content',
  BALANCE_SECTION: 'customer-credit-balance-card-balance-section',
  BALANCE_VALUE: 'customer-credit-balance-card-balance-value',
  BALANCE_LABEL: 'customer-credit-balance-card-balance-label',
  ACTIONS_SECTION: 'customer-credit-balance-card-actions-section',
  SCAN_QR_BUTTON: 'customer-credit-balance-card-scan-qr-button',
  ENTER_MANUALLY_BUTTON: 'customer-credit-balance-card-enter-manually-button',
  HELP_TEXT: 'customer-credit-balance-card-help-text',
} as const;

export const CUSTOMER_SHOP_PROFILE_CARD_TEST_IDS = {
  CONTAINER: 'customer-shop-profile-card-container',
  BANNER: 'customer-shop-profile-card-banner',
  CONTENT: 'customer-shop-profile-card-content',
  AVATAR: 'customer-shop-profile-card-avatar',
  NAME: 'customer-shop-profile-card-name',
  DESCRIPTION: 'customer-shop-profile-card-description',
  CONTACT_SECTION: 'customer-shop-profile-card-contact-section',
  EMAIL: 'customer-shop-profile-card-email',
  PHONE: 'customer-shop-profile-card-phone',
} as const;

// Credit Display Component Test IDs
export const CREDIT_DISPLAY_TEST_IDS = {
  // Main CreditDisplay component
  CONTAINER: 'credit-display-container',
  CREDITS_BADGE: 'credit-display-credits-badge',
  CALCULATOR_BUTTON: 'credit-display-calculator-button',
  CALCULATOR_ICON: 'credit-display-calculator-icon',
  
  // Conversion section
  CONVERSION_CONTAINER: 'credit-display-conversion-container',
  CURRENCY_SELECT: 'credit-display-currency-select',
  CURRENCY_TRIGGER: 'credit-display-currency-trigger',
  CURRENCY_CONTENT: 'credit-display-currency-content',
  CURRENCY_ITEM: (currency: string) => `credit-display-currency-item-${currency}`,
  
  // Conversion result states
  LOADING_SPINNER: 'credit-display-loading-spinner',
  CONVERSION_RESULT: 'credit-display-conversion-result',
  NO_RATE_BADGE: 'credit-display-no-rate-badge',
  
  // CreditWithConversion component
  WITH_CONVERSION_CONTAINER: 'credit-with-conversion-container',
  WITH_CONVERSION_CREDITS: 'credit-with-conversion-credits',
  WITH_CONVERSION_SEPARATOR: 'credit-with-conversion-separator',
  WITH_CONVERSION_AMOUNT: 'credit-with-conversion-amount',
  
  // CreditInputWithConversion component
  INPUT_CONTAINER: 'credit-input-with-conversion-container',
  INPUT_CARD: 'credit-input-with-conversion-card',
  INPUT_HEADER: 'credit-input-with-conversion-header',
  INPUT_TITLE: 'credit-input-with-conversion-title',
  INPUT_CONTENT: 'credit-input-with-conversion-content',
  INPUT_FIELD: 'credit-input-with-conversion-field',
  INPUT_CONVERSION_DISPLAY: 'credit-input-with-conversion-display',
} as const;

// Cookie Banner Component Test IDs
export const COOKIE_BANNER_TEST_IDS = {
  // Main banner
  CONTAINER: 'cookie-banner-container',
  BANNER: 'cookie-banner-banner',
  ICON: 'cookie-banner-icon',
  CONTENT: 'cookie-banner-content',
  TITLE: 'cookie-banner-title',
  DESCRIPTION: 'cookie-banner-description',
  PRIVACY_LINK: 'cookie-banner-privacy-link',
  
  // Banner buttons
  BUTTONS_CONTAINER: 'cookie-banner-buttons-container',
  CUSTOMIZE_BUTTON: 'cookie-banner-customize-button',
  CUSTOMIZE_ICON: 'cookie-banner-customize-icon',
  ESSENTIAL_ONLY_BUTTON: 'cookie-banner-essential-only-button',
  ACCEPT_ALL_BUTTON: 'cookie-banner-accept-all-button',
  
  // Preferences dialog
  PREFERENCES_DIALOG: 'cookie-banner-preferences-dialog',
  PREFERENCES_TRIGGER: 'cookie-banner-preferences-trigger',
  PREFERENCES_CONTENT: 'cookie-banner-preferences-content',
  PREFERENCES_HEADER: 'cookie-banner-preferences-header',
  PREFERENCES_TITLE: 'cookie-banner-preferences-title',
  PREFERENCES_DESCRIPTION: 'cookie-banner-preferences-description',
  
  // Cookie category cards
  ESSENTIAL_CARD: 'cookie-banner-essential-card',
  ESSENTIAL_HEADER: 'cookie-banner-essential-header',
  ESSENTIAL_TITLE: 'cookie-banner-essential-title',
  ESSENTIAL_DESCRIPTION: 'cookie-banner-essential-description',
  ESSENTIAL_SWITCH: 'cookie-banner-essential-switch',
  ESSENTIAL_CONTENT: 'cookie-banner-essential-content',
  
  FUNCTIONAL_CARD: 'cookie-banner-functional-card',
  FUNCTIONAL_HEADER: 'cookie-banner-functional-header',
  FUNCTIONAL_TITLE: 'cookie-banner-functional-title',
  FUNCTIONAL_DESCRIPTION: 'cookie-banner-functional-description',
  FUNCTIONAL_SWITCH: 'cookie-banner-functional-switch',
  FUNCTIONAL_CONTENT: 'cookie-banner-functional-content',
  
  ANALYTICS_CARD: 'cookie-banner-analytics-card',
  ANALYTICS_HEADER: 'cookie-banner-analytics-header',
  ANALYTICS_TITLE: 'cookie-banner-analytics-title',
  ANALYTICS_DESCRIPTION: 'cookie-banner-analytics-description',
  ANALYTICS_SWITCH: 'cookie-banner-analytics-switch',
  ANALYTICS_CONTENT: 'cookie-banner-analytics-content',
  
  MARKETING_CARD: 'cookie-banner-marketing-card',
  MARKETING_HEADER: 'cookie-banner-marketing-header',
  MARKETING_TITLE: 'cookie-banner-marketing-title',
  MARKETING_DESCRIPTION: 'cookie-banner-marketing-description',
  MARKETING_SWITCH: 'cookie-banner-marketing-switch',
  MARKETING_CONTENT: 'cookie-banner-marketing-content',
  
  // Dialog action buttons
  DIALOG_BUTTONS: 'cookie-banner-dialog-buttons',
  SAVE_PREFERENCES_BUTTON: 'cookie-banner-save-preferences-button',
  DIALOG_ACCEPT_ALL_BUTTON: 'cookie-banner-dialog-accept-all-button',
} as const;

// Customer Credits Display Component Test IDs
export const CUSTOMER_CREDITS_DISPLAY_TEST_IDS = {
  // Main container
  CONTAINER: 'customer-credits-display-container',
  
  // Loading state
  LOADING_CONTAINER: 'customer-credits-display-loading-container',
  LOADING_CARD: 'customer-credits-display-loading-card',
  LOADING_HEADER: 'customer-credits-display-loading-header',
  LOADING_CONTENT: 'customer-credits-display-loading-content',
  
  // Error state
  ERROR_CARD: 'customer-credits-display-error-card',
  ERROR_CONTENT: 'customer-credits-display-error-content',
  ERROR_MESSAGE: 'customer-credits-display-error-message',
  
  // Credit balance overview grid
  OVERVIEW_GRID: 'customer-credits-display-overview-grid',
  
  // Current balance card
  BALANCE_CARD: 'customer-credits-display-balance-card',
  BALANCE_HEADER: 'customer-credits-display-balance-header',
  BALANCE_TITLE: 'customer-credits-display-balance-title',
  BALANCE_ICON: 'customer-credits-display-balance-icon',
  BALANCE_CONTENT: 'customer-credits-display-balance-content',
  BALANCE_AMOUNT: 'customer-credits-display-balance-amount',
  BALANCE_DESCRIPTION: 'customer-credits-display-balance-description',
  
  // Total earned card
  EARNED_CARD: 'customer-credits-display-earned-card',
  EARNED_HEADER: 'customer-credits-display-earned-header',
  EARNED_TITLE: 'customer-credits-display-earned-title',
  EARNED_ICON: 'customer-credits-display-earned-icon',
  EARNED_CONTENT: 'customer-credits-display-earned-content',
  EARNED_AMOUNT: 'customer-credits-display-earned-amount',
  EARNED_DESCRIPTION: 'customer-credits-display-earned-description',
  
  // Total spent card
  SPENT_CARD: 'customer-credits-display-spent-card',
  SPENT_HEADER: 'customer-credits-display-spent-header',
  SPENT_TITLE: 'customer-credits-display-spent-title',
  SPENT_ICON: 'customer-credits-display-spent-icon',
  SPENT_CONTENT: 'customer-credits-display-spent-content',
  SPENT_AMOUNT: 'customer-credits-display-spent-amount',
  SPENT_DESCRIPTION: 'customer-credits-display-spent-description',
  
  // Transactions section
  TRANSACTIONS_CARD: 'customer-credits-display-transactions-card',
  TRANSACTIONS_HEADER: 'customer-credits-display-transactions-header',
  TRANSACTIONS_TITLE: 'customer-credits-display-transactions-title',
  TRANSACTIONS_ICON: 'customer-credits-display-transactions-icon',
  TRANSACTIONS_DESCRIPTION: 'customer-credits-display-transactions-description',
  TRANSACTIONS_CONTENT: 'customer-credits-display-transactions-content',
  
  // Transaction list
  TRANSACTIONS_LIST: 'customer-credits-display-transactions-list',
  TRANSACTION_ITEM: (transactionId: string) => `customer-credits-display-transaction-item-${transactionId}`,
  TRANSACTION_ICON: (transactionId: string) => `customer-credits-display-transaction-icon-${transactionId}`,
  TRANSACTION_INFO: (transactionId: string) => `customer-credits-display-transaction-info-${transactionId}`,
  TRANSACTION_DESCRIPTION: (transactionId: string) => `customer-credits-display-transaction-description-${transactionId}`,
  TRANSACTION_DATE: (transactionId: string) => `customer-credits-display-transaction-date-${transactionId}`,
  TRANSACTION_AMOUNT: (transactionId: string) => `customer-credits-display-transaction-amount-${transactionId}`,
  TRANSACTION_TYPE_BADGE: (transactionId: string) => `customer-credits-display-transaction-type-badge-${transactionId}`,
  
  // Empty state
  EMPTY_TRANSACTIONS: 'customer-credits-display-empty-transactions',
  
  // Refresh button
  REFRESH_BUTTON_CONTAINER: 'customer-credits-display-refresh-button-container',
  REFRESH_BUTTON: 'customer-credits-display-refresh-button',
} as const;

// Dashboard Analytics Component Test IDs
// Real Usage Details Component Test IDs
export const REAL_USAGE_DETAILS_TEST_IDS = {
  // Main container
  CONTAINER: 'real-usage-details-container',
  
  // Layout sections
  USAGE_CARD_SECTION: 'real-usage-details-usage-card-section',
  ANALYTICS_SECTION: 'real-usage-details-analytics-section',
  
  // Analytics card
  ANALYTICS_CARD: 'real-usage-details-analytics-card',
  ANALYTICS_TITLE: 'real-usage-details-analytics-title',
  ANALYTICS_DESCRIPTION: 'real-usage-details-analytics-description',
  
  // Tabs
  TABS_CONTAINER: 'real-usage-details-tabs-container',
  TABS_LIST: 'real-usage-details-tabs-list',
  TAB_TRIGGER: (tab: string) => `real-usage-details-tab-trigger-${tab}`,
  TAB_CONTENT: (tab: string) => `real-usage-details-tab-content-${tab}`,
  
  // Period selector buttons
  PERIOD_SELECTOR_CONTAINER: 'real-usage-details-period-selector-container',
  PERIOD_BUTTON: (period: string) => `real-usage-details-period-button-${period}`,
  PERIOD_BUTTON_DAY: 'real-usage-details-period-button-day',
  PERIOD_BUTTON_WEEK: 'real-usage-details-period-button-week',
  PERIOD_BUTTON_MONTH: 'real-usage-details-period-button-month',
  
  // Loading states
  LOADING_CONTAINER: 'real-usage-details-loading-container',
  LOADING_SKELETON_MAIN: 'real-usage-details-loading-skeleton-main',
  LOADING_SKELETON_SECONDARY: 'real-usage-details-loading-skeleton-secondary',
  LOADING_SKELETON_TERTIARY: 'real-usage-details-loading-skeleton-tertiary',
  TABLE_LOADING_SKELETON: 'real-usage-details-table-loading-skeleton',
  
  // Summary tab content
  SUMMARY_CONTENT: 'real-usage-details-summary-content',
  API_KEYS_LIST: 'real-usage-details-api-keys-list',
  API_KEY_ITEM: (keyId: string) => `real-usage-details-api-key-item-${keyId}`,
  API_KEY_HEADER: (keyId: string) => `real-usage-details-api-key-header-${keyId}`,
  API_KEY_NAME: (keyId: string) => `real-usage-details-api-key-name-${keyId}`,
  API_KEY_CREDITS_BADGE: (keyId: string) => `real-usage-details-api-key-credits-badge-${keyId}`,
  
  // Endpoint details
  ENDPOINTS_LIST: (keyId: string) => `real-usage-details-endpoints-list-${keyId}`,
  ENDPOINT_ITEM: (keyId: string, endpointIndex: number) => `real-usage-details-endpoint-item-${keyId}-${endpointIndex}`,
  ENDPOINT_PATH: (keyId: string, endpointIndex: number) => `real-usage-details-endpoint-path-${keyId}-${endpointIndex}`,
  ENDPOINT_CREDITS: (keyId: string, endpointIndex: number) => `real-usage-details-endpoint-credits-${keyId}-${endpointIndex}`,
  ENDPOINT_PROGRESS: (keyId: string, endpointIndex: number) => `real-usage-details-endpoint-progress-${keyId}-${endpointIndex}`,
  
  // Details tab content
  DETAILS_CONTENT: 'real-usage-details-details-content',
  API_KEY_FILTER_CONTAINER: 'real-usage-details-api-key-filter-container',
  API_KEY_FILTER_LABEL: 'real-usage-details-api-key-filter-label',
  API_KEY_FILTER_SELECT: 'real-usage-details-api-key-filter-select',
  API_KEY_FILTER_TRIGGER: 'real-usage-details-api-key-filter-trigger',
  API_KEY_FILTER_CONTENT: 'real-usage-details-api-key-filter-content',
  API_KEY_FILTER_ITEM: (keyId: string) => `real-usage-details-api-key-filter-item-${keyId}`,
  
  // Usage table
  USAGE_TABLE_CONTAINER: 'real-usage-details-usage-table-container',
  USAGE_TABLE: 'real-usage-details-usage-table',
  USAGE_TABLE_HEADER: 'real-usage-details-usage-table-header',
  USAGE_TABLE_BODY: 'real-usage-details-usage-table-body',
  
  // Table headers
  TABLE_HEADER_TIMESTAMP: 'real-usage-details-table-header-timestamp',
  TABLE_HEADER_ENDPOINT: 'real-usage-details-table-header-endpoint',
  TABLE_HEADER_METHOD: 'real-usage-details-table-header-method',
  TABLE_HEADER_CREDITS: 'real-usage-details-table-header-credits',
  TABLE_HEADER_STATUS: 'real-usage-details-table-header-status',
  
  // Table rows and cells
  USAGE_TABLE_ROW: (usageId: string) => `real-usage-details-usage-table-row-${usageId}`,
  USAGE_CELL_TIMESTAMP: (usageId: string) => `real-usage-details-usage-cell-timestamp-${usageId}`,
  USAGE_CELL_ENDPOINT: (usageId: string) => `real-usage-details-usage-cell-endpoint-${usageId}`,
  USAGE_CELL_METHOD: (usageId: string) => `real-usage-details-usage-cell-method-${usageId}`,
  USAGE_CELL_CREDITS: (usageId: string) => `real-usage-details-usage-cell-credits-${usageId}`,
  USAGE_CELL_STATUS: (usageId: string) => `real-usage-details-usage-cell-status-${usageId}`,
  STATUS_BADGE: (usageId: string) => `real-usage-details-status-badge-${usageId}`,
  STATUS_BADGE_SUCCESS: (usageId: string) => `real-usage-details-status-badge-success-${usageId}`,
  STATUS_BADGE_FAILED: (usageId: string) => `real-usage-details-status-badge-failed-${usageId}`,
  
  // Pagination and summary
  RECORDS_SUMMARY: 'real-usage-details-records-summary',
  SHOWING_RECORDS_TEXT: 'real-usage-details-showing-records-text',
  
  // Empty states
  NO_USAGE_DATA_SUMMARY: 'real-usage-details-no-usage-data-summary',
  NO_USAGE_DATA_DETAILS: 'real-usage-details-no-usage-data-details',
  EMPTY_STATE_MESSAGE: 'real-usage-details-empty-state-message',
} as const;

// HTML5 QR Code Scanner Plugin Test IDs
export const HTML5_QR_CODE_PLUGIN_TEST_IDS = {
  // Main container
  CONTAINER: 'html5-qr-code-plugin-container',
  
  // Scanner region
  SCANNER_REGION: 'html5-qr-code-plugin-scanner-region',
  CAMERA_VIEW: 'html5-qr-code-plugin-camera-view',
  
  // Status indicators
  STATUS_CONTAINER: 'html5-qr-code-plugin-status-container',
  STATUS_MESSAGE: 'html5-qr-code-plugin-status-message',
  INSTRUCTIONS_TEXT: 'html5-qr-code-plugin-instructions-text',
  SCANNING_STATUS: 'html5-qr-code-plugin-scanning-status',
  SUPPORTED_FORMATS_TEXT: 'html5-qr-code-plugin-supported-formats-text',
  
  // Error states
  ERROR_CONTAINER: 'html5-qr-code-plugin-error-container',
  ERROR_ICON: 'html5-qr-code-plugin-error-icon',
  ERROR_TITLE: 'html5-qr-code-plugin-error-title',
  ERROR_MESSAGE: 'html5-qr-code-plugin-error-message',
  ERROR_DESCRIPTION: 'html5-qr-code-plugin-error-description',
  
  // Error action buttons
  TRY_AGAIN_BUTTON: 'html5-qr-code-plugin-try-again-button',
  RELOAD_BUTTON: 'html5-qr-code-plugin-reload-button',
  
  // Specific error types
  PERMISSION_ERROR: 'html5-qr-code-plugin-permission-error',
  CAMERA_NOT_FOUND_ERROR: 'html5-qr-code-plugin-camera-not-found-error',
  CAMERA_BUSY_ERROR: 'html5-qr-code-plugin-camera-busy-error',
  CAMERA_SETTINGS_ERROR: 'html5-qr-code-plugin-camera-settings-error',
  INITIALIZATION_ERROR: 'html5-qr-code-plugin-initialization-error',
  
  // Loading and initialization states
  LOADING_CONTAINER: 'html5-qr-code-plugin-loading-container',
  LOADING_MESSAGE: 'html5-qr-code-plugin-loading-message',
  INITIALIZING_CAMERA: 'html5-qr-code-plugin-initializing-camera',
  
  // Scanner controls (if added in future)
  CONTROLS_CONTAINER: 'html5-qr-code-plugin-controls-container',
  START_BUTTON: 'html5-qr-code-plugin-start-button',
  STOP_BUTTON: 'html5-qr-code-plugin-stop-button',
  SWITCH_CAMERA_BUTTON: 'html5-qr-code-plugin-switch-camera-button',
  
  // Scanner configuration indicators
  CONFIG_INDICATOR: 'html5-qr-code-plugin-config-indicator',
  FPS_INDICATOR: 'html5-qr-code-plugin-fps-indicator',
  CAMERA_MODE_INDICATOR: 'html5-qr-code-plugin-camera-mode-indicator',
  
  // Scan result indicators
  SCAN_SUCCESS_INDICATOR: 'html5-qr-code-plugin-scan-success-indicator',
  SCAN_ERROR_INDICATOR: 'html5-qr-code-plugin-scan-error-indicator',
  LAST_SCANNED_RESULT: 'html5-qr-code-plugin-last-scanned-result',
} as const;

export const DASHBOARD_ANALYTICS_TEST_IDS = {
  // Main container
  CONTAINER: 'dashboard-analytics-container',
  LOADING_OVERLAY: 'dashboard-analytics-loading-overlay',
  LOADING_SPINNER: 'dashboard-analytics-loading-spinner',
  PARTICLE_CANVAS: 'dashboard-analytics-particle-canvas',
  
  // Header
  HEADER: 'dashboard-analytics-header',
  LOGO_ICON: 'dashboard-analytics-logo-icon',
  LOGO_TEXT: 'dashboard-analytics-logo-text',
  SEARCH_CONTAINER: 'dashboard-analytics-search-container',
  SEARCH_ICON: 'dashboard-analytics-search-icon',
  SEARCH_INPUT: 'dashboard-analytics-search-input',
  
  // Header controls
  NOTIFICATIONS_BUTTON: 'dashboard-analytics-notifications-button',
  THEME_TOGGLE_BUTTON: 'dashboard-analytics-theme-toggle-button',
  THEME_ICON: 'dashboard-analytics-theme-icon',
  USER_AVATAR: 'dashboard-analytics-user-avatar',
  
  // Main grid
  MAIN_GRID: 'dashboard-analytics-main-grid',
  
  // Sidebar
  SIDEBAR: 'dashboard-analytics-sidebar',
  SIDEBAR_NAV: 'dashboard-analytics-sidebar-nav',
  NAV_ITEM: (item: string) => `dashboard-analytics-nav-item-${item}`,
  
  // System status section
  SYSTEM_STATUS_SECTION: 'dashboard-analytics-system-status-section',
  SYSTEM_STATUS_TITLE: 'dashboard-analytics-system-status-title',
  STATUS_ITEM: (type: string) => `dashboard-analytics-status-item-${type}`,
  
  // Main dashboard area
  MAIN_DASHBOARD: 'dashboard-analytics-main-dashboard',
  
  // System overview card
  SYSTEM_OVERVIEW_CARD: 'dashboard-analytics-system-overview-card',
  SYSTEM_OVERVIEW_HEADER: 'dashboard-analytics-system-overview-header',
  SYSTEM_OVERVIEW_TITLE: 'dashboard-analytics-system-overview-title',
  LIVE_BADGE: 'dashboard-analytics-live-badge',
  REFRESH_BUTTON: 'dashboard-analytics-refresh-button',
  
  // Metric cards
  METRICS_GRID: 'dashboard-analytics-metrics-grid',
  METRIC_CARD: (type: string) => `dashboard-analytics-metric-card-${type}`,
  METRIC_TITLE: (type: string) => `dashboard-analytics-metric-title-${type}`,
  METRIC_VALUE: (type: string) => `dashboard-analytics-metric-value-${type}`,
  METRIC_ICON: (type: string) => `dashboard-analytics-metric-icon-${type}`,
  METRIC_DETAIL: (type: string) => `dashboard-analytics-metric-detail-${type}`,
  METRIC_TREND: (type: string) => `dashboard-analytics-metric-trend-${type}`,
  
  // Tabs section
  TABS_CONTAINER: 'dashboard-analytics-tabs-container',
  TABS_LIST: 'dashboard-analytics-tabs-list',
  TAB_PERFORMANCE: 'dashboard-analytics-tab-performance',
  TAB_PROCESSES: 'dashboard-analytics-tab-processes',
  TAB_STORAGE: 'dashboard-analytics-tab-storage',
  TABS_LEGEND: 'dashboard-analytics-tabs-legend',
  
  // Performance tab
  PERFORMANCE_CHART: 'dashboard-analytics-performance-chart',
  PERFORMANCE_OVERLAY: 'dashboard-analytics-performance-overlay',
  SYSTEM_LOAD_VALUE: 'dashboard-analytics-system-load-value',
  
  // Processes tab
  PROCESSES_CONTAINER: 'dashboard-analytics-processes-container',
  PROCESSES_HEADER: 'dashboard-analytics-processes-header',
  PROCESS_ROW: (pid: string) => `dashboard-analytics-process-row-${pid}`,
  
  // Storage tab
  STORAGE_CONTAINER: 'dashboard-analytics-storage-container',
  STORAGE_GRID: 'dashboard-analytics-storage-grid',
  STORAGE_ITEM: (drive: string) => `dashboard-analytics-storage-item-${drive}`,
  
  // Security status card
  SECURITY_CARD: 'dashboard-analytics-security-card',
  SECURITY_HEADER: 'dashboard-analytics-security-header',
  SECURITY_TITLE: 'dashboard-analytics-security-title',
  SECURITY_ITEM: (item: string) => `dashboard-analytics-security-item-${item}`,
  SECURITY_LEVEL_PROGRESS: 'dashboard-analytics-security-level-progress',
  
  // System alerts card
  ALERTS_CARD: 'dashboard-analytics-alerts-card',
  ALERTS_HEADER: 'dashboard-analytics-alerts-header',
  ALERTS_TITLE: 'dashboard-analytics-alerts-title',
  ALERT_ITEM: (index: number) => `dashboard-analytics-alert-item-${index}`,
  
  // Communications card
  COMMUNICATIONS_CARD: 'dashboard-analytics-communications-card',
  COMMUNICATIONS_HEADER: 'dashboard-analytics-communications-header',
  COMMUNICATIONS_TITLE: 'dashboard-analytics-communications-title',
  COMMUNICATIONS_BADGE: 'dashboard-analytics-communications-badge',
  COMMUNICATION_ITEM: (index: number) => `dashboard-analytics-communication-item-${index}`,
  COMMUNICATIONS_FOOTER: 'dashboard-analytics-communications-footer',
  MESSAGE_INPUT: 'dashboard-analytics-message-input',
  MIC_BUTTON: 'dashboard-analytics-mic-button',
  SEND_BUTTON: 'dashboard-analytics-send-button',
  
  // Right sidebar
  RIGHT_SIDEBAR: 'dashboard-analytics-right-sidebar',
  
  // System time card
  SYSTEM_TIME_CARD: 'dashboard-analytics-system-time-card',
  SYSTEM_TIME_DISPLAY: 'dashboard-analytics-system-time-display',
  SYSTEM_DATE_DISPLAY: 'dashboard-analytics-system-date-display',
  UPTIME_DISPLAY: 'dashboard-analytics-uptime-display',
  TIMEZONE_DISPLAY: 'dashboard-analytics-timezone-display',
  
  // Quick actions card
  QUICK_ACTIONS_CARD: 'dashboard-analytics-quick-actions-card',
  QUICK_ACTIONS_HEADER: 'dashboard-analytics-quick-actions-header',
  QUICK_ACTIONS_GRID: 'dashboard-analytics-quick-actions-grid',
  ACTION_BUTTON: (action: string) => `dashboard-analytics-action-button-${action}`,
  
  // Resource allocation card
  RESOURCE_ALLOCATION_CARD: 'dashboard-analytics-resource-allocation-card',
  RESOURCE_ALLOCATION_HEADER: 'dashboard-analytics-resource-allocation-header',
  RESOURCE_ITEM: (resource: string) => `dashboard-analytics-resource-item-${resource}`,
  PRIORITY_SLIDER: 'dashboard-analytics-priority-slider',
  
  // Environment controls card
  ENVIRONMENT_CONTROLS_CARD: 'dashboard-analytics-environment-controls-card',
  ENVIRONMENT_CONTROLS_HEADER: 'dashboard-analytics-environment-controls-header',
  CONTROL_SWITCH: (control: string) => `dashboard-analytics-control-switch-${control}`,
} as const;

export const CUSTOMER_QR_SCANNER_DIALOG_TEST_IDS = {
  CONTAINER: 'customer-qr-scanner-dialog-container',
  HEADER: 'customer-qr-scanner-dialog-header',
  TITLE: 'customer-qr-scanner-dialog-title',
  DESCRIPTION: 'customer-qr-scanner-dialog-description',
  CONTENT: 'customer-qr-scanner-dialog-content',
  LOADING_CONTAINER: 'customer-qr-scanner-dialog-loading-container',
  LOADING_ICON: 'customer-qr-scanner-dialog-loading-icon',
  LOADING_MESSAGE: 'customer-qr-scanner-dialog-loading-message',
  SCANNER_CONTAINER: 'customer-qr-scanner-dialog-scanner-container',
  CAMERA_ICON: 'customer-qr-scanner-dialog-camera-icon',
  CAMERA_MESSAGE: 'customer-qr-scanner-dialog-camera-message',
  START_SCANNER_BUTTON: 'customer-qr-scanner-dialog-start-scanner-button',
  SUCCESS_ALERT: 'customer-qr-scanner-dialog-success-alert',
  ERROR_ALERT: 'customer-qr-scanner-dialog-error-alert',
  PROCESSING_ALERT: 'customer-qr-scanner-dialog-processing-alert',
  FOOTER: 'customer-qr-scanner-dialog-footer',
  SCAN_AGAIN_BUTTON: 'customer-qr-scanner-dialog-scan-again-button',
  CONTINUE_BUTTON: 'customer-qr-scanner-dialog-continue-button',
  CANCEL_BUTTON: 'customer-qr-scanner-dialog-cancel-button',
} as const;

export const CUSTOMER_REDEEM_TEST_IDS = {
  CONTAINER: 'customer-redeem-container',
  MAIN_CONTENT: 'customer-redeem-main-content',
  BREADCRUMBS: 'customer-redeem-breadcrumbs',
  HEADER: 'customer-redeem-header',
  FORM_SECTION: 'customer-redeem-form-section',
  REDEEM_CARD: 'customer-redeem-card',
  CARD_HEADER: 'customer-redeem-card-header',
  CARD_TITLE: 'customer-redeem-card-title',
  CARD_DESCRIPTION: 'customer-redeem-card-description',
  CARD_CONTENT: 'customer-redeem-card-content',
  CARD_FOOTER: 'customer-redeem-card-footer',
  
  // Form Fields
  FORM_FIELDS: 'customer-redeem-form-fields',
  CODE_INPUT_GROUP: 'customer-redeem-code-input-group',
  CODE_LABEL: 'customer-redeem-code-label',
  INPUT_WITH_SCAN: 'customer-redeem-input-with-scan',
  CODE_INPUT: 'customer-redeem-code-input',
  SCAN_BUTTON: 'customer-redeem-scan-button',
  SCAN_ICON: 'customer-redeem-scan-icon',
  
  // Scanning State
  SCANNING_STATE: 'customer-redeem-scanning-state',
  SCANNING_ICON: 'customer-redeem-scanning-icon',
  SCANNING_MESSAGE: 'customer-redeem-scanning-message',
  CANCEL_SCANNING_BUTTON: 'customer-redeem-cancel-scanning-button',
  
  // Result States
  SUCCESS_RESULT: 'customer-redeem-success-result',
  ERROR_RESULT: 'customer-redeem-error-result',
  RESULT_STATUS: 'customer-redeem-result-status',
  RESULT_MESSAGE: 'customer-redeem-result-message',
  SUCCESS_DETAILS: 'customer-redeem-success-details',
  BALANCE_INFO: 'customer-redeem-balance-info',
  
  // Actions
  REDEEM_BUTTON: 'customer-redeem-redeem-button',
  
  // Footer Navigation
  FOOTER: 'customer-redeem-footer',
  NAVIGATION_BAR: 'customer-redeem-navigation-bar',
  BACK_LINK: 'customer-redeem-back-link',
  BACK_ICON_CONTAINER: 'customer-redeem-back-icon-container',
  BACK_ICON: 'customer-redeem-back-icon',
  BACK_LABEL: 'customer-redeem-back-label',
  REDEEM_TAB_ACTIVE: 'customer-redeem-redeem-tab-active',
  REDEEM_ICON_CONTAINER_ACTIVE: 'customer-redeem-redeem-icon-container-active',
  REDEEM_ICON_ACTIVE: 'customer-redeem-redeem-icon-active',
  REDEEM_LABEL_ACTIVE: 'customer-redeem-redeem-label-active',
} as const;

// Static Pages Test IDs
export const TERMS_TEST_IDS = {
  CONTAINER: 'terms-container',
  MAIN_CONTENT: 'terms-main-content',
  HEADER_SECTION: 'terms-header-section',
  TITLE: 'terms-title',
  LAST_UPDATED: 'terms-last-updated',
  CONTENT_SECTIONS: 'terms-content-sections',
  AGREEMENT_SECTION: 'terms-agreement-section',
  AGREEMENT_HEADER: 'terms-agreement-header',
  AGREEMENT_TITLE: 'terms-agreement-title',
  AGREEMENT_CONTENT: 'terms-agreement-content',
} as const;

export const PRIVACY_TEST_IDS = {
  CONTAINER: 'privacy-container',
  MAIN_CONTENT: 'privacy-main-content',
  HEADER_SECTION: 'privacy-header-section',
  TITLE: 'privacy-title',
  LAST_UPDATED: 'privacy-last-updated',
  CONTENT_SECTIONS: 'privacy-content-sections',
  
  // Introduction Section
  INTRODUCTION_SECTION: 'privacy-introduction-section',
  INTRODUCTION_HEADER: 'privacy-introduction-header',
  INTRODUCTION_TITLE: 'privacy-introduction-title',
  INTRODUCTION_CONTENT: 'privacy-introduction-content',
  
  // Information We Collect Section
  INFORMATION_COLLECT_SECTION: 'privacy-information-collect-section',
  INFORMATION_COLLECT_HEADER: 'privacy-information-collect-header',
  INFORMATION_COLLECT_TITLE: 'privacy-information-collect-title',
  INFORMATION_COLLECT_CONTENT: 'privacy-information-collect-content',
  PERSONAL_INFO_SUBSECTION: 'privacy-personal-info-subsection',
  PERSONAL_INFO_TITLE: 'privacy-personal-info-title',
  PERSONAL_INFO_DESCRIPTION: 'privacy-personal-info-description',
  PERSONAL_INFO_LIST: 'privacy-personal-info-list',
  AUTO_COLLECT_SUBSECTION: 'privacy-auto-collect-subsection',
  AUTO_COLLECT_TITLE: 'privacy-auto-collect-title',
  AUTO_COLLECT_DESCRIPTION: 'privacy-auto-collect-description',
  AUTO_COLLECT_LIST: 'privacy-auto-collect-list',
  
  // How We Use Information Section
  USE_INFO_SECTION: 'privacy-use-info-section',
  USE_INFO_HEADER: 'privacy-use-info-header',
  USE_INFO_TITLE: 'privacy-use-info-title',
  USE_INFO_CONTENT: 'privacy-use-info-content',
  USE_INFO_DESCRIPTION: 'privacy-use-info-description',
  USE_INFO_LIST: 'privacy-use-info-list',
  
  // Information Sharing Section
  SHARING_SECTION: 'privacy-sharing-section',
  SHARING_HEADER: 'privacy-sharing-header',
  SHARING_TITLE: 'privacy-sharing-title',
  SHARING_CONTENT: 'privacy-sharing-content',
  SHARING_DESCRIPTION: 'privacy-sharing-description',
  SERVICE_PROVIDERS_SUBSECTION: 'privacy-service-providers-subsection',
  SERVICE_PROVIDERS_TITLE: 'privacy-service-providers-title',
  SERVICE_PROVIDERS_DESCRIPTION: 'privacy-service-providers-description',
  LEGAL_REQUIREMENTS_SUBSECTION: 'privacy-legal-requirements-subsection',
  LEGAL_REQUIREMENTS_TITLE: 'privacy-legal-requirements-title',
  LEGAL_REQUIREMENTS_DESCRIPTION: 'privacy-legal-requirements-description',
  BUSINESS_TRANSFERS_SUBSECTION: 'privacy-business-transfers-subsection',
  BUSINESS_TRANSFERS_TITLE: 'privacy-business-transfers-title',
  BUSINESS_TRANSFERS_DESCRIPTION: 'privacy-business-transfers-description',
  
  // Data Security Section
  SECURITY_SECTION: 'privacy-security-section',
  SECURITY_HEADER: 'privacy-security-header',
  SECURITY_TITLE: 'privacy-security-title',
  SECURITY_CONTENT: 'privacy-security-content',
  SECURITY_DESCRIPTION: 'privacy-security-description',
  SECURITY_LIST: 'privacy-security-list',
  SECURITY_DISCLAIMER: 'privacy-security-disclaimer',
  
  // Data Retention Section
  RETENTION_SECTION: 'privacy-retention-section',
  RETENTION_HEADER: 'privacy-retention-header',
  RETENTION_TITLE: 'privacy-retention-title',
  RETENTION_CONTENT: 'privacy-retention-content',
  RETENTION_DESCRIPTION: 'privacy-retention-description',
  
  // Your Rights Section
  RIGHTS_SECTION: 'privacy-rights-section',
  RIGHTS_HEADER: 'privacy-rights-header',
  RIGHTS_TITLE: 'privacy-rights-title',
  RIGHTS_CONTENT: 'privacy-rights-content',
  RIGHTS_DESCRIPTION: 'privacy-rights-description',
  RIGHTS_LIST: 'privacy-rights-list',
  RIGHTS_CONTACT: 'privacy-rights-contact',
  
  // Cookies Section
  COOKIES_SECTION: 'privacy-cookies-section',
  COOKIES_HEADER: 'privacy-cookies-header',
  COOKIES_TITLE: 'privacy-cookies-title',
  COOKIES_CONTENT: 'privacy-cookies-content',
  COOKIES_DESCRIPTION: 'privacy-cookies-description',
  COOKIES_LIST: 'privacy-cookies-list',
  COOKIES_CONTROL: 'privacy-cookies-control',
  
  // Third Party Section
  THIRD_PARTY_SECTION: 'privacy-third-party-section',
  THIRD_PARTY_HEADER: 'privacy-third-party-header',
  THIRD_PARTY_TITLE: 'privacy-third-party-title',
  THIRD_PARTY_CONTENT: 'privacy-third-party-content',
  THIRD_PARTY_DESCRIPTION: 'privacy-third-party-description',
  
  // Children's Privacy Section
  CHILDREN_SECTION: 'privacy-children-section',
  CHILDREN_HEADER: 'privacy-children-header',
  CHILDREN_TITLE: 'privacy-children-title',
  CHILDREN_CONTENT: 'privacy-children-content',
  CHILDREN_DESCRIPTION: 'privacy-children-description',
  
  // International Transfers Section
  INTERNATIONAL_SECTION: 'privacy-international-section',
  INTERNATIONAL_HEADER: 'privacy-international-header',
  INTERNATIONAL_TITLE: 'privacy-international-title',
  INTERNATIONAL_CONTENT: 'privacy-international-content',
  INTERNATIONAL_DESCRIPTION: 'privacy-international-description',
  
  // Changes Section
  CHANGES_SECTION: 'privacy-changes-section',
  CHANGES_HEADER: 'privacy-changes-header',
  CHANGES_TITLE: 'privacy-changes-title',
  CHANGES_CONTENT: 'privacy-changes-content',
  CHANGES_DESCRIPTION: 'privacy-changes-description',
  
  // Contact Section
  CONTACT_SECTION: 'privacy-contact-section',
  CONTACT_HEADER: 'privacy-contact-header',
  CONTACT_TITLE: 'privacy-contact-title',
  CONTACT_CONTENT: 'privacy-contact-content',
  CONTACT_DESCRIPTION: 'privacy-contact-description',
  CONTACT_INFO: 'privacy-contact-info',
} as const;

export const SECURITY_TEST_IDS = {
  CONTAINER: 'security-container',
  MAIN_CONTENT: 'security-main-content',
  HEADER_SECTION: 'security-header-section',
  TITLE: 'security-title',
  DESCRIPTION: 'security-description',
  CONTENT_SECTIONS: 'security-content-sections',
  
  // Security Overview Section
  OVERVIEW_SECTION: 'security-overview-section',
  OVERVIEW_HEADER: 'security-overview-header',
  OVERVIEW_TITLE: 'security-overview-title',
  OVERVIEW_CONTENT: 'security-overview-content',
  
  // Data Protection Section
  DATA_PROTECTION_SECTION: 'security-data-protection-section',
  DATA_PROTECTION_HEADER: 'security-data-protection-header',
  DATA_PROTECTION_TITLE: 'security-data-protection-title',
  DATA_PROTECTION_CONTENT: 'security-data-protection-content',
  ENCRYPTION_SUBSECTION: 'security-encryption-subsection',
  ENCRYPTION_TITLE: 'security-encryption-title',
  ENCRYPTION_DESCRIPTION: 'security-encryption-description',
  ENCRYPTION_LIST: 'security-encryption-list',
  ANONYMIZATION_SUBSECTION: 'security-anonymization-subsection',
  ANONYMIZATION_TITLE: 'security-anonymization-title',
  ANONYMIZATION_DESCRIPTION: 'security-anonymization-description',
  
  // Authentication Section
  AUTH_SECTION: 'security-auth-section',
  AUTH_HEADER: 'security-auth-header',
  AUTH_TITLE: 'security-auth-title',
  AUTH_CONTENT: 'security-auth-content',
  MFA_SUBSECTION: 'security-mfa-subsection',
  MFA_TITLE: 'security-mfa-title',
  MFA_DESCRIPTION: 'security-mfa-description',
  MFA_LIST: 'security-mfa-list',
  RBAC_SUBSECTION: 'security-rbac-subsection',
  RBAC_TITLE: 'security-rbac-title',
  RBAC_DESCRIPTION: 'security-rbac-description',
  RBAC_LIST: 'security-rbac-list',
  API_SECURITY_SUBSECTION: 'security-api-security-subsection',
  API_SECURITY_TITLE: 'security-api-security-title',
  API_SECURITY_DESCRIPTION: 'security-api-security-description',
  API_SECURITY_LIST: 'security-api-security-list',
  
  // Infrastructure Section
  INFRASTRUCTURE_SECTION: 'security-infrastructure-section',
  INFRASTRUCTURE_HEADER: 'security-infrastructure-header',
  INFRASTRUCTURE_TITLE: 'security-infrastructure-title',
  INFRASTRUCTURE_CONTENT: 'security-infrastructure-content',
  CLOUD_SECURITY_SUBSECTION: 'security-cloud-security-subsection',
  CLOUD_SECURITY_TITLE: 'security-cloud-security-title',
  CLOUD_SECURITY_DESCRIPTION: 'security-cloud-security-description',
  CLOUD_SECURITY_LIST: 'security-cloud-security-list',
  CONTAINER_SECURITY_SUBSECTION: 'security-container-security-subsection',
  CONTAINER_SECURITY_TITLE: 'security-container-security-title',
  CONTAINER_SECURITY_DESCRIPTION: 'security-container-security-description',
  CONTAINER_SECURITY_LIST: 'security-container-security-list',
  DATABASE_SECURITY_SUBSECTION: 'security-database-security-subsection',
  DATABASE_SECURITY_TITLE: 'security-database-security-title',
  DATABASE_SECURITY_DESCRIPTION: 'security-database-security-description',
  DATABASE_SECURITY_LIST: 'security-database-security-list',
  
  // Monitoring Section
  MONITORING_SECTION: 'security-monitoring-section',
  MONITORING_HEADER: 'security-monitoring-header',
  MONITORING_TITLE: 'security-monitoring-title',
  MONITORING_CONTENT: 'security-monitoring-content',
  SECURITY_MONITORING_SUBSECTION: 'security-monitoring-subsection',
  SECURITY_MONITORING_TITLE: 'security-monitoring-subsection-title',
  SECURITY_MONITORING_DESCRIPTION: 'security-monitoring-subsection-description',
  SECURITY_MONITORING_LIST: 'security-monitoring-subsection-list',
  COMPLIANCE_SUBSECTION: 'security-compliance-subsection',
  COMPLIANCE_TITLE: 'security-compliance-title',
  COMPLIANCE_DESCRIPTION: 'security-compliance-description',
  COMPLIANCE_LIST: 'security-compliance-list',
  AUDIT_SUBSECTION: 'security-audit-subsection',
  AUDIT_TITLE: 'security-audit-title',
  AUDIT_DESCRIPTION: 'security-audit-description',
  AUDIT_LIST: 'security-audit-list',
  
  // Team Security Section
  TEAM_SECTION: 'security-team-section',
  TEAM_HEADER: 'security-team-header',
  TEAM_TITLE: 'security-team-title',
  TEAM_CONTENT: 'security-team-content',
  TEAM_DESCRIPTION: 'security-team-description',
  TEAM_LIST: 'security-team-list',
  
  // Best Practices Section
  BEST_PRACTICES_SECTION: 'security-best-practices-section',
  BEST_PRACTICES_HEADER: 'security-best-practices-header',
  BEST_PRACTICES_TITLE: 'security-best-practices-title',
  BEST_PRACTICES_CONTENT: 'security-best-practices-content',
  BEST_PRACTICES_DESCRIPTION: 'security-best-practices-description',
  ACCOUNT_SECURITY_SUBSECTION: 'security-account-security-subsection',
  ACCOUNT_SECURITY_TITLE: 'security-account-security-title',
  ACCOUNT_SECURITY_LIST: 'security-account-security-list',
  API_BEST_PRACTICES_SUBSECTION: 'security-api-best-practices-subsection',
  API_BEST_PRACTICES_TITLE: 'security-api-best-practices-title',
  API_BEST_PRACTICES_LIST: 'security-api-best-practices-list',
  REPORTING_SUBSECTION: 'security-reporting-subsection',
  REPORTING_TITLE: 'security-reporting-title',
  REPORTING_DESCRIPTION: 'security-reporting-description',
  
  // Contact Section
  CONTACT_SECTION: 'security-contact-section',
  CONTACT_HEADER: 'security-contact-header',
  CONTACT_TITLE: 'security-contact-title',
  CONTACT_CONTENT: 'security-contact-content',
  CONTACT_DESCRIPTION: 'security-contact-description',
  CONTACT_INFO: 'security-contact-info',
} as const;

export const PRICING_TEST_IDS = {
  CONTAINER: 'pricing-container',
  NAVBAR: 'pricing-navbar',
  MAIN_CONTENT: 'pricing-main-content',
  PRICING_SECTION: 'pricing-section',
  CONTAINER_INNER: 'pricing-container-inner',
  HEADER_SECTION: 'pricing-header-section',
  TITLE_SECTION: 'pricing-title-section',
  TITLE: 'pricing-title',
  DESCRIPTION: 'pricing-description',
  
  // Plans Grid
  PLANS_GRID: 'pricing-plans-grid',
  
  // Free Plan
  FREE_PLAN_CARD: 'pricing-free-plan-card',
  FREE_PLAN_CONTENT: 'pricing-free-plan-content',
  FREE_PLAN_TITLE: 'pricing-free-plan-title',
  FREE_PLAN_PRICE: 'pricing-free-plan-price',
  FREE_PLAN_CTA: 'pricing-free-plan-cta',
  FREE_PLAN_BUTTON: 'pricing-free-plan-button',
  
  // Pro Plan
  PRO_PLAN_CARD: 'pricing-pro-plan-card',
  PRO_PLAN_BADGE: 'pricing-pro-plan-badge',
  PRO_PLAN_CONTENT: 'pricing-pro-plan-content',
  PRO_PLAN_TITLE: 'pricing-pro-plan-title',
  PRO_PLAN_PRICE: 'pricing-pro-plan-price',
  PRO_PLAN_CTA: 'pricing-pro-plan-cta',
  PRO_PLAN_BUTTON: 'pricing-pro-plan-button',
  
  // Enterprise Plan
  ENTERPRISE_PLAN_CARD: 'pricing-enterprise-plan-card',
  ENTERPRISE_PLAN_CONTENT: 'pricing-enterprise-plan-content',
  ENTERPRISE_PLAN_TITLE: 'pricing-enterprise-plan-title',
  ENTERPRISE_PLAN_PRICE: 'pricing-enterprise-plan-price',
  ENTERPRISE_PLAN_CTA: 'pricing-enterprise-plan-cta',
  ENTERPRISE_PLAN_BUTTON: 'pricing-enterprise-plan-button',
} as const;

// Landing Components Test IDs
export const HERO_SECTION_TEST_IDS = {
  CONTAINER: 'hero-section-container',
  WRAPPER: 'hero-section-wrapper',
  HERO_CARD: 'hero-section-hero-card',
  BACKGROUND_OVERLAY: 'hero-section-background-overlay',
  CONTENT_SECTION: 'hero-section-content-section',
  TITLE: 'hero-section-title',
  DESCRIPTION: 'hero-section-description',
  CTA_BUTTONS: 'hero-section-cta-buttons',
  GET_STARTED_BUTTON: 'hero-section-get-started-button',
  GET_STARTED_TEXT: 'hero-section-get-started-text',
  LEARN_MORE_BUTTON: 'hero-section-learn-more-button',
  LEARN_MORE_TEXT: 'hero-section-learn-more-text',
} as const;

export const FEATURES_SECTION_TEST_IDS = {
  CONTAINER: 'features-section-container',
  HEADER_SECTION: 'features-section-header-section',
  TITLE: 'features-section-title',
  DESCRIPTION: 'features-section-description',
  FEATURES_GRID: 'features-section-features-grid',
  FEATURE_CARD: (index: number) => `features-section-feature-card-${index}`,
  FEATURE_ICON: (index: number) => `features-section-feature-icon-${index}`,
  FEATURE_CONTENT: (index: number) => `features-section-feature-content-${index}`,
  FEATURE_TITLE: (index: number) => `features-section-feature-title-${index}`,
  FEATURE_DESCRIPTION: (index: number) => `features-section-feature-description-${index}`,
} as const;

export const CTA_SECTION_TEST_IDS = {
  CONTAINER: 'cta-section-container',
  BACKGROUND_OVERLAY: 'cta-section-background-overlay',
  CONTENT_SECTION: 'cta-section-content-section',
  TITLE: 'cta-section-title',
  DESCRIPTION: 'cta-section-description',
  CTA_CONTAINER: 'cta-section-cta-container',
  CTA_LINK: 'cta-section-cta-link',
  CTA_BUTTON: 'cta-section-cta-button',
  CTA_TEXT: 'cta-section-cta-text',
} as const;

// Error Handling & Edge Cases Test IDs (Phase 10)
export const ERROR_HANDLING_TEST_IDS = {
  // Auth Error Boundary
  AUTH_ERROR_BOUNDARY: {
    CONTAINER: 'auth-error-boundary-container',
    ERROR_MESSAGE: 'auth-error-boundary-error-message',
    SIGNIN_REDIRECT: 'auth-error-boundary-signin-redirect',
    FALLBACK_MESSAGE: 'auth-error-boundary-fallback-message',
  },
  
  // Network Error States
  NETWORK_ERROR: {
    CONTAINER: 'network-error-container',
    TITLE: 'network-error-title',
    MESSAGE: 'network-error-message',
    RETRY_BUTTON: 'network-error-retry-button',
    OFFLINE_INDICATOR: 'network-error-offline-indicator',
  },
  
  // Validation Errors
  VALIDATION_ERROR: {
    FORM_CONTAINER: 'validation-error-form-container',
    FIELD_ERROR: (fieldName: string) => `validation-error-field-${fieldName}`,
    SUMMARY: 'validation-error-summary',
    SUMMARY_LIST: 'validation-error-summary-list',
    SUMMARY_ITEM: (index: number) => `validation-error-summary-item-${index}`,
  },
  
  // API Error States
  API_ERROR: {
    CONTAINER: 'api-error-container',
    TITLE: 'api-error-title',
    MESSAGE: 'api-error-message',
    STATUS_CODE: 'api-error-status-code',
    RETRY_BUTTON: 'api-error-retry-button',
    CONTACT_SUPPORT: 'api-error-contact-support',
  },
  
  // Loading Timeout
  LOADING_TIMEOUT: {
    CONTAINER: 'loading-timeout-container',
    MESSAGE: 'loading-timeout-message',
    RETRY_BUTTON: 'loading-timeout-retry-button',
    CANCEL_BUTTON: 'loading-timeout-cancel-button',
  },
  
  // Permission Errors
  PERMISSION_ERROR: {
    CONTAINER: 'permission-error-container',
    TITLE: 'permission-error-title',
    MESSAGE: 'permission-error-message',
    LOGIN_BUTTON: 'permission-error-login-button',
    REQUEST_ACCESS_BUTTON: 'permission-error-request-access-button',
  },
  
  // Server Error States
  SERVER_ERROR: {
    CONTAINER: 'server-error-container',
    TITLE: 'server-error-title',
    MESSAGE: 'server-error-message',
    ERROR_CODE: 'server-error-code',
    REPORT_BUTTON: 'server-error-report-button',
    HOME_BUTTON: 'server-error-home-button',
  },
  
  // Rate Limit Error
  RATE_LIMIT_ERROR: {
    CONTAINER: 'rate-limit-error-container',
    TITLE: 'rate-limit-error-title',
    MESSAGE: 'rate-limit-error-message',
    RETRY_AFTER: 'rate-limit-error-retry-after',
    UPGRADE_BUTTON: 'rate-limit-error-upgrade-button',
  },
  
  // Empty States with Actions
  EMPTY_STATES: {
    NO_DATA_CONTAINER: 'empty-state-no-data-container',
    NO_DATA_ICON: 'empty-state-no-data-icon',
    NO_DATA_TITLE: 'empty-state-no-data-title',
    NO_DATA_MESSAGE: 'empty-state-no-data-message',
    NO_DATA_ACTION: 'empty-state-no-data-action',
    
    NO_RESULTS_CONTAINER: 'empty-state-no-results-container',
    NO_RESULTS_TITLE: 'empty-state-no-results-title',
    NO_RESULTS_MESSAGE: 'empty-state-no-results-message',
    CLEAR_FILTERS_BUTTON: 'empty-state-clear-filters-button',
  },
  
  // Edge Case Scenarios
  EDGE_CASES: {
    // Slow Connection
    SLOW_CONNECTION_WARNING: 'edge-case-slow-connection-warning',
    SLOW_CONNECTION_MESSAGE: 'edge-case-slow-connection-message',
    
    // Browser Compatibility
    BROWSER_WARNING: 'edge-case-browser-warning',
    BROWSER_UPDATE_LINK: 'edge-case-browser-update-link',
    
    // JavaScript Disabled
    NO_JS_MESSAGE: 'edge-case-no-js-message',
    NO_JS_FALLBACK: 'edge-case-no-js-fallback',
    
    // Mobile Orientation
    ORIENTATION_WARNING: 'edge-case-orientation-warning',
    ROTATE_DEVICE_MESSAGE: 'edge-case-rotate-device-message',
    
    // Maintenance Mode
    MAINTENANCE_CONTAINER: 'edge-case-maintenance-container',
    MAINTENANCE_TITLE: 'edge-case-maintenance-title',
    MAINTENANCE_MESSAGE: 'edge-case-maintenance-message',
    MAINTENANCE_ETA: 'edge-case-maintenance-eta',
  },
} as const;

// ==========================================
// UI Component Test IDs (shadcn/ui components)
// ==========================================

// Button Component Test IDs
export const BUTTON_TEST_IDS = {
  // Button variants
  DEFAULT: 'ui-button-default',
  PRIMARY: 'ui-button-primary',
  SECONDARY: 'ui-button-secondary',
  DESTRUCTIVE: 'ui-button-destructive',
  OUTLINE: 'ui-button-outline',
  GHOST: 'ui-button-ghost',
  LINK: 'ui-button-link',
  
  // Button sizes
  SIZE_SM: 'ui-button-size-sm',
  SIZE_DEFAULT: 'ui-button-size-default',
  SIZE_LG: 'ui-button-size-lg',
  SIZE_ICON: 'ui-button-size-icon',
  
  // Button states
  DISABLED: 'ui-button-disabled',
  LOADING: 'ui-button-loading',
  LOADING_SPINNER: 'ui-button-loading-spinner',
} as const;

// Input Component Test IDs
export const INPUT_TEST_IDS = {
  // Input types
  TEXT: 'ui-input-text',
  EMAIL: 'ui-input-email',
  PASSWORD: 'ui-input-password',
  NUMBER: 'ui-input-number',
  SEARCH: 'ui-input-search',
  URL: 'ui-input-url',
  TEL: 'ui-input-tel',
  DATE: 'ui-input-date',
  TIME: 'ui-input-time',
  FILE: 'ui-input-file',
  
  // Input states
  DISABLED: 'ui-input-disabled',
  READONLY: 'ui-input-readonly',
  INVALID: 'ui-input-invalid',
  REQUIRED: 'ui-input-required',
  
  // Input features
  PLACEHOLDER: 'ui-input-placeholder',
  ERROR_MESSAGE: 'ui-input-error-message',
  HELP_TEXT: 'ui-input-help-text',
} as const;

// Dialog Component Test IDs
export const DIALOG_TEST_IDS = {
  // Dialog elements
  ROOT: 'ui-dialog-root',
  TRIGGER: 'ui-dialog-trigger',
  PORTAL: 'ui-dialog-portal',
  OVERLAY: 'ui-dialog-overlay',
  CONTENT: 'ui-dialog-content',
  CLOSE: 'ui-dialog-close',
  CLOSE_BUTTON: 'ui-dialog-close-button',
  
  // Dialog sections
  HEADER: 'ui-dialog-header',
  TITLE: 'ui-dialog-title',
  DESCRIPTION: 'ui-dialog-description',
  BODY: 'ui-dialog-body',
  FOOTER: 'ui-dialog-footer',
  
  // Dialog actions
  CANCEL_BUTTON: 'ui-dialog-cancel-button',
  CONFIRM_BUTTON: 'ui-dialog-confirm-button',
  SUBMIT_BUTTON: 'ui-dialog-submit-button',
} as const;

// Form Component Test IDs
export const FORM_TEST_IDS = {
  // Form elements
  ROOT: 'ui-form-root',
  FIELD: 'ui-form-field',
  ITEM: 'ui-form-item',
  LABEL: 'ui-form-label',
  CONTROL: 'ui-form-control',
  
  // Form feedback
  DESCRIPTION: 'ui-form-description',
  MESSAGE: 'ui-form-message',
  ERROR_MESSAGE: 'ui-form-error-message',
  SUCCESS_MESSAGE: 'ui-form-success-message',
  
  // Form states
  REQUIRED: 'ui-form-required',
  OPTIONAL: 'ui-form-optional',
  DISABLED: 'ui-form-disabled',
  LOADING: 'ui-form-loading',
  
  // Form actions
  SUBMIT_BUTTON: 'ui-form-submit-button',
  RESET_BUTTON: 'ui-form-reset-button',
  CANCEL_BUTTON: 'ui-form-cancel-button',
} as const;

// Additional UI Component Test IDs
export const UI_TEST_IDS = {
  // Alert Dialog
  ALERT_DIALOG: {
    ROOT: 'ui-alert-dialog-root',
    TRIGGER: 'ui-alert-dialog-trigger',
    CONTENT: 'ui-alert-dialog-content',
    HEADER: 'ui-alert-dialog-header',
    TITLE: 'ui-alert-dialog-title',
    DESCRIPTION: 'ui-alert-dialog-description',
    FOOTER: 'ui-alert-dialog-footer',
    CANCEL: 'ui-alert-dialog-cancel',
    ACTION: 'ui-alert-dialog-action',
  },
  
  // Card
  CARD: {
    ROOT: 'ui-card-root',
    HEADER: 'ui-card-header',
    TITLE: 'ui-card-title',
    DESCRIPTION: 'ui-card-description',
    CONTENT: 'ui-card-content',
    FOOTER: 'ui-card-footer',
  },
  
  // Select
  SELECT: {
    ROOT: 'ui-select-root',
    TRIGGER: 'ui-select-trigger',
    VALUE: 'ui-select-value',
    CONTENT: 'ui-select-content',
    ITEM: (value: string) => `ui-select-item-${value}`,
    LABEL: 'ui-select-label',
    SEPARATOR: 'ui-select-separator',
  },
  
  // Checkbox
  CHECKBOX: {
    ROOT: 'ui-checkbox-root',
    INDICATOR: 'ui-checkbox-indicator',
    LABEL: 'ui-checkbox-label',
  },
  
  // Switch
  SWITCH: {
    ROOT: 'ui-switch-root',
    THUMB: 'ui-switch-thumb',
    LABEL: 'ui-switch-label',
  },
  
  // Textarea
  TEXTAREA: {
    ROOT: 'ui-textarea-root',
    LABEL: 'ui-textarea-label',
    ERROR: 'ui-textarea-error',
  },
  
  // Badge
  BADGE: {
    ROOT: 'ui-badge-root',
    VARIANT_DEFAULT: 'ui-badge-default',
    VARIANT_SECONDARY: 'ui-badge-secondary',
    VARIANT_DESTRUCTIVE: 'ui-badge-destructive',
    VARIANT_OUTLINE: 'ui-badge-outline',
  },
} as const;