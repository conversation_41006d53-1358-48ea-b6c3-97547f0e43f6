/**
 * Analytics module for tracking slug usage and performance
 */

interface SlugEvent {
  slug: string;
  event: 'view' | 'error' | 'redirect' | 'prefetch';
  timestamp: number;
  metadata?: Record<string, any>;
}

interface SlugMetrics {
  totalViews: number;
  uniqueViews: number;
  errorRate: number;
  avgLoadTime: number;
  popularSlugs: Array<{ slug: string; count: number }>;
  errorSlugs: Array<{ slug: string; error: string; count: number }>;
}

class SlugAnalytics {
  private events: SlugEvent[] = [];
  private sessionId: string;
  private enabled: boolean;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.enabled = process.env.NODE_ENV === 'production';
  }

  private generateSessionId(): string {
    return `slug_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Track a slug view event
   */
  trackSlugView(slug: string, loadTime?: number) {
    if (!this.enabled) return;

    this.addEvent({
      slug,
      event: 'view',
      timestamp: Date.now(),
      metadata: { 
        loadTime,
        sessionId: this.sessionId,
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined
      }
    });
  }

  /**
   * Track a slug error event
   */
  trackSlugError(slug: string, error: string, errorType: 'invalid' | 'not_found' | 'network') {
    if (!this.enabled) return;

    this.addEvent({
      slug,
      event: 'error',
      timestamp: Date.now(),
      metadata: { 
        error,
        errorType,
        sessionId: this.sessionId
      }
    });

    // Also log to console for debugging
    console.warn(`Slug error: ${slug} - ${error} (${errorType})`);
  }

  /**
   * Track a slug redirect event (UUID to slug)
   */
  trackSlugRedirect(originalSlug: string, targetPath: string) {
    if (!this.enabled) return;

    this.addEvent({
      slug: originalSlug,
      event: 'redirect',
      timestamp: Date.now(),
      metadata: { 
        targetPath,
        sessionId: this.sessionId
      }
    });
  }

  /**
   * Track a slug prefetch event
   */
  trackSlugPrefetch(slug: string, source: 'hover' | 'prediction' | 'manual') {
    if (!this.enabled) return;

    this.addEvent({
      slug,
      event: 'prefetch',
      timestamp: Date.now(),
      metadata: { 
        source,
        sessionId: this.sessionId
      }
    });
  }

  /**
   * Add an event to the analytics buffer
   */
  private addEvent(event: SlugEvent) {
    this.events.push(event);

    // Keep only last 1000 events to prevent memory issues
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    // Optionally send to analytics service
    this.sendToAnalyticsService(event);
  }

  /**
   * Send event to external analytics service
   */
  private async sendToAnalyticsService(event: SlugEvent) {
    try {
      // Only send in production and if analytics endpoint is configured
      if (process.env.NODE_ENV !== 'production' || !process.env.NEXT_PUBLIC_ANALYTICS_URL) {
        return;
      }

      await fetch(process.env.NEXT_PUBLIC_ANALYTICS_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'slug_event',
          ...event
        })
      });
    } catch (error) {
      // Silently fail analytics to not affect user experience
      console.debug('Analytics send failed:', error);
    }
  }

  /**
   * Get current session metrics
   */
  getSessionMetrics(): SlugMetrics {
    const viewEvents = this.events.filter(e => e.event === 'view');
    const errorEvents = this.events.filter(e => e.event === 'error');

    const slugCounts = new Map<string, number>();
    const errorCounts = new Map<string, { error: string; count: number }>();
    const loadTimes: number[] = [];

    viewEvents.forEach(event => {
      slugCounts.set(event.slug, (slugCounts.get(event.slug) || 0) + 1);
      if (event.metadata?.loadTime) {
        loadTimes.push(event.metadata.loadTime);
      }
    });

    errorEvents.forEach(event => {
      const key = `${event.slug}:${event.metadata?.error}`;
      const existing = errorCounts.get(key) || { error: event.metadata?.error || 'Unknown', count: 0 };
      errorCounts.set(key, { ...existing, count: existing.count + 1 });
    });

    const popularSlugs = Array.from(slugCounts.entries())
      .map(([slug, count]) => ({ slug, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const errorSlugs = Array.from(errorCounts.entries())
      .map(([key, data]) => ({ slug: key.split(':')[0], error: data.error, count: data.count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalViews: viewEvents.length,
      uniqueViews: new Set(viewEvents.map(e => e.slug)).size,
      errorRate: this.events.length > 0 ? errorEvents.length / this.events.length : 0,
      avgLoadTime: loadTimes.length > 0 ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length : 0,
      popularSlugs,
      errorSlugs
    };
  }

  /**
   * Export analytics data for debugging
   */
  exportData() {
    return {
      sessionId: this.sessionId,
      events: this.events,
      metrics: this.getSessionMetrics()
    };
  }

  /**
   * Clear analytics data
   */
  clear() {
    this.events = [];
  }
}

// Singleton instance
export const slugAnalytics = new SlugAnalytics();

// Helper functions for easy usage
export const trackSlugView = (slug: string, loadTime?: number) => {
  slugAnalytics.trackSlugView(slug, loadTime);
};

export const trackSlugError = (slug: string, error: string, errorType: 'invalid' | 'not_found' | 'network') => {
  slugAnalytics.trackSlugError(slug, error, errorType);
};

export const trackSlugRedirect = (originalSlug: string, targetPath: string) => {
  slugAnalytics.trackSlugRedirect(originalSlug, targetPath);
};

export const trackSlugPrefetch = (slug: string, source: 'hover' | 'prediction' | 'manual') => {
  slugAnalytics.trackSlugPrefetch(slug, source);
};