/**
 * Test ID Utility Functions for ADC Credit Service
 * 
 * Utilities for managing test-id attributes in components.
 * Includes environment-specific logic to only include test-ids in non-production builds.
 * Based on the Multi-Languages Service test utilities.
 */

import React from 'react';

/**
 * Environment check for test ID inclusion
 * Only include test-ids in development and test environments
 */
const shouldIncludeTestIds = (): boolean => {
  return process.env.NODE_ENV !== 'production';
};

/**
 * Creates a test-id attribute object that can be spread into component props
 * Returns empty object in production to keep builds clean
 * 
 * @param testId - The test-id value
 * @returns Object with data-testid attribute or empty object
 * 
 * @example
 * // In a component
 * <button {...getTestId('submit-button')}>Submit</button>
 * 
 * // Results in development:
 * <button data-testid="submit-button">Submit</button>
 * 
 * // Results in production:
 * <button>Submit</button>
 */
export const getTestId = (testId: string | undefined): Record<string, string> => {
  if (!testId || !shouldIncludeTestIds()) {
    return {};
  }
  
  return {
    'data-testid': testId,
  };
};

/**
 * Creates a test-id attribute string for use in template literals or direct assignment
 * Returns undefined in production
 * 
 * @param testId - The test-id value
 * @returns The test-id string or undefined
 * 
 * @example
 * // Direct assignment
 * element.setAttribute('data-testid', getTestIdString('my-element') || '');
 * 
 * // Template literal
 * const selector = `[data-testid="${getTestIdString('my-element')}"]`;
 */
export const getTestIdString = (testId: string | undefined): string | undefined => {
  if (!testId || !shouldIncludeTestIds()) {
    return undefined;
  }
  
  return testId;
};

/**
 * Creates a CSS selector for a test-id that can be used in testing frameworks
 * Returns empty string in production
 * 
 * @param testId - The test-id value
 * @returns CSS selector string or empty string
 * 
 * @example
 * // In tests
 * const button = page.locator(getTestIdSelector('submit-button'));
 * 
 * // Results in: '[data-testid="submit-button"]'
 */
export const getTestIdSelector = (testId: string | undefined): string => {
  if (!testId || !shouldIncludeTestIds()) {
    return '';
  }
  
  return `[data-testid="${testId}"]`;
};

/**
 * Combines multiple test-id utility functions for comprehensive component support
 * 
 * @param testId - The test-id value
 * @returns Object with all test-id related utilities
 * 
 * @example
 * const testProps = getTestIdProps('my-button');
 * // Returns: { 
 * //   props: { 'data-testid': 'my-button' },
 * //   selector: '[data-testid="my-button"]',
 * //   string: 'my-button'
 * // }
 */
export const getTestIdProps = (testId: string | undefined) => {
  return {
    props: getTestId(testId),
    selector: getTestIdSelector(testId),
    string: getTestIdString(testId),
  };
};

/**
 * Validates test-id naming convention
 * Ensures test-ids follow the feature-component-action pattern
 * 
 * @param testId - The test-id to validate
 * @returns Boolean indicating if test-id follows convention
 * 
 * @example
 * validateTestId('settings-user-save-button'); // true
 * validateTestId('invalid-id'); // false
 */
export const validateTestId = (testId: string): boolean => {
  // Convention: feature-component-action (at least 3 parts)
  const parts = testId.split('-');
  return parts.length >= 3 && parts.every(part => part.length > 0);
};

/**
 * Generates test-id for dynamic content with validation
 * 
 * @param prefix - The prefix for the test-id
 * @param dynamicPart - The dynamic part (ID, slug, etc.)
 * @param suffix - Optional suffix
 * @returns Generated test-id string
 * 
 * @example
 * generateDynamicTestId('shop-card', 'shop-123'); // 'shop-card-shop-123'
 * generateDynamicTestId('shop-card', 'shop-123', 'edit-button'); // 'shop-card-shop-123-edit-button'
 */
export const generateDynamicTestId = (
  prefix: string, 
  dynamicPart: string, 
  suffix?: string
): string => {
  const cleanDynamicPart = dynamicPart.replace(/[^a-zA-Z0-9-]/g, '-');
  const parts = [prefix, cleanDynamicPart];
  
  if (suffix) {
    parts.push(suffix);
  }
  
  return parts.join('-');
};

/**
 * React hook for managing test-ids in components
 * Provides consistent test-id management across components
 * 
 * @param baseTestId - Base test-id for the component
 * @returns Object with test-id utilities
 * 
 * @example
 * function MyComponent() {
 *   const testIds = useTestIds('my-component');
 *   
 *   return (
 *     <div {...testIds.getProps()}>
 *       <button {...testIds.getProps('submit-button')}>Submit</button>
 *     </div>
 *   );
 * }
 */
export const useTestIds = (baseTestId: string) => {
  const getProps = (suffix?: string) => {
    const fullTestId = suffix ? `${baseTestId}-${suffix}` : baseTestId;
    return getTestId(fullTestId);
  };
  
  const getSelector = (suffix?: string) => {
    const fullTestId = suffix ? `${baseTestId}-${suffix}` : baseTestId;
    return getTestIdSelector(fullTestId);
  };
  
  const getString = (suffix?: string) => {
    const fullTestId = suffix ? `${baseTestId}-${suffix}` : baseTestId;
    return getTestIdString(fullTestId);
  };
  
  return {
    getProps,
    getSelector,
    getString,
    baseTestId,
  };
};

/**
 * Settings Service specific test utilities
 * Helper functions for testing Settings Service integration
 */
export const settingsTestUtils = {
  /**
   * Generates test-id for settings form fields
   */
  settingField: (scope: string, settingKey: string) => 
    `settings-${scope}-${settingKey.replace(/\./g, '-')}`,
  
  /**
   * Generates test-id for settings sections
   */
  settingSection: (scope: string, section: string) => 
    `settings-${scope}-${section}-section`,
  
  /**
   * Generates test-id for settings actions
   */
  settingAction: (scope: string, action: string) => 
    `settings-${scope}-${action}-button`,
  
  /**
   * Generates test-id for settings status indicators
   */
  settingStatus: (scope: string, status: string) => 
    `settings-${scope}-${status}-status`,
};

/**
 * Type definitions for test-id props
 */
export type TestIdProps = Record<string, string>;
export type TestIdSelector = string;
export type TestIdString = string | undefined;