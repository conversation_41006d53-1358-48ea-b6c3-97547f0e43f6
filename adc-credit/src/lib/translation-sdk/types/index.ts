/**
 * TypeScript definitions for ADC Translation SDK
 */

export interface TranslationSDKConfig {
  defaultLocale: string;
  apiUrl: string;
  projectId: string;
  generateLocales?: boolean;
  debug?: boolean;
}

export interface TranslationData {
  [key: string]: string;
}

export interface NamespaceTranslations {
  [namespace: string]: TranslationData;
}

export interface TranslationResponse {
  success: boolean;
  data?: {
    translations: TranslationData;
    namespace: string;
    locale: string;
    project_id: string;
    count: number;
  };
  error?: string;
  message?: string;
}

export interface BulkSyncResponse {
  success: boolean;
  data?: {
    namespace: string;
    locale: string;
    created_keys: string[];
    updated_keys: string[];
    total_keys: number;
  };
  error?: string;
  message?: string;
}

export interface UseTranslationReturn {
  t: (key: string, defaultText: string, variables?: Record<string, string | number>) => string;
  locale: string;
  setLocale: (locale: string) => void;
  loading: boolean;
  error: string | null;
  isReady: boolean;
}

export interface LocaleFileData {
  [key: string]: string;
}

export interface GenerationOptions {
  namespace: string;
  key: string;
  defaultText: string;
  force?: boolean;
}

export interface CacheEntry {
  data: TranslationData;
  timestamp: number;
  locale: string;
  namespace: string;
}

export interface ApiClientOptions {
  baseUrl: string;
  projectId: string;
  apiKey?: string;
  timeout?: number;
}

export type TranslationStrategy = 'static-files' | 'api-with-cache' | 'fallback-only';

export interface LanguageConfig {
  code: string;
  name: string;
  strategy: TranslationStrategy;
}