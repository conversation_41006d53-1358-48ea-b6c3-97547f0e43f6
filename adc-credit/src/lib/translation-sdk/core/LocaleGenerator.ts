/**
 * Runtime locale file generator
 * Generates default language locale files when GENERATE_LOCALES flag is enabled
 */

import { writeFile, readFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { join, dirname } from 'path';
import type { LocaleFileData, GenerationOptions } from '../types';

export class LocaleGenerator {
  private defaultLocale: string;
  private localesDir: string;
  private generateEnabled: boolean;
  private debug: boolean;
  private cache: Map<string, LocaleFileData> = new Map();

  constructor(
    defaultLocale: string = 'en',
    localesDir: string = 'src/locales',
    generateEnabled: boolean = false,
    debug: boolean = false
  ) {
    this.defaultLocale = defaultLocale;
    this.localesDir = localesDir;
    this.generateEnabled = generateEnabled;
    this.debug = debug;
  }

  /**
   * Add a translation key to the locale file (runtime generation)
   */
  async addTranslationKey(options: GenerationOptions): Promise<void> {
    // Only generate if flag is enabled and we're in development
    if (!this.generateEnabled || process.env.NODE_ENV === 'production') {
      return;
    }

    const { namespace, key, defaultText, force = false } = options;
    const filePath = this.getLocaleFilePath(namespace);

    try {
      // Load existing data from cache or file
      let localeData = this.cache.get(namespace);
      if (!localeData) {
        localeData = await this.loadLocaleFile(filePath);
        this.cache.set(namespace, localeData);
      }

      // Only add if key doesn't exist or force is true
      if (!localeData[key] || force) {
        localeData[key] = defaultText;
        
        // Write to file
        await this.writeLocaleFile(filePath, localeData);
        
        // Update cache
        this.cache.set(namespace, localeData);
        
        if (this.debug) {
          console.log(`📝 Generated translation key: ${namespace}.${key} = "${defaultText}"`);
        }
      }
    } catch (error) {
      if (this.debug) {
        console.error(`Failed to generate locale key ${namespace}.${key}:`, error);
      }
    }
  }

  /**
   * Load locale file for a namespace
   */
  async loadNamespaceLocale(namespace: string): Promise<LocaleFileData> {
    // Check cache first
    const cached = this.cache.get(namespace);
    if (cached) {
      return cached;
    }

    const filePath = this.getLocaleFilePath(namespace);
    const data = await this.loadLocaleFile(filePath);
    this.cache.set(namespace, data);
    return data;
  }

  /**
   * Generate bulk locale data for sync with backend
   */
  async generateBulkData(namespace: string): Promise<LocaleFileData> {
    return this.loadNamespaceLocale(namespace);
  }

  /**
   * Clear cache for a namespace
   */
  clearCache(namespace?: string): void {
    if (namespace) {
      this.cache.delete(namespace);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Check if generation is enabled
   */
  isGenerationEnabled(): boolean {
    return this.generateEnabled;
  }

  /**
   * Get all available namespaces
   */
  async getAvailableNamespaces(): Promise<string[]> {
    try {
      const localeDir = join(process.cwd(), this.localesDir, this.defaultLocale);
      if (!existsSync(localeDir)) {
        return [];
      }

      const { readdir } = await import('fs/promises');
      const files = await readdir(localeDir);
      return files
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''));
    } catch {
      return [];
    }
  }

  private getLocaleFilePath(namespace: string): string {
    return join(process.cwd(), this.localesDir, this.defaultLocale, `${namespace}.json`);
  }

  private async loadLocaleFile(filePath: string): Promise<LocaleFileData> {
    try {
      if (!existsSync(filePath)) {
        return {};
      }

      const content = await readFile(filePath, 'utf-8');
      return JSON.parse(content) as LocaleFileData;
    } catch {
      return {};
    }
  }

  private async writeLocaleFile(filePath: string, data: LocaleFileData): Promise<void> {
    try {
      // Ensure directory exists
      const dir = dirname(filePath);
      await mkdir(dir, { recursive: true });

      // Sort keys for consistent output
      const sortedData: LocaleFileData = {};
      Object.keys(data)
        .sort()
        .forEach(key => {
          sortedData[key] = data[key];
        });

      // Write with pretty formatting
      const jsonContent = JSON.stringify(sortedData, null, 2);
      await writeFile(filePath, jsonContent, 'utf-8');
    } catch (error) {
      if (this.debug) {
        console.error(`Failed to write locale file ${filePath}:`, error);
      }
      throw error;
    }
  }
}