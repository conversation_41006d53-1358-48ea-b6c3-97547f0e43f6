/**
 * Cache manager for translation data
 * Handles SWR caching for API calls and static file caching
 */

import type { TranslationData, CacheEntry } from '../types';

export class CacheManager {
  private cache: Map<string, CacheEntry> = new Map();
  private defaultTTL: number;
  private maxCacheSize: number;
  private debug: boolean;

  constructor(
    defaultTTL: number = 5 * 60 * 1000, // 5 minutes
    maxCacheSize: number = 100,
    debug: boolean = false
  ) {
    this.defaultTTL = defaultTTL;
    this.maxCacheSize = maxCacheSize;
    this.debug = debug;
  }

  /**
   * Get cached translations
   */
  get(namespace: string, locale: string): TranslationData | null {
    const key = this.getCacheKey(namespace, locale);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if cache is still valid
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      if (this.debug) {
        console.log(`🗑️ Cache expired for ${namespace}/${locale}`);
      }
      return null;
    }

    if (this.debug) {
      console.log(`💾 Cache hit for ${namespace}/${locale}`);
    }

    return entry.data;
  }

  /**
   * Set cached translations
   */
  set(
    namespace: string, 
    locale: string, 
    data: TranslationData, 
    ttl?: number
  ): void {
    const key = this.getCacheKey(namespace, locale);
    
    // Evict old entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldest();
    }

    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      locale,
      namespace,
    };

    this.cache.set(key, entry);

    if (this.debug) {
      console.log(`💾 Cached ${Object.keys(data).length} translations for ${namespace}/${locale}`);
    }

    // Set TTL cleanup
    if (ttl || this.defaultTTL) {
      setTimeout(() => {
        if (this.cache.has(key)) {
          this.cache.delete(key);
          if (this.debug) {
            console.log(`🕒 TTL expired for ${namespace}/${locale}`);
          }
        }
      }, ttl || this.defaultTTL);
    }
  }

  /**
   * Check if cache entry should be revalidated
   */
  shouldRevalidate(namespace: string, locale: string): boolean {
    const key = this.getCacheKey(namespace, locale);
    const entry = this.cache.get(key);

    if (!entry) {
      return true; // No cache, needs fetch
    }

    // Revalidate if cache is old (SWR behavior)
    const age = Date.now() - entry.timestamp;
    const shouldRevalidate = age > this.defaultTTL / 2; // Revalidate at half TTL

    if (this.debug && shouldRevalidate) {
      console.log(`🔄 Should revalidate ${namespace}/${locale} (age: ${Math.round(age / 1000)}s)`);
    }

    return shouldRevalidate;
  }

  /**
   * Invalidate cache for specific namespace/locale
   */
  invalidate(namespace?: string, locale?: string): void {
    if (!namespace && !locale) {
      // Clear all cache
      this.cache.clear();
      if (this.debug) {
        console.log('🗑️ Cleared all cache');
      }
      return;
    }

    if (namespace && locale) {
      // Clear specific entry
      const key = this.getCacheKey(namespace, locale);
      this.cache.delete(key);
      if (this.debug) {
        console.log(`🗑️ Cleared cache for ${namespace}/${locale}`);
      }
      return;
    }

    // Clear by namespace or locale pattern
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache) {
      if (
        (namespace && entry.namespace === namespace) ||
        (locale && entry.locale === locale)
      ) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (this.debug) {
      console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for ${namespace || '*'}/${locale || '*'}`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    entries: Array<{ namespace: string; locale: string; age: number; keyCount: number }>;
  } {
    const entries = Array.from(this.cache.values()).map(entry => ({
      namespace: entry.namespace,
      locale: entry.locale,
      age: Date.now() - entry.timestamp,
      keyCount: Object.keys(entry.data).length,
    }));

    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      entries,
    };
  }

  /**
   * Force cache cleanup
   */
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    if (this.debug) {
      console.log(`🧹 Cleaned up ${keysToDelete.length} expired cache entries`);
    }
  }

  private getCacheKey(namespace: string, locale: string): string {
    return `${namespace}:${locale}`;
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > this.defaultTTL;
  }

  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      if (this.debug) {
        console.log(`🗑️ Evicted oldest cache entry: ${oldestKey}`);
      }
    }
  }
}