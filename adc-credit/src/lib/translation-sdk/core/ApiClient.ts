/**
 * API client for Multi-Languages service integration
 * Handles fetching translations and bulk sync operations
 */

import type { 
  TranslationResponse, 
  BulkSyncResponse, 
  ApiClientOptions, 
  TranslationData 
} from '../types';

export class ApiClient {
  private baseUrl: string;
  private projectId: string;
  private apiKey?: string;
  private timeout: number;
  private debug: boolean;

  constructor(options: ApiClientOptions & { debug?: boolean }) {
    this.baseUrl = options.baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.projectId = options.projectId;
    this.apiKey = options.apiKey;
    this.timeout = options.timeout || 10000;
    this.debug = options.debug || false;
  }

  /**
   * Fetch translations for a namespace and locale
   */
  async fetchNamespaceTranslations(
    namespace: string, 
    locale: string
  ): Promise<TranslationResponse> {
    try {
      const url = `/api/translations/${namespace}/${locale}`;
      
      if (this.debug) {
        console.log(`🌐 Fetching translations: ${namespace}/${locale}`);
      }

      const response = await this.makeRequest(url, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (this.debug) {
        console.log(`✅ Fetched ${Object.keys(data.data?.translations || {}).length} translations for ${namespace}/${locale}`);
      }

      return data;
    } catch (error) {
      if (this.debug) {
        console.error(`❌ Failed to fetch ${namespace}/${locale}:`, error);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Sync namespace translations with backend (bulk operation)
   */
  async syncNamespace(
    namespace: string,
    locale: string,
    translations: TranslationData
  ): Promise<BulkSyncResponse> {
    try {
      const url = `/api/translations/sync-namespace`;
      
      const payload = {
        namespace,
        locale,
        project_id: this.projectId,
        translations,
      };

      if (this.debug) {
        console.log(`🔄 Syncing ${Object.keys(translations).length} keys for ${namespace}/${locale}`);
      }

      const response = await this.makeRequest(url, {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      
      if (this.debug) {
        console.log(`✅ Synced ${namespace}/${locale}:`, data.data);
      }

      return data;
    } catch (error) {
      if (this.debug) {
        console.error(`❌ Failed to sync ${namespace}/${locale}:`, error);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Fetch a single translation key
   */
  async fetchSingleTranslation(
    namespace: string,
    key: string,
    locale: string
  ): Promise<string | null> {
    try {
      const url = `/api/translations/${namespace}/${key}/${locale}`;
      
      const response = await this.makeRequest(url, {
        method: 'GET',
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data.data?.value || null;
    } catch {
      return null;
    }
  }

  /**
   * Check if API is available
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/health', {
        method: 'GET',
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async makeRequest(endpoint: string, options: RequestInit): Promise<Response> {
    const url = endpoint.startsWith('/') 
      ? endpoint 
      : `${this.baseUrl}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add API key if available
    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
}