/**
 * Main Translation SDK class
 * Orchestrates locale generation, API calls, and caching
 */

import { LocaleGenerator } from './LocaleGenerator';
import { ApiClient } from './ApiClient';
import { CacheManager } from './CacheManager';
import type { 
  TranslationSDKConfig, 
  TranslationData, 
  BulkSyncResponse,
  LanguageConfig 
} from '../types';

export class TranslationSDK {
  private config: TranslationSDKConfig;
  private localeGenerator: LocaleGenerator;
  private apiClient: ApiClient;
  private cacheManager: CacheManager;
  private currentLocale: string;
  private supportedLanguages: LanguageConfig[];

  constructor(config: TranslationSDKConfig) {
    this.config = {
      generateLocales: process.env.GENERATE_LOCALES === 'true',
      debug: process.env.NODE_ENV === 'development',
      ...config,
    };

    this.currentLocale = this.config.defaultLocale;
    this.supportedLanguages = this.initializeSupportedLanguages();

    // Initialize core components
    this.localeGenerator = new LocaleGenerator(
      this.config.defaultLocale,
      'src/locales',
      this.config.generateLocales,
      this.config.debug
    );

    this.apiClient = new ApiClient({
      baseUrl: this.config.apiUrl,
      projectId: this.config.projectId,
      debug: this.config.debug,
    });

    this.cacheManager = new CacheManager(
      5 * 60 * 1000, // 5 minutes TTL
      100, // Max 100 cache entries
      this.config.debug
    );

    if (this.config.debug) {
      console.log('🚀 Translation SDK initialized:', {
        projectId: this.config.projectId,
        defaultLocale: this.config.defaultLocale,
        generateLocales: this.config.generateLocales,
        apiUrl: this.config.apiUrl,
      });
    }
  }

  /**
   * Get translation for a key
   */
  async getTranslation(
    namespace: string,
    key: string,
    locale: string = this.currentLocale,
    defaultText: string = key
  ): Promise<string> {
    try {
      // Generate locale file if enabled (default language only)
      if (locale === this.config.defaultLocale && this.config.generateLocales) {
        await this.localeGenerator.addTranslationKey({
          namespace,
          key,
          defaultText,
        });
      }

      // For default language, try static files first
      if (locale === this.config.defaultLocale) {
        const staticTranslations = await this.loadStaticTranslations(namespace);
        if (staticTranslations[key]) {
          return this.interpolate(staticTranslations[key], {});
        }
      }

      // For other languages, use API with caching
      const translations = await this.loadNamespaceTranslations(namespace, locale);
      const translation = translations[key];

      return translation || defaultText;
    } catch (error) {
      if (this.config.debug) {
        console.error(`Failed to get translation ${namespace}.${key}:`, error);
      }
      return defaultText;
    }
  }

  /**
   * Load all translations for a namespace and locale
   */
  async loadNamespaceTranslations(
    namespace: string,
    locale: string = this.currentLocale
  ): Promise<TranslationData> {
    // Check cache first
    const cached = this.cacheManager.get(namespace, locale);
    if (cached && !this.cacheManager.shouldRevalidate(namespace, locale)) {
      return cached;
    }

    try {
      // For default language, prefer static files
      if (locale === this.config.defaultLocale) {
        const staticTranslations = await this.loadStaticTranslations(namespace);
        if (Object.keys(staticTranslations).length > 0) {
          this.cacheManager.set(namespace, locale, staticTranslations);
          return staticTranslations;
        }
      }

      // Load from API
      const response = await this.apiClient.fetchNamespaceTranslations(namespace, locale);
      
      if (response.success && response.data) {
        const translations = response.data.translations;
        this.cacheManager.set(namespace, locale, translations);
        return translations;
      } else {
        // Return cached data if API fails
        if (cached) {
          if (this.config.debug) {
            console.warn(`API failed for ${namespace}/${locale}, using cached data`);
          }
          return cached;
        }
        
        throw new Error(response.error || 'Failed to load translations');
      }
    } catch (error) {
      if (this.config.debug) {
        console.error(`Failed to load ${namespace}/${locale}:`, error);
      }
      
      // Return cached data if available
      return cached || {};
    }
  }

  /**
   * Sync namespace with backend (bulk operation)
   */
  async syncNamespace(namespace: string): Promise<BulkSyncResponse> {
    try {
      // Get generated locale data for default language
      const localeData = await this.localeGenerator.generateBulkData(namespace);
      
      if (Object.keys(localeData).length === 0) {
        return {
          success: false,
          error: `No locale data found for namespace: ${namespace}`,
        };
      }

      // Sync with backend
      const result = await this.apiClient.syncNamespace(
        namespace,
        this.config.defaultLocale,
        localeData
      );

      // Invalidate cache for this namespace
      this.cacheManager.invalidate(namespace);

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Sync all available namespaces
   */
  async syncAllNamespaces(): Promise<BulkSyncResponse[]> {
    const namespaces = await this.localeGenerator.getAvailableNamespaces();
    const results: BulkSyncResponse[] = [];

    for (const namespace of namespaces) {
      const result = await this.syncNamespace(namespace);
      results.push(result);
    }

    return results;
  }

  /**
   * Set current locale
   */
  setLocale(locale: string): void {
    this.currentLocale = locale;
    if (this.config.debug) {
      console.log(`🌐 Locale changed to: ${locale}`);
    }
  }

  /**
   * Get current locale
   */
  getLocale(): string {
    return this.currentLocale;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): LanguageConfig[] {
    return this.supportedLanguages;
  }

  /**
   * Clear cache
   */
  clearCache(namespace?: string, locale?: string): void {
    this.cacheManager.invalidate(namespace, locale);
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheManager.getStats();
  }

  /**
   * Check if generation is enabled
   */
  isGenerationEnabled(): boolean {
    return this.localeGenerator.isGenerationEnabled();
  }

  private async loadStaticTranslations(namespace: string): Promise<TranslationData> {
    try {
      return await this.localeGenerator.loadNamespaceLocale(namespace);
    } catch {
      return {};
    }
  }

  private interpolate(text: string, variables: Record<string, string | number>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key]?.toString() || match;
    });
  }

  private initializeSupportedLanguages(): LanguageConfig[] {
    return [
      {
        code: 'en',
        name: 'English',
        strategy: 'static-files',
      },
      {
        code: 'fr', 
        name: 'Français',
        strategy: 'api-with-cache',
      },
      {
        code: 'th',
        name: 'ไทย',
        strategy: 'api-with-cache',
      },
    ];
  }
}