/**
 * React context provider for Translation SDK
 * Provides translation functionality to React components
 */

'use client';

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { TranslationSDK } from '../core/TranslationSDK';
import type { TranslationSDKConfig, UseTranslationReturn } from '../types';

interface TranslationContextValue {
  sdk: TranslationSDK;
  locale: string;
  setLocale: (locale: string) => void;
  loading: boolean;
  error: string | null;
}

const TranslationContext = createContext<TranslationContextValue | null>(null);

interface TranslationProviderProps {
  children: React.ReactNode;
  config: TranslationSDKConfig;
}

export function TranslationProvider({ children, config }: TranslationProviderProps) {
  const sdkRef = useRef<TranslationSDK | null>(null);
  const [locale, setLocaleState] = useState(config.defaultLocale);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize SDK once
  if (!sdkRef.current) {
    sdkRef.current = new TranslationSDK(config);
  }

  const sdk = sdkRef.current;

  const setLocale = useCallback((newLocale: string) => {
    setLocaleState(newLocale);
    sdk.setLocale(newLocale);
    
    if (config.debug) {
      console.log(`🌐 Provider locale changed to: ${newLocale}`);
    }
  }, [sdk, config.debug]);

  const contextValue: TranslationContextValue = {
    sdk,
    locale,
    setLocale,
    loading,
    error,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

/**
 * Hook to access translation context
 */
export function useTranslationContext(): TranslationContextValue {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }

  return context;
}

/**
 * Main translation hook for React components
 */
export function useTranslation(namespace: string): UseTranslationReturn {
  const { sdk, locale, setLocale } = useTranslationContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Cache for namespace translations
  const [translations, setTranslations] = useState<Record<string, string>>({});

  // Load namespace translations
  const loadNamespace = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const namespaceTranslations = await sdk.loadNamespaceTranslations(namespace, locale);
      setTranslations(namespaceTranslations);
      setIsReady(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load translations');
      setIsReady(false);
    } finally {
      setLoading(false);
    }
  }, [sdk, namespace, locale]);

  // Load translations when namespace or locale changes
  useEffect(() => {
    loadNamespace();
  }, [loadNamespace]);

  // Translation function
  const t = useCallback((
    key: string,
    defaultText: string,
    variables?: Record<string, string | number>
  ): string => {
    // Get translation from loaded namespace
    let translation = translations[key];

    // If not found and we're generating locales, trigger generation and use default
    if (!translation && sdk.isGenerationEnabled() && locale === sdk.getLocale()) {
      // Trigger generation asynchronously
      sdk.getTranslation(namespace, key, locale, defaultText).catch(err => {
        if (sdk.getCacheStats) { // Check debug mode
          console.error('Background translation generation failed:', err);
        }
      });
      translation = defaultText;
    }

    // Use translation or fallback to default text
    const finalText = translation || defaultText;

    // Interpolate variables if provided
    if (variables && Object.keys(variables).length > 0) {
      return finalText.replace(/\{\{(\w+)\}\}/g, (match, varKey) => {
        return variables[varKey]?.toString() || match;
      });
    }

    return finalText;
  }, [translations, namespace, locale, defaultText, sdk]);

  return {
    t,
    locale,
    setLocale,
    loading,
    error,
    isReady,
  };
}

/**
 * Hook for preloading multiple namespaces
 */
export function useTranslationPreload(namespaces: string[]): {
  loading: boolean;
  error: string | null;
  loadedNamespaces: string[];
} {
  const { sdk, locale } = useTranslationContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadedNamespaces, setLoadedNamespaces] = useState<string[]>([]);

  useEffect(() => {
    const preloadNamespaces = async () => {
      setLoading(true);
      setError(null);
      const loaded: string[] = [];

      try {
        await Promise.all(
          namespaces.map(async (namespace) => {
            try {
              await sdk.loadNamespaceTranslations(namespace, locale);
              loaded.push(namespace);
            } catch (err) {
              console.warn(`Failed to preload namespace ${namespace}:`, err);
            }
          })
        );

        setLoadedNamespaces(loaded);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to preload namespaces');
      } finally {
        setLoading(false);
      }
    };

    if (namespaces.length > 0) {
      preloadNamespaces();
    }
  }, [namespaces, locale, sdk]);

  return {
    loading,
    error,
    loadedNamespaces,
  };
}

/**
 * Hook for bulk sync operations
 */
export function useTranslationSync(): {
  syncNamespace: (namespace: string) => Promise<void>;
  syncAllNamespaces: () => Promise<void>;
  loading: boolean;
  error: string | null;
  lastSyncResult: any;
} {
  const { sdk } = useTranslationContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSyncResult, setLastSyncResult] = useState<any>(null);

  const syncNamespace = useCallback(async (namespace: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await sdk.syncNamespace(namespace);
      setLastSyncResult(result);
      
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sync failed');
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  const syncAllNamespaces = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const results = await sdk.syncAllNamespaces();
      setLastSyncResult(results);
      
      const failures = results.filter(r => !r.success);
      if (failures.length > 0) {
        throw new Error(`${failures.length} namespaces failed to sync`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bulk sync failed');
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  return {
    syncNamespace,
    syncAllNamespaces,
    loading,
    error,
    lastSyncResult,
  };
}