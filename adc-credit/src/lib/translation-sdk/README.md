# ADC Translation SDK

A modern, efficient translation system for React applications with runtime locale generation and Multi-Languages service integration.

## Key Features

- **🚀 Runtime Generation**: Automatically generate locale files as you use translations
- **📦 Framework Agnostic**: Core SDK works with any JavaScript framework
- **⚛️ React Integration**: Dedicated hooks and providers for React apps
- **💾 Smart Caching**: SWR-style caching with automatic revalidation
- **🌐 Multi-Language**: Static files for default language, API calls for others
- **🔄 Bulk Operations**: Efficient namespace-based sync with backend

## Quick Start

### 1. Setup Translation Provider

```tsx
// app/layout.tsx
import { TranslationProvider } from '@/lib/translation-sdk';

export default function RootLayout({ children }) {
  return (
    <TranslationProvider config={{
      defaultLocale: 'en',
      apiUrl: 'http://localhost:8400',
      projectId: 'your-project-id',
      generateLocales: process.env.GENERATE_LOCALES === 'true',
    }}>
      {children}
    </TranslationProvider>
  );
}
```

### 2. Use Translations in Components

```tsx
// components/MyComponent.tsx
import { useTranslation } from '@/lib/translation-sdk';

export function MyComponent() {
  const { t } = useTranslation('namespace');
  
  return (
    <div>
      <h1>{t('title', 'Default Title')}</h1>
      <p>{t('description', 'Default description text')}</p>
    </div>
  );
}
```

### 3. Generate and Sync Translations

```bash
# Enable runtime generation during development
npm run dev:generate

# Browse your app to generate locale files
# Files will be created in src/locales/en/{namespace}.json

# Sync with backend when ready
npm run sync-translations
```

## Development Workflow

### 1. **Development with Generation**
```bash
# Start development with locale generation enabled
GENERATE_LOCALES=true npm run dev
# or
npm run dev:generate

# As you browse your app and use t() calls:
# - src/locales/en/homepage.json gets created
# - src/locales/en/features.json gets created
# - etc.
```

### 2. **Translation Key Format**
```typescript
// This call:
const { t } = useTranslation('homepage');
t('hero_title', 'Shop Credit Management Platform');

// Generates in src/locales/en/homepage.json:
{
  "hero_title": "Shop Credit Management Platform"
}
```

### 3. **Sync with Backend**
```bash
# Sync all namespaces
npm run sync-translations

# Sync specific namespace
npm run sync-translations homepage

# The script will:
# - Read your locale files
# - Upload to Multi-Languages service via bulk API
# - Create translation keys if they don't exist
# - Set English content for auto-translation
```

### 4. **Runtime Behavior**
- **English (default)**: Loads from static files (fast)
- **Other languages**: Fetches from API with SWR caching
- **Fallback**: Always shows default text if translation missing
- **Caching**: Smart revalidation for optimal performance

## API Reference

### useTranslation(namespace)

```typescript
const { t, locale, setLocale, loading, error, isReady } = useTranslation('namespace');

// Translate with fallback
const text = t('key', 'Default Text');

// Translate with variables
const text = t('welcome_user', 'Welcome {{name}}!', { name: 'John' });
```

### useTranslationSync()

```typescript
const { syncNamespace, syncAllNamespaces, loading, error } = useTranslationSync();

// Sync specific namespace
await syncNamespace('homepage');

// Sync all available namespaces
await syncAllNamespaces();
```

### useTranslationPreload(namespaces)

```typescript
const { loading, loadedNamespaces } = useTranslationPreload(['homepage', 'features']);

// Preload multiple namespaces for better performance
```

## Configuration

### Environment Variables

```bash
# Enable locale generation (development only)
GENERATE_LOCALES=true

# Backend API URL
NEXT_PUBLIC_BACKEND_URL=http://localhost:8400

# Project ID for Multi-Languages service
NEXT_PUBLIC_CREDIT_PROJECT_ID=your-project-id
```

### TranslationProvider Config

```typescript
interface TranslationSDKConfig {
  defaultLocale: string;        // Default: 'en'
  apiUrl: string;              // Backend API URL
  projectId: string;           // Multi-Languages project ID
  generateLocales?: boolean;   // Enable runtime generation
  debug?: boolean;             // Enable debug logging
}
```

## File Structure

```
src/locales/
├── en/
│   ├── homepage.json      # Generated automatically
│   ├── features.json      # Generated automatically
│   ├── pricing.json       # Generated automatically
│   └── ...
```

## Performance

- **Static Files**: Default language loads instantly
- **API Caching**: SWR-based caching with 5-minute TTL
- **Smart Revalidation**: Background updates when needed
- **Minimal Bundle**: Only loads what you use

## Best Practices

### 1. **Namespace Organization**
```typescript
// Good: Organize by page/feature
useTranslation('homepage')    // Landing page content
useTranslation('dashboard')   // Dashboard-specific content
useTranslation('settings')    // Settings page content

// Avoid: Generic namespaces
useTranslation('ui')         // Too broad
```

### 2. **Translation Keys**
```typescript
// Good: Descriptive, hierarchical keys
t('hero_title', 'Shop Credit Management Platform')
t('pricing_cta_button', 'Choose Plan')
t('error_invalid_email', 'Please enter a valid email')

// Avoid: Generic or unclear keys
t('title', 'Title')
t('button', 'Button')
```

### 3. **Default Text**
```typescript
// Good: Meaningful defaults
t('welcome_message', 'Welcome to our platform!')

// Avoid: Key names as defaults
t('welcome_message', 'welcome_message')
```

## Troubleshooting

### Locale Files Not Generated
1. Check `GENERATE_LOCALES=true` is set
2. Verify you're in development mode
3. Make sure you're actually visiting pages with `t()` calls

### Sync Failures
1. Check backend is running (`npm run sync-translations` shows health check)
2. Verify API endpoints are accessible
3. Check Multi-Languages service integration

### Translation Not Showing
1. Check browser console for errors
2. Verify namespace and key names match
3. Check API responses in Network tab
4. Ensure fallback text is provided

## Migration from Old System

The new SDK replaces the complex fallback translation system with a simpler, more efficient approach:

1. **Before**: Complex fallback files with hardcoded translations
2. **After**: Runtime generation + API-first approach with caching

Migration is automatic when you update your import statements:
```typescript
// Old
import { useTranslation } from '@/lib/i18n/hooks';

// New
import { useTranslation } from '@/lib/translation-sdk';
```