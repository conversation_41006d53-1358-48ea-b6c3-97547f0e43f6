/**
 * ADC Translation SDK
 * Main entry point for the translation system
 */

// Core SDK classes
export { TranslationSDK } from './core/TranslationSDK';
export { LocaleGenerator } from './core/LocaleGenerator';
export { ApiClient } from './core/ApiClient';
export { CacheManager } from './core/CacheManager';

// React integration
export { 
  TranslationProvider, 
  useTranslation, 
  useTranslationContext,
  useTranslationPreload,
  useTranslationSync
} from './react/TranslationProvider';

// Types
export type {
  TranslationSDKConfig,
  TranslationData,
  NamespaceTranslations,
  TranslationResponse,
  BulkSyncResponse,
  UseTranslationReturn,
  LocaleFileData,
  GenerationOptions,
  CacheEntry,
  ApiClientOptions,
  TranslationStrategy,
  LanguageConfig,
} from './types';

// Convenience function to create SDK instance
export function createTranslationSDK(config: Partial<TranslationSDKConfig> = {}): TranslationSDK {
  const defaultConfig: TranslationSDKConfig = {
    defaultLocale: 'en',
    apiUrl: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400',
    projectId: process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'b90e383d-6e7d-4881-ba9a-c31649719348',
    generateLocales: process.env.GENERATE_LOCALES === 'true',
    debug: process.env.NODE_ENV === 'development',
  };

  return new TranslationSDK({ ...defaultConfig, ...config });
}

// Default SDK instance for simple usage
export const defaultSDK = createTranslationSDK();