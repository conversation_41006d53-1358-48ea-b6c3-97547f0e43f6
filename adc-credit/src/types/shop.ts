import { BaseEntity } from './base';
import { APIKey } from './auth';
import { ShopCustomer } from './customer';
import { CreditCode } from './credits';
import { ExternalUser } from './user';

// Unified Shop types
export interface Shop extends BaseEntity {
  slug: string;
  name: string;
  description: string;
  shop_type: 'retail' | 'api_service' | 'enterprise';
  contact_email: string;
  contact_phone: string;
  owner_user_id: string;
  image_url?: string;
  credit_balance?: number; // For customer-specific views
  branches?: ShopBranch[];
  customers?: ShopCustomer[];
  credit_codes?: CreditCode[];
  api_keys?: APIKey[];
}

export interface ShopBranch extends BaseEntity {
  shop_id: string;
  name: string;
  description: string;
  branch_type: 'location' | 'department' | 'division';
  contact_email: string;
  contact_phone: string;
  address: string;
  users?: ExternalUser[];
  api_keys?: APIKey[];
}

export interface CreateShop {
  name: string;
  description: string;
  shop_type: 'retail' | 'api_service' | 'enterprise';
  contact_email: string;
  contact_phone: string;
}

export interface UpdateShop {
  name?: string;
  description?: string;
  contact_email?: string;
  contact_phone?: string;
}

export interface CreateShopBranch {
  shop_id: string;
  name: string;
  description: string;
  branch_type: 'location' | 'department' | 'division';
  contact_email: string;
  contact_phone: string;
  address: string;
}

export interface ShopStats {
  total_customers: number;
  total_credit_codes: number;
  total_credits_distributed: number;
  active_customers: number;
  monthly_transactions: number;
}

export interface ShopExchangeRate extends BaseEntity {
  shop_id: string;
  currency_code: string;
  rate: number;
  is_active: boolean;
}

export interface CreateExchangeRate {
  currency_code: string;
  rate: number;
}

export interface UpdateExchangeRate {
  rate?: number;
  is_active?: boolean;
}

export interface CurrencyConversion {
  credits: number;
  currency_code: string;
  rate: number;
  local_currency_amount: number;
}