// Analytics types
export interface AnalyticsSummary {
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
  p95_response_time: number;
  p99_response_time: number;
  start_date: string;
  end_date: string;
  subscription_tier: string;
}

export interface AnalyticsTrend {
  period: string;
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
}

export interface EndpointAnalytics {
  endpoint: string;
  total_requests: number;
  total_credits: number;
  avg_response_time: number;
  error_rate: number;
  p95_response_time: number;
  p99_response_time: number;
}

export interface PerformanceMetrics {
  metric: string;
  data: {
    date: string;
    value: number;
  }[];
}

export interface DashboardAnalytics {
  overview: {
    total_api_calls: number;
    total_credits_used: number;
    active_api_keys: number;
    error_rate: number;
  };
  recent_activity: RecentActivity[];
  usage_trends: UsageTrend[];
  top_endpoints: TopEndpoint[];
}

export interface RecentActivity {
  id: string;
  type: 'api_call' | 'credit_add' | 'credit_use' | 'webhook_call';
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'pending';
}

export interface UsageTrend {
  date: string;
  api_calls: number;
  credits_used: number;
  response_time: number;
}

export interface TopEndpoint {
  endpoint: string;
  method: string;
  calls: number;
  credits: number;
  avg_response_time: number;
  error_rate: number;
}

export interface AnalyticsDateRange {
  start_date: string;
  end_date: string;
  period: 'hour' | 'day' | 'week' | 'month';
}

export interface AnalyticsFilter {
  api_key_id?: string;
  endpoint?: string;
  method?: string;
  status_code?: number;
  date_range: AnalyticsDateRange;
}