import { BaseEntity } from './base';
import { ExternalUser } from './user';

// Legacy organization types (for backward compatibility)
export interface Organization extends BaseEntity {
  name: string;
  slug: string;
  description: string;
  owner_user_id: string;
  branches?: Branch[];
}

export interface Branch extends BaseEntity {
  organization_id: string;
  name: string;
  description: string;
  users?: ExternalUser[];
}

export interface CreateOrganization {
  name: string;
  description: string;
}

export interface UpdateOrganization {
  name?: string;
  description?: string;
}

export interface CreateBranch {
  organization_id: string;
  name: string;
  description: string;
}

export interface UpdateBranch {
  name?: string;
  description?: string;
}

export interface OrganizationStats {
  total_branches: number;
  total_users: number;
  total_credits_allocated: number;
  active_users: number;
  monthly_usage: number;
}

export interface BranchStats {
  total_users: number;
  active_users: number;
  total_credits_used: number;
  monthly_usage: number;
}