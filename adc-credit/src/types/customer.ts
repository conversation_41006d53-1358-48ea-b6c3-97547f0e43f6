import { BaseEntity } from './base';

// Shop Customer types
export interface ShopCustomer extends BaseEntity {
  shop_id: string;
  user_id: string;
  phone?: string;
  credit_balance: number;
  user?: {
    name: string;
    email: string;
    phone?: string;
  };
}

export interface CreateShopCustomer {
  shop_id: string;
  user_id: string;
  phone?: string;
}

export interface UpdateShopCustomer {
  phone?: string;
}

export interface CustomerCreditOperation {
  shopId: string;
  customerId: string;
  amount: number;
  description?: string;
}

export interface CustomerTransactionHistory {
  customer_id: string;
  transactions: CustomerTransaction[];
  total_credits_earned: number;
  total_credits_used: number;
  current_balance: number;
}

export interface CustomerTransaction {
  id: string;
  type: 'credit_add' | 'credit_use' | 'credit_redeem';
  amount: number;
  description: string;
  created_at: string;
}

export interface CustomerSearchResult {
  customers: ShopCustomer[];
  total: number;
  page: number;
  has_next: boolean;
}