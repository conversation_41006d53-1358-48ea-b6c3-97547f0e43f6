import { User as NextA<PERSON><PERSON><PERSON>, DefaultSession } from "next-auth";

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    accessToken?: string;
    backendToken?: string;
    error?: string;
    user: {
      id?: string;
      name?: string;
      email?: string;
      image?: string;
      picture?: string;
      role?: string;
      provider?: string;
      accessToken?: string;
    } & DefaultSession["user"];
  }

  interface JWT {
    accessToken?: string;
    backendToken?: string;
    provider?: string;
    userId?: string;
    exchangeError?: string;
  }
}

// API Key types
export interface APIKey {
  id: string;
  user_id: string;
  name: string;
  key: string;
  last_used?: string;
  enabled: boolean;
  permissions: string[];
  rate_limit_max: number;
  rate_limit_rate: number;
  created_at: string;
  updated_at: string;
}

// Login/Register types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken?: string;
}