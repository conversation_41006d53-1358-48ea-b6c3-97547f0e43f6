import { BaseEntity } from './base';
import { Shop } from './shop';
import { ShopCustomer } from './customer';

// Subscription Tier types
export interface SubscriptionTier extends BaseEntity {
  id: number;
  name: string;
  description: string;
  price: number;
  credit_limit: number;
  features: string[];
  default_rate_limit_max: number;
  default_rate_limit_rate: number;
  max_webhooks: number;
  advanced_analytics: boolean;
}

// Subscription types
export interface Subscription extends BaseEntity {
  user_id: string;
  subscription_tier_id: number;
  subscription_tier: SubscriptionTier;
  start_date: string;
  end_date?: string;
  auto_renew: boolean;
  status: string;
  credit_balance: number;
  subscription_type: 'personal' | 'shop' | 'customer';
  shop_id?: string;
  shop?: Shop;
  shop_customer_id?: string;
  shop_customer?: ShopCustomer;
}

export interface CreateSubscription {
  subscription_tier_id: number;
  subscription_type: 'personal' | 'shop' | 'customer';
  shop_id?: string;
  shop_customer_id?: string;
  auto_renew?: boolean;
}

export interface UpdateSubscription {
  subscription_tier_id?: number;
  auto_renew?: boolean;
  status?: string;
}

export interface SubscriptionLimits {
  max_shops: number;
  max_customers_per_shop: number;
  max_api_keys: number;
  max_webhooks: number;
  max_monthly_credits: number;
  advanced_analytics: boolean;
  current_usage: {
    shops: number;
    customers: number;
    api_keys: number;
    webhooks: number;
    monthly_credits_used: number;
  };
}

export interface SubscriptionUsage {
  subscription_id: string;
  period_start: string;
  period_end: string;
  credits_used: number;
  credits_remaining: number;
  api_calls: number;
  overage_charges: number;
}