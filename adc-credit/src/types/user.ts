import { BaseEntity } from './base';
import { Subscription } from './subscription';

// User profile types
export interface UserProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
  role: string;
  created_at: string;
  updated_at: string;
}

export interface UpdateUserProfile {
  name?: string;
  picture?: string;
}

// External user types for organizations/branches
export interface ExternalUser extends BaseEntity {
  email: string;
  name: string;
  picture?: string;
  google_id?: string;
  role: string;
  organization_id: string;
  branch_id: string;
  monthly_credits: number;
  next_credit_reset_date: string;
  is_external_user: boolean;
  external_user_identifier: string;
  subscriptions?: Subscription[];
}

export interface ExternalUserCreditResponse {
  credit_balance: number;
  message: string;
}

export interface CreateExternalUser {
  email: string;
  name: string;
  role: string;
  organization_id: string;
  branch_id: string;
  monthly_credits: number;
  external_user_identifier: string;
}