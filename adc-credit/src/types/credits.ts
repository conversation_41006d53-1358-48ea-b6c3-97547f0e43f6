import { BaseEntity, TimePeriodSummary } from './base';

// Transaction types
export interface Transaction extends BaseEntity {
  user_id: string;
  subscription_id: string;
  type: "credit_add" | "credit_use" | "credit_scheduled";
  amount: number;
  description: string;
  reference: string;
}

// Credit Code types
export interface CreditCode extends BaseEntity {
  shop_id: string;
  code: string;
  amount: number;
  description: string;
  is_used: boolean;
  used_by_user_id?: string;
  expires_at?: string;
}

export interface CreateCreditCode {
  shop_id: string;
  amount: number;
  description: string;
  expires_at?: string;
}

export interface RedeemCreditCode {
  code: string;
  shop_id: string;
}

// Usage tracking types
export interface Usage extends BaseEntity {
  api_key_id: string;
  endpoint: string;
  method: string;
  credits: number;
  timestamp: string;
  success: boolean;
  ip_address: string;
  user_agent: string;
  response_time: number;
  status_code: number;
}

export interface UsageSummary {
  total_usage: number;
  total_credits: number;
  api_keys: APIKeySummary[];
  time_series: TimePeriodSummary[];
}

export interface APIKeySummary {
  id: string;
  name: string;
  total_usage: number;
  total_credits: number;
  endpoints: EndpointSummary[];
}

export interface EndpointSummary {
  endpoint: string;
  total_usage: number;
  total_credits: number;
}


// Webhook types
export interface Webhook extends BaseEntity {
  user_id: string;
  name: string;
  url: string;
  secret: string;
  events: string[];
  active: boolean;
  last_called?: string;
  fail_count: number;
}

export interface WebhookDelivery extends BaseEntity {
  webhook_id: string;
  event: string;
  payload: string;
  status_code: number;
  response: string;
  success: boolean;
  duration: number;
}

export interface CreateWebhook {
  name: string;
  url: string;
  events: string[];
  active?: boolean;
}

export interface UpdateWebhook {
  name?: string;
  url?: string;
  events?: string[];
  active?: boolean;
}

// Scheduled credit types
export interface ScheduledCredit extends BaseEntity {
  user_id: string;
  subscription_id: string;
  amount: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  next_run_at: string;
  is_active: boolean;
  description: string;
}

export interface CreateScheduledCredit {
  subscription_id: string;
  amount: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  description: string;
}