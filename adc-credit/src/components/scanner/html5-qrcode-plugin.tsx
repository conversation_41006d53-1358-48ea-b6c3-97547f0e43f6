"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { Html5Qrcode } from "html5-qrcode";
import { HTML5_QR_CODE_PLUGIN_TEST_IDS } from "@/lib/test-ids";

interface Html5QrcodePluginProps {
  fps?: number;
  qrbox?: number;
  aspectRatio?: number;
  disableFlip?: boolean;
  qrCodeSuccessCallback: (decodedText: string, decodedResult: any) => void;
  qrCodeErrorCallback?: (error: any) => void;
}

const qrcodeRegionId = "html5qr-code-full-region";

const Html5QrcodePlugin = ({
  fps = 10,
  qrbox = 250,
  aspectRatio = 1.0,
  disableFlip = false,
  qrCodeSuccessCallback,
  qrCodeErrorCallback,
}: Html5QrcodePluginProps) => {
  const html5QrCodeRef = useRef<Html5Qrcode | null>(null);
  const successCallbackRef = useRef(qrCodeSuccessCallback);
  const errorCallbackRef = useRef(qrCodeErrorCallback);
  const [isScanning, setIsScanning] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);

  // Update refs when callbacks change
  useEffect(() => {
    successCallbackRef.current = qrCodeSuccessCallback;
  }, [qrCodeSuccessCallback]);

  useEffect(() => {
    errorCallbackRef.current = qrCodeErrorCallback;
  }, [qrCodeErrorCallback]);

  // Stable callback functions
  const stableSuccessCallback = useCallback((decodedText: string, decodedResult: any) => {
    console.log("QR Code scanned successfully:", decodedText);
    successCallbackRef.current(decodedText, decodedResult);
  }, []);

  const stableErrorCallback = useCallback((error: any) => {
    // Filter out routine scanning errors that don't indicate actual problems
    const errorStr = error?.toString() || "";
    const isRoutineError = 
      errorStr.includes("No QR code found") ||
      errorStr.includes("Unable to detect QR code") ||
      errorStr.includes("QR code parse error") ||
      errorStr.includes("NotFoundException") ||
      errorStr.includes("No MultiFormat Readers");
      
    if (!isRoutineError) {
      console.error("QR Scanner error:", error);
      if (errorCallbackRef.current) {
        errorCallbackRef.current(error);
      }
    }
  }, []);

  useEffect(() => {
    const startScanner = async () => {
      try {
        setCameraError(null);
        setIsScanning(true);
        
        // Create instance of Html5Qrcode
        const html5QrCode = new Html5Qrcode(qrcodeRegionId);
        html5QrCodeRef.current = html5QrCode;

        // Enhanced configuration for better detection
        const config = {
          fps: Math.max(fps, 10), // Ensure minimum fps for better detection
          qrbox: {
            width: qrbox,
            height: qrbox
          },
          aspectRatio,
          disableFlip,
          // Better scanning configuration
          rememberLastUsedCamera: true,
          supportedScanTypes: ["QR_CODE", "DATA_MATRIX", "AZTEC", "PDF_417"],
        };
        
        // Try multiple camera configurations
        const cameraConfigs = [
          { facingMode: "environment" }, // Back camera preferred
          { facingMode: "user" }, // Front camera fallback
          "environment", // Legacy string format
          { facingMode: { exact: "environment" } }, // Exact match
        ];
        
        let scannerStarted = false;
        
        for (const cameraConfig of cameraConfigs) {
          try {
            await html5QrCode.start(
              cameraConfig,
              config,
              stableSuccessCallback,
              stableErrorCallback
            );
            scannerStarted = true;
            console.log("QR Scanner started successfully with config:", cameraConfig);
            break;
          } catch (err) {
            console.warn("Camera config failed:", cameraConfig, err);
          }
        }
        
        if (!scannerStarted) {
          throw new Error("Failed to start camera with any configuration");
        }
        
      } catch (err) {
        console.error("Error starting QR code scanner:", err);
        const errorMessage = err instanceof Error ? err.message : String(err);
        
        // Provide user-friendly error messages
        if (errorMessage.includes("Permission") || errorMessage.includes("NotAllowedError")) {
          setCameraError("Camera permission denied. Please allow camera access in your browser settings.");
        } else if (errorMessage.includes("NotFoundError") || errorMessage.includes("DevicesNotFoundError")) {
          setCameraError("No camera found. Please ensure your device has a working camera.");
        } else if (errorMessage.includes("NotReadableError")) {
          setCameraError("Camera is busy. Please close other apps using the camera and try again.");
        } else if (errorMessage.includes("OverconstrainedError")) {
          setCameraError("Camera settings not supported. Please try a different browser or device.");
        } else {
          setCameraError(`Scanner initialization failed: ${errorMessage}`);
        }
        
        setIsScanning(false);
      }
    };
    
    startScanner();

    // Cleanup on unmount
    return () => {
      if (html5QrCodeRef.current) {
        const scanner = html5QrCodeRef.current;
        if (scanner.isScanning) {
          scanner.stop().catch((err) => {
            console.error("Error stopping QR code scanner:", err);
          });
        }
      }
      setIsScanning(false);
    };
  }, [fps, qrbox, aspectRatio, disableFlip, stableSuccessCallback, stableErrorCallback]);

  // Show camera error if any
  if (cameraError) {
    return (
      <div className="w-full" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.CONTAINER}>
        <div className="flex flex-col items-center justify-center p-6 bg-red-50 border border-red-200 rounded-md min-h-[300px] text-center" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.ERROR_CONTAINER}>
          <div className="text-red-600 mb-3" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.ERROR_ICON}>
            <svg className="w-10 h-10 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-red-800 font-medium mb-2" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.ERROR_TITLE}>Camera Error</h3>
          <p className="text-red-700 text-sm mb-4" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.ERROR_MESSAGE}>{cameraError}</p>
          <button 
            onClick={() => {
              setCameraError(null);
              window.location.reload();
            }}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
            data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.TRY_AGAIN_BUTTON}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.CONTAINER}>
      <div 
        id={qrcodeRegionId} 
        className="w-full rounded-md overflow-hidden"
        style={{ 
          position: "relative",
          padding: "0",
          border: "2px solid #e5e7eb",
          minHeight: "300px",
          backgroundColor: isScanning ? "transparent" : "#f9fafb"
        }}
        data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.SCANNER_REGION}
      />
      <div className="text-center mt-3 space-y-1" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.STATUS_CONTAINER}>
        <p className="text-sm text-muted-foreground" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.STATUS_MESSAGE}>
          <span data-testid={isScanning ? HTML5_QR_CODE_PLUGIN_TEST_IDS.SCANNING_STATUS : HTML5_QR_CODE_PLUGIN_TEST_IDS.INITIALIZING_CAMERA}>
            {isScanning ? "Position QR code within the frame" : "Starting camera..."}
          </span>
        </p>
        <p className="text-xs text-muted-foreground" data-testid={HTML5_QR_CODE_PLUGIN_TEST_IDS.SUPPORTED_FORMATS_TEXT}>
          Supports QR codes and other 2D barcodes
        </p>
      </div>
    </div>
  );
};

export default Html5QrcodePlugin;
