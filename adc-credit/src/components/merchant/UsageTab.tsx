"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { QrCode, User, CurrencyDollar, TrendUp, TrendDown, Activity, CheckCircle, XCircle, Clock } from "phosphor-react";
import { useGetUsageSummaryQuery, useGetAnalyticsSummaryQuery, useGetUsageQuery } from "@/lib/api/apiSlice";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import { formatDistanceToNow } from "date-fns";
import { CREDIT_SERVICE_TEST_IDS } from "@/lib/test-ids";

export function UsageTab() {
  // Get current date range for last 30 days
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);

  // API queries
  const { data: usageSummary, isLoading: isUsageLoading, error: usageError } = useGetUsageSummaryQuery({
    period: 'month',
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
  });

  const { data: analytics, isLoading: isAnalyticsLoading } = useGetAnalyticsSummaryQuery({
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
  });

  const { data: recentUsage, isLoading: isRecentUsageLoading } = useGetUsageQuery({
    start_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Last 24 hours
    end_date: endDate.toISOString().split('T')[0],
  });

  const { data: subscriptionLimits, isLoading: isLimitsLoading } = useSubscriptionLimits();

  const isLoading = isUsageLoading || isAnalyticsLoading || isLimitsLoading;
  const hasError = usageError;

  return (
    <TabsContent value="usage" className="space-y-4" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.CONTAINER}>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-[#181510] flex items-center" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.TITLE}>
            <Activity className="mr-2 h-5 w-5" />
            Usage Analytics
          </CardTitle>
          <CardDescription data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.DESCRIPTION}>Track API usage, credits, and system activity over the last 30 days</CardDescription>
        </CardHeader>
        <CardContent>
          {hasError && (
            <Alert className="mb-6" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ERROR_ALERT}>
              <AlertDescription>
                Unable to load usage data. Please try refreshing the page.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Usage Summary */}
            <Card data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_USAGE_CARD}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">API Usage Summary</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_USAGE_LOADING}>
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-12" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_TOTAL_CALLS}>
                      <div className="flex items-center gap-2">
                        <Activity size={16} className="text-muted-foreground" />
                        <span className="text-sm">Total API Calls</span>
                      </div>
                      <span className="text-sm font-medium">{analytics?.total_requests || usageSummary?.total_usage || 0}</span>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_CREDITS_USED}>
                      <div className="flex items-center gap-2">
                        <CurrencyDollar size={16} className="text-muted-foreground" />
                        <span className="text-sm">Credits Used</span>
                      </div>
                      <span className="text-sm font-medium">{analytics?.total_credits || usageSummary?.total_credits || 0}</span>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_QR_CODES_USED}>
                      <div className="flex items-center gap-2">
                        <QrCode size={16} className="text-muted-foreground" />
                        <span className="text-sm">QR Codes Used</span>
                      </div>
                      <span className="text-sm font-medium">{subscriptionLimits?.limits.qr_codes_per_month.current || 0}</span>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.API_RESPONSE_TIME}>
                      <div className="flex items-center gap-2">
                        <Clock size={16} className="text-muted-foreground" />
                        <span className="text-sm">Avg Response Time</span>
                      </div>
                      <span className="text-sm font-medium">{analytics?.avg_response_time ? `${analytics.avg_response_time}ms` : 'N/A'}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Subscription Limits */}
            <Card data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.SUBSCRIPTION_CARD}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Subscription Usage</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.SUBSCRIPTION_LOADING}>
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-2 w-16 rounded-full" />
                          <Skeleton className="h-4 w-12" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.SHOPS_LIMIT}>
                      <span className="text-sm">Shops</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-muted rounded-full" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.SHOPS_PROGRESS}>
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ 
                              width: subscriptionLimits?.limits.shops.unlimited 
                                ? '20%' 
                                : `${Math.min((subscriptionLimits?.limits.shops.current || 0) / (subscriptionLimits?.limits.shops.max || 1) * 100, 100)}%` 
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {subscriptionLimits?.limits.shops.unlimited 
                            ? `${subscriptionLimits.limits.shops.current} / ∞`
                            : `${subscriptionLimits?.limits.shops.current || 0} / ${subscriptionLimits?.limits.shops.max || 0}`
                          }
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.QR_LIMIT}>
                      <span className="text-sm">QR Codes (Monthly)</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-muted rounded-full" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.QR_PROGRESS}>
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ 
                              width: subscriptionLimits?.limits.qr_codes_per_month.unlimited 
                                ? '20%' 
                                : `${Math.min((subscriptionLimits?.limits.qr_codes_per_month.current || 0) / (subscriptionLimits?.limits.qr_codes_per_month.max || 1) * 100, 100)}%` 
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {subscriptionLimits?.limits.qr_codes_per_month.unlimited 
                            ? `${subscriptionLimits.limits.qr_codes_per_month.current} / ∞`
                            : `${subscriptionLimits?.limits.qr_codes_per_month.current || 0} / ${subscriptionLimits?.limits.qr_codes_per_month.max || 0}`
                          }
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.WEBHOOKS_LIMIT}>
                      <span className="text-sm">Webhooks</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-muted rounded-full" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.WEBHOOKS_PROGRESS}>
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ 
                              width: `${Math.min((subscriptionLimits?.limits.webhooks.current || 0) / (subscriptionLimits?.limits.webhooks.max || 1) * 100, 100)}%` 
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {subscriptionLimits?.limits.webhooks.current || 0} / {subscriptionLimits?.limits.webhooks.max || 0}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.CREDITS_LIMIT}>
                      <span className="text-sm">Credits Balance</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-muted rounded-full" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.CREDITS_PROGRESS}>
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ 
                              width: `${Math.min((subscriptionLimits?.limits.credits.current || 0) / (subscriptionLimits?.limits.credits.limit || 1) * 100, 100)}%` 
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {subscriptionLimits?.limits.credits.current || 0} / {subscriptionLimits?.limits.credits.limit || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent API Activity */}
          <Card data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ACTIVITY_CARD}>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Recent API Activity (Last 24h)</CardTitle>
            </CardHeader>
            <CardContent>
              {isRecentUsageLoading ? (
                <div className="space-y-4" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ACTIVITY_LOADING}>
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                      <Skeleton className="w-2 h-2 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-48 mb-1" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentUsage && recentUsage.length > 0 ? (
                <div className="space-y-4 max-h-64 overflow-y-auto" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ACTIVITY_LIST}>
                  {recentUsage.slice(0, 10).map((usage) => {
                    const StatusIcon = usage.success ? CheckCircle : XCircle;
                    const statusColor = usage.success ? 'text-green-500' : 'text-red-500';
                    
                    return (
                      <div key={usage.id} className="flex items-center gap-3 p-3 bg-muted rounded-lg" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ACTIVITY_ITEM(usage.id)}>
                        <StatusIcon size={16} className={statusColor} />
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {usage.method} {usage.endpoint}
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>Status: {usage.status_code}</span>
                            <span>•</span>
                            <span>Credits: {usage.credits}</span>
                            <span>•</span>
                            <span>{formatDistanceToNow(new Date(usage.timestamp), { addSuffix: true })}</span>
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {usage.response_time}ms
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.ACTIVITY_EMPTY_STATE}>
                  <Activity size={48} className="text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">No recent API activity</p>
                  <p className="text-sm text-muted-foreground">API calls will appear here when you start using the service</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          {analytics && (
            <Card data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.PERFORMANCE_CARD}>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.PERFORMANCE_GRID}>
                  <div className="text-center" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.METRIC_AVG_RESPONSE}>
                    <div className="text-2xl font-bold text-foreground">{analytics.avg_response_time}ms</div>
                    <div className="text-sm text-muted-foreground">Avg Response</div>
                  </div>
                  <div className="text-center" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.METRIC_P95_RESPONSE}>
                    <div className="text-2xl font-bold text-foreground">{analytics.p95_response_time}ms</div>
                    <div className="text-sm text-muted-foreground">95th Percentile</div>
                  </div>
                  <div className="text-center" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.METRIC_ERROR_RATE}>
                    <div className="text-2xl font-bold text-foreground">{analytics.error_rate.toFixed(2)}%</div>
                    <div className="text-sm text-muted-foreground">Error Rate</div>
                  </div>
                  <div className="text-center" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.USAGE_TAB.METRIC_TOTAL_REQUESTS}>
                    <div className="text-2xl font-bold text-foreground">{analytics.total_requests}</div>
                    <div className="text-sm text-muted-foreground">Total Requests</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

        </CardContent>
      </Card>
    </TabsContent>
  );
}