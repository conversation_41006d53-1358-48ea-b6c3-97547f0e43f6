"use client";

import { Card, CardContent, CardDes<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { Plus, QrCode, CurrencyDollar, User } from "phosphor-react";
import Link from "next/link";
import { MERCHANT_DASHBOARD_TEST_IDS } from "@/lib/test-ids";

export function QuickActionsTab() {
  return (
    <TabsContent 
      value="quick-actions" 
      className="space-y-4"
      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.TAB_CONTENT}
    >
      <Card data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CARD}>
        <CardHeader 
          className="pb-2"
          data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.HEADER}
        >
          <CardTitle 
            className="text-[#181510] flex items-center"
            data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.TITLE}
          >
            <CurrencyDollar className="mr-2 h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.DESCRIPTION}>
            Common tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CONTENT}>
          <div 
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
            data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.GRID}
          >
            <Card 
              className="overflow-hidden hover:shadow-md transition-shadow"
              data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.MANAGE_CREDITS_CARD}
            >
              <Link 
                href="/dashboard/merchant/credits" 
                className="block p-4"
                data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.MANAGE_CREDITS_LINK}
              >
                <div className="flex items-center gap-4">
                  <div 
                    className="bg-[#e5ccb2] rounded-full p-3"
                    data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.MANAGE_CREDITS_ICON}
                  >
                    <CurrencyDollar size={24} className="text-[#181510]" />
                  </div>
                  <div>
                    <h3 
                      className="font-medium text-[#181510]"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.MANAGE_CREDITS_TITLE}
                    >
                      Manage Credits
                    </h3>
                    <p 
                      className="text-sm text-muted-foreground"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.MANAGE_CREDITS_DESCRIPTION}
                    >
                      View and manage customer credits
                    </p>
                  </div>
                </div>
              </Link>
            </Card>

            <Card 
              className="overflow-hidden hover:shadow-md transition-shadow"
              data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREDIT_CODES_CARD}
            >
              <Link 
                href="/dashboard/merchant/codes" 
                className="block p-4"
                data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREDIT_CODES_LINK}
              >
                <div className="flex items-center gap-4">
                  <div 
                    className="bg-[#e5ccb2] rounded-full p-3"
                    data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREDIT_CODES_ICON}
                  >
                    <QrCode size={24} className="text-[#181510]" />
                  </div>
                  <div>
                    <h3 
                      className="font-medium text-[#181510]"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREDIT_CODES_TITLE}
                    >
                      Credit Codes
                    </h3>
                    <p 
                      className="text-sm text-muted-foreground"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREDIT_CODES_DESCRIPTION}
                    >
                      Generate and manage credit codes
                    </p>
                  </div>
                </div>
              </Link>
            </Card>

            <Card 
              className="overflow-hidden hover:shadow-md transition-shadow"
              data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREATE_SHOP_CARD}
            >
              <Link 
                href="/dashboard/merchant/new" 
                className="block p-4"
                data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREATE_SHOP_LINK}
              >
                <div className="flex items-center gap-4">
                  <div 
                    className="bg-[#e5ccb2] rounded-full p-3"
                    data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREATE_SHOP_ICON}
                  >
                    <Plus size={24} className="text-[#181510]" />
                  </div>
                  <div>
                    <h3 
                      className="font-medium text-[#181510]"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREATE_SHOP_TITLE}
                    >
                      Create Shop
                    </h3>
                    <p 
                      className="text-sm text-muted-foreground"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.CREATE_SHOP_DESCRIPTION}
                    >
                      Add a new merchant shop
                    </p>
                  </div>
                </div>
              </Link>
            </Card>

            <Card 
              className="overflow-hidden hover:shadow-md transition-shadow"
              data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.PROFILE_CARD}
            >
              <Link 
                href="/dashboard/merchant/profile" 
                className="block p-4"
                data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.PROFILE_LINK}
              >
                <div className="flex items-center gap-4">
                  <div 
                    className="bg-[#e5ccb2] rounded-full p-3"
                    data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.PROFILE_ICON}
                  >
                    <User size={24} className="text-[#181510]" />
                  </div>
                  <div>
                    <h3 
                      className="font-medium text-[#181510]"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.PROFILE_TITLE}
                    >
                      Profile
                    </h3>
                    <p 
                      className="text-sm text-muted-foreground"
                      data-testid={MERCHANT_DASHBOARD_TEST_IDS.QUICK_ACTIONS.PROFILE_DESCRIPTION}
                    >
                      View and edit your profile
                    </p>
                  </div>
                </div>
              </Link>
            </Card>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  );
}