"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Di<PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { QrCode, Copy, Download, Share } from "phosphor-react";
import { useGenerateQRCodeMutation } from "@/lib/api/apiSlice";
import { CREDIT_SERVICE_TEST_IDS } from "@/lib/test-ids";

interface PaymentQRCodeProps {
  shopId: string;
  shopName: string;
}

export function PaymentQRCode({ shopId, shopName }: PaymentQRCodeProps) {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amount, setAmount] = useState(0);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  
  // Generate QR code mutation
  const [generateQRCode] = useGenerateQRCodeMutation();
  
  // Reset state when dialog closes
  useEffect(() => {
    if (!isDialogOpen) {
      setQrCodeUrl(null);
      setIsGenerating(false);
    }
  }, [isDialogOpen]);
  
  // Generate payment QR code
  const handleGenerateQRCode = async () => {
    if (amount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid amount greater than 0",
        variant: "destructive",
      });
      return;
    }
    
    setIsGenerating(true);
    
    try {
      // Generate a temporary code for this payment
      const tempCode = `pay-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      const result = await generateQRCode({
        shopId,
        code: tempCode,
        amount
      }).unwrap();
      
      setQrCodeUrl(result.qr_code);
    } catch (error) {
      console.error("Failed to generate QR code:", error);
      
      // Fallback to external service if backend fails
      const fallbackQrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=shop:${shopId};amount:${amount}`;
      setQrCodeUrl(fallbackQrUrl);
      
      toast({
        title: "Warning",
        description: "Using fallback QR code service. Some features may be limited.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Download QR code
  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    setIsDownloading(true);
    
    // Create a temporary link element
    const link = document.createElement("a");
    
    // If the QR code URL is a data URL (base64), we can download it directly
    if (qrCodeUrl.startsWith("data:")) {
      link.href = qrCodeUrl;
      link.download = `payment-qr-${shopName.replace(/\s+/g, '-').toLowerCase()}-${amount}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsDownloading(false);
    } else {
      // If it's a regular URL, we need to fetch it first
      fetch(qrCodeUrl)
        .then(response => response.blob())
        .then(blob => {
          const url = window.URL.createObjectURL(blob);
          link.href = url;
          link.download = `payment-qr-${shopName.replace(/\s+/g, '-').toLowerCase()}-${amount}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          setIsDownloading(false);
        })
        .catch(error => {
          console.error("Error downloading QR code:", error);
          toast({
            title: "Download failed",
            description: "Failed to download QR code image",
            variant: "destructive",
          });
          setIsDownloading(false);
        });
    }
  };
  
  // Share QR code
  const shareQRCode = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${shopName} Payment QR Code`,
          text: `Scan this QR code to pay ${amount} credits at ${shopName}`,
          url: window.location.href,
        });
        toast({
          title: "Shared successfully",
          description: "Payment QR code has been shared",
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      toast({
        title: "Share not supported",
        description: "Your browser doesn't support sharing. Try downloading the QR code instead.",
      });
    }
  };
  
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
          data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.TRIGGER_BUTTON}
        >
          <QrCode className="mr-2 h-5 w-5" />
          <span>Generate Payment QR</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.DIALOG_CONTAINER}>
        <DialogHeader>
          <DialogTitle>Payment QR Code</DialogTitle>
          <DialogDescription>
            Generate a QR code for customers to scan and pay with credits
          </DialogDescription>
        </DialogHeader>
        
        {!qrCodeUrl ? (
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="amount">Amount (Credits)</Label>
              <Input
                id="amount"
                type="number"
                min="1"
                value={amount || ""}
                onChange={(e) => setAmount(parseInt(e.target.value) || 0)}
                placeholder="Enter amount"
                className="bg-[#f1edea]"
                data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.AMOUNT_INPUT}
              />
              <p className="text-xs text-muted-foreground">
                Enter the amount of credits the customer will pay
              </p>
            </div>
            
            <Button
              onClick={handleGenerateQRCode}
              disabled={isGenerating || amount <= 0}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
              data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.GENERATE_BUTTON}
            >
              {isGenerating ? "Generating..." : "Generate QR Code"}
            </Button>
          </div>
        ) : (
          <div className="py-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">{amount} Credits</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <div className="relative w-64 h-64 mb-4" data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.QR_DISPLAY}>
                  <Image
                    src={qrCodeUrl}
                    alt={`Payment QR Code for ${amount} credits`}
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
                
                <p className="text-center text-muted-foreground mb-2">
                  Scan this QR code to pay {amount} credits
                </p>
                
                <p className="text-xs text-center text-muted-foreground">
                  This QR code is for one-time use only
                </p>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadQRCode}
                  disabled={isDownloading}
                  data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.DOWNLOAD_BUTTON}
                >
                  <Download size={16} className="mr-2" />
                  {isDownloading ? "Downloading..." : "Download"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={shareQRCode}
                  data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.SHARE_BUTTON}
                >
                  <Share size={16} className="mr-2" />
                  Share
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
            data-testid={CREDIT_SERVICE_TEST_IDS.MERCHANT.PAYMENT_QR.CANCEL_BUTTON}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
