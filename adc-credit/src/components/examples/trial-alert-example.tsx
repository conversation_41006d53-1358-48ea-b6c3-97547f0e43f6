'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useTrialAlert } from '@/hooks/useTrialAlert';

export function TrialAlertExample() {
  const { showAlert, isAlertVisible } = useTrialAlert();

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold">Manual Trial Alert Trigger</h3>
      <p className="text-sm text-muted-foreground">
        Use this to manually trigger the trial alert modal from anywhere in your app.
      </p>
      <Button 
        onClick={showAlert}
        disabled={isAlertVisible}
        variant="outline"
      >
        {isAlertVisible ? 'Alert Currently Showing' : 'Show Trial Alert'}
      </Button>
    </div>
  );
}