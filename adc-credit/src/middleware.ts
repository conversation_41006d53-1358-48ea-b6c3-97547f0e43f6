import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { v4 as uuidv4 } from 'uuid';

// Helper function to check if a string is a UUID
function isUUID(str: string): boolean {
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidPattern.test(str);
}

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;
  
  // Add correlation ID to all requests
  const response = NextResponse.next();
  
  // Get correlation ID from request or generate new one
  let correlationId = request.headers.get('X-Correlation-ID');
  if (!correlationId) {
    correlationId = uuidv4();
  }
  
  // Add correlation ID to response headers
  response.headers.set('X-Correlation-ID', correlationId);

  // Handle redirects for old ID-based URLs to new slug-based URLs
  const idRedirectPatterns = [
    /^\/dashboard\/customer\/shops\/([a-f0-9-]{36})$/,
    /^\/dashboard\/merchant\/([a-f0-9-]{36})$/,
    /^\/dashboard\/shops\/([a-f0-9-]{36})$/
  ];

  for (const pattern of idRedirectPatterns) {
    const match = path.match(pattern);
    if (match && match[1] && isUUID(match[1])) {
      // Redirect to the base shops/merchant page since we can't resolve ID to slug in middleware
      const redirectResponse = path.startsWith('/dashboard/customer/shops/') 
        ? NextResponse.redirect(new URL('/dashboard/customer/shops', request.url))
        : path.startsWith('/dashboard/merchant/')
        ? NextResponse.redirect(new URL('/dashboard/merchant', request.url))
        : NextResponse.redirect(new URL('/dashboard/shops', request.url));
      
      // Add correlation ID to redirect response
      redirectResponse.headers.set('X-Correlation-ID', correlationId);
      return redirectResponse;
    }
  }

  // Check if the path is for API routes that need authentication
  const isApiRoute = path.startsWith('/api/v1') &&
                    !path.startsWith('/api/v1/auth') &&
                    !path.startsWith('/api/v1/health');

  if (isApiRoute) {
    // Get the token from the request using NextAuth.js
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    // If no token and this is an API route that requires authentication
    if (!token) {
      // Respond with JSON indicating an error
      const errorResponse = new NextResponse(
        JSON.stringify({ error: 'Authentication required', correlationId }),
        {
          status: 401,
          headers: { 
            'content-type': 'application/json',
            'X-Correlation-ID': correlationId
          }
        }
      );
      return errorResponse;
    }

    // Add the token to the request headers for the backend
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('X-Correlation-ID', correlationId);

    // Check if token has accessToken property
    if (token.accessToken) {
      requestHeaders.set('Authorization', `Bearer ${token.accessToken}`);
    } else {
      console.error('No access token found in token object:', token);
      // Return 401 if no access token is found
      return new NextResponse(
        JSON.stringify({ error: 'No access token found', correlationId }),
        {
          status: 401,
          headers: { 
            'content-type': 'application/json',
            'X-Correlation-ID': correlationId
          }
        }
      );
    }

    // Return the request with the modified headers
    const apiResponse = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
    
    // Add correlation ID to response
    apiResponse.headers.set('X-Correlation-ID', correlationId);
    return apiResponse;
  }

  // Continue for non-API routes or public API routes
  return response;
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    // Match all API routes
    '/api/:path*',
    // Match dashboard routes for redirects
    '/dashboard/:path*',
  ],
};
