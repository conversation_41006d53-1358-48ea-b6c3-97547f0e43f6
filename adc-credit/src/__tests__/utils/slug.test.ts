import { 
  isValidSlug, 
  isUUID, 
  getSlugErrorMessage, 
  sanitizeSlug, 
  generateSlugFromName 
} from '@/lib/utils';

describe('Slug Utilities', () => {
  // Test real-world slug scenarios
  describe('Real-world slug scenarios', () => {
    it('should handle common shop names correctly', () => {
      const realShopNames = [
        { input: "Joe's Coffee Shop", expected: 'joes-coffee-shop' },
        { input: "McDonald's Restaurant", expected: 'mcdonalds-restaurant' },
        { input: "7-Eleven Store", expected: '7-eleven-store' },
        { input: "H&M Fashion", expected: 'hm-fashion' },
        { input: "AT&T Store", expected: 'att-store' },
        { input: "Best Buy Electronics", expected: 'best-buy-electronics' },
        { input: "Walmart Supercenter #1234", expected: 'walmart-supercenter-1234' },
      ];

      realShopNames.forEach(({ input, expected }) => {
        expect(sanitizeSlug(input)).toBe(expected);
      });
    });

    it('should handle international characters', () => {
      const internationalNames = [
        { input: "Café Français", expected: 'caf-franais' },
        { input: "München Bakery", expected: 'mnchen-bakery' },
        { input: "José's Tacos", expected: 'joss-tacos' },
        { input: "北京 Restaurant", expected: 'restaurant' },
      ];

      internationalNames.forEach(({ input, expected }) => {
        expect(sanitizeSlug(input)).toBe(expected);
      });
    });
  });
  describe('isValidSlug', () => {
    it('should return true for valid slugs', () => {
      expect(isValidSlug('my-shop')).toBe(true);
      expect(isValidSlug('shop123')).toBe(true);
      expect(isValidSlug('my-coffee-shop-123')).toBe(true);
      expect(isValidSlug('a')).toBe(false); // Too short
      expect(isValidSlug('ab')).toBe(true); // Minimum length
    });

    it('should return false for invalid slugs', () => {
      expect(isValidSlug('')).toBe(false);
      expect(isValidSlug('My-Shop')).toBe(false); // Uppercase
      expect(isValidSlug('my_shop')).toBe(false); // Underscore
      expect(isValidSlug('my shop')).toBe(false); // Space
      expect(isValidSlug('my-shop-')).toBe(false); // Trailing hyphen
      expect(isValidSlug('-my-shop')).toBe(false); // Leading hyphen
      expect(isValidSlug('my--shop')).toBe(false); // Double hyphen
      expect(isValidSlug('shop@123')).toBe(false); // Special character
    });

    it('should handle edge cases', () => {
      expect(isValidSlug(null as any)).toBe(false);
      expect(isValidSlug(undefined as any)).toBe(false);
      expect(isValidSlug(123 as any)).toBe(false);
      expect(isValidSlug('a'.repeat(101))).toBe(false); // Too long
    });
  });

  describe('isUUID', () => {
    it('should return true for valid UUIDs', () => {
      expect(isUUID('123e4567-e89b-12d3-a456-************')).toBe(true);
      expect(isUUID('550e8400-e29b-41d4-a716-************')).toBe(true);
      expect(isUUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')).toBe(true);
    });

    it('should return false for invalid UUIDs', () => {
      expect(isUUID('not-a-uuid')).toBe(false);
      expect(isUUID('123e4567-e89b-12d3-a456')).toBe(false); // Too short
      expect(isUUID('123e4567-e89b-12d3-a456-************-extra')).toBe(false); // Too long
      expect(isUUID('')).toBe(false);
      expect(isUUID('my-shop-slug')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(isUUID(null as any)).toBe(false);
      expect(isUUID(undefined as any)).toBe(false);
      expect(isUUID(123 as any)).toBe(false);
    });
  });

  describe('getSlugErrorMessage', () => {
    it('should return appropriate error messages', () => {
      expect(getSlugErrorMessage('')).toBe('Shop identifier is missing');
      expect(getSlugErrorMessage('123e4567-e89b-12d3-a456-************'))
        .toBe('This URL format is no longer supported. Please use the updated shop link.');
      expect(getSlugErrorMessage('Invalid_Slug'))
        .toBe('"Invalid_Slug" is not a valid shop identifier');
      expect(getSlugErrorMessage('valid-slug')).toBe('Shop not found');
    });
  });

  describe('sanitizeSlug', () => {
    it('should sanitize input to valid slug format', () => {
      expect(sanitizeSlug('My Coffee Shop!')).toBe('my-coffee-shop');
      expect(sanitizeSlug('  Shop   123  ')).toBe('shop-123');
      expect(sanitizeSlug('shop@#$%^&*()123')).toBe('shop123');
      expect(sanitizeSlug('Multiple---Hyphens')).toBe('multiple-hyphens');
      expect(sanitizeSlug('-Leading-And-Trailing-')).toBe('leading-and-trailing');
    });

    it('should handle edge cases', () => {
      expect(sanitizeSlug('')).toBe('');
      expect(sanitizeSlug('   ')).toBe('');
      expect(sanitizeSlug('@#$%^&*()')).toBe('');
      expect(sanitizeSlug(null as any)).toBe('');
      expect(sanitizeSlug(undefined as any)).toBe('');
    });
  });

  describe('generateSlugFromName', () => {
    it('should generate unique slugs', () => {
      expect(generateSlugFromName('My Shop')).toBe('my-shop');
      expect(generateSlugFromName('My Shop', ['my-shop'])).toBe('my-shop-1');
      expect(generateSlugFromName('My Shop', ['my-shop', 'my-shop-1'])).toBe('my-shop-2');
    });

    it('should handle empty or invalid names', () => {
      expect(generateSlugFromName('')).toBe('shop');
      expect(generateSlugFromName('@#$%')).toBe('shop');
      expect(generateSlugFromName('   ')).toBe('shop');
    });

    it('should handle collision detection', () => {
      const existingSlugs = ['coffee-shop', 'coffee-shop-1', 'coffee-shop-2'];
      expect(generateSlugFromName('Coffee Shop', existingSlugs)).toBe('coffee-shop-3');
    });
  });
});