/**
 * Integration tests for slug migration functionality
 * Tests the new slug-based routing and redirect behavior
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('Slug Migration Integration Tests', () => {
  let authToken: string;
  let testUserId: string;
  let testShop: any;

  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
    
    // Setup authentication
    const email = TestDataGenerators.generateEmail();
    
    const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email,
        password: 'testpassword123',
        name: 'Slug Test User',
      }),
    });

    if (registerResponse.ok) {
      const userData = await registerResponse.json();
      authToken = userData.access_token;
      testUserId = userData.user.id;

      // Create a test shop for slug testing
      const shopData = {
        name: 'Test Shop for Slugs',
        description: 'A shop for testing slug functionality',
        shop_type: 'retail',
        contact_email: '<EMAIL>',
        contact_phone: '+1234567890',
      };

      const shopResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shopData),
      });

      if (shopResponse.ok) {
        testShop = await shopResponse.json();
      }
    }
  });

  describe('Slug-based API endpoints', () => {
    it('GET /api/v1/shops/slug/:slug should return shop by slug', async () => {
      if (!testShop?.slug) return;

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/slug/${testShop.slug}`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.id).toBe(testShop.id);
        expect(data.slug).toBe(testShop.slug);
        expect(data.name).toBe(testShop.name);
      } else {
        // May return 401 if shop operations require subscription
        expect(response.status).not.toBe(500);
      }
    });

    it('GET /api/v1/shops/slug/:slug should return 404 for non-existent slug', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/slug/non-existent-shop`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.status === 404) {
        const data = await response.json();
        expect(data.error).toBeDefined();
      } else {
        // May return 401 if shop operations require subscription
        expect([401, 403]).toContain(response.status);
      }
    });

    it('should handle invalid slug formats gracefully', async () => {
      const invalidSlugs = [
        'Invalid_Slug',
        'slug with spaces',
        'UPPERCASE-SLUG',
        'slug@with#special!chars',
        '-leading-hyphen',
        'trailing-hyphen-',
        'double--hyphens'
      ];

      for (const invalidSlug of invalidSlugs) {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/slug/${invalidSlug}`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        // Should return 404 for invalid slugs, or 401/403 for auth issues
        expect([401, 403, 404]).toContain(response.status);
      }
    });
  });

  describe('Customer shop endpoints with slugs', () => {
    it('GET /api/v1/customer/shops should include slug in response', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/customer/shops`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        expect(Array.isArray(data)).toBe(true);
        
        if (data.length > 0) {
          const shop = data.find((s: any) => s.id === testShop?.id);
          if (shop) {
            expect(shop.slug).toBeDefined();
            expect(typeof shop.slug).toBe('string');
            expect(shop.slug.length).toBeGreaterThan(0);
          }
        }
      } else {
        // May return 401 if customer operations require subscription
        expect([401, 403]).toContain(response.status);
      }
    });
  });

  describe('Slug validation', () => {
    it('should generate valid slugs for shop names', () => {
      const testCases = [
        { name: 'My Coffee Shop', expectedPattern: /^my-coffee-shop$/ },
        { name: 'Test Shop 123', expectedPattern: /^test-shop-123$/ },
        { name: 'Shop with Special@#$%Characters!', expectedPattern: /^shop-with-specialcharacters$/ },
        { name: '  Leading and Trailing Spaces  ', expectedPattern: /^leading-and-trailing-spaces$/ },
        { name: 'Multiple---Hyphens', expectedPattern: /^multiple-hyphens$/ },
      ];

      // This would typically be tested in the backend, but we can verify the pattern
      testCases.forEach(({ name, expectedPattern }) => {
        const slug = name
          .toLowerCase()
          .trim()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        expect(slug).toMatch(expectedPattern);
      });
    });

    it('should ensure slug uniqueness in the database', async () => {
      if (!authToken) return;

      // Try to create two shops with the same name (which would generate the same slug)
      const shopName = 'Duplicate Name Test Shop';
      
      const shop1Data = {
        name: shopName,
        description: 'First shop with this name',
        shop_type: 'retail',
        contact_email: '<EMAIL>',
        contact_phone: '+1234567890',
      };

      const shop2Data = {
        name: shopName,
        description: 'Second shop with this name',
        shop_type: 'retail',  
        contact_email: '<EMAIL>',
        contact_phone: '+1234567891',
      };

      const response1 = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shop1Data),
      });

      const response2 = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shop2Data),
      });

      if (response1.ok && response2.ok) {
        const shop1 = await response1.json();
        const shop2 = await response2.json();

        // Slugs should be different (second one should have a suffix)
        expect(shop1.slug).not.toBe(shop2.slug);
        expect(shop1.slug).toBeDefined();
        expect(shop2.slug).toBeDefined();
        
        // Second slug should be the first slug with a suffix
        if (shop1.slug === 'duplicate-name-test-shop') {
          expect(shop2.slug).toMatch(/^duplicate-name-test-shop-\d+$/);
        }
      } else {
        // May fail due to subscription limits or auth issues
        console.log('Slug uniqueness test skipped due to API constraints');
      }
    });
  });

  describe('Backend slug endpoint coverage', () => {
    it('should have all expected slug endpoints available', async () => {
      const slugEndpoints = [
        `/api/v1/shops/slug/${testShop?.slug || 'test-slug'}`,
        // Add more slug endpoints as they become available
      ];

      for (const endpoint of slugEndpoints) {
        const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(500);
        
        if (response.status === 404) {
          console.warn(`Slug endpoint not found: ${endpoint}`);
        }
      }
    });
  });
});