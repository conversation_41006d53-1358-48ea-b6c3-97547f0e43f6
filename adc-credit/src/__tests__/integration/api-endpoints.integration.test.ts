/**
 * Comprehensive Integration Tests for All Backend API Endpoints
 * Direct HTTP requests to test all API endpoints systematically
 * 
 * Prerequisites:
 * - Backend server running on port 8100 (use `make backend` or `make dev`)
 * - PostgreSQL database available and migrated
 * - Environment variables configured (see jest.env.js)
 * 
 * Note: These tests use direct HTTP calls to the backend API,
 * not the frontend proxy, to test the actual backend endpoints.
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('Backend API Endpoints Integration Tests', () => {
  let authToken: string;
  let testUserEmail: string;
  let testUserId: string;

  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
    
    // Setup authentication for protected endpoints
    testUserEmail = TestDataGenerators.generateEmail();
    
    try {
      // Try to register a test user
      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUserEmail,
          password: 'testpassword123',
          name: 'Test User',
        }),
      });

      if (registerResponse.ok) {
        const registerData = await registerResponse.json();
        authToken = registerData.token;
        testUserId = registerData.user?.id;
      } else {
        // Try to login if user already exists
        const loginResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testUserEmail,
            password: 'testpassword123',
          }),
        });

        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          authToken = loginData.token;
          testUserId = loginData.user?.id;
        }
      }
    } catch (error) {
      console.warn('Authentication setup failed:', error);
      // Continue with tests - some will be skipped
    }
  });

  describe('Public Endpoints (No Authentication Required)', () => {
    describe('Health Check', () => {
      it('GET /api/v1/health should return health status', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
        
        expect(response.ok).toBe(true);
        expect(response.status).toBe(200);
        
        const data = await response.json();
        expect(data).toBeDefined();
      });
    });

    describe('Authentication Endpoints', () => {
      it('POST /api/v1/auth/register should register new user', async () => {
        const email = TestDataGenerators.generateEmail();
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email,
            password: 'testpassword123',
            name: 'New Test User',
          }),
        });

        expect(response.ok).toBe(true);
        const data = await response.json();
        expect(data.user).toBeDefined();
        expect(data.user.email).toBe(email);
        expect(data.token).toBeDefined();
      });

      it('POST /api/v1/auth/login should authenticate user', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testUserEmail,
            password: 'testpassword123',
          }),
        });

        expect(response.ok).toBe(true);
        const data = await response.json();
        expect(data.user).toBeDefined();
        expect(data.token).toBeDefined();
      });

      it('POST /api/v1/auth/google should exist (even if not configured)', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/google`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: 'mock-google-token',
          }),
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(404);
        expect(response.status).not.toBe(500);
      });

      it('POST /api/v1/auth/forgot-password should accept email', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/forgot-password`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testUserEmail,
          }),
        });

        // Should return success for security (even if email doesn't exist) or validation error
        expect([200, 400, 404]).toContain(response.status);
        expect(response.status).not.toBe(500); // Should not crash
      });

      it('POST /api/v1/auth/reset-password should exist', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/reset-password`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: 'mock-reset-token',
            password: 'newpassword123',
          }),
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(404);
        expect(response.status).not.toBe(500);
      });
    });

    describe('Public Information', () => {
      it('GET /api/v1/public/subscriptions/tiers should return subscription tiers', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/public/subscriptions/tiers`);
        
        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          // Endpoint might not be implemented yet or requires seeding
          expect([404, 500]).toContain(response.status);
        }
      });
    });

    describe('External API Endpoints', () => {
      it('POST /api/v1/external/verify should check API key', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/verify`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
          },
          body: JSON.stringify({}),
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(404);
        expect(response.status).not.toBe(500);
      });

      it('POST /api/v1/external/consume should handle credit consumption', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/consume`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
          },
          body: JSON.stringify({
            amount: 10,
            description: 'Test consumption',
          }),
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(404);
        expect(response.status).not.toBe(500);
      });
    });

    describe('Stripe Webhooks', () => {
      it('POST /webhook/stripe should exist', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/webhook/stripe`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'test.event',
            data: {},
          }),
        });

        // Should not return 404 (endpoint exists) or 500 (server error)
        expect(response.status).not.toBe(404);
        expect(response.status).not.toBe(500);
      });
    });
  });

  describe('Protected Endpoints (Require Authentication)', () => {
    beforeEach(() => {
      if (!authToken) {
        console.warn('Authentication setup failed - skipping protected endpoint tests');
        pending('Authentication token not available');
      }
    });

    describe('User Management', () => {
      it('GET /api/v1/users/me should return current user', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.ok).toBe(true);
        const data = await response.json();
        expect(data.id).toBeDefined();
        expect(data.email).toBe(testUserEmail);
      });

      it('PUT /api/v1/users/me should update user profile', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Updated Test User',
          }),
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.name).toBe('Updated Test User');
        } else {
          // Check if it's a validation error rather than auth error
          expect(response.status).not.toBe(500); // Should not crash
        }
      });
    });

    describe('API Key Management', () => {
      it('GET /api/v1/apikeys should return user API keys', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/apikeys`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          // Endpoint might not be implemented yet
          expect([404, 500]).toContain(response.status);
        }
      });

      it('POST /api/v1/apikeys should create API key', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/apikeys`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test API Key',
            permissions: ['read', 'write'],
          }),
        });

        // Should not return 401 (authentication works)
        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Credit Management', () => {
      it('GET /api/v1/credits should return credit balance', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          // API might return different structure - check actual response
          expect(data).toBeDefined();
          if (data.balance !== undefined) {
            expect(typeof data.balance).toBe('number');
          }
        } else {
          expect(response.status).not.toBe(500); // Should not crash
        }
      });

      it('POST /api/v1/credits/add should add credits', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/add`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: 100,
            description: 'Test credit addition',
          }),
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/credits/transactions should return transaction history', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/transactions`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Usage Statistics', () => {
      it('GET /api/v1/usage should return usage data', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/usage/summary should return usage summary', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage/summary`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Subscription Management', () => {
      it('GET /api/v1/subscriptions should return user subscriptions', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/subscriptions/active should return active subscription', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions/active`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/subscriptions/tiers should return subscription tiers', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions/tiers`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('POST /api/v1/subscriptions should create subscription', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tier_id: 'basic',
            subscription_type: 'personal',
          }),
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Subscription Limits', () => {
      it('GET /api/v1/subscription-limits should return all limits', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        // Subscription limits endpoints might not be implemented yet
        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/subscription-limits/shops/check should check shop creation limit', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shops/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/subscription-limits/qr-codes/check should check QR code limit', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/qr-codes/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Shop Management (Unified System)', () => {
      let testShopId: string;

      it('GET /api/v1/shops should return user shops', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect(response.status).not.toBe(500); // Should not crash
        }
      });

      it('POST /api/v1/shops should create shop', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: TestDataGenerators.generateShopName(),
            description: 'Test shop for integration testing',
            shop_type: 'retail',
            contact_email: '<EMAIL>',
            contact_phone: '+1234567890',
          }),
        });

        if (response.ok) {
          const data = await response.json();
          testShopId = data.id;
          expect(data.name).toBeDefined();
          expect(data.id).toBeDefined();
        } else {
          // Shop creation might fail due to subscription limits or validation
          expect(response.status).not.toBe(500); // Should not crash
        }
      });

      it('GET /api/v1/shops/:id should get specific shop', async () => {
        if (!testShopId) {
          pending('Test shop not created');
          return;
        }

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${testShopId}`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('PUT /api/v1/shops/:id should update shop', async () => {
        if (!testShopId) {
          pending('Test shop not created');
          return;
        }

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${testShopId}`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Updated Test Shop',
          }),
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('GET /api/v1/shops/:id/stats should get shop statistics', async () => {
        if (!testShopId) {
          pending('Test shop not created');
          return;
        }

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${testShopId}/stats`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Webhook Management', () => {
      it('GET /api/v1/webhooks should return user webhooks', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/webhooks`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('POST /api/v1/webhooks should create webhook', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/webhooks`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: 'https://example.com/webhook',
            events: ['credit.added', 'credit.used'],
            name: 'Test Webhook',
          }),
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });

    describe('Customer Endpoints', () => {
      it('GET /api/v1/customer/shops should return customer shops', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/customer/shops`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(500); // Should not crash
      });

      it('POST /api/v1/customer/redeem-code should redeem credit code', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/customer/redeem-code`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code: 'test-credit-code',
          }),
        });

        expect(response.status).not.toBe(500); // Should not crash
      });
    });
  });

  describe('Authentication Requirements', () => {
    it('should reject requests without authentication', async () => {
      const protectedEndpoints = [
        '/api/v1/users/me',
        '/api/v1/apikeys',
        '/api/v1/credits',
        '/api/v1/subscriptions',
        '/api/v1/shops',
        '/api/v1/webhooks',
      ];

      for (const endpoint of protectedEndpoints) {
        const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}`);
        expect(response.status).toBe(401);
      }
    });

    it('should reject requests with invalid tokens', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': 'Bearer invalid-token' },
      });

      expect(response.status).toBe(401);
    });

    it('should reject requests with malformed Authorization header', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': 'Invalid Header Format' },
      });

      expect(response.status).toBe(401);
    });
  });

  describe('HTTP Methods and CORS', () => {
    it('should support CORS preflight requests', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`, {
        method: 'OPTIONS',
      });

      expect([200, 204]).toContain(response.status);
    });

    it('should return 405 for unsupported methods', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`, {
        method: 'DELETE',
      });

      // Backend might return 404 or 405 for unsupported methods
      expect([404, 405]).toContain(response.status);
    });

    it('should include proper CORS headers', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
      
      // Check for CORS headers (may vary based on configuration)
      const corsHeader = response.headers.get('access-control-allow-origin');
      if (corsHeader) {
        expect(corsHeader).toBeDefined();
      }
    });
  });

  describe('Error Handling', () => {
    it('should return JSON error responses', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/nonexistent-endpoint`);
      
      expect(response.status).toBe(404);
      
      // Backend might return text/plain or application/json for 404s
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        expect(data.error).toBeDefined();
      } else {
        // If it's plain text, just check it's not empty
        const text = await response.text();
        expect(text.length).toBeGreaterThan(0);
      }
    });

    it('should handle malformed JSON in request body', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json',
      });

      expect(response.status).toBe(400);
    });

    it('should validate Content-Type for POST requests', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'text/plain' },
        body: JSON.stringify({ email: '<EMAIL>', password: 'test' }),
      });

      // Backend might accept different content types or return 401 for invalid credentials
      expect([400, 401, 415]).toContain(response.status);
    });
  });

  // Cleanup
  afterAll(async () => {
    // Clean up test data if needed
    if (authToken && testUserId) {
      try {
        // Note: Admin endpoints may not be available in all environments
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${testUserId}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${authToken}` },
        });
        
        if (!response.ok && response.status !== 404) {
          console.warn(`Cleanup response status: ${response.status}`);
        }
      } catch (error) {
        console.warn('Failed to cleanup test user:', error);
      }
    }
  });
});