/**
 * Debug authentication integration tests
 * Tests auth endpoints directly to debug the unmarshal issue
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('Authentication Debug Tests', () => {
  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
  });

  describe('Direct Backend Auth Tests', () => {
    it('should register a user with direct backend call', async () => {
      const testUser = {
        email: TestDataGenerators.generateEmail(),
        password: 'testpassword123',
        name: 'Debug Test User',
      };

      console.log('Sending registration request:', testUser);

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testUser),
      });

      console.log('Response status:', response.status);
      
      const responseText = await response.text();
      console.log('Response body:', responseText);

      if (response.ok) {
        const data = JSON.parse(responseText);
        expect(data.token).toBeDefined();
        expect(data.user).toBeDefined();
        expect(data.user.email).toBe(testUser.email);
      } else {
        console.error('Registration failed with status:', response.status);
        console.error('Error response:', responseText);
        
        // Even if it fails, we want to see the actual error
        expect(response.status).not.toBe(500); // Should not be server error
      }
    });

    it('should test different JSON formats', async () => {
      const testCases = [
        {
          name: 'Standard format',
          data: {
            email: '<EMAIL>',
            password: 'testpassword123',
            name: 'Test User 1',
          },
        },
        {
          name: 'String format (sanity check)',
          data: JSON.stringify({
            email: '<EMAIL>',
            password: 'testpassword123',
            name: 'Test User 2',
          }),
        },
      ];

      for (const testCase of testCases) {
        console.log(`\nTesting ${testCase.name}:`);
        console.log('Data:', testCase.data);

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: typeof testCase.data === 'string' ? testCase.data : JSON.stringify(testCase.data),
        });

        console.log(`${testCase.name} - Status:`, response.status);
        
        const responseText = await response.text();
        console.log(`${testCase.name} - Response:`, responseText.substring(0, 200));

        if (!response.ok) {
          console.error(`${testCase.name} failed:`, responseText);
        }
      }

      // This test is for debugging, so we don't fail it
      expect(true).toBe(true);
    });

    it('should test login with different formats', async () => {
      // First register a user
      const testUser = {
        email: TestDataGenerators.generateEmail(),
        password: 'testpassword123',
        name: 'Login Test User',
      };

      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testUser),
      });

      if (registerResponse.ok) {
        // Now try to login
        const loginData = {
          email: testUser.email,
          password: testUser.password,
        };

        console.log('Sending login request:', loginData);

        const loginResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(loginData),
        });

        console.log('Login response status:', loginResponse.status);
        
        const loginResponseText = await loginResponse.text();
        console.log('Login response body:', loginResponseText);

        if (loginResponse.ok) {
          const data = JSON.parse(loginResponseText);
          expect(data.token).toBeDefined();
          expect(data.user).toBeDefined();
        } else {
          console.error('Login failed:', loginResponseText);
        }
      } else {
        console.log('Registration failed, skipping login test');
      }

      expect(true).toBe(true); // Debug test
    });

    it('should check backend health', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
      console.log('Health check status:', response.status);
      
      const data = await response.text();
      console.log('Health response:', data);
      
      expect(response.ok).toBe(true);
    });

    it('should test with curl-like request', async () => {
      const testUser = {
        email: '<EMAIL>',
        password: 'testpassword123',
        name: 'Curl Test User',
      };

      // Simulate exactly what curl would send
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'integration-test/1.0',
        },
        body: JSON.stringify(testUser),
      });

      console.log('Curl-like request status:', response.status);
      console.log('Request headers:', {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });
      console.log('Request body:', JSON.stringify(testUser));

      const responseText = await response.text();
      console.log('Curl-like response:', responseText);

      expect(response.status).not.toBe(500);
    });
  });
});