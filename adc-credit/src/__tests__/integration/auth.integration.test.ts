/**
 * Integration tests for Authentication API endpoints using direct HTTP calls
 * Tests all auth-related endpoints including registration, login, token refresh, and password reset
 */

import { APITestHelpers, APIAssertions, TestDataGenerators, TEST_CONFIG } from './api-test-utils';

describe('Authentication API Integration Tests', () => {
  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
  });

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const email = TestDataGenerators.generateEmail();
      const password = 'testpassword123';
      const name = 'Test User';

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, name }),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data.user).toBeDefined();
      expect(data.user.email).toBe(email);
      expect(data.user.name).toBe(name);
      expect(data.token).toBeDefined();
      APIAssertions.assertValidUUID(data.user.id);
      
      // Should not return password
      expect(data.user.password).toBeUndefined();
    });

    it('should fail with duplicate email', async () => {
      const userData = {
        email: TestDataGenerators.generateEmail(),
        password: 'testpassword123',
        name: 'Test User',
      };

      // Register first user
      const firstResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      expect(firstResponse.ok).toBe(true);

      // Try to register with same email
      const secondResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      expect(secondResponse.ok).toBe(false);
      const errorData = await secondResponse.json();
      expect(errorData.error).toMatch(/email/i);
    });

    it('should fail with invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'testpassword123',
        name: 'Test User',
      };

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      
      expect(response.ok).toBe(false);
      const data = await response.json();
      expect(data.error).toMatch(/email/i);
    });

    it('should fail with weak password', async () => {
      const userData = {
        email: TestDataGenerators.generateEmail(),
        password: '123',
        name: 'Test User',
      };

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });
      
      expect(response.ok).toBe(false);
      const data = await response.json();
      expect(data.error).toMatch(/password/i);
    });

    it('should fail with missing required fields', async () => {
      const incompleteData = {
        email: TestDataGenerators.generateEmail(),
        // Missing password and name
      };

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(incompleteData),
      });
      
      expect(response.ok).toBe(false);
    });
  });

  describe('POST /api/v1/auth/login', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = {
        email: TestDataGenerators.generateEmail(),
        password: 'testpassword123',
        name: 'Test User',
      };

      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testUser),
      });
      expect(registerResponse.ok).toBe(true);
    });

    it('should login with valid credentials', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password,
        }),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data.token).toBeDefined();
      expect(data.user).toBeDefined();
      expect(data.user.email).toBe(testUser.email);
      APIAssertions.assertValidJWTToken(data.token);
    });

    it('should fail with invalid email', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: testUser.password,
        }),
      });

      expect(response.ok).toBe(false);
      const data = await response.json();
      expect(data.error).toMatch(/credentials|email/i);
    });

    it('should fail with invalid password', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
          password: 'wrongpassword',
        }),
      });

      expect(response.ok).toBe(false);
      const data = await response.json();
      expect(data.error).toMatch(/credentials|password/i);
    });

    it('should fail with missing credentials', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
          // Missing password
        }),
      });

      expect(response.ok).toBe(false);
    });
  });

  describe('POST /api/v1/auth/google', () => {
    it('should handle Google OAuth token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/google`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: 'mock-google-token',
        }),
      });

      // Should return 400 for invalid token, not 404 (endpoint exists)
      expect(response.status).not.toBe(404);
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    let authToken: string;

    beforeEach(async () => {
      const email = TestDataGenerators.generateEmail();
      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password: 'testpassword123',
          name: 'Test User',
        }),
      });
      
      if (registerResponse.ok) {
        const data = await registerResponse.json();
        authToken = data.token;
      }
    });

    it('should refresh valid token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: authToken, // Using same token for simplicity
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.token).toBeDefined();
        APIAssertions.assertValidJWTToken(data.token);
      } else {
        // Endpoint might not be implemented or require special refresh tokens
        expect(response.status).not.toBe(404);
      }
    });

    it('should fail with invalid token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: 'invalid-token',
        }),
      });

      expect(response.ok).toBe(false);
    });

    it('should fail without token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refresh_token: 'some-token',
        }),
      });

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/v1/auth/forgot-password', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = {
        email: TestDataGenerators.generateEmail(),
        password: 'testpassword123',
        name: 'Test User',
      };

      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testUser),
      });
      expect(registerResponse.ok).toBe(true);
    });

    it('should initiate password reset for valid email', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/forgot-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUser.email,
        }),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data.message).toMatch(/reset/i);
    });

    it('should not reveal if email exists (security)', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/forgot-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
        }),
      });

      // Should return success even for non-existent email (security best practice)
      expect(response.ok).toBe(true);
    });

    it('should fail with invalid email format', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/forgot-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: 'invalid-email',
        }),
      });

      expect(response.ok).toBe(false);
    });
  });

  describe('POST /api/v1/auth/reset-password', () => {
    it('should require reset token and new password', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/reset-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: 'mock-reset-token',
          password: 'newpassword123',
        }),
      });

      // Should not return 404 (endpoint exists)
      expect(response.status).not.toBe(404);
      // Would fail with invalid token
      expect(response.ok).toBe(false);
    });

    it('should fail with missing token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/reset-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          password: 'newpassword123',
        }),
      });

      expect(response.ok).toBe(false);
    });

    it('should fail with weak password', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/reset-password`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: 'mock-reset-token',
          password: '123',
        }),
      });

      expect(response.ok).toBe(false);
    });
  });

  describe('Authentication Token Validation', () => {
    let authToken: string;

    beforeEach(async () => {
      const email = TestDataGenerators.generateEmail();
      const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password: 'testpassword123',
          name: 'Test User',
        }),
      });
      
      if (registerResponse.ok) {
        const data = await registerResponse.json();
        authToken = data.token;
      }
    });

    it('should accept valid JWT token in Authorization header', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      });

      expect(response.ok).toBe(true);
    });

    it('should reject malformed JWT token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: {
          'Authorization': 'Bearer invalid.token.format',
        },
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);
    });

    it('should reject expired JWT token', async () => {
      // This would require generating an expired token
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
      
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${expiredToken}`,
        },
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);
    });

    it('should reject request without Authorization header', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`);

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);
    });
  });

  describe('Rate Limiting', () => {
    it('should rate limit login attempts', async () => {
      const testEmail = TestDataGenerators.generateEmail();
      
      // Make multiple failed login attempts
      const attempts = 10;
      const promises = Array(attempts).fill(null).map(() =>
        fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testEmail,
            password: 'wrongpassword',
          }),
        })
      );

      const responses = await Promise.all(promises);
      
      // At least some requests should be rate limited (429) or fail
      const rateLimited = responses.some(r => r.status === 429);
      const allFailed = responses.every(r => !r.ok);
      expect(rateLimited || allFailed).toBe(true);
    });

    it('should rate limit registration attempts', async () => {
      // Make multiple registration attempts with different emails to test rate limiting
      const attempts = 5;
      const promises = Array(attempts).fill(null).map(() => {
        const email = TestDataGenerators.generateEmail();
        return fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email,
            password: 'testpassword123',
            name: 'Test User',
          }),
        });
      });

      const responses = await Promise.all(promises);
      
      // Most should succeed, but if rate limiting is implemented, some might fail
      const successfulResponses = responses.filter(r => r.ok);
      const failedResponses = responses.filter(r => !r.ok);
      
      // At least one should succeed (not all can be rate limited)
      expect(successfulResponses.length).toBeGreaterThan(0);
      
      // If rate limiting is active, expect some failures
      if (failedResponses.length > 0) {
        // Check if failures are due to valid reasons (rate limiting, validation, duplicates)
        const validFailures = failedResponses.every(r => [429, 400, 409].includes(r.status));
        expect(validFailures).toBe(true);
      }
    });
  });
});