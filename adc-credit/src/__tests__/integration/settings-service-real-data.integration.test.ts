/**
 * Settings Service Integration Tests with Real Data - ADC Credit Service
 * 
 * Tests the integration between Credit Service frontend and the centralized ADC Settings Service
 * using actual data from Supabase databases.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { SETTINGS_TEST_IDS, SETTINGS_INTEGRATION_TEST_IDS, CREDIT_DYNAMIC_TEST_IDS } from '@/lib/test-ids';
import { getTestIdSelector } from '@/lib/test-utils';

// Real database URLs from environment
const SETTINGS_SERVICE_URL = 'http://localhost:9200'; // Settings Service with real Supabase data
const CREDIT_DB_URL = 'postgresql://postgres:<EMAIL>:5432/postgres';
const MULTILANG_DB_URL = 'postgresql://postgres:<EMAIL>:5432/postgres';

// Settings Service SDK for real API calls
class RealSettingsServiceSDK {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  async healthCheck(): Promise<{ status: string; database: string; cache: string }> {
    const response = await fetch(`${this.baseURL}/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    return response.json();
  }

  async getUserSettings(userId?: string): Promise<any[]> {
    const response = await fetch(`${this.baseURL}/api/v1/settings/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        scope: 'user',
        user_id: userId,
        is_active: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user settings: ${response.statusText}`);
    }

    const data = await response.json();
    return data.settings || [];
  }

  async getShopSettings(shopId: string): Promise<any[]> {
    const response = await fetch(`${this.baseURL}/api/v1/settings/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        scope: 'shop',
        shop_id: shopId,
        is_active: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch shop settings: ${response.statusText}`);
    }

    const data = await response.json();
    return data.settings || [];
  }

  async getGlobalSettings(): Promise<any[]> {
    const response = await fetch(`${this.baseURL}/api/v1/settings/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        scope: 'global',
        is_active: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch global settings: ${response.statusText}`);
    }

    const data = await response.json();
    return data.settings || [];
  }

  async createSetting(setting: any): Promise<any> {
    const response = await fetch(`${this.baseURL}/api/v1/settings`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(setting),
    });

    if (!response.ok) {
      throw new Error(`Failed to create setting: ${response.statusText}`);
    }

    return response.json();
  }

  async updateSetting(settingId: string, updates: any): Promise<any> {
    const response = await fetch(`${this.baseURL}/api/v1/settings/${settingId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      throw new Error(`Failed to update setting: ${response.statusText}`);
    }

    return response.json();
  }
}

// Real Shop Data fetcher
class CreditServiceDataFetcher {
  private dbUrl: string;

  constructor(dbUrl: string) {
    this.dbUrl = dbUrl;
  }

  async getShops(limit = 5): Promise<any[]> {
    // In a real implementation, this would use the Credit Service API
    // For testing purposes, we'll simulate the data structure
    return [
      { id: 'shop-1', slug: 'verawat1234s-org', name: 'verawat1234\'s Org', shop_type: 'api_service' },
      { id: 'shop-2', slug: 'test-shop', name: 'Test Shop', shop_type: 'retail' },
      { id: 'shop-3', slug: 'mynewfacebooks-org', name: 'mynewfacebook\'s Org', shop_type: 'retail' },
    ];
  }

  async getShopById(shopId: string): Promise<any | null> {
    const shops = await this.getShops();
    return shops.find(shop => shop.id === shopId) || null;
  }
}

// Test setup with real services
const setupRealServices = () => {
  const settingsSDK = new RealSettingsServiceSDK(SETTINGS_SERVICE_URL);
  const creditDataFetcher = new CreditServiceDataFetcher(CREDIT_DB_URL);

  return { settingsSDK, creditDataFetcher };
};

// Mock Redux Store with real data structure
const createRealDataStore = (realShops: any[] = []) => {
  return configureStore({
    reducer: {
      settings: (state = { 
        user: {}, 
        shop: {}, 
        organization: {}, 
        global: {},
        loading: false,
        error: null 
      }, action) => state,
      auth: (state = { 
        user: { id: 'user-123', email: '<EMAIL>' }, 
        organization: { id: 'org-123', name: 'Test Organization' }
      }, action) => state,
      shops: (state = { 
        items: realShops,
        currentShop: realShops[0] || null,
        loading: false 
      }, action) => state,
    },
  });
};

// Mock React Component that integrates with real Settings Service
const RealDataUserSettingsPage = ({ settingsSDK }: { settingsSDK: RealSettingsServiceSDK }) => {
  const [settings, setSettings] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [saving, setSaving] = React.useState(false);

  React.useEffect(() => {
    const loadSettings = async () => {
      try {
        const userSettings = await settingsSDK.getUserSettings();
        setSettings(userSettings);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load settings');
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [settingsSDK]);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Create a new user setting as a test
      await settingsSDK.createSetting({
        key: 'user.test.integration_test',
        name: 'Integration Test Setting',
        value: JSON.stringify({ tested: true, timestamp: new Date().toISOString() }),
        scope: 'user',
        data_type: 'json',
        visibility: 'private',
        category: 'test',
        tags: ['integration', 'test'],
        is_active: true,
      });

      // Reload settings
      const updatedSettings = await settingsSDK.getUserSettings();
      setSettings(updatedSettings);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div data-testid={SETTINGS_TEST_IDS.USER.CONTAINER}>
        <div data-testid={SETTINGS_TEST_IDS.USER.LOADING_SPINNER}>Loading real settings...</div>
      </div>
    );
  }

  return (
    <div data-testid={SETTINGS_TEST_IDS.USER.CONTAINER}>
      <form data-testid={SETTINGS_TEST_IDS.USER.FORM}>
        <h2>Real User Settings Integration</h2>
        
        {error && (
          <div data-testid={SETTINGS_TEST_IDS.USER.ERROR_MESSAGE}>
            Error: {error}
          </div>
        )}

        <div>
          <h3>Current Settings from Real Database:</h3>
          <ul data-testid="real-settings-list">
            {settings.map((setting, index) => (
              <li key={setting.id || index} data-testid={`real-setting-${setting.key}`}>
                <strong>{setting.key}</strong>: {setting.value}
                <small> ({setting.scope})</small>
              </li>
            ))}
          </ul>
        </div>

        <select data-testid={SETTINGS_TEST_IDS.USER.THEME_SELECT}>
          <option value="light">Light</option>
          <option value="dark">Dark</option>
        </select>

        <select data-testid={SETTINGS_TEST_IDS.USER.CURRENCY_SELECT}>
          <option value="USD">USD</option>
          <option value="EUR">EUR</option>
        </select>

        <button
          type="button"
          data-testid={SETTINGS_TEST_IDS.USER.SAVE_BUTTON}
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </button>

        {saving && (
          <div data-testid={SETTINGS_TEST_IDS.USER.LOADING_SPINNER}>
            Saving to real database...
          </div>
        )}
      </form>
    </div>
  );
};

const RealDataShopSettingsPage = ({ 
  shopData, 
  settingsSDK 
}: { 
  shopData: any; 
  settingsSDK: RealSettingsServiceSDK; 
}) => {
  const [shopSettings, setShopSettings] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const loadShopSettings = async () => {
      try {
        const settings = await settingsSDK.getShopSettings(shopData.id);
        setShopSettings(settings);
      } catch (err) {
        console.error('Failed to load shop settings:', err);
      } finally {
        setLoading(false);
      }
    };

    loadShopSettings();
  }, [shopData.id, settingsSDK]);

  return (
    <div data-testid={SETTINGS_TEST_IDS.SHOP.CONTAINER}>
      <h2>Real Shop Settings: {shopData.name}</h2>
      <p>Shop Type: {shopData.shop_type}</p>
      <p>Slug: {shopData.slug}</p>
      
      {loading ? (
        <div data-testid={SETTINGS_TEST_IDS.SHOP.LOADING_SPINNER}>
          Loading shop settings...
        </div>
      ) : (
        <div>
          <h3>Shop Settings from Real Database:</h3>
          <ul data-testid="real-shop-settings-list">
            {shopSettings.length > 0 ? (
              shopSettings.map((setting) => (
                <li key={setting.id} data-testid={`real-shop-setting-${setting.key}`}>
                  <strong>{setting.key}</strong>: {setting.value}
                </li>
              ))
            ) : (
              <li data-testid="no-shop-settings">No shop-specific settings found</li>
            )}
          </ul>
        </div>
      )}

      <form data-testid={SETTINGS_TEST_IDS.SHOP.FORM}>
        <section data-testid={SETTINGS_TEST_IDS.SHOP.GENERAL.SECTION}>
          <input
            data-testid={SETTINGS_TEST_IDS.SHOP.GENERAL.NAME_INPUT}
            defaultValue={shopData.name}
          />
        </section>

        <section data-testid={SETTINGS_TEST_IDS.SHOP.QR_CODE.SECTION}>
          <select data-testid={SETTINGS_TEST_IDS.SHOP.QR_CODE.TEMPLATE_SELECT}>
            <option value="default">Default</option>
            <option value="modern">Modern</option>
          </select>
        </section>

        <button type="submit" data-testid={SETTINGS_TEST_IDS.SHOP.SAVE_BUTTON}>
          Save Shop Settings
        </button>
      </form>
    </div>
  );
};

const RealDataSettingsServiceIntegration = ({ 
  settingsSDK 
}: { 
  settingsSDK: RealSettingsServiceSDK; 
}) => {
  const [connectionStatus, setConnectionStatus] = React.useState<string>('checking');
  const [healthData, setHealthData] = React.useState<any>(null);
  const [globalSettings, setGlobalSettings] = React.useState<any[]>([]);

  const checkConnection = async () => {
    try {
      setConnectionStatus('checking');
      const health = await settingsSDK.healthCheck();
      setHealthData(health);
      setConnectionStatus('connected');
    } catch (err) {
      setConnectionStatus('error');
      console.error('Connection check failed:', err);
    }
  };

  const loadGlobalSettings = async () => {
    try {
      const settings = await settingsSDK.getGlobalSettings();
      setGlobalSettings(settings);
    } catch (err) {
      console.error('Failed to load global settings:', err);
    }
  };

  React.useEffect(() => {
    checkConnection();
    loadGlobalSettings();
  }, []);

  return (
    <div data-testid={SETTINGS_INTEGRATION_TEST_IDS.CONNECTION.STATUS_INDICATOR}>
      <h2>Real Settings Service Integration Status</h2>
      
      <div>
        <strong>Connection Status:</strong> {connectionStatus}
        {healthData && (
          <div data-testid="real-health-data">
            <p>Service: {healthData.status}</p>
            <p>Database: {healthData.database}</p>
            <p>Cache: {healthData.cache}</p>
          </div>
        )}
      </div>

      <button
        data-testid={SETTINGS_INTEGRATION_TEST_IDS.CONNECTION.HEALTH_CHECK_BUTTON}
        onClick={checkConnection}
      >
        Check Real Connection
      </button>

      <div>
        <h3>Real Global Settings:</h3>
        <ul data-testid="real-global-settings-list">
          {globalSettings.map((setting) => (
            <li key={setting.id} data-testid={`real-global-setting-${setting.key}`}>
              <strong>{setting.key}</strong>: {setting.value}
              <small> (Category: {setting.category}, System: {setting.is_system ? 'Yes' : 'No'})</small>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

// Integration Tests with Real Data
describe('Settings Service Integration with Real Data - Credit Service', () => {
  let store: ReturnType<typeof createRealDataStore>;
  let realServices: ReturnType<typeof setupRealServices>;

  beforeAll(() => {
    realServices = setupRealServices();
  });

  beforeEach(() => {
    store = createRealDataStore();
  });

  describe('Real Settings Service Connection', () => {
    it('should connect to real Settings Service and fetch health data', async () => {
      const { settingsSDK } = realServices;

      render(<RealDataSettingsServiceIntegration settingsSDK={settingsSDK} />);

      // Verify connection status component renders
      expect(screen.getByTestId(SETTINGS_INTEGRATION_TEST_IDS.CONNECTION.STATUS_INDICATOR)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_INTEGRATION_TEST_IDS.CONNECTION.HEALTH_CHECK_BUTTON)).toBeInTheDocument();

      // Wait for initial connection check
      await waitFor(() => {
        expect(screen.getByTestId('real-health-data')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Verify health data is displayed
      expect(screen.getByText(/Service:/)).toBeInTheDocument();
      expect(screen.getByText(/Database:/)).toBeInTheDocument();
      expect(screen.getByText(/Cache:/)).toBeInTheDocument();
    });

    it('should load real global settings from Settings Service', async () => {
      const { settingsSDK } = realServices;

      render(<RealDataSettingsServiceIntegration settingsSDK={settingsSDK} />);

      // Wait for global settings to load
      await waitFor(() => {
        expect(screen.getByTestId('real-global-settings-list')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Verify we have the expected global settings from our migration
      expect(screen.getByTestId('real-global-setting-ui.theme')).toBeInTheDocument();
      expect(screen.getByTestId('real-global-setting-ui.language')).toBeInTheDocument();
      expect(screen.getByTestId('real-global-setting-system.maintenance_mode')).toBeInTheDocument();
      expect(screen.getByTestId('real-global-setting-system.api_rate_limit')).toBeInTheDocument();
    });
  });

  describe('Real User Settings Integration', () => {
    it('should render user settings page and connect to real Settings Service', async () => {
      const { settingsSDK } = realServices;

      render(
        <Provider store={store}>
          <RealDataUserSettingsPage settingsSDK={settingsSDK} />
        </Provider>
      );

      // Verify page renders
      expect(screen.getByTestId(SETTINGS_TEST_IDS.USER.CONTAINER)).toBeInTheDocument();

      // Wait for real settings to load
      await waitFor(() => {
        expect(screen.queryByTestId(SETTINGS_TEST_IDS.USER.LOADING_SPINNER)).not.toBeInTheDocument();
      }, { timeout: 10000 });

      // Verify form elements are present
      expect(screen.getByTestId(SETTINGS_TEST_IDS.USER.FORM)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.USER.THEME_SELECT)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.USER.CURRENCY_SELECT)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.USER.SAVE_BUTTON)).toBeInTheDocument();
    });

    it('should create new settings in real Settings Service', async () => {
      const { settingsSDK } = realServices;

      render(
        <Provider store={store}>
          <RealDataUserSettingsPage settingsSDK={settingsSDK} />
        </Provider>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.queryByText('Loading real settings...')).not.toBeInTheDocument();
      }, { timeout: 10000 });

      // Click save to create a new test setting
      const saveButton = screen.getByTestId(SETTINGS_TEST_IDS.USER.SAVE_BUTTON);
      fireEvent.click(saveButton);

      // Wait for save operation
      await waitFor(() => {
        expect(screen.queryByText('Saving to real database...')).not.toBeInTheDocument();
      }, { timeout: 15000 });

      // Verify the test setting was created (it should appear in the list)
      await waitFor(() => {
        expect(screen.getByTestId('real-setting-user.test.integration_test')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Real Shop Settings Integration', () => {
    it('should load real shop data and integrate with Settings Service', async () => {
      const { settingsSDK, creditDataFetcher } = realServices;

      // Get real shop data
      const shops = await creditDataFetcher.getShops(3);
      expect(shops.length).toBeGreaterThan(0);

      const testShop = shops[0]; // Use first shop for testing
      store = createRealDataStore(shops);

      render(
        <Provider store={store}>
          <RealDataShopSettingsPage shopData={testShop} settingsSDK={settingsSDK} />
        </Provider>
      );

      // Verify shop information is displayed
      expect(screen.getByText(`Real Shop Settings: ${testShop.name}`)).toBeInTheDocument();
      expect(screen.getByText(`Shop Type: ${testShop.shop_type}`)).toBeInTheDocument();
      expect(screen.getByText(`Slug: ${testShop.slug}`)).toBeInTheDocument();

      // Verify Settings Service components are present
      expect(screen.getByTestId(SETTINGS_TEST_IDS.SHOP.CONTAINER)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.SHOP.FORM)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.SHOP.GENERAL.SECTION)).toBeInTheDocument();
      expect(screen.getByTestId(SETTINGS_TEST_IDS.SHOP.QR_CODE.SECTION)).toBeInTheDocument();

      // Wait for shop settings to load from Settings Service
      await waitFor(() => {
        expect(screen.queryByText('Loading shop settings...')).not.toBeInTheDocument();
      }, { timeout: 10000 });

      // Verify shop settings list is rendered
      expect(screen.getByTestId('real-shop-settings-list')).toBeInTheDocument();
    });

    it('should use dynamic test IDs for real shop data', async () => {
      const { creditDataFetcher } = realServices;
      const shops = await creditDataFetcher.getShops(3);
      const testShop = shops[0];

      // Test dynamic test ID generation with real shop data
      const dynamicTestId = CREDIT_DYNAMIC_TEST_IDS.SHOP_SETTING(testShop.id, 'qr.template');
      expect(dynamicTestId).toBe(`settings-shop-${testShop.id}-qr.template`);

      const qrCodeTestId = CREDIT_DYNAMIC_TEST_IDS.SHOP_QR_CODE(testShop.id);
      expect(qrCodeTestId).toBe(`settings-shop-qr-code-${testShop.id}`);
    });
  });

  describe('Real API Integration Tests', () => {
    it('should perform CRUD operations on real Settings Service', async () => {
      const { settingsSDK } = realServices;

      // Test health check
      const health = await settingsSDK.healthCheck();
      expect(health.status).toBe('healthy');
      expect(health.database).toBe('connected');

      // Test fetching global settings
      const globalSettings = await settingsSDK.getGlobalSettings();
      expect(globalSettings).toBeInstanceOf(Array);
      expect(globalSettings.length).toBeGreaterThan(0);

      // Verify we have expected migrated settings
      const themeSettings = globalSettings.find(s => s.key === 'ui.theme');
      expect(themeSettings).toBeDefined();
      expect(themeSettings.scope).toBe('global');

      // Test creating a new setting
      const newSetting = await settingsSDK.createSetting({
        key: 'test.real_integration.timestamp',
        name: 'Real Integration Test',
        value: JSON.stringify({ created: new Date().toISOString() }),
        scope: 'global',
        data_type: 'json',
        visibility: 'private',
        category: 'test',
        tags: ['real-test', 'integration'],
        is_active: true,
      });

      expect(newSetting.id).toBeDefined();
      expect(newSetting.key).toBe('test.real_integration.timestamp');

      // Test fetching the created setting
      const updatedGlobalSettings = await settingsSDK.getGlobalSettings();
      const createdSetting = updatedGlobalSettings.find(s => s.key === 'test.real_integration.timestamp');
      expect(createdSetting).toBeDefined();
    });
  });
});

// Export real data utilities for other tests
export {
  RealSettingsServiceSDK,
  CreditServiceDataFetcher,
  setupRealServices,
  SETTINGS_SERVICE_URL,
  CREDIT_DB_URL,
  MULTILANG_DB_URL,
};