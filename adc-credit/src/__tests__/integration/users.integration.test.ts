/**
 * Integration tests for User Management API endpoints
 * Tests user profile operations using direct HTTP calls
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('User Management API Integration Tests', () => {
  let authToken: string;
  let testUserId: string;
  let testUserEmail: string;

  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
    
    // Setup authentication
    testUserEmail = TestDataGenerators.generateEmail();
    
    const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUserEmail,
        password: 'testpassword123',
        name: 'User Test User',
      }),
    });

    if (registerResponse.ok) {
      const data = await registerResponse.json();
      authToken = data.token;
      testUserId = data.user.id;
    }
  });

  describe('GET /api/v1/users/me', () => {
    it('should get current user profile', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      expect(response.ok).toBe(true);
      
      const data = await response.json();
      expect(data.id).toBeDefined();
      expect(data.email).toBe(testUserEmail);
      expect(data.name).toBeDefined();
      expect(data.role).toBeDefined();
      expect(data.created_at).toBeDefined();
      
      // Should not expose sensitive data
      expect(data.password).toBeUndefined();
    });

    it('should fail without authentication', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`);
      
      expect(response.status).toBe(401);
    });

    it('should fail with invalid token', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': 'Bearer invalid-token' },
      });
      
      expect(response.status).toBe(401);
    });
  });

  describe('PUT /api/v1/users/me', () => {
    it('should update user profile', async () => {
      const updateData = {
        name: 'Updated Test User',
      };

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.name).toBe(updateData.name);
        expect(data.updated_at).toBeDefined();
      } else {
        // Endpoint might not be implemented or have validation errors
        expect([400, 404, 422]).toContain(response.status);
      }
    });

    it('should update only provided fields', async () => {
      // Get current user
      const currentResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });
      
      if (!currentResponse.ok) return;
      
      const originalUser = await currentResponse.json();
      
      const updateData = {
        name: 'Partially Updated User',
      };

      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.name).toBe(updateData.name);
        expect(data.email).toBe(originalUser.email);
      } else {
        expect([400, 404, 422]).toContain(response.status);
      }
    });

    it('should fail with invalid email format', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: 'invalid-email-format',
        }),
      });

      expect([400, 422]).toContain(response.status);
    });

    it('should fail without authentication', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'Should Fail',
        }),
      });

      expect(response.status).toBe(401);
    });

    it('should validate input fields', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '', // Empty name should be invalid
        }),
      });

      expect([400, 422]).toContain(response.status);
    });
  });

  describe('User Profile Data Validation', () => {
    it('should maintain data integrity after updates', async () => {
      // Get current user
      const beforeResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });
      
      if (!beforeResponse.ok) return;
      
      const originalUser = await beforeResponse.json();

      // Update user
      const updateData = {
        name: 'Data Integrity Test User',
      };

      const updateResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!updateResponse.ok) return;

      // Get updated user
      const afterResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (afterResponse.ok) {
        const updatedUser = await afterResponse.json();
        
        // Check that immutable fields weren't changed
        expect(updatedUser.id).toBe(originalUser.id);
        expect(updatedUser.email).toBe(originalUser.email);
        expect(updatedUser.role).toBe(originalUser.role);
        expect(updatedUser.created_at).toBe(originalUser.created_at);
        
        // Check that the update was applied
        expect(updatedUser.name).toBe(updateData.name);
        
        // Check that updated_at was changed
        if (updatedUser.updated_at && originalUser.updated_at) {
          expect(new Date(updatedUser.updated_at).getTime()).toBeGreaterThan(
            new Date(originalUser.updated_at).getTime()
          );
        }
      }
    });

    it('should include all expected user fields', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        
        // Required fields
        const requiredFields = ['id', 'email', 'name', 'role', 'created_at'];
        
        for (const field of requiredFields) {
          expect(data[field]).toBeDefined();
        }

        // Optional fields that might be present
        const optionalFields = ['updated_at', 'last_login', 'subscription_id'];
        
        for (const field of optionalFields) {
          if (data[field] !== undefined) {
            expect(typeof data[field]).toBe('string');
          }
        }
      }
    });
  });

  describe('User Role and Permissions', () => {
    it('should have default user role for new users', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        expect(['user', 'customer', 'basic_user']).toContain(data.role);
      }
    });

    it('should not allow role modification through profile update', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: 'admin', // Should not be allowed
        }),
      });

      // Should either ignore role change or return error
      expect([400, 403, 422]).toContain(response.status);
    });
  });

  describe('User Data Privacy and Security', () => {
    it('should not expose sensitive data', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        
        // Sensitive fields that should not be exposed
        const sensitiveFields = ['password', 'password_hash', 'password_salt', 'private_key'];
        
        for (const field of sensitiveFields) {
          expect(data[field]).toBeUndefined();
        }
      }
    });

    it('should handle concurrent updates gracefully', async () => {
      const updatePromises = Array(5).fill(null).map((_, index) => 
        fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: `Concurrent Update ${index}`,
          }),
        })
      );

      const responses = await Promise.all(updatePromises);
      const successfulUpdates = responses.filter(r => r.ok);
      
      // At least one update should succeed
      expect(successfulUpdates.length).toBeGreaterThan(0);
    });
  });

  describe('Input Sanitization and Validation', () => {
    it('should handle special characters in name', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User with Special Characters áéíóú ñ',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.name).toBe('Test User with Special Characters áéíóú ñ');
      } else {
        expect([400, 422]).toContain(response.status);
      }
    });

    it('should enforce name length limits', async () => {
      const longName = 'A'.repeat(256); // Very long name
      
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: longName,
        }),
      });

      expect([400, 422]).toContain(response.status);
    });

    it('should handle Unicode characters correctly', async () => {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/users/me`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '测试用户 🚀 émojis',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        expect(data.name).toBe('测试用户 🚀 émojis');
      } else {
        expect([400, 422]).toContain(response.status);
      }
    });
  });

  // Cleanup
  afterAll(async () => {
    if (authToken && testUserId) {
      try {
        await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${testUserId}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${authToken}` },
        });
      } catch (error) {
        console.warn('Failed to cleanup test user:', error);
      }
    }
  });
});