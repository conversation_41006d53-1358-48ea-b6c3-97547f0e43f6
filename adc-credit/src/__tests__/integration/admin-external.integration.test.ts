/**
 * Integration tests for Admin and External API endpoints
 * Tests admin-only endpoints and external API key authentication
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('Admin & External API Integration Tests', () => {
  let adminToken: string;
  let userToken: string;
  let testUserId: string;

  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
    
    // Setup regular user authentication
    const userEmail = TestDataGenerators.generateEmail();
    const userRegisterResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: userEmail,
        password: 'testpassword123',
        name: 'Test User',
      }),
    });

    if (userRegisterResponse.ok) {
      const userData = await userRegisterResponse.json();
      userToken = userData.token;
      testUserId = userData.user.id;
    }

    // Try to setup admin authentication
    // This might fail if no admin user exists
    const adminEmail = '<EMAIL>';
    const adminLoginResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: adminEmail,
        password: 'adminpassword',
      }),
    });

    if (adminLoginResponse.ok) {
      const adminData = await adminLoginResponse.json();
      adminToken = adminData.token;
    }
  });

  describe('External API Endpoints (API Key Authentication)', () => {
    describe('API Key Verification', () => {
      it('POST /api/v1/external/verify should verify API key', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/verify`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
          },
          body: JSON.stringify({}),
        });

        // Should not return 404 (endpoint exists)
        expect(response.status).not.toBe(404);
        
        if (!response.ok) {
          // Likely invalid API key, which is expected
          expect([400, 401, 403]).toContain(response.status);
        } else {
          const data = await response.json();
          expect(data.valid).toBeDefined();
        }
      });

      it('POST /api/v1/external/verify should require API key header', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/verify`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        });

        expect([400, 401]).toContain(response.status);
      });

      it('POST /api/v1/external/verify should reject invalid API key format', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/verify`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'invalid-key-format',
          },
          body: JSON.stringify({}),
        });

        expect([400, 401, 403]).toContain(response.status);
      });
    });

    describe('Credit Consumption', () => {
      it('POST /api/v1/external/consume should handle credit consumption', async () => {
        const consumeData = {
          amount: 10,
          description: 'Test credit consumption',
          user_id: testUserId,
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/consume`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
          },
          body: JSON.stringify(consumeData),
        });

        // Should not return 404 (endpoint exists)
        expect(response.status).not.toBe(404);
        
        if (!response.ok) {
          // Likely invalid API key or insufficient credits
          expect([400, 401, 403]).toContain(response.status);
        } else {
          const data = await response.json();
          expect(data.message).toBeDefined();
          expect(typeof data.remaining_balance).toBe('number');
        }
      });

      it('POST /api/v1/external/consume should require API key', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/consume`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            amount: 10,
            description: 'Test without API key',
          }),
        });

        expect([400, 401]).toContain(response.status);
      });

      it('POST /api/v1/external/consume should validate amount', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/consume`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
          },
          body: JSON.stringify({
            amount: -10, // Negative amount should be invalid
            description: 'Invalid amount test',
          }),
        });

        expect([400, 401, 403]).toContain(response.status);
      });
    });

    describe('Rate Limiting', () => {
      it('should rate limit external API calls', async () => {
        const requests = Array(20).fill(null).map(() =>
          fetch(`${TEST_CONFIG.apiUrl}/api/v1/external/verify`, {
            method: 'POST',
            headers: { 
              'Content-Type': 'application/json',
              'X-API-Key': 'test-api-key',
            },
            body: JSON.stringify({}),
          })
        );

        const responses = await Promise.all(requests);
        
        // Check if any requests were rate limited
        const rateLimitedResponses = responses.filter(r => r.status === 429);
        
        // With 20 rapid requests, we expect some rate limiting
        if (rateLimitedResponses.length > 0) {
          expect(rateLimitedResponses.length).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Task/Scheduled Endpoints (API Key Authentication)', () => {
    describe('Scheduled Credits', () => {
      it('POST /api/v1/tasks/process-scheduled-credits should process scheduled credits', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/tasks/process-scheduled-credits`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': process.env.TEST_SCHEDULER_API_KEY || 'test-scheduler-key',
          },
          body: JSON.stringify({}),
        });

        // Should not return 404 (endpoint exists)
        expect(response.status).not.toBe(404);
        
        if (response.ok) {
          const data = await response.json();
          expect(data.message).toBeDefined();
        } else {
          // Likely invalid API key or already processed
          expect([400, 401, 403]).toContain(response.status);
        }
      });

      it('POST /api/v1/tasks/process-credit-resets should process credit resets', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/tasks/process-credit-resets`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': process.env.TEST_SCHEDULER_API_KEY || 'test-scheduler-key',
          },
          body: JSON.stringify({}),
        });

        expect(response.status).not.toBe(404);
      });

      it('POST /api/v1/tasks/process-monthly-quota-reset should process monthly quota resets', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/tasks/process-monthly-quota-reset`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': process.env.TEST_SCHEDULER_API_KEY || 'test-scheduler-key',
          },
          body: JSON.stringify({}),
        });

        expect(response.status).not.toBe(404);
      });
    });

    describe('Task Authentication', () => {
      it('should require API key for scheduled tasks', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/tasks/process-scheduled-credits`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        });

        expect([401, 403]).toContain(response.status);
      });

      it('should reject invalid API key for scheduled tasks', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/tasks/process-scheduled-credits`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-API-Key': 'invalid-scheduler-key',
          },
          body: JSON.stringify({}),
        });

        expect([401, 403]).toContain(response.status);
      });
    });
  });

  describe('Admin Endpoints (Admin Role Required)', () => {
    describe('User Administration', () => {
      it('GET /api/v1/admin/users should return all users (admin only)', async () => {
        if (!adminToken) {
          console.warn('Admin token not available, skipping admin tests');
          return;
        }

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users`, {
          headers: { 'Authorization': `Bearer ${adminToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
          
          if (data.length > 0) {
            const user = data[0];
            expect(user.id).toBeDefined();
            expect(user.email).toBeDefined();
            expect(user.role).toBeDefined();
          }
        } else {
          expect([403, 404]).toContain(response.status);
        }
      });

      it('GET /api/v1/admin/users/:id should return specific user (admin only)', async () => {
        if (!adminToken || !testUserId) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${testUserId}`, {
          headers: { 'Authorization': `Bearer ${adminToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.id).toBe(testUserId);
        } else {
          expect([403, 404]).toContain(response.status);
        }
      });

      it('PUT /api/v1/admin/users/:id should update user (admin only)', async () => {
        if (!adminToken || !testUserId) return;

        const updateData = {
          role: 'premium_user',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${testUserId}`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });

        expect([200, 403, 404]).toContain(response.status);
      });

      it('DELETE /api/v1/admin/users/:id should delete user (admin only)', async () => {
        if (!adminToken) return;

        // Create a user to delete
        const deleteUserEmail = TestDataGenerators.generateEmail();
        const createResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: deleteUserEmail,
            password: 'testpassword123',
            name: 'User to Delete',
          }),
        });

        if (createResponse.ok) {
          const createData = await createResponse.json();
          const userToDeleteId = createData.user.id;

          const deleteResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${userToDeleteId}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${adminToken}` },
          });

          expect([200, 403, 404]).toContain(deleteResponse.status);
        }
      });
    });

    describe('Subscription Administration', () => {
      it('GET /api/v1/admin/subscriptions should return all subscriptions (admin only)', async () => {
        if (!adminToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/subscriptions`, {
          headers: { 'Authorization': `Bearer ${adminToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect([403, 404]).toContain(response.status);
        }
      });

      it('POST /api/v1/admin/subscription-tiers should create subscription tier (admin only)', async () => {
        if (!adminToken) return;

        const tierData = {
          name: 'Test Tier',
          price: 29.99,
          max_shops: 5,
          max_customers_per_shop: 1000,
          max_api_keys_per_shop: 3,
          max_qr_codes_per_month: 500,
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/subscription-tiers`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(tierData),
        });

        expect([200, 201, 403, 404]).toContain(response.status);
      });
    });

    describe('Credit Administration', () => {
      it('POST /api/v1/admin/credits/scheduled/process should process scheduled credits (admin only)', async () => {
        if (!adminToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/credits/scheduled/process`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });

        expect([200, 403, 404]).toContain(response.status);
      });

      it('POST /api/v1/admin/credits/scheduled/manual should manually add scheduled credits (admin only)', async () => {
        if (!adminToken) return;

        const creditData = {
          user_id: testUserId,
          amount: 100,
          description: 'Manual admin credit addition',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/credits/scheduled/manual`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(creditData),
        });

        expect([200, 403, 404]).toContain(response.status);
      });
    });

    describe('Quota Management (Admin)', () => {
      it('POST /api/v1/admin/quota/reset-monthly should reset monthly quotas (admin only)', async () => {
        if (!adminToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/quota/reset-monthly`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });

        expect([200, 403, 404]).toContain(response.status);
      });

      it('GET /api/v1/admin/quota/usage/:user_id should get user monthly usage (admin only)', async () => {
        if (!adminToken || !testUserId) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/quota/usage/${testUserId}`, {
          headers: { 'Authorization': `Bearer ${adminToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.user_id).toBe(testUserId);
          expect(typeof data.qr_codes_generated).toBe('number');
          expect(typeof data.api_calls_made).toBe('number');
          expect(typeof data.credits_consumed).toBe('number');
        } else {
          expect([403, 404]).toContain(response.status);
        }
      });

      it('POST /api/v1/admin/quota/reset-user/:user_id should force reset user quota (admin only)', async () => {
        if (!adminToken || !testUserId) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/quota/reset-user/${testUserId}`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });

        expect([200, 403, 404]).toContain(response.status);
      });

      it('GET /api/v1/admin/quota/stats should get monthly usage statistics (admin only)', async () => {
        if (!adminToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/quota/stats?year=2024&month=12`, {
          headers: { 'Authorization': `Bearer ${adminToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data).toBeDefined();
        } else {
          expect([403, 404]).toContain(response.status);
        }
      });
    });

    describe('Admin Authorization', () => {
      it('should reject regular user access to admin endpoints', async () => {
        if (!userToken) return;

        const adminEndpoints = [
          '/api/v1/admin/users',
          '/api/v1/admin/subscriptions',
          '/api/v1/admin/quota/stats',
        ];

        for (const endpoint of adminEndpoints) {
          const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}`, {
            headers: { 'Authorization': `Bearer ${userToken}` },
          });

          expect([403, 404]).toContain(response.status);
        }
      });

      it('should reject unauthenticated access to admin endpoints', async () => {
        const adminEndpoints = [
          '/api/v1/admin/users',
          '/api/v1/admin/subscriptions',
          '/api/v1/admin/quota/stats',
        ];

        for (const endpoint of adminEndpoints) {
          const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}`);
          expect([401, 403]).toContain(response.status);
        }
      });
    });
  });

  describe('Legacy Organization Endpoints', () => {
    describe('Organization Management', () => {
      it('GET /api/v1/organizations should return organizations', async () => {
        if (!userToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/organizations`, {
          headers: { 'Authorization': `Bearer ${userToken}` },
        });

        // These might be deprecated endpoints
        expect([200, 404]).toContain(response.status);
      });

      it('POST /api/v1/organizations should create organization', async () => {
        if (!userToken) return;

        const orgData = {
          name: 'Test Organization',
          description: 'Test organization for integration testing',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/organizations`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(orgData),
        });

        expect([200, 201, 404]).toContain(response.status);
      });
    });

    describe('External User Management', () => {
      it('GET /api/v1/org-users should return external users', async () => {
        if (!userToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/org-users`, {
          headers: { 'Authorization': `Bearer ${userToken}` },
        });

        // This endpoint requires organization_id and branch_id query parameters
        // Without them, it should return 400 Bad Request
        expect([400, 404]).toContain(response.status);
      });

      it('POST /api/v1/org-users should create external user', async () => {
        if (!userToken) return;

        const userData = {
          organization_id: 'test-org-id',
          branch_id: 'test-branch-id',
          name: 'External Test User',
          email: TestDataGenerators.generateEmail(),
          monthly_credits: 100,
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/org-users`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userData),
        });

        expect([200, 201, 400, 404]).toContain(response.status);
      });
    });

    describe('Legacy Merchant Endpoints', () => {
      it('GET /api/v1/merchant-shops should return merchant shops', async () => {
        if (!userToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/merchant-shops`, {
          headers: { 'Authorization': `Bearer ${userToken}` },
        });

        // These are legacy endpoints
        expect([200, 404]).toContain(response.status);
      });

      it('GET /api/v1/merchant/credit-stats should return merchant stats', async () => {
        if (!userToken) return;

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/merchant/credit-stats`, {
          headers: { 'Authorization': `Bearer ${userToken}` },
        });

        expect([200, 404]).toContain(response.status);
      });
    });
  });

  describe('API Security and Headers', () => {
    describe('Security Headers', () => {
      it('should include security headers in responses', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
        
        // Check for common security headers
        const headers = response.headers;
        
        // These headers might be set by the backend or reverse proxy
        const securityHeaders = [
          'x-content-type-options',
          'x-frame-options',
          'x-xss-protection',
          'strict-transport-security',
        ];

        // At least some security headers should be present
        const presentHeaders = securityHeaders.filter(header => 
          headers.get(header) !== null
        );

        // This test might pass even if no headers are present (depends on deployment)
        console.log('Security headers present:', presentHeaders);
      });
    });

    describe('Content Type Validation', () => {
      it('should validate Content-Type for JSON endpoints', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'text/plain' },
          body: JSON.stringify({ email: '<EMAIL>', password: 'test' }),
        });

        expect([400, 401, 415]).toContain(response.status);
      });

      it('should accept proper JSON Content-Type', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>', password: 'test' }),
        });

        // Should not fail due to Content-Type (will fail due to invalid credentials)
        expect(response.status).not.toBe(415);
      });
    });
  });
});