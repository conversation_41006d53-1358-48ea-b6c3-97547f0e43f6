/**
 * Integration tests for Subscription and Credit Management API endpoints
 * Tests subscription tiers, limits, and credit operations
 */

import { APITestHelpers, TEST_CONFIG, TestDataGenerators } from './api-test-utils';

describe('Subscription & Credit Management API Integration Tests', () => {
  let authToken: string;
  let testUserId: string;

  beforeAll(async () => {
    await APITestHelpers.skipIfBackendNotRunning();
    
    // Setup authentication
    const email = TestDataGenerators.generateEmail();
    
    const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email,
        password: 'testpassword123',
        name: 'Subscription Test User',
      }),
    });

    if (registerResponse.ok) {
      const data = await registerResponse.json();
      authToken = data.token;
      testUserId = data.user.id;
    }
  });

  describe('Subscription Management', () => {
    describe('Subscription Tiers', () => {
      it('GET /api/v1/subscriptions/tiers should return available tiers', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions/tiers`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
          
          if (data.length > 0) {
            const tier = data[0];
            expect(tier.id).toBeDefined();
            expect(tier.name).toBeDefined();
            expect(tier.price).toBeDefined();
          }
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/public/subscriptions/tiers should return tiers without auth', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/public/subscriptions/tiers`);

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          // Endpoint might not be implemented
          expect([404, 500]).toContain(response.status);
        }
      });
    });

    describe('User Subscriptions', () => {
      it('GET /api/v1/subscriptions should return user subscriptions', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/subscriptions/active should return active subscription', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions/active`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data).toBeDefined();
          
          if (data.id) {
            expect(data.status).toBe('active');
            expect(data.tier_id).toBeDefined();
          }
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('POST /api/v1/subscriptions should create subscription', async () => {
        const subscriptionData = {
          tier_id: 'basic',
          subscription_type: 'personal',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscriptionData),
        });

        expect(response.status).not.toBe(401);
        
        if (response.ok) {
          const data = await response.json();
          expect(data.tier_id).toBe(subscriptionData.tier_id);
          expect(data.subscription_type).toBe(subscriptionData.subscription_type);
        }
      });

      it('GET /api/v1/subscriptions with filtering should work', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions?status=active&type=personal`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(401);
      });
    });
  });

  describe('Subscription Limits', () => {
    describe('General Limits', () => {
      it('GET /api/v1/subscription-limits should return all user limits', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data).toBeDefined();
          expect(typeof data.shops).toBe('object');
          expect(typeof data.customers).toBe('object');
          expect(typeof data.api_keys).toBe('object');
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });

    describe('Shop Limits', () => {
      it('GET /api/v1/subscription-limits/shops/check should check shop creation limit', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shops/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(typeof data.allowed).toBe('boolean');
          expect(typeof data.current_usage).toBe('number');
          expect(typeof data.limit).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/subscription-limits/shop-types/check should check allowed shop types', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shop-types/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data.allowed_types)).toBe(true);
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });

    describe('Customer Limits', () => {
      it('GET /api/v1/subscription-limits/shops/:shopId/customers/check should check customer limit', async () => {
        // Create a test shop first
        const shopResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: TestDataGenerators.generateShopName(),
            description: 'Test shop for customer limits',
            shop_type: 'retail',
            contact_email: '<EMAIL>',
            contact_phone: '+1234567890',
          }),
        });

        if (shopResponse.ok) {
          const shopData = await shopResponse.json();
          const shopId = shopData.id;

          const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shops/${shopId}/customers/check`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
          });

          if (response.ok) {
            const data = await response.json();
            expect(typeof data.allowed).toBe('boolean');
            expect(typeof data.current_usage).toBe('number');
            expect(typeof data.limit).toBe('number');
          } else {
            expect(response.status).not.toBe(401);
          }

          // Cleanup
          await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${shopId}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${authToken}` },
          });
        }
      });
    });

    describe('API Key Limits', () => {
      it('GET /api/v1/subscription-limits/shops/:shopId/api-keys/check should check API key limit', async () => {
        // Create a test shop first
        const shopResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: TestDataGenerators.generateShopName(),
            description: 'Test shop for API key limits',
            shop_type: 'api_service',
            contact_email: '<EMAIL>',
            contact_phone: '+1234567890',
          }),
        });

        if (shopResponse.ok) {
          const shopData = await shopResponse.json();
          const shopId = shopData.id;

          const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shops/${shopId}/api-keys/check`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
          });

          if (response.ok) {
            const data = await response.json();
            expect(typeof data.allowed).toBe('boolean');
            expect(typeof data.current_usage).toBe('number');
            expect(typeof data.limit).toBe('number');
          } else {
            expect(response.status).not.toBe(401);
          }

          // Cleanup
          await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${shopId}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${authToken}` },
          });
        }
      });
    });

    describe('QR Code Limits', () => {
      it('GET /api/v1/subscription-limits/qr-codes/check should check QR code generation limit', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/qr-codes/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(typeof data.allowed).toBe('boolean');
          expect(typeof data.current_usage).toBe('number');
          expect(typeof data.limit).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });

    describe('Branch Limits', () => {
      it('GET /api/v1/subscription-limits/shops/:shopId/branches/check should check branch limit', async () => {
        // Create a test shop first
        const shopResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: TestDataGenerators.generateShopName(),
            description: 'Test shop for branch limits',
            shop_type: 'enterprise',
            contact_email: '<EMAIL>',
            contact_phone: '+1234567890',
          }),
        });

        if (shopResponse.ok) {
          const shopData = await shopResponse.json();
          const shopId = shopData.id;

          const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/shops/${shopId}/branches/check`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
          });

          if (response.ok) {
            const data = await response.json();
            expect(typeof data.allowed).toBe('boolean');
            expect(typeof data.current_usage).toBe('number');
            expect(typeof data.limit).toBe('number');
          } else {
            expect(response.status).not.toBe(401);
          }

          // Cleanup
          await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${shopId}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${authToken}` },
          });
        }
      });
    });

    describe('Webhook Limits', () => {
      it('GET /api/v1/subscription-limits/webhooks/check should check webhook limit', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/webhooks/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(typeof data.allowed).toBe('boolean');
          expect(typeof data.current_usage).toBe('number');
          expect(typeof data.limit).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });

    describe('Credit Limits', () => {
      it('GET /api/v1/subscription-limits/credits/check should check credit balance', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits/credits/check`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(typeof data.current_balance).toBe('number');
          expect(typeof data.monthly_allocation).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });
  });

  describe('Credit Management', () => {
    describe('Credit Balance', () => {
      it('GET /api/v1/credits should return credit balance', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(typeof data.balance).toBe('number');
          expect(data.balance).toBeGreaterThanOrEqual(0);
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });

    describe('Credit Transactions', () => {
      it('POST /api/v1/credits/add should add credits', async () => {
        const creditData = {
          amount: 100,
          description: 'Test credit addition for integration test',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/add`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(creditData),
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.message).toBeDefined();
          expect(typeof data.new_balance).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/credits/transactions should return transaction history', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/transactions`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
          
          if (data.length > 0) {
            const transaction = data[0];
            expect(transaction.id).toBeDefined();
            expect(transaction.amount).toBeDefined();
            expect(transaction.type).toBeDefined();
            expect(transaction.created_at).toBeDefined();
          }
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/credits/transactions should support pagination', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/transactions?page=1&limit=5`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(401);
      });

      it('GET /api/v1/credits/transactions should support filtering', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/transactions?type=addition&start_date=2024-01-01`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(401);
      });
    });

    describe('Scheduled Credits', () => {
      it('GET /api/v1/credits/scheduled/next should return next scheduled credit date', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/scheduled/next`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.next_reset_date).toBeDefined();
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/credits/scheduled/history should return scheduled credit history', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/credits/scheduled/history`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect(response.status).not.toBe(401);
        }
      });
    });
  });

  describe('Stripe Integration', () => {
    describe('Checkout Session', () => {
      it('POST /api/v1/stripe/create-checkout-session should create checkout session', async () => {
        const checkoutData = {
          tier_id: 'premium',
          subscription_type: 'personal',
          success_url: 'https://example.com/success',
          cancel_url: 'https://example.com/cancel',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/stripe/create-checkout-session`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(checkoutData),
        });

        if (response.ok) {
          const data = await response.json();
          expect(data.checkout_url).toBeDefined();
          expect(data.session_id).toBeDefined();
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('POST /api/stripe/create-checkout-session (alternative endpoint) should work', async () => {
        const checkoutData = {
          tier_id: 'basic',
          subscription_type: 'personal',
        };

        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/stripe/create-checkout-session`, {
          method: 'POST',
          headers: { 
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(checkoutData),
        });

        expect(response.status).not.toBe(401);
      });
    });
  });

  describe('Usage Statistics', () => {
    describe('Usage Data', () => {
      it('GET /api/v1/usage should return usage data', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/usage/summary should return usage summary', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage/summary`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data).toBeDefined();
          expect(typeof data.total_requests).toBe('number');
        } else {
          expect(response.status).not.toBe(401);
        }
      });

      it('GET /api/v1/usage should support date filtering', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage?start_date=2024-01-01&end_date=2024-12-31`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(401);
      });

      it('GET /api/v1/usage should support endpoint filtering', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/usage?endpoint=/api/v1/credits`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect(response.status).not.toBe(401);
      });
    });
  });

  describe('Advanced Analytics', () => {
    describe('Analytics Summary', () => {
      it('GET /api/v1/analytics/summary should return analytics summary', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/analytics/summary`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(data).toBeDefined();
        } else {
          // Analytics might be subscription tier dependent
          expect([401, 403, 404]).toContain(response.status);
        }
      });

      it('GET /api/v1/analytics/trends should return analytics trends', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/analytics/trends`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        if (response.ok) {
          const data = await response.json();
          expect(Array.isArray(data)).toBe(true);
        } else {
          expect([401, 403, 404]).toContain(response.status);
        }
      });

      it('GET /api/v1/analytics/endpoints should return endpoint analytics', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/analytics/endpoints`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect([200, 401, 403, 404]).toContain(response.status);
      });

      it('GET /api/v1/analytics/performance should return performance metrics', async () => {
        const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/analytics/performance`, {
          headers: { 'Authorization': `Bearer ${authToken}` },
        });

        expect([200, 401, 403, 404]).toContain(response.status);
      });
    });
  });

  describe('Subscription Tier Validation', () => {
    it('should enforce subscription limits based on tier', async () => {
      // This test would require knowing the user's current subscription tier
      // and testing that limits are properly enforced
      
      // Get current subscription
      const subscriptionResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscriptions/active`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });

      if (subscriptionResponse.ok) {
        const subscription = await subscriptionResponse.json();
        
        if (subscription.tier_id) {
          // Get limits for this tier
          const limitsResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/subscription-limits`, {
            headers: { 'Authorization': `Bearer ${authToken}` },
          });

          if (limitsResponse.ok) {
            const limits = await limitsResponse.json();
            expect(limits).toBeDefined();
            
            // Verify that limits are consistent with the subscription tier
            expect(limits.shops).toBeDefined();
            expect(limits.customers).toBeDefined();
            expect(limits.api_keys).toBeDefined();
          }
        }
      }
    });
  });
});