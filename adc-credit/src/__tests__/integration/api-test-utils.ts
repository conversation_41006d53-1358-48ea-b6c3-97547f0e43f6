/**
 * Enhanced test utilities for comprehensive API integration tests
 */

import { ADCCreditSDK } from '../../sdk';
import type { Shop, APIKey } from '../../types';

interface ADCCreditConfig {
  baseUrl?: string;
  token?: string;
  debug?: boolean;
  timeout?: number;
}

// Test configuration
export const TEST_CONFIG = {
  apiUrl: process.env.TEST_API_URL || 'http://localhost:8100',
  apiKey: process.env.TEST_API_KEY || 'test-api-key',
  token: process.env.TEST_JWT_TOKEN || 'test-jwt-token',
  debug: true,
  timeout: 10000,
  enableDebugLogs: process.env.NODE_ENV !== 'production',
};

// Test data templates
export const TEST_DATA = {
  user: {
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Integration Test User',
  },
  shop: {
    name: 'Integration Test Shop',
    description: 'A shop for integration testing',
    shop_type: 'retail' as const,
    contact_email: '<EMAIL>',
    contact_phone: '+1234567890',
  },
  customer: {
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+1234567890',
  },
  apiKey: {
    name: 'Test API Key',
    permissions: ['read', 'write'],
  },
  webhook: {
    url: 'https://example.com/webhook',
    events: ['credit.added', 'credit.used'],
    name: 'Test Webhook',
  },
};

// Authentication tokens for different scenarios
export interface TestAuth {
  token?: string;
  apiKey?: string;
  userId?: string;
  shopId?: string;
}

/**
 * Test suite manager for API integration tests
 */
export class APITestSuite {
  private sdk: ADCCreditSDK;
  private auth: TestAuth = {};
  private cleanupTasks: Array<() => Promise<void>> = [];

  constructor(config?: Partial<ADCCreditConfig>) {
    this.sdk = new ADCCreditSDK({
      baseUrl: TEST_CONFIG.apiUrl, // Direct backend URL
      debug: TEST_CONFIG.debug,
      timeout: TEST_CONFIG.timeout,
      ...config,
    });
  }

  /**
   * Setup authentication for tests using direct HTTP calls
   */
  async setupAuth(): Promise<TestAuth> {
    // Register a test user
    const uniqueId = this.generateTestId();
    const testUser = {
      ...TEST_DATA.user,
      email: `test-${uniqueId}@example.com`,
    };

    const registerResponse = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
        name: testUser.name,
      }),
    });

    if (!registerResponse.ok) {
      throw new Error(`Failed to register test user: ${registerResponse.statusText}`);
    }

    const registerData = await registerResponse.json();
    
    this.auth = {
      token: registerData.token,
      userId: registerData.user?.id,
    };

    // Update SDK with token
    this.sdk.updateConfig({ token: this.auth.token });

    // Schedule cleanup
    this.addCleanupTask(async () => {
      try {
        if (this.auth.userId && this.auth.token) {
          await fetch(`${TEST_CONFIG.apiUrl}/api/v1/admin/users/${this.auth.userId}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${this.auth.token}` },
          });
        }
      } catch (error) {
        console.warn('Failed to cleanup test user:', error);
      }
    });

    return this.auth;
  }

  /**
   * Create a test shop using direct HTTP calls
   */
  async createTestShop(): Promise<any> {
    const uniqueId = this.generateTestId();
    const testShop = {
      ...TEST_DATA.shop,
      name: `Test Shop ${uniqueId}`,
    };

    const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.auth.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testShop),
    });

    if (!response.ok) {
      throw new Error(`Failed to create test shop: ${response.statusText}`);
    }

    const shop = await response.json();
    this.auth.shopId = shop.id;

    // Schedule cleanup
    this.addCleanupTask(async () => {
      try {
        await fetch(`${TEST_CONFIG.apiUrl}/api/v1/shops/${shop.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${this.auth.token}` },
        });
      } catch (error) {
        console.warn('Failed to cleanup test shop:', error);
      }
    });

    return shop;
  }

  /**
   * Create a test API key using direct HTTP calls
   */
  async createTestAPIKey(shopId?: string): Promise<any> {
    const uniqueId = this.generateTestId();
    const testAPIKey = {
      ...TEST_DATA.apiKey,
      name: `Test API Key ${uniqueId}`,
    };

    const endpoint = shopId 
      ? `/api/v1/shops/${shopId}/apikeys`
      : '/api/v1/apikeys';

    const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.auth.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testAPIKey),
    });

    if (!response.ok) {
      throw new Error(`Failed to create test API key: ${response.statusText}`);
    }

    const apiKey = await response.json();
    this.auth.apiKey = apiKey.key;

    // Schedule cleanup
    this.addCleanupTask(async () => {
      try {
        const deleteEndpoint = shopId
          ? `/api/v1/shops/${shopId}/apikeys/${apiKey.id}`
          : `/api/v1/apikeys/${apiKey.id}`;
        
        await fetch(`${TEST_CONFIG.apiUrl}${deleteEndpoint}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.auth.token}`,
          },
        });
      } catch (error) {
        console.warn('Failed to cleanup test API key:', error);
      }
    });

    return apiKey;
  }

  /**
   * Get SDK instance
   */
  getSDK(): ADCCreditSDK {
    return this.sdk;
  }

  /**
   * Get current authentication
   */
  getAuth(): TestAuth {
    return this.auth;
  }

  /**
   * Add cleanup task
   */
  addCleanupTask(task: () => Promise<void>): void {
    this.cleanupTasks.push(task);
  }

  /**
   * Execute all cleanup tasks
   */
  async cleanup(): Promise<void> {
    for (const task of this.cleanupTasks.reverse()) {
      try {
        await task();
      } catch (error) {
        console.warn('Cleanup task failed:', error);
      }
    }
    this.cleanupTasks = [];
  }

  /**
   * Generate unique test identifier
   */
  generateTestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Wait for specified time
   */
  async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Test helpers for API endpoints
 */
export class APITestHelpers {
  /**
   * Check if backend is running
   */
  static async isBackendRunning(): Promise<boolean> {
    try {
      const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Skip test if backend not running - throws error to be caught by beforeAll
   */
  static async skipIfBackendNotRunning(): Promise<void> {
    const isRunning = await this.isBackendRunning();
    if (!isRunning) {
      console.warn('Backend server is not running. Skipping integration tests.');
      throw new Error('Backend not running - skipping tests');
    }
  }

  /**
   * Make raw API request for testing edge cases
   */
  static async makeRawRequest(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${TEST_CONFIG.apiUrl}${endpoint}`;
    return fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
  }

  /**
   * Test rate limiting by making multiple requests
   */
  static async testRateLimit(
    endpoint: string,
    authHeader: string,
    requestCount: number = 10
  ): Promise<{ limitHit: boolean; responses: Response[] }> {
    const requests = Array(requestCount).fill(null).map(() =>
      this.makeRawRequest(endpoint, {
        headers: { Authorization: authHeader },
      })
    );

    const responses = await Promise.all(requests);
    const limitHit = responses.some(r => r.status === 429);

    return { limitHit, responses };
  }

  /**
   * Test pagination for list endpoints using direct HTTP calls
   */
  static async testPagination(
    endpoint: string,
    authToken: string,
    params: any = {}
  ): Promise<{ totalItems: number; pages: number }> {
    let totalItems = 0;
    let pages = 0;
    let hasMore = true;
    let page = 1;

    while (hasMore && pages < 10) { // Safety limit
      const queryParams = new URLSearchParams({ ...params, page: page.toString(), limit: '10' });
      const response = await fetch(`${TEST_CONFIG.apiUrl}${endpoint}?${queryParams}`, {
        headers: { 'Authorization': `Bearer ${authToken}` },
      });
      
      if (!response.ok) {
        throw new Error(`Pagination test failed: ${response.statusText}`);
      }

      const data = await response.json();
      totalItems += Array.isArray(data) ? data.length : (data.items?.length || 0);
      pages++;
      
      hasMore = data.pagination?.hasMore || false;
      page++;
    }

    return { totalItems, pages };
  }
}

/**
 * Assertion helpers for API responses
 */
export const APIAssertions = {
  /**
   * Assert successful response
   */
  assertSuccess<T>(response: { error?: string; data?: T }): asserts response is { data: T } {
    expect(response.error).toBeUndefined();
    expect(response.data).toBeDefined();
  },

  /**
   * Assert error response
   */
  assertError<T>(response: { error?: string; data?: T }): asserts response is { error: string } {
    expect(response.error).toBeDefined();
    expect(typeof response.error).toBe('string');
  },

  /**
   * Assert valid UUID
   */
  assertValidUUID(value: any): void {
    expect(typeof value).toBe('string');
    expect(value).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
  },

  /**
   * Assert valid API key format
   */
  assertValidAPIKey(value: any): void {
    expect(typeof value).toBe('string');
    expect(value).toMatch(/^sk_[a-zA-Z0-9_]+$/);
  },

  /**
   * Assert valid JWT token
   */
  assertValidJWTToken(value: any): void {
    expect(typeof value).toBe('string');
    expect(value.split('.')).toHaveLength(3);
  },

  /**
   * Assert valid pagination response
   */
  assertValidPagination(pagination: any): void {
    expect(pagination).toBeDefined();
    expect(typeof pagination.page).toBe('number');
    expect(typeof pagination.limit).toBe('number');
    expect(typeof pagination.total).toBe('number');
    expect(typeof pagination.hasMore).toBe('boolean');
  },

  /**
   * Assert valid timestamp
   */
  assertValidTimestamp(value: any): void {
    expect(typeof value).toBe('string');
    expect(new Date(value)).toBeInstanceOf(Date);
    expect(new Date(value).getTime()).not.toBeNaN();
  },

  /**
   * Assert valid email format
   */
  assertValidEmail(value: any): void {
    expect(typeof value).toBe('string');
    expect(value).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  },

  /**
   * Assert rate limit headers
   */
  assertRateLimitHeaders(headers: Headers): void {
    expect(headers.get('X-RateLimit-Limit')).toBeDefined();
    expect(headers.get('X-RateLimit-Remaining')).toBeDefined();
    expect(headers.get('X-RateLimit-Reset')).toBeDefined();
  },
};

/**
 * Test data generators
 */
export const TestDataGenerators = {
  /**
   * Generate unique email
   */
  generateEmail(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `test-${timestamp}-${random}@example.com`;
  },

  /**
   * Generate unique shop name
   */
  generateShopName(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `Test Shop ${timestamp} ${random}`;
  },

  /**
   * Generate test customer data
   */
  generateCustomer(): any {
    const id = Date.now().toString();
    return {
      name: `Test Customer ${id}`,
      email: this.generateEmail(),
      phone: `+1234567${id.substring(id.length - 3)}`,
    };
  },

  /**
   * Generate test webhook data
   */
  generateWebhook(): any {
    const id = Date.now().toString();
    return {
      url: `https://webhook-${id}.example.com/webhook`,
      events: ['credit.added', 'credit.used'],
      name: `Test Webhook ${id}`,
    };
  },
};