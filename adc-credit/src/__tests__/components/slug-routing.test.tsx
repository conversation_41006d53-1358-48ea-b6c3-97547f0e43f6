/**
 * Unit tests for slug-based routing components
 */

import { render, screen, waitFor } from '@testing-library/react';
import { useRouter, useParams } from 'next/navigation';
import { isValidSlug, isUUID, getSlugErrorMessage } from '@/lib/utils';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(),
}));

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;

describe('Slug-based Routing', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any);
  });

  describe('URL Parameter Validation', () => {
    it('should validate slug parameters correctly', () => {
      const validSlugs = [
        'my-shop',
        'coffee-shop-123',
        'test-retail-store',
        'shop1',
        'ab', // minimum length
      ];

      const invalidSlugs = [
        'My-Shop', // uppercase
        'my_shop', // underscore
        'my shop', // space
        '-my-shop', // leading hyphen
        'my-shop-', // trailing hyphen
        'my--shop', // double hyphen
        'shop@123', // special characters
        'a', // too short
        '', // empty
      ];

      validSlugs.forEach(slug => {
        expect(isValidSlug(slug)).toBe(true);
      });

      invalidSlugs.forEach(slug => {
        expect(isValidSlug(slug)).toBe(false);
      });
    });

    it('should detect UUID format correctly', () => {
      const uuids = [
        '123e4567-e89b-12d3-a456-************',
        '550e8400-e29b-41d4-a716-************',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
      ];

      const nonUuids = [
        'my-shop-slug',
        '123-456-789',
        'not-a-uuid-at-all',
        '',
        'abc123def456',
      ];

      uuids.forEach(uuid => {
        expect(isUUID(uuid)).toBe(true);
      });

      nonUuids.forEach(nonUuid => {
        expect(isUUID(nonUuid)).toBe(false);
      });
    });

    it('should provide appropriate error messages', () => {
      expect(getSlugErrorMessage('')).toBe('Shop identifier is missing');
      expect(getSlugErrorMessage('123e4567-e89b-12d3-a456-************'))
        .toBe('This URL format is no longer supported. Please use the updated shop link.');
      expect(getSlugErrorMessage('Invalid_Slug'))
        .toBe('"Invalid_Slug" is not a valid shop identifier');
      expect(getSlugErrorMessage('valid-slug')).toBe('Shop not found');
    });
  });

  describe('Route Transitions', () => {
    it('should handle slug-based navigation correctly', () => {
      // Simulate navigating to a slug-based route
      const shopSlug = 'my-coffee-shop';
      
      // This would be how components use the router
      const navigateToShop = (slug: string) => {
        if (isValidSlug(slug)) {
          mockPush(`/dashboard/customer/shops/${slug}`);
        } else {
          console.error('Invalid slug:', slug);
        }
      };

      navigateToShop(shopSlug);
      expect(mockPush).toHaveBeenCalledWith('/dashboard/customer/shops/my-coffee-shop');
    });

    it('should redirect UUID-based URLs', () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      
      // Simulate the redirect logic from our components
      const handleOldUrl = (identifier: string) => {
        if (isUUID(identifier)) {
          mockReplace('/dashboard/customer/shops');
          return true; // Redirected
        }
        return false; // No redirect needed
      };

      const wasRedirected = handleOldUrl(uuid);
      expect(wasRedirected).toBe(true);
      expect(mockReplace).toHaveBeenCalledWith('/dashboard/customer/shops');
    });
  });

  describe('URL Generation', () => {
    it('should generate correct slug-based URLs', () => {
      const shop = {
        id: 'shop-123',
        slug: 'my-coffee-shop',
        name: 'My Coffee Shop'
      };

      // Test URL generation patterns used in our components
      const customerShopUrl = `/dashboard/customer/shops/${shop.slug}`;
      const merchantShopUrl = `/dashboard/merchant/shop/${shop.slug}`;
      const shopSettingsUrl = `/dashboard/shops/${shop.slug}/settings`;

      expect(customerShopUrl).toBe('/dashboard/customer/shops/my-coffee-shop');
      expect(merchantShopUrl).toBe('/dashboard/merchant/shop/my-coffee-shop');
      expect(shopSettingsUrl).toBe('/dashboard/shops/my-coffee-shop/settings');
    });

    it('should handle URL building with proper escaping', () => {
      const shops = [
        { slug: 'normal-shop', expected: '/dashboard/customer/shops/normal-shop' },
        { slug: 'shop-123', expected: '/dashboard/customer/shops/shop-123' },
        { slug: 'test-retail-store', expected: '/dashboard/customer/shops/test-retail-store' },
      ];

      shops.forEach(({ slug, expected }) => {
        const url = `/dashboard/customer/shops/${encodeURIComponent(slug)}`;
        expect(url).toBe(expected);
      });
    });
  });

  describe('Breadcrumb Generation', () => {
    it('should generate correct breadcrumbs for slug-based routes', () => {
      const shop = {
        slug: 'my-coffee-shop',
        name: 'My Coffee Shop'
      };

      // Simulate breadcrumb generation logic
      const generateBreadcrumbs = (shopSlug: string, shopName?: string) => [
        { label: 'Dashboard', href: '/dashboard/customer' },
        { label: 'Shops', href: '/dashboard/customer/shops' },
        { label: shopName || 'Shop Detail', href: `/dashboard/customer/shops/${shopSlug}` },
      ];

      const breadcrumbs = generateBreadcrumbs(shop.slug, shop.name);
      
      expect(breadcrumbs).toHaveLength(3);
      expect(breadcrumbs[2]).toEqual({
        label: 'My Coffee Shop',
        href: '/dashboard/customer/shops/my-coffee-shop'
      });
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle malformed slug parameters', () => {
      const malformedSlugs = [
        undefined,
        null,
        123,
        {},
        [],
        'slug with spaces',
        'UPPERCASE-SLUG',
      ];

      malformedSlugs.forEach(slug => {
        const result = isValidSlug(slug as any);
        expect(result).toBe(false);
      });
    });

    it('should provide fallback behavior for invalid routes', () => {
      const handleInvalidSlug = (slug: string) => {
        if (!slug || !isValidSlug(slug)) {
          return {
            shouldRedirect: true,
            redirectPath: '/dashboard/customer/shops',
            errorMessage: getSlugErrorMessage(slug)
          };
        }
        return { shouldRedirect: false };
      };

      const result = handleInvalidSlug('Invalid_Slug');
      expect(result.shouldRedirect).toBe(true);
      expect(result.redirectPath).toBe('/dashboard/customer/shops');
      expect(result.errorMessage).toBe('"Invalid_Slug" is not a valid shop identifier');
    });
  });

  describe('Performance Considerations', () => {
    it('should handle slug validation efficiently', () => {
      const testSlugs = Array.from({ length: 1000 }, (_, i) => `test-slug-${i}`);
      
      const startTime = Date.now();
      testSlugs.forEach(slug => isValidSlug(slug));
      const endTime = Date.now();
      
      // Should complete validation of 1000 slugs in reasonable time (< 100ms)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should cache slug validation results if needed', () => {
      // This would test caching if we implement it
      const slug = 'test-shop-123';
      
      // First call
      const result1 = isValidSlug(slug);
      // Second call (would use cache if implemented)
      const result2 = isValidSlug(slug);
      
      expect(result1).toBe(result2);
      expect(result1).toBe(true);
    });
  });
});