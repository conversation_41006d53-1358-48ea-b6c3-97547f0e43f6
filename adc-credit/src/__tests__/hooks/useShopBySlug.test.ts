import { renderHook, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useShopBySlug, useCustomerShopBySlug } from '@/hooks/useShopBySlug';
import { baseApi } from '@/lib/api/baseApi';

// Mock the API
const mockShop = {
  id: 'shop-123',
  slug: 'test-shop',
  name: 'Test Shop',
  description: 'A test shop',
  shop_type: 'retail' as const,
  contact_email: '<EMAIL>',
  contact_phone: '+**********',
  owner_user_id: 'user-123',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

// Mock store setup
const createMockStore = () => configureStore({
  reducer: {
    api: baseApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(baseApi.middleware),
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={createMockStore()}>
    {children}
  </Provider>
);

// Mock the API endpoints
jest.mock('@/lib/api/apiSlice', () => ({
  useGetShopBySlugQuery: jest.fn(),
}));

import { useGetShopBySlugQuery } from '@/lib/api/apiSlice';

const mockUseGetShopBySlugQuery = useGetShopBySlugQuery as jest.MockedFunction<typeof useGetShopBySlugQuery>;

describe('useShopBySlug', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return shop data for valid slug', async () => {
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: mockShop,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useShopBySlug('test-shop'), { wrapper });

    await waitFor(() => {
      expect(result.current.shop).toEqual(mockShop);
      expect(result.current.shopId).toBe('shop-123');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.isValidSlug).toBe(true);
      expect(result.current.isUUID).toBe(false);
    });
  });

  it('should handle invalid slug format', async () => {
    const { result } = renderHook(() => useShopBySlug('Invalid_Slug'), { wrapper });

    await waitFor(() => {
      expect(result.current.shop).toBeUndefined();
      expect(result.current.shopId).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toEqual({ message: 'Invalid slug format' });
      expect(result.current.isValidSlug).toBe(false);
      expect(result.current.isUUID).toBe(false);
    });
  });

  it('should handle UUID format', async () => {
    const uuid = '123e4567-e89b-12d3-a456-************';
    const { result } = renderHook(() => useShopBySlug(uuid), { wrapper });

    await waitFor(() => {
      expect(result.current.shop).toBeUndefined();
      expect(result.current.shopId).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toEqual({ message: 'UUID format not supported' });
      expect(result.current.isValidSlug).toBe(false);
      expect(result.current.isUUID).toBe(true);
    });
  });

  it('should handle loading state', async () => {
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useShopBySlug('test-shop'), { wrapper });

    expect(result.current.isLoading).toBe(true);
    expect(result.current.shop).toBeUndefined();
  });

  it('should handle API error', async () => {
    const apiError = { message: 'Shop not found' };
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: apiError,
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useShopBySlug('non-existent-shop'), { wrapper });

    await waitFor(() => {
      expect(result.current.error).toEqual(apiError);
      expect(result.current.shop).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });
  });
});

describe('useCustomerShopBySlug', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return customer-ready data', async () => {
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: mockShop,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useCustomerShopBySlug('test-shop'), { wrapper });

    await waitFor(() => {
      expect(result.current.isReadyForCustomerOps).toBe(true);
      expect(result.current.shopId).toBe('shop-123');
    });
  });

  it('should indicate not ready when loading', async () => {
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useCustomerShopBySlug('test-shop'), { wrapper });

    expect(result.current.isReadyForCustomerOps).toBe(false);
    expect(result.current.isShopLoading).toBe(true);
  });

  it('should indicate not ready when error occurs', async () => {
    mockUseGetShopBySlugQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: { message: 'Network error' },
      refetch: jest.fn(),
    } as any);

    const { result } = renderHook(() => useCustomerShopBySlug('test-shop'), { wrapper });

    await waitFor(() => {
      expect(result.current.isReadyForCustomerOps).toBe(false);
      expect(result.current.shopError).toEqual({ message: 'Network error' });
    });
  });
});