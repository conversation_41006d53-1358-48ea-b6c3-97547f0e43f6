/**
 * Shops Module Integration Tests
 * Tests the SDK shops functionality
 */

import { 
  createTestSDKWithAPIKey,
  createTestSDKWithToken,
  TEST_SHOP,
  TEST_CUSTOMER,
  generateTestShopName,
  generateTestEmail,
  generateTestId,
  isBackendRunning,
  skipIfBackendNotRunning,
  assertions,
  TestDataCleanup,
  mockFetchAPIError,
  createMockResponse
} from './test-utils';

describe('Shops Module Integration Tests', () => {
  let cleanup: TestDataCleanup;

  beforeEach(() => {
    cleanup = new TestDataCleanup();
  });

  afterEach(async () => {
    await cleanup.cleanup();
  });

  describe('SDK Initialization for Shops', () => {
    it('should create SDK instance with API key for shops', () => {
      const sdk = createTestSDKWithAPIKey();
      expect(sdk.shops).toBeDefined();
    });

    it('should create SDK instance with token for shops', () => {
      const sdk = createTestSDKWithToken();
      expect(sdk.shops).toBeDefined();
    });
  });

  describe('Shop Management', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get all shops (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockResponse = [
        {
          id: 'shop-1',
          name: 'Coffee Shop',
          description: 'Best coffee in town',
          shop_type: 'retail',
          slug: 'coffee-shop',
          contact_email: '<EMAIL>',
          contact_phone: '+1234567890',
          created_at: '2023-12-01T10:00:00Z',
        },
        {
          id: 'shop-2',
          name: 'API Service',
          description: 'API integration service',
          shop_type: 'api_service',
          slug: 'api-service',
          contact_email: '<EMAIL>',
          contact_phone: '+1234567891',
          created_at: '2023-12-01T11:00:00Z',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShops();

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.data[0].shop_type).toBe('retail');
      expect(response.data[1].shop_type).toBe('api_service');
    });

    it('should create a new shop (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const newShop = {
        ...TEST_SHOP,
        name: generateTestShopName(),
      };

      const mockResponse = {
        id: 'shop-new-123',
        ...newShop,
        slug: 'test-shop-123',
        created_at: '2023-12-01T12:00:00Z',
        updated_at: '2023-12-01T12:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.createShop(newShop);

      assertions.assertSuccess(response);
      expect(response.data.name).toBe(newShop.name);
      expect(response.data.shop_type).toBe(newShop.shop_type);
      expect(response.data.id).toBeDefined();
      assertions.assertValidUUID(response.data.id);
    });

    it('should get a specific shop by ID (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const shopId = 'shop-123';
      const mockResponse = {
        id: shopId,
        name: 'Test Shop',
        description: 'A test shop',
        shop_type: 'retail',
        slug: 'test-shop',
        contact_email: '<EMAIL>',
        contact_phone: '+1234567890',
        created_at: '2023-12-01T10:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShop(shopId);

      assertions.assertSuccess(response);
      expect(response.data.id).toBe(shopId);
      expect(response.data.name).toBe('Test Shop');
    });

    it('should update a shop (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const shopId = 'shop-123';
      const updateData = {
        name: 'Updated Shop Name',
        description: 'Updated description',
      };

      const mockResponse = {
        id: shopId,
        name: updateData.name,
        description: updateData.description,
        shop_type: 'retail',
        slug: 'updated-shop-name',
        contact_email: '<EMAIL>',
        contact_phone: '+1234567890',
        updated_at: '2023-12-01T13:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.updateShop(shopId, updateData);

      assertions.assertSuccess(response);
      expect(response.data.name).toBe(updateData.name);
      expect(response.data.description).toBe(updateData.description);
    });

    it('should delete a shop (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const shopId = 'shop-123';
      const mockResponse = {
        message: 'Shop deleted successfully',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.deleteShop(shopId);

      assertions.assertSuccess(response);
      expect(response.data.message).toContain('deleted successfully');
    });

    it('should handle shop not found', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(404, 'Shop not found');

      const response = await sdk.shops.getShop('non-existent-shop');

      assertions.assertError(response);
      expect(response.error).toContain('Shop not found');

      restoreFetch();
    });

    it('should handle unauthorized shop access', async () => {
      const sdk = createTestSDKWithAPIKey({ apiKey: 'invalid-key' });
      
      const restoreFetch = mockFetchAPIError(401, 'Unauthorized');

      const response = await sdk.shops.getShops();

      assertions.assertError(response);
      expect(response.error).toContain('Unauthorized');

      restoreFetch();
    });

    it('should handle shop creation validation errors', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(400, 'Shop name is required');

      const response = await sdk.shops.createShop({
        name: '',
        shop_type: 'retail',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Shop name is required');

      restoreFetch();
    });
  });

  describe('Shop Customer Management', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get shop customers (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const shopId = 'shop-123';
      const mockResponse = [
        {
          id: 'customer-1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          credit_balance: 100,
          created_at: '2023-12-01T10:00:00Z',
        },
        {
          id: 'customer-2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+1234567891',
          credit_balance: 250,
          created_at: '2023-12-01T11:00:00Z',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShopCustomers(shopId);

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.data[0].credit_balance).toBe(100);
      expect(response.data[1].credit_balance).toBe(250);
    });

    it('should add customer to shop (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const shopId = 'shop-123';
      const customerData = {
        ...TEST_CUSTOMER,
        email: generateTestEmail(),
      };

      const mockResponse = {
        id: 'customer-new-123',
        ...customerData,
        credit_balance: 0,
        created_at: '2023-12-01T12:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.addShopCustomer(shopId, customerData);

      assertions.assertSuccess(response);
      expect(response.data.name).toBe(customerData.name);
      expect(response.data.email).toBe(customerData.email);
      expect(response.data.credit_balance).toBe(0);
      assertions.assertValidUUID(response.data.id);
    });

    it('should handle duplicate customer email', async () => {
      const sdk = createTestSDKWithAPIKey();

      const restoreFetch = mockFetchAPIError(409, 'Customer email already exists');

      const response = await sdk.shops.addShopCustomer('shop-123', {
        name: 'Duplicate Customer',
        email: '<EMAIL>',
        phone: '+1234567890',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Customer email already exists');

      restoreFetch();
    });
  });

  describe('Shop Credit Management', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should add credit to shop customer (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const creditData = {
        customer_id: 'customer-123',
        amount: 50,
        description: 'Loyalty bonus',
      };

      const mockResponse = {
        transaction_id: 'txn-credit-123',
        customer_id: creditData.customer_id,
        amount: creditData.amount,
        description: creditData.description,
        new_balance: 150,
        created_at: '2023-12-01T12:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.addShopCredit(shopId, creditData);

      assertions.assertSuccess(response);
      expect(response.data.amount).toBe(creditData.amount);
      expect(response.data.new_balance).toBe(150);
      expect(response.data.transaction_id).toBeDefined();
    });

    it('should generate credit code (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const codeData = {
        amount: 25,
        description: 'Promotional code',
        expires_at: '2023-12-31T23:59:59Z',
      };

      const mockResponse = {
        id: 'code-123',
        code: 'PROMO25',
        amount: codeData.amount,
        description: codeData.description,
        expires_at: codeData.expires_at,
        is_used: false,
        created_at: '2023-12-01T12:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.generateCreditCode(shopId, codeData);

      assertions.assertSuccess(response);
      expect(response.data.code).toBeDefined();
      expect(response.data.amount).toBe(codeData.amount);
      expect(response.data.is_used).toBe(false);
    });

    it('should get shop credit transactions (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const mockResponse = [
        {
          id: 'txn-1',
          customer_id: 'customer-1',
          amount: 50,
          type: 'credit',
          description: 'Loyalty bonus',
          created_at: '2023-12-01T10:00:00Z',
        },
        {
          id: 'txn-2',
          customer_id: 'customer-2',
          amount: -25,
          type: 'debit',
          description: 'Purchase',
          created_at: '2023-12-01T11:00:00Z',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShopCreditTransactions(shopId);

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.data[0].type).toBe('credit');
      expect(response.data[1].type).toBe('debit');
    });
  });

  describe('Shop API Key Management', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get shop API keys (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const mockResponse = [
        {
          id: 'key-1',
          name: 'Production Key',
          key: 'sk_prod_123',
          enabled: true,
          permissions: ['read', 'write'],
          created_at: '2023-12-01T10:00:00Z',
        },
        {
          id: 'key-2',
          name: 'Development Key',
          key: 'sk_dev_456',
          enabled: false,
          permissions: ['read'],
          created_at: '2023-12-01T11:00:00Z',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShopAPIKeys(shopId);

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      assertions.assertValidAPIKey(response.data[0].key);
      assertions.assertValidAPIKey(response.data[1].key);
    });

    it('should create shop API key (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const keyData = {
        name: 'POS Terminal',
        permissions: ['read', 'write'],
      };

      const mockResponse = {
        id: 'key-new-123',
        name: keyData.name,
        key: 'sk_new_789',
        enabled: true,
        permissions: keyData.permissions,
        created_at: '2023-12-01T12:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.createShopAPIKey(shopId, keyData.name, keyData.permissions);

      assertions.assertSuccess(response);
      expect(response.data.name).toBe(keyData.name);
      expect(response.data.permissions).toEqual(keyData.permissions);
      assertions.assertValidAPIKey(response.data.key);
    });

    it('should update shop API key (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const keyId = 'key-123';
      const updateData = {
        name: 'Updated Key Name',
        enabled: false,
      };

      const mockResponse = {
        id: keyId,
        name: updateData.name,
        key: 'sk_updated_123',
        enabled: updateData.enabled,
        permissions: ['read'],
        updated_at: '2023-12-01T13:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.updateShopAPIKey(shopId, keyId, updateData);

      assertions.assertSuccess(response);
      expect(response.data.name).toBe(updateData.name);
      expect(response.data.enabled).toBe(updateData.enabled);
    });

    it('should delete shop API key (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const keyId = 'key-123';
      const mockResponse = {
        message: 'API key deleted successfully',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.deleteShopAPIKey(shopId, keyId);

      assertions.assertSuccess(response);
      expect(response.data.message).toContain('deleted successfully');
    });
  });

  describe('Shop Statistics', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get shop statistics (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();

      const shopId = 'shop-123';
      const mockResponse = {
        total_customers: 150,
        total_credit_issued: 5000,
        total_credit_used: 3500,
        total_transactions: 450,
        active_credit_codes: 5,
        monthly_stats: {
          new_customers: 25,
          credit_issued: 1000,
          credit_used: 750,
          transactions: 120,
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.shops.getShopStats(shopId);

      assertions.assertSuccess(response);
      expect(response.data.total_customers).toBe(150);
      expect(response.data.total_credit_issued).toBe(5000);
      expect(response.data.monthly_stats).toBeDefined();
      expect(response.data.monthly_stats.new_customers).toBe(25);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const sdk = createTestSDKWithAPIKey();

      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const response = await sdk.shops.getShops();

      assertions.assertError(response);
      expect(response.error).toContain('Network error');

      global.fetch = originalFetch;
    });

    it('should handle subscription limits', async () => {
      const sdk = createTestSDKWithAPIKey();

      const restoreFetch = mockFetchAPIError(403, 'Shop limit exceeded for your subscription');

      const response = await sdk.shops.createShop(TEST_SHOP);

      assertions.assertError(response);
      expect(response.error).toContain('Shop limit exceeded');

      restoreFetch();
    });
  });
});
