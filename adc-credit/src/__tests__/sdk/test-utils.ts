/**
 * Test utilities for SDK integration tests
 */

import { ADCCreditSDK } from '@/sdk';
import { ADCCreditConfig } from '@/sdk/types';

// Test configuration
export const TEST_CONFIG = {
  apiUrl: process.env.TEST_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400',
  apiKey: process.env.TEST_API_KEY || 'test-api-key',
  token: process.env.TEST_JWT_TOKEN || 'test-jwt-token',
  debug: true,
};

// Test user data
export const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User',
};

// Test shop data
export const TEST_SHOP = {
  name: 'Test Shop',
  description: 'A test shop for integration testing',
  shop_type: 'retail' as const,
  contact_email: '<EMAIL>',
  contact_phone: '+1234567890',
};

// Test customer data
export const TEST_CUSTOMER = {
  name: 'Test Customer',
  email: '<EMAIL>',
  phone: '+1234567890',
};

/**
 * Creates a test SDK instance with API key authentication
 */
export function createTestSDKWithAPIKey(config?: Partial<ADCCreditConfig>): ADCCreditSDK {
  return new ADCCreditSDK({
    ...TEST_CONFIG,
    token: undefined, // Remove token to use API key
    ...config,
  });
}

/**
 * Creates a test SDK instance with JWT token authentication
 */
export function createTestSDKWithToken(config?: Partial<ADCCreditConfig>): ADCCreditSDK {
  return new ADCCreditSDK({
    ...TEST_CONFIG,
    apiKey: undefined, // Remove API key to use token
    ...config,
  });
}

/**
 * Creates a test SDK instance with no authentication (for testing auth endpoints)
 */
export function createTestSDKNoAuth(config?: Partial<ADCCreditConfig>): ADCCreditSDK {
  return new ADCCreditSDK({
    apiUrl: TEST_CONFIG.apiUrl,
    debug: TEST_CONFIG.debug,
    ...config,
  });
}

/**
 * Waits for a specified amount of time
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generates a unique test identifier
 */
export function generateTestId(): string {
  return `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generates test email with unique identifier
 */
export function generateTestEmail(): string {
  return `test-${generateTestId()}@example.com`;
}

/**
 * Generates test shop name with unique identifier
 */
export function generateTestShopName(): string {
  return `Test Shop ${generateTestId()}`;
}

/**
 * Checks if the backend server is running
 */
export async function isBackendRunning(): Promise<boolean> {
  try {
    const response = await fetch(`${TEST_CONFIG.apiUrl}/api/v1/health`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Skips test if backend is not running
 */
export async function skipIfBackendNotRunning(): Promise<void> {
  const isRunning = await isBackendRunning();
  if (!isRunning) {
    console.warn('Backend server is not running. Skipping integration tests.');
    return;
  }
}

/**
 * Mock response helper for testing error scenarios
 */
export function createMockResponse(data: any, status: number = 200, ok: boolean = true) {
  return {
    ok,
    status,
    statusText: ok ? 'OK' : 'Error',
    json: async () => data,
    text: async () => JSON.stringify(data),
  } as Response;
}

/**
 * Mock fetch for testing network errors
 */
export function mockFetchNetworkError() {
  const originalFetch = global.fetch;
  global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
  return () => {
    global.fetch = originalFetch;
  };
}

/**
 * Mock fetch for testing API errors
 */
export function mockFetchAPIError(status: number, message: string) {
  const originalFetch = global.fetch;
  global.fetch = jest.fn().mockResolvedValue(
    createMockResponse({ error: message }, status, false)
  );
  return () => {
    global.fetch = originalFetch;
  };
}

/**
 * Test data cleanup helper
 */
export class TestDataCleanup {
  private cleanupTasks: Array<() => Promise<void>> = [];

  /**
   * Adds a cleanup task
   */
  addTask(task: () => Promise<void>): void {
    this.cleanupTasks.push(task);
  }

  /**
   * Executes all cleanup tasks
   */
  async cleanup(): Promise<void> {
    for (const task of this.cleanupTasks.reverse()) {
      try {
        await task();
      } catch (error) {
        console.warn('Cleanup task failed:', error);
      }
    }
    this.cleanupTasks = [];
  }
}

/**
 * Assertion helpers for SDK responses
 */
export const assertions = {
  /**
   * Asserts that a response is successful
   */
  assertSuccess<T>(response: { error?: string; data?: T }): asserts response is { data: T } {
    expect(response.error).toBeUndefined();
    expect(response.data).toBeDefined();
  },

  /**
   * Asserts that a response has an error
   */
  assertError<T>(response: { error?: string; data?: T }): asserts response is { error: string } {
    expect(response.error).toBeDefined();
    expect(typeof response.error).toBe('string');
  },

  /**
   * Asserts that a value is a valid UUID
   */
  assertValidUUID(value: any): void {
    expect(typeof value).toBe('string');
    expect(value).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
  },

  /**
   * Asserts that a value is a valid API key
   */
  assertValidAPIKey(value: any): void {
    expect(typeof value).toBe('string');
    expect(value).toMatch(/^sk_[a-zA-Z0-9_]+$/);
  },

  /**
   * Asserts that a value is a valid JWT token
   */
  assertValidJWTToken(value: any): void {
    expect(typeof value).toBe('string');
    expect(value.split('.')).toHaveLength(3);
  },
};
