/**
 * Main SDK Integration Tests
 * Tests the complete SDK functionality and end-to-end workflows
 */

import { ADCCreditSDK } from '@/sdk';
import { 
  TEST_CONFIG,
  TEST_USER,
  TEST_SHOP,
  createTestSDKWith<PERSON>IKey,
  createTestSDKWithToken,
  createTestSDKNoAuth,
  generateTestEmail,
  generateTestShopName,
  generateTestId,
  isBackendRunning,
  skipIfBackendNotRunning,
  assertions,
  TestDataCleanup,
  mockFetchAPIError,
  createMockResponse
} from './test-utils';

describe('SDK Integration Tests', () => {
  let cleanup: TestDataCleanup;

  beforeEach(() => {
    cleanup = new TestDataCleanup();
  });

  afterEach(async () => {
    await cleanup.cleanup();
  });

  describe('SDK Initialization', () => {
    it('should initialize SDK with default configuration', () => {
      const sdk = new ADCCreditSDK();
      expect(sdk).toBeDefined();
      expect(sdk.client).toBeDefined();
      expect(sdk.auth).toBeDefined();
      expect(sdk.credits).toBeDefined();
      expect(sdk.shops).toBeDefined();
      expect(sdk.apiKeys).toBeDefined();
      expect(sdk.users).toBeDefined();
      expect(sdk.usage).toBeDefined();
      expect(sdk.webhooks).toBeDefined();
      expect(sdk.organizations).toBeDefined();
      expect(sdk.merchant).toBeDefined();
      expect(sdk.customer).toBeDefined();
      expect(sdk.subscriptions).toBeDefined();
    });

    it('should initialize SDK with API key configuration', () => {
      const sdk = createTestSDKWithAPIKey();
      expect(sdk).toBeDefined();
      expect(sdk.client).toBeDefined();
    });

    it('should initialize SDK with token configuration', () => {
      const sdk = createTestSDKWithToken();
      expect(sdk).toBeDefined();
      expect(sdk.client).toBeDefined();
    });

    it('should initialize SDK with custom configuration', () => {
      const customConfig = {
        apiUrl: 'https://custom-api.example.com',
        apiKey: 'custom-api-key',
        debug: true,
      };

      const sdk = new ADCCreditSDK(customConfig);
      expect(sdk).toBeDefined();
      expect(sdk.client).toBeDefined();
    });
  });

  describe('End-to-End Workflows', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should complete user registration and shop creation workflow (mocked)', async () => {
      // Step 1: Register new user
      const sdk = createTestSDKNoAuth();
      
      const newUser = {
        email: generateTestEmail(),
        password: 'testpassword123',
        name: 'Test User',
      };

      const registerMockResponse = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token',
        user: {
          id: 'user-123',
          email: newUser.email,
          name: newUser.name,
          picture: '',
          role: 'user',
        },
      };

      let mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(registerMockResponse)
      );
      global.fetch = mockFetch;

      const registerResponse = await sdk.auth.register(newUser.email, newUser.password, newUser.name);
      assertions.assertSuccess(registerResponse);
      expect(registerResponse.data.user.email).toBe(newUser.email);

      // Step 2: Create SDK with token
      const authenticatedSDK = new ADCCreditSDK({
        apiUrl: TEST_CONFIG.apiUrl,
        token: registerResponse.data.token,
        debug: true,
      });

      // Step 3: Create a shop
      const shopData = {
        ...TEST_SHOP,
        name: generateTestShopName(),
      };

      const shopMockResponse = {
        id: 'shop-123',
        ...shopData,
        slug: 'test-shop-123',
        created_at: '2023-12-01T12:00:00Z',
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(shopMockResponse)
      );
      global.fetch = mockFetch;

      const shopResponse = await authenticatedSDK.shops.createShop(shopData);
      assertions.assertSuccess(shopResponse);
      expect(shopResponse.data.name).toBe(shopData.name);
      assertions.assertValidUUID(shopResponse.data.id);

      // Step 4: Get credit balance
      const balanceMockResponse = {
        credit_balance: 1000,
        credit_limit: 5000,
        subscription: {
          id: 'sub-123',
          tier: 'starter',
          status: 'active',
          credits_per_month: 1000,
        },
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(balanceMockResponse)
      );
      global.fetch = mockFetch;

      const balanceResponse = await authenticatedSDK.credits.getBalance();
      assertions.assertSuccess(balanceResponse);
      expect(balanceResponse.data.credit_balance).toBe(1000);
    });

    it('should complete shop customer and credit workflow (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      const shopId = 'shop-123';

      // Step 1: Add customer to shop
      const customerData = {
        name: 'John Doe',
        email: generateTestEmail(),
        phone: '+1234567890',
      };

      const customerMockResponse = {
        id: 'customer-123',
        ...customerData,
        credit_balance: 0,
        created_at: '2023-12-01T12:00:00Z',
      };

      let mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(customerMockResponse)
      );
      global.fetch = mockFetch;

      const customerResponse = await sdk.shops.addShopCustomer(shopId, customerData);
      assertions.assertSuccess(customerResponse);
      expect(customerResponse.data.email).toBe(customerData.email);

      // Step 2: Add credit to customer
      const creditData = {
        customer_id: customerResponse.data.id,
        amount: 100,
        description: 'Welcome bonus',
      };

      const creditMockResponse = {
        transaction_id: 'txn-123',
        customer_id: creditData.customer_id,
        amount: creditData.amount,
        description: creditData.description,
        new_balance: 100,
        created_at: '2023-12-01T12:30:00Z',
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(creditMockResponse)
      );
      global.fetch = mockFetch;

      const creditResponse = await sdk.shops.addShopCredit(shopId, creditData);
      assertions.assertSuccess(creditResponse);
      expect(creditResponse.data.new_balance).toBe(100);

      // Step 3: Generate credit code
      const codeData = {
        amount: 25,
        description: 'Promotional code',
        expires_at: '2023-12-31T23:59:59Z',
      };

      const codeMockResponse = {
        id: 'code-123',
        code: 'PROMO25',
        amount: codeData.amount,
        description: codeData.description,
        expires_at: codeData.expires_at,
        is_used: false,
        created_at: '2023-12-01T13:00:00Z',
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(codeMockResponse)
      );
      global.fetch = mockFetch;

      const codeResponse = await sdk.shops.generateCreditCode(shopId, codeData);
      assertions.assertSuccess(codeResponse);
      expect(codeResponse.data.code).toBeDefined();
      expect(codeResponse.data.amount).toBe(25);
    });

    it('should complete API key management workflow (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      const shopId = 'shop-123';

      // Step 1: Create API key
      const keyData = {
        name: 'POS Terminal',
        permissions: ['read', 'write'],
      };

      const createKeyMockResponse = {
        id: 'key-123',
        name: keyData.name,
        key: 'sk_new_123',
        enabled: true,
        permissions: keyData.permissions,
        created_at: '2023-12-01T12:00:00Z',
      };

      let mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(createKeyMockResponse)
      );
      global.fetch = mockFetch;

      const createKeyResponse = await sdk.shops.createShopAPIKey(shopId, keyData.name, keyData.permissions);
      assertions.assertSuccess(createKeyResponse);
      assertions.assertValidAPIKey(createKeyResponse.data.key);

      // Step 2: Update API key
      const updateData = {
        name: 'Updated POS Terminal',
        enabled: false,
      };

      const updateKeyMockResponse = {
        id: createKeyResponse.data.id,
        name: updateData.name,
        key: createKeyResponse.data.key,
        enabled: updateData.enabled,
        permissions: keyData.permissions,
        updated_at: '2023-12-01T13:00:00Z',
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(updateKeyMockResponse)
      );
      global.fetch = mockFetch;

      const updateKeyResponse = await sdk.shops.updateShopAPIKey(shopId, createKeyResponse.data.id, updateData);
      assertions.assertSuccess(updateKeyResponse);
      expect(updateKeyResponse.data.name).toBe(updateData.name);
      expect(updateKeyResponse.data.enabled).toBe(updateData.enabled);

      // Step 3: Delete API key
      const deleteMockResponse = {
        message: 'API key deleted successfully',
      };

      mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(deleteMockResponse)
      );
      global.fetch = mockFetch;

      const deleteKeyResponse = await sdk.shops.deleteShopAPIKey(shopId, createKeyResponse.data.id);
      assertions.assertSuccess(deleteKeyResponse);
      expect(deleteKeyResponse.data.message).toContain('deleted successfully');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle authentication errors across modules', async () => {
      const sdk = createTestSDKWithAPIKey({ apiKey: 'invalid-key' });
      
      const restoreFetch = mockFetchAPIError(401, 'Invalid API key');

      // Test multiple modules with invalid auth
      const balanceResponse = await sdk.credits.getBalance();
      assertions.assertError(balanceResponse);

      const shopsResponse = await sdk.shops.getShops();
      assertions.assertError(shopsResponse);

      const usageResponse = await sdk.usage.getUsage();
      assertions.assertError(usageResponse);

      restoreFetch();
    });

    it('should handle network errors gracefully', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Network connection failed'));

      const response = await sdk.credits.getBalance();
      assertions.assertError(response);
      expect(response.error).toContain('Network connection failed');

      global.fetch = originalFetch;
    });

    it('should handle rate limiting', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(429, 'Rate limit exceeded');

      const response = await sdk.credits.consume({
        endpoint: '/api/test',
        method: 'GET',
        credits: 1,
        ip_address: '***********',
        user_agent: 'TestApp/1.0',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Rate limit exceeded');

      restoreFetch();
    });
  });

  describe('Real Backend Integration', () => {
    beforeEach(async () => {
      const isRunning = await isBackendRunning();
      if (!isRunning) {
        console.warn('Backend server is not running. Skipping real integration tests.');
        return;
      }
    });

    it('should connect to health endpoint', async () => {
      const isRunning = await isBackendRunning();
      if (!isRunning) {
        console.warn('Skipping test - backend not running');
        return;
      }

      const sdk = new ADCCreditSDK({
        apiUrl: TEST_CONFIG.apiUrl,
      });

      // Health endpoint should be accessible without authentication
      const response = await sdk.client.get('/api/v1/health');
      expect(response.error).toBeUndefined();
      expect(response.data).toBeDefined();
    });
  });
});
