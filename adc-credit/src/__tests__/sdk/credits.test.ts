/**
 * Credits Module Integration Tests
 * Tests the SDK credits functionality
 */

import { 
  createTestSDKWithAPIKey,
  createTestSDKWithToken,
  isBackendRunning,
  skipIfBackendNotRunning,
  assertions,
  TestDataCleanup,
  mockFetchAPIError,
  createMockResponse,
  generateTestId
} from './test-utils';

describe('Credits Module Integration Tests', () => {
  let cleanup: TestDataCleanup;

  beforeEach(() => {
    cleanup = new TestDataCleanup();
  });

  afterEach(async () => {
    await cleanup.cleanup();
  });

  describe('SDK Initialization for Credits', () => {
    it('should create SDK instance with API key for credits', () => {
      const sdk = createTestSDKWithAPIKey();
      expect(sdk.credits).toBeDefined();
    });

    it('should create SDK instance with token for credits', () => {
      const sdk = createTestSDKWithToken();
      expect(sdk.credits).toBeDefined();
    });
  });

  describe('Get Credit Balance', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get credit balance with valid authentication (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockResponse = {
        credit_balance: 1000,
        credit_limit: 5000,
        subscription: {
          id: 'sub-123',
          tier: 'business',
          status: 'active',
          credits_per_month: 5000,
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.getBalance();

      assertions.assertSuccess(response);
      expect(response.data.credit_balance).toBe(1000);
      expect(response.data.credit_limit).toBe(5000);
      expect(response.data.subscription).toBeDefined();
      expect(response.data.subscription.tier).toBe('business');
    });

    it('should handle unauthorized access', async () => {
      const sdk = createTestSDKWithAPIKey({ apiKey: 'invalid-key' });
      
      const restoreFetch = mockFetchAPIError(401, 'Invalid API key');

      const response = await sdk.credits.getBalance();

      assertions.assertError(response);
      expect(response.error).toContain('Invalid API key');

      restoreFetch();
    });

    it('should handle missing authentication', async () => {
      const sdk = createTestSDKWithAPIKey({ apiKey: undefined });
      
      const restoreFetch = mockFetchAPIError(401, 'Authentication required');

      const response = await sdk.credits.getBalance();

      assertions.assertError(response);
      expect(response.error).toContain('Authentication required');

      restoreFetch();
    });
  });

  describe('Get Transaction History', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get transaction history (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockResponse = [
        {
          id: 'txn-1',
          type: 'credit',
          amount: 100,
          description: 'Monthly credit allocation',
          created_at: '2023-12-01T10:00:00Z',
        },
        {
          id: 'txn-2',
          type: 'debit',
          amount: -10,
          description: 'API usage',
          created_at: '2023-12-01T11:00:00Z',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.getTransactions();

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.data[0].type).toBe('credit');
      expect(response.data[1].type).toBe('debit');
    });

    it('should handle empty transaction history', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse([])
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.getTransactions();

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(0);
    });
  });

  describe('Add Credits', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should add credits successfully (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const addCreditsData = {
        amount: 500,
        description: 'Credit top-up',
        payment_method: 'stripe',
        payment_id: 'pi_test_123',
      };

      const mockResponse = {
        transaction_id: 'txn-add-123',
        new_balance: 1500,
        amount_added: 500,
        description: 'Credit top-up',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.add(addCreditsData);

      assertions.assertSuccess(response);
      expect(response.data.amount_added).toBe(500);
      expect(response.data.new_balance).toBe(1500);
      expect(response.data.transaction_id).toBeDefined();
    });

    it('should handle invalid amount', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(400, 'Amount must be positive');

      const response = await sdk.credits.add({
        amount: -100,
        description: 'Invalid amount',
        payment_method: 'stripe',
        payment_id: 'pi_test_123',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Amount must be positive');

      restoreFetch();
    });

    it('should handle payment failure', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(402, 'Payment failed');

      const response = await sdk.credits.add({
        amount: 500,
        description: 'Failed payment',
        payment_method: 'stripe',
        payment_id: 'pi_failed_123',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Payment failed');

      restoreFetch();
    });
  });

  describe('Consume Credits (External API)', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should consume credits successfully (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const consumeData = {
        endpoint: '/api/data',
        method: 'GET',
        credits: 5,
        ip_address: '***********',
        user_agent: 'TestApp/1.0',
      };

      const mockResponse = {
        success: true,
        credit_balance: 995,
        credits_consumed: 5,
        usage: {
          id: 'usage-123',
          endpoint: '/api/data',
          method: 'GET',
          credits: 5,
          timestamp: '2023-12-01T12:00:00Z',
        },
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.consume(consumeData);

      assertions.assertSuccess(response);
      expect(response.data.success).toBe(true);
      expect(response.data.credits_consumed).toBe(5);
      expect(response.data.credit_balance).toBe(995);
      expect(response.data.usage.id).toBeDefined();
    });

    it('should handle insufficient credits', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(402, 'Insufficient credits');

      const response = await sdk.credits.consume({
        endpoint: '/api/expensive',
        method: 'POST',
        credits: 1000,
        ip_address: '***********',
        user_agent: 'TestApp/1.0',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Insufficient credits');

      restoreFetch();
    });

    it('should handle invalid endpoint', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(400, 'Invalid endpoint');

      const response = await sdk.credits.consume({
        endpoint: '',
        method: 'GET',
        credits: 1,
        ip_address: '***********',
        user_agent: 'TestApp/1.0',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Invalid endpoint');

      restoreFetch();
    });

    it('should handle rate limiting', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const restoreFetch = mockFetchAPIError(429, 'Rate limit exceeded');

      const response = await sdk.credits.consume({
        endpoint: '/api/data',
        method: 'GET',
        credits: 1,
        ip_address: '***********',
        user_agent: 'TestApp/1.0',
      });

      assertions.assertError(response);
      expect(response.error).toContain('Rate limit exceeded');

      restoreFetch();
    });
  });

  describe('Scheduled Credits', () => {
    beforeEach(async () => {
      await skipIfBackendNotRunning();
    });

    it('should get next scheduled credit date (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockResponse = {
        next_credit_date: '2023-12-31T00:00:00Z',
        amount: 1000,
        subscription_tier: 'business',
      };

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.getNextScheduledCreditDate();

      assertions.assertSuccess(response);
      expect(response.data.next_credit_date).toBeDefined();
      expect(response.data.amount).toBe(1000);
      expect(response.data.subscription_tier).toBe('business');
    });

    it('should get scheduled credit history (mocked)', async () => {
      const sdk = createTestSDKWithAPIKey();
      
      const mockResponse = [
        {
          id: 'sched-1',
          amount: 1000,
          processed_at: '2023-11-01T00:00:00Z',
          subscription_tier: 'business',
        },
        {
          id: 'sched-2',
          amount: 1000,
          processed_at: '2023-10-01T00:00:00Z',
          subscription_tier: 'business',
        },
      ];

      const mockFetch = jest.fn().mockResolvedValue(
        createMockResponse(mockResponse)
      );
      global.fetch = mockFetch;

      const response = await sdk.credits.getScheduledCreditHistory();

      assertions.assertSuccess(response);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
      expect(response.data[0].amount).toBe(1000);
    });
  });
});
