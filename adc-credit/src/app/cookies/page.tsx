"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON>ie, Shield, BarChart3, Target, Settings2 } from "lucide-react";
import { toast } from "sonner";

interface CookiePreferences {
  essential: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
}

const defaultPreferences: CookiePreferences = {
  essential: true,
  functional: false,
  analytics: false,
  marketing: false,
};

const COOKIE_CONSENT_KEY = "adc-credit-cookie-consent";
const COOKIE_PREFERENCES_KEY = "adc-credit-cookie-preferences";

// Cookie Page Test IDs
const COOKIE_TEST_IDS = {
  CONTAINER: "cookie-settings-container",
  TITLE: "cookie-settings-title",
  OVERVIEW_CARD: "cookie-overview-card",
  ESSENTIAL_CARD: "cookie-essential-card",
  ESSENTIAL_TOGGLE: "cookie-essential-toggle",
  FUNCTIONAL_CARD: "cookie-functional-card", 
  FUNCTIONAL_TOGGLE: "cookie-functional-toggle",
  ANALYTICS_CARD: "cookie-analytics-card",
  ANALYTICS_TOGGLE: "cookie-analytics-toggle",
  MARKETING_CARD: "cookie-marketing-card",
  MARKETING_TOGGLE: "cookie-marketing-toggle",
  SAVE_BUTTON: "cookie-save-button",
  ACCEPT_ALL_BUTTON: "cookie-accept-all-button",
  ESSENTIAL_ONLY_BUTTON: "cookie-essential-only-button",
  INFO_CARD: "cookie-info-card",
  CONTACT_EMAIL: "cookie-contact-email"
} as const;

export default function CookieSettingsPage() {
  const [preferences, setPreferences] = useState<CookiePreferences>(defaultPreferences);
  const [hasExistingConsent, setHasExistingConsent] = useState(false);

  useEffect(() => {
    const savedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY);
    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY);

    setHasExistingConsent(!!hasConsent);

    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.error("Error parsing cookie preferences:", error);
      }
    }
  }, []);

  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: key === "essential" ? true : value, // Essential cookies cannot be disabled
    }));
  };

  const savePreferences = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, "true");
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(preferences));
    toast.success("Cookie preferences saved successfully!");
  };

  const acceptAll = () => {
    const allAccepted: CookiePreferences = {
      essential: true,
      functional: true,
      analytics: true,
      marketing: true,
    };
    setPreferences(allAccepted);
    localStorage.setItem(COOKIE_CONSENT_KEY, "true");
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(allAccepted));
    toast.success("All cookies accepted!");
  };

  const resetToEssential = () => {
    setPreferences(defaultPreferences);
    localStorage.setItem(COOKIE_CONSENT_KEY, "true");
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(defaultPreferences));
    toast.success("Reset to essential cookies only!");
  };

  return (
    <div className="w-full" data-testid={COOKIE_TEST_IDS.CONTAINER}>
      <main className="container py-10 max-w-4xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Cookie className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold" data-testid={COOKIE_TEST_IDS.TITLE}>Cookie Settings</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Manage your cookie preferences and understand how we use cookies to improve your experience.
          </p>
          {hasExistingConsent && (
            <p className="text-sm text-muted-foreground mt-2">
              You can change your cookie preferences at any time. Changes will take effect immediately.
            </p>
          )}
        </div>

        <div className="space-y-6">
          {/* Cookie Overview */}
          <Card data-testid={COOKIE_TEST_IDS.OVERVIEW_CARD}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings2 className="h-5 w-5" />
                What are cookies?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Cookies are small text files that are stored on your device when you visit a website. 
                They help websites remember information about your visit, which can both make it easier 
                to visit the site again and make the site more useful to you.
              </p>
              <p className="text-sm text-muted-foreground">
                We use cookies to enhance your browsing experience, provide personalized content, 
                analyze our traffic, and deliver relevant advertisements. You can control which types 
                of cookies you allow below.
              </p>
            </CardContent>
          </Card>

          {/* Essential Cookies */}
          <Card data-testid={COOKIE_TEST_IDS.ESSENTIAL_CARD}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-600" />
                  <div>
                    <CardTitle>Essential Cookies</CardTitle>
                    <CardDescription>
                      Required for basic website functionality
                    </CardDescription>
                  </div>
                </div>
                <Switch
                  checked={preferences.essential}
                  disabled={true}
                  aria-label="Essential cookies (always enabled)"
                  data-testid={COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                These cookies are necessary for the website to function and cannot be switched off in our systems. 
                They are usually only set in response to actions made by you which amount to a request for services.
              </p>
              <div className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
                <strong>Examples:</strong> Authentication tokens, security cookies, session management, 
                basic functionality, user preferences for essential features.
              </div>
            </CardContent>
          </Card>

          {/* Functional Cookies */}
          <Card data-testid={COOKIE_TEST_IDS.FUNCTIONAL_CARD}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Settings2 className="h-5 w-5 text-blue-600" />
                  <div>
                    <CardTitle>Functional Cookies</CardTitle>
                    <CardDescription>
                      Enable enhanced functionality and personalization
                    </CardDescription>
                  </div>
                </div>
                <Switch
                  checked={preferences.functional}
                  onCheckedChange={(checked) => updatePreference("functional", checked)}
                  aria-label="Functional cookies"
                  data-testid={COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                These cookies enable the website to provide enhanced functionality and personalization. 
                They may be set by us or by third party providers whose services we have added to our pages.
              </p>
              <div className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
                <strong>Examples:</strong> Language preferences, theme settings, dashboard customizations, 
                saved search filters, user interface preferences.
              </div>
            </CardContent>
          </Card>

          {/* Analytics Cookies */}
          <Card data-testid={COOKIE_TEST_IDS.ANALYTICS_CARD}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                  <div>
                    <CardTitle>Analytics Cookies</CardTitle>
                    <CardDescription>
                      Help us understand how visitors use our website
                    </CardDescription>
                  </div>
                </div>
                <Switch
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => updatePreference("analytics", checked)}
                  aria-label="Analytics cookies"
                  data-testid={COOKIE_TEST_IDS.ANALYTICS_TOGGLE}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                These cookies allow us to count visits and traffic sources so we can measure and improve 
                the performance of our site. They help us know which pages are most and least popular 
                and see how visitors move around the site.
              </p>
              <div className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
                <strong>Examples:</strong> Google Analytics, page view tracking, user behavior analysis, 
                performance monitoring, error tracking.
              </div>
            </CardContent>
          </Card>

          {/* Marketing Cookies */}
          <Card data-testid={COOKIE_TEST_IDS.MARKETING_CARD}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Target className="h-5 w-5 text-orange-600" />
                  <div>
                    <CardTitle>Marketing Cookies</CardTitle>
                    <CardDescription>
                      Used to deliver relevant advertisements
                    </CardDescription>
                  </div>
                </div>
                <Switch
                  checked={preferences.marketing}
                  onCheckedChange={(checked) => updatePreference("marketing", checked)}
                  aria-label="Marketing cookies"
                  data-testid={COOKIE_TEST_IDS.MARKETING_TOGGLE}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                These cookies may be set through our site by our advertising partners. They may be used 
                to build a profile of your interests and show you relevant adverts on other sites.
              </p>
              <div className="text-xs text-muted-foreground bg-muted p-3 rounded-md">
                <strong>Examples:</strong> Google Ads, Facebook Pixel, retargeting pixels, 
                advertising campaign tracking, social media integrations.
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button onClick={savePreferences} className="flex-1" data-testid={COOKIE_TEST_IDS.SAVE_BUTTON}>
              Save Preferences
            </Button>
            <Button variant="outline" onClick={acceptAll} className="flex-1" data-testid={COOKIE_TEST_IDS.ACCEPT_ALL_BUTTON}>
              Accept All Cookies
            </Button>
            <Button variant="outline" onClick={resetToEssential} className="flex-1" data-testid={COOKIE_TEST_IDS.ESSENTIAL_ONLY_BUTTON}>
              Essential Only
            </Button>
          </div>

          {/* Additional Information */}
          <Card className="bg-muted/50" data-testid={COOKIE_TEST_IDS.INFO_CARD}>
            <CardHeader>
              <CardTitle className="text-base">Need more information?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm text-muted-foreground">
                For more detailed information about how we use cookies and process your data, 
                please refer to our Privacy Policy.
              </p>
              <p className="text-sm text-muted-foreground">
                If you have any questions about our cookie policy, please contact us at{" "}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline" data-testid={COOKIE_TEST_IDS.CONTACT_EMAIL}>
                  <EMAIL>
                </a>
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}