"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Storefront, CreditCard, QrCode, User, House, CaretRight, Camera } from "phosphor-react";
import Link from "next/link";
import { useGetCustomerShopsQuery } from "@/lib/api/apiSlice";
import { CREDIT_SERVICE_TEST_IDS } from "@/lib/test-ids";

export default function CustomerDashboardPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const { data: shops, isLoading } = useGetCustomerShopsQuery();

  // Calculate total credits across all shops
  const totalCredits = shops?.reduce((sum, shop) => sum + shop.credit_balance, 0) || 0;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.CONTAINER}>
      <div>
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.HEADER}>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.TITLE}>My Credits</h2>
          <div className="flex items-center justify-end gap-1">
            <Button
              className="flex cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 bg-transparent text-[#181510] text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
              variant="ghost"
              onClick={() => router.push("/dashboard/customer/scan")}
              title="Scan QR Code"
              data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SCAN_BUTTON}
            >
              <Camera size={24} weight="regular" className="text-[#181510]" />
            </Button>
            <Button
              className="flex cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 bg-transparent text-[#181510] text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
              variant="ghost"
              onClick={() => router.push("/dashboard/customer/redeem")}
              title="Redeem Code"
              data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.REDEEM_BUTTON}
            >
              <QrCode size={24} weight="regular" className="text-[#181510]" />
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 p-4">
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 bg-[#f1edea]" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.TOTAL_CREDITS_CARD}>
            <p className="text-[#181510] text-base font-medium leading-normal">Total Credits</p>
            <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.TOTAL_CREDITS_VALUE}>{totalCredits}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 bg-[#f1edea]" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOPS_COUNT_CARD}>
            <p className="text-[#181510] text-base font-medium leading-normal">Shops</p>
            <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOPS_COUNT_VALUE}>{shops?.length || 0}</p>
          </div>
        </div>

        <div className="flex justify-between items-center px-4 py-2" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOPS_SECTION}>
          <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em]" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOPS_TITLE}>My Shops</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/dashboard/customer/redeem")}
            data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.REDEEM_CODE_BUTTON}
          >
            <QrCode size={16} className="mr-2" />
            Redeem Code
          </Button>
        </div>

        {isLoading ? (
          <div className="p-4" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.LOADING_STATE}>Loading shops...</div>
        ) : shops && shops.length > 0 ? (
          <div data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOPS_LIST}>
            {shops.map((shop) => (
              <Link href={`/dashboard/customer/shops/${shop.id}`} key={shop.id}>
                <div className="flex items-center gap-4 bg-[#fbfaf9] px-4 min-h-[72px] py-2 justify-between hover:bg-[#f1edea] transition-colors" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOP_ITEM(shop.id)}>
                  <div className="flex items-center gap-4">
                    <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14 bg-[#e6e0db] flex items-center justify-center" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOP_ICON(shop.id)}>
                      <Storefront size={32} weight="regular" className="text-muted-foreground" />
                    </div>
                  <div className="flex flex-col justify-center">
                      <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOP_NAME(shop.id)}>{shop.name}</p>
                      <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOP_CREDITS(shop.id)}>
                        {shop.credit_balance} credits available
                      </p>
                  </div>
                </div>
                  <div className="shrink-0">
                    <div className="text-[#181510] flex size-7 items-center justify-center" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.SHOP_ARROW(shop.id)}>
                      <CaretRight size={24} weight="regular" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.EMPTY_STATE}>
            <p className="text-muted-foreground mb-4" data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.EMPTY_STATE_MESSAGE}>You don't have any shop credits yet.</p>
            <Button onClick={() => router.push("/dashboard/customer/redeem")} data-testid={CREDIT_SERVICE_TEST_IDS.CUSTOMER.DASHBOARD.EMPTY_STATE_BUTTON}>
              Redeem Your First Code
            </Button>
          </div>
        )}
      </div>

      {/* Footer removed in favor of drawer navigation */}
    </div>
  );
}
