"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { Camera, QrCode, CreditCard, Check, X } from "phosphor-react";
import { CustomerBreadcrumbs, createBreadcrumbSegments } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useRedeemCreditCodeMutation } from "@/lib/api/apiSlice";
import { CUSTOMER_SCAN_TEST_IDS } from "@/lib/test-ids";

// Dynamically import the HTML5QrcodeScanner to avoid SSR issues
const Html5QrcodePlugin = dynamic(() => import("@/components/scanner/html5-qrcode-plugin"), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-md h-[300px]">
      <QrCode size={64} className="text-muted-foreground mb-4" />
      <p className="text-muted-foreground">Loading scanner...</p>
    </div>
  ),
});

export default function ScanPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [scanning, setScanning] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<'prompt' | 'granted' | 'denied' | null>(null);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [redeemCode] = useRedeemCreditCodeMutation();
  const [redemptionResult, setRedemptionResult] = useState<{
    success: boolean;
    message: string;
    creditBalance?: number;
    shopName?: string;
  } | null>(null);

  // Handle redeeming the code
  const handleRedeemCode = useCallback(async (code: string) => {
    try {
      const result = await redeemCode({ code }).unwrap();
      const successMessage = "Credit code redeemed successfully!";

      setRedemptionResult({
        success: true,
        message: successMessage,
        creditBalance: result.credit_balance,
        shopName: result.shop_name,
      });

      // Show success toast
      toast({
        title: "Success!",
        description: `${successMessage} Added ${result.credit_balance} credits from ${result.shop_name}.`,
        variant: "default",
      });
    } catch (error: unknown) {
      const errorData = error as { data?: { error?: string; message?: string }; message?: string };
      console.error("Error redeeming code:", error);

      // Handle different types of errors
      let errorMessage = "Failed to redeem code. Please try again.";

      if (errorData.data?.error) {
        errorMessage = errorData.data.error;
      } else if (errorData.data?.message) {
        errorMessage = errorData.data.message;
      } else if (errorData.message) {
        errorMessage = errorData.message;
      }

      // Provide more specific error messages
      if (errorMessage.includes("not found") || errorMessage.includes("already redeemed")) {
        errorMessage = "This credit code is invalid or has already been used.";
      } else if (errorMessage.includes("expired")) {
        errorMessage = "This credit code has expired.";
      } else if (errorMessage.includes("unauthorized")) {
        errorMessage = "You are not authorized to redeem this code.";
      }

      setRedemptionResult({
        success: false,
        message: errorMessage,
      });

      // Show error toast
      toast({
        title: "Redemption Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [redeemCode, toast]);

  // Enhanced QR code parsing to handle multiple formats
  const parseQRCodeData = (decodedText: string): string | null => {
    console.log("Parsing QR code:", decodedText);
    
    // Format 1: Structured format - shop:SHOP_ID;code:CODE;amount:AMOUNT
    if (decodedText.includes(';') && decodedText.includes(':')) {
      const parts = decodedText.split(';');
      const codeData: Record<string, string> = {};

      parts.forEach(part => {
        const [key, value] = part.split(':');
        if (key && value) {
          codeData[key] = value;
        }
      });

      if (codeData.code) {
        return codeData.code;
      }
    }
    
    // Format 2: Direct credit code (12 characters, alphanumeric)
    const directCodeMatch = decodedText.match(/^[A-Za-z0-9]{12}$/);
    if (directCodeMatch) {
      return decodedText;
    }
    
    // Format 3: URL format - extract code from URL parameter
    try {
      const url = new URL(decodedText);
      const codeParam = url.searchParams.get('code') || url.searchParams.get('c');
      if (codeParam && codeParam.match(/^[A-Za-z0-9]{12}$/)) {
        return codeParam;
      }
    } catch {
      // Not a valid URL, continue to next format
    }
    
    // Format 4: JSON format
    try {
      const jsonData = JSON.parse(decodedText);
      if (jsonData.code && typeof jsonData.code === 'string') {
        return jsonData.code;
      }
      if (jsonData.creditCode && typeof jsonData.creditCode === 'string') {
        return jsonData.creditCode;
      }
    } catch {
      // Not valid JSON, continue
    }
    
    // Format 5: Extract any 12-character alphanumeric string from the text
    const codeMatch = decodedText.match(/[A-Za-z0-9]{12}/);
    if (codeMatch) {
      return codeMatch[0];
    }
    
    return null;
  };

  // Handle successful scan
  const onScanSuccess = useCallback((decodedText: string) => {
    setScanning(false);

    // Parse the QR code data using enhanced parser
    const extractedCode = parseQRCodeData(decodedText);
    
    if (extractedCode) {
      setScannedCode(extractedCode);

      // Show immediate feedback that QR code was scanned
      toast({
        title: "QR Code Scanned",
        description: `Processing credit code: ${extractedCode}`,
        variant: "default",
      });

      handleRedeemCode(extractedCode);
    } else {
      console.warn("Could not extract credit code from QR data:", decodedText);
      toast({
        title: "Invalid QR Code",
        description: "The scanned QR code doesn't contain a valid credit code. Please try scanning a credit code QR.",
        variant: "destructive",
      });
    }
  }, [toast, handleRedeemCode]);

  // Handle scan error - only log serious errors
  const onScanError = useCallback((error: unknown) => {
    const errorStr = error?.toString() || "";
    
    // Filter out routine scanning messages that aren't actual errors
    const isRoutineMessage = 
      errorStr.includes("No QR code found") ||
      errorStr.includes("Unable to detect QR code") ||
      errorStr.includes("QR code parse error") ||
      errorStr.includes("NotFoundException") ||
      errorStr.includes("No MultiFormat Readers");
    
    if (!isRoutineMessage) {
      console.error("QR Scanner error:", error);
      
      // Handle permission errors
      if (errorStr.includes("Permission") || errorStr.includes("NotAllowedError")) {
        setCameraPermission('denied');
        toast({
          title: "Camera Permission Required",
          description: "Please allow camera access to scan QR codes",
          variant: "destructive",
        });
      }
    }
  }, [toast]);

  // Check camera permissions
  const checkCameraPermission = async () => {
    try {
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
        setCameraPermission(permission.state);
        
        permission.addEventListener('change', () => {
          setCameraPermission(permission.state);
        });
      }
    } catch (error) {
      console.warn("Could not check camera permissions:", error);
    }
  };
  
  // Reset the scanner
  const resetScanner = () => {
    setScannedCode(null);
    setRedemptionResult(null);
    setScanning(true);
  };
  
  // Start scanning with permission check
  const startScanning = async () => {
    await checkCameraPermission();
    setScanning(true);
  };
  
  // Check permissions on mount
  useEffect(() => {
    checkCameraPermission();
  }, []);

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden" data-testid={CUSTOMER_SCAN_TEST_IDS.CONTAINER}>
      {/* Breadcrumbs */}
      <CustomerBreadcrumbs
        segments={[
          createBreadcrumbSegments.scan(),
        ]}
        data-testid={CUSTOMER_SCAN_TEST_IDS.BREADCRUMBS}
      />
      
      {/* Header */}
      <CustomerPageHeader
        title="Scan QR Code"
        backHref="/dashboard/customer"
        data-testid={CUSTOMER_SCAN_TEST_IDS.HEADER}
      />

      

      {/* Main Content */}
      <div className="flex-1 p-4" data-testid={CUSTOMER_SCAN_TEST_IDS.MAIN_CONTENT}>
        <div className="w-full max-w-md mx-auto">
          <Card data-testid={CUSTOMER_SCAN_TEST_IDS.SCAN_CARD}>
            <CardHeader data-testid={CUSTOMER_SCAN_TEST_IDS.CARD_HEADER}>
              <CardTitle data-testid={CUSTOMER_SCAN_TEST_IDS.CARD_TITLE}>Scan Credit Code</CardTitle>
              <CardDescription data-testid={CUSTOMER_SCAN_TEST_IDS.CARD_DESCRIPTION}>
                Scan a QR code to redeem credits for your account
              </CardDescription>
            </CardHeader>
            <CardContent data-testid={CUSTOMER_SCAN_TEST_IDS.CARD_CONTENT}>
              {!scannedCode && !redemptionResult ? (
                <>
                  {scanning ? (
                    <div data-testid={CUSTOMER_SCAN_TEST_IDS.SCANNER_ACTIVE}>
                      <Html5QrcodePlugin
                        fps={10}
                        qrbox={250}
                        disableFlip={false}
                        qrCodeSuccessCallback={onScanSuccess}
                        qrCodeErrorCallback={onScanError}
                      />
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-md" data-testid={CUSTOMER_SCAN_TEST_IDS.CAMERA_PERMISSION_STATE}>
                      <Camera size={64} className="text-muted-foreground mb-4" data-testid={CUSTOMER_SCAN_TEST_IDS.CAMERA_ICON} />
                      <p className="text-muted-foreground mb-4 text-center" data-testid={CUSTOMER_SCAN_TEST_IDS.PERMISSION_MESSAGE}>
                        {cameraPermission === 'denied' 
                          ? "Camera permission denied. Please enable camera access in your browser settings."
                          : "Camera access required to scan QR codes"
                        }
                      </p>
                      {cameraPermission === 'denied' ? (
                        <div className="text-center" data-testid={CUSTOMER_SCAN_TEST_IDS.PERMISSION_DENIED_ACTIONS}>
                          <Button 
                            onClick={() => {
                              toast({
                                title: "Enable Camera Access",
                                description: "Go to browser settings and allow camera access for this site, then refresh the page.",
                                variant: "default",
                              });
                            }}
                            variant="outline"
                            className="mb-2"
                            data-testid={CUSTOMER_SCAN_TEST_IDS.HELP_BUTTON}
                          >
                            Help with Permissions
                          </Button>
                          <br />
                          <Button onClick={() => window.location.reload()} data-testid={CUSTOMER_SCAN_TEST_IDS.REFRESH_BUTTON}>
                            Refresh Page
                          </Button>
                        </div>
                      ) : (
                        <Button onClick={startScanning} data-testid={CUSTOMER_SCAN_TEST_IDS.START_SCANNER_BUTTON}>
                          Start Scanner
                        </Button>
                      )}
                    </div>
                  )}
                </>
              ) : redemptionResult ? (
                <div className="py-4" data-testid={CUSTOMER_SCAN_TEST_IDS.REDEMPTION_RESULT}>
                  {redemptionResult.success ? (
                    <Alert className="bg-green-50 border-green-200" data-testid={CUSTOMER_SCAN_TEST_IDS.SUCCESS_ALERT}>
                      <Check className="h-5 w-5 text-green-600" data-testid={CUSTOMER_SCAN_TEST_IDS.SUCCESS_ICON} />
                      <AlertTitle className="text-green-800" data-testid={CUSTOMER_SCAN_TEST_IDS.SUCCESS_TITLE}>Success!</AlertTitle>
                      <AlertDescription className="text-green-700" data-testid={CUSTOMER_SCAN_TEST_IDS.SUCCESS_MESSAGE}>
                        {redemptionResult.message}
                        {redemptionResult.shopName && (
                          <p className="mt-2">Shop: {redemptionResult.shopName}</p>
                        )}
                        {redemptionResult.creditBalance !== undefined && (
                          <p className="mt-1">New Balance: {redemptionResult.creditBalance} credits</p>
                        )}
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Alert className="bg-red-50 border-red-200" data-testid={CUSTOMER_SCAN_TEST_IDS.ERROR_ALERT}>
                      <X className="h-5 w-5 text-red-600" data-testid={CUSTOMER_SCAN_TEST_IDS.ERROR_ICON} />
                      <AlertTitle className="text-red-800" data-testid={CUSTOMER_SCAN_TEST_IDS.ERROR_TITLE}>Error</AlertTitle>
                      <AlertDescription className="text-red-700" data-testid={CUSTOMER_SCAN_TEST_IDS.ERROR_MESSAGE}>
                        {redemptionResult.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="py-4" data-testid={CUSTOMER_SCAN_TEST_IDS.PROCESSING_STATE}>
                  <Alert data-testid={CUSTOMER_SCAN_TEST_IDS.PROCESSING_ALERT}>
                    <QrCode className="h-5 w-5" data-testid={CUSTOMER_SCAN_TEST_IDS.PROCESSING_ICON} />
                    <AlertTitle data-testid={CUSTOMER_SCAN_TEST_IDS.PROCESSING_TITLE}>Processing Code</AlertTitle>
                    <AlertDescription data-testid={CUSTOMER_SCAN_TEST_IDS.PROCESSING_MESSAGE}>
                      Redeeming code: {scannedCode}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between" data-testid={CUSTOMER_SCAN_TEST_IDS.CARD_FOOTER}>
              {redemptionResult ? (
                <div className="flex justify-between w-full" data-testid={CUSTOMER_SCAN_TEST_IDS.RESULT_ACTIONS}>
                  <Button variant="outline" onClick={resetScanner} data-testid={CUSTOMER_SCAN_TEST_IDS.SCAN_ANOTHER_BUTTON}>
                    Scan Another Code
                  </Button>
                  <Button onClick={() => router.push("/dashboard/customer")} data-testid={CUSTOMER_SCAN_TEST_IDS.VIEW_CREDITS_BUTTON}>
                    View My Credits
                  </Button>
                </div>
              ) : (
                <div className="space-y-2" data-testid={CUSTOMER_SCAN_TEST_IDS.SCAN_ACTIONS}>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push("/dashboard/customer/redeem")}
                    data-testid={CUSTOMER_SCAN_TEST_IDS.MANUAL_ENTRY_BUTTON}
                  >
                    Enter Code Manually
                  </Button>
                  <div className="text-center" data-testid={CUSTOMER_SCAN_TEST_IDS.TROUBLESHOOTING_SECTION}>
                    <details className="text-xs text-muted-foreground">
                      <summary className="cursor-pointer hover:text-foreground" data-testid={CUSTOMER_SCAN_TEST_IDS.TROUBLESHOOTING_TOGGLE}>
                        Troubleshooting
                      </summary>
                      <div className="mt-2 text-left space-y-1" data-testid={CUSTOMER_SCAN_TEST_IDS.TROUBLESHOOTING_CONTENT}>
                        <p>• Ensure good lighting</p>
                        <p>• Hold camera steady</p>
                        <p>• Clean camera lens</p>
                        <p>• Try different angles</p>
                        <p>• Move closer/farther from code</p>
                      </div>
                    </details>
                  </div>
                </div>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto" data-testid={CUSTOMER_SCAN_TEST_IDS.FOOTER}>
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <Button
              className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#d9b99a]"
              onClick={() => router.push("/dashboard/customer")}
              data-testid={CUSTOMER_SCAN_TEST_IDS.MY_CREDITS_FOOTER_BUTTON}
            >
              <CreditCard className="mr-2 h-5 w-5" data-testid={CUSTOMER_SCAN_TEST_IDS.CREDIT_ICON} />
              <span className="truncate">My Credits</span>
            </Button>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
