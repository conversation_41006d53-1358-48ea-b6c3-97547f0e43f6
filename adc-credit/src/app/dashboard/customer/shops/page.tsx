"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { MagnifyingGlass, Storefront, House } from "phosphor-react";
import { CustomerBreadcrumbs, createBreadcrumbSegments } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useGetCustomerShopsQuery } from "@/lib/api/apiSlice";
import { prefetchShopBySlug } from "@/lib/prefetch";
import { trackSlugPrefetch } from "@/lib/analytics/slug-analytics";
import { CUSTOMER_SHOPS_TEST_IDS } from "@/lib/test-ids";

export default function ShopsPage() {
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch shops data
  const { data: shops, isLoading } = useGetCustomerShopsQuery();

  // Filter shops based on search query
  const filteredShops = shops?.filter(shop =>
    shop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    shop.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden" data-testid={CUSTOMER_SHOPS_TEST_IDS.CONTAINER}>
       {/* Breadcrumbs */}
      <CustomerBreadcrumbs
        segments={[
          createBreadcrumbSegments.customerShops(),
        ]}
        data-testid={CUSTOMER_SHOPS_TEST_IDS.BREADCRUMBS}
      />
      {/* Header */}
      <CustomerPageHeader
        title="Shops"
        backHref="/dashboard/customer"
        data-testid={CUSTOMER_SHOPS_TEST_IDS.HEADER}
      />

     

      {/* Search */}
      <div className="p-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.SEARCH_SECTION}>
        <div className="relative mb-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.SEARCH_CONTAINER}>
          <MagnifyingGlass className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" data-testid={CUSTOMER_SHOPS_TEST_IDS.SEARCH_ICON} />
          <Input
            className="pl-10 bg-[#f1edea] border-none h-12"
            placeholder="Search shops..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            data-testid={CUSTOMER_SHOPS_TEST_IDS.SEARCH_INPUT}
          />
        </div>

        {/* Shops List */}
        <div className="space-y-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOPS_LIST}>
          {isLoading ? (
            // Loading state
            <div data-testid={CUSTOMER_SHOPS_TEST_IDS.LOADING_STATE}>
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="animate-pulse">
                    <div className="h-24 bg-gray-200"></div>
                    <CardContent className="p-4">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          ) : filteredShops && filteredShops.length > 0 ? (
            // Shops list
            filteredShops.map((shop) => (
              <Card key={shop.id} className="overflow-hidden" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_CARD(shop.id)}>
                <Link 
                  href={`/dashboard/customer/shops/${shop.slug}`}
                  onMouseEnter={() => {
                    prefetchShopBySlug(shop.slug);
                    trackSlugPrefetch(shop.slug, 'hover');
                  }}
                  data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_LINK(shop.id)}
                >
                  <div className="relative h-24 w-full bg-[#f1edea]" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_IMAGE_CONTAINER(shop.id)}>
                    {shop.image_url ? (
                      <Image
                        src={shop.image_url}
                        alt={shop.name}
                        fill
                        className="object-cover"
                        data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_IMAGE(shop.id)}
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_PLACEHOLDER(shop.id)}>
                        <Storefront size={48} className="text-muted-foreground" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_PLACEHOLDER_ICON(shop.id)} />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_CONTENT(shop.id)}>
                    <h3 className="text-[#181510] text-lg font-bold mb-1" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_NAME(shop.id)}>{shop.name}</h3>
                    {shop.description && (
                      <p className="text-muted-foreground text-sm line-clamp-2" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_DESCRIPTION(shop.id)}>{shop.description}</p>
                    )}
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between items-center" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_FOOTER(shop.id)}>
                    <div className="text-sm text-muted-foreground" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_CREDITS_INFO(shop.id)}>
                      {shop.credit_balance ? (
                        <span data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_CREDITS_VALUE(shop.id)}>Your credits: <strong>{shop.credit_balance}</strong></span>
                      ) : (
                        <span data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_NO_CREDITS(shop.id)}>No credits yet</span>
                      )}
                    </div>
                    <Button variant="ghost" size="sm" className="text-[#181510]" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOP_VIEW_BUTTON(shop.id)}>
                      View Shop
                    </Button>
                  </CardFooter>
                </Link>
              </Card>
            ))
          ) : (
            // No shops or no search results
            <div className="text-center py-8" data-testid={CUSTOMER_SHOPS_TEST_IDS.EMPTY_STATE}>
              <Storefront size={48} className="mx-auto text-muted-foreground mb-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.EMPTY_STATE_ICON} />
              <h3 className="text-[#181510] text-lg font-bold mb-2" data-testid={CUSTOMER_SHOPS_TEST_IDS.EMPTY_STATE_TITLE}>
                {searchQuery ? "No shops found" : "No shops available"}
              </h3>
              <p className="text-muted-foreground mb-4" data-testid={CUSTOMER_SHOPS_TEST_IDS.EMPTY_STATE_MESSAGE}>
                {searchQuery
                  ? `No shops match "${searchQuery}"`
                  : "There are no shops available at the moment."}
              </p>
              {searchQuery && (
                <Button variant="outline" onClick={() => setSearchQuery("")} data-testid={CUSTOMER_SHOPS_TEST_IDS.CLEAR_SEARCH_BUTTON}>
                  Clear Search
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto" data-testid={CUSTOMER_SHOPS_TEST_IDS.FOOTER}>
        <div className="flex gap-1 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2" data-testid={CUSTOMER_SHOPS_TEST_IDS.NAVIGATION_BAR}>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-muted-foreground" href="/dashboard/customer" data-testid={CUSTOMER_SHOPS_TEST_IDS.HOME_LINK}>
            <div className="text-muted-foreground flex h-8 items-center justify-center" data-testid={CUSTOMER_SHOPS_TEST_IDS.HOME_ICON_CONTAINER}>
              <House size={24} weight="regular" data-testid={CUSTOMER_SHOPS_TEST_IDS.HOME_ICON} />
            </div>
            <p className="text-muted-foreground text-xs font-medium leading-normal tracking-[0.015em]" data-testid={CUSTOMER_SHOPS_TEST_IDS.HOME_LABEL}>Home</p>
          </Link>
          <div className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOPS_TAB_ACTIVE}>
            <div className="text-[#181510] flex h-8 items-center justify-center" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOPS_ICON_CONTAINER_ACTIVE}>
              <Storefront size={24} weight="fill" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOPS_ICON_ACTIVE} />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]" data-testid={CUSTOMER_SHOPS_TEST_IDS.SHOPS_LABEL_ACTIVE}>Shops</p>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
