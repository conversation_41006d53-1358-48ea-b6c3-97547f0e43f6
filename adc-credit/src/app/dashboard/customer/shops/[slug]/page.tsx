"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { use } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { Storefront } from "phosphor-react";
import { CustomerBreadcrumbs, createBreadcrumbSegments } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { getSlugErrorMessage } from "@/lib/utils";
import { useCustomerShopBySlug } from "@/hooks/useShopBySlug";
import { trackSlugView, trackSlugError, trackSlugRedirect } from "@/lib/analytics/slug-analytics";
import { CUSTOMER_SHOP_DETAIL_TEST_IDS } from "@/lib/test-ids";
import {
  useGetCustomerShopQuery,
  useGetCustomerTransactionsQuery,
  useUseShopCreditMutation
} from "@/lib/api/apiSlice";

// Import the new granular components
import { CustomerShopProfileCard } from "@/components/shop/customer/CustomerShopProfileCard";
import { CreditBalanceCard } from "@/components/shop/customer/CreditBalanceCard";
import { RecentActivityCard } from "@/components/shop/customer/RecentActivityCard";
import { TransactionHistory } from "@/components/shop/customer/TransactionHistory";
import { QRScannerDialog } from "@/components/shop/customer/QRScannerDialog";
import { UseCreditDialog } from "@/components/shop/customer/UseCreditDialog";

export default function CustomerShopDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const shopSlug = unwrappedParams.slug;
  const router = useRouter();
  const { toast } = useToast();

  // Use the custom hook for better slug handling
  const {
    shopId,
    isShopLoading,
    shopError,
    isValidSlug,
    isUUID
  } = useCustomerShopBySlug(shopSlug);

  // Early redirect for UUID-based URLs
  useEffect(() => {
    if (isUUID) {
      trackSlugRedirect(shopSlug, '/dashboard/customer/shops');
      router.replace('/dashboard/customer/shops');
    }
  }, [isUUID, router, shopSlug]);

  // Fetch customer-specific data when shop ID is available
  const { data: shopData, isLoading: isCustomerDataLoading, error: shopDataError, refetch: refetchCustomerData } = useGetCustomerShopQuery(shopId!, { skip: !shopId });
  const { data: transactions, isLoading: isTransactionsLoading, refetch: refetchTransactions } = useGetCustomerTransactionsQuery(shopId!, { skip: !shopId });
  const [triggerCreditUse, { isLoading: isUsingCredit }] = useUseShopCreditMutation();

  // Extract shop and credit balance from the response
  const shop = shopData ? { ...shopData.shop, credit_balance: shopData.credit_balance } : null;
  
  // Combined loading state
  const isLoading = isShopLoading || isCustomerDataLoading;

  // Update page title and track analytics when shop data is loaded
  useEffect(() => {
    if (shop?.name) {
      document.title = `${shop.name} - Customer Dashboard`;
      trackSlugView(shopSlug);
    }
  }, [shop?.name, shopSlug]);

  // Track errors
  useEffect(() => {
    if (!shopSlug) return;
    
    if (shopError) {
      const errorMessage = 'message' in shopError ? (shopError.message || 'Unknown error') : 'Unknown error';
      trackSlugError(shopSlug, errorMessage, 'network');
    } else if (shopDataError) {
      const errorMessage = 'message' in shopDataError ? (shopDataError.message || 'Customer data error') : 'Customer data error';
      trackSlugError(shopSlug, errorMessage, 'network');
    } else if (!isValidSlug) {
      trackSlugError(shopSlug, 'Invalid slug format', 'invalid');
    }
  }, [shopError, shopDataError, isValidSlug, shopSlug]);

  // Dialog states
  const [isUseDialogOpen, setIsUseDialogOpen] = useState(false);
  const [isScanDialogOpen, setIsScanDialogOpen] = useState(false);
  const [scannedAmount, setScannedAmount] = useState(0);

  // Handle QR code scan success
  const handleQRScanSuccess = (amount: number) => {
    setScannedAmount(amount);
    setIsScanDialogOpen(false);
    setIsUseDialogOpen(true);
  };

  // Handle using shop credits
  const handleUseCredit = async (amount: number, description: string) => {
    await triggerCreditUse({
      shopId: shopId!,
      amount,
      description
    }).unwrap();

    toast({
      title: "Success",
      description: `${amount} credits used successfully`,
    });

    // Refresh data after successful credit use
    refetchCustomerData();
    refetchTransactions();
    setScannedAmount(0);
  };

  // Breadcrumb segments using utility functions
  const breadcrumbSegments = [
    createBreadcrumbSegments.dashboard('customer'),
    createBreadcrumbSegments.customerShops(),
    createBreadcrumbSegments.customerShopDetail(shopSlug, shop?.name || 'Shop Details'),
  ];

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.LOADING_CONTAINER}>
        {/* Breadcrumbs - Desktop only */}
        <div className="hidden md:block">
          <CustomerBreadcrumbs segments={breadcrumbSegments} />
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <CustomerPageHeader
            title="Shop Details"
            backHref="/dashboard/customer/shops"
          />
        </div>

        <div className="p-4">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-12 w-24" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state - shop not found, invalid slug, or network error
  if (!isLoading && (!isValidSlug || shopError || shopDataError || !shop)) {
    return (
      <div className="space-y-6" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.ERROR_CONTAINER}>
        {/* Breadcrumbs - Desktop only */}
        <div className="hidden md:block">
          <CustomerBreadcrumbs segments={breadcrumbSegments} />
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <CustomerPageHeader
            title="Shop Not Found"
            backHref="/dashboard/customer/shops"
          />
        </div>

        <div className="p-4 text-center">
          <Card>
            <CardContent className="pt-6 pb-6">
              <Storefront size={48} className="mx-auto text-muted-foreground mb-4" />
              <h3 className="text-foreground text-lg font-bold mb-2" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.ERROR_TITLE}>Shop Not Found</h3>
              <p className="text-muted-foreground mb-4" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.ERROR_MESSAGE}>
                {getSlugErrorMessage(shopSlug)}
              </p>
              <Button
                className="bg-primary text-primary-foreground hover:bg-primary/90"
                onClick={() => router.push('/dashboard/customer/shops')}
                data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.ERROR_BACK_BUTTON}
              >
                Back to Shops
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Type guard - ensure shop is not null before rendering
  if (!shop) {
    return (
      <div className="space-y-6">
        <div className="p-4 text-center">
          <Card>
            <CardContent className="pt-6 pb-6">
              <Storefront size={48} className="mx-auto text-muted-foreground mb-4" />
              <h3 className="text-foreground text-lg font-bold mb-2">Loading Shop Data</h3>
              <p className="text-muted-foreground mb-4">Please wait while we load the shop information...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.CONTAINER}>
      {/* Breadcrumbs - Desktop only */}
      <div className="hidden md:block">
        <CustomerBreadcrumbs segments={breadcrumbSegments} />
      </div>

      {/* Mobile Header */}
      <div className="md:hidden">
        <CustomerPageHeader
          title={shop.name}
          backHref="/dashboard/customer/shops"
        />
      </div>

      {/* Page Header - Desktop only */}
      <div className="hidden md:block px-4" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.HEADER}>
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl text-foreground" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.SHOP_NAME}>
          {shop.name}
        </h1>
        <p className="text-muted-foreground mt-1" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.SHOP_DESCRIPTION}>
          {shop.description || "Shop details and credit information"}
        </p>
      </div>

      {/* Main Content */}
      <div className="md:max-w-6xl md:mx-auto md:px-6" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.MAIN_CONTENT}>
        {/* Shop Profile Card */}
        <CustomerShopProfileCard shop={shop} />

        {/* Credit Balance and Recent Activity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6" data-testid={CUSTOMER_SHOP_DETAIL_TEST_IDS.CREDIT_ACTIVITY_SECTION}>
          <CreditBalanceCard
            creditBalance={shop.credit_balance}
            onScanQRCode={() => setIsScanDialogOpen(true)}
            onEnterManually={() => setIsUseDialogOpen(true)}
          />
          <RecentActivityCard
            transactions={transactions}
            isLoading={isTransactionsLoading}
          />
        </div>

        {/* Transaction History */}
        <TransactionHistory
          transactions={transactions}
          isLoading={isTransactionsLoading}
        />
      </div>

      {/* Dialogs */}
      <UseCreditDialog
        isOpen={isUseDialogOpen}
        onClose={() => {
          setIsUseDialogOpen(false);
          setScannedAmount(0);
        }}
        shop={shop}
        isLoading={isUsingCredit}
        onSubmit={handleUseCredit}
        initialAmount={scannedAmount}
      />

      <QRScannerDialog
        isOpen={isScanDialogOpen}
        onClose={() => setIsScanDialogOpen(false)}
        onScanSuccess={handleQRScanSuccess}
        shopId={shopId}
      />
    </div>
  );
}