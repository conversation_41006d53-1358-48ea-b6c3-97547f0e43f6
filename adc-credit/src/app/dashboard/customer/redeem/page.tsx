"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Qr<PERSON>ode, Scan, CreditCard, Camera } from "phosphor-react";
import { CustomerBreadcrumbs, createBreadcrumbSegments } from "@/components/navigation/breadcrumbs";
import { CustomerPageHeader } from "@/components/layout/page-header-mobile";
import { useRedeemCreditCodeMutation } from "@/lib/api/apiSlice";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { CUSTOMER_REDEEM_TEST_IDS } from "@/lib/test-ids";

export default function RedeemCodePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [code, setCode] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const [redeemCode, { isLoading }] = useRedeemCreditCodeMutation();
  const [redemptionResult, setRedemptionResult] = useState<{
    success: boolean;
    message: string;
    creditBalance?: number;
    shopName?: string;
  } | null>(null);

  const handleRedeemCode = async () => {
    if (!code.trim()) {
      toast({
        title: "Error",
        description: "Please enter a credit code",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await redeemCode({ code }).unwrap();
      setRedemptionResult({
        success: true,
        message: "Credit code redeemed successfully!",
        creditBalance: result.credit_balance,
        shopName: result.shop_name,
      });
      setCode("");
    } catch (error: any) {
      setRedemptionResult({
        success: false,
        message: error.data?.message || "Failed to redeem code. Please try again.",
      });
    }
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden" data-testid={CUSTOMER_REDEEM_TEST_IDS.CONTAINER}>
      <div data-testid={CUSTOMER_REDEEM_TEST_IDS.MAIN_CONTENT}>
          {/* Breadcrumbs */}
        <CustomerBreadcrumbs
          segments={[
            createBreadcrumbSegments.redeem(),
          ]}
          data-testid={CUSTOMER_REDEEM_TEST_IDS.BREADCRUMBS}
        />
        <CustomerPageHeader
          title="Redeem Credit Code"
          backHref="/dashboard/customer"
          data-testid={CUSTOMER_REDEEM_TEST_IDS.HEADER}
        />

        <div className="p-4" data-testid={CUSTOMER_REDEEM_TEST_IDS.FORM_SECTION}>
          <Card data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_CARD}>
            <CardHeader data-testid={CUSTOMER_REDEEM_TEST_IDS.CARD_HEADER}>
              <CardTitle data-testid={CUSTOMER_REDEEM_TEST_IDS.CARD_TITLE}>Redeem a Credit Code</CardTitle>
              <CardDescription data-testid={CUSTOMER_REDEEM_TEST_IDS.CARD_DESCRIPTION}>
                Enter a credit code or scan a QR code to add credits to your account
              </CardDescription>
            </CardHeader>
            <CardContent data-testid={CUSTOMER_REDEEM_TEST_IDS.CARD_CONTENT}>
              <div className="space-y-4" data-testid={CUSTOMER_REDEEM_TEST_IDS.FORM_FIELDS}>
                <div className="grid gap-2" data-testid={CUSTOMER_REDEEM_TEST_IDS.CODE_INPUT_GROUP}>
                  <Label htmlFor="code" data-testid={CUSTOMER_REDEEM_TEST_IDS.CODE_LABEL}>Credit Code</Label>
                  <div className="flex gap-2" data-testid={CUSTOMER_REDEEM_TEST_IDS.INPUT_WITH_SCAN}>
                    <Input
                      id="code"
                      placeholder="Enter credit code"
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                      data-testid={CUSTOMER_REDEEM_TEST_IDS.CODE_INPUT}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => router.push("/dashboard/customer/scan")}
                      data-testid={CUSTOMER_REDEEM_TEST_IDS.SCAN_BUTTON}
                    >
                      <Camera size={20} weight="regular" data-testid={CUSTOMER_REDEEM_TEST_IDS.SCAN_ICON} />
                    </Button>
                  </div>
                </div>

                {isScanning && (
                  <div className="p-4 border rounded-md bg-muted text-center" data-testid={CUSTOMER_REDEEM_TEST_IDS.SCANNING_STATE}>
                    <QrCode size={64} className="mx-auto mb-2" data-testid={CUSTOMER_REDEEM_TEST_IDS.SCANNING_ICON} />
                    <p data-testid={CUSTOMER_REDEEM_TEST_IDS.SCANNING_MESSAGE}>Scanning QR code...</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsScanning(false)}
                      className="mt-2"
                      data-testid={CUSTOMER_REDEEM_TEST_IDS.CANCEL_SCANNING_BUTTON}
                    >
                      Cancel
                    </Button>
                  </div>
                )}

                {redemptionResult && (
                  <div
                    className={`p-4 border rounded-md ${
                      redemptionResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                    }`}
                    data-testid={redemptionResult.success ? CUSTOMER_REDEEM_TEST_IDS.SUCCESS_RESULT : CUSTOMER_REDEEM_TEST_IDS.ERROR_RESULT}
                  >
                    <p
                      className={`font-medium ${
                        redemptionResult.success ? "text-green-700" : "text-red-700"
                      }`}
                      data-testid={CUSTOMER_REDEEM_TEST_IDS.RESULT_STATUS}
                    >
                      {redemptionResult.success ? "Success!" : "Error"}
                    </p>
                    <p className="mt-1" data-testid={CUSTOMER_REDEEM_TEST_IDS.RESULT_MESSAGE}>{redemptionResult.message}</p>
                    {redemptionResult.success && redemptionResult.creditBalance !== undefined && (
                      <div className="mt-2" data-testid={CUSTOMER_REDEEM_TEST_IDS.SUCCESS_DETAILS}>
                        <p data-testid={CUSTOMER_REDEEM_TEST_IDS.BALANCE_INFO}>
                          New balance at {redemptionResult.shopName}: {redemptionResult.creditBalance} credits
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter data-testid={CUSTOMER_REDEEM_TEST_IDS.CARD_FOOTER}>
              <Button
                className="w-full"
                onClick={handleRedeemCode}
                disabled={isLoading || !code.trim() || isScanning}
                data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_BUTTON}
              >
                {isLoading ? "Redeeming..." : "Redeem Code"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <div data-testid={CUSTOMER_REDEEM_TEST_IDS.FOOTER}>
        <div className="flex gap-2 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2" data-testid={CUSTOMER_REDEEM_TEST_IDS.NAVIGATION_BAR}>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-muted-foreground" href="/dashboard/customer" data-testid={CUSTOMER_REDEEM_TEST_IDS.BACK_LINK}>
            <div className="text-muted-foreground flex h-8 items-center justify-center" data-testid={CUSTOMER_REDEEM_TEST_IDS.BACK_ICON_CONTAINER}>
              <ArrowLeft size={24} weight="regular" data-testid={CUSTOMER_REDEEM_TEST_IDS.BACK_ICON} />
            </div>
            <p className="text-muted-foreground text-xs font-medium leading-normal tracking-[0.015em]" data-testid={CUSTOMER_REDEEM_TEST_IDS.BACK_LABEL}>Back</p>
          </Link>
          <div className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_TAB_ACTIVE}>
            <div className="text-[#181510] flex h-8 items-center justify-center" data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_ICON_CONTAINER_ACTIVE}>
              <CreditCard size={24} weight="fill" data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_ICON_ACTIVE} />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]" data-testid={CUSTOMER_REDEEM_TEST_IDS.REDEEM_LABEL_ACTIVE}>Redeem</p>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
