"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Plus, User } from "phosphor-react";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {
  useGetShopBySlugQuery,
  useGetShopCustomersQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation
} from "@/lib/api/apiSlice";
import { ShopCustomer } from "@/types";

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, { message: "Customer name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().optional(),
  username: z.string().min(1, { message: "Username is required" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  initial_credits: z.number().min(0, { message: "Initial credits must be 0 or more" }),
  send_welcome_email: z.boolean(),
});

const inviteFormSchema = z.object({
  name: z.string().min(1, { message: "Customer name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().optional(),
  initial_credits: z.number().min(0, { message: "Initial credits must be 0 or more" }),
  welcome_message: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;
type InviteFormValues = z.infer<typeof inviteFormSchema>;

export default function CustomersPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const { toast } = useToast();

  // Fetch shop and customer data
  const { data: shop, isLoading: isShopLoading } = useGetShopBySlugQuery(params.slug);
  const shopId = shop?.id;
  const { data: customersData, isLoading: isCustomersLoading, refetch: refetchCustomers } = useGetShopCustomersQuery({ shopId: shopId! }, { skip: !shopId });
  
  // Extract customers from the response
  const customers = customersData?.customers;

  // Mutations
  const [addCustomer, { isLoading: isAddingCustomer }] = useAddShopCustomerMutation();
  const [addCredit] = useAddShopCreditMutation();

  // Dialog state
  const [isAddCustomerDialogOpen, setIsAddCustomerDialogOpen] = useState(false);
  const [isInviteCustomerDialogOpen, setIsInviteCustomerDialogOpen] = useState(false);

  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      username: "",
      password: "",
      initial_credits: 50,
      send_welcome_email: true,
    },
  });

  const inviteForm = useForm<InviteFormValues>({
    resolver: zodResolver(inviteFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      initial_credits: 50,
      welcome_message: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      const response = await fetch(`/api/shops/${shopId}/customers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create customer');
      }

      const customer = await response.json();

      toast({
        title: "Customer created!",
        description: `${customer.name} has been added with username: ${customer.username}`,
      });

      setIsAddCustomerDialogOpen(false);
      form.reset();
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add customer",
        variant: "destructive",
      });
    }
  };

  const onInviteSubmit = async (data: InviteFormValues) => {
    try {
      const response = await fetch(`/api/shops/${shopId}/customers/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          welcome_message: data.welcome_message || 
            `Welcome to ${shop?.name}! You've received ${data.initial_credits} credits to get started.`
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send invitation');
      }

      const result = await response.json();

      toast({
        title: "Invitation sent!",
        description: `Invitation sent to ${result.email}`,
      });

      setIsInviteCustomerDialogOpen(false);
      inviteForm.reset();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to send invitation",
        variant: "destructive",
      });
    }
  };

  const handleAddCredit = async (customerId: string) => {
    router.push(`/dashboard/merchant/${params.slug}/customers/${customerId}/add-credit`);
  };

  if (isShopLoading) {
    return <div className="p-4 text-center">Loading shop details...</div>;
  }

  if (!shop) {
    return <div className="p-4 text-center">Shop not found</div>;
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <MerchantPageHeader
          title="Customers"
          backHref={`/dashboard/merchant/${params.slug}`}
        />

        {/* Breadcrumbs */}
        <MerchantBreadcrumbs
          segments={[
            { label: "Shops", href: "/dashboard/merchant" },
            { label: shop?.name || "Shop", href: `/dashboard/merchant/${params.slug}` },
            { label: "Customers", href: `/dashboard/merchant/${params.slug}/customers`, isCurrentPage: true }
          ]}
        />

        {isCustomersLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#e5ccb2]"></div>
          </div>
        ) : customers && customers.length > 0 ? (
          <div>
            {customers.map((customer: ShopCustomer) => (
              <div key={customer.id} className="flex items-center gap-4 bg-[#fbfaf9] px-4 min-h-[72px] py-2">
                <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-14 flex items-center justify-center bg-[#e6e0db]">
                  <User size={24} weight="regular" className="text-muted-foreground" />
                </div>
                <div className="flex flex-col justify-center flex-1">
                  <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">{customer.user?.name}</p>
                  <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">{customer.user?.email}</p>
                </div>
                <div className="text-[#181510] text-base font-medium">
                  ${customer.credit_balance}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center">
            <User size={48} weight="regular" className="text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">No customers yet</p>
            <p className="text-muted-foreground text-sm mb-4">Add your first customer to get started</p>
          </div>
        )}
      </div>

      <div>
        <div className="flex justify-end gap-2 overflow-hidden px-5 pb-5">
          <Button
            variant="outline"
            onClick={() => setIsInviteCustomerDialogOpen(true)}
          >
            Send Invite
          </Button>
          <Button
            className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-14 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] min-w-0 gap-4 pl-4 pr-6"
            onClick={() => setIsAddCustomerDialogOpen(true)}
          >
            <Plus size={24} weight="regular" className="text-[#181510]" />
            <span className="truncate">Add Customer</span>
          </Button>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>

      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerDialogOpen} onOpenChange={setIsAddCustomerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your shop
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer login username" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Initial password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="initial_credits"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Credits</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field} 
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsAddCustomerDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isAddingCustomer}>
                  {isAddingCustomer ? "Adding..." : "Create Customer"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Invite Customer Dialog */}
      <Dialog open={isInviteCustomerDialogOpen} onOpenChange={setIsInviteCustomerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Customer</DialogTitle>
            <DialogDescription>
              Send an email invitation to a new customer
            </DialogDescription>
          </DialogHeader>

          <Form {...inviteForm}>
            <form onSubmit={inviteForm.handleSubmit(onInviteSubmit)} className="space-y-4">
              <FormField
                control={inviteForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={inviteForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={inviteForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="+****************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={inviteForm.control}
                name="initial_credits"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Credits</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field} 
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsInviteCustomerDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Send Invitation
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
