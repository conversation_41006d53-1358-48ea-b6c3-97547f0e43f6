"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useGetShopQuery } from '@/lib/api/apiSlice';
import { Skeleton } from '@/components/ui/skeleton';

export default function ShopDetailRedirectPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const unwrappedParams = React.use(params as any) as { slug: string };
  const { data: shop, isLoading, error } = useGetShopQuery(unwrappedParams.slug);

  useEffect(() => {
    if (shop?.slug) {
      // Redirect to slug-based URL
      router.replace(`/dashboard/merchant/shop/${shop.slug}`);
    } else if (error) {
      // Redirect to merchant dashboard if shop not found
      router.replace('/dashboard/merchant');
    }
  }, [shop, error, router]);

  // Show loading state while redirecting
  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      <div className="p-4 text-center">
        <div className="flex items-center justify-center">
          <Skeleton className="h-32 w-32 rounded-full" />
        </div>
        <Skeleton className="mt-4 h-6 w-48 mx-auto" />
        <Skeleton className="mt-2 h-4 w-64 mx-auto" />
        <p className="mt-4 text-muted-foreground">Redirecting to shop...</p>
      </div>
    </div>
  );
}
