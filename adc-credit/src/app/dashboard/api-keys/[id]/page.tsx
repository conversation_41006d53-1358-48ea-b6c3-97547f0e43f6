"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { APIKey } from "@/types";
import { toast } from "sonner";
import { useGetAPIKeysQuery, useUpdateAPIKeyMutation } from "@/lib/api/apiSlice";
import { ArrowLeft, Save } from "lucide-react";
import Link from "next/link";
import { CREDIT_SERVICE_TEST_IDS } from "@/lib/test-ids";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define the form schema with Zod
const formSchema = z.object({
  permissions: z.array(z.string()).optional(),
});

// Define available permissions
const availablePermissions = [
  { id: "read", label: "Read" },
  { id: "write", label: "Write" },
  { id: "delete", label: "Delete" },
  { id: "admin", label: "Admin" },
];

export default function APIKeyDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const [apiKey, setApiKey] = useState<APIKey | null>(null);
  const [updateError, setUpdateError] = useState<string | null>(null);

  // Get the API key ID from the URL
  const apiKeyId = params.id as string;

  // Use RTK Query hooks
  const { data: apiKeys = [], isLoading } = useGetAPIKeysQuery();
  const [updateAPIKey, { isLoading: isUpdating }] = useUpdateAPIKeyMutation();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      permissions: [],
    },
  });

  // Find the API key in the list
  useEffect(() => {
    if (apiKeys.length > 0) {
      const foundKey = apiKeys.find(key => key.id === apiKeyId);
      if (foundKey) {
        setApiKey(foundKey);
        // Set form default values
        form.reset({
          permissions: foundKey.permissions || [],
        });
      } else {
        // API key not found, redirect to the API keys list
        toast.error("API key not found");
        router.push("/dashboard/api-keys");
      }
    }
  }, [apiKeys, apiKeyId, router, form]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setUpdateError(null);

    try {
      await updateAPIKey({
        id: apiKeyId,
        permissions: values.permissions,
      }).unwrap();

      // Show success message
      toast.success("API key permissions updated successfully");

      // Redirect to the API keys list page after successful update
      router.push("/dashboard/api-keys?updated=true");
    } catch (error: any) {
      console.error("Error updating API key:", error);

      if (error.status === 401) {
        setUpdateError("Authentication error: Please log in again");
        toast.error("Authentication error: Please log in again");
      } else if (error.data?.error) {
        setUpdateError(error.data.error);
        toast.error(`Error: ${error.data.error}`);
      } else {
        setUpdateError("Failed to update API key");
        toast.error("Failed to update API key");
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading || isUpdating) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center" data-testid="api-key-detail-loading-container">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary" data-testid="api-key-detail-loading-spinner"></div>
      </div>
    );
  }

  return (
    <div className="container py-10" data-testid="api-key-detail-container">
      <div className="flex items-center mb-8" data-testid="api-key-detail-header">
        <Link href="/dashboard/api-keys">
          <Button variant="outline" size="sm" className="mr-4" data-testid="api-key-detail-back-button">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to API Keys
          </Button>
        </Link>
        <h1 className="text-3xl font-bold" data-testid="api-key-detail-title">API Key Details</h1>
      </div>

      {updateError && (
        <Alert className="mb-6" data-testid="api-key-detail-error-alert">
          <AlertDescription className="text-red-500" data-testid="api-key-detail-error-message">
            {updateError}
          </AlertDescription>
        </Alert>
      )}

      {apiKey && (
        <div className="grid gap-6" data-testid="api-key-detail-content">
          <Card data-testid="api-key-detail-info-card">
            <CardHeader data-testid="api-key-detail-info-header">
              <CardTitle data-testid="api-key-detail-info-title">{apiKey.name}</CardTitle>
              <CardDescription data-testid="api-key-detail-info-description">
                API Key Information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4" data-testid="api-key-detail-info-content">
              <div className="grid grid-cols-2 gap-4" data-testid="api-key-detail-info-grid">
                <div data-testid="api-key-detail-id-field">
                  <p className="text-sm font-medium text-muted-foreground">ID</p>
                  <p className="text-sm" data-testid="api-key-detail-id-value">{apiKey.id}</p>
                </div>
                <div data-testid="api-key-detail-created-field">
                  <p className="text-sm font-medium text-muted-foreground">Created</p>
                  <p className="text-sm" data-testid="api-key-detail-created-value">{formatDate(apiKey.created_at)}</p>
                </div>
                <div data-testid="api-key-detail-last-used-field">
                  <p className="text-sm font-medium text-muted-foreground">Last Used</p>
                  <p className="text-sm" data-testid="api-key-detail-last-used-value">{apiKey.last_used ? formatDate(apiKey.last_used) : "Never"}</p>
                </div>
                <div data-testid="api-key-detail-status-field">
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <p className="text-sm">
                    <span
                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        apiKey.enabled
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                      data-testid="api-key-detail-status-badge"
                    >
                      {apiKey.enabled ? "Active" : "Disabled"}
                    </span>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-testid="api-key-detail-permissions-card">
            <CardHeader data-testid="api-key-detail-permissions-header">
              <CardTitle data-testid="api-key-detail-permissions-title">Permissions</CardTitle>
              <CardDescription data-testid="api-key-detail-permissions-description">
                Manage the permissions for this API key
              </CardDescription>
            </CardHeader>
            <CardContent data-testid="api-key-detail-permissions-content">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6" data-testid="api-key-detail-permissions-form">
                  <div className="space-y-3" data-testid="api-key-detail-permissions-section">
                    <FormLabel data-testid="api-key-detail-permissions-label">Permissions</FormLabel>
                    <FormDescription data-testid="api-key-detail-permissions-help-text">
                      Select the permissions for this API key
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-4" data-testid="api-key-detail-permissions-grid">
                      {availablePermissions.map((permission) => (
                        <FormField
                          key={permission.id}
                          control={form.control}
                          name="permissions"
                          render={({ field }) => (
                            <FormItem
                              key={permission.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                              data-testid={`api-key-detail-permission-item-${permission.id}`}
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(permission.id)}
                                  onCheckedChange={(checked) => {
                                    const currentPermissions = field.value || [];
                                    if (checked) {
                                      field.onChange([...currentPermissions, permission.id]);
                                    } else {
                                      field.onChange(
                                        currentPermissions.filter((p) => p !== permission.id)
                                      );
                                    }
                                  }}
                                  data-testid={`api-key-detail-permission-checkbox-${permission.id}`}
                                />
                              </FormControl>
                              <FormLabel className="font-normal" data-testid={`api-key-detail-permission-label-${permission.id}`}>
                                {permission.label}
                              </FormLabel>
                            </FormItem>
                          )}
                        />
                      ))}
                    </div>
                  </div>
                  <Button type="submit" className="mt-4" data-testid="api-key-detail-save-button">
                    <Save className="h-4 w-4 mr-2" />
                    Save Permissions
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
