"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { APIKey } from "@/types";
import { toast } from "sonner";
import { useCreateAPIKeyMutation } from "@/lib/api/apiSlice";
import { ArrowLeft, Copy, Key } from "lucide-react";
import Link from "next/link";
import { CREDIT_SERVICE_TEST_IDS } from "@/lib/test-ids";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define the form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  permissions: z.array(z.string()).optional(),
});

// Define available permissions
const availablePermissions = [
  { id: "read", label: "Read" },
  { id: "write", label: "Write" },
  { id: "delete", label: "Delete" },
  { id: "admin", label: "Admin" },
];

export default function NewAPIKeyPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [newKey, setNewKey] = useState<APIKey | null>(null);

  // Store access token in session storage if available
  useEffect(() => {
    // Use type assertion to access accessToken
    const userAccessToken = (session?.user as any)?.accessToken;
    const sessionAccessToken = (session as any)?.accessToken;

    if (userAccessToken) {
      console.log('Setting access token from user:', userAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', userAccessToken);
    } else if (sessionAccessToken) {
      console.log('Setting access token from session:', sessionAccessToken.substring(0, 10) + '...');
      sessionStorage.setItem('accessToken', sessionAccessToken);
    } else {
      console.warn('No access token found in session');
      // Redirect to login page or show an error message
      toast.error("Authentication error: Please log in to continue");
      router.push('/auth/login');
    }
  }, [session, router]);

  // Use RTK Query hook for creating API key
  const [createAPIKey, { isLoading }] = useCreateAPIKeyMutation();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      permissions: ["read"], // Default to read permission
    },
  });

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Check if we have an access token
    const token = sessionStorage.getItem('accessToken');
    if (!token) {
      console.error("No access token found in session storage");
      toast.error("Authentication error: No access token found");
      return;
    }

    console.log('Submitting form with values:', values);
    console.log('Using access token:', token.substring(0, 10) + '...');

    try {
      const result = await createAPIKey({
        name: values.name,
        permissions: values.permissions,
      }).unwrap();

      console.log('API key created successfully:', result);
      setNewKey(result);
      toast.success("API key created successfully");
    } catch (error: any) {
      console.error("Error creating API key:", error);

      // More detailed error handling
      if (error.status === 401) {
        toast.error("Authentication error: Please log in again");
      } else if (error.data?.error) {
        toast.error(`Error: ${error.data.error}`);
      } else {
        toast.error("Failed to create API key");
      }
    }
  };

  return (
    <div className="container py-10" data-testid="api-key-new-container">
      <div className="flex items-center gap-2 mb-8" data-testid="api-key-new-header">
        <Link href="/dashboard/api-keys">
          <Button variant="outline" size="icon" className="h-8 w-8" data-testid="api-key-new-back-button">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold" data-testid="api-key-new-title">Create New API Key</h1>
      </div>

      {newKey ? (
        <div className="space-y-6" data-testid="api-key-new-success-container">
          <Alert className="mb-6" data-testid="api-key-new-success-alert">
            <AlertDescription className="flex flex-col gap-2">
              <p data-testid="api-key-new-success-message">
                <strong>Important:</strong> Copy your new API key now. You won&apos;t be able to see it again!
              </p>
              <div className="flex items-center gap-2" data-testid="api-key-new-key-display">
                <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-testid="api-key-new-key-value">
                  {newKey.key}
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(newKey.key);
                    toast.success("API key copied to clipboard");
                  }}
                  data-testid="api-key-new-copy-button"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
              </div>
            </AlertDescription>
          </Alert>

          <div className="flex gap-4" data-testid="api-key-new-actions">
            <Button onClick={() => setNewKey(null)} data-testid="api-key-new-create-another-button">Create Another Key</Button>
            <Link href="/dashboard/api-keys">
              <Button variant="outline" data-testid="api-key-new-return-button">Return to API Keys</Button>
            </Link>
          </div>
        </div>
      ) : (
        <Card data-testid="api-key-new-form-card">
          <CardHeader data-testid="api-key-new-form-header">
            <CardTitle data-testid="api-key-new-form-title">Create API Key</CardTitle>
            <CardDescription data-testid="api-key-new-form-description">
              Create a new API key for integrating with external systems. You will only be able to view the key once after creation.
            </CardDescription>
          </CardHeader>
          <CardContent data-testid="api-key-new-form-content">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6" data-testid="api-key-new-form">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem data-testid="api-key-new-name-field">
                      <FormLabel data-testid="api-key-new-name-label">Name</FormLabel>
                      <FormControl>
                        <Input placeholder="My API Key" {...field} data-testid="api-key-new-name-input" />
                      </FormControl>
                      <FormDescription data-testid="api-key-new-name-description">
                        A descriptive name to identify this API key
                      </FormDescription>
                      <FormMessage data-testid="api-key-new-name-error" />
                    </FormItem>
                  )}
                />

                <div className="space-y-3" data-testid="api-key-new-permissions-section">
                  <FormLabel data-testid="api-key-new-permissions-label">Permissions</FormLabel>
                  <FormDescription data-testid="api-key-new-permissions-description">
                    Select the permissions for this API key
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-4" data-testid="api-key-new-permissions-grid">
                    {availablePermissions.map((permission) => (
                      <FormField
                        key={permission.id}
                        control={form.control}
                        name="permissions"
                        render={({ field }) => (
                          <FormItem
                            key={permission.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                            data-testid={`api-key-new-permission-item-${permission.id}`}
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(permission.id)}
                                onCheckedChange={(checked) => {
                                  const currentPermissions = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentPermissions, permission.id]);
                                  } else {
                                    field.onChange(
                                      currentPermissions.filter((p) => p !== permission.id)
                                    );
                                  }
                                }}
                                data-testid={`api-key-new-permission-checkbox-${permission.id}`}
                              />
                            </FormControl>
                            <FormLabel className="font-normal" data-testid={`api-key-new-permission-label-${permission.id}`}>
                              {permission.label}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex justify-end gap-4" data-testid="api-key-new-form-actions">
                  <Link href="/dashboard/api-keys">
                    <Button variant="outline" type="button" data-testid="api-key-new-cancel-button">Cancel</Button>
                  </Link>
                  <Button type="submit" disabled={isLoading} data-testid="api-key-new-submit-button">
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" data-testid="api-key-new-loading-spinner"></div>
                        Creating...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        Create API Key
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
