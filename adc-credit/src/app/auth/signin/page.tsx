"use client";

import { signIn } from "next-auth/react";
import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import Link from "next/link";
import { useState, Suspense } from "react";
import { useRouter } from "next/navigation";
import { AlertTriangle, Lock, Mail } from "lucide-react";
import { AUTH_TEST_IDS } from "@/lib/test-ids";

function SignInForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
  const error = searchParams.get("error");

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Function to hash password (simple implementation for demo purposes)
  const hashPassword = async (password: string) => {
    // In a real app, you would use bcrypt or another secure hashing algorithm
    // This is just a simple hash for demonstration
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  // Function to check local credentials
  const checkLocalCredentials = async (email: string, password: string) => {
    try {
      // For development, allow a demo user
      if (email === "<EMAIL>" && password === "password123") {
        return {
          success: true,
          user: {
            id: "demo-user-1",
            name: "Demo User",
            email: "<EMAIL>",
            role: "user"
          }
        };
      }

      // Check for other local users
      const users = JSON.parse(localStorage.getItem('users') || '[]');
      const user = users.find((u: any) => u.email === email);

      if (!user) {
        return { success: false, error: "User not found" };
      }

      const hashedPassword = await hashPassword(password);

      if (user.password === hashedPassword) {
        return { success: true, user: { ...user, password: undefined } };
      } else {
        return { success: false, error: "Invalid password" };
      }
    } catch (error) {
      console.error("Local auth error:", error);
      return { success: false, error: "Authentication failed" };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage("");

    try {
      // Try to sign in with NextAuth first
      try {
        const result = await signIn("credentials", {
          email,
          password,
          redirect: false,
          callbackUrl,
        });

        if (!result?.error) {
          if (result?.url) {
            router.push(result.url);
          }
          return; // Success, exit early
        }
      } catch (nextAuthError) {
        console.warn("NextAuth sign in failed, trying local auth:", nextAuthError);
      }

      // If NextAuth fails, try local authentication
      const localAuthResult = await checkLocalCredentials(email, password);

      if (localAuthResult.success) {
        // Store user in session storage for demo purposes
        sessionStorage.setItem('currentUser', JSON.stringify(localAuthResult.user));
        router.push(callbackUrl);
      } else {
        setErrorMessage("Invalid email or password");
      }
    } catch (error) {
      setErrorMessage("An error occurred during sign in");
      console.error("Sign in error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getErrorMessage = (errorCode: string) => {
    const errorMessages: Record<string, string> = {
      "OAuthSignin": "Error starting the OAuth sign in flow.",
      "OAuthCallback": "Error completing the OAuth sign in flow.",
      "OAuthCreateAccount": "Error creating the OAuth user in the database.",
      "EmailCreateAccount": "Error creating the email user in the database.",
      "Callback": "Error during the OAuth callback.",
      "OAuthAccountNotLinked": "The email on the OAuth account is already linked to another account.",
      "EmailSignin": "Error sending the email sign in link.",
      "CredentialsSignin": "The sign in credentials are invalid.",
      "SessionRequired": "You must be signed in to access this page.",
      "Default": "An unexpected error occurred."
    };

    return errorMessages[errorCode] || "An unexpected error occurred.";
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4" data-testid={AUTH_TEST_IDS.SIGNIN.CONTAINER}>
      <div className="w-full max-w-md">
        <div className="mb-6 text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-primary/10 p-2 flex items-center justify-center mb-4">
            <Lock className="h-6 w-6 text-primary" />
          </div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white" data-testid={AUTH_TEST_IDS.SIGNIN.TITLE}>
            Welcome back
          </h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Sign in to your account to continue
          </p>
        </div>

        <Card className="w-full">
          <CardContent className="pt-6 space-y-4">
            {(error || errorMessage) && (
              <Alert variant="destructive" className="mb-4 bg-destructive/10 border-destructive/20" data-testid={AUTH_TEST_IDS.SIGNIN.ERROR_MESSAGE}>
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <AlertDescription>
                  {error ? getErrorMessage(error) : errorMessage}
                </AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4" data-testid="auth-signin-form">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                    data-testid="auth-signin-email-input"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-xs text-primary hover:text-primary/80 font-medium"
                    data-testid="auth-signin-forgot-password-link"
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                    data-testid="auth-signin-password-input"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                  data-testid="auth-signin-remember-checkbox"
                />
                <label
                  htmlFor="remember"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Remember me for 30 days
                </label>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading} data-testid="auth-signin-submit-button">
                {isLoading ? "Signing in..." : "Sign in"}
              </Button>
            </form>

            <div className="relative my-4" data-testid={AUTH_TEST_IDS.SIGNIN.DIVIDER}>
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => signIn("google", { callbackUrl })}
              data-testid={AUTH_TEST_IDS.SIGNIN.GOOGLE_BUTTON}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
                <path d="M1 1h22v22H1z" fill="none" />
              </svg>
              Sign in with Google
            </Button>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 border-t pt-6">
            <div className="text-sm text-center text-gray-500 dark:text-gray-400">
              Don't have an account?{" "}
              <Link href="/auth/register" className="font-medium text-primary hover:text-primary/80" data-testid="auth-signin-register-link">
                Create an account
              </Link>
            </div>
            <div className="text-xs text-center text-gray-500 dark:text-gray-400">
              By signing in, you agree to our{" "}
              <Link href="/terms" className="underline hover:text-primary">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link href="/privacy" className="underline hover:text-primary">
                Privacy Policy
              </Link>
              .
            </div>
          </CardFooter>
        </Card>

        <div className="mt-4 text-center">
          <Link href="/" className="text-sm text-gray-500 hover:text-primary" data-testid="auth-signin-back-home-link">
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
        <div className="w-full max-w-md">
          <div className="mb-6 text-center">
            <div className="mx-auto h-12 w-12 rounded-full bg-primary/10 p-2 flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-primary" />
            </div>
            <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
              Welcome back
            </h1>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Loading...
            </p>
          </div>
          <Card className="w-full">
            <CardContent className="pt-6">
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" data-testid={AUTH_TEST_IDS.SIGNIN.LOADING_SPINNER}></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <SignInForm />
    </Suspense>
  );
}
