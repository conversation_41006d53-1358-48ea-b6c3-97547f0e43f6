"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { AUTH_TEST_IDS } from "@/lib/test-ids";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const response = await fetch(`/api/auth/forgot-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setErrorMessage(data.error || "Failed to send password reset email");
        return;
      }

      setSuccessMessage("Password reset instructions have been sent to your email");
      setEmail("");
    } catch (error) {
      console.error("Password reset error:", error);
      setErrorMessage("An error occurred while sending the password reset email");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.CONTAINER}>
      <Card className="w-full max-w-md" data-testid="auth-forgot-password-card">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.TITLE}>Forgot Password</CardTitle>
          <CardDescription>
            Enter your email address and we'll send you a link to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent>
          {errorMessage && (
            <div className="p-3 mb-4 bg-red-50 text-red-500 text-sm rounded-md" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.ERROR_MESSAGE}>
              {errorMessage}
            </div>
          )}
          
          {successMessage && (
            <div className="p-3 mb-4 bg-green-50 text-green-500 text-sm rounded-md" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.SUCCESS_MESSAGE}>
              {successMessage}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.FORM}>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="<EMAIL>" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.EMAIL_INPUT}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading} data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.SUBMIT_BUTTON}>
              {isLoading && <div className="inline-block mr-2 h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.LOADING_SPINNER}></div>}
              {isLoading ? "Sending..." : "Send Reset Link"}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-gray-500 dark:text-gray-400">
            Remember your password?{" "}
            <Link href="/auth/signin" className="underline hover:text-primary" data-testid="auth-forgot-password-signin-link">
              Sign in
            </Link>
          </div>
          <div className="text-sm text-center text-gray-500 dark:text-gray-400">
            <Link href="/" className="underline hover:text-primary" data-testid={AUTH_TEST_IDS.FORGOT_PASSWORD.BACK_LINK}>
              Back to home
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
