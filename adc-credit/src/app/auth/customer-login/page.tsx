"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Eye, EyeSlash } from "phosphor-react";
import Link from "next/link";
import { AUTH_TEST_IDS } from "@/lib/test-ids";

export default function CustomerLoginPage() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/customer-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
          login_type: 'shop_customer'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      // Store authentication token and user data
      localStorage.setItem('customerToken', data.token);
      localStorage.setItem('shopId', data.user.shop_id);
      localStorage.setItem('customerData', JSON.stringify(data.user));

      toast({
        title: "Login successful!",
        description: `Welcome back, ${data.user.name}!`,
      });

      // Redirect to customer dashboard
      router.push('/dashboard/customer');
    } catch (error) {
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#fbfaf9] flex items-center justify-center p-4" data-testid={AUTH_TEST_IDS.CUSTOMER_LOGIN.CONTAINER}>
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center" data-testid={AUTH_TEST_IDS.CUSTOMER_LOGIN.TITLE}>Customer Login</CardTitle>
          <CardDescription className="text-center">
            Login with your shop-provided credentials
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4" data-testid={AUTH_TEST_IDS.CUSTOMER_LOGIN.FORM}>
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                required
                disabled={isLoading}
                data-testid="customer-login-username-input"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                  disabled={isLoading}
                  data-testid="customer-login-password-input"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                  data-testid="customer-login-password-toggle"
                >
                  {showPassword ? (
                    <EyeSlash className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
              disabled={isLoading}
              data-testid="customer-login-submit-button"
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm">
            <p className="text-muted-foreground">
              Don't have an account? Contact your shop to get access.
            </p>
            <Link 
              href="/auth/login" 
              className="text-primary hover:underline mt-2 inline-block"
              data-testid="customer-login-business-link"
            >
              Business Owner? Sign in here
            </Link>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>Customer Access:</strong> Use the username and password provided by your shop to access your credits and rewards.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}