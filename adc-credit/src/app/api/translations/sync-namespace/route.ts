/**
 * Bulk namespace sync API endpoint
 * Accepts full namespace locale data and syncs with Multi-Languages service
 */

import { NextRequest, NextResponse } from 'next/server';

const MULTILANG_SERVICE_URL = process.env.NEXT_PUBLIC_MULTILANG_URL || 'http://localhost:8300';
const MULTILANG_INTERNAL_KEY = process.env.ADC_MULTILANG_SERVICE_API_KEY || 'adc_development_7ad37376349339fc45467e9d98978591efca1fe6732f35f046b1157b18f75126';
const CREDIT_PROJECT_ID = process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'b90e383d-6e7d-4881-ba9a-c31649719348';

interface SyncRequest {
  namespace: string;
  locale: string;
  project_id?: string;
  translations: Record<string, string>;
}

async function ensureTranslationKey(
  projectId: string, 
  keyName: string, 
  namespace: string
): Promise<{ exists: boolean; created?: boolean; error?: string }> {
  try {
    // Check if translation key exists
    const checkResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/internal/translations/fetch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id: projectId,
        namespace,
        key: keyName,
        locale: 'en',
        force_create: false
      }),
    });

    if (checkResponse.ok) {
      return { exists: true };
    }

    // Create translation key if it doesn't exist
    const createKeyResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/api-access/translation-keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id: projectId,
        key_name: keyName,
        description: `Translation key for ${namespace}.${keyName}`,
        context: `Generated for Credit Service - ${namespace} namespace`,
        is_plural: false,
        max_length: 1000
      }),
    });

    if (!createKeyResponse.ok) {
      const errorData = await createKeyResponse.text();
      throw new Error(`Failed to create translation key: ${errorData}`);
    }

    return { exists: false, created: true };
  } catch (error) {
    return { 
      exists: false, 
      created: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function syncTranslationContent(
  projectId: string,
  namespace: string,
  key: string,
  locale: string,
  content: string
): Promise<{ success: boolean; error?: string; created?: boolean }> {
  try {
    // Use internal fetch endpoint with force_create
    const response = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/internal/translations/fetch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id: projectId,
        namespace,
        key,
        locale,
        force_create: true,
        content // Include content to be set
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorData}` 
      };
    }

    const data = await response.json();
    return { 
      success: true, 
      created: data.created_new || false 
    };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: SyncRequest = await request.json();
    const { namespace, locale, translations, project_id } = body;

    // Validate required fields
    if (!namespace || !locale || !translations) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: namespace, locale, translations' 
        },
        { status: 400 }
      );
    }

    const finalProjectId = project_id || CREDIT_PROJECT_ID;
    const translationKeys = Object.keys(translations);

    if (translationKeys.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No translations provided' 
        },
        { status: 400 }
      );
    }

    console.log(`🔄 Syncing ${translationKeys.length} keys for ${namespace}/${locale}`);

    // Track results
    const results = {
      created_keys: [] as string[],
      updated_keys: [] as string[],
      failed_keys: [] as string[],
      errors: [] as string[],
    };

    // Process each translation key
    for (const [key, content] of Object.entries(translations)) {
      try {
        // Step 1: Ensure translation key exists
        const keyResult = await ensureTranslationKey(finalProjectId, key, namespace);
        
        if (keyResult.error) {
          results.failed_keys.push(key);
          results.errors.push(`Key ${key}: ${keyResult.error}`);
          continue;
        }

        // Step 2: Sync translation content
        const syncResult = await syncTranslationContent(
          finalProjectId,
          namespace,
          key,
          locale,
          content
        );

        if (syncResult.success) {
          if (syncResult.created) {
            results.created_keys.push(key);
          } else {
            results.updated_keys.push(key);
          }
        } else {
          results.failed_keys.push(key);
          results.errors.push(`Sync ${key}: ${syncResult.error}`);
        }
      } catch (error) {
        results.failed_keys.push(key);
        results.errors.push(`Process ${key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const totalProcessed = results.created_keys.length + results.updated_keys.length;
    const success = results.failed_keys.length === 0;

    console.log(`✅ Sync complete: ${totalProcessed}/${translationKeys.length} successful`);

    return NextResponse.json({
      success,
      data: {
        namespace,
        locale,
        created_keys: results.created_keys,
        updated_keys: results.updated_keys,
        failed_keys: results.failed_keys,
        total_keys: translationKeys.length,
        successful_keys: totalProcessed,
      },
      message: success 
        ? `Successfully synced ${totalProcessed} translations for ${namespace}/${locale}`
        : `Synced ${totalProcessed}/${translationKeys.length} translations with ${results.failed_keys.length} failures`,
      errors: results.errors.length > 0 ? results.errors : undefined,
    });

  } catch (error) {
    console.error('Namespace sync error:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error: ' + (error instanceof Error ? error.message : 'Unknown error'),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk namespace sync endpoint',
    usage: 'POST with { namespace, locale, translations, project_id? }',
    example: {
      namespace: 'homepage',
      locale: 'en',
      project_id: CREDIT_PROJECT_ID,
      translations: {
        'hero_title': 'Shop Credit Management Platform',
        'hero_subtitle': 'Revolutionize Your Business',
        'hero_description': 'Revolutionize your business with QR code-based credit transactions...'
      }
    }
  });
}