import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { getCorrelationId, CORRELATION_ID_HEADER, logWithCorrelation, logErrorWithCorrelation } from '@/lib/correlation';

/**
 * Universal proxy API route that forwards all requests to the backend API
 * This route handles all HTTP methods and preserves the path structure
 * 
 * Frontend: /api/users/me -> Backend: /api/v1/users/me
 * Frontend: /api/stripe/create-checkout-session -> Backend: /api/v1/stripe/create-checkout-session
 */

// Get backend URL from environment
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8400';

// Multi-Languages service configuration for i18n
const MULTILANG_SERVICE_URL = process.env.NEXT_PUBLIC_MULTILANG_URL || 'http://localhost:8300';
const MULTILANG_INTERNAL_KEY = process.env.ADC_MULTILANG_SERVICE_API_KEY || 'adc_development_7ad37376349339fc45467e9d98978591efca1fe6732f35f046b1157b18f75126';
const CREDIT_PROJECT_ID = process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'b90e383d-6e7d-4881-ba9a-c31649719348';

// Settings service configuration for auto-translation
const SETTINGS_SERVICE_URL = process.env.NEXT_PUBLIC_SETTINGS_URL || 'http://localhost:9200';

/**
 * Helper function to get authentication token
 */
async function getAuthToken(request: NextRequest): Promise<string | null> {
  // First try to get token from Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try to get token from session
  try {
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return session.accessToken as string;
    }
    if ((session?.user as any)?.accessToken) {
      return (session.user as any).accessToken;
    }
  } catch (error) {
    // Silently handle session errors
  }

  return null;
}

/**
 * Helper function to handle translation requests directly to Multi-Languages service
 */
async function handleTranslationRequest(
  request: NextRequest,
  servicePath: string[]
): Promise<NextResponse> {
  const correlationId = getCorrelationId(request);
  
  try {
    // Parse translation request: translations/namespace/key/locale or translations/namespace/locale
    if (servicePath.length === 4 && servicePath[0] === 'translations') {
      // Single translation: translations/namespace/key/locale
      const [, namespace, key, locale] = servicePath;
      
      logWithCorrelation(`Fetching translation: ${namespace}.${key} for ${locale}`, undefined, correlationId);
      
      // Use external API endpoint with query parameters
      const url = new URL(`${MULTILANG_SERVICE_URL}/api/v2/external/translations`);
      url.searchParams.append('language', locale);
      url.searchParams.append('namespace', namespace);
      url.searchParams.append('key', key);
      url.searchParams.append('project_id', CREDIT_PROJECT_ID);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'X-API-Key': MULTILANG_INTERNAL_KEY,
          'Content-Type': 'application/json',
        },
      });

      logWithCorrelation(`Translation response: ${response.status}`, undefined, correlationId);

      // Handle non-JSON responses (like HTML error pages)
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        logWithCorrelation(`Non-JSON response: ${text.substring(0, 200)}`, undefined, correlationId);
        
        return NextResponse.json({
          success: false,
          error: 'Invalid response from translation service',
          message: `Expected JSON but got ${contentType}`,
          details: text.substring(0, 200),
          timestamp: new Date().toISOString(),
          correlationId
        }, { status: 502 });
      }

      // Handle errors from Multi-Languages service gracefully
      if (!response.ok) {
        logWithCorrelation(`Multi-Languages service error: ${response.status} ${response.statusText}`, undefined, correlationId);
        
        // Return empty translation so fallback system can handle it
        return NextResponse.json({
          success: true,
          data: {
            translations: {
              [key]: '' // Empty content to trigger fallback
            },
            language: locale,
            namespace: namespace,
            key: key
          },
          message: 'Using fallback translations due to service error',
          correlationId
        }, { status: 200 });
      }

      const data = await response.json();
      
      return NextResponse.json(data, { status: response.status });
      
    } else if (servicePath.length === 3 && servicePath[0] === 'translations') {
      // Namespace translations: translations/namespace/locale
      const [, namespace, locale] = servicePath;
      
      logWithCorrelation(`Fetching namespace translations: ${namespace} for ${locale}`, undefined, correlationId);
      
      // Use external API endpoint with query parameters for namespace
      const url = new URL(`${MULTILANG_SERVICE_URL}/api/v2/external/translations`);
      url.searchParams.append('language', locale);
      url.searchParams.append('namespace', namespace);
      url.searchParams.append('project_id', CREDIT_PROJECT_ID);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'X-API-Key': MULTILANG_INTERNAL_KEY,
          'Content-Type': 'application/json',
        },
      });

      logWithCorrelation(`Namespace translation response: ${response.status}`, undefined, correlationId);

      // Handle non-JSON responses (like HTML error pages)
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        logWithCorrelation(`Namespace non-JSON response: ${text.substring(0, 200)}`, undefined, correlationId);
        
        return NextResponse.json({
          success: false,
          error: 'Invalid response from translation service',
          message: `Expected JSON but got ${contentType}`,
          details: text.substring(0, 200),
          timestamp: new Date().toISOString(),
          correlationId
        }, { status: 502 });
      }

      // Handle errors from Multi-Languages service gracefully
      if (!response.ok) {
        logWithCorrelation(`Multi-Languages service error: ${response.status} ${response.statusText}`, undefined, correlationId);
        
        // Return empty translations so fallback system can handle it
        return NextResponse.json({
          success: true,
          data: {
            translations: {},
            language: locale,
            namespace: namespace
          },
          message: 'Using fallback translations due to service error',
          correlationId
        }, { status: 200 });
      }

      const data = await response.json();
      
      // Transform Multi-Languages service response to Credit Service format
      if (data.success && data.data && data.data.translations) {
        // Convert array format to key-value format
        const transformedTranslations: Record<string, string> = {};
        
        if (Array.isArray(data.data.translations)) {
          // Handle array format from Multi-Languages service
          data.data.translations.forEach((translation: any) => {
            if (translation.key && translation.content) {
              transformedTranslations[translation.key] = translation.content;
            }
          });
        } else {
          // Handle object format (if service returns it differently)
          Object.assign(transformedTranslations, data.data.translations);
        }
        
        const transformedData = {
          success: true,
          data: {
            translations: transformedTranslations,
            language: locale,
            namespace: namespace
          },
          message: data.message || 'Translations retrieved successfully',
          correlationId
        };
        
        logWithCorrelation(`Transformed ${Object.keys(transformedTranslations).length} translations`, undefined, correlationId);
        return NextResponse.json(transformedData, { status: 200 });
      }
      
      return NextResponse.json(data, { status: response.status });
    }
    
    // If not a valid translation path, return error
    return NextResponse.json(
      { 
        error: 'Invalid translation request path',
        message: 'Expected /api/translations/namespace/key/locale or /api/translations/namespace/locale',
        timestamp: new Date().toISOString(),
        correlationId
      },
      { status: 400 }
    );
    
  } catch (error) {
    logErrorWithCorrelation(error instanceof Error ? error : String(error), correlationId);
    
    return NextResponse.json(
      { 
        error: 'Translation request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        correlationId
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to forward request to backend
 */
async function forwardRequest(
  request: NextRequest,
  method: string,
  servicePath: string[]
): Promise<NextResponse> {
  // Get correlation ID from request or generate new one
  const correlationId = getCorrelationId(request);
  
  try {
    // Check if this is a translation request - route directly to Multi-Languages service
    if (servicePath[0] === 'translations') {
      return handleTranslationRequest(request, servicePath);
    }
    
    // Construct the backend URL - add /api/v1 prefix for backend
    const backendPath = `/api/v1/${servicePath.join('/')}`;
    const url = new URL(backendPath, BACKEND_URL);
    
    // Preserve query parameters
    const searchParams = request.nextUrl.searchParams;
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    logWithCorrelation(`Proxying ${method} ${servicePath.join('/')} -> ${url.toString()}`, undefined, correlationId);

    // Get authentication token
    const token = await getAuthToken(request);

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      [CORRELATION_ID_HEADER]: correlationId,
    };

    // Add authentication if available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      logWithCorrelation(`Using auth token: ${token.substring(0, 20)}...`, undefined, correlationId);
    }

    // Forward other relevant headers
    const forwardHeaders = [
      'user-agent',
      'accept',
      'accept-language',
      'x-forwarded-for',
      'x-real-ip',
      CORRELATION_ID_HEADER.toLowerCase(),
    ];

    forwardHeaders.forEach(headerName => {
      const headerValue = request.headers.get(headerName);
      if (headerValue) {
        headers[headerName] = headerValue;
      }
    });

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add body for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const body = await request.text();
        if (body) {
          requestOptions.body = body;
        }
      } catch (error) {
        // Silently handle request body errors
      }
    }

    // Make the request to backend
    const response = await fetch(url.toString(), requestOptions);

    logWithCorrelation(`Backend response status: ${response.status}`, undefined, correlationId);

    // Handle 401 Unauthorized responses by adding a special header
    // The frontend will detect this and trigger sign-out
    if (response.status === 401) {
      logWithCorrelation(`Universal Proxy: 401 Unauthorized from backend - adding sign-out trigger header`, undefined, correlationId);
    }

    // Get response body
    const responseText = await response.text();
    let responseData;

    // Try to parse as JSON, fallback to text
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    // Create response with same status and headers
    const nextResponse = NextResponse.json(responseData, {
      status: response.status,
      statusText: response.statusText,
    });

    // Forward relevant response headers including correlation ID
    const responseHeaders = [
      'content-type',
      'cache-control',
      'etag',
      'last-modified',
      CORRELATION_ID_HEADER.toLowerCase(),
    ];

    responseHeaders.forEach(headerName => {
      const headerValue = response.headers.get(headerName);
      if (headerValue) {
        nextResponse.headers.set(headerName, headerValue);
      }
    });

    // Add special header for 401 responses to trigger frontend sign-out
    if (response.status === 401) {
      nextResponse.headers.set('X-Auth-Error', 'token-expired');
    }

    // Add CORS headers including correlation ID support
    nextResponse.headers.set('Access-Control-Allow-Origin', '*');
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    nextResponse.headers.set('Access-Control-Allow-Headers', `Content-Type, Authorization, ${CORRELATION_ID_HEADER}`);
    nextResponse.headers.set('Access-Control-Expose-Headers', `Content-Type, Authorization, ${CORRELATION_ID_HEADER}`);
    
    // Ensure correlation ID is in response headers
    nextResponse.headers.set(CORRELATION_ID_HEADER, correlationId);

    return nextResponse;

  } catch (error) {
    logErrorWithCorrelation(error instanceof Error ? error : String(error), correlationId);
    
    const errorResponse = NextResponse.json(
      { 
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        correlationId
      },
      { status: 500 }
    );
    
    // Add correlation ID to error response
    errorResponse.headers.set(CORRELATION_ID_HEADER, correlationId);
    
    return errorResponse;
  }
}

// HTTP method handlers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  const resolvedParams = await params;
  return forwardRequest(request, 'GET', resolvedParams.services);
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  const resolvedParams = await params;
  return forwardRequest(request, 'POST', resolvedParams.services);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  const resolvedParams = await params;
  return forwardRequest(request, 'PUT', resolvedParams.services);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  const resolvedParams = await params;
  return forwardRequest(request, 'DELETE', resolvedParams.services);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  const resolvedParams = await params;
  return forwardRequest(request, 'PATCH', resolvedParams.services);
}

export async function OPTIONS(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': `Content-Type, Authorization, X-Requested-With, ${CORRELATION_ID_HEADER}`,
      'Access-Control-Expose-Headers': `Content-Type, Authorization, ${CORRELATION_ID_HEADER}`,
      'Access-Control-Max-Age': '86400',
    },
  });
}
