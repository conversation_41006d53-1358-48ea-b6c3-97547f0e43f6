import { NextRequest, NextResponse } from 'next/server';

const MULTILANG_SERVICE_URL = process.env.NEXT_PUBLIC_MULTILANG_URL || 'http://localhost:8300';
const MULTILANG_INTERNAL_KEY = process.env.ADC_MULTILANG_SERVICE_API_KEY || 'adc_development_7ad37376349339fc45467e9d98978591efca1fe6732f35f046b1157b18f75126';

async function ensureTranslationKey(project_id: string, keyName: string, namespace: string) {
  try {
    // First check if translation key exists by trying to fetch it
    const checkResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/internal/translations/fetch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id,
        namespace,
        key: keyName,
        locale: 'en', // Use English to check if key exists
        force_create: false
      }),
    });

    if (checkResponse.ok) {
      return { exists: true, message: 'Translation key already exists' };
    }

    // If key doesn't exist, create it using the API access endpoint
    const createKeyResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/api-access/translation-keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id,
        key_name: keyName,
        description: `Translation key for ${namespace}.${keyName}`,
        context: `Generated for Credit Service - ${namespace} namespace`,
        is_plural: false,
        max_length: 1000
      }),
    });

    if (!createKeyResponse.ok) {
      const errorData = await createKeyResponse.text();
      throw new Error(`Failed to create translation key: ${errorData}`);
    }

    return { exists: false, created: true, message: 'Translation key created successfully' };
  } catch (error) {
    console.error('Error ensuring translation key:', error);
    return { exists: false, created: false, error: (error as Error).message };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { key, namespace, language, content, project_id } = body;

    if (!key || !namespace || !language || !content) {
      return NextResponse.json(
        { error: 'Missing required fields: key, namespace, language, content' },
        { status: 400 }
      );
    }

    const finalProjectId = project_id || 'b90e383d-6e7d-4881-ba9a-c31649719348';

    // Step 1: Ensure the translation key exists
    const keyResult = await ensureTranslationKey(finalProjectId, key, namespace);
    if (keyResult.error) {
      return NextResponse.json(
        { error: `Translation key creation failed: ${keyResult.error}` },
        { status: 500 }
      );
    }

    // Step 2: Use the internal fetch endpoint with force_create to create/update the translation
    const fetchResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/internal/translations/fetch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': MULTILANG_INTERNAL_KEY,
      },
      body: JSON.stringify({
        project_id: finalProjectId,
        namespace,
        key,
        locale: language,
        force_create: true,
        content // Include content to be set when creating
      }),
    });

    if (!fetchResponse.ok) {
      const errorData = await fetchResponse.text();
      console.error('Multi-Languages service error:', errorData);
      
      return NextResponse.json(
        { error: `Failed to create/update translation: ${errorData}` },
        { status: fetchResponse.status }
      );
    }

    const responseData = await fetchResponse.json();
    
    // Step 3: If the fetch created the translation but with default content, update it
    if (responseData.content !== content && responseData.translation_id) {
      const updateResponse = await fetch(`${MULTILANG_SERVICE_URL}/api/v2/api-access/translations/${responseData.translation_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': MULTILANG_INTERNAL_KEY,
        },
        body: JSON.stringify({
          content
        }),
      });

      if (updateResponse.ok) {
        const updateData = await updateResponse.json();
        return NextResponse.json({
          success: true,
          message: 'Translation updated successfully',
          data: updateData,
          key_status: keyResult.message
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Translation created/fetched successfully',
      data: responseData,
      key_status: keyResult.message
    });

  } catch (error) {
    console.error('Translation update error:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + (error as Error).message },
      { status: 500 }
    );
  }
}

// Also handle GET requests for testing
export async function GET() {
  return NextResponse.json({
    message: 'Translation update endpoint',
    usage: 'POST with { key, namespace, language, content, project_id }',
    example: {
      key: 'hero_title',
      namespace: 'ui',
      language: 'en',
      content: 'Shop Credit Management Platform',
      project_id: 'b90e383d-6e7d-4881-ba9a-c31649719348'
    }
  });
}