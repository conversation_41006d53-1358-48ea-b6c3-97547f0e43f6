'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, Plus, Search, Globe, Key, RefreshCw } from 'lucide-react';

interface TranslationKey {
  key: string;
  namespace: string;
  content: Record<string, string>;
  created_at: string;
  updated_at: string;
}

interface HomepageTranslations {
  [key: string]: {
    en: string;
    th: string;
    status: 'empty' | 'partial' | 'complete';
  };
}

export default function TranslationManagementPage() {
  const [selectedNamespace, setSelectedNamespace] = useState('ui');
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [homepageTranslations, setHomepageTranslations] = useState<HomepageTranslations>({});

  // Homepage translation keys structure
  const homepageKeys = {
    // Hero Section
    'hero_title': { en: 'Shop Credit Management Platform', th: 'แพลตฟอร์มจัดการเครดิตร้านค้า' },
    'hero_subtitle': { en: 'Revolutionize Your Business', th: 'ปฏิวัติธุรกิจของคุณ' },
    'hero_description': { 
      en: 'Revolutionize your business with QR code-based credit transactions. Manage shops, customers, and payments seamlessly with our comprehensive platform.',
      th: 'ปฏิวัติธุรกิจของคุณด้วยธุรกรรมเครดิตที่ใช้ QR Code จัดการร้านค้า ลูกค้า และการชำระเงินได้อย่างราบรื่นด้วยแพลตฟอร์มที่ครอบคลุมของเรา'
    },
    'button_start_shop': { en: 'Start Your Shop', th: 'เริ่มต้นร้านค้า' },
    'button_view_docs': { en: 'View Documentation', th: 'ดูเอกสารประกอบ' },

    // Navigation
    'nav_dashboard': { en: 'Dashboard', th: 'แดชบอร์ด' },
    'nav_shops': { en: 'Shops', th: 'ร้านค้า' },
    'nav_customers': { en: 'Customers', th: 'ลูกค้า' },
    'nav_documentation': { en: 'Documentation', th: 'เอกสารประกอบ' },
    'button_sign_in': { en: 'Sign In', th: 'เข้าสู่ระบบ' },

    // Features Section
    'features_title': { en: 'Powerful Features', th: 'ฟีเจอร์ที่ทรงพลัง' },
    'features_subtitle': { en: 'Everything you need to manage your business', th: 'ทุกสิ่งที่คุณต้องการในการจัดการธุรกิจ' },
    
    // Feature Cards
    'feature_qr_title': { en: 'QR Code Payments', th: 'การชำระเงินด้วย QR Code' },
    'feature_qr_description': { 
      en: 'Generate and manage QR codes for seamless customer transactions',
      th: 'สร้างและจัดการ QR Code สำหรับธุรกรรมลูกค้าที่ราบรื่น'
    },
    
    'feature_analytics_title': { en: 'Real-time Analytics', th: 'การวิเคราะห์แบบเรียลไทม์' },
    'feature_analytics_description': {
      en: 'Track your business performance with detailed analytics and insights',
      th: 'ติดตามประสิทธิภาพธุรกิจด้วยการวิเคราะห์และข้อมูลเชิงลึกที่ละเอียด'
    },

    'feature_security_title': { en: 'Enterprise Security', th: 'ความปลอดภัยระดับองค์กร' },
    'feature_security_description': {
      en: 'Bank-level security with encrypted transactions and secure data storage',
      th: 'ความปลอดภัยระดับธนาคารด้วยธุรกรรมที่เข้ารหัสและการจัดเก็บข้อมูลที่ปลอดภัย'
    },

    // Common UI Elements
    'loading': { en: 'Loading...', th: 'กำลังโหลด...' },
    'save': { en: 'Save', th: 'บันทึก' },
    'cancel': { en: 'Cancel', th: 'ยกเลิก' },
    'edit': { en: 'Edit', th: 'แก้ไข' },
    'delete': { en: 'Delete', th: 'ลบ' },
    'confirm': { en: 'Confirm', th: 'ยืนยัน' },
    'success': { en: 'Success', th: 'สำเร็จ' },
    'error': { en: 'Error', th: 'ข้อผิดพลาด' },
  };

  useEffect(() => {
    loadHomepageTranslations();
  }, []);

  const loadHomepageTranslations = async () => {
    setLoading(true);
    const translations: HomepageTranslations = {};

    for (const [key, defaultValues] of Object.entries(homepageKeys)) {
      translations[key] = {
        en: '',
        th: '',
        status: 'empty'
      };

      // Load current values from API
      try {
        const enResponse = await fetch(`/api/translations/ui/${key}/en`);
        const thResponse = await fetch(`/api/translations/ui/${key}/th`);

        if (enResponse.ok) {
          const enData = await enResponse.json();
          translations[key].en = enData.content || defaultValues.en;
        } else {
          translations[key].en = defaultValues.en;
        }

        if (thResponse.ok) {
          const thData = await thResponse.json();
          translations[key].th = thData.content || defaultValues.th;
        } else {
          translations[key].th = defaultValues.th;
        }

        // Determine status
        if (translations[key].en && translations[key].th) {
          translations[key].status = 'complete';
        } else if (translations[key].en || translations[key].th) {
          translations[key].status = 'partial';
        } else {
          translations[key].status = 'empty';
        }
      } catch (error) {
        console.error(`Error loading translation for ${key}:`, error);
        translations[key].en = defaultValues.en;
        translations[key].th = defaultValues.th;
        translations[key].status = 'empty';
      }
    }

    setHomepageTranslations(translations);
    setLoading(false);
  };

  const updateTranslation = async (key: string, language: string, content: string) => {
    try {
      // Since external API is read-only, we need to use internal API
      const response = await fetch('/api/admin/translations/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key,
          namespace: 'ui',
          language,
          content,
          project_id: process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'b90e383d-6e7d-4881-ba9a-c31649719348'
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update translation');
      }

      return data;
    } catch (error) {
      console.error('Update translation error:', error);
      throw error;
    }
  };

  const saveAllTranslations = async () => {
    setSaveLoading(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const [key, translations] of Object.entries(homepageTranslations)) {
        try {
          // Update English
          if (translations.en) {
            await updateTranslation(key, 'en', translations.en);
            successCount++;
          }

          // Update Thai
          if (translations.th) {
            await updateTranslation(key, 'th', translations.th);
            successCount++;
          }

          // Small delay to prevent overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error(`Failed to update ${key}:`, error);
          errorCount++;
        }
      }

      setMessage(`Saved ${successCount} translations successfully. ${errorCount} errors.`);
      if (errorCount === 0) {
        await loadHomepageTranslations(); // Reload to verify
      }
    } catch (error) {
      setMessage('Failed to save translations: ' + (error as Error).message);
    } finally {
      setSaveLoading(false);
    }
  };

  const updateHomepageTranslation = (key: string, language: string, content: string) => {
    setHomepageTranslations(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        [language]: content,
        status: prev[key].en && prev[key].th ? 'complete' : 'partial'
      }
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'complete':
        return <Badge variant="default" className="bg-green-500">Complete</Badge>;
      case 'partial':
        return <Badge variant="secondary">Partial</Badge>;
      case 'empty':
        return <Badge variant="destructive">Empty</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const filteredTranslations = Object.entries(homepageTranslations).filter(([key]) =>
    key.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Loading translations...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Translation Management</h1>
            <p className="text-muted-foreground mt-2">
              Manage translations for the Credit Service homepage
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              onClick={loadHomepageTranslations} 
              variant="outline" 
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button 
              onClick={saveAllTranslations} 
              disabled={saveLoading}
              className="bg-primary hover:bg-primary/90"
            >
              {saveLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save All Changes
            </Button>
          </div>
        </div>

        {message && (
          <Alert className="mb-6">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search translation keys..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Badge variant="secondary" className="px-3 py-1">
            {filteredTranslations.length} keys
          </Badge>
        </div>

        <div className="grid gap-6">
          {filteredTranslations.map(([key, translation]) => (
            <Card key={key}>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <CardTitle className="text-lg">{key}</CardTitle>
                    {getStatusBadge(translation.status)}
                  </div>
                  <Badge variant="outline">{selectedNamespace}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="en" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="en" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      English
                    </TabsTrigger>
                    <TabsTrigger value="th" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Thai
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="en" className="mt-4">
                    <Textarea
                      placeholder="Enter English translation..."
                      value={translation.en}
                      onChange={(e) => updateHomepageTranslation(key, 'en', e.target.value)}
                      className="min-h-[80px]"
                    />
                  </TabsContent>
                  
                  <TabsContent value="th" className="mt-4">
                    <Textarea
                      placeholder="Enter Thai translation..."
                      value={translation.th}
                      onChange={(e) => updateHomepageTranslation(key, 'th', e.target.value)}
                      className="min-h-[80px]"
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTranslations.length === 0 && (
          <Card>
            <CardContent className="py-12 text-center">
              <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No translations found</h3>
              <p className="text-muted-foreground">
                {searchTerm ? 'Try adjusting your search terms.' : 'No translation keys available.'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}