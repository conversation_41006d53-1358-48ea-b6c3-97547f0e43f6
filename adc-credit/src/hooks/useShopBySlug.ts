import { useEffect, useState } from 'react';
import { useGetShopBySlugQuery } from '@/lib/api/apiSlice';
import { useGetCustomerShopBySlugQuery } from '@/lib/api/customerApi';
import { isValidSlug, isUUID } from '@/lib/utils';

interface UseShopBySlugOptions {
  redirectOnInvalid?: boolean;
  skipFetch?: boolean;
}

interface UseShopBySlugReturn {
  shop: any;
  shopId: string | undefined;
  isLoading: boolean;
  error: any;
  isValidSlug: boolean;
  isUUID: boolean;
  refetch: () => void;
}

/**
 * Custom hook for managing shop data by slug with validation and error handling
 */
export function useShopBySlug(
  slug: string,
  options: UseShopBySlugOptions = {}
): UseShopBySlugReturn {
  const { redirectOnInvalid = false, skipFetch = false } = options;
  const [hasValidated, setHasValidated] = useState(false);

  // Validate slug format
  const slugIsValid = isValidSlug(slug);
  const slugIsUUID = isUUID(slug);
  const shouldSkip = skipFetch || !slugIsValid || slugIsUUID;

  // Fetch shop data by slug
  const {
    data: shop,
    isLoading,
    error,
    refetch
  } = useGetShopBySlugQuery(slug, {
    skip: shouldSkip
  });

  // Handle validation on mount
  useEffect(() => {
    if (!hasValidated && slug) {
      setHasValidated(true);
      
      if (redirectOnInvalid && (slugIsUUID || !slugIsValid)) {
        // Could trigger a redirect here if needed
        console.warn(`Invalid slug detected: ${slug}`);
      }
    }
  }, [slug, hasValidated, redirectOnInvalid, slugIsValid, slugIsUUID]);

  return {
    shop,
    shopId: shop?.id,
    isLoading: shouldSkip ? false : isLoading,
    error: shouldSkip ? (slugIsUUID ? { message: 'UUID format not supported' } : { message: 'Invalid slug format' }) : error,
    isValidSlug: slugIsValid,
    isUUID: slugIsUUID,
    refetch
  };
}

/**
 * Hook specifically for customer shop operations with ID resolution
 */
export function useCustomerShopBySlug(slug: string) {
  const [hasValidated, setHasValidated] = useState(false);

  // Validate slug format
  const slugIsValid = isValidSlug(slug);
  const slugIsUUID = isUUID(slug);
  const shouldSkip = !slugIsValid || slugIsUUID;

  // Use customer-specific endpoint
  const {
    data: customerShopData,
    isLoading,
    error,
    refetch
  } = useGetCustomerShopBySlugQuery(slug, {
    skip: shouldSkip
  });

  // Handle validation on mount
  useEffect(() => {
    if (!hasValidated && slug) {
      setHasValidated(true);
      
      if (slugIsUUID || !slugIsValid) {
        console.warn(`Invalid slug detected: ${slug}`);
      }
    }
  }, [slug, hasValidated, slugIsValid, slugIsUUID]);

  // Extract shop data from customer response
  const shop = customerShopData?.shop;
  const shopId = shop?.id;

  return {
    shop: customerShopData ? { ...shop, credit_balance: customerShopData.credit_balance } : null,
    shopId,
    isShopLoading: shouldSkip ? false : isLoading,
    shopError: shouldSkip ? (slugIsUUID ? { message: 'UUID format not supported' } : { message: 'Invalid slug format' }) : error,
    isValidSlug: slugIsValid,
    isUUID: slugIsUUID,
    refetchShop: refetch,
    // Helper method to check if shop data is ready for customer operations
    isReadyForCustomerOps: !!(shopId && !isLoading && !error)
  };
}