import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Types for subscription limits
export interface LimitCheckResult {
  allowed: boolean;
  current_usage?: number;
  limit?: number;
  unlimited?: boolean;
  message: string;
}

export interface SubscriptionLimits {
  subscription_tier: {
    id: number;
    name: string;
    description: string;
    price: number;
    credit_limit: number;
    features: string[];
    max_shops: number;
    max_customers_per_shop: number;
    max_api_keys_per_shop: number;
    max_branches_per_shop: number;
    max_qr_codes_per_month: number;
    analytics_history_days: number;
    support_level: string;
    allowed_shop_types: string[];
    unlimited_shops: boolean;
    unlimited_customers: boolean;
    unlimited_qr_codes: boolean;
    unlimited_branches: boolean;
  };
  limits: {
    shops: {
      max: number;
      current: number;
      unlimited: boolean;
      allowed: boolean;
    };
    customers_per_shop: {
      max: number;
      unlimited: boolean;
    };
    api_keys_per_shop: {
      max: number;
    };
    branches_per_shop: {
      max: number;
      unlimited: boolean;
    };
    qr_codes_per_month: {
      max: number;
      current: number;
      unlimited: boolean;
      allowed: boolean;
    };
    webhooks: {
      max: number;
      current: number;
      allowed: boolean;
    };
    credits: {
      limit: number;
      current: number;
    };
    analytics_history_days: number;
    support_level: string;
    allowed_shop_types: string[];
  };
}

// API functions
const fetchUserLimits = async (): Promise<SubscriptionLimits> => {
  const response = await fetch('/api/subscription-limits', {
    credentials: 'include', // Include cookies for NextAuth
  });

  if (!response.ok) {
    throw new Error('Failed to fetch subscription limits');
  }

  return response.json();
};

const checkShopLimit = async (): Promise<LimitCheckResult> => {
  const response = await fetch('/api/subscription-limits/shops/check', {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check shop limit');
  }

  return response.json();
};

const checkCustomerLimit = async (shopId: string): Promise<LimitCheckResult> => {
  const response = await fetch(`/api/subscription-limits/shops/${shopId}/customers/check`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check customer limit');
  }

  return response.json();
};

const checkAPIKeyLimit = async (shopId: string): Promise<LimitCheckResult> => {
  const response = await fetch(`/api/subscription-limits/shops/${shopId}/api-keys/check`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check API key limit');
  }

  return response.json();
};

const checkBranchLimit = async (shopId: string): Promise<LimitCheckResult> => {
  const response = await fetch(`/api/subscription-limits/shops/${shopId}/branches/check`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check branch limit');
  }

  return response.json();
};

const checkQRCodeLimit = async (): Promise<LimitCheckResult> => {
  const response = await fetch('/api/subscription-limits/qr-codes/check', {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check QR code limit');
  }

  return response.json();
};

const checkWebhookLimit = async (): Promise<LimitCheckResult> => {
  const response = await fetch('/api/subscription-limits/webhooks/check', {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check webhook limit');
  }

  return response.json();
};

const checkShopTypeAllowed = async (shopType: string): Promise<LimitCheckResult> => {
  const response = await fetch(`/api/subscription-limits/shop-types/check?shop_type=${shopType}`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check shop type permission');
  }

  return response.json();
};

const checkCreditBalance = async (requiredCredits?: number): Promise<LimitCheckResult> => {
  const url = requiredCredits 
    ? `/api/subscription-limits/credits/check?required_credits=${requiredCredits}`
    : '/api/subscription-limits/credits/check';
    
  const response = await fetch(url, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to check credit balance');
  }

  return response.json();
};

// Custom hooks
export const useSubscriptionLimits = () => {
  return useQuery({
    queryKey: ['subscription-limits'],
    queryFn: fetchUserLimits,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};

export const useShopLimitCheck = () => {
  return useQuery({
    queryKey: ['subscription-limits', 'shops'],
    queryFn: checkShopLimit,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useCustomerLimitCheck = (shopId: string) => {
  return useQuery({
    queryKey: ['subscription-limits', 'customers', shopId],
    queryFn: () => checkCustomerLimit(shopId),
    enabled: !!shopId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useAPIKeyLimitCheck = (shopId: string) => {
  return useQuery({
    queryKey: ['subscription-limits', 'api-keys', shopId],
    queryFn: () => checkAPIKeyLimit(shopId),
    enabled: !!shopId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useBranchLimitCheck = (shopId: string) => {
  return useQuery({
    queryKey: ['subscription-limits', 'branches', shopId],
    queryFn: () => checkBranchLimit(shopId),
    enabled: !!shopId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useQRCodeLimitCheck = () => {
  return useQuery({
    queryKey: ['subscription-limits', 'qr-codes'],
    queryFn: checkQRCodeLimit,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useWebhookLimitCheck = () => {
  return useQuery({
    queryKey: ['subscription-limits', 'webhooks'],
    queryFn: checkWebhookLimit,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useShopTypeCheck = (shopType: string) => {
  return useQuery({
    queryKey: ['subscription-limits', 'shop-types', shopType],
    queryFn: () => checkShopTypeAllowed(shopType),
    enabled: !!shopType,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreditBalanceCheck = (requiredCredits?: number) => {
  return useQuery({
    queryKey: ['subscription-limits', 'credits', requiredCredits],
    queryFn: () => checkCreditBalance(requiredCredits),
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Utility hook for limit enforcement with user feedback
export const useLimitEnforcement = () => {
  const queryClient = useQueryClient();

  const enforceLimit = async (
    checkFunction: () => Promise<LimitCheckResult>,
    action: () => Promise<void>,
    actionName: string
  ) => {
    try {
      const limitCheck = await checkFunction();
      
      if (!limitCheck.allowed) {
        toast.error(`Cannot ${actionName}`, {
          description: limitCheck.message,
        });
        return false;
      }

      await action();
      
      // Invalidate relevant queries to refresh limits
      queryClient.invalidateQueries({ queryKey: ['subscription-limits'] });
      
      return true;
    } catch (error) {
      toast.error(`Failed to ${actionName}`, {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  };

  return { enforceLimit };
};

// Helper function to format limit display
export const formatLimitDisplay = (current: number, max: number, unlimited: boolean): string => {
  if (unlimited) {
    return `${current} / Unlimited`;
  }
  return `${current} / ${max}`;
};

// Helper function to get limit status
export const getLimitStatus = (current: number, max: number, unlimited: boolean): 'safe' | 'warning' | 'danger' => {
  if (unlimited) return 'safe';
  
  const percentage = (current / max) * 100;
  
  if (percentage >= 90) return 'danger';
  if (percentage >= 75) return 'warning';
  return 'safe';
};
