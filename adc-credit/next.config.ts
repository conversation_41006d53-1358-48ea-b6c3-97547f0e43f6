import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  reactStrictMode: false,
  output: 'standalone',
  images: {
    domains: process.env.NEXT_PUBLIC_ALLOWED_IMAGE_DOMAINS 
      ? process.env.NEXT_PUBLIC_ALLOWED_IMAGE_DOMAINS.split(',').map(domain => domain.trim())
      : ["localhost", "127.0.0.1", "api.qrserver.com", "via.placeholder", "images.unsplash.com", "credit.adcshop.store"],
  },
};

export default nextConfig;
