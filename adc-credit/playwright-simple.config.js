/**
 * Simple Playwright Configuration for Test Validation
 * 
 * Runs tests without starting web server - for test validation only
 */

const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests/e2e',
  testMatch: [
    '**/cookies-with-test-ids.spec.ts',
    '**/dashboard-comprehensive.spec.ts',
    '**/api-integration-forms.spec.ts',
  ],
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: 0,
  workers: 1,
  reporter: [
    ['list'],
    ['json', { outputFile: 'test-results/simple-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:3400',
    trace: 'on',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 5000,
    navigationTimeout: 10000
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  // No web server - assumes app is already running or tests handle navigation appropriately
  
  // Expect configuration
  expect: {
    timeout: 5000,
  },

  // Output directories
  outputDir: 'test-results/',

  // Timeout for entire test suite
  timeout: 30000,
});