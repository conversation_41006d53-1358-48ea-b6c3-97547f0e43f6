/**
 * Simple Test ID Validation Script
 * 
 * Validates that our implemented test IDs are present in the application
 */

import { chromium } from 'playwright';

async function validateTestIds() {
  console.log('🔧 Starting Test ID validation...\n');
  
  const browser = await chromium.launch();
  const context = await browser.newContext({
    ignoreHTTPSErrors: true,
  });
  const page = await context.newPage();
  
  try {
    // Navigate to the application
    console.log('📡 Connecting to https://localhost:3400...');
    await page.goto('https://localhost:3400', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    console.log('✅ Successfully loaded the application\n');
    
    // Wait a moment for any dynamic content to load
    await page.waitForTimeout(2000);
    
    // Get all test IDs from the page
    const allTestIds = await page.locator('[data-testid]').evaluateAll(elements => 
      elements.map(el => el.getAttribute('data-testid')).filter(Boolean)
    );
    
    console.log(`🎯 Found ${allTestIds.length} elements with test IDs:\n`);
    
    if (allTestIds.length === 0) {
      console.log('❌ No test IDs found on the page. This could mean:');
      console.log('   - Test IDs are not being rendered');
      console.log('   - Components with test IDs are not present on this page');
      console.log('   - There may be an issue with the implementation\n');
    } else {
      // Group test IDs by our implemented patterns
      const ourPatterns = {
        'Credit Display': allTestIds.filter(id => id.includes('credit-display-')),
        'Cookie Banner': allTestIds.filter(id => id.includes('cookie-banner-')),
        'Real Usage Details': allTestIds.filter(id => id.includes('real-usage-details-')),
        'Dashboard Analytics': allTestIds.filter(id => id.includes('dashboard-analytics-')),
        'HTML5 QR Code': allTestIds.filter(id => id.includes('html5-qr-code-plugin-')),
        'Landing Page': allTestIds.filter(id => id.includes('landing-')),
        'Other': allTestIds.filter(id => 
          !id.includes('credit-display-') &&
          !id.includes('cookie-banner-') &&
          !id.includes('real-usage-details-') &&
          !id.includes('dashboard-analytics-') &&
          !id.includes('html5-qr-code-plugin-') &&
          !id.includes('landing-')
        )
      };
      
      // Display results by category
      for (const [category, ids] of Object.entries(ourPatterns)) {
        if (ids.length > 0) {
          console.log(`📂 ${category}: ${ids.length} test IDs`);
          ids.slice(0, 5).forEach(id => console.log(`   ✅ ${id}`));
          if (ids.length > 5) {
            console.log(`   ... and ${ids.length - 5} more\n`);
          } else {
            console.log('');
          }
        }
      }
    }
    
    // Check naming convention compliance
    const invalidTestIds = allTestIds.filter(id => {
      return (
        id.includes('_') || // Should use kebab-case, not snake_case
        id.includes(' ') || // No spaces
        /[A-Z]/.test(id) || // No uppercase letters
        id.startsWith('-') || id.endsWith('-') || // No leading/trailing dashes
        id.includes('--') // No double dashes
      );
    });
    
    if (invalidTestIds.length === 0) {
      console.log('✅ All test IDs follow proper kebab-case naming convention\n');
    } else {
      console.log(`⚠️ Found ${invalidTestIds.length} test IDs with invalid naming:`);
      invalidTestIds.slice(0, 5).forEach(id => console.log(`   - ${id}`));
      console.log('');
    }
    
    // Try to check if specific components are present
    console.log('🔍 Checking for specific components...\n');
    
    const componentsToCheck = [
      { name: 'Cookie Banner', selector: '[data-testid*="cookie-banner"]' },
      { name: 'Credit Display', selector: '[data-testid*="credit-display"]' },
      { name: 'Landing Hero', selector: '[data-testid*="landing-hero"]' },
      { name: 'Navigation', selector: '[data-testid*="nav"]' },
      { name: 'Footer', selector: '[data-testid*="footer"]' }
    ];
    
    for (const component of componentsToCheck) {
      const count = await page.locator(component.selector).count();
      if (count > 0) {
        console.log(`✅ ${component.name}: ${count} elements found`);
      } else {
        console.log(`❌ ${component.name}: No elements found`);
      }
    }
    
    console.log('\n📊 Summary:');
    console.log(`   Total test IDs found: ${allTestIds.length}`);
    console.log(`   Naming convention compliance: ${((allTestIds.length - invalidTestIds.length) / allTestIds.length * 100).toFixed(1)}%`);
    
    // Check if we can navigate to other pages
    console.log('\n🔍 Testing other pages...\n');
    
    const pagesToTest = [
      { path: '/dashboard', name: 'Dashboard' },
      { path: '/redeem', name: 'Redeem' },
      { path: '/cookies', name: 'Cookies Policy' }
    ];
    
    for (const pageInfo of pagesToTest) {
      try {
        await page.goto(`https://localhost:3400${pageInfo.path}`, { 
          waitUntil: 'networkidle',
          timeout: 10000 
        });
        
        await page.waitForTimeout(1000);
        
        const pageTestIds = await page.locator('[data-testid]').count();
        console.log(`📄 ${pageInfo.name}: ${pageTestIds} test IDs`);
        
        // Check if we were redirected to auth
        if (page.url().includes('/auth') || page.url().includes('/signin')) {
          console.log(`   🔐 Requires authentication`);
        }
        
      } catch (error) {
        console.log(`❌ ${pageInfo.name}: Failed to load (${error.message})`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error during validation:', error.message);
  } finally {
    await browser.close();
  }
  
  console.log('\n✅ Test ID validation completed!');
}

// Run the validation
validateTestIds().catch(console.error);