package examples

import (
	"context"
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/verawat1234/adc-subscription-service/sdk"
)

// Example of how ADC Credit will integrate with the new subscription service
// This demonstrates the migration from internal subscription logic to external service calls

// SubscriptionServiceClient wraps the SDK client with ADC-specific logic
type SubscriptionServiceClient struct {
	client *sdk.Client
}

// NewSubscriptionServiceClient initializes the subscription service client
func NewSubscriptionServiceClient() *SubscriptionServiceClient {
	baseURL := os.Getenv("SUBSCRIPTION_SERVICE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:9100"
	}

	client := sdk.NewClient(
		baseURL,
		sdk.WithTimeout(30*time.Second),
		sdk.WithAPIKey(os.Getenv("SUBSCRIPTION_SERVICE_API_KEY")),
	)

	return &SubscriptionServiceClient{
		client: client,
	}
}

// Example 1: QR Code Generation with Subscription Limits
func (sc *SubscriptionServiceClient) HandleQRCodeGeneration(c *gin.Context) {
	// Extract context
	userID := c.GetString("user_id")
	organizationID := c.GetString("organization_id")
	shopIDStr := c.Query("shop_id")

	orgID, _ := uuid.Parse(organizationID)
	var shopID *uuid.UUID
	if shopIDStr != "" {
		id, _ := uuid.Parse(shopIDStr)
		shopID = &id
	}

	// Determine subscription type based on context
	subscriptionType := "personal"
	if shopID != nil {
		subscriptionType = "shop"
	}

	// Check if QR code generation is allowed
	canGenerate, err := sc.client.CanUseFeatureWithSubscription(
		c.Request.Context(),
		orgID,
		subscriptionType,
		"qr_codes_per_month",
		5, // Requesting 5 QR codes
		shopID,
		nil,
	)

	if err != nil {
		log.Printf("Error checking QR code limits: %v", err)
		c.JSON(500, gin.H{"error": "Failed to check subscription limits"})
		return
	}

	if !canGenerate {
		c.JSON(402, gin.H{
			"error": "QR code generation limit exceeded",
			"code":  "SUBSCRIPTION_LIMIT_EXCEEDED",
		})
		return
	}

	// Generate QR codes (existing logic)
	qrCodes := generateQRCodes(5) // Mock function

	// Record usage after successful generation
	err = sc.client.RecordFeatureUsage(
		c.Request.Context(),
		orgID,
		subscriptionType,
		"adc-credit",
		"qr_codes",
		5,
		shopID,
		nil,
		map[string]interface{}{
			"user_id":    userID,
			"endpoint":   "/api/qr-codes/generate",
			"batch_size": 5,
		},
	)

	if err != nil {
		log.Printf("Failed to record QR code usage: %v", err)
		// Don't fail the request, just log the error
	}

	c.JSON(200, gin.H{
		"success":  true,
		"qr_codes": qrCodes,
		"count":    len(qrCodes),
	})
}

// Example 2: Shop Creation with Subscription Limits
func (sc *SubscriptionServiceClient) HandleShopCreation(c *gin.Context) {
	organizationID := c.GetString("organization_id")
	userID := c.GetString("user_id")

	orgID, _ := uuid.Parse(organizationID)

	// Check shop creation limits
	canCreate, err := sc.client.CanUseFeatureWithSubscription(
		c.Request.Context(),
		orgID,
		"personal",
		"max_shops",
		1,
		nil,
		nil,
	)

	if err != nil {
		log.Printf("Error checking shop creation limits: %v", err)
		c.JSON(500, gin.H{"error": "Failed to check subscription limits"})
		return
	}

	if !canCreate {
		c.JSON(402, gin.H{
			"error": "Shop creation limit exceeded",
			"code":  "SUBSCRIPTION_LIMIT_EXCEEDED",
		})
		return
	}

	// Create shop (existing logic)
	shop := createShop(c) // Mock function

	// Record shop creation
	err = sc.client.RecordFeatureUsage(
		c.Request.Context(),
		orgID,
		"personal",
		"adc-credit",
		"shops",
		1,
		nil,
		nil,
		map[string]interface{}{
			"user_id":   userID,
			"shop_name": shop.Name,
			"shop_type": shop.Type,
		},
	)

	if err != nil {
		log.Printf("Failed to record shop creation: %v", err)
	}

	c.JSON(201, gin.H{
		"success": true,
		"shop":    shop,
	})
}

// Example 3: API Key Creation with Limits
func (sc *SubscriptionServiceClient) HandleAPIKeyCreation(c *gin.Context) {
	organizationID := c.GetString("organization_id")
	shopIDStr := c.Param("shop_id")

	orgID, _ := uuid.Parse(organizationID)
	shopID, _ := uuid.Parse(shopIDStr)

	// Check API key creation limits for this shop
	canCreate, err := sc.client.CanUseFeatureWithSubscription(
		c.Request.Context(),
		orgID,
		"shop",
		"max_api_keys_per_shop",
		1,
		&shopID,
		nil,
	)

	if err != nil {
		log.Printf("Error checking API key limits: %v", err)
		c.JSON(500, gin.H{"error": "Failed to check subscription limits"})
		return
	}

	if !canCreate {
		c.JSON(402, gin.H{
			"error": "API key creation limit exceeded for this shop",
			"code":  "SUBSCRIPTION_LIMIT_EXCEEDED",
		})
		return
	}

	// Create API key (existing logic)
	apiKey := createAPIKey(shopIDStr) // Mock function

	// Record API key creation
	err = sc.client.RecordFeatureUsage(
		c.Request.Context(),
		orgID,
		"shop",
		"adc-credit",
		"api_keys",
		1,
		&shopID,
		nil,
		map[string]interface{}{
			"shop_id":     shopIDStr,
			"key_type":    apiKey.Type,
			"permissions": apiKey.Permissions,
		},
	)

	if err != nil {
		log.Printf("Failed to record API key creation: %v", err)
	}

	c.JSON(201, gin.H{
		"success": true,
		"api_key": apiKey,
	})
}

// Example 4: Credit Consumption with Balance Checking
func (sc *SubscriptionServiceClient) HandleCreditConsumption(c *gin.Context) {
	organizationID := c.GetString("organization_id")
	creditsRequested := c.GetInt64("credits") // From request body

	orgID, _ := uuid.Parse(organizationID)

	// Check credit balance
	canConsume, err := sc.client.CanUseFeatureWithSubscription(
		c.Request.Context(),
		orgID,
		"personal",
		"credits",
		creditsRequested,
		nil,
		nil,
	)

	if err != nil {
		log.Printf("Error checking credit balance: %v", err)
		c.JSON(500, gin.H{"error": "Failed to check credit balance"})
		return
	}

	if !canConsume {
		c.JSON(402, gin.H{
			"error": "Insufficient credits",
			"code":  "INSUFFICIENT_CREDITS",
		})
		return
	}

	// Perform credit-consuming operation
	result := performCreditOperation(creditsRequested) // Mock function

	// Record credit consumption
	err = sc.client.RecordFeatureUsage(
		c.Request.Context(),
		orgID,
		"personal",
		"adc-credit",
		"credits",
		creditsRequested,
		nil,
		nil,
		map[string]interface{}{
			"operation_type": result.Type,
			"operation_id":   result.ID,
		},
	)

	if err != nil {
		log.Printf("Failed to record credit consumption: %v", err)
	}

	c.JSON(200, gin.H{
		"success": true,
		"result":  result,
		"credits_consumed": creditsRequested,
	})
}

// Example 5: Feature Access Check (Advanced Analytics)
func (sc *SubscriptionServiceClient) HandleAdvancedAnalytics(c *gin.Context) {
	organizationID := c.GetString("organization_id")
	shopIDStr := c.Query("shop_id")

	orgID, _ := uuid.Parse(organizationID)

	subscriptionType := "personal"
	var shopID *uuid.UUID
	if shopIDStr != "" {
		id, _ := uuid.Parse(shopIDStr)
		shopID = &id
		subscriptionType = "shop"
	}

	// Check if advanced analytics is available
	response, err := sc.client.CheckFeatureAccess(
		c.Request.Context(),
		orgID,
		"advanced_analytics",
		&sdk.FeatureAccessParams{
			SubscriptionType: subscriptionType,
			ShopID:           shopIDStr,
		},
	)

	if err != nil {
		log.Printf("Error checking feature access: %v", err)
		c.JSON(500, gin.H{"error": "Failed to check feature access"})
		return
	}

	if !response.FeatureAvailable {
		c.JSON(403, gin.H{
			"error":  "Advanced analytics not available in your subscription",
			"reason": response.Reason,
			"code":   "FEATURE_NOT_AVAILABLE",
		})
		return
	}

	// Generate advanced analytics
	analytics := generateAdvancedAnalytics(organizationID, shopIDStr) // Mock function

	c.JSON(200, gin.H{
		"success":   true,
		"analytics": analytics,
	})
}

// Example 6: Middleware for Automatic Usage Tracking
func (sc *SubscriptionServiceClient) UsageTrackingMiddleware(featureName string, usageAmount int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		organizationID := c.GetString("organization_id")
		shopIDStr := c.Query("shop_id")

		if organizationID == "" {
			c.Next()
			return
		}

		orgID, _ := uuid.Parse(organizationID)
		subscriptionType := "personal"
		var shopID *uuid.UUID

		if shopIDStr != "" {
			id, _ := uuid.Parse(shopIDStr)
			shopID = &id
			subscriptionType = "shop"
		}

		// Check limits before processing
		canUse, err := sc.client.CanUseFeatureWithSubscription(
			c.Request.Context(),
			orgID,
			subscriptionType,
			featureName,
			usageAmount,
			shopID,
			nil,
		)

		if err != nil {
			log.Printf("Error checking usage limits: %v", err)
			c.JSON(500, gin.H{"error": "Failed to check subscription limits"})
			c.Abort()
			return
		}

		if !canUse {
			c.JSON(402, gin.H{
				"error": "Usage limit exceeded",
				"code":  "SUBSCRIPTION_LIMIT_EXCEEDED",
			})
			c.Abort()
			return
		}

		// Process request
		c.Next()

		// Record usage if successful
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			go func() {
				ctx := context.Background()
				err := sc.client.RecordFeatureUsage(
					ctx,
					orgID,
					subscriptionType,
					"adc-credit",
					featureName,
					usageAmount,
					shopID,
					nil,
					map[string]interface{}{
						"endpoint":    c.Request.URL.Path,
						"method":      c.Request.Method,
						"status_code": c.Writer.Status(),
					},
				)
				if err != nil {
					log.Printf("Failed to record usage: %v", err)
				}
			}()
		}
	}
}

// Example 7: Subscription Status Check
func (sc *SubscriptionServiceClient) CheckSubscriptionStatus(c *gin.Context) {
	organizationID := c.GetString("organization_id")
	orgID, _ := uuid.Parse(organizationID)

	// Get usage stats
	stats, err := sc.client.GetSubscriptionUsageStats(
		c.Request.Context(),
		orgID,
		&sdk.UsageStatsParams{
			SubscriptionType: "personal",
			Months:           1,
		},
	)

	if err != nil {
		log.Printf("Error getting usage stats: %v", err)
		c.JSON(500, gin.H{"error": "Failed to get subscription status"})
		return
	}

	c.JSON(200, gin.H{
		"organization_id": organizationID,
		"usage_stats":     stats,
	})
}

// Mock functions for demonstration

type QRCode struct {
	ID   string `json:"id"`
	URL  string `json:"url"`
	Data string `json:"data"`
}

type Shop struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type APIKey struct {
	ID          string   `json:"id"`
	Key         string   `json:"key"`
	Type        string   `json:"type"`
	Permissions []string `json:"permissions"`
}

type OperationResult struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

func generateQRCodes(count int) []QRCode {
	qrCodes := make([]QRCode, count)
	for i := 0; i < count; i++ {
		qrCodes[i] = QRCode{
			ID:   uuid.New().String(),
			URL:  "https://qr.adc.com/" + uuid.New().String(),
			Data: "sample-data",
		}
	}
	return qrCodes
}

func createShop(c *gin.Context) Shop {
	return Shop{
		ID:   uuid.New().String(),
		Name: "Sample Shop",
		Type: "retail",
	}
}

func createAPIKey(shopID string) APIKey {
	return APIKey{
		ID:          uuid.New().String(),
		Key:         "ak_" + uuid.New().String(),
		Type:        "standard",
		Permissions: []string{"read", "write"},
	}
}

func performCreditOperation(credits int64) OperationResult {
	return OperationResult{
		ID:   uuid.New().String(),
		Type: "ai_generation",
	}
}

func generateAdvancedAnalytics(organizationID, shopID string) map[string]interface{} {
	return map[string]interface{}{
		"total_revenue":      12500.00,
		"conversion_rate":    0.15,
		"customer_lifetime":  180.5,
		"popular_products":   []string{"Product A", "Product B"},
		"traffic_sources":    map[string]int{"organic": 60, "paid": 25, "social": 15},
	}
}