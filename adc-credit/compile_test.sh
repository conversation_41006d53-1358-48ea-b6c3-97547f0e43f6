#!/bin/bash

echo "🔬 Testing Analytics Implementation Compilation"
echo "============================================="

cd ../adc-muti-languages/adc-subscription-service

echo "Testing analytics service compilation..."
if go build -o /tmp/analytics_test ./internal/application/services/analytics_service.go 2>&1; then
    echo "✅ Analytics service compiles successfully"
    rm -f /tmp/analytics_test
else
    echo "❌ Analytics service compilation failed"
fi

echo ""
echo "Testing analytics handler compilation..."
if go build -o /tmp/handler_test ./internal/presentation/handlers/analytics_handler.go 2>&1; then
    echo "✅ Analytics handler compiles successfully"
    rm -f /tmp/handler_test
else
    echo "❌ Analytics handler compilation failed"
fi

echo ""
echo "Testing full application build..."
if go build -o /tmp/app_test ./cmd/api/main.go 2>&1; then
    echo "✅ Full application builds successfully!"
    rm -f /tmp/app_test
else
    echo "❌ Full application build failed"
fi