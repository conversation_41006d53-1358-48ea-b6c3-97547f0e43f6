steps:
  # Build the backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--target=backend'
      - '-t'
      - 'gcr.io/${PROJECT_ID}/adc-credit-backend-prod:${BUILD_ID}'
      - '.'

  # Push the image to GCR
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/${PROJECT_ID}/adc-credit-backend-prod:${BUILD_ID}'

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'adc-credit-backend-prod'
      - '--image'
      - 'gcr.io/${PROJECT_ID}/adc-credit-backend-prod:${BUILD_ID}'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8400'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--add-cloudsql-instances'
      - 'scandine-457107:us-central1:adc-credit-prod'
      - '--set-env-vars'
      - 'DATABASE_URL=*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

images:
  - 'gcr.io/${PROJECT_ID}/adc-credit-backend-prod:${BUILD_ID}'