/**
 * Accessibility Testing Configuration
 * ADC Credit Service - WCAG 2.1 AA Compliance Configuration
 * 
 * Configures accessibility testing standards, utilities, and reporting
 * for comprehensive WCAG 2.1 Level AA compliance verification.
 */

import { defineConfig, devices } from '@playwright/test';

// WCAG 2.1 AA accessibility configuration
export const ACCESSIBILITY_CONFIG = {
  // WCAG Guidelines and Success Criteria
  WCAG_STANDARDS: {
    // Level A (minimum)
    LEVEL_A: [
      '1.1.1',  // Non-text Content
      '1.2.1',  // Audio-only and Video-only (Prerecorded)
      '1.2.2',  // Captions (Prerecorded)
      '1.2.3',  // Audio Description or Media Alternative (Prerecorded)
      '1.3.1',  // Info and Relationships
      '1.3.2',  // Meaningful Sequence
      '1.3.3',  // Sensory Characteristics
      '1.4.1',  // Use of Color
      '1.4.2',  // Audio Control
      '2.1.1',  // Keyboard
      '2.1.2',  // No Keyboard Trap
      '2.2.1',  // Timing Adjustable
      '2.2.2',  // Pause, Stop, Hide
      '2.3.1',  // Three Flashes or Below Threshold
      '2.4.1',  // Bypass Blocks
      '2.4.2',  // Page Titled
      '2.4.3',  // Focus Order
      '2.4.4',  // Link Purpose (In Context)
      '3.1.1',  // Language of Page
      '3.2.1',  // On Focus
      '3.2.2',  // On Input
      '3.3.1',  // Error Identification
      '3.3.2',  // Labels or Instructions
      '4.1.1',  // Parsing
      '4.1.2',  // Name, Role, Value
    ],
    
    // Level AA (target compliance)
    LEVEL_AA: [
      '1.2.4',  // Captions (Live)
      '1.2.5',  // Audio Description (Prerecorded)
      '1.4.3',  // Contrast (Minimum)
      '1.4.4',  // Resize text
      '1.4.5',  // Images of Text
      '2.4.5',  // Multiple Ways
      '2.4.6',  // Headings and Labels
      '2.4.7',  // Focus Visible
      '3.1.2',  // Language of Parts
      '3.2.3',  // Consistent Navigation
      '3.2.4',  // Consistent Identification
      '3.3.3',  // Error Suggestion
      '3.3.4',  // Error Prevention (Legal, Financial, Data)
    ]
  },

  // Color contrast requirements
  COLOR_CONTRAST: {
    NORMAL_TEXT: {
      LEVEL_AA: 4.5,
      LEVEL_AAA: 7.0
    },
    LARGE_TEXT: {
      LEVEL_AA: 3.0,
      LEVEL_AAA: 4.5
    },
    UI_COMPONENTS: {
      LEVEL_AA: 3.0,
      LEVEL_AAA: 4.5
    }
  },

  // Touch target sizes (mobile accessibility)
  TOUCH_TARGETS: {
    MINIMUM_SIZE: 44,     // 44x44px minimum
    RECOMMENDED_SIZE: 48, // 48x48px recommended
    SPACING: 8,           // 8px minimum spacing between targets
  },

  // Focus management
  FOCUS_MANAGEMENT: {
    VISIBLE_OUTLINE: true,
    LOGICAL_TAB_ORDER: true,
    FOCUS_TRAP_IN_MODALS: true,
    FOCUS_RESTORATION: true,
  },

  // Screen reader support
  SCREEN_READER: {
    ARIA_LABELS_REQUIRED: true,
    LANDMARK_ROLES: true,
    HEADING_HIERARCHY: true,
    LIVE_REGIONS: true,
    ALTERNATIVE_TEXT: true,
  },

  // Form accessibility
  FORMS: {
    LABELS_REQUIRED: true,
    ERROR_IDENTIFICATION: true,
    ERROR_SUGGESTIONS: true,
    REQUIRED_FIELD_INDICATION: true,
    FIELDSET_GROUPING: true,
  },

  // Testing configuration
  TESTING: {
    AXE_CORE_TAGS: ['wcag2a', 'wcag2aa', 'wcag21aa'],
    CUSTOM_RULES: [
      'color-contrast',
      'keyboard-navigation',
      'focus-management',
      'aria-labels',
      'heading-hierarchy',
      'form-accessibility'
    ]
  }
};

// Accessibility testing utilities
export class AccessibilityUtils {
  /**
   * Get color contrast ratio between two colors
   */
  static getContrastRatio(color1: string, color2: string): number {
    // This would need to be implemented with a color contrast library
    // For now, return a placeholder value
    return 4.5;
  }

  /**
   * Check if element meets touch target size requirements
   */
  static async checkTouchTargetSize(page: any, selector: string): Promise<boolean> {
    return await page.evaluate((sel: string) => {
      const element = document.querySelector(sel);
      if (!element) return false;
      
      const rect = element.getBoundingClientRect();
      const minSize = 44;
      
      return rect.width >= minSize && rect.height >= minSize;
    }, selector);
  }

  /**
   * Check heading hierarchy
   */
  static async checkHeadingHierarchy(page: any): Promise<{ valid: boolean; errors: string[] }> {
    return await page.evaluate(() => {
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
      const errors: string[] = [];
      let lastLevel = 0;
      
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        
        if (index === 0 && level !== 1) {
          errors.push('Page should start with h1');
        }
        
        if (level > lastLevel + 1) {
          errors.push(`Heading level ${level} follows h${lastLevel}, skipping levels`);
        }
        
        lastLevel = level;
      });
      
      return {
        valid: errors.length === 0,
        errors
      };
    });
  }

  /**
   * Check for keyboard trap
   */
  static async checkKeyboardTrap(page: any, containerSelector: string): Promise<boolean> {
    return await page.evaluate((container: string) => {
      const containerElement = document.querySelector(container);
      if (!containerElement) return true;
      
      const focusableElements = containerElement.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length === 0) return true;
      
      // Simple check: focus should be able to leave the container
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
      
      firstElement.focus();
      
      // Simulate Shift+Tab to try to leave container
      const shiftTabEvent = new KeyboardEvent('keydown', {
        key: 'Tab',
        shiftKey: true,
        bubbles: true
      });
      
      document.dispatchEvent(shiftTabEvent);
      
      // If focus is still in container after trying to leave, it might be trapped
      return !containerElement.contains(document.activeElement);
    }, containerSelector);
  }

  /**
   * Check ARIA label coverage
   */
  static async checkARIALabels(page: any): Promise<{ coverage: number; unlabeled: string[] }> {
    return await page.evaluate(() => {
      const interactiveElements = Array.from(document.querySelectorAll(
        'button, a, input, select, textarea, [role="button"], [role="link"], [role="tab"], [role="menuitem"]'
      ));
      
      const unlabeled: string[] = [];
      
      interactiveElements.forEach((element, index) => {
        const hasAccessibleName = !!(
          element.textContent?.trim() ||
          element.getAttribute('aria-label') ||
          element.getAttribute('aria-labelledby') ||
          element.getAttribute('title') ||
          (element.tagName === 'INPUT' && element.getAttribute('placeholder'))
        );
        
        if (!hasAccessibleName) {
          unlabeled.push(`${element.tagName}[${index}]`);
        }
      });
      
      const coverage = ((interactiveElements.length - unlabeled.length) / interactiveElements.length) * 100;
      
      return {
        coverage: Math.round(coverage),
        unlabeled
      };
    });
  }

  /**
   * Generate accessibility report
   */
  static generateAccessibilityReport(results: any) {
    const timestamp = new Date().toISOString();
    
    const report = {
      timestamp,
      summary: {
        totalViolations: results.violations?.length || 0,
        criticalViolations: results.violations?.filter((v: any) => v.impact === 'critical').length || 0,
        seriousViolations: results.violations?.filter((v: any) => v.impact === 'serious').length || 0,
        moderateViolations: results.violations?.filter((v: any) => v.impact === 'moderate').length || 0,
        minorViolations: results.violations?.filter((v: any) => v.impact === 'minor').length || 0,
        passes: results.passes?.length || 0,
      },
      violations: results.violations?.map((violation: any) => ({
        id: violation.id,
        description: violation.description,
        impact: violation.impact,
        help: violation.help,
        helpUrl: violation.helpUrl,
        nodes: violation.nodes?.length || 0,
        tags: violation.tags
      })) || [],
      wcagCompliance: this.calculateWCAGCompliance(results),
      recommendations: this.generateRecommendations(results)
    };
    
    return report;
  }

  /**
   * Calculate WCAG compliance level
   */
  private static calculateWCAGCompliance(results: any) {
    const violations = results.violations || [];
    const wcagAAViolations = violations.filter((v: any) => 
      v.tags.includes('wcag2aa') || v.tags.includes('wcag21aa')
    );
    
    const wcagAViolations = violations.filter((v: any) => 
      v.tags.includes('wcag2a') || v.tags.includes('wcag21a')
    );
    
    return {
      levelA: wcagAViolations.length === 0,
      levelAA: wcagAAViolations.length === 0,
      levelAAViolations: wcagAAViolations.length,
      levelAViolations: wcagAViolations.length
    };
  }

  /**
   * Generate accessibility recommendations
   */
  private static generateRecommendations(results: any) {
    const violations = results.violations || [];
    const recommendations: string[] = [];
    
    // Color contrast recommendations
    const contrastViolations = violations.filter((v: any) => v.id === 'color-contrast');
    if (contrastViolations.length > 0) {
      recommendations.push('Improve color contrast ratios to meet WCAG AA standards (4.5:1 for normal text, 3:1 for large text)');
    }
    
    // Keyboard navigation recommendations
    const keyboardViolations = violations.filter((v: any) => 
      v.tags.includes('keyboard') || v.id.includes('focus')
    );
    if (keyboardViolations.length > 0) {
      recommendations.push('Ensure all interactive elements are keyboard accessible with visible focus indicators');
    }
    
    // ARIA recommendations
    const ariaViolations = violations.filter((v: any) => 
      v.id.includes('aria') || v.id.includes('label')
    );
    if (ariaViolations.length > 0) {
      recommendations.push('Add proper ARIA labels and roles to improve screen reader support');
    }
    
    // Form recommendations
    const formViolations = violations.filter((v: any) => 
      v.id.includes('label') || v.id.includes('form')
    );
    if (formViolations.length > 0) {
      recommendations.push('Associate form controls with descriptive labels');
    }
    
    return recommendations;
  }

  /**
   * Create accessibility baseline
   */
  static createAccessibilityBaseline(results: any) {
    return {
      createdAt: new Date().toISOString(),
      baseline: {
        violations: results.violations?.length || 0,
        wcagAACompliant: this.calculateWCAGCompliance(results).levelAA,
        criticalIssues: results.violations?.filter((v: any) => v.impact === 'critical').length || 0,
        seriousIssues: results.violations?.filter((v: any) => v.impact === 'serious').length || 0,
      }
    };
  }
}

// Playwright configuration for accessibility testing
export default defineConfig({
  testDir: './tests/accessibility',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/accessibility-results.json' }],
    ['junit', { outputFile: 'test-results/accessibility-junit.xml' }],
  ],
  use: {
    baseURL: 'http://localhost:3800',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  
  projects: [
    {
      name: 'accessibility-chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'accessibility-firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'accessibility-webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'accessibility-mobile',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'accessibility-tablet',
      use: { ...devices['iPad Pro'] },
    },
    {
      name: 'accessibility-high-contrast',
      use: {
        ...devices['Desktop Chrome'],
        // Simulate high contrast mode
        colorScheme: 'dark',
      },
    },
  ],
});

export { ACCESSIBILITY_CONFIG as default };