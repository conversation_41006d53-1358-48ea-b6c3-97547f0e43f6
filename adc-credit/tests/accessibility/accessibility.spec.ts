/**
 * Accessibility Testing Suite
 * ADC Credit Service - WCAG 2.1 AA Compliance Testing
 * 
 * Tests for accessibility compliance across all major components,
 * following WCAG 2.1 Level AA guidelines for inclusive user experience.
 * 
 * Accessibility Categories:
 * - Keyboard Navigation (Focus management, tab order)
 * - Screen Reader Support (ARIA labels, roles, descriptions)
 * - Color Contrast (Text visibility, UI element contrast)
 * - Form Accessibility (Labels, validation, error messaging)
 * - Interactive Elements (Button states, link accessibility)
 * - Semantic HTML (Proper heading hierarchy, landmarks)
 * - Focus Indicators (Visible focus states)
 * - Alternative Text (Image descriptions, icons)
 */

import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

// WCAG 2.1 AA accessibility standards
const ACCESSIBILITY_STANDARDS = {
  // Color contrast ratios (WCAG 2.1 AA)
  COLOR_CONTRAST: {
    NORMAL_TEXT: 4.5,      // Normal text minimum contrast ratio
    LARGE_TEXT: 3.0,       // Large text (18pt+ or 14pt+ bold) minimum contrast ratio
    UI_COMPONENTS: 3.0,    // UI components and graphical objects
  },
  
  // Focus management
  FOCUS: {
    VISIBLE_OUTLINE: true,  // Focus indicators must be visible
    LOGICAL_ORDER: true,    // Tab order should be logical
    KEYBOARD_TRAP: false,   // No keyboard traps allowed
  },
  
  // ARIA requirements
  ARIA: {
    REQUIRED_LABELS: true,  // Interactive elements need accessible names
    VALID_ATTRIBUTES: true, // ARIA attributes must be valid
    PROPER_ROLES: true,     // Elements must have appropriate roles
  },
  
  // Form accessibility
  FORMS: {
    LABELS_REQUIRED: true,  // Form inputs must have labels
    ERROR_IDENTIFICATION: true, // Errors must be clearly identified
    INSTRUCTIONS_AVAILABLE: true, // Instructions provided where needed
  }
};

// Test Group: Automated Accessibility Testing with axe-core
test.describe('Automated Accessibility Testing', () => {
  test('should pass axe accessibility tests on dashboard', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page.locator('[data-testid="credit-dashboard-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should pass axe accessibility tests on shops page', async ({ page }) => {
    await page.goto('/dashboard/shops');
    await expect(page.locator('[data-testid="credit-shops-list-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should pass axe accessibility tests on API keys page', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should pass axe accessibility tests on customer dashboard', async ({ page }) => {
    await page.goto('/dashboard/customer');
    await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should pass axe accessibility tests on subscriptions page', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    await expect(page.locator('[data-testid="subscriptions-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should pass axe accessibility tests on authentication pages', async ({ page }) => {
    await page.goto('/auth/customer-login');
    await expect(page.locator('[data-testid="auth-customer-login-container"]')).toBeVisible();
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
});

// Test Group: Keyboard Navigation
test.describe('Keyboard Navigation', () => {
  test('should support keyboard navigation on dashboard', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Test Tab navigation through interactive elements
    await page.keyboard.press('Tab');
    let focusedElement = await page.evaluate(() => document.activeElement?.tagName);
    expect(['BUTTON', 'A', 'INPUT', 'SELECT']).toContain(focusedElement);
    
    // Test that focus is visible
    const focusedElementOutline = await page.evaluate(() => {
      const element = document.activeElement as HTMLElement;
      if (element) {
        const styles = window.getComputedStyle(element);
        return styles.outline !== 'none' || styles.boxShadow !== 'none';
      }
      return false;
    });
    expect(focusedElementOutline).toBe(true);
    
    // Test Shift+Tab (reverse navigation)
    await page.keyboard.press('Shift+Tab');
    expect(await page.evaluate(() => document.activeElement !== null)).toBe(true);
  });

  test('should support keyboard navigation on forms', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Test form navigation
    await page.keyboard.press('Tab');
    await page.keyboard.type('Test Shop Name');
    
    await page.keyboard.press('Tab');
    await page.keyboard.type('Test Description');
    
    // Test form submission with Enter key
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    // Should handle form submission appropriately
  });

  test('should support keyboard navigation in modals', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Open modal with keyboard
    await page.locator('[data-testid="api-keys-create-button"]').focus();
    await page.keyboard.press('Enter');
    
    // Verify modal is open and focusable
    await expect(page.locator('[data-testid="api-keys-create-dialog"]')).toBeVisible();
    
    // Test Tab navigation within modal
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => {
      const element = document.activeElement;
      return element?.getAttribute('data-testid') || element?.tagName;
    });
    
    expect(focusedElement).toBeTruthy();
    
    // Test Escape key to close modal
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="api-keys-create-dialog"]')).not.toBeVisible();
  });

  test('should trap focus in modal dialogs', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    
    // Mock subscription tiers
    await page.route('**/api/subscription-tiers', async (route) => {
      await route.fulfill({
        json: [
          { id: 1, name: 'Free', price: 0, credit_limit: 1000, features: ['Basic'] }
        ]
      });
    });
    
    await page.reload();
    
    // Open subscription confirmation dialog
    await page.locator('[data-testid="subscriptions-plan-select-1"]').click();
    await expect(page.locator('[data-testid="subscriptions-confirm-dialog"]')).toBeVisible();
    
    // Get all focusable elements in modal
    const focusableElements = await page.locator('[data-testid="subscriptions-confirm-dialog"] button, [data-testid="subscriptions-confirm-dialog"] input, [data-testid="subscriptions-confirm-dialog"] select, [data-testid="subscriptions-confirm-dialog"] textarea, [data-testid="subscriptions-confirm-dialog"] a[href]').count();
    
    if (focusableElements > 1) {
      // Tab through all elements
      for (let i = 0; i < focusableElements + 1; i++) {
        await page.keyboard.press('Tab');
      }
      
      // Focus should cycle back to first element (focus trap)
      const firstElementFocused = await page.evaluate(() => {
        const dialog = document.querySelector('[data-testid="subscriptions-confirm-dialog"]');
        const focusableElements = dialog?.querySelectorAll('button, input, select, textarea, a[href]');
        return document.activeElement === focusableElements?.[0];
      });
      
      expect(firstElementFocused).toBe(true);
    }
  });

  test('should support arrow key navigation in tab lists', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    
    // Focus on tab list
    await page.locator('[data-testid="subscriptions-tab-personal"]').focus();
    
    // Test arrow key navigation
    await page.keyboard.press('ArrowRight');
    const focusedTab = await page.evaluate(() => {
      return document.activeElement?.getAttribute('data-testid');
    });
    
    expect(focusedTab).toBe('subscriptions-tab-shop');
    
    // Test arrow key navigation in reverse
    await page.keyboard.press('ArrowLeft');
    const backToFirstTab = await page.evaluate(() => {
      return document.activeElement?.getAttribute('data-testid');
    });
    
    expect(backToFirstTab).toBe('subscriptions-tab-personal');
  });
});

// Test Group: Screen Reader Support
test.describe('Screen Reader Support', () => {
  test('should have proper heading hierarchy', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check heading structure (h1 -> h2 -> h3, etc.)
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
    const headingLevels = await page.locator('h1, h2, h3, h4, h5, h6').evaluateAll(elements => 
      elements.map(el => parseInt(el.tagName.charAt(1)))
    );
    
    expect(headings.length).toBeGreaterThan(0);
    expect(headingLevels[0]).toBe(1); // Should start with h1
  });

  test('should have accessible labels for form inputs', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Check that all inputs have labels
    const inputs = page.locator('input[type="text"], input[type="email"], textarea, select');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      
      // Check for associated label or aria-label
      const hasLabel = await input.evaluate(el => {
        const id = el.id;
        const label = id ? document.querySelector(`label[for="${id}"]`) : null;
        const ariaLabel = el.getAttribute('aria-label');
        const ariaLabelledBy = el.getAttribute('aria-labelledby');
        
        return !!(label || ariaLabel || ariaLabelledBy);
      });
      
      expect(hasLabel).toBe(true);
    }
  });

  test('should have proper ARIA roles and properties', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    // Check for proper button roles
    const buttons = page.locator('button, [role="button"]');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      
      // Each button should have accessible text
      const hasAccessibleName = await button.evaluate(el => {
        const textContent = el.textContent?.trim();
        const ariaLabel = el.getAttribute('aria-label');
        const ariaLabelledBy = el.getAttribute('aria-labelledby');
        const title = el.getAttribute('title');
        
        return !!(textContent || ariaLabel || ariaLabelledBy || title);
      });
      
      expect(hasAccessibleName).toBe(true);
    }
  });

  test('should announce loading states to screen readers', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Mock delayed API response to test loading state
    await page.route('**/api/api-keys', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });
    
    await page.reload();
    
    // Check for aria-live regions or loading indicators
    const loadingIndicator = page.locator('[data-testid="api-keys-loading-spinner"], [aria-live], [role="status"]').first();
    
    if (await loadingIndicator.isVisible()) {
      const hasAriaLive = await loadingIndicator.evaluate(el => {
        return el.hasAttribute('aria-live') || el.hasAttribute('role');
      });
      
      expect(hasAriaLive).toBe(true);
    }
  });

  test('should provide alternative text for images and icons', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    // Check all images have alt text
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const hasAltText = await img.evaluate(el => {
        const alt = el.getAttribute('alt');
        const ariaLabel = el.getAttribute('aria-label');
        const ariaLabelledBy = el.getAttribute('aria-labelledby');
        
        return !!(alt !== null || ariaLabel || ariaLabelledBy);
      });
      
      expect(hasAltText).toBe(true);
    }
    
    // Check that decorative images have empty alt text
    const decorativeImages = page.locator('img[alt=""], img[role="presentation"]');
    const decorativeCount = await decorativeImages.count();
    
    // This is acceptable - decorative images should have empty alt
    console.log(`Found ${decorativeCount} decorative images with proper empty alt text`);
  });

  test('should have proper landmarks and regions', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check for main landmark
    const main = page.locator('main, [role="main"]');
    await expect(main).toHaveCount(1);
    
    // Check for navigation landmarks
    const nav = page.locator('nav, [role="navigation"]');
    expect(await nav.count()).toBeGreaterThan(0);
    
    // Check for complementary regions if present
    const complementary = page.locator('[role="complementary"], aside');
    // This is optional, just log if found
    const complementaryCount = await complementary.count();
    console.log(`Found ${complementaryCount} complementary regions`);
  });
});

// Test Group: Color Contrast and Visual Accessibility
test.describe('Color Contrast and Visual Accessibility', () => {
  test('should meet color contrast requirements', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Use axe-core to specifically test color contrast
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .include('[data-testid="credit-dashboard-container"]')
      .analyze();

    const colorContrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    );
    
    expect(colorContrastViolations).toHaveLength(0);
  });

  test('should be usable without color alone', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    // Check that status indicators don't rely solely on color
    const statusElements = page.locator('[data-testid*="status"], .status, .error, .success, .warning');
    const statusCount = await statusElements.count();
    
    for (let i = 0; i < statusCount; i++) {
      const element = statusElements.nth(i);
      
      // Check for text content or icons in addition to color
      const hasNonColorIndicator = await element.evaluate(el => {
        const textContent = el.textContent?.trim();
        const hasIcon = el.querySelector('svg, .icon, [class*="icon"]');
        const ariaLabel = el.getAttribute('aria-label');
        
        return !!(textContent || hasIcon || ariaLabel);
      });
      
      expect(hasNonColorIndicator).toBe(true);
    }
  });

  test('should support dark mode accessibility', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Toggle dark mode if available
    const darkModeToggle = page.locator('[data-testid*="theme"], [data-testid*="dark"], button[aria-label*="dark"], button[aria-label*="theme"]').first();
    
    if (await darkModeToggle.isVisible()) {
      await darkModeToggle.click();
      
      // Wait for theme change
      await page.waitForTimeout(500);
      
      // Test accessibility in dark mode
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    } else {
      console.log('Dark mode toggle not found, skipping dark mode accessibility test');
    }
  });

  test('should maintain accessibility at high zoom levels', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Set zoom to 200% (simulating browser zoom)
    await page.evaluate(() => {
      document.body.style.zoom = '2';
    });
    
    // Check that content is still accessible
    await expect(page.locator('[data-testid="credit-dashboard-container"]')).toBeVisible();
    
    // Test that interactive elements are still clickable
    const buttons = page.locator('button').first();
    if (await buttons.isVisible()) {
      await expect(buttons).toBeEnabled();
    }
    
    // Reset zoom
    await page.evaluate(() => {
      document.body.style.zoom = '1';
    });
  });
});

// Test Group: Focus Management
test.describe('Focus Management', () => {
  test('should have visible focus indicators', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Test focus visibility on interactive elements
    const interactiveElements = page.locator('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const elementCount = await interactiveElements.count();
    
    // Test a few representative elements
    const testCount = Math.min(5, elementCount);
    
    for (let i = 0; i < testCount; i++) {
      const element = interactiveElements.nth(i);
      
      if (await element.isVisible()) {
        await element.focus();
        
        // Check that focus is visible
        const hasFocusIndicator = await element.evaluate(el => {
          const styles = window.getComputedStyle(el);
          const pseudoStyles = window.getComputedStyle(el, ':focus');
          
          return (
            styles.outline !== 'none' ||
            styles.boxShadow !== 'none' ||
            pseudoStyles.outline !== 'none' ||
            pseudoStyles.boxShadow !== 'none' ||
            styles.border !== pseudoStyles.border
          );
        });
        
        expect(hasFocusIndicator).toBe(true);
      }
    }
  });

  test('should manage focus correctly in dynamic content', async ({ page }) => {
    await page.goto('/dashboard/shops');
    
    // Mock shop creation
    await page.route('**/api/shops', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          json: { id: 'new-shop', name: 'New Shop', slug: 'new-shop' }
        });
      } else {
        await route.continue();
      }
    });
    
    // Focus should be managed appropriately when new content appears
    await page.goto('/dashboard/shops/create');
    
    // Submit form and check focus management
    await page.locator('[data-testid="shop-create-name-input"]').fill('Test Shop');
    await page.locator('[data-testid="shop-create-submit-button"]').click();
    
    // Focus should be managed appropriately after form submission
    // (Implementation depends on specific behavior)
  });

  test('should restore focus after modal closes', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Focus on create button and open modal
    const createButton = page.locator('[data-testid="api-keys-create-button"]');
    await createButton.focus();
    await createButton.click();
    
    // Close modal with Escape
    await page.keyboard.press('Escape');
    
    // Focus should return to the trigger button
    const focusedElement = await page.evaluate(() => {
      return document.activeElement?.getAttribute('data-testid');
    });
    
    expect(focusedElement).toBe('api-keys-create-button');
  });
});

// Test Group: Form Accessibility
test.describe('Form Accessibility', () => {
  test('should provide clear error messages', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Submit form without required fields
    await page.locator('[data-testid="shop-create-submit-button"]').click();
    
    // Check for error messages
    const errorMessages = page.locator('[role="alert"], .error-message, [data-testid*="error"]');
    
    if (await errorMessages.count() > 0) {
      // Error messages should be associated with form fields
      const firstError = errorMessages.first();
      const hasProperAssociation = await firstError.evaluate(el => {
        const id = el.id;
        const associatedField = id ? document.querySelector(`[aria-describedby*="${id}"]`) : null;
        const parentForm = el.closest('form');
        
        return !!(associatedField || parentForm);
      });
      
      expect(hasProperAssociation).toBe(true);
    }
  });

  test('should provide helpful instructions', async ({ page }) => {
    await page.goto('/auth/customer-login');
    
    // Check for form instructions
    const instructions = page.locator('[data-testid*="instructions"], .instructions, [role="note"], .help-text');
    
    if (await instructions.count() > 0) {
      // Instructions should be accessible
      const firstInstruction = instructions.first();
      await expect(firstInstruction).toBeVisible();
    }
  });

  test('should support required field indicators', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Check for required field indicators
    const requiredFields = page.locator('input[required], select[required], textarea[required]');
    const requiredCount = await requiredFields.count();
    
    for (let i = 0; i < requiredCount; i++) {
      const field = requiredFields.nth(i);
      
      // Check for visual and programmatic indication of required status
      const hasRequiredIndicator = await field.evaluate(el => {
        const hasAsterisk = el.parentElement?.textContent?.includes('*');
        const hasAriaRequired = el.hasAttribute('aria-required');
        const hasRequiredAttribute = el.hasAttribute('required');
        const labelText = document.querySelector(`label[for="${el.id}"]`)?.textContent || '';
        const hasRequiredInLabel = labelText.includes('required') || labelText.includes('*');
        
        return !!(hasAsterisk || hasAriaRequired || hasRequiredAttribute || hasRequiredInLabel);
      });
      
      expect(hasRequiredIndicator).toBe(true);
    }
  });
});

// Test Group: Mobile Accessibility
test.describe('Mobile Accessibility', () => {
  test('should be accessible on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 });
    await page.goto('/dashboard/customer');
    
    // Run accessibility scan on mobile
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should support touch accessibility', async ({ page }) => {
    await page.setViewportSize({ width: 390, height: 844 });
    await page.goto('/dashboard/customer');
    
    // Check touch target sizes (minimum 44x44px)
    const touchTargets = page.locator('button, a, input[type="button"], input[type="submit"], [role="button"]');
    const targetCount = await touchTargets.count();
    
    // Test a representative sample
    const testCount = Math.min(5, targetCount);
    
    for (let i = 0; i < testCount; i++) {
      const target = touchTargets.nth(i);
      
      if (await target.isVisible()) {
        const boundingBox = await target.boundingBox();
        
        if (boundingBox) {
          expect(boundingBox.width).toBeGreaterThanOrEqual(44);
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
        }
      }
    }
  });
});

// Test Group: Accessibility Performance
test.describe('Accessibility Performance', () => {
  test('should load accessibility features quickly', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard');
    
    // Wait for page to be fully accessible
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[role="main"], main')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`Accessibility features load time: ${loadTime}ms`);
    
    // Accessibility features should load quickly
    expect(loadTime).toBeLessThan(3000);
  });

  test('should maintain accessibility during interactions', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    
    // Perform interactions and verify accessibility is maintained
    await page.locator('[data-testid="subscriptions-tab-shop"]').click();
    
    // Run accessibility scan after interaction
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
});