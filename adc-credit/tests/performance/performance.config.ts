/**
 * Performance Testing Configuration
 * ADC Credit Service - Performance Testing Setup and Utilities
 * 
 * Configures performance testing thresholds, monitoring utilities,
 * and reporting mechanisms for comprehensive frontend performance analysis.
 */

import { defineConfig, devices } from '@playwright/test';

// Performance monitoring configuration
export const PERFORMANCE_CONFIG = {
  // Core Web Vitals thresholds (Google recommendations)
  WEB_VITALS: {
    LCP: {
      GOOD: 2500,       // Good: ≤ 2.5s
      NEEDS_IMPROVEMENT: 4000, // Needs improvement: 2.5s - 4s
      POOR: 4001        // Poor: > 4s
    },
    FID: {
      GOOD: 100,        // Good: ≤ 100ms
      NEEDS_IMPROVEMENT: 300,  // Needs improvement: 100ms - 300ms
      POOR: 301         // Poor: > 300ms
    },
    CLS: {
      GOOD: 0.1,        // Good: ≤ 0.1
      NEEDS_IMPROVEMENT: 0.25, // Needs improvement: 0.1 - 0.25
      POOR: 0.26        // Poor: > 0.25
    },
    FCP: {
      GOOD: 1800,       // Good: ≤ 1.8s
      NEEDS_IMPROVEMENT: 3000, // Needs improvement: 1.8s - 3s
      POOR: 3001        // Poor: > 3s
    },
    TTI: {
      GOOD: 3800,       // Good: ≤ 3.8s
      NEEDS_IMPROVEMENT: 7300, // Needs improvement: 3.8s - 7.3s
      POOR: 7301        // Poor: > 7.3s
    }
  },

  // Application-specific performance targets
  APPLICATION: {
    PAGE_LOAD: {
      DASHBOARD: 3000,          // Dashboard should load in 3s
      SHOPS: 2500,              // Shops page should load in 2.5s
      API_KEYS: 2000,           // API keys page should load in 2s
      CUSTOMER: 2500,           // Customer dashboard should load in 2.5s
      SUBSCRIPTIONS: 3500,      // Subscriptions page should load in 3.5s
    },
    API_RESPONSE: {
      FAST: 200,                // Fast API responses
      ACCEPTABLE: 500,          // Acceptable API responses
      SLOW: 1000,               // Slow but tolerable API responses
      TIMEOUT: 5000,            // API timeout threshold
    },
    INTERACTION: {
      CLICK_RESPONSE: 16,       // 60fps = 16ms per frame
      FORM_INPUT: 50,           // Form input response time
      NAVIGATION: 200,          // Page navigation time
      MODAL_OPEN: 100,          // Modal opening time
    },
    MEMORY: {
      BASELINE: 20000000,       // 20MB baseline memory usage
      MAX_USAGE: 100000000,     // 100MB maximum memory usage
      LEAK_THRESHOLD: 50,       // 50% memory increase threshold
    },
    BUNDLE: {
      MAIN_CHUNK: 200000,       // 200KB main bundle
      VENDOR_CHUNK: 300000,     // 300KB vendor bundle
      TOTAL_SIZE: 1000000,      // 1MB total bundle size
    }
  },

  // Network conditions for testing
  NETWORK_CONDITIONS: {
    FAST_3G: {
      downloadThroughput: 1500000,  // 1.5 Mbps
      uploadThroughput: 750000,     // 750 Kbps
      latency: 40,                  // 40ms latency
    },
    SLOW_3G: {
      downloadThroughput: 500000,   // 500 Kbps
      uploadThroughput: 500000,     // 500 Kbps
      latency: 400,                 // 400ms latency
    },
    OFFLINE: {
      downloadThroughput: 0,
      uploadThroughput: 0,
      latency: 0,
    }
  },

  // Device configurations for performance testing
  DEVICES: {
    LOW_END_MOBILE: {
      name: 'Low-end Mobile',
      viewport: { width: 375, height: 667 },
      deviceScaleFactor: 2,
      isMobile: true,
      hasTouch: true,
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    MID_RANGE_MOBILE: {
      name: 'Mid-range Mobile',
      viewport: { width: 390, height: 844 },
      deviceScaleFactor: 3,
      isMobile: true,
      hasTouch: true,
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    TABLET: {
      name: 'Tablet',
      viewport: { width: 768, height: 1024 },
      deviceScaleFactor: 2,
      isMobile: false,
      hasTouch: true,
      userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    DESKTOP: {
      name: 'Desktop',
      viewport: { width: 1920, height: 1080 },
      deviceScaleFactor: 1,
      isMobile: false,
      hasTouch: false,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  }
};

// Performance utility functions
export class PerformanceUtils {
  /**
   * Measure Core Web Vitals for a page
   */
  static async measureWebVitals(page: any) {
    return await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals: any = {};
        
        // Measure LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Measure FID
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            vitals.fid = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        
        // Measure CLS
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          vitals.cls = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        
        // Measure FCP
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime;
            }
          });
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        
        // Wait for measurements to complete
        setTimeout(() => {
          resolve(vitals);
        }, 5000);
      });
    });
  }

  /**
   * Measure page load timing
   */
  static async measurePageTiming(page: any) {
    return await page.evaluate(() => {
      const timing = performance.timing;
      return {
        navigationStart: timing.navigationStart,
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        firstByte: timing.responseStart - timing.navigationStart,
        domInteractive: timing.domInteractive - timing.navigationStart,
      };
    });
  }

  /**
   * Measure JavaScript execution time
   */
  static async measureJSExecution(page: any, fn: Function) {
    return await page.evaluate((func: string) => {
      const start = performance.now();
      eval(func);
      return performance.now() - start;
    }, fn.toString());
  }

  /**
   * Measure memory usage
   */
  static async measureMemoryUsage(page: any) {
    return await page.evaluate(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          totalMB: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        };
      }
      return null;
    });
  }

  /**
   * Monitor network requests and measure performance
   */
  static async monitorNetworkPerformance(page: any, callback: Function) {
    const networkData: any[] = [];
    
    page.on('response', (response: any) => {
      networkData.push({
        url: response.url(),
        status: response.status(),
        size: response.headers()['content-length'] || 0,
        timing: response.timing(),
        method: response.request().method(),
      });
    });
    
    await callback();
    
    return networkData;
  }

  /**
   * Generate performance report
   */
  static generateReport(metrics: any) {
    const report = {
      timestamp: new Date().toISOString(),
      webVitals: {
        lcp: {
          value: metrics.lcp,
          rating: this.rateMetric(metrics.lcp, PERFORMANCE_CONFIG.WEB_VITALS.LCP),
        },
        fid: {
          value: metrics.fid,
          rating: this.rateMetric(metrics.fid, PERFORMANCE_CONFIG.WEB_VITALS.FID),
        },
        cls: {
          value: metrics.cls,
          rating: this.rateMetric(metrics.cls, PERFORMANCE_CONFIG.WEB_VITALS.CLS),
        },
        fcp: {
          value: metrics.fcp,
          rating: this.rateMetric(metrics.fcp, PERFORMANCE_CONFIG.WEB_VITALS.FCP),
        },
      },
      pageLoad: metrics.pageLoad,
      memory: metrics.memory,
      network: metrics.network,
    };
    
    return report;
  }

  /**
   * Rate a metric as good, needs improvement, or poor
   */
  private static rateMetric(value: number, thresholds: any) {
    if (value <= thresholds.GOOD) return 'good';
    if (value <= thresholds.NEEDS_IMPROVEMENT) return 'needs-improvement';
    return 'poor';
  }

  /**
   * Create performance baseline from current metrics
   */
  static createBaseline(metrics: any) {
    return {
      createdAt: new Date().toISOString(),
      baseline: {
        lcp: metrics.lcp * 1.1,      // 10% buffer
        fid: metrics.fid * 1.1,      // 10% buffer
        cls: metrics.cls * 1.1,      // 10% buffer
        fcp: metrics.fcp * 1.1,      // 10% buffer
        loadTime: metrics.loadTime * 1.1, // 10% buffer
        memoryUsage: metrics.memoryUsage * 1.2, // 20% buffer for memory
      }
    };
  }

  /**
   * Compare current metrics against baseline
   */
  static compareToBaseline(currentMetrics: any, baseline: any) {
    const comparison = {
      lcp: {
        current: currentMetrics.lcp,
        baseline: baseline.lcp,
        regression: currentMetrics.lcp > baseline.lcp,
        change: ((currentMetrics.lcp - baseline.lcp) / baseline.lcp * 100).toFixed(2) + '%'
      },
      fid: {
        current: currentMetrics.fid,
        baseline: baseline.fid,
        regression: currentMetrics.fid > baseline.fid,
        change: ((currentMetrics.fid - baseline.fid) / baseline.fid * 100).toFixed(2) + '%'
      },
      cls: {
        current: currentMetrics.cls,
        baseline: baseline.cls,
        regression: currentMetrics.cls > baseline.cls,
        change: ((currentMetrics.cls - baseline.cls) / baseline.cls * 100).toFixed(2) + '%'
      },
      loadTime: {
        current: currentMetrics.loadTime,
        baseline: baseline.loadTime,
        regression: currentMetrics.loadTime > baseline.loadTime,
        change: ((currentMetrics.loadTime - baseline.loadTime) / baseline.loadTime * 100).toFixed(2) + '%'
      }
    };
    
    return comparison;
  }
}

// Playwright configuration for performance testing
export default defineConfig({
  testDir: './tests/performance',
  fullyParallel: false,  // Run performance tests sequentially for accurate measurements
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: 1,  // Use single worker for performance tests
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/performance-results.json' }],
  ],
  use: {
    baseURL: 'http://localhost:3800',
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  
  projects: [
    {
      name: 'performance-desktop',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'performance-mobile',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'performance-tablet',
      use: { ...devices['iPad Pro'] },
    },
    {
      name: 'performance-slow-network',
      use: {
        ...devices['Desktop Chrome'],
        // Simulate slow network
        extraHTTPHeaders: {
          'User-Agent': 'Mozilla/5.0 (compatible; Performance Test Bot)',
        },
      },
    },
  ],
});

export { PERFORMANCE_CONFIG as default };