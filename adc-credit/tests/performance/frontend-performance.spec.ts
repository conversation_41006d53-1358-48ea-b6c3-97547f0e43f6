/**
 * Frontend Performance Testing Suite
 * ADC Credit Service - Comprehensive Performance Benchmarking
 * 
 * Tests frontend application performance across key user workflows,
 * measuring Core Web Vitals, bundle sizes, API response times,
 * and user experience metrics.
 * 
 * Performance Categories:
 * - Page Load Performance (LCP, FID, CLS)
 * - Runtime Performance (JavaScript execution, memory usage)
 * - Network Performance (bundle sizes, API latency)
 * - User Experience Metrics (navigation timing, interaction delays)
 * - Bundle Analysis (chunk sizes, optimization opportunities)
 */

import { test, expect } from '@playwright/test';

// Performance thresholds based on Core Web Vitals and industry standards
const PERFORMANCE_THRESHOLDS = {
  // Core Web Vitals
  LCP_THRESHOLD: 2500,        // Largest Contentful Paint (ms)
  FID_THRESHOLD: 100,         // First Input Delay (ms)
  CLS_THRESHOLD: 0.1,         // Cumulative Layout Shift
  
  // Custom thresholds
  TTI_THRESHOLD: 3500,        // Time to Interactive (ms)
  FCP_THRESHOLD: 1800,        // First Contentful Paint (ms)
  LOAD_THRESHOLD: 5000,       // Page Load Complete (ms)
  
  // Network thresholds
  API_RESPONSE_THRESHOLD: 1000,  // API response time (ms)
  BUNDLE_SIZE_THRESHOLD: 500000, // JavaScript bundle size (bytes)
  
  // Memory thresholds
  MEMORY_USAGE_THRESHOLD: 50000000, // 50MB memory usage
  
  // Interaction thresholds
  CLICK_RESPONSE_THRESHOLD: 16,      // Click response time (ms)
  SCROLL_PERFORMANCE_THRESHOLD: 60,  // Scroll FPS
};

// Test Group: Core Web Vitals Performance
test.describe('Core Web Vitals Performance', () => {
  test('should meet LCP threshold on dashboard page', async ({ page }) => {
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    const lcpMetric = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            resolve(lastEntry.startTime);
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Timeout after 10 seconds
        setTimeout(() => resolve(null), 10000);
      });
    });
    
    if (lcpMetric) {
      console.log(`LCP: ${lcpMetric}ms`);
      expect(lcpMetric).toBeLessThan(PERFORMANCE_THRESHOLDS.LCP_THRESHOLD);
    }
  });

  test('should meet FCP threshold on customer dashboard', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    const fcpMetric = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          if (entries.length > 0) {
            resolve(entries[0].startTime);
          }
        });
        observer.observe({ entryTypes: ['paint'] });
        
        setTimeout(() => resolve(null), 10000);
      });
    });
    
    if (fcpMetric) {
      console.log(`FCP: ${fcpMetric}ms`);
      expect(fcpMetric).toBeLessThan(PERFORMANCE_THRESHOLDS.FCP_THRESHOLD);
    }
  });

  test('should maintain low CLS on merchant dashboard', async ({ page }) => {
    await page.goto('/dashboard/merchant');
    
    // Wait for page to stabilize
    await page.waitForTimeout(3000);
    
    const clsMetric = await page.evaluate(() => {
      return new Promise((resolve) => {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          resolve(clsValue);
        });
        observer.observe({ entryTypes: ['layout-shift'] });
        
        setTimeout(() => resolve(clsValue), 5000);
      });
    });
    
    console.log(`CLS: ${clsMetric}`);
    expect(clsMetric).toBeLessThan(PERFORMANCE_THRESHOLDS.CLS_THRESHOLD);
  });
});

// Test Group: Page Load Performance
test.describe('Page Load Performance', () => {
  test('should load main dashboard within threshold', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="credit-dashboard-container"]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`Dashboard load time: ${loadTime}ms`);
    
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD);
  });

  test('should load shops page efficiently', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard/shops', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="credit-shops-list-container"]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`Shops page load time: ${loadTime}ms`);
    
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD);
  });

  test('should load API keys page efficiently', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard/api-keys', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`API keys page load time: ${loadTime}ms`);
    
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD);
  });

  test('should load subscriptions page efficiently', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/dashboard/subscriptions', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="subscriptions-container"]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    console.log(`Subscriptions page load time: ${loadTime}ms`);
    
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD);
  });
});

// Test Group: API Performance
test.describe('API Performance', () => {
  test('should have fast API response times for dashboard data', async ({ page }) => {
    // Monitor API calls during dashboard load
    const apiTimes = new Map();
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        const requestTime = response.timing().responseEnd;
        const endpoint = new URL(response.url()).pathname;
        apiTimes.set(endpoint, requestTime);
      }
    });
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Check API response times
    for (const [endpoint, time] of apiTimes) {
      console.log(`API ${endpoint}: ${time}ms`);
      expect(time).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE_THRESHOLD);
    }
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Mock large dataset
    await page.route('**/api/shops', async (route) => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `shop${i}`,
        name: `Shop ${i}`,
        slug: `shop-${i}`,
        credit_balance: Math.floor(Math.random() * 10000),
        created_at: new Date().toISOString()
      }));
      
      await route.fulfill({
        json: largeDataset,
        headers: { 'Content-Type': 'application/json' }
      });
    });
    
    const startTime = Date.now();
    await page.goto('/dashboard/shops', { waitUntil: 'networkidle' });
    
    // Ensure page renders efficiently with large dataset
    await expect(page.locator('[data-testid="credit-shops-list-container"]')).toBeVisible();
    
    const renderTime = Date.now() - startTime;
    console.log(`Large dataset render time: ${renderTime}ms`);
    
    expect(renderTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD * 1.5); // Allow 50% more time for large datasets
  });
});

// Test Group: Memory Performance
test.describe('Memory Performance', () => {
  test('should maintain reasonable memory usage', async ({ page, context }) => {
    // Enable memory tracking
    await context.tracing.start({ screenshots: true, snapshots: true });
    
    await page.goto('/dashboard');
    
    // Navigate through multiple pages to test memory usage
    await page.goto('/dashboard/shops');
    await page.goto('/dashboard/merchant');
    await page.goto('/dashboard/customer');
    await page.goto('/dashboard/api-keys');
    await page.goto('/dashboard/subscriptions');
    
    // Measure memory usage
    const memoryUsage = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return null;
    });
    
    await context.tracing.stop({ path: 'test-results/memory-trace.zip' });
    
    if (memoryUsage) {
      console.log(`Memory usage: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      expect(memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_USAGE_THRESHOLD);
    }
  });

  test('should not have memory leaks during navigation', async ({ page }) => {
    // Baseline memory measurement
    await page.goto('/dashboard');
    await page.waitForTimeout(1000);
    
    const baselineMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });
    
    // Navigate multiple times to detect memory leaks
    for (let i = 0; i < 10; i++) {
      await page.goto('/dashboard/shops');
      await page.goto('/dashboard/merchant');
      await page.goto('/dashboard/customer');
      await page.goto('/dashboard');
    }
    
    // Force garbage collection if available
    await page.evaluate(() => {
      if ('gc' in window) {
        (window as any).gc();
      }
    });
    
    await page.waitForTimeout(2000);
    
    const finalMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });
    
    const memoryIncrease = finalMemory - baselineMemory;
    const memoryIncreasePercentage = (memoryIncrease / baselineMemory) * 100;
    
    console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB (${memoryIncreasePercentage.toFixed(2)}%)`);
    
    // Memory should not increase by more than 50% after multiple navigations
    expect(memoryIncreasePercentage).toBeLessThan(50);
  });
});

// Test Group: Interaction Performance
test.describe('Interaction Performance', () => {
  test('should have fast button click responses', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Measure button click response time
    const startTime = Date.now();
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await expect(page.locator('[data-testid="api-keys-create-dialog"]')).toBeVisible();
    const responseTime = Date.now() - startTime;
    
    console.log(`Button click response: ${responseTime}ms`);
    expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CLICK_RESPONSE_THRESHOLD * 10); // Allow more time for dialog opening
  });

  test('should have smooth scrolling performance', async ({ page }) => {
    // Mock large shop list for scrolling test
    await page.route('**/api/shops', async (route) => {
      const largeShopList = Array.from({ length: 200 }, (_, i) => ({
        id: `shop${i}`,
        name: `Shop ${i}`,
        slug: `shop-${i}`,
        credit_balance: Math.floor(Math.random() * 10000)
      }));
      
      await route.fulfill({ json: largeShopList });
    });
    
    await page.goto('/dashboard/shops', { waitUntil: 'networkidle' });
    
    // Measure scroll performance
    const scrollPerformance = await page.evaluate(async () => {
      return new Promise((resolve) => {
        let frameCount = 0;
        let startTime = performance.now();
        
        const measureFPS = () => {
          frameCount++;
          const currentTime = performance.now();
          
          if (currentTime - startTime >= 1000) {
            resolve(frameCount);
          } else {
            requestAnimationFrame(measureFPS);
          }
        };
        
        // Start scrolling
        window.scrollBy(0, 10);
        requestAnimationFrame(measureFPS);
      });
    });
    
    console.log(`Scroll FPS: ${scrollPerformance}`);
    expect(scrollPerformance).toBeGreaterThan(PERFORMANCE_THRESHOLDS.SCROLL_PERFORMANCE_THRESHOLD);
  });

  test('should handle form interactions efficiently', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Measure form input response times
    const formTests = [
      { selector: '[data-testid="shop-create-name-input"]', value: 'Test Shop' },
      { selector: '[data-testid="shop-create-description-input"]', value: 'Test Description' },
      { selector: '[data-testid="shop-create-email-input"]', value: '<EMAIL>' }
    ];
    
    for (const test of formTests) {
      const startTime = Date.now();
      await page.locator(test.selector).fill(test.value);
      const responseTime = Date.now() - startTime;
      
      console.log(`Form input response (${test.selector}): ${responseTime}ms`);
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CLICK_RESPONSE_THRESHOLD * 5);
    }
  });
});

// Test Group: Bundle Size Analysis
test.describe('Bundle Size Performance', () => {
  test('should have reasonable JavaScript bundle sizes', async ({ page }) => {
    // Monitor resource loading
    const resourceSizes = new Map();
    
    page.on('response', response => {
      if (response.url().includes('.js') && !response.url().includes('node_modules')) {
        response.body().then(body => {
          const size = body.length;
          const filename = response.url().split('/').pop();
          resourceSizes.set(filename, size);
        }).catch(() => {
          // Ignore errors for body reading
        });
      }
    });
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Wait for resources to load
    await page.waitForTimeout(2000);
    
    let totalBundleSize = 0;
    for (const [filename, size] of resourceSizes) {
      console.log(`Bundle ${filename}: ${(size / 1024).toFixed(2)}KB`);
      totalBundleSize += size;
    }
    
    console.log(`Total bundle size: ${(totalBundleSize / 1024).toFixed(2)}KB`);
    expect(totalBundleSize).toBeLessThan(PERFORMANCE_THRESHOLDS.BUNDLE_SIZE_THRESHOLD);
  });

  test('should load critical resources first', async ({ page }) => {
    const resourceTimings = [];
    
    page.on('response', response => {
      if (response.url().includes('/dashboard') || response.url().includes('.js') || response.url().includes('.css')) {
        resourceTimings.push({
          url: response.url(),
          timing: response.timing(),
          status: response.status()
        });
      }
    });
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Analyze resource loading order
    resourceTimings.sort((a, b) => a.timing.responseEnd - b.timing.responseEnd);
    
    console.log('Resource loading order:');
    resourceTimings.slice(0, 5).forEach((resource, index) => {
      const filename = resource.url.split('/').pop();
      console.log(`${index + 1}. ${filename}: ${resource.timing.responseEnd}ms`);
    });
    
    // Critical resources should load within first few resources
    const criticalResourcesFirst = resourceTimings.slice(0, 3).some(r => 
      r.url.includes('/dashboard') || r.url.includes('main') || r.url.includes('app')
    );
    
    expect(criticalResourcesFirst).toBe(true);
  });
});

// Test Group: Network Performance
test.describe('Network Performance', () => {
  test('should handle slow network conditions', async ({ page, context }) => {
    // Simulate slow 3G network
    await context.route('**/*', route => {
      setTimeout(() => route.continue(), 100); // Add 100ms delay
    });
    
    const startTime = Date.now();
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="credit-dashboard-container"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    console.log(`Slow network load time: ${loadTime}ms`);
    
    // Should still load within reasonable time on slow networks
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD * 2);
  });

  test('should efficiently handle concurrent API calls', async ({ page }) => {
    const apiCallCount = new Map();
    
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        const endpoint = new URL(request.url()).pathname;
        apiCallCount.set(endpoint, (apiCallCount.get(endpoint) || 0) + 1);
      }
    });
    
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    
    // Check for duplicate API calls
    for (const [endpoint, count] of apiCallCount) {
      console.log(`API calls to ${endpoint}: ${count}`);
      expect(count).toBeLessThanOrEqual(2); // Allow max 2 calls (initial + retry)
    }
  });
});

// Test Group: Performance Regression Detection
test.describe('Performance Regression Detection', () => {
  test('should maintain performance baselines', async ({ page }) => {
    const performanceMetrics = {};
    
    // Dashboard performance
    let startTime = Date.now();
    await page.goto('/dashboard', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="credit-dashboard-container"]')).toBeVisible();
    performanceMetrics.dashboard = Date.now() - startTime;
    
    // Shops page performance
    startTime = Date.now();
    await page.goto('/dashboard/shops', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="credit-shops-list-container"]')).toBeVisible();
    performanceMetrics.shops = Date.now() - startTime;
    
    // API keys page performance
    startTime = Date.now();
    await page.goto('/dashboard/api-keys', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    performanceMetrics.apiKeys = Date.now() - startTime;
    
    // Customer dashboard performance
    startTime = Date.now();
    await page.goto('/dashboard/customer', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
    performanceMetrics.customer = Date.now() - startTime;
    
    // Log performance metrics for baseline comparison
    console.log('Performance Baseline Metrics:');
    console.log(JSON.stringify(performanceMetrics, null, 2));
    
    // All pages should load within threshold
    Object.entries(performanceMetrics).forEach(([page, time]) => {
      expect(time).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD);
    });
    
    // Store metrics for future comparison (in real implementation, save to file or database)
    await page.evaluate((metrics) => {
      localStorage.setItem('performanceBaseline', JSON.stringify(metrics));
    }, performanceMetrics);
  });
});

// Test Group: Mobile Performance
test.describe('Mobile Performance', () => {
  test('should perform well on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 });
    
    const startTime = Date.now();
    await page.goto('/dashboard/customer', { waitUntil: 'networkidle' });
    await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    console.log(`Mobile load time: ${loadTime}ms`);
    
    // Mobile should load within threshold (allowing 25% more time)
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.LOAD_THRESHOLD * 1.25);
  });

  test('should handle touch interactions efficiently', async ({ page }) => {
    await page.setViewportSize({ width: 390, height: 844 });
    await page.goto('/dashboard/customer');
    
    // Test touch interaction response time
    const startTime = Date.now();
    await page.locator('[data-testid="customer-dashboard-scan-button"]').tap();
    const responseTime = Date.now() - startTime;
    
    console.log(`Touch response time: ${responseTime}ms`);
    expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CLICK_RESPONSE_THRESHOLD * 2);
  });
});