/**
 * ADC Credit Authenticated Pages E2E Tests
 * 
 * Tests all authenticated pages in the ADC Credit application
 * Requires user to be logged in to access these pages
 */

import { test, expect } from '@playwright/test';

// Test user credentials for authenticated tests
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('ADC Credit Authenticated Pages', () => {
  
  // Helper function to login
  async function loginUser(page: any) {
    await page.goto('/auth/signin');
    
    // Try to fill login form
    const emailInput = page.locator('input[type="email"], input[name="email"], input[placeholder*="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"], input[placeholder*="password"]');
    const loginButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")');
    
    if (await emailInput.isVisible()) {
      await emailInput.fill(TEST_USER.email);
      await passwordInput.fill(TEST_USER.password);
      await loginButton.click();
      
      // Wait for redirect to dashboard
      await page.waitForURL(/\/(dashboard|app)/, { timeout: 15000 });
    } else {
      // If no form, might redirect to OAuth - skip login for this test
      console.log('⚠️ No login form found, might be OAuth redirect');
      // Navigate directly to dashboard to test authenticated pages
      await page.goto('/dashboard');
    }
  }
  
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
  });
  
  test.describe('Dashboard Pages', () => {
    test('should render main dashboard page', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Check if we're on dashboard or redirected to auth
      const currentUrl = page.url();
      
      if (currentUrl.includes('/auth/')) {
        console.log('⚠️ Redirected to auth - user not authenticated');
        test.skip('Authentication required for dashboard tests');
      }
      
      // Check main dashboard elements
      const dashboard = page.locator('[data-testid="dashboard"], main, .dashboard');
      await expect(dashboard.first()).toBeVisible();
      
      // Check for user menu or profile indicator
      const userMenu = page.locator('[data-testid="user-menu"], [data-testid="profile-menu"], button:has-text("Profile")');
      if (await userMenu.count() > 0) {
        await expect(userMenu.first()).toBeVisible();
      }
      
      // Check for navigation
      const nav = page.locator('nav, [data-testid="sidebar"], [data-testid="navigation"]');
      if (await nav.count() > 0) {
        await expect(nav.first()).toBeVisible();
      }
      
      console.log('✅ Dashboard page rendered successfully');
    });
    
    test('should display credit balance and stats', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for credit balance displays
      const creditElements = page.locator(
        '[data-testid*="credit"], [data-testid*="balance"], text*="credits", text*="balance"'
      );
      
      const creditCount = await creditElements.count();
      if (creditCount > 0) {
        await expect(creditElements.first()).toBeVisible();
        console.log('✅ Credit balance information found');
      }
      
      // Look for stats or metrics
      const statsElements = page.locator(
        '[data-testid*="stat"], [data-testid*="metric"], .stat, .metric'
      );
      
      const statsCount = await statsElements.count();
      if (statsCount > 0) {
        console.log(`✅ Found ${statsCount} stats elements`);
      }
    });
  });
  
  test.describe('Shop Management Pages', () => {
    test('should access shops listing page', async ({ page }) => {
      await page.goto('/shops');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Check for shops page content
      const shopsPage = page.locator('[data-testid="shops"], main, .shops');
      await expect(shopsPage.first()).toBeVisible();
      
      // Look for create shop button
      const createButton = page.locator(
        '[data-testid="create-shop"], button:has-text("Create"), button:has-text("Add Shop")'
      );
      
      if (await createButton.count() > 0) {
        await expect(createButton.first()).toBeVisible();
        console.log('✅ Create shop functionality available');
      }
      
      // Look for shops list or empty state
      const shopsList = page.locator('[data-testid="shops-list"], .shops-list');
      const emptyState = page.locator('[data-testid="empty-shops"], text*="No shops"');
      
      const hasShops = await shopsList.count() > 0;
      const isEmpty = await emptyState.count() > 0;
      
      expect(hasShops || isEmpty).toBeTruthy();
      console.log('✅ Shops page content verified');
    });
    
    test('should handle shop creation flow', async ({ page }) => {
      await page.goto('/shops');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Try to click create shop button
      const createButton = page.locator(
        '[data-testid="create-shop"], button:has-text("Create"), button:has-text("Add Shop")'
      );
      
      if (await createButton.count() > 0) {
        await createButton.first().click();
        
        // Should open create shop form or navigate to create page
        const form = page.locator('form, [data-testid="shop-form"], [data-testid="create-shop-form"]');
        const modal = page.locator('[role="dialog"], .modal, [data-testid="modal"]');
        
        const hasForm = await form.count() > 0;
        const hasModal = await modal.count() > 0;
        const isCreatePage = page.url().includes('/create') || page.url().includes('/new');
        
        expect(hasForm || hasModal || isCreatePage).toBeTruthy();
        
        if (hasForm || hasModal) {
          // Look for form fields
          const nameField = page.locator('input[name="name"], input[placeholder*="name"], [data-testid*="name"]');
          if (await nameField.count() > 0) {
            await expect(nameField.first()).toBeVisible();
            console.log('✅ Shop creation form is functional');
          }
        }
      } else {
        console.log('⚠️ No create shop button found');
      }
    });
  });
  
  test.describe('Credit Management Pages', () => {
    test('should access credit management page', async ({ page }) => {
      await page.goto('/credits');
      
      if (page.url().includes('/auth/')) {
        // Try alternative routes
        await page.goto('/dashboard/credits');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Check for credit management content
      const creditsPage = page.locator('[data-testid="credits"], main, .credits');
      await expect(creditsPage.first()).toBeVisible();
      
      // Look for credit-related actions
      const creditActions = page.locator(
        'button:has-text("Add Credits"), button:has-text("Purchase"), [data-testid*="credit-action"]'
      );
      
      if (await creditActions.count() > 0) {
        console.log('✅ Credit management actions available');
      }
    });
    
    test('should display credit history', async ({ page }) => {
      await page.goto('/credits/history');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/dashboard/history');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for transaction history
      const historyTable = page.locator('table, [data-testid="history"], [data-testid="transactions"]');
      const emptyState = page.locator('text*="No transactions", text*="No history"');
      
      const hasHistory = await historyTable.count() > 0;
      const isEmpty = await emptyState.count() > 0;
      
      expect(hasHistory || isEmpty).toBeTruthy();
      console.log('✅ Credit history page accessible');
    });
  });
  
  test.describe('Settings Pages', () => {
    test('should access account settings', async ({ page }) => {
      await page.goto('/settings');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/account');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/profile');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for settings content
      const settingsPage = page.locator('[data-testid="settings"], main, .settings');
      await expect(settingsPage.first()).toBeVisible();
      
      // Look for common settings sections
      const settingsSections = [
        'Profile',
        'Account',
        'Preferences',
        'Security',
        'Notifications'
      ];
      
      for (const section of settingsSections) {
        const sectionElement = page.locator(`text="${section}", [data-testid*="${section.toLowerCase()}"]`);
        if (await sectionElement.count() > 0) {
          console.log(`✅ Found settings section: ${section}`);
        }
      }
    });
    
    test('should access API keys management', async ({ page }) => {
      await page.goto('/api-keys');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/settings/api-keys');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/dashboard/api');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for API keys management
      const apiKeysPage = page.locator('[data-testid*="api"], main, .api-keys');
      await expect(apiKeysPage.first()).toBeVisible();
      
      // Look for create API key functionality
      const createApiKeyButton = page.locator(
        'button:has-text("Create"), button:has-text("Generate"), [data-testid*="create-api"]'
      );
      
      if (await createApiKeyButton.count() > 0) {
        console.log('✅ API key management available');
      }
    });
  });
  
  test.describe('Analytics Pages', () => {
    test('should access analytics dashboard', async ({ page }) => {
      await page.goto('/analytics');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/dashboard/analytics');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for analytics content
      const analyticsPage = page.locator('[data-testid="analytics"], main, .analytics');
      await expect(analyticsPage.first()).toBeVisible();
      
      // Look for charts or metrics
      const charts = page.locator('canvas, svg, [data-testid*="chart"], .chart');
      if (await charts.count() > 0) {
        console.log('✅ Analytics charts found');
      }
      
      // Look for date range pickers or filters
      const filters = page.locator('select, [data-testid*="filter"], [data-testid*="date"]');
      if (await filters.count() > 0) {
        console.log('✅ Analytics filters available');
      }
    });
  });
  
  test.describe('Subscription Pages', () => {
    test('should access subscription management', async ({ page }) => {
      await page.goto('/subscription');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/billing');
      }
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for subscription content
      const subscriptionPage = page.locator('[data-testid="subscription"], main, .subscription');
      await expect(subscriptionPage.first()).toBeVisible();
      
      // Look for current plan information
      const planInfo = page.locator('[data-testid*="plan"], text*="Plan", text*="Subscription"');
      if (await planInfo.count() > 0) {
        console.log('✅ Subscription plan information found');
      }
      
      // Look for upgrade/downgrade options
      const planActions = page.locator(
        'button:has-text("Upgrade"), button:has-text("Change Plan"), [data-testid*="upgrade"]'
      );
      
      if (await planActions.count() > 0) {
        console.log('✅ Plan management options available');
      }
    });
  });
  
  test.describe('Navigation and User Experience', () => {
    test('should have working navigation between pages', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Test navigation to different sections
      const navigationTests = [
        { name: 'shops', url: '/shops' },
        { name: 'credits', url: '/credits' },
        { name: 'analytics', url: '/analytics' },
        { name: 'settings', url: '/settings' }
      ];
      
      for (const navTest of navigationTests) {
        try {
          await page.goto(navTest.url);
          
          // Wait a moment for page to load
          await page.waitForTimeout(1000);
          
          // Check if we're still authenticated (not redirected to auth)
          if (!page.url().includes('/auth/')) {
            console.log(`✅ Successfully navigated to ${navTest.name} page`);
          }
        } catch (error) {
          console.log(`⚠️ Could not navigate to ${navTest.name} page`);
        }
      }
    });
    
    test('should display user information consistently', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for user information display
      const userInfo = page.locator(
        '[data-testid*="user"], [data-testid*="profile"], .user-info, .profile'
      );
      
      if (await userInfo.count() > 0) {
        await expect(userInfo.first()).toBeVisible();
        console.log('✅ User information displayed');
      }
      
      // Test that user info is consistent across pages
      const pages = ['/dashboard', '/shops', '/settings'];
      let userDisplays = [];
      
      for (const pagePath of pages) {
        try {
          await page.goto(pagePath);
          
          if (!page.url().includes('/auth/')) {
            const userElement = page.locator('[data-testid*="user"], [data-testid*="profile"]');
            if (await userElement.count() > 0) {
              const userText = await userElement.first().textContent();
              userDisplays.push(userText);
            }
          }
        } catch (error) {
          // Page might not exist
        }
      }
      
      // User information should be consistent
      if (userDisplays.length > 1) {
        const firstDisplay = userDisplays[0];
        const isConsistent = userDisplays.every(display => display === firstDisplay);
        expect(isConsistent).toBeTruthy();
        console.log('✅ User information consistent across pages');
      }
    });
    
    test('should handle logout functionality', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
      }
      
      // Look for logout button
      const logoutButton = page.locator(
        'button:has-text("Logout"), button:has-text("Sign Out"), [data-testid*="logout"]'
      );
      
      if (await logoutButton.count() > 0) {
        await logoutButton.first().click();
        
        // Should redirect to login or home page
        await page.waitForURL(/\/(auth|signin|login|$)/, { timeout: 10000 });
        
        // Should no longer have access to protected pages
        await page.goto('/dashboard');
        
        // Should be redirected to auth
        await expect(page).toHaveURL(/\/(auth|signin|login)/);
        
        console.log('✅ Logout functionality working');
      } else {
        console.log('⚠️ No logout button found');
      }
    });
  });
});