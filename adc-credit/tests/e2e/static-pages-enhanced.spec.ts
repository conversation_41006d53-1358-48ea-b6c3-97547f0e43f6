/**
 * Enhanced Static Pages E2E Tests
 * ADC Credit Service - Legal and Marketing Pages Testing
 * 
 * Tests static pages using the comprehensive test ID system
 * implemented in Phase 5. This test suite covers:
 * 
 * Test Categories:
 * - Terms of Service Page Navigation & Content
 * - Privacy Policy Page Structure & Sections
 * - Pricing Page Plans & Interactive Elements
 * - Cross-Page Navigation & SEO Elements
 * - Responsive Design & Accessibility
 * 
 * Features the new test ID constants:
 * - TERMS_TEST_IDS
 * - PRIVACY_TEST_IDS
 * - PRICING_TEST_IDS
 */

import { test, expect } from '@playwright/test';

// Test Group: Terms of Service Page
test.describe('Enhanced Terms of Service Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/terms');
  });

  test('should render terms page with comprehensive test IDs', async ({ page }) => {
    // Verify page structure
    await expect(page.locator('[data-testid="terms-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-main-content"]')).toBeVisible();
    
    // Test header section
    await expect(page.locator('[data-testid="terms-header-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-title"]')).toHaveText('Terms of Service');
    await expect(page.locator('[data-testid="terms-last-updated"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-last-updated"]')).toContainText('Last updated:');
    
    // Test content sections
    await expect(page.locator('[data-testid="terms-content-sections"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-agreement-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-agreement-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="terms-agreement-title"]')).toHaveText('Agreement to Terms');
    await expect(page.locator('[data-testid="terms-agreement-content"]')).toBeVisible();
  });

  test('should have proper page metadata and SEO', async ({ page }) => {
    // Test page title
    await expect(page).toHaveTitle(/Terms of Service/);
    
    // Test heading hierarchy
    const mainHeading = page.locator('[data-testid="terms-title"]');
    await expect(mainHeading).toBeVisible();
    
    // Verify content structure
    const contentSections = page.locator('[data-testid="terms-content-sections"]');
    await expect(contentSections).toBeVisible();
    
    // Check for proper content organization
    const agreementSection = page.locator('[data-testid="terms-agreement-section"]');
    await expect(agreementSection).toBeVisible();
  });

  test('should be accessible with proper ARIA attributes', async ({ page }) => {
    // Test heading structure
    await expect(page.locator('h1[data-testid="terms-title"]')).toBeVisible();
    
    // Verify content is readable
    const content = page.locator('[data-testid="terms-agreement-content"]');
    await expect(content).toBeVisible();
    await expect(content).toContainText('These Terms of Service');
  });

  test('should display current date in last updated', async ({ page }) => {
    const lastUpdated = page.locator('[data-testid="terms-last-updated"]');
    await expect(lastUpdated).toBeVisible();
    
    // Should contain current year
    const currentYear = new Date().getFullYear().toString();
    await expect(lastUpdated).toContainText(currentYear);
  });
});

// Test Group: Privacy Policy Page
test.describe('Enhanced Privacy Policy Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/privacy');
  });

  test('should render privacy page with comprehensive test IDs', async ({ page }) => {
    // Verify page structure
    await expect(page.locator('[data-testid="privacy-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-main-content"]')).toBeVisible();
    
    // Test header section
    await expect(page.locator('[data-testid="privacy-header-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-title"]')).toHaveText('Privacy Policy');
    await expect(page.locator('[data-testid="privacy-last-updated"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-last-updated"]')).toContainText('Last updated:');
    
    // Test content sections
    await expect(page.locator('[data-testid="privacy-content-sections"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-introduction-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-introduction-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-introduction-title"]')).toHaveText('Introduction');
    await expect(page.locator('[data-testid="privacy-introduction-content"]')).toBeVisible();
  });

  test('should have comprehensive privacy content', async ({ page }) => {
    // Test content is substantial
    const introContent = page.locator('[data-testid="privacy-introduction-content"]');
    await expect(introContent).toBeVisible();
    await expect(introContent).toContainText('ADC Credit');
    await expect(introContent).toContainText('privacy');
    await expect(introContent).toContainText('information');
  });

  test('should have proper page metadata', async ({ page }) => {
    // Test page title
    await expect(page).toHaveTitle(/Privacy Policy/);
    
    // Test heading hierarchy
    const mainHeading = page.locator('[data-testid="privacy-title"]');
    await expect(mainHeading).toBeVisible();
  });

  test('should display current date in last updated', async ({ page }) => {
    const lastUpdated = page.locator('[data-testid="privacy-last-updated"]');
    await expect(lastUpdated).toBeVisible();
    
    // Should contain current year
    const currentYear = new Date().getFullYear().toString();
    await expect(lastUpdated).toContainText(currentYear);
  });
});

// Test Group: Pricing Page
test.describe('Enhanced Pricing Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/pricing');
  });

  test('should render pricing page with comprehensive test IDs', async ({ page }) => {
    // Verify page structure
    await expect(page.locator('[data-testid="pricing-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-main-content"]')).toBeVisible();
    
    // Test navbar
    await expect(page.locator('[data-testid="pricing-navbar"]')).toBeVisible();
    
    // Test pricing section
    await expect(page.locator('[data-testid="pricing-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-header-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-title"]')).toHaveText('Simple, Transparent Pricing');
    await expect(page.locator('[data-testid="pricing-description"]')).toContainText('Choose the plan that fits your needs');
  });

  test('should display all pricing plans with test IDs', async ({ page }) => {
    // Test plans grid
    await expect(page.locator('[data-testid="pricing-plans-grid"]')).toBeVisible();
    
    // Test Free Plan
    await expect(page.locator('[data-testid="pricing-free-plan-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-free-plan-title"]')).toHaveText('Free');
    await expect(page.locator('[data-testid="pricing-free-plan-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-free-plan-button"]')).toHaveText('Get Started');
    
    // Test Pro Plan
    await expect(page.locator('[data-testid="pricing-pro-plan-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-pro-plan-badge"]')).toBeVisible();
    await expect(page.locator('[data-testid="pricing-pro-plan-badge"]')).toHaveText('Popular');
    
    // Test Enterprise Plan (if implemented)
    // await expect(page.locator('[data-testid="pricing-enterprise-plan-card"]')).toBeVisible();
  });

  test('should handle plan selection and navigation', async ({ page }) => {
    // Test Free plan navigation
    await page.locator('[data-testid="pricing-free-plan-button"]').click();
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Go back to pricing
    await page.goto('/pricing');
    
    // Test Pro plan navigation
    const proButton = page.locator('[data-testid="pricing-pro-plan-button"]');
    if (await proButton.isVisible()) {
      await proButton.click();
      await expect(page).toHaveURL(/.*\/dashboard/);
    }
  });

  test('should display pricing information correctly', async ({ page }) => {
    // Test Free plan pricing
    const freePlanCard = page.locator('[data-testid="pricing-free-plan-card"]');
    await expect(freePlanCard).toBeVisible();
    await expect(freePlanCard).toContainText('$0');
    await expect(freePlanCard).toContainText('month');
    
    // Test Pro plan pricing
    const proPlanCard = page.locator('[data-testid="pricing-pro-plan-card"]');
    await expect(proPlanCard).toBeVisible();
    await expect(proPlanCard).toContainText('$29.99');
    await expect(proPlanCard).toContainText('month');
  });

  test('should have proper page metadata and SEO', async ({ page }) => {
    // Test page title
    await expect(page).toHaveTitle(/Pricing/);
    
    // Test main heading
    const mainHeading = page.locator('[data-testid="pricing-title"]');
    await expect(mainHeading).toBeVisible();
    
    // Test description for SEO
    const description = page.locator('[data-testid="pricing-description"]');
    await expect(description).toBeVisible();
    await expect(description).toContainText('API');
    await expect(description).toContainText('dashboard');
  });
});

// Test Group: Cross-Page Static Navigation
test.describe('Enhanced Static Pages Navigation', () => {
  test('should navigate between static pages correctly', async ({ page }) => {
    // Start from terms page
    await page.goto('/terms');
    await expect(page.locator('[data-testid="terms-container"]')).toBeVisible();
    
    // Navigate to privacy page
    await page.goto('/privacy');
    await expect(page.locator('[data-testid="privacy-container"]')).toBeVisible();
    
    // Navigate to pricing page
    await page.goto('/pricing');
    await expect(page.locator('[data-testid="pricing-container"]')).toBeVisible();
    
    // Return to main application
    await page.goto('/dashboard');
    // Should redirect or show dashboard
  });

  test('should maintain consistent page structure across static pages', async ({ page }) => {
    const staticPages = [
      { url: '/terms', container: 'terms-container', title: 'terms-title' },
      { url: '/privacy', container: 'privacy-container', title: 'privacy-title' },
      { url: '/pricing', container: 'pricing-container', title: 'pricing-title' }
    ];

    for (const pageInfo of staticPages) {
      await page.goto(pageInfo.url);
      
      // Verify consistent structure
      await expect(page.locator(`[data-testid="${pageInfo.container}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${pageInfo.title}"]`)).toBeVisible();
      
      // Check for proper heading structure
      const heading = page.locator(`[data-testid="${pageInfo.title}"]`);
      await expect(heading).toBeVisible();
    }
  });

  test('should have proper breadcrumb or navigation context', async ({ page }) => {
    const pages = ['/terms', '/privacy', '/pricing'];
    
    for (const url of pages) {
      await page.goto(url);
      
      // Each page should have clear navigation context
      // This could be breadcrumbs, navbar, or other navigation elements
      // The specific implementation depends on the design
      
      // At minimum, should be able to navigate back to main site
      await expect(page.locator('body')).toBeVisible();
    }
  });
});

// Test Group: Static Pages Responsive Design
test.describe('Enhanced Static Pages Responsive Design', () => {
  ['mobile', 'tablet', 'desktop'].forEach(device => {
    test(`should render static pages optimally on ${device}`, async ({ page }) => {
      // Set viewport for device
      if (device === 'mobile') {
        await page.setViewportSize({ width: 375, height: 667 });
      } else if (device === 'tablet') {
        await page.setViewportSize({ width: 768, height: 1024 });
      } else {
        await page.setViewportSize({ width: 1440, height: 900 });
      }

      // Test Terms page responsiveness
      await page.goto('/terms');
      await expect(page.locator('[data-testid="terms-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="terms-title"]')).toBeVisible();
      
      // Test Privacy page responsiveness
      await page.goto('/privacy');
      await expect(page.locator('[data-testid="privacy-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="privacy-title"]')).toBeVisible();
      
      // Test Pricing page responsiveness
      await page.goto('/pricing');
      await expect(page.locator('[data-testid="pricing-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="pricing-plans-grid"]')).toBeVisible();
      
      // Plans should be visible and clickable on all devices
      await expect(page.locator('[data-testid="pricing-free-plan-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="pricing-pro-plan-card"]')).toBeVisible();
    });
  });
});

// Test Group: Static Pages Performance and Accessibility
test.describe('Enhanced Static Pages Performance & Accessibility', () => {
  test('should load static pages quickly', async ({ page }) => {
    const pages = ['/terms', '/privacy', '/pricing'];
    
    for (const url of pages) {
      const startTime = Date.now();
      await page.goto(url);
      
      // Wait for main content to be visible
      const container = page.locator('[data-testid*="container"]').first();
      await expect(container).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    }
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/terms');
    
    // Test heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    // Should be able to navigate through interactive elements
    
    // Test for proper color contrast (would need additional tools for full testing)
    // Test for proper semantic markup
    const main = page.locator('main');
    await expect(main).toBeVisible();
  });

  test('should have proper meta tags for SEO', async ({ page }) => {
    const pages = [
      { url: '/terms', expectedTitle: /Terms of Service/ },
      { url: '/privacy', expectedTitle: /Privacy Policy/ },
      { url: '/pricing', expectedTitle: /Pricing/ }
    ];

    for (const pageInfo of pages) {
      await page.goto(pageInfo.url);
      await expect(page).toHaveTitle(pageInfo.expectedTitle);
      
      // Should have proper meta description (if implemented)
      // Should have proper Open Graph tags (if implemented)
    }
  });
});