/**
 * Multi-Language E2E Tests - RTL (Right-to-Left) Language Support
 * ADC Credit Service - RTL Languages Testing
 * 
 * This test suite verifies RTL language support for Arabic and Hebrew:
 * 
 * Test Categories:
 * - RTL Layout and Text Direction
 * - RTL CSS Classes and Styling
 * - RTL Navigation and UI Components
 * - RTL Form Layouts and Input Fields
 * - RTL Language Switcher Positioning
 * 
 * Features tested:
 * - Arabic (العربية) RTL support
 * - Hebrew (עברית) RTL support  
 * - CSS direction property changes
 * - Layout mirroring for RTL languages
 * - Component positioning in RTL mode
 */

import { test, expect } from '@playwright/test';

// RTL Languages to test
const RTL_LANGUAGES = [
  { text: 'العربية', code: 'ar', name: 'Arabic' },
  { text: 'עברית', code: 'he', name: 'Hebrew' }
];

// Helper function to change to RTL language
async function changeToRTLLanguage(page: any, languageText: string, localeCode: string) {
  const languageSwitcherButton = page.locator('[data-testid*="language-switcher-trigger"]').first();
  await languageSwitcherButton.click();
  
  const languageDropdown = page.locator('[data-testid*="language-switcher-content"]').first();
  await expect(languageDropdown).toBeVisible();
  
  const languageOption = languageDropdown.locator(`text=${languageText}`);
  await languageOption.click();
  
  // Wait for language change and potential layout updates
  await page.waitForTimeout(1000);
  
  // Verify language changed
  const storedLocale = await page.evaluate(() => localStorage.getItem('locale'));
  expect(storedLocale).toBe(localeCode);
}

// Helper function to check text direction
async function checkTextDirection(page: any, selector: string, expectedDirection: string) {
  const element = page.locator(selector).first();
  if (await element.isVisible()) {
    const direction = await element.evaluate((el: Element) => getComputedStyle(el).direction);
    expect(direction).toBe(expectedDirection);
  }
}

// Test Group: Basic RTL Layout Testing
test.describe('RTL Layout and Text Direction E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  RTL_LANGUAGES.forEach(({ text, code, name }) => {
    test(`should set text direction to RTL for ${name}`, async ({ page }) => {
      await page.goto('/');
      
      // Change to RTL language
      await changeToRTLLanguage(page, text, code);
      
      // Check if body or html has dir="rtl" or CSS direction: rtl
      const bodyDirection = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;
        
        // Check dir attribute
        const bodyDir = body.getAttribute('dir');
        const htmlDir = html.getAttribute('dir');
        
        // Check computed style
        const bodyStyle = getComputedStyle(body);
        const htmlStyle = getComputedStyle(html);
        
        return {
          bodyDir,
          htmlDir,
          bodyDirection: bodyStyle.direction,
          htmlDirection: htmlStyle.direction
        };
      });
      
      // At least one should indicate RTL
      const isRTL = bodyDirection.bodyDir === 'rtl' || 
                    bodyDirection.htmlDir === 'rtl' || 
                    bodyDirection.bodyDirection === 'rtl' || 
                    bodyDirection.htmlDirection === 'rtl';
      
      expect(isRTL).toBeTruthy();
    });

    test(`should apply RTL classes for ${name}`, async ({ page }) => {
      await page.goto('/');
      
      // Change to RTL language
      await changeToRTLLanguage(page, text, code);
      
      // Check if RTL-related classes are applied
      const rtlClasses = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;
        
        const bodyClasses = Array.from(body.classList);
        const htmlClasses = Array.from(html.classList);
        
        return { bodyClasses, htmlClasses };
      });
      
      // Look for RTL-related classes
      const hasRTLClass = rtlClasses.bodyClasses.some(cls => cls.includes('rtl')) ||
                          rtlClasses.htmlClasses.some(cls => cls.includes('rtl'));
      
      // If no explicit RTL classes, at least the page should be functional
      await expect(page.locator('body')).toBeVisible();
    });

    test(`should mirror layout elements for ${name}`, async ({ page }) => {
      await page.goto('/');
      
      // Get initial layout positions
      const initialLayout = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('[data-testid]')).slice(0, 5);
        return elements.map(el => {
          const rect = el.getBoundingClientRect();
          return {
            testId: el.getAttribute('data-testid'),
            left: rect.left,
            right: rect.right,
            width: rect.width
          };
        });
      });
      
      // Change to RTL language
      await changeToRTLLanguage(page, text, code);
      
      // Get layout after RTL change
      const rtlLayout = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('[data-testid]')).slice(0, 5);
        return elements.map(el => {
          const rect = el.getBoundingClientRect();
          return {
            testId: el.getAttribute('data-testid'),
            left: rect.left,
            right: rect.right,
            width: rect.width
          };
        });
      });
      
      // Verify elements are still visible and positioned
      expect(rtlLayout.length).toBeGreaterThan(0);
      
      // Each element should still have reasonable positioning
      rtlLayout.forEach(layout => {
        expect(layout.width).toBeGreaterThan(0);
        expect(layout.left).toBeGreaterThanOrEqual(0);
      });
    });
  });
});

// Test Group: RTL Navigation and Components
test.describe('RTL Navigation and Components E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should position navigation elements correctly in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Get navigation layout in LTR
    const ltrNavLayout = await page.evaluate(() => {
      const nav = document.querySelector('nav, [data-testid*="nav"]');
      if (!nav) return null;
      
      const rect = nav.getBoundingClientRect();
      const navItems = Array.from(nav.querySelectorAll('a, button')).slice(0, 3);
      
      return {
        nav: { left: rect.left, right: rect.right, width: rect.width },
        items: navItems.map(item => {
          const itemRect = item.getBoundingClientRect();
          return { left: itemRect.left, right: itemRect.right, width: itemRect.width };
        })
      };
    });
    
    // Change to Arabic
    await changeToRTLLanguage(page, 'العربية', 'ar');
    
    // Get navigation layout in RTL
    const rtlNavLayout = await page.evaluate(() => {
      const nav = document.querySelector('nav, [data-testid*="nav"]');
      if (!nav) return null;
      
      const rect = nav.getBoundingClientRect();
      const navItems = Array.from(nav.querySelectorAll('a, button')).slice(0, 3);
      
      return {
        nav: { left: rect.left, right: rect.right, width: rect.width },
        items: navItems.map(item => {
          const itemRect = item.getBoundingClientRect();
          return { left: itemRect.left, right: itemRect.right, width: itemRect.width };
        })
      };
    });
    
    // Navigation should still be functional
    if (rtlNavLayout) {
      expect(rtlNavLayout.nav.width).toBeGreaterThan(0);
      
      // Items should still be positioned within navigation bounds
      rtlNavLayout.items.forEach(item => {
        expect(item.width).toBeGreaterThan(0);
      });
    }
    
    // Verify navigation is still clickable
    const navItems = page.locator('nav a, nav button, [data-testid*="nav"] a, [data-testid*="nav"] button');
    if (await navItems.count() > 0) {
      await expect(navItems.first()).toBeVisible();
    }
  });

  test('should position language switcher correctly in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Get language switcher position in LTR
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    const ltrPosition = await languageSwitcher.boundingBox();
    
    // Change to Hebrew
    await changeToRTLLanguage(page, 'עברית', 'he');
    
    // Get language switcher position in RTL
    const rtlPosition = await languageSwitcher.boundingBox();
    
    // Language switcher should still be visible and functional
    await expect(languageSwitcher).toBeVisible();
    
    if (ltrPosition && rtlPosition) {
      // Position should be reasonable (not negative, within viewport)
      expect(rtlPosition.x).toBeGreaterThanOrEqual(0);
      expect(rtlPosition.y).toBeGreaterThanOrEqual(0);
      expect(rtlPosition.width).toBeGreaterThan(0);
      expect(rtlPosition.height).toBeGreaterThan(0);
    }
    
    // Should still be clickable
    await languageSwitcher.click();
    const dropdown = page.locator('[data-testid*="language-switcher-content"]').first();
    await expect(dropdown).toBeVisible();
  });

  test('should handle dropdown menus correctly in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Change to Arabic
    await changeToRTLLanguage(page, 'العربية', 'ar');
    
    // Test language switcher dropdown
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    await languageSwitcher.click();
    
    const dropdown = page.locator('[data-testid*="language-switcher-content"]').first();
    await expect(dropdown).toBeVisible();
    
    // Check dropdown positioning
    const dropdownBox = await dropdown.boundingBox();
    if (dropdownBox) {
      expect(dropdownBox.width).toBeGreaterThan(0);
      expect(dropdownBox.height).toBeGreaterThan(0);
      expect(dropdownBox.x).toBeGreaterThanOrEqual(0);
    }
    
    // Dropdown items should be clickable
    const dropdownItems = dropdown.locator('[role="menuitem"], a, button').filter({ hasText: /.+/ });
    if (await dropdownItems.count() > 0) {
      await expect(dropdownItems.first()).toBeVisible();
    }
    
    // Close dropdown by clicking outside or pressing Escape
    await page.keyboard.press('Escape');
    await expect(dropdown).not.toBeVisible();
  });
});

// Test Group: RTL Form and Input Layouts
test.describe('RTL Form and Input Layout E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should align form elements correctly in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Look for forms on the page
    const forms = page.locator('form');
    
    if (await forms.count() > 0) {
      // Change to Arabic
      await changeToRTLLanguage(page, 'العربية', 'ar');
      
      const firstForm = forms.first();
      await expect(firstForm).toBeVisible();
      
      // Check form inputs
      const inputs = firstForm.locator('input, textarea, select');
      if (await inputs.count() > 0) {
        for (let i = 0; i < Math.min(3, await inputs.count()); i++) {
          const input = inputs.nth(i);
          await expect(input).toBeVisible();
          
          // Check text alignment for text inputs
          const inputType = await input.getAttribute('type');
          if (!inputType || inputType === 'text' || inputType === 'email') {
            const textAlign = await input.evaluate(el => getComputedStyle(el).textAlign);
            // In RTL, text should align right or be explicitly set
            expect(['right', 'start', 'left'].includes(textAlign)).toBeTruthy();
          }
        }
      }
      
      // Check form labels
      const labels = firstForm.locator('label');
      if (await labels.count() > 0) {
        const firstLabel = labels.first();
        await expect(firstLabel).toBeVisible();
        
        // Labels should be positioned correctly relative to inputs
        const labelBox = await firstLabel.boundingBox();
        if (labelBox) {
          expect(labelBox.width).toBeGreaterThan(0);
        }
      }
    }
  });

  test('should handle input field text direction in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Find input fields
    const inputs = page.locator('input[type="text"], input[type="email"], textarea');
    
    if (await inputs.count() > 0) {
      // Change to Hebrew
      await changeToRTLLanguage(page, 'עברית', 'he');
      
      const firstInput = inputs.first();
      await expect(firstInput).toBeVisible();
      
      // Test typing in RTL language
      await firstInput.focus();
      await firstInput.fill('טקסט בעברית'); // Hebrew text
      
      // Verify text appears correctly
      const inputValue = await firstInput.inputValue();
      expect(inputValue).toBe('טקסט בעברית');
      
      // Text direction should be appropriate
      const direction = await firstInput.evaluate(el => getComputedStyle(el).direction);
      // Should be rtl or inherit properly
      expect(['rtl', 'ltr'].includes(direction)).toBeTruthy();
    }
  });

  test('should position form buttons correctly in RTL mode', async ({ page }) => {
    await page.goto('/');
    
    // Look for forms with buttons
    const formButtons = page.locator('form button, form input[type="submit"]');
    
    if (await formButtons.count() > 0) {
      // Get button positions in LTR
      const ltrPositions = [];
      for (let i = 0; i < Math.min(3, await formButtons.count()); i++) {
        const button = formButtons.nth(i);
        const box = await button.boundingBox();
        if (box) ltrPositions.push(box);
      }
      
      // Change to Arabic
      await changeToRTLLanguage(page, 'العربية', 'ar');
      
      // Check button positions in RTL
      for (let i = 0; i < Math.min(3, await formButtons.count()); i++) {
        const button = formButtons.nth(i);
        await expect(button).toBeVisible();
        
        const rtlBox = await button.boundingBox();
        if (rtlBox) {
          expect(rtlBox.width).toBeGreaterThan(0);
          expect(rtlBox.height).toBeGreaterThan(0);
        }
      }
    }
  });
});

// Test Group: RTL Text and Content Display
test.describe('RTL Text and Content Display E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should display Arabic text correctly', async ({ page }) => {
    await page.goto('/');
    
    // Change to Arabic
    await changeToRTLLanguage(page, 'العربية', 'ar');
    
    // Add some Arabic text to test display
    await page.evaluate(() => {
      const testDiv = document.createElement('div');
      testDiv.id = 'rtl-test-arabic';
      testDiv.textContent = 'مرحبا بكم في نظام إدارة المتاجر';
      testDiv.style.padding = '10px';
      testDiv.style.border = '1px solid #ccc';
      testDiv.style.direction = 'rtl';
      document.body.appendChild(testDiv);
    });
    
    // Check if Arabic text displays correctly
    const arabicText = page.locator('#rtl-test-arabic');
    await expect(arabicText).toBeVisible();
    
    const textContent = await arabicText.textContent();
    expect(textContent).toBe('مرحبا بكم في نظام إدارة المتاجر');
    
    // Check text direction
    const direction = await arabicText.evaluate(el => getComputedStyle(el).direction);
    expect(direction).toBe('rtl');
  });

  test('should display Hebrew text correctly', async ({ page }) => {
    await page.goto('/');
    
    // Change to Hebrew
    await changeToRTLLanguage(page, 'עברית', 'he');
    
    // Add Hebrew text to test display
    await page.evaluate(() => {
      const testDiv = document.createElement('div');
      testDiv.id = 'rtl-test-hebrew';
      testDiv.textContent = 'ברוכים הבאים למערכת ניהול חנויות';
      testDiv.style.padding = '10px';
      testDiv.style.border = '1px solid #ccc';
      testDiv.style.direction = 'rtl';
      document.body.appendChild(testDiv);
    });
    
    // Check if Hebrew text displays correctly
    const hebrewText = page.locator('#rtl-test-hebrew');
    await expect(hebrewText).toBeVisible();
    
    const textContent = await hebrewText.textContent();
    expect(textContent).toBe('ברוכים הבאים למערכת ניהול חנויות');
    
    // Check text direction
    const direction = await hebrewText.evaluate(el => getComputedStyle(el).direction);
    expect(direction).toBe('rtl');
  });

  test('should handle mixed LTR/RTL content correctly', async ({ page }) => {
    await page.goto('/');
    
    // Change to Arabic
    await changeToRTLLanguage(page, 'العربية', 'ar');
    
    // Add mixed content
    await page.evaluate(() => {
      const testDiv = document.createElement('div');
      testDiv.id = 'rtl-test-mixed';
      testDiv.innerHTML = 'Text in English and نص باللغة العربية together';
      testDiv.style.padding = '10px';
      testDiv.style.border = '1px solid #ccc';
      testDiv.style.direction = 'rtl';
      document.body.appendChild(testDiv);
    });
    
    const mixedText = page.locator('#rtl-test-mixed');
    await expect(mixedText).toBeVisible();
    
    // Content should be readable
    const textContent = await mixedText.textContent();
    expect(textContent).toContain('English');
    expect(textContent).toContain('العربية');
  });
});

// Test Group: RTL Responsive Design
test.describe('RTL Responsive Design E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  ['mobile', 'tablet', 'desktop'].forEach(device => {
    test(`should maintain RTL layout integrity on ${device}`, async ({ page }) => {
      const viewports = {
        'mobile': { width: 375, height: 667 },
        'tablet': { width: 768, height: 1024 },
        'desktop': { width: 1440, height: 900 }
      };
      
      await page.setViewportSize(viewports[device]);
      await page.goto('/');
      
      // Change to Arabic
      await changeToRTLLanguage(page, 'العربية', 'ar');
      
      // Check that layout is not broken
      await expect(page.locator('body')).toBeVisible();
      
      // Check that main elements are still properly positioned
      const mainElements = page.locator('header, main, nav, [data-testid*="main"]');
      for (let i = 0; i < Math.min(3, await mainElements.count()); i++) {
        const element = mainElements.nth(i);
        if (await element.isVisible()) {
          const box = await element.boundingBox();
          if (box) {
            expect(box.width).toBeGreaterThan(0);
            expect(box.x).toBeGreaterThanOrEqual(-10); // Allow small overflow
          }
        }
      }
      
      // Language switcher should still be accessible
      const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
      if (await languageSwitcher.isVisible()) {
        await expect(languageSwitcher).toBeVisible();
        
        // Should be clickable on mobile
        if (device === 'mobile') {
          await languageSwitcher.tap();
        } else {
          await languageSwitcher.click();
        }
        
        const dropdown = page.locator('[data-testid*="language-switcher-content"]').first();
        await expect(dropdown).toBeVisible();
      }
    });
  });

  test('should handle RTL layout on very small screens', async ({ page }) => {
    await page.setViewportSize({ width: 320, height: 568 });
    await page.goto('/');
    
    // Change to Hebrew
    await changeToRTLLanguage(page, 'עברית', 'he');
    
    // Page should still be usable
    await expect(page.locator('body')).toBeVisible();
    
    // Check that content doesn't overflow inappropriately
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
    const viewportWidth = 320;
    
    // Allow some reasonable overflow but not excessive
    expect(bodyWidth).toBeLessThan(viewportWidth * 1.2);
    
    // Text should still be readable
    const textElements = page.locator('p, span, div').filter({ hasText: /.+/ });
    if (await textElements.count() > 0) {
      await expect(textElements.first()).toBeVisible();
    }
  });
});