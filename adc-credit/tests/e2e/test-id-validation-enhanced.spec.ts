/**
 * ADC Credit Service - Enhanced Test ID Validation Suite
 * 
 * Comprehensive E2E tests to validate all newly implemented test IDs
 * Focus: CreditDisplay, CookieBanner, CustomerCreditsDisplay components
 */

import { test, expect } from '@playwright/test';

// Import test ID constants from our centralized test-ids file
const CREDIT_DISPLAY_TEST_IDS = {
  CONTAINER: 'credit-display-container',
  CREDITS_BADGE: 'credit-display-credits-badge',
  CALCULATOR_BUTTON: 'credit-display-calculator-button',
  CALCULATOR_ICON: 'credit-display-calculator-icon',
  CONVERSION_CONTAINER: 'credit-display-conversion-container',
  CURRENCY_SELECT: 'credit-display-currency-select',
  CURRENCY_TRIGGER: 'credit-display-currency-trigger',
  CURRENCY_CONTENT: 'credit-display-currency-content',
  CURRENCY_ITEM: (currency: string) => `credit-display-currency-item-${currency}`,
  LOADING_SPINNER: 'credit-display-loading-spinner',
  CONVERSION_RESULT: 'credit-display-conversion-result',
  NO_RATE_BADGE: 'credit-display-no-rate-badge',
} as const;

const COOKIE_BANNER_TEST_IDS = {
  CONTAINER: 'cookie-banner-container',
  BANNER: 'cookie-banner-banner',
  ICON: 'cookie-banner-icon',
  CONTENT: 'cookie-banner-content',
  TITLE: 'cookie-banner-title',
  DESCRIPTION: 'cookie-banner-description',
  PRIVACY_LINK: 'cookie-banner-privacy-link',
  BUTTONS_CONTAINER: 'cookie-banner-buttons-container',
  CUSTOMIZE_BUTTON: 'cookie-banner-customize-button',
  CUSTOMIZE_ICON: 'cookie-banner-customize-icon',
  ESSENTIAL_ONLY_BUTTON: 'cookie-banner-essential-only-button',
  ACCEPT_ALL_BUTTON: 'cookie-banner-accept-all-button',
  PREFERENCES_DIALOG: 'cookie-banner-preferences-dialog',
  PREFERENCES_CONTENT: 'cookie-banner-preferences-content',
  ESSENTIAL_CARD: 'cookie-banner-essential-card',
  ESSENTIAL_SWITCH: 'cookie-banner-essential-switch',
  FUNCTIONAL_CARD: 'cookie-banner-functional-card',
  FUNCTIONAL_SWITCH: 'cookie-banner-functional-switch',
  ANALYTICS_CARD: 'cookie-banner-analytics-card',
  ANALYTICS_SWITCH: 'cookie-banner-analytics-switch',
  MARKETING_CARD: 'cookie-banner-marketing-card',
  MARKETING_SWITCH: 'cookie-banner-marketing-switch',
  SAVE_PREFERENCES_BUTTON: 'cookie-banner-save-preferences-button',
  DIALOG_ACCEPT_ALL_BUTTON: 'cookie-banner-dialog-accept-all-button',
} as const;

const CUSTOMER_CREDITS_DISPLAY_TEST_IDS = {
  CONTAINER: 'customer-credits-display-container',
  LOADING_CONTAINER: 'customer-credits-display-loading-container',
  ERROR_CARD: 'customer-credits-display-error-card',
  OVERVIEW_GRID: 'customer-credits-display-overview-grid',
  BALANCE_CARD: 'customer-credits-display-balance-card',
  BALANCE_AMOUNT: 'customer-credits-display-balance-amount',
  EARNED_CARD: 'customer-credits-display-earned-card',
  EARNED_AMOUNT: 'customer-credits-display-earned-amount',
  SPENT_CARD: 'customer-credits-display-spent-card',
  SPENT_AMOUNT: 'customer-credits-display-spent-amount',
  TRANSACTIONS_CARD: 'customer-credits-display-transactions-card',
  TRANSACTIONS_LIST: 'customer-credits-display-transactions-list',
  TRANSACTION_ITEM: (id: string) => `customer-credits-display-transaction-item-${id}`,
  EMPTY_TRANSACTIONS: 'customer-credits-display-empty-transactions',
  REFRESH_BUTTON: 'customer-credits-display-refresh-button',
} as const;

test.describe('Enhanced Test ID Validation Suite', () => {
  
  test.describe('CreditDisplay Component Test IDs', () => {
    test('should validate CreditDisplay component test IDs', async ({ page }) => {
      // Visit a page that contains CreditDisplay component (shop detail or customer view)
      await page.goto('/dashboard/merchant/shop/test-shop');
      
      // Check if CreditDisplay container exists
      const creditDisplayContainer = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONTAINER}"]`);
      
      if (await creditDisplayContainer.count() > 0) {
        // Validate main elements
        await expect(creditDisplayContainer).toBeVisible();
        await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CREDITS_BADGE}"]`)).toBeVisible();
        
        // Check calculator button if showConverter prop is true
        const calculatorButton = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CALCULATOR_BUTTON}"]`);
        if (await calculatorButton.count() > 0) {
          await expect(calculatorButton).toBeVisible();
          await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CALCULATOR_ICON}"]`)).toBeVisible();
          
          // Test calculator button interaction
          await calculatorButton.click();
          
          // Validate conversion container appears
          await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONVERSION_CONTAINER}"]`)).toBeVisible();
          await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_SELECT}"]`)).toBeVisible();
          await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_TRIGGER}"]`)).toBeVisible();
          
          // Test currency selection
          await page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_TRIGGER}"]`).click();
          await expect(page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_CONTENT}"]`)).toBeVisible();
          
          // Test specific currency items
          const usdItem = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_ITEM('USD')}"]`);
          const eurItem = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_ITEM('EUR')}"]`);
          const thbItem = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_ITEM('THB')}"]`);
          
          if (await usdItem.count() > 0) await expect(usdItem).toBeVisible();
          if (await eurItem.count() > 0) await expect(eurItem).toBeVisible();
          if (await thbItem.count() > 0) await expect(thbItem).toBeVisible();
          
          // Select a currency and check for conversion result or loading
          if (await usdItem.count() > 0) {
            await usdItem.click();
            
            // Check for either loading spinner or conversion result
            const loadingSpinner = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.LOADING_SPINNER}"]`);
            const conversionResult = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONVERSION_RESULT}"]`);
            const noRateBadge = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.NO_RATE_BADGE}"]`);
            
            // Wait for one of these states to appear
            await expect(loadingSpinner.or(conversionResult).or(noRateBadge)).toBeVisible({ timeout: 5000 });
          }
        }
        
        console.log('✅ CreditDisplay component test IDs validated');
      } else {
        console.log('ℹ️ CreditDisplay component not found on this page');
      }
    });
  });

  test.describe('CookieBanner Component Test IDs', () => {
    test('should validate CookieBanner test IDs and interactions', async ({ page }) => {
      // Clear localStorage to ensure cookie banner appears
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      
      await page.goto('/');
      
      // Wait for cookie banner to appear
      const cookieBannerContainer = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
      
      if (await cookieBannerContainer.isVisible({ timeout: 3000 })) {
        // Validate main banner elements
        await expect(cookieBannerContainer).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.BANNER}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ICON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTENT}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.TITLE}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.DESCRIPTION}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.PRIVACY_LINK}"]`)).toBeVisible();
        
        // Validate button container and buttons
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.BUTTONS_CONTAINER}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CUSTOMIZE_BUTTON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CUSTOMIZE_ICON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ESSENTIAL_ONLY_BUTTON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`)).toBeVisible();
        
        // Test customize button - opens preferences dialog
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CUSTOMIZE_BUTTON}"]`).click();
        
        // Validate preferences dialog
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.PREFERENCES_DIALOG}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.PREFERENCES_CONTENT}"]`)).toBeVisible();
        
        // Validate all cookie category cards
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ESSENTIAL_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.FUNCTIONAL_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ANALYTICS_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.MARKETING_CARD}"]`)).toBeVisible();
        
        // Validate all switches
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ESSENTIAL_SWITCH}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.FUNCTIONAL_SWITCH}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ANALYTICS_SWITCH}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.MARKETING_SWITCH}"]`)).toBeVisible();
        
        // Test switch interactions
        const functionalSwitch = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.FUNCTIONAL_SWITCH}"]`);
        const analyticsSwitch = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ANALYTICS_SWITCH}"]`);
        
        await functionalSwitch.click();
        await expect(functionalSwitch).toBeChecked();
        
        await analyticsSwitch.click();
        await expect(analyticsSwitch).toBeChecked();
        
        // Validate dialog action buttons
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.SAVE_PREFERENCES_BUTTON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.DIALOG_ACCEPT_ALL_BUTTON}"]`)).toBeVisible();
        
        // Test save preferences
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.SAVE_PREFERENCES_BUTTON}"]`).click();
        
        // Banner should disappear after saving
        await expect(cookieBannerContainer).not.toBeVisible({ timeout: 3000 });
        
        console.log('✅ CookieBanner component test IDs validated');
      } else {
        console.log('ℹ️ CookieBanner not visible (user may have already set preferences)');
      }
    });
    
    test('should validate Accept All and Essential Only functionality', async ({ page }) => {
      // Clear localStorage to ensure cookie banner appears
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      
      await page.goto('/');
      
      const cookieBannerContainer = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
      
      if (await cookieBannerContainer.isVisible({ timeout: 3000 })) {
        // Test Accept All functionality
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
        
        // Banner should disappear
        await expect(cookieBannerContainer).not.toBeVisible({ timeout: 3000 });
        
        // Verify localStorage was set correctly
        const preferences = await page.evaluate(() => {
          const prefs = localStorage.getItem('adc-credit-cookie-preferences');
          return prefs ? JSON.parse(prefs) : null;
        });
        
        expect(preferences).toEqual({
          essential: true,
          functional: true,
          analytics: true,
          marketing: true
        });
        
        // Reset and test Essential Only
        await page.evaluate(() => {
          localStorage.removeItem('adc-credit-cookie-consent');
          localStorage.removeItem('adc-credit-cookie-preferences');
        });
        
        await page.reload();
        
        if (await cookieBannerContainer.isVisible({ timeout: 3000 })) {
          await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ESSENTIAL_ONLY_BUTTON}"]`).click();
          
          // Banner should disappear
          await expect(cookieBannerContainer).not.toBeVisible({ timeout: 3000 });
          
          // Verify localStorage was set correctly
          const essentialPrefs = await page.evaluate(() => {
            const prefs = localStorage.getItem('adc-credit-cookie-preferences');
            return prefs ? JSON.parse(prefs) : null;
          });
          
          expect(essentialPrefs).toEqual({
            essential: true,
            functional: false,
            analytics: false,
            marketing: false
          });
        }
        
        console.log('✅ CookieBanner Accept All and Essential Only functionality validated');
      }
    });
  });

  test.describe('CustomerCreditsDisplay Component Test IDs', () => {
    test('should validate CustomerCreditsDisplay test IDs', async ({ page }) => {
      // Navigate to customer credits page (assuming customer authentication)
      await page.goto('/customer/shops/test-shop');
      
      const creditsContainer = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.CONTAINER}"]`);
      
      if (await creditsContainer.count() > 0) {
        // Check for loading state first
        const loadingContainer = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.LOADING_CONTAINER}"]`);
        
        if (await loadingContainer.isVisible({ timeout: 2000 })) {
          console.log('ℹ️ CustomerCreditsDisplay loading state detected');
          
          // Wait for loading to complete
          await expect(loadingContainer).not.toBeVisible({ timeout: 10000 });
        }
        
        // Check for error state
        const errorCard = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.ERROR_CARD}"]`);
        
        if (await errorCard.isVisible({ timeout: 2000 })) {
          console.log('ℹ️ CustomerCreditsDisplay error state detected');
          return;
        }
        
        // Validate main container and overview grid
        await expect(creditsContainer).toBeVisible();
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.OVERVIEW_GRID}"]`)).toBeVisible();
        
        // Validate credit balance cards
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.BALANCE_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.BALANCE_AMOUNT}"]`)).toBeVisible();
        
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.EARNED_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.EARNED_AMOUNT}"]`)).toBeVisible();
        
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.SPENT_CARD}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.SPENT_AMOUNT}"]`)).toBeVisible();
        
        // Validate transactions section
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.TRANSACTIONS_CARD}"]`)).toBeVisible();
        
        // Check for transactions list or empty state
        const transactionsList = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.TRANSACTIONS_LIST}"]`);
        const emptyTransactions = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.EMPTY_TRANSACTIONS}"]`);
        
        if (await transactionsList.count() > 0) {
          // Has transactions - validate dynamic transaction items
          await expect(transactionsList).toBeVisible();
          
          // Get all transaction items and validate their test IDs
          const transactionItems = await page.locator('[data-testid*="customer-credits-display-transaction-item-"]').all();
          
          for (const item of transactionItems) {
            const testId = await item.getAttribute('data-testid');
            if (testId) {
              const transactionId = testId.replace('customer-credits-display-transaction-item-', '');
              
              // Validate related elements for this transaction
              await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.TRANSACTION_ITEM(transactionId)}"]`)).toBeVisible();
              
              // Check if other dynamic elements exist
              const transactionIcon = page.locator(`[data-testid="customer-credits-display-transaction-icon-${transactionId}"]`);
              if (await transactionIcon.count() > 0) {
                await expect(transactionIcon).toBeVisible();
              }
            }
          }
          
          console.log(`✅ Found ${transactionItems.length} transaction items with proper test IDs`);
        } else if (await emptyTransactions.count() > 0) {
          // Empty state
          await expect(emptyTransactions).toBeVisible();
          console.log('ℹ️ CustomerCreditsDisplay showing empty transactions state');
        }
        
        // Validate refresh button
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.REFRESH_BUTTON}"]`)).toBeVisible();
        
        // Test refresh functionality
        await page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.REFRESH_BUTTON}"]`).click();
        
        // Button should be disabled during refresh
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.REFRESH_BUTTON}"]`)).toBeDisabled({ timeout: 2000 });
        
        // Button should be enabled again after refresh
        await expect(page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.REFRESH_BUTTON}"]`)).toBeEnabled({ timeout: 5000 });
        
        console.log('✅ CustomerCreditsDisplay component test IDs validated');
      } else {
        console.log('ℹ️ CustomerCreditsDisplay component not found on this page');
      }
    });
  });

  test.describe('Cross-Component Test ID Integration', () => {
    test('should validate test IDs work across multiple components', async ({ page }) => {
      // Clear localStorage to ensure cookie banner appears
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      
      await page.goto('/');
      
      // Handle cookie banner if it appears
      const cookieBanner = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
      if (await cookieBanner.isVisible({ timeout: 3000 })) {
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
        await expect(cookieBanner).not.toBeVisible({ timeout: 3000 });
      }
      
      // Navigate to a page with multiple components
      await page.goto('/dashboard/merchant/shop/test-shop');
      
      // Look for CreditDisplay components
      const creditDisplays = await page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONTAINER}"]`).all();
      
      if (creditDisplays.length > 0) {
        console.log(`✅ Found ${creditDisplays.length} CreditDisplay components with test IDs`);
        
        // Test interaction with first CreditDisplay
        const firstDisplay = creditDisplays[0];
        const calculatorButton = firstDisplay.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CALCULATOR_BUTTON}"]`);
        
        if (await calculatorButton.count() > 0) {
          await calculatorButton.click();
          
          // Verify conversion UI appears
          await expect(firstDisplay.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONVERSION_CONTAINER}"]`)).toBeVisible();
        }
      }
      
      console.log('✅ Cross-component test ID integration validated');
    });
  });

  test.describe('Test ID Performance and Reliability', () => {
    test('should ensure test IDs provide faster and more reliable selectors', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      
      // Test fast loading with test ID selectors
      await page.locator(`[data-testid]`).first().waitFor({ state: 'visible', timeout: 5000 });
      
      const loadTime = Date.now() - startTime;
      console.log(`✅ Page with test IDs loaded in ${loadTime}ms`);
      
      // Test reliability - test IDs should be more stable than CSS selectors
      const testIdElements = await page.locator('[data-testid]').count();
      console.log(`✅ Found ${testIdElements} elements with test IDs`);
      
      // Verify test IDs follow naming convention
      const allTestIds = await page.locator('[data-testid]').evaluateAll(elements => 
        elements.map(el => el.getAttribute('data-testid')).filter(Boolean)
      );
      
      const invalidTestIds = allTestIds.filter(id => 
        !id.match(/^[a-z-]+$/) || id.includes('_') || id.includes(' ')
      );
      
      if (invalidTestIds.length > 0) {
        console.log(`⚠️ Found ${invalidTestIds.length} test IDs that don't follow naming convention:`, invalidTestIds);
      } else {
        console.log('✅ All test IDs follow kebab-case naming convention');
      }
      
      expect(loadTime).toBeLessThan(5000);
      expect(testIdElements).toBeGreaterThan(0);
    });
  });
});