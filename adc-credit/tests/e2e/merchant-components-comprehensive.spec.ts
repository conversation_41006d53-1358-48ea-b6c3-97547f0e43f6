/**
 * ADC Credit Merchant Components E2E Tests
 * 
 * Comprehensive testing of merchant-specific components with stable test IDs
 */

import { test, expect } from '@playwright/test';

test.describe('Merchant Components Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up console monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test.describe('Usage Tab Component', () => {
    test('should render usage tab with comprehensive test IDs', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      
      // Wait for page to load
      await page.waitForTimeout(2000);
      
      // Check for usage tab (might need to click on it)
      const usageTabTrigger = page.locator('[role="tab"]').filter({ hasText: 'Usage' });
      if (await usageTabTrigger.isVisible()) {
        await usageTabTrigger.click();
        await page.waitForTimeout(1000);
      }
      
      // Check usage tab container
      const usageTabContainer = page.locator('[data-testid="merchant-usage-tab-container"]');
      if (await usageTabContainer.isVisible()) {
        // Check main title and description
        await expect(page.locator('[data-testid="merchant-usage-title"]')).toBeVisible();
        await expect(page.locator('[data-testid="merchant-usage-description"]')).toBeVisible();
        
        console.log('✅ Usage tab main elements rendered correctly');
        
        // Check API Usage Summary Card
        const apiUsageCard = page.locator('[data-testid="merchant-usage-api-card"]');
        if (await apiUsageCard.isVisible()) {
          console.log('✅ API Usage Summary card found');
          
          // Check for loading state or data
          const apiLoading = page.locator('[data-testid="merchant-usage-api-loading"]');
          const apiTotalCalls = page.locator('[data-testid="merchant-usage-api-total-calls"]');
          
          if (await apiLoading.isVisible()) {
            console.log('✅ API usage loading state displayed');
          } else if (await apiTotalCalls.isVisible()) {
            // Check API usage metrics
            await expect(page.locator('[data-testid="merchant-usage-api-credits-used"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-api-qr-codes-used"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-api-response-time"]')).toBeVisible();
            console.log('✅ API usage metrics displayed');
          }
        }
        
        // Check Subscription Usage Card
        const subscriptionCard = page.locator('[data-testid="merchant-usage-subscription-card"]');
        if (await subscriptionCard.isVisible()) {
          console.log('✅ Subscription Usage card found');
          
          // Check for loading state or data
          const subscriptionLoading = page.locator('[data-testid="merchant-usage-subscription-loading"]');
          const shopsLimit = page.locator('[data-testid="merchant-usage-shops-limit"]');
          
          if (await subscriptionLoading.isVisible()) {
            console.log('✅ Subscription loading state displayed');
          } else if (await shopsLimit.isVisible()) {
            // Check subscription limit elements
            await expect(page.locator('[data-testid="merchant-usage-shops-progress"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-qr-limit"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-qr-progress"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-webhooks-limit"]')).toBeVisible();
            await expect(page.locator('[data-testid="merchant-usage-credits-limit"]')).toBeVisible();
            console.log('✅ Subscription usage metrics displayed');
          }
        }
        
        // Check Recent API Activity Card
        const activityCard = page.locator('[data-testid="merchant-usage-activity-card"]');
        if (await activityCard.isVisible()) {
          console.log('✅ Recent API Activity card found');
          
          // Check for different states
          const activityLoading = page.locator('[data-testid="merchant-usage-activity-loading"]');
          const activityList = page.locator('[data-testid="merchant-usage-activity-list"]');
          const activityEmpty = page.locator('[data-testid="merchant-usage-activity-empty-state"]');
          
          if (await activityLoading.isVisible()) {
            console.log('✅ Activity loading state displayed');
          } else if (await activityList.isVisible()) {
            // Check for activity items
            const activityItems = page.locator('[data-testid^="merchant-usage-activity-item-"]');
            const itemCount = await activityItems.count();
            console.log(`✅ Found ${itemCount} activity items`);
          } else if (await activityEmpty.isVisible()) {
            console.log('✅ Activity empty state displayed');
          }
        }
        
        // Check Performance Metrics Card (optional)
        const performanceCard = page.locator('[data-testid="merchant-usage-performance-card"]');
        if (await performanceCard.isVisible()) {
          await expect(page.locator('[data-testid="merchant-usage-performance-grid"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-usage-metric-avg-response"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-usage-metric-p95-response"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-usage-metric-error-rate"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-usage-metric-total-requests"]')).toBeVisible();
          console.log('✅ Performance metrics displayed');
        }
      } else {
        console.log('⚠️ Usage tab not visible - may need authentication or different navigation path');
      }
    });

    test('should handle usage tab error states', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      
      // Mock API error responses
      await page.route('**/api/v1/usage/**', async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });
      
      // Navigate to usage tab
      const usageTabTrigger = page.locator('[role="tab"]').filter({ hasText: 'Usage' });
      if (await usageTabTrigger.isVisible()) {
        await usageTabTrigger.click();
        await page.waitForTimeout(1000);
        
        // Check for error alert
        const errorAlert = page.locator('[data-testid="merchant-usage-error-alert"]');
        if (await errorAlert.isVisible()) {
          console.log('✅ Usage error alert displayed correctly');
        }
      }
    });
  });

  test.describe('Analytics Tab Component', () => {
    test('should render analytics tab with test IDs', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      
      // Wait for page to load
      await page.waitForTimeout(2000);
      
      // Navigate to analytics tab
      const analyticsTabTrigger = page.locator('[role="tab"]').filter({ hasText: 'Analytics' });
      if (await analyticsTabTrigger.isVisible()) {
        await analyticsTabTrigger.click();
        await page.waitForTimeout(1000);
        
        // Check analytics tab container
        const analyticsContainer = page.locator('[data-testid="merchant-analytics-tab-container"]');
        if (await analyticsContainer.isVisible()) {
          await expect(page.locator('[data-testid="merchant-analytics-title"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-analytics-description"]')).toBeVisible();
          
          // Check analytics cards
          await expect(page.locator('[data-testid="merchant-analytics-credit-performance-card"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-analytics-shop-performance-card"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-analytics-growth-metrics-card"]')).toBeVisible();
          
          // Check chart placeholder
          const chartCard = page.locator('[data-testid="merchant-analytics-chart-card"]');
          if (await chartCard.isVisible()) {
            await expect(page.locator('[data-testid="merchant-analytics-chart-placeholder"]')).toBeVisible();
          }
          
          console.log('✅ Analytics tab elements rendered correctly');
        } else {
          console.log('⚠️ Analytics tab not visible');
        }
      } else {
        console.log('⚠️ Analytics tab trigger not found');
      }
    });
  });

  test.describe('Payment QR Code Component', () => {
    test('should render payment QR code dialog with test IDs', async ({ page }) => {
      // Navigate to a page that has the payment QR component
      await page.goto('/dashboard/merchant');
      
      // Wait for page to load
      await page.waitForTimeout(2000);
      
      // Look for Payment QR trigger button
      const qrTrigger = page.locator('[data-testid="merchant-payment-qr-trigger"]');
      
      if (await qrTrigger.isVisible()) {
        // Click to open QR dialog
        await qrTrigger.click();
        
        // Check dialog container
        const qrDialog = page.locator('[data-testid="merchant-payment-qr-dialog"]');
        await expect(qrDialog).toBeVisible();
        
        // Check form elements
        await expect(page.locator('[data-testid="merchant-payment-qr-amount-input"]')).toBeVisible();
        await expect(page.locator('[data-testid="merchant-payment-qr-generate-button"]')).toBeVisible();
        await expect(page.locator('[data-testid="merchant-payment-qr-cancel-button"]')).toBeVisible();
        
        console.log('✅ Payment QR dialog elements rendered correctly');
        
        // Test amount input and generation
        const amountInput = page.locator('[data-testid="merchant-payment-qr-amount-input"]');
        await amountInput.fill('100');
        
        // Mock QR generation response
        await page.route('**/api/v1/qr-code/**', async route => {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=='
            })
          });
        });
        
        // Click generate button
        const generateButton = page.locator('[data-testid="merchant-payment-qr-generate-button"]');
        await generateButton.click();
        
        // Wait for QR code to be generated
        await page.waitForTimeout(2000);
        
        // Check for QR code display
        const qrDisplay = page.locator('[data-testid="merchant-payment-qr-display"]');
        if (await qrDisplay.isVisible()) {
          await expect(page.locator('[data-testid="merchant-payment-qr-download-button"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-payment-qr-share-button"]')).toBeVisible();
          console.log('✅ QR code generated and action buttons displayed');
        }
        
        // Close dialog
        const cancelButton = page.locator('[data-testid="merchant-payment-qr-cancel-button"]');
        await cancelButton.click();
        
        // Verify dialog is closed
        await expect(qrDialog).not.toBeVisible();
        console.log('✅ QR dialog closes correctly');
        
      } else {
        console.log('⚠️ Payment QR trigger not found on this page');
      }
    });

    test('should handle QR code generation validation', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      await page.waitForTimeout(2000);
      
      const qrTrigger = page.locator('[data-testid="merchant-payment-qr-trigger"]');
      
      if (await qrTrigger.isVisible()) {
        await qrTrigger.click();
        
        // Try to generate without amount
        const generateButton = page.locator('[data-testid="merchant-payment-qr-generate-button"]');
        
        // Button should be disabled with no amount
        const isDisabled = await generateButton.isDisabled();
        if (isDisabled) {
          console.log('✅ Generate button properly disabled without amount');
        }
        
        // Enter invalid amount
        const amountInput = page.locator('[data-testid="merchant-payment-qr-amount-input"]');
        await amountInput.fill('0');
        
        // Button should still be disabled
        const stillDisabled = await generateButton.isDisabled();
        if (stillDisabled) {
          console.log('✅ Generate button properly disabled with invalid amount');
        }
        
        // Enter valid amount
        await amountInput.fill('50');
        
        // Button should be enabled
        const isEnabled = !(await generateButton.isDisabled());
        if (isEnabled) {
          console.log('✅ Generate button enabled with valid amount');
        }
      }
    });
  });

  test.describe('Cross-Merchant Component Integration', () => {
    test('should maintain consistent merchant interface across tabs', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      await page.waitForTimeout(2000);
      
      // Test navigation between merchant tabs
      const tabs = ['shops', 'analytics', 'usage'];
      
      for (const tabName of tabs) {
        const tabTrigger = page.locator('[role="tab"]').filter({ hasText: new RegExp(tabName, 'i') });
        
        if (await tabTrigger.isVisible()) {
          await tabTrigger.click();
          await page.waitForTimeout(1000);
          
          console.log(`📄 Testing ${tabName} tab integration`);
          
          // Check for tab-specific test IDs
          switch (tabName) {
            case 'shops':
              const shopsContainer = page.locator('[data-testid="merchant-shops-tab-content"], [data-testid="merchant-shops-card"]');
              if (await shopsContainer.isVisible()) {
                console.log(`✅ ${tabName} tab content visible`);
              }
              break;
            case 'analytics':
              const analyticsContainer = page.locator('[data-testid="merchant-analytics-tab-container"]');
              if (await analyticsContainer.isVisible()) {
                console.log(`✅ ${tabName} tab content visible`);
              }
              break;
            case 'usage':
              const usageContainer = page.locator('[data-testid="merchant-usage-tab-container"]');
              if (await usageContainer.isVisible()) {
                console.log(`✅ ${tabName} tab content visible`);
              }
              break;
          }
        }
      }
    });

    test('should handle merchant responsive design', async ({ page }) => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/dashboard/merchant');
        
        console.log(`\n📱 Testing merchant components on ${viewport.name} (${viewport.width}x${viewport.height})`);
        
        await page.waitForTimeout(1000);
        
        // Check for merchant content visibility
        const merchantContent = page.locator('[data-testid*="merchant-"]').first();
        if (await merchantContent.isVisible()) {
          console.log('✅ Merchant content visible on this viewport');
        }
        
        // Check for Payment QR trigger (should be responsive)
        const qrTrigger = page.locator('[data-testid="merchant-payment-qr-trigger"]');
        if (await qrTrigger.isVisible()) {
          console.log('✅ Payment QR trigger responsive');
        }
        
        await page.waitForTimeout(500);
      }
    });

    test('should handle merchant component loading states', async ({ page }) => {
      // Simulate slow network
      await page.route('**/api/v1/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        await route.continue();
      });
      
      await page.goto('/dashboard/merchant');
      
      // Check for loading states in different components
      const loadingElements = [
        '[data-testid="merchant-usage-api-loading"]',
        '[data-testid="merchant-usage-subscription-loading"]',
        '[data-testid="merchant-usage-activity-loading"]',
        '[data-testid="merchant-shops-loading"]'
      ];
      
      for (const loadingSelector of loadingElements) {
        const loadingElement = page.locator(loadingSelector);
        if (await loadingElement.isVisible()) {
          console.log(`✅ Loading state visible: ${loadingSelector}`);
        }
      }
      
      // Wait for loading to complete
      await page.waitForTimeout(3000);
      
      console.log('✅ Merchant component loading states tested');
    });
  });
});