/**
 * ADC Credit Cookies Page E2E Tests - WITH TEST IDS
 * 
 * Tests that use stable data-testid selectors for reliable test execution
 */

import { test, expect } from '@playwright/test';

// Cookie Page Test IDs (matching the implementation)
const COOKIE_TEST_IDS = {
  CONTAINER: "cookie-settings-container",
  TITLE: "cookie-settings-title",
  OVERVIEW_CARD: "cookie-overview-card",
  ESSENTIAL_CARD: "cookie-essential-card",
  ESSENTIAL_TOGGLE: "cookie-essential-toggle",
  FUNCTIONAL_CARD: "cookie-functional-card", 
  FUNCTIONAL_TOGGLE: "cookie-functional-toggle",
  ANALYTICS_CARD: "cookie-analytics-card",
  ANALYTICS_TOGGLE: "cookie-analytics-toggle",
  MARKETING_CARD: "cookie-marketing-card",
  MARKETING_TOGGLE: "cookie-marketing-toggle",
  SAVE_BUTTON: "cookie-save-button",
  ACCEPT_ALL_BUTTON: "cookie-accept-all-button",
  ESSENTIAL_ONLY_BUTTON: "cookie-essential-only-button",
  INFO_CARD: "cookie-info-card",
  CONTACT_EMAIL: "cookie-contact-email"
} as const;

test.describe('ADC Credit Cookies Page - With Test IDs', () => {
  
  test.describe('Page Structure and Elements', () => {
    test('should render all cookie page elements with test IDs', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check main container
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTAINER}"]`)).toBeVisible();
      
      // Check title
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.TITLE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.TITLE}"]`)).toHaveText('Cookie Settings');
      
      // Check overview card
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.OVERVIEW_CARD}"]`)).toBeVisible();
      
      // Check all cookie category cards
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_CARD}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_CARD}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_CARD}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_CARD}"]`)).toBeVisible();
      
      // Check all toggle switches
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`)).toBeVisible();
      
      // Check action buttons
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ACCEPT_ALL_BUTTON}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_ONLY_BUTTON}"]`)).toBeVisible();
      
      // Check info card and contact email
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.INFO_CARD}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTACT_EMAIL}"]`)).toBeVisible();
      
      console.log('✅ All cookie page elements with test IDs verified');
    });
    
    test('should verify initial toggle states', async ({ page }) => {
      await page.goto('/cookies');
      
      // Essential should be checked and disabled
      const essentialToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`);
      await expect(essentialToggle).toBeChecked();
      await expect(essentialToggle).toBeDisabled();
      
      // Other toggles should be unchecked by default (unless saved preferences exist)
      const functionalToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`);
      const analyticsToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`);
      const marketingToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`);
      
      // Clear localStorage first to ensure clean state
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      await page.reload();
      
      await expect(functionalToggle).not.toBeChecked();
      await expect(analyticsToggle).not.toBeChecked();
      await expect(marketingToggle).not.toBeChecked();
      
      console.log('✅ Initial toggle states verified');
    });
  });
  
  test.describe('Cookie Preferences Interactions', () => {
    test('should handle individual toggle interactions', async ({ page }) => {
      await page.goto('/cookies');
      
      // Clear localStorage for clean state
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      await page.reload();
      
      // Toggle functional cookies
      const functionalToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`);
      await functionalToggle.click();
      await expect(functionalToggle).toBeChecked();
      
      // Toggle analytics cookies
      const analyticsToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`);
      await analyticsToggle.click();
      await expect(analyticsToggle).toBeChecked();
      
      // Toggle marketing cookies
      const marketingToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`);
      await marketingToggle.click();
      await expect(marketingToggle).toBeChecked();
      
      // Toggle them off
      await functionalToggle.click();
      await expect(functionalToggle).not.toBeChecked();
      
      console.log('✅ Individual toggle interactions verified');
    });
    
    test('should save preferences correctly', async ({ page }) => {
      await page.goto('/cookies');
      
      // Clear localStorage for clean state
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      await page.reload();
      
      // Set specific preferences
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`).click();
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`).click();
      
      // Save preferences
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`).click();
      
      // Check for success toast
      await expect(page.locator('text="Cookie preferences saved successfully!"')).toBeVisible({ timeout: 5000 });
      
      // Verify localStorage
      const consent = await page.evaluate(() => localStorage.getItem('adc-credit-cookie-consent'));
      const preferences = await page.evaluate(() => localStorage.getItem('adc-credit-cookie-preferences'));
      
      expect(consent).toBe('true');
      expect(preferences).toBeTruthy();
      
      // Parse and verify preferences
      const parsedPrefs = await page.evaluate(() => {
        const prefs = localStorage.getItem('adc-credit-cookie-preferences');
        return prefs ? JSON.parse(prefs) : null;
      });
      
      expect(parsedPrefs).toEqual({
        essential: true,
        functional: true,
        analytics: true,
        marketing: false
      });
      
      console.log('✅ Save preferences functionality verified');
    });
    
    test('should handle Accept All Cookies functionality', async ({ page }) => {
      await page.goto('/cookies');
      
      // Clear localStorage for clean state
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      await page.reload();
      
      // Click Accept All button
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
      
      // Check for success toast
      await expect(page.locator('text="All cookies accepted!"')).toBeVisible({ timeout: 5000 });
      
      // Verify all toggles are now checked
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`)).toBeChecked();
      
      // Verify localStorage
      const preferences = await page.evaluate(() => {
        const prefs = localStorage.getItem('adc-credit-cookie-preferences');
        return prefs ? JSON.parse(prefs) : null;
      });
      
      expect(preferences).toEqual({
        essential: true,
        functional: true,
        analytics: true,
        marketing: true
      });
      
      console.log('✅ Accept All Cookies functionality verified');
    });
    
    test('should handle Essential Only functionality', async ({ page }) => {
      await page.goto('/cookies');
      
      // First accept all cookies
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
      await expect(page.locator('text="All cookies accepted!"')).toBeVisible({ timeout: 5000 });
      
      // Then click Essential Only
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_ONLY_BUTTON}"]`).click();
      
      // Check for success toast
      await expect(page.locator('text="Reset to essential cookies only!"')).toBeVisible({ timeout: 5000 });
      
      // Verify only essential is checked
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`)).not.toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`)).not.toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`)).not.toBeChecked();
      
      // Verify localStorage
      const preferences = await page.evaluate(() => {
        const prefs = localStorage.getItem('adc-credit-cookie-preferences');
        return prefs ? JSON.parse(prefs) : null;
      });
      
      expect(preferences).toEqual({
        essential: true,
        functional: false,
        analytics: false,
        marketing: false
      });
      
      console.log('✅ Essential Only functionality verified');
    });
  });
  
  test.describe('Persistence and State Management', () => {
    test('should persist preferences across page reloads', async ({ page }) => {
      await page.goto('/cookies');
      
      // Clear localStorage for clean state
      await page.evaluate(() => {
        localStorage.removeItem('adc-credit-cookie-consent');
        localStorage.removeItem('adc-credit-cookie-preferences');
      });
      await page.reload();
      
      // Set specific preferences
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`).click();
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`).click();
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`).click();
      
      // Wait for toast
      await expect(page.locator('text="Cookie preferences saved successfully!"')).toBeVisible();
      
      // Reload page
      await page.reload();
      
      // Verify preferences are maintained
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`)).not.toBeChecked();
      
      console.log('✅ Preferences persistence verified');
    });
    
    test('should handle invalid localStorage data gracefully', async ({ page }) => {
      await page.goto('/cookies');
      
      // Inject invalid localStorage data
      await page.evaluate(() => {
        localStorage.setItem('adc-credit-cookie-preferences', 'invalid-json');
      });
      
      // Reload page
      await page.reload();
      
      // Page should still load without errors
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.TITLE}"]`)).toBeVisible();
      
      // Should fall back to default preferences
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`)).toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`)).not.toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.ANALYTICS_TOGGLE}"]`)).not.toBeChecked();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.MARKETING_TOGGLE}"]`)).not.toBeChecked();
      
      console.log('✅ Invalid localStorage handling verified');
    });
  });
  
  test.describe('UI/UX and Accessibility', () => {
    test('should have proper accessibility attributes', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check page title
      const title = await page.title();
      expect(title).toBeTruthy();
      
      // Check main heading
      const mainHeading = page.locator(`[data-testid="${COOKIE_TEST_IDS.TITLE}"]`);
      await expect(mainHeading).toBeVisible();
      
      // Check that essential switch is disabled (accessibility requirement)
      const essentialToggle = page.locator(`[data-testid="${COOKIE_TEST_IDS.ESSENTIAL_TOGGLE}"]`);
      await expect(essentialToggle).toBeDisabled();
      
      // Check that buttons are keyboard accessible
      const saveButton = page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`);
      await saveButton.focus();
      const isFocused = await saveButton.evaluate(el => document.activeElement === el);
      expect(isFocused).toBeTruthy();
      
      console.log('✅ Accessibility attributes verified');
    });
    
    test('should handle responsive design', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/cookies');
      
      // Main elements should still be visible
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTAINER}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.TITLE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`)).toBeVisible();
      
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await expect(page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTAINER}"]`)).toBeVisible();
      
      console.log('✅ Responsive design verified');
    });
    
    test('should verify contact email link', async ({ page }) => {
      await page.goto('/cookies');
      
      const contactEmail = page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTACT_EMAIL}"]`);
      await expect(contactEmail).toBeVisible();
      await expect(contactEmail).toHaveAttribute('href', 'mailto:<EMAIL>');
      await expect(contactEmail).toHaveText('<EMAIL>');
      
      console.log('✅ Contact email link verified');
    });
  });
  
  test.describe('Performance and Error Handling', () => {
    test('should load quickly with test IDs', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/cookies');
      
      // Wait for main content using test ID
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.CONTAINER}"]`).waitFor({ state: 'visible' });
      
      const loadTime = Date.now() - startTime;
      
      // Page should load within 5 seconds (faster than previous structure-based tests)
      expect(loadTime).toBeLessThan(5000);
      
      console.log(`✅ Page with test IDs loaded in ${loadTime}ms`);
    });
    
    test('should handle JavaScript errors gracefully', async ({ page }) => {
      const jsErrors: string[] = [];
      
      page.on('pageerror', err => {
        jsErrors.push(err.message);
      });
      
      await page.goto('/cookies');
      
      // Interact with elements using test IDs
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.FUNCTIONAL_TOGGLE}"]`).click();
      await page.locator(`[data-testid="${COOKIE_TEST_IDS.SAVE_BUTTON}"]`).click();
      
      // Wait a moment for any async errors
      await page.waitForTimeout(2000);
      
      if (jsErrors.length > 0) {
        console.log(`⚠️ Found ${jsErrors.length} JavaScript errors:`);
        jsErrors.forEach(error => console.log(`  - ${error}`));
      } else {
        console.log('✅ No JavaScript errors detected');
      }
      
      // Test should not fail for JS errors, just log them
    });
  });
});