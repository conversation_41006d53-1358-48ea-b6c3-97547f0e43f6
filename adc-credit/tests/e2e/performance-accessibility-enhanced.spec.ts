/**
 * Enhanced Performance & Accessibility E2E Tests
 * ADC Credit Service - Comprehensive Non-Functional Testing
 * 
 * Tests performance, accessibility, and user experience using the comprehensive 
 * test ID system implemented across all phases. This test suite covers:
 * 
 * Test Categories:
 * - Page Load Performance & Core Web Vitals
 * - Accessibility Compliance (WCAG 2.1 AA)
 * - Keyboard Navigation & Screen Reader Support
 * - Mobile Performance & Touch Interactions
 * - Progressive Enhancement & Offline Capabilities
 * 
 * Features comprehensive test ID coverage:
 * - All phases 1-7 test IDs for performance measurements
 * - Focus management and accessibility validation
 * - Mobile-specific test ID variations
 * - Performance monitoring across workflows
 */

import { test, expect } from '@playwright/test';

// Test Group: Page Load Performance
test.describe('Enhanced Page Load Performance', () => {
  test('should meet Core Web Vitals benchmarks on all major pages', async ({ page }) => {
    const pages = [
      { url: '/', name: 'Landing Page', container: 'hero-section-container' },
      { url: '/dashboard', name: 'Dashboard', container: 'dashboard-container' },
      { url: '/dashboard/shops', name: 'Shops', container: 'shops-container' },
      { url: '/dashboard/customers', name: 'Customers', container: 'customers-container' },
      { url: '/dashboard/api-keys', name: 'API Keys', container: 'api-keys-container' },
      { url: '/dashboard/generate', name: 'Generate', container: 'generate-container' },
      { url: '/dashboard/usage', name: 'Usage', container: 'usage-container' },
      { url: '/dashboard/subscription', name: 'Subscription', container: 'subscription-container' },
      { url: '/dashboard/webhooks', name: 'Webhooks', container: 'webhooks-container' }
    ];

    for (const pageInfo of pages) {
      // Measure page load time
      const startTime = Date.now();
      await page.goto(pageInfo.url);
      
      // Wait for main container to be visible
      await expect(page.locator(`[data-testid="${pageInfo.container}"]`)).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      
      // Performance benchmarks
      expect(loadTime, `${pageInfo.name} load time`).toBeLessThan(3000); // < 3 seconds
      
      // Test First Contentful Paint equivalent (main content visible)
      const fcpTime = Date.now() - startTime;
      expect(fcpTime, `${pageInfo.name} FCP`).toBeLessThan(1500); // < 1.5 seconds
      
      console.log(`${pageInfo.name}: Load time ${loadTime}ms, FCP ${fcpTime}ms`);
    }
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Mock large shop dataset
    const largeShopsData = Array.from({ length: 100 }, (_, i) => ({
      id: `shop-${i}`,
      slug: `test-shop-${i}`,
      name: `Test Shop ${i}`,
      description: `Description for test shop ${i}`,
      customer_count: Math.floor(Math.random() * 1000),
      total_credits_distributed: Math.floor(Math.random() * 50000),
      created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    }));

    await page.route('**/api/v1/shops', async (route) => {
      await route.fulfill({ json: largeShopsData });
    });

    const startTime = Date.now();
    await page.goto('/dashboard/shops');
    
    // Wait for shops grid to be visible
    await expect(page.locator('[data-testid="shops-grid"]')).toBeVisible();
    
    // Wait for all shop cards to render
    await expect(page.locator('[data-testid^="shops-shop-card-"]')).toHaveCount(100);
    
    const renderTime = Date.now() - startTime;
    expect(renderTime, 'Large dataset render time').toBeLessThan(5000); // < 5 seconds
    
    // Test scrolling performance with large dataset
    const scrollStartTime = Date.now();
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(100); // Allow scroll to complete
    const scrollTime = Date.now() - scrollStartTime;
    
    expect(scrollTime, 'Scroll performance').toBeLessThan(500); // < 500ms
  });

  test('should maintain performance during API interactions', async ({ page }) => {
    await page.goto('/dashboard/customers');
    
    // Mock customer search with delay
    await page.route('**/api/v1/customers*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network delay
      await route.fulfill({
        json: [
          { id: 'perf-customer-1', email: '<EMAIL>', name: 'Performance Test 1' },
          { id: 'perf-customer-2', email: '<EMAIL>', name: 'Performance Test 2' }
        ]
      });
    });

    // Test search performance
    const searchInput = page.locator('[data-testid="customers-search-input"]');
    
    const searchStartTime = Date.now();
    await searchInput.fill('performance');
    await searchInput.press('Enter');
    
    // Wait for search results
    await expect(page.locator('[data-testid="customers-table-row-perf-customer-1"]')).toBeVisible();
    
    const searchTime = Date.now() - searchStartTime;
    expect(searchTime, 'Search response time').toBeLessThan(1000); // < 1 second including network delay
    
    // Test that UI remains responsive during search
    await expect(page.locator('[data-testid="customers-search-input"]')).toBeEnabled();
    await expect(page.locator('[data-testid="customers-add-customer-button"]')).toBeEnabled();
  });

  test('should optimize bundle size and loading behavior', async ({ page }) => {
    // Test that initial page load doesn't block critical rendering
    await page.goto('/dashboard');
    
    // Critical elements should be visible immediately
    await expect(page.locator('[data-testid="dashboard-container"]')).toBeVisible({ timeout: 2000 });
    await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible({ timeout: 2000 });
    await expect(page.locator('[data-testid="dashboard-navigation"]')).toBeVisible({ timeout: 2000 });
    
    // Test lazy loading behavior
    await page.goto('/dashboard/usage');
    
    // Analytics charts should load progressively
    await expect(page.locator('[data-testid="usage-container"]')).toBeVisible();
    
    // Heavy components should load after critical content
    await expect(page.locator('[data-testid="usage-analytics-chart"]')).toBeVisible({ timeout: 3000 });
  });
});

// Test Group: Accessibility Compliance
test.describe('Enhanced Accessibility (WCAG 2.1 AA)', () => {
  test('should have proper heading hierarchy on all pages', async ({ page }) => {
    const pages = [
      { url: '/dashboard', h1: 'dashboard-title' },
      { url: '/dashboard/shops', h1: 'shops-title' },
      { url: '/dashboard/customers', h1: 'customers-title' },
      { url: '/dashboard/api-keys', h1: 'api-keys-title' },
      { url: '/terms', h1: 'terms-title' },
      { url: '/privacy', h1: 'privacy-title' },
      { url: '/pricing', h1: 'pricing-title' }
    ];

    for (const pageInfo of pages) {
      await page.goto(pageInfo.url);
      
      // Check for single H1 element
      const h1Elements = page.locator('h1');
      await expect(h1Elements).toHaveCount(1);
      
      // Verify H1 has proper test ID and content
      const mainHeading = page.locator(`[data-testid="${pageInfo.h1}"]`);
      await expect(mainHeading).toBeVisible();
      
      // Check for logical heading hierarchy (no heading level gaps)
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      const headingLevels = await Promise.all(
        headings.map(async (heading) => {
          const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
          return parseInt(tagName.charAt(1));
        })
      );
      
      // Verify no gaps in heading hierarchy
      for (let i = 1; i < headingLevels.length; i++) {
        const currentLevel = headingLevels[i];
        const previousLevel = headingLevels[i - 1];
        const gap = currentLevel - previousLevel;
        
        expect(gap, `Heading hierarchy gap on ${pageInfo.url}`).toBeLessThanOrEqual(1);
      }
    }
  });

  test('should have proper ARIA labels and roles', async ({ page }) => {
    await page.goto('/dashboard/shops');
    
    // Check main navigation has proper ARIA
    const navigation = page.locator('[data-testid="dashboard-navigation"]');
    await expect(navigation).toHaveAttribute('role', 'navigation');
    await expect(navigation).toHaveAttribute('aria-label', /navigation|menu/i);
    
    // Check form elements have proper labels
    await page.goto('/dashboard/shops/create');
    
    const nameInput = page.locator('[data-testid="shops-create-name-input"]');
    const nameLabel = page.locator('[data-testid="shops-create-name-label"]');
    
    // Verify form association
    const labelFor = await nameLabel.getAttribute('for');
    const inputId = await nameInput.getAttribute('id');
    expect(labelFor).toBe(inputId);
    
    // Check buttons have accessible names
    const submitButton = page.locator('[data-testid="shops-create-submit-button"]');
    const buttonText = await submitButton.textContent();
    expect(buttonText?.trim()).toBeTruthy();
    
    // Check that loading states have proper ARIA
    await page.goto('/dashboard/customers');
    
    // Mock loading state
    await page.route('**/api/v1/customers', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    
    const loadingElement = page.locator('[data-testid="customers-loading-state"]');
    if (await loadingElement.isVisible()) {
      await expect(loadingElement).toHaveAttribute('aria-live', 'polite');
      await expect(loadingElement).toHaveAttribute('aria-busy', 'true');
    }
  });

  test('should have sufficient color contrast', async ({ page }) => {
    // Test high contrast scenarios
    await page.goto('/dashboard');
    
    // Check that important text elements have sufficient contrast
    const textElements = [
      '[data-testid="dashboard-title"]',
      '[data-testid="dashboard-total-credits-value"]',
      '[data-testid="dashboard-shops-count-value"]'
    ];

    for (const selector of textElements) {
      const element = page.locator(selector);
      await expect(element).toBeVisible();
      
      // Get computed styles
      const styles = await element.evaluate((el) => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize
        };
      });
      
      // Basic checks for color values (would need more sophisticated contrast checking)
      expect(styles.color).not.toBe('transparent');
      expect(styles.color).not.toBe(styles.backgroundColor);
    }
  });

  test('should support screen reader navigation', async ({ page }) => {
    await page.goto('/dashboard/shops');
    
    // Check for landmark regions
    const main = page.locator('main');
    await expect(main).toBeVisible();
    
    const navigation = page.locator('[role="navigation"]');
    await expect(navigation).toBeVisible();
    
    // Check for skip links (if implemented)
    const skipLink = page.locator('[data-testid="skip-to-main"]');
    if (await skipLink.isVisible()) {
      await expect(skipLink).toHaveAttribute('href', '#main');
    }
    
    // Check that interactive elements are focusable
    const interactiveElements = page.locator('button, input, select, textarea, a[href]');
    const elements = await interactiveElements.all();
    
    for (const element of elements.slice(0, 5)) { // Check first 5 for performance
      const tabIndex = await element.getAttribute('tabindex');
      if (tabIndex === '-1') {
        // Element is programmatically focusable but not in tab order
        continue;
      }
      
      await element.focus();
      const isFocused = await element.evaluate(el => document.activeElement === el);
      expect(isFocused, 'Interactive element should be focusable').toBe(true);
    }
  });
});

// Test Group: Keyboard Navigation
test.describe('Enhanced Keyboard Navigation', () => {
  test('should support full keyboard navigation on all forms', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Tab through all form elements
    const formElements = [
      '[data-testid="shops-create-name-input"]',
      '[data-testid="shops-create-description-textarea"]',
      '[data-testid="shops-create-cancel-button"]',
      '[data-testid="shops-create-submit-button"]'
    ];

    for (let i = 0; i < formElements.length; i++) {
      if (i === 0) {
        await page.locator(formElements[i]).focus();
      } else {
        await page.keyboard.press('Tab');
      }
      
      const currentElement = page.locator(formElements[i]);
      const isFocused = await currentElement.evaluate(el => document.activeElement === el);
      expect(isFocused, `Element ${formElements[i]} should be focused`).toBe(true);
    }
    
    // Test Enter key submission
    await page.locator('[data-testid="shops-create-name-input"]').fill('Keyboard Test Shop');
    await page.locator('[data-testid="shops-create-submit-button"]').focus();
    await page.keyboard.press('Enter');
    
    // Should attempt form submission (would need proper form handling to test fully)
  });

  test('should support keyboard navigation in data tables', async ({ page }) => {
    // Mock table data
    await page.route('**/api/v1/customers', async (route) => {
      await route.fulfill({
        json: [
          { id: 'kb-customer-1', email: '<EMAIL>', name: 'Keyboard Test 1' },
          { id: 'kb-customer-2', email: '<EMAIL>', name: 'Keyboard Test 2' },
          { id: 'kb-customer-3', email: '<EMAIL>', name: 'Keyboard Test 3' }
        ]
      });
    });

    await page.goto('/dashboard/customers');
    
    // Wait for table to load
    await expect(page.locator('[data-testid="customers-table"]')).toBeVisible();
    
    // Test navigation through table actions
    const firstRowActions = page.locator('[data-testid="customers-table-actions-kb-customer-1"]');
    const viewButton = page.locator('[data-testid="customers-table-view-button-kb-customer-1"]');
    const editButton = page.locator('[data-testid="customers-table-edit-button-kb-customer-1"]');
    
    await viewButton.focus();
    await page.keyboard.press('Tab');
    
    const editIsFocused = await editButton.evaluate(el => document.activeElement === el);
    expect(editIsFocused, 'Should navigate between table action buttons').toBe(true);
    
    // Test Enter key activation
    await editButton.press('Enter');
    // Should navigate to edit page (would need routing to test fully)
  });

  test('should support modal keyboard navigation and escape handling', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Open create modal
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await expect(page.locator('[data-testid="api-keys-create-modal"]')).toBeVisible();
    
    // Test focus trapping in modal
    const modalElements = [
      '[data-testid="api-keys-create-name-input"]',
      '[data-testid="api-keys-create-description-input"]',
      '[data-testid="api-keys-create-cancel-button"]',
      '[data-testid="api-keys-create-submit-button"]'
    ];

    // Focus should start on first element
    const firstElement = page.locator(modalElements[0]);
    await expect(firstElement).toBeFocused();
    
    // Tab to last element
    for (let i = 1; i < modalElements.length; i++) {
      await page.keyboard.press('Tab');
    }
    
    // Tab again should wrap to first element (focus trapping)
    await page.keyboard.press('Tab');
    await expect(firstElement).toBeFocused();
    
    // Test Escape key closes modal
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="api-keys-create-modal"]')).not.toBeVisible();
  });

  test('should support keyboard shortcuts for common actions', async ({ page }) => {
    await page.goto('/dashboard/shops');
    
    // Test potential keyboard shortcuts (if implemented)
    // Ctrl/Cmd + N for new shop
    const isMac = process.platform === 'darwin';
    const newShortcut = isMac ? 'Meta+KeyN' : 'Control+KeyN';
    
    await page.keyboard.press(newShortcut);
    
    // Should navigate to create shop page or open modal
    // (This depends on actual keyboard shortcut implementation)
    
    // Test search focus shortcut (Ctrl/Cmd + F or /)
    await page.goto('/dashboard/customers');
    await page.keyboard.press('/');
    
    // Should focus search input
    const searchInput = page.locator('[data-testid="customers-search-input"]');
    if (await searchInput.isVisible()) {
      const isFocused = await searchInput.evaluate(el => document.activeElement === el);
      expect(isFocused, 'Search shortcut should focus search input').toBe(true);
    }
  });
});

// Test Group: Mobile Performance & Touch Interactions
test.describe('Enhanced Mobile Performance', () => {
  test('should perform well on mobile viewports', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    const mobilePages = [
      { url: '/dashboard/customer/scan', container: 'customer-scan-container' },
      { url: '/dashboard/customer/shops', container: 'customer-shops-container' },
      { url: '/dashboard/customer/redeem', container: 'customer-redeem-container' },
      { url: '/dashboard', container: 'dashboard-container' }
    ];

    for (const pageInfo of mobilePages) {
      const startTime = Date.now();
      await page.goto(pageInfo.url);
      
      await expect(page.locator(`[data-testid="${pageInfo.container}"]`)).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      expect(loadTime, `Mobile ${pageInfo.url} load time`).toBeLessThan(4000); // Slightly higher for mobile
      
      // Test touch-friendly targets
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const boundingBox = await button.boundingBox();
        
        if (boundingBox) {
          // WCAG recommends minimum 44x44 pixels for touch targets
          expect(boundingBox.width, 'Button width for touch').toBeGreaterThanOrEqual(44);
          expect(boundingBox.height, 'Button height for touch').toBeGreaterThanOrEqual(44);
        }
      }
    }
  });

  test('should handle touch gestures on mobile interfaces', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard/customer/shops');
    
    // Mock shops data
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({
        json: Array.from({ length: 20 }, (_, i) => ({
          id: `mobile-shop-${i}`,
          name: `Mobile Shop ${i}`,
          credit_balance: i * 10
        }))
      });
    });

    await page.reload();
    
    // Test scroll performance on mobile
    const scrollContainer = page.locator('[data-testid="customer-shops-shops-list"]');
    await expect(scrollContainer).toBeVisible();
    
    const startY = 300;
    const endY = 100;
    
    // Simulate swipe/scroll gesture
    await page.touchscreen.tap(200, startY);
    await page.touchscreen.tap(200, endY);
    
    // Should maintain smooth scrolling
    await page.waitForTimeout(100);
    
    // Test that elements remain interactive after scroll
    const firstShop = page.locator('[data-testid^="customer-shops-shop-card-"]').first();
    await expect(firstShop).toBeVisible();
  });

  test('should optimize mobile navigation patterns', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard/customer/scan');
    
    // Test mobile footer navigation
    await expect(page.locator('[data-testid="customer-scan-footer"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-navigation-bar"]')).toBeVisible();
    
    // Test navigation button size and spacing
    const navButtons = page.locator('[data-testid="customer-scan-footer"] button');
    const buttonCount = await navButtons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = navButtons.nth(i);
      const boundingBox = await button.boundingBox();
      
      if (boundingBox) {
        expect(boundingBox.height, 'Nav button height').toBeGreaterThanOrEqual(48);
      }
    }
    
    // Test swipe navigation between customer pages
    await page.goto('/dashboard/customer/shops');
    await expect(page.locator('[data-testid="customer-shops-container"]')).toBeVisible();
    
    // Navigate via footer
    await page.locator('[data-testid="customer-shops-home-link"]').click();
    await expect(page).toHaveURL(/.*\/customer$/);
  });
});

// Test Group: Progressive Enhancement & Offline Capabilities
test.describe('Enhanced Progressive Enhancement', () => {
  test('should work with JavaScript disabled for basic functionality', async ({ page, browser }) => {
    // Create new context with JavaScript disabled
    const context = await browser.newContext({ javaScriptEnabled: false });
    const pageNoJS = await context.newPage();
    
    await pageNoJS.goto('/terms');
    
    // Static content should still be accessible
    await expect(pageNoJS.locator('[data-testid="terms-container"]')).toBeVisible();
    await expect(pageNoJS.locator('[data-testid="terms-title"]')).toHaveText('Terms of Service');
    await expect(pageNoJS.locator('[data-testid="terms-content-sections"]')).toBeVisible();
    
    // Navigation should work with basic links
    await pageNoJS.goto('/privacy');
    await expect(pageNoJS.locator('[data-testid="privacy-container"]')).toBeVisible();
    
    await pageNoJS.goto('/pricing');
    await expect(pageNoJS.locator('[data-testid="pricing-container"]')).toBeVisible();
    
    // Close the no-JS context
    await context.close();
  });

  test('should handle slow network conditions gracefully', async ({ page }) => {
    // Simulate slow 3G network
    await page.route('**/*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });

    const startTime = Date.now();
    await page.goto('/dashboard');
    
    // Should show loading states appropriately
    const loadingElement = page.locator('[data-testid="dashboard-loading-state"]');
    
    if (await loadingElement.isVisible()) {
      await expect(loadingElement).toBeVisible();
      await expect(loadingElement).toContainText(/loading|wait/i);
    }
    
    // Eventually content should load
    await expect(page.locator('[data-testid="dashboard-container"]')).toBeVisible({ timeout: 10000 });
    
    const totalTime = Date.now() - startTime;
    expect(totalTime, 'Slow network load time').toBeLessThan(10000);
  });

  test('should provide meaningful error messages for failed requests', async ({ page }) => {
    await page.goto('/dashboard/shops');
    
    // Mock network failure
    await page.route('**/api/v1/shops', async (route) => {
      await route.abort('failed');
    });

    await page.reload();
    
    // Should show user-friendly error message
    const errorElement = page.locator('[data-testid="shops-network-error"]');
    await expect(errorElement).toBeVisible();
    await expect(errorElement).toContainText(/error|failed|try again/i);
    
    // Should provide retry option
    const retryButton = page.locator('[data-testid="shops-retry-button"]');
    await expect(retryButton).toBeVisible();
    await expect(retryButton).toBeEnabled();
  });

  test('should maintain functionality across different browser capabilities', async ({ page }) => {
    // Test without modern JavaScript features (mock older browser)
    await page.addInitScript(() => {
      // Simulate older browser by removing modern features
      delete window.fetch;
      delete window.Promise;
    });

    await page.goto('/');
    
    // Landing page should still render basic content
    await expect(page.locator('[data-testid="hero-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
    
    // Links should work for basic navigation
    await page.goto('/pricing');
    await expect(page.locator('[data-testid="pricing-container"]')).toBeVisible();
  });
});