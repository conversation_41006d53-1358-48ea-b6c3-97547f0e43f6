/**
 * Quick Test ID Validation
 * 
 * Simple tests to verify our test IDs are properly implemented without complex interactions
 */

import { test, expect } from '@playwright/test';

// Import our test ID constants
const CREDIT_DISPLAY_TEST_IDS = {
  CONTAINER: 'credit-display-container',
  CREDITS_BADGE: 'credit-display-credits-badge',
  CALCULATOR_BUTTON: 'credit-display-calculator-button',
  LOADING_SPINNER: 'credit-display-loading-spinner',
} as const;

const COOKIE_BANNER_TEST_IDS = {
  CONTAINER: 'cookie-banner-container',
  BANNER: 'cookie-banner-banner',
  ACCEPT_ALL_BUTTON: 'cookie-banner-accept-all-button',
  PREFERENCES_BUTTON: 'cookie-banner-preferences-button',
} as const;

const REAL_USAGE_DETAILS_TEST_IDS = {
  CONTAINER: 'real-usage-details-container',
  ANALYTICS_CARD: 'real-usage-details-analytics-card',
  TABS_CONTAINER: 'real-usage-details-tabs-container',
  PERIOD_BUTTON_DAY: 'real-usage-details-period-button-day',
} as const;

const HTML5_QR_CODE_PLUGIN_TEST_IDS = {
  CONTAINER: 'html5-qr-code-plugin-container',
  SCANNER_REGION: 'html5-qr-code-plugin-scanner-region',
  ERROR_CONTAINER: 'html5-qr-code-plugin-error-container',
  TRY_AGAIN_BUTTON: 'html5-qr-code-plugin-try-again-button',
} as const;

test.describe('Quick Test ID Validation', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up context to avoid localStorage issues
    await page.goto('/', { waitUntil: 'networkidle' });
  });

  test('should validate test IDs are present on landing page', async ({ page }) => {
    // Check for any test IDs on the landing page
    const testIdElements = await page.locator('[data-testid]').count();
    
    console.log(`🎯 Found ${testIdElements} elements with test IDs on landing page`);
    expect(testIdElements).toBeGreaterThan(0);
    
    // Check for specific landing page test IDs
    const landingTestIds = [
      'landing-hero-section',
      'landing-pricing-section',
      'landing-features-section',
      'cookie-banner-container'
    ];
    
    let foundTestIds = 0;
    for (const testId of landingTestIds) {
      const element = page.locator(`[data-testid="${testId}"]`);
      if (await element.count() > 0) {
        foundTestIds++;
        console.log(`✅ Found test ID: ${testId}`);
      }
    }
    
    console.log(`📊 Found ${foundTestIds}/${landingTestIds.length} expected landing page test IDs`);
  });

  test('should validate cookie banner test IDs are implemented', async ({ page }) => {
    // Look for cookie banner
    const cookieBanner = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
    
    if (await cookieBanner.count() > 0) {
      console.log('🍪 Cookie banner found with test ID');
      await expect(cookieBanner).toBeVisible();
      
      // Check for accept button
      const acceptButton = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`);
      if (await acceptButton.count() > 0) {
        console.log('✅ Accept All button test ID found');
        await expect(acceptButton).toBeVisible();
      }
    } else {
      console.log('ℹ️ Cookie banner not currently visible (may be already accepted)');
    }
  });

  test('should validate dashboard test IDs when accessible', async ({ page }) => {
    // Try to access dashboard
    await page.goto('/dashboard');
    
    // Check if we can access dashboard or if authentication is required
    if (page.url().includes('/auth') || page.url().includes('/signin')) {
      console.log('🔐 Dashboard requires authentication - skipping dashboard-specific tests');
      test.skip();
      return;
    }
    
    // Look for dashboard test IDs
    const dashboardTestIds = await page.locator('[data-testid*="dashboard"]').count();
    console.log(`🏁 Found ${dashboardTestIds} dashboard-related test IDs`);
    
    // Look for any of our implemented test IDs
    const searchTestIds = [
      'real-usage-details-container',
      'dashboard-analytics-container',
      'credit-display-container'
    ];
    
    let foundDashboardTestIds = 0;
    for (const testId of searchTestIds) {
      const element = page.locator(`[data-testid="${testId}"]`);
      if (await element.count() > 0) {
        foundDashboardTestIds++;
        console.log(`✅ Found dashboard test ID: ${testId}`);
      }
    }
    
    console.log(`📈 Found ${foundDashboardTestIds}/${searchTestIds.length} expected dashboard test IDs`);
  });

  test('should validate QR scanner test IDs when accessible', async ({ page }) => {
    // Try to access QR scanner pages
    const qrPages = ['/customer/scan', '/redeem', '/scan'];
    
    let qrTestIdsFound = 0;
    
    for (const qrPage of qrPages) {
      await page.goto(qrPage);
      
      // Skip if authentication required
      if (page.url().includes('/auth') || page.url().includes('/signin')) {
        console.log(`🔐 ${qrPage} requires authentication - skipping`);
        continue;
      }
      
      // Look for QR scanner test IDs
      const qrContainer = page.locator(`[data-testid="${HTML5_QR_CODE_PLUGIN_TEST_IDS.CONTAINER}"]`);
      const qrScanner = page.locator(`[data-testid="${HTML5_QR_CODE_PLUGIN_TEST_IDS.SCANNER_REGION}"]`);
      
      if (await qrContainer.count() > 0) {
        qrTestIdsFound++;
        console.log(`✅ Found QR scanner container test ID on ${qrPage}`);
      }
      
      if (await qrScanner.count() > 0) {
        qrTestIdsFound++;
        console.log(`✅ Found QR scanner region test ID on ${qrPage}`);
      }
    }
    
    console.log(`📱 Found ${qrTestIdsFound} QR scanner test IDs across accessible pages`);
  });

  test('should validate comprehensive test ID coverage', async ({ page }) => {
    // Navigate through different pages and count test IDs
    const pagesToTest = [
      { path: '/', name: 'Landing' },
      { path: '/dashboard', name: 'Dashboard' },
      { path: '/redeem', name: 'Redeem' },
      { path: '/terms', name: 'Terms' },
      { path: '/cookies', name: 'Cookies' }
    ];
    
    let totalTestIds = 0;
    let accessiblePages = 0;
    
    for (const pageInfo of pagesToTest) {
      await page.goto(pageInfo.path);
      
      // Skip if authentication required
      if (page.url().includes('/auth') || page.url().includes('/signin')) {
        console.log(`🔐 ${pageInfo.name} requires authentication - skipping`);
        continue;
      }
      
      accessiblePages++;
      const testIdsOnPage = await page.locator('[data-testid]').count();
      totalTestIds += testIdsOnPage;
      
      console.log(`📄 ${pageInfo.name} page: ${testIdsOnPage} test IDs`);
      
      // Look for our specific test ID patterns
      const ourTestIds = await page.locator('[data-testid*="credit-display"], [data-testid*="cookie-banner"], [data-testid*="real-usage"], [data-testid*="html5-qr"], [data-testid*="dashboard-analytics"]').count();
      
      if (ourTestIds > 0) {
        console.log(`  ✅ Found ${ourTestIds} of our implemented test IDs`);
      }
    }
    
    console.log(`\n📊 Test ID Implementation Summary:`);
    console.log(`   Accessible pages tested: ${accessiblePages}`);
    console.log(`   Total test IDs found: ${totalTestIds}`);
    console.log(`   Average test IDs per page: ${Math.round(totalTestIds / Math.max(accessiblePages, 1))}`);
    
    expect(totalTestIds).toBeGreaterThan(0);
    expect(accessiblePages).toBeGreaterThan(0);
  });

  test('should validate test ID naming conventions', async ({ page }) => {
    // Get all test IDs from the current page
    const allTestIds = await page.locator('[data-testid]').evaluateAll(elements => 
      elements.map(el => el.getAttribute('data-testid')).filter(Boolean)
    );
    
    console.log(`🎯 Analyzing ${allTestIds.length} test IDs for naming conventions`);
    
    // Check naming convention compliance
    const invalidTestIds = allTestIds.filter(id => {
      return (
        id!.includes('_') || // Should use kebab-case, not snake_case
        id!.includes(' ') || // No spaces
        /[A-Z]/.test(id!) || // No uppercase letters
        id!.startsWith('-') || id!.endsWith('-') || // No leading/trailing dashes
        id!.includes('--') // No double dashes
      );
    });
    
    if (invalidTestIds.length === 0) {
      console.log('✅ All test IDs follow proper kebab-case naming convention');
    } else {
      console.log(`⚠️ Found ${invalidTestIds.length} test IDs with invalid naming:`);
      invalidTestIds.slice(0, 5).forEach(id => console.log(`  - ${id}`));
    }
    
    // Check for our implemented test ID patterns
    const ourPatterns = [
      'credit-display-',
      'cookie-banner-', 
      'real-usage-details-',
      'html5-qr-code-plugin-',
      'dashboard-analytics-'
    ];
    
    let foundPatterns = 0;
    for (const pattern of ourPatterns) {
      const matchingIds = allTestIds.filter(id => id!.includes(pattern));
      if (matchingIds.length > 0) {
        foundPatterns++;
        console.log(`✅ Found ${matchingIds.length} test IDs with pattern: ${pattern}`);
      }
    }
    
    console.log(`📈 Found ${foundPatterns}/${ourPatterns.length} of our implemented test ID patterns`);
    
    // Test should pass if we have good naming convention compliance
    const complianceRate = (allTestIds.length - invalidTestIds.length) / allTestIds.length;
    expect(complianceRate).toBeGreaterThan(0.9); // 90% compliance rate
  });
});