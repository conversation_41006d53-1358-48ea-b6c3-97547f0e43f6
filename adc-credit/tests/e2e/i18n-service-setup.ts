/**
 * Multi-Languages Service Integration Setup
 * ADC Credit Service - E2E Testing Service Setup
 * 
 * This setup script starts both services for real integration testing:
 * - Multi-Languages service (port 8300)
 * - Credit Service frontend (port 3800)
 * 
 * Used for comprehensive i18n E2E testing with real service-to-service communication
 */

import { exec, spawn, ChildProcess } from 'child_process';
import { promisify } from 'util';
import fetch from 'node-fetch';

const execAsync = promisify(exec);

export class I18nServiceSetup {
  private multiLangProcess: ChildProcess | null = null;
  private creditServiceProcess: ChildProcess | null = null;

  /**
   * Setup both services for i18n integration testing
   */
  async setupServices(): Promise<void> {
    console.log('🔧 Setting up Multi-Languages and Credit Service integration...');
    
    try {
      // Start Multi-Languages service first
      await this.startMultiLanguagesService();
      
      // Wait for Multi-Languages service to be ready
      await this.waitForService('http://localhost:8300/health', 'Multi-Languages Service');
      
      // Credit Service should already be running for E2E tests
      // But verify it's accessible
      await this.waitForService('http://localhost:3800', 'Credit Service Frontend');
      
      // Verify services can communicate
      await this.verifyServiceCommunication();
      
      console.log('✅ Both services are ready for i18n integration testing');
      
    } catch (error) {
      console.error('❌ Failed to setup services:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Start the Multi-Languages service backend
   */
  private async startMultiLanguagesService(): Promise<void> {
    console.log('📡 Starting Multi-Languages service...');
    
    const multiLangPath = '/Users/<USER>/Desktop/adc-platform/services/core/adc-muti-languages';
    
    // Check if directory exists
    try {
      await execAsync(`ls "${multiLangPath}"`);
    } catch (error) {
      throw new Error(`Multi-Languages service directory not found: ${multiLangPath}`);
    }
    
    // Start the backend service
    this.multiLangProcess = spawn('make', ['backend'], {
      cwd: multiLangPath,
      stdio: ['ignore', 'pipe', 'pipe'],
      env: { ...process.env }
    });
    
    // Handle process output
    this.multiLangProcess.stdout?.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Server starting') || output.includes('Listening')) {
        console.log('📡 Multi-Languages service:', output.trim());
      }
    });
    
    this.multiLangProcess.stderr?.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('warning') && !error.includes('info')) {
        console.error('📡 Multi-Languages service error:', error.trim());
      }
    });
    
    this.multiLangProcess.on('error', (error) => {
      console.error('❌ Failed to start Multi-Languages service:', error);
    });
    
    // Give it time to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  /**
   * Wait for a service to be ready
   */
  private async waitForService(url: string, serviceName: string, maxRetries: number = 30): Promise<void> {
    console.log(`⏳ Waiting for ${serviceName} at ${url}...`);
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await fetch(url, { 
          timeout: 5000,
          headers: {
            'User-Agent': 'E2E-Test-Setup'
          }
        });
        
        if (response.status < 500) {
          console.log(`✅ ${serviceName} is ready`);
          return;
        }
      } catch (error) {
        // Service not ready yet, continue waiting
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`${serviceName} did not become ready within ${maxRetries} seconds`);
  }

  /**
   * Verify that services can communicate with each other
   */
  private async verifyServiceCommunication(): Promise<void> {
    console.log('🔗 Verifying service-to-service communication...');
    
    try {
      // Test Multi-Languages service health endpoint
      const healthResponse = await fetch('http://localhost:8300/health', {
        timeout: 5000
      });
      
      if (!healthResponse.ok) {
        throw new Error(`Multi-Languages service health check failed: ${healthResponse.status}`);
      }
      
      // Test translation API endpoint (this is what Credit Service will call)
      const translationResponse = await fetch('http://localhost:8300/api/v2/internal/translations/fetch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Key': 'adc-internal-2024'
        },
        body: JSON.stringify({
          project_id: 'adc-credit-project',
          namespace: 'ui',
          key: 'test',
          locale: 'en'
        }),
        timeout: 5000
      });
      
      // Should get a response (even if no translation exists)
      if (translationResponse.status === 404 || translationResponse.status === 200) {
        console.log('✅ Translation API endpoint is accessible');
      } else if (translationResponse.status === 401) {
        console.log('⚠️ Translation API authentication issue (expected during setup)');
      } else {
        console.log(`ℹ️ Translation API responded with status: ${translationResponse.status}`);
      }
      
      console.log('✅ Service communication verified');
      
    } catch (error) {
      console.warn('⚠️ Service communication verification failed (may be normal during startup):', error.message);
      // Don't throw here as this might be normal during initial setup
    }
  }

  /**
   * Cleanup services
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up i18n test services...');
    
    if (this.multiLangProcess) {
      console.log('📡 Stopping Multi-Languages service...');
      this.multiLangProcess.kill('SIGTERM');
      
      // Give it time to clean shutdown
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (this.multiLangProcess.exitCode === null) {
        this.multiLangProcess.kill('SIGKILL');
      }
      
      this.multiLangProcess = null;
    }
    
    console.log('✅ Service cleanup completed');
  }

  /**
   * Check if services are running
   */
  async checkServices(): Promise<{ multiLang: boolean; creditService: boolean }> {
    const checks = await Promise.allSettled([
      fetch('http://localhost:8300/health', { timeout: 2000 }),
      fetch('http://localhost:3800', { timeout: 2000 })
    ]);
    
    return {
      multiLang: checks[0].status === 'fulfilled',
      creditService: checks[1].status === 'fulfilled'
    };
  }
}

// Helper function for E2E tests
export async function setupI18nServices(): Promise<I18nServiceSetup> {
  const setup = new I18nServiceSetup();
  await setup.setupServices();
  return setup;
}

// Helper function to cleanup after tests
export async function cleanupI18nServices(setup: I18nServiceSetup): Promise<void> {
  if (setup) {
    await setup.cleanup();
  }
}