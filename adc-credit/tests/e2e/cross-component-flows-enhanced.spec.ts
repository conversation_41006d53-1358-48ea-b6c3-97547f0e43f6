/**
 * ADC Credit Service - Cross-Component Flow Tests with Test IDs
 * 
 * End-to-end workflows that span multiple components using stable test IDs
 * Focus: Complete user journeys from landing to business operations
 */

import { test, expect } from '@playwright/test';

// Test ID constants for cross-component flows
const LANDING_TEST_IDS = {
  PRICING: {
    SECTION: 'landing-pricing-section',
    PLAN_CARD: (planId: string | number) => `landing-pricing-plan-card-${planId}`,
    PLAN_CTA_BUTTON: (planId: string | number) => `landing-pricing-plan-cta-button-${planId}`,
    CONFIRM_DIALOG: 'landing-pricing-confirm-dialog',
    CONFIRM_DIALOG_CONFIRM_BUTTON: 'landing-pricing-confirm-dialog-confirm-button',
  },
  INTEGRATION: {
    SECTION: 'landing-integration-section',
    COPY_BUTTON: 'landing-integration-copy-button',
    CODE_BLOCK: 'landing-integration-code-block',
  },
  TESTIMONIALS: {
    SECTION: 'landing-testimonials-section',
    CIRCULAR_TESTIMONIALS: 'landing-testimonials-circular',
  },
} as const;

const HERO_SECTION_TEST_IDS = {
  CONTAINER: 'hero-section-container',
  GET_STARTED_BUTTON: 'hero-section-get-started-button',
  LEARN_MORE_BUTTON: 'hero-section-learn-more-button',
} as const;

const COOKIE_BANNER_TEST_IDS = {
  CONTAINER: 'cookie-banner-container',
  ACCEPT_ALL_BUTTON: 'cookie-banner-accept-all-button',
  ESSENTIAL_ONLY_BUTTON: 'cookie-banner-essential-only-button',
  CUSTOMIZE_BUTTON: 'cookie-banner-customize-button',
} as const;

const CREDIT_DISPLAY_TEST_IDS = {
  CONTAINER: 'credit-display-container',
  CALCULATOR_BUTTON: 'credit-display-calculator-button',
  CONVERSION_CONTAINER: 'credit-display-conversion-container',
  CURRENCY_TRIGGER: 'credit-display-currency-trigger',
  CURRENCY_ITEM: (currency: string) => `credit-display-currency-item-${currency}`,
  CONVERSION_RESULT: 'credit-display-conversion-result',
} as const;

const CUSTOMER_CREDITS_DISPLAY_TEST_IDS = {
  CONTAINER: 'customer-credits-display-container',
  BALANCE_AMOUNT: 'customer-credits-display-balance-amount',
  REFRESH_BUTTON: 'customer-credits-display-refresh-button',
  TRANSACTIONS_LIST: 'customer-credits-display-transactions-list',
  TRANSACTION_ITEM: (id: string) => `customer-credits-display-transaction-item-${id}`,
} as const;

test.describe('Cross-Component Flow Tests - Enhanced with Test IDs', () => {
  
  test.describe('Landing Page to Dashboard Journey', () => {
    test('should complete full user journey from landing page to dashboard', async ({ page }) => {
      // Start at landing page
      await page.goto('/');
      
      // Handle cookie banner using test IDs
      const cookieBanner = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
      if (await cookieBanner.isVisible({ timeout: 3000 })) {
        console.log('🍪 Cookie banner detected - accepting all cookies');
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
        await expect(cookieBanner).not.toBeVisible({ timeout: 3000 });
      }
      
      // Validate hero section test IDs
      const heroContainer = page.locator(`[data-testid="${HERO_SECTION_TEST_IDS.CONTAINER}"]`);
      if (await heroContainer.count() > 0) {
        await expect(heroContainer).toBeVisible();
        await expect(page.locator(`[data-testid="${HERO_SECTION_TEST_IDS.GET_STARTED_BUTTON}"]`)).toBeVisible();
        await expect(page.locator(`[data-testid="${HERO_SECTION_TEST_IDS.LEARN_MORE_BUTTON}"]`)).toBeVisible();
        console.log('✅ Hero section test IDs validated');
      }
      
      // Validate pricing section test IDs
      const pricingSection = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.SECTION}"]`);
      if (await pricingSection.count() > 0) {
        await expect(pricingSection).toBeVisible();
        
        // Check for pricing plan cards
        const basicPlanCard = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.PLAN_CARD('1')}"]`);
        const proPlanCard = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.PLAN_CARD('2')}"]`);
        const enterprisePlanCard = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.PLAN_CARD('3')}"]`);
        
        if (await basicPlanCard.count() > 0) await expect(basicPlanCard).toBeVisible();
        if (await proPlanCard.count() > 0) await expect(proPlanCard).toBeVisible();
        if (await enterprisePlanCard.count() > 0) await expect(enterprisePlanCard).toBeVisible();
        
        // Test pricing plan CTA interaction
        const proPlanCTA = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.PLAN_CTA_BUTTON('2')}"]`);
        if (await proPlanCTA.count() > 0) {
          await proPlanCTA.click();
          
          // Check if confirmation dialog appears
          const confirmDialog = page.locator(`[data-testid="${LANDING_TEST_IDS.PRICING.CONFIRM_DIALOG}"]`);
          if (await confirmDialog.isVisible({ timeout: 3000 })) {
            console.log('💰 Pricing confirmation dialog detected');
            
            // Close dialog for now (would redirect to auth in real scenario)
            await page.keyboard.press('Escape');
            await expect(confirmDialog).not.toBeVisible({ timeout: 2000 });
          }
        }
        
        console.log('✅ Pricing section test IDs validated');
      }
      
      // Validate integration section test IDs
      const integrationSection = page.locator(`[data-testid="${LANDING_TEST_IDS.INTEGRATION.SECTION}"]`);
      if (await integrationSection.count() > 0) {
        await expect(integrationSection).toBeVisible();
        
        // Test code copy functionality
        const copyButton = page.locator(`[data-testid="${LANDING_TEST_IDS.INTEGRATION.COPY_BUTTON}"]`);
        const codeBlock = page.locator(`[data-testid="${LANDING_TEST_IDS.INTEGRATION.CODE_BLOCK}"]`);
        
        if (await copyButton.count() > 0) {
          await expect(copyButton).toBeVisible();
          await copyButton.click();
          console.log('📋 Code copy button clicked');
        }
        
        if (await codeBlock.count() > 0) {
          await expect(codeBlock).toBeVisible();
        }
        
        console.log('✅ Integration section test IDs validated');
      }
      
      // Validate testimonials section test IDs
      const testimonialsSection = page.locator(`[data-testid="${LANDING_TEST_IDS.TESTIMONIALS.SECTION}"]`);
      if (await testimonialsSection.count() > 0) {
        await expect(testimonialsSection).toBeVisible();
        
        const circularTestimonials = page.locator(`[data-testid="${LANDING_TEST_IDS.TESTIMONIALS.CIRCULAR_TESTIMONIALS}"]`);
        if (await circularTestimonials.count() > 0) {
          await expect(circularTestimonials).toBeVisible();
        }
        
        console.log('✅ Testimonials section test IDs validated');
      }
      
      console.log('🎉 Complete landing page journey with test IDs validated');
    });
  });

  test.describe('Credit Management Cross-Component Flow', () => {
    test('should validate credit display and management across components', async ({ page }) => {
      // Navigate to a shop page that might contain credit components
      await page.goto('/dashboard/merchant/shop/test-shop');
      
      // Handle authentication redirects gracefully
      if (page.url().includes('/auth/signin')) {
        console.log('🔐 Authentication required - test limited to public components');
        return;
      }
      
      // Look for CreditDisplay components
      const creditDisplays = await page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONTAINER}"]`).all();
      
      if (creditDisplays.length > 0) {
        console.log(`💰 Found ${creditDisplays.length} CreditDisplay components`);
        
        for (let i = 0; i < Math.min(creditDisplays.length, 3); i++) {
          const display = creditDisplays[i];
          
          // Test calculator functionality if available
          const calculatorButton = display.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CALCULATOR_BUTTON}"]`);
          
          if (await calculatorButton.count() > 0) {
            console.log(`🧮 Testing calculator in CreditDisplay ${i + 1}`);
            
            await calculatorButton.click();
            
            // Wait for conversion container
            const conversionContainer = display.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONVERSION_CONTAINER}"]`);
            await expect(conversionContainer).toBeVisible({ timeout: 3000 });
            
            // Test currency selection
            const currencyTrigger = display.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_TRIGGER}"]`);
            await currencyTrigger.click();
            
            // Try selecting USD currency
            const usdItem = page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CURRENCY_ITEM('USD')}"]`);
            if (await usdItem.count() > 0) {
              await usdItem.click();
              
              // Check for conversion result
              const conversionResult = display.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONVERSION_RESULT}"]`);
              
              // Wait for either result or error
              try {
                await expect(conversionResult).toBeVisible({ timeout: 5000 });
                console.log('💱 Currency conversion result displayed');
              } catch {
                console.log('ℹ️ Currency conversion not available (no exchange rate)');
              }
            }
          }
        }
        
        console.log('✅ CreditDisplay components validated across page');
      } else {
        console.log('ℹ️ No CreditDisplay components found on this page');
      }
    });
  });

  test.describe('Customer Credit Journey Flow', () => {
    test('should validate complete customer credit viewing experience', async ({ page }) => {
      // Try customer credit page
      await page.goto('/customer/shops/test-shop');
      
      // Handle authentication redirects
      if (page.url().includes('/auth/signin') || page.url().includes('/customer/auth')) {
        console.log('🔐 Customer authentication required - simulating authenticated state');
        
        // For testing purposes, try to navigate to a customer demo page
        await page.goto('/');
        return;
      }
      
      // Look for CustomerCreditsDisplay component
      const creditsContainer = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.CONTAINER}"]`);
      
      if (await creditsContainer.count() > 0) {
        console.log('💳 CustomerCreditsDisplay component found');
        
        // Wait for loading to complete
        await expect(creditsContainer).toBeVisible({ timeout: 10000 });
        
        // Check balance display
        const balanceAmount = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.BALANCE_AMOUNT}"]`);
        if (await balanceAmount.count() > 0) {
          await expect(balanceAmount).toBeVisible();
          const balance = await balanceAmount.textContent();
          console.log(`💰 Current balance: ${balance}`);
        }
        
        // Test refresh functionality
        const refreshButton = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.REFRESH_BUTTON}"]`);
        if (await refreshButton.count() > 0) {
          console.log('🔄 Testing refresh functionality');
          
          await refreshButton.click();
          
          // Button should be disabled during refresh
          await expect(refreshButton).toBeDisabled({ timeout: 2000 });
          
          // Wait for refresh to complete
          await expect(refreshButton).toBeEnabled({ timeout: 10000 });
          
          console.log('✅ Refresh functionality validated');
        }
        
        // Check transactions list
        const transactionsList = page.locator(`[data-testid="${CUSTOMER_CREDITS_DISPLAY_TEST_IDS.TRANSACTIONS_LIST}"]`);
        if (await transactionsList.count() > 0) {
          console.log('📋 Transactions list found');
          
          // Get all transaction items
          const transactionItems = await page.locator('[data-testid*="customer-credits-display-transaction-item-"]').all();
          
          console.log(`📊 Found ${transactionItems.length} transaction items`);
          
          // Validate a few transaction items
          for (let i = 0; i < Math.min(transactionItems.length, 3); i++) {
            const item = transactionItems[i];
            const testId = await item.getAttribute('data-testid');
            
            if (testId) {
              const transactionId = testId.replace('customer-credits-display-transaction-item-', '');
              console.log(`💸 Validating transaction: ${transactionId}`);
              
              await expect(item).toBeVisible();
            }
          }
        }
        
        console.log('✅ Customer credit journey validated');
      } else {
        console.log('ℹ️ CustomerCreditsDisplay component not found - may require specific authentication');
      }
    });
  });

  test.describe('Multi-Component Integration Flow', () => {
    test('should validate multiple components working together', async ({ page }) => {
      // Start with landing page
      await page.goto('/');
      
      // Handle cookies
      const cookieBanner = page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.CONTAINER}"]`);
      if (await cookieBanner.isVisible({ timeout: 2000 })) {
        await page.locator(`[data-testid="${COOKIE_BANNER_TEST_IDS.ACCEPT_ALL_BUTTON}"]`).click();
        await expect(cookieBanner).not.toBeVisible({ timeout: 3000 });
      }
      
      // Navigate through different pages to test component persistence
      const testPages = [
        { path: '/privacy', name: 'Privacy Page' },
        { path: '/docs', name: 'Documentation Page' },
        { path: '/dashboard/merchant', name: 'Merchant Dashboard' },
      ];
      
      for (const testPage of testPages) {
        console.log(`🧭 Testing ${testPage.name}: ${testPage.path}`);
        
        await page.goto(testPage.path);
        
        // Handle authentication redirects
        if (page.url().includes('/auth/signin')) {
          console.log(`🔐 ${testPage.name} requires authentication - skipping detailed validation`);
          continue;
        }
        
        // Look for any test ID elements on the page
        const testIdElements = await page.locator('[data-testid]').count();
        console.log(`🎯 Found ${testIdElements} elements with test IDs on ${testPage.name}`);
        
        // Verify at least some test IDs exist
        expect(testIdElements).toBeGreaterThan(0);
        
        // Check for common component test IDs
        const creditDisplays = await page.locator(`[data-testid="${CREDIT_DISPLAY_TEST_IDS.CONTAINER}"]`).count();
        if (creditDisplays > 0) {
          console.log(`💰 Found ${creditDisplays} CreditDisplay components on ${testPage.name}`);
        }
        
        // Wait a moment between page navigations
        await page.waitForTimeout(500);
      }
      
      console.log('✅ Multi-component integration flow validated');
    });
  });

  test.describe('Test ID Performance Across Components', () => {
    test('should verify test IDs provide consistent performance', async ({ page }) => {
      const performanceResults = [];
      
      const testPages = [
        { path: '/', name: 'Landing Page' },
        { path: '/privacy', name: 'Privacy Page' },
        { path: '/docs', name: 'Documentation Page' },
      ];
      
      for (const testPage of testPages) {
        const startTime = Date.now();
        
        await page.goto(testPage.path);
        
        // Wait for first test ID element to appear
        await page.locator('[data-testid]').first().waitFor({ state: 'visible', timeout: 10000 });
        
        const loadTime = Date.now() - startTime;
        
        // Count all test ID elements
        const testIdCount = await page.locator('[data-testid]').count();
        
        performanceResults.push({
          page: testPage.name,
          loadTime,
          testIdCount,
        });
        
        console.log(`⚡ ${testPage.name}: ${loadTime}ms load time, ${testIdCount} test IDs`);
        
        // Verify reasonable load time
        expect(loadTime).toBeLessThan(10000);
        expect(testIdCount).toBeGreaterThan(0);
      }
      
      // Calculate average performance
      const avgLoadTime = performanceResults.reduce((sum, result) => sum + result.loadTime, 0) / performanceResults.length;
      const totalTestIds = performanceResults.reduce((sum, result) => sum + result.testIdCount, 0);
      
      console.log(`📊 Performance Summary:`);
      console.log(`   Average load time: ${avgLoadTime.toFixed(2)}ms`);
      console.log(`   Total test IDs across pages: ${totalTestIds}`);
      console.log(`   Test ID density: ${(totalTestIds / performanceResults.length).toFixed(1)} per page`);
      
      // Verify overall performance is acceptable
      expect(avgLoadTime).toBeLessThan(5000);
      expect(totalTestIds).toBeGreaterThan(50);
      
      console.log('✅ Test ID performance validated across components');
    });
  });

  test.describe('Error Handling with Test IDs', () => {
    test('should handle component errors gracefully using test IDs', async ({ page }) => {
      // Track JavaScript errors
      const jsErrors: string[] = [];
      page.on('pageerror', err => {
        jsErrors.push(err.message);
      });
      
      // Test error handling on various pages
      const testPages = [
        '/',
        '/privacy',
        '/dashboard/merchant/shop/non-existent-shop',
        '/customer/shops/non-existent-shop',
      ];
      
      for (const testPath of testPages) {
        console.log(`🔍 Testing error handling on: ${testPath}`);
        
        await page.goto(testPath);
        
        // Wait for page to stabilize
        await page.waitForTimeout(2000);
        
        // Look for error state test IDs
        const errorElements = await page.locator('[data-testid*="error"]').count();
        const loadingElements = await page.locator('[data-testid*="loading"]').count();
        
        if (errorElements > 0) {
          console.log(`❌ Found ${errorElements} error state elements with test IDs`);
        }
        
        if (loadingElements > 0) {
          console.log(`⏳ Found ${loadingElements} loading state elements with test IDs`);
        }
        
        // Verify page has some test ID elements even in error states
        const totalTestIds = await page.locator('[data-testid]').count();
        expect(totalTestIds).toBeGreaterThan(0);
      }
      
      if (jsErrors.length > 0) {
        console.log(`⚠️ JavaScript errors detected:`);
        jsErrors.forEach(error => console.log(`  - ${error}`));
      } else {
        console.log('✅ No JavaScript errors detected during error handling tests');
      }
      
      console.log('✅ Error handling with test IDs validated');
    });
  });
});