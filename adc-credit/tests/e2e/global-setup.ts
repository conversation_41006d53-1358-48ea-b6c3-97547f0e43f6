/**
 * Global Setup for ADC Credit E2E Tests
 * 
 * Configures test environment and dependencies before running tests
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🔧 Setting up ADC Credit E2E test environment...');

  // Check if the application is running
  try {
    const browser = await chromium.launch();
    const context = await browser.newContext({
      ignoreHTTPSErrors: true,
    });
    const page = await context.newPage();
    
    // Test if frontend is accessible
    console.log('📡 Checking frontend availability...');
    await page.goto('https://localhost:3400', { timeout: 30000 });
    console.log('✅ Frontend is accessible');
    
    // Test if backend API is accessible (optional for frontend-only tests)
    console.log('📡 Checking backend API availability...');
    try {
      const apiResponse = await page.request.get('http://localhost:8400/health');
      if (apiResponse.ok()) {
        console.log('✅ Backend API is accessible');
      } else {
        console.log('⚠️ Backend API health check failed, but continuing...');
      }
    } catch (backendError) {
      console.log('⚠️ Backend API not accessible, but continuing with frontend-only tests...');
    }
    
    await browser.close();
  } catch (error) {
    console.error('❌ Frontend not accessible:', error);
    console.log('💡 Make sure to run "npm run dev" or "make dev" before running tests');
    throw new Error('Frontend is not running. Please start the development server.');
  }

  // Set up test data if needed
  console.log('📝 Setting up test data...');
  
  // Create test users or seed data here if needed
  // This would typically involve API calls to create test accounts
  
  console.log('✅ Global setup completed successfully');
}

export default globalSetup;