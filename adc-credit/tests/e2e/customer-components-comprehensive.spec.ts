/**
 * Customer Components Comprehensive E2E Tests
 * ADC Credit Service - Customer-Facing Components Testing
 * 
 * Tests customer dashboard, shop interactions, credit redemption,
 * QR scanning, and customer authentication flows.
 * 
 * Test Categories:
 * - Customer Dashboard (credit display, shop listing)
 * - Customer Authentication (login form)
 * - Shop Profile Components (contact info, shop details)
 * - Recent Activity Components (transaction history)
 * - QR Code Components (scanning, redemption)
 */

import { test, expect } from '@playwright/test';

// Test Group: Customer Dashboard
test.describe('Customer Dashboard Components', () => {
  test.beforeEach(async ({ page }) => {
    // Mock customer authentication
    await page.goto('/dashboard/customer');
  });

  test('should render customer dashboard with test IDs', async ({ page }) => {
    // Main container
    await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
    
    // Header section
    await expect(page.locator('[data-testid="customer-dashboard-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-title"]')).toHaveText('My Credits');
    
    // Action buttons
    await expect(page.locator('[data-testid="customer-dashboard-scan-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-redeem-button"]')).toBeVisible();
  });

  test('should display credit and shop summary cards', async ({ page }) => {
    // Credit summary card
    await expect(page.locator('[data-testid="customer-dashboard-total-credits-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-total-credits-value"]')).toBeVisible();
    
    // Shop count card
    await expect(page.locator('[data-testid="customer-dashboard-shops-count-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-shops-count-value"]')).toBeVisible();
  });

  test('should render shops section with proper test IDs', async ({ page }) => {
    // Shops section
    await expect(page.locator('[data-testid="customer-dashboard-shops-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-shops-title"]')).toHaveText('My Shops');
    await expect(page.locator('[data-testid="customer-dashboard-redeem-code-button"]')).toBeVisible();
  });

  test('should handle loading state correctly', async ({ page }) => {
    // Mock loading state
    await page.route('**/api/customers/shops', async (route) => {
      // Delay response to show loading state
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    await expect(page.locator('[data-testid="customer-dashboard-loading-state"]')).toBeVisible();
  });

  test('should display empty state when no shops available', async ({ page }) => {
    // Mock empty shops response
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({ json: [] });
    });

    await page.reload();
    
    await expect(page.locator('[data-testid="customer-dashboard-empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-empty-state-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-empty-state-button"]')).toBeVisible();
  });

  test('should display shop list with individual shop items', async ({ page }) => {
    // Mock shops with data
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({
        json: [
          { id: 'shop1', name: 'Coffee Shop', credit_balance: 50 },
          { id: 'shop2', name: 'Restaurant', credit_balance: 100 }
        ]
      });
    });

    await page.reload();
    
    // Shops list container
    await expect(page.locator('[data-testid="customer-dashboard-shops-list"]')).toBeVisible();
    
    // Individual shop items
    await expect(page.locator('[data-testid="customer-dashboard-shop-item-shop1"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-shop-item-shop2"]')).toBeVisible();
    
    // Shop details
    await expect(page.locator('[data-testid="customer-dashboard-shop-name-shop1"]')).toHaveText('Coffee Shop');
    await expect(page.locator('[data-testid="customer-dashboard-shop-credits-shop1"]')).toContainText('50 credits');
    
    // Shop icons and arrows
    await expect(page.locator('[data-testid="customer-dashboard-shop-icon-shop1"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-dashboard-shop-arrow-shop1"]')).toBeVisible();
  });

  test('should handle action button clicks', async ({ page }) => {
    // Test scan button click
    await page.locator('[data-testid="customer-dashboard-scan-button"]').click();
    await expect(page).toHaveURL(/.*\/customer\/scan/);
    
    await page.goBack();
    
    // Test redeem button click
    await page.locator('[data-testid="customer-dashboard-redeem-button"]').click();
    await expect(page).toHaveURL(/.*\/customer\/redeem/);
    
    await page.goBack();
    
    // Test redeem code button click
    await page.locator('[data-testid="customer-dashboard-redeem-code-button"]').click();
    await expect(page).toHaveURL(/.*\/customer\/redeem/);
  });
});

// Test Group: Customer Authentication
test.describe('Customer Authentication Components', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/customer-login');
  });

  test('should render customer login form with test IDs', async ({ page }) => {
    // Main container
    await expect(page.locator('[data-testid="auth-customer-login-container"]')).toBeVisible();
    
    // Form elements
    await expect(page.locator('[data-testid="auth-customer-login-title"]')).toHaveText('Customer Login');
    await expect(page.locator('[data-testid="auth-customer-login-form"]')).toBeVisible();
    
    // Input fields
    await expect(page.locator('[data-testid="customer-login-username-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-login-password-input"]')).toBeVisible();
    
    // Action buttons
    await expect(page.locator('[data-testid="customer-login-password-toggle"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-login-submit-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-login-business-link"]')).toBeVisible();
  });

  test('should handle form input and validation', async ({ page }) => {
    const usernameInput = page.locator('[data-testid="customer-login-username-input"]');
    const passwordInput = page.locator('[data-testid="customer-login-password-input"]');
    
    // Test input functionality
    await usernameInput.fill('testcustomer');
    await expect(usernameInput).toHaveValue('testcustomer');
    
    await passwordInput.fill('testpassword');
    await expect(passwordInput).toHaveValue('testpassword');
  });

  test('should toggle password visibility', async ({ page }) => {
    const passwordInput = page.locator('[data-testid="customer-login-password-input"]');
    const toggleButton = page.locator('[data-testid="customer-login-password-toggle"]');
    
    // Initially password type
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Click toggle to hide password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should handle form submission', async ({ page }) => {
    // Mock login API
    await page.route('**/api/auth/customer-login', async (route) => {
      await route.fulfill({
        status: 200,
        json: { 
          token: 'mock-token',
          user: { id: 'customer1', name: 'Test Customer', shop_id: 'shop1' }
        }
      });
    });

    // Fill form
    await page.locator('[data-testid="customer-login-username-input"]').fill('testcustomer');
    await page.locator('[data-testid="customer-login-password-input"]').fill('testpassword');
    
    // Submit form
    await page.locator('[data-testid="customer-login-submit-button"]').click();
    
    // Should redirect to customer dashboard
    await expect(page).toHaveURL(/.*\/customer/);
  });

  test('should handle business owner link navigation', async ({ page }) => {
    await page.locator('[data-testid="customer-login-business-link"]').click();
    await expect(page).toHaveURL(/.*\/auth\/login/);
  });
});

// Test Group: Customer Shop Profile Components
test.describe('Customer Shop Profile Components', () => {
  test.beforeEach(async ({ page }) => {
    // Mock shop data
    await page.route('**/api/shops/shop1', async (route) => {
      await route.fulfill({
        json: {
          id: 'shop1',
          name: 'Test Coffee Shop',
          description: 'Premium coffee and pastries',
          contact_email: '<EMAIL>',
          contact_phone: '******-0123',
          image_url: 'https://example.com/shop.jpg'
        }
      });
    });
    
    await page.goto('/dashboard/customer/shops/shop1');
  });

  test('should render shop profile card with test IDs', async ({ page }) => {
    // Main profile card
    await expect(page.locator('[data-testid="customer-shop-profile-card"]')).toBeVisible();
    
    // Shop details
    await expect(page.locator('[data-testid="customer-shop-profile-name"]')).toHaveText('Test Coffee Shop');
    await expect(page.locator('[data-testid="customer-shop-profile-description"]')).toHaveText('Premium coffee and pastries');
    
    // Contact information
    await expect(page.locator('[data-testid="customer-shop-profile-contact"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shop-profile-email"]')).toContainText('<EMAIL>');
    await expect(page.locator('[data-testid="customer-shop-profile-phone"]')).toContainText('******-0123');
  });

  test('should handle missing contact information gracefully', async ({ page }) => {
    // Mock shop with minimal data
    await page.route('**/api/shops/shop2', async (route) => {
      await route.fulfill({
        json: {
          id: 'shop2',
          name: 'Basic Shop',
          description: null,
          contact_email: null,
          contact_phone: null
        }
      });
    });
    
    await page.goto('/dashboard/customer/shops/shop2');
    
    await expect(page.locator('[data-testid="customer-shop-profile-name"]')).toHaveText('Basic Shop');
    await expect(page.locator('[data-testid="customer-shop-profile-description"]')).toHaveText('No description available');
    
    // Contact elements should not be visible when no contact info
    await expect(page.locator('[data-testid="customer-shop-profile-email"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="customer-shop-profile-phone"]')).not.toBeVisible();
  });
});

// Test Group: Recent Activity Components
test.describe('Recent Activity Components', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/customer/shops/shop1');
  });

  test('should render recent activity card with test IDs', async ({ page }) => {
    // Main activity card
    await expect(page.locator('[data-testid="customer-shop-recent-activity"]')).toBeVisible();
  });

  test('should display loading state', async ({ page }) => {
    // Mock delayed response to show loading
    await page.route('**/api/shops/shop1/transactions', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    await expect(page.locator('[data-testid="customer-shop-loading-state"]')).toBeVisible();
  });

  test('should display transaction history with test IDs', async ({ page }) => {
    // Mock transaction data
    await page.route('**/api/shops/shop1/transactions', async (route) => {
      await route.fulfill({
        json: [
          { id: 'tx1', type: 'credit_add', amount: 50, created_at: '2025-01-01T10:00:00Z' },
          { id: 'tx2', type: 'credit_use', amount: -20, created_at: '2025-01-01T11:00:00Z' }
        ]
      });
    });

    await page.reload();
    
    // Transaction history container
    await expect(page.locator('[data-testid="customer-shop-transaction-history"]')).toBeVisible();
    
    // Individual transaction items
    await expect(page.locator('[data-testid="customer-shop-transaction-item-tx1"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shop-transaction-item-tx2"]')).toBeVisible();
  });
});

// Test Group: Responsive Design
test.describe('Customer Components - Responsive Design', () => {
  ['iphone-12', 'ipad', 'desktop'].forEach(device => {
    test(`should render properly on ${device}`, async ({ page, browserName }) => {
      if (device === 'iphone-12') {
        await page.setViewportSize({ width: 390, height: 844 });
      } else if (device === 'ipad') {
        await page.setViewportSize({ width: 768, height: 1024 });
      } else {
        await page.setViewportSize({ width: 1920, height: 1080 });
      }

      await page.goto('/dashboard/customer');
      
      // Essential elements should be visible on all devices
      await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-dashboard-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-dashboard-total-credits-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-dashboard-shops-count-card"]')).toBeVisible();
    });
  });
});

// Test Group: Error Handling
test.describe('Customer Components - Error Handling', () => {
  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({ status: 500, json: { error: 'Internal server error' } });
    });

    await page.goto('/dashboard/customer');
    
    // Should show error state or fallback UI
    // Note: Actual error handling depends on implementation
  });

  test('should handle network errors', async ({ page }) => {
    // Mock network failure
    await page.route('**/api/**', async (route) => {
      await route.abort('failed');
    });

    await page.goto('/dashboard/customer');
    
    // Should handle network errors gracefully
    // Note: Actual error handling depends on implementation
  });
});

// Test Group: Accessibility
test.describe('Customer Components - Accessibility', () => {
  test('should have proper ARIA labels and roles', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    // Check for proper heading structure
    const title = page.locator('[data-testid="customer-dashboard-title"]');
    await expect(title).toBeVisible();
    
    // Check for button accessibility
    const scanButton = page.locator('[data-testid="customer-dashboard-scan-button"]');
    await expect(scanButton).toHaveAttribute('title', 'Scan QR Code');
    
    const redeemButton = page.locator('[data-testid="customer-dashboard-redeem-button"]');
    await expect(redeemButton).toHaveAttribute('title', 'Redeem Code');
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/dashboard/customer');
    
    // Test tab navigation through interactive elements
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Verify focus management
    // Note: Specific focus tests depend on component implementation
  });
});

// Test Group: Performance
test.describe('Customer Components - Performance', () => {
  test('should load within acceptable time limits', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/dashboard/customer');
    await expect(page.locator('[data-testid="customer-dashboard-container"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Mock large shop list
    const largeShopsList = Array.from({ length: 100 }, (_, i) => ({
      id: `shop${i}`,
      name: `Shop ${i}`,
      credit_balance: Math.floor(Math.random() * 1000)
    }));

    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({ json: largeShopsList });
    });

    await page.goto('/dashboard/customer');
    
    // Should handle large datasets without performance issues
    await expect(page.locator('[data-testid="customer-dashboard-shops-list"]')).toBeVisible();
  });
});