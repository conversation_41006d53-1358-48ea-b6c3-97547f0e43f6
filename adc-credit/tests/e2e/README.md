# ADC Credit E2E Tests

This directory contains end-to-end tests for the ADC Credit application using Playwright.

## Overview

The E2E tests cover:

- **Public Pages**: Landing page, cookies page, error handling
- **Authenticated Pages**: Dashboard, shop management, credit management, settings
- **Business Flows**: Credit redemption, shop registration, analytics, API integration
- **Mobile Responsiveness**: Touch interactions and responsive design

## Test Files

- `pages.spec.ts` - Tests for public pages and basic functionality
- `authenticated-pages.spec.ts` - Tests for authenticated user pages
- `business-flows.spec.ts` - Tests for complete business workflows
- `global-setup.ts` - Global test environment setup
- `global-teardown.ts` - Global test cleanup

## Prerequisites

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Install Playwright Browsers**:
   ```bash
   npx playwright install
   ```

3. **Start the Application**:
   ```bash
   # Make sure both frontend and backend are running
   npm run dev        # Frontend on port 3400
   # In another terminal:
   cd backend && go run cmd/api/main.go  # Backend on port 8400
   ```

## Running Tests

### All E2E Tests
```bash
npm run test:e2e
```

### Specific Test Files
```bash
# Test public pages only
npx playwright test pages.spec.ts

# Test authenticated pages only
npx playwright test authenticated-pages.spec.ts

# Test business flows only
npx playwright test business-flows.spec.ts
```

### Interactive Mode
```bash
# Run tests with UI for debugging
npm run test:e2e:ui

# Run tests in headed mode (visible browser)
npm run test:e2e:headed

# Debug specific test
npm run test:e2e:debug
```

### Cross-Browser Testing
```bash
# Run on specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Run on mobile devices
npx playwright test --project="Mobile Chrome"
npx playwright test --project="Mobile Safari"
```

## Test Reports

```bash
# Generate and view HTML report
npm run test:e2e:report

# View test results
npx playwright show-report
```

## Test Structure

### Public Pages Tests (`pages.spec.ts`)
- Landing page rendering and functionality
- Cookie preferences page and interactions
- Responsive design across viewports
- SEO and accessibility checks
- Navigation and error handling

### Authenticated Pages Tests (`authenticated-pages.spec.ts`)
- Dashboard and user interface
- Shop management pages
- Credit management functionality
- Settings and API key management
- User authentication flows

### Business Flows Tests (`business-flows.spec.ts`)
- Customer credit redemption workflows
- Shop owner registration and management
- Credit purchase and usage tracking
- Analytics and reporting features
- API integration and documentation
- Mobile device compatibility

## Configuration

The tests use the configuration in `playwright.config.js`:

- **Base URL**: `http://localhost:3400`
- **Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile**: iPhone 12, Pixel 5
- **Timeout**: 10s actions, 30s navigation
- **Retries**: 2 retries in CI, 0 locally

## Test Data

Tests are designed to work with the application in any state:

- **No Authentication Required**: Most tests gracefully handle authentication redirects
- **Adaptive Testing**: Tests check for elements and adapt to what's available
- **Graceful Degradation**: Missing features are logged but don't fail tests

## Environment Variables

Set these environment variables for customized testing:

```bash
# Test against different environments
PLAYWRIGHT_BASE_URL=http://localhost:3400
PLAYWRIGHT_TIMEOUT=30000

# Backend API URL for health checks
BACKEND_URL=http://localhost:8400

# Test user credentials (if needed)
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=TestPassword123!
```

## Debugging Tests

### Local Debugging
```bash
# Debug with browser open
npx playwright test --debug

# Run specific test with debugging
npx playwright test pages.spec.ts --debug
```

### Screenshots and Videos
- Screenshots are captured on failure
- Videos are recorded for failed tests
- Traces are saved for the first retry

### Common Issues

1. **Application Not Running**:
   ```
   Error: Application is not running
   ```
   Solution: Start both frontend (`npm run dev`) and backend (`cd backend && go run cmd/api/main.go`)

2. **Port Conflicts**:
   ```
   Error: EADDRINUSE :::3400
   ```
   Solution: Check if another application is using port 3400

3. **Slow Tests**:
   - Increase timeouts in `playwright.config.js`
   - Check if backend services are running properly

## Writing New Tests

### Test Structure
```typescript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test('should do something specific', async ({ page }) => {
    await page.goto('/path');
    await expect(page.locator('[data-testid="element"]')).toBeVisible();
  });
});
```

### Best Practices

1. **Use data-testid Attributes**: Prefer `[data-testid="element"]` over CSS selectors
2. **Graceful Failures**: Check if elements exist before interacting
3. **Adaptive Testing**: Handle authentication redirects gracefully
4. **Mobile-First**: Test mobile responsiveness
5. **Performance**: Check page load times
6. **Accessibility**: Verify form labels and ARIA attributes

### Example Test
```typescript
test('should handle feature gracefully', async ({ page }) => {
  await page.goto('/feature');
  
  // Check if we need authentication
  if (page.url().includes('/auth/')) {
    test.skip('Authentication required for this test');
  }
  
  // Check if feature exists
  const feature = page.locator('[data-testid="feature"]');
  if (await feature.count() > 0) {
    await expect(feature).toBeVisible();
    console.log('✅ Feature is working');
  } else {
    console.log('⚠️ Feature not found, may not be implemented yet');
  }
});
```

## CI/CD Integration

Tests are configured for continuous integration:

- **Parallel Execution**: Tests run in parallel for speed
- **Retry Logic**: Failed tests are retried automatically
- **Cross-Browser**: All major browsers are tested
- **Reports**: HTML and JUnit reports are generated

## Maintenance

- Update test selectors when UI changes
- Add new tests for new features
- Remove obsolete tests for removed features
- Keep test data and credentials updated
- Monitor test performance and optimize slow tests

## Support

For questions about the E2E tests:

1. Check the [Playwright documentation](https://playwright.dev/)
2. Review the test files for examples
3. Check the application's development setup in the main README
4. Verify all services are running properly