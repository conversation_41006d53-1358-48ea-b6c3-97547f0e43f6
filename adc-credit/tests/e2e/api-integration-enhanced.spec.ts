/**
 * Enhanced API Integration E2E Tests
 * ADC Credit Service - External API Integration Testing
 * 
 * Tests external API functionality using the comprehensive test ID system
 * implemented across all phases. This test suite covers:
 * 
 * Test Categories:
 * - API Key Validation & Authentication
 * - Credit Consumption & Balance Checking
 * - Rate Limiting & Subscription Enforcement
 * - Webhook Management & Event Handling
 * - Error Handling & Edge Cases
 * 
 * Features the new test ID constants:
 * - API_KEYS_TEST_IDS (API key management)
 * - WEBHOOKS_TEST_IDS (webhook configuration)
 * - USAGE_TEST_IDS (analytics and tracking)
 * - SUBSCRIPTION_TEST_IDS (tier management)
 */

import { test, expect } from '@playwright/test';

// Test Group: API Key Validation & Authentication
test.describe('Enhanced API Key Authentication', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/api-keys');
  });

  test('should validate API key creation and authentication flow', async ({ page }) => {
    // Create new API key through UI
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    await page.locator('[data-testid="api-keys-create-button"]').click();
    
    // Fill API key creation form
    await expect(page.locator('[data-testid="api-keys-create-modal"]')).toBeVisible();
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('E2E Test API Key');
    await page.locator('[data-testid="api-keys-create-description-input"]').fill('API key for end-to-end testing');
    
    // Mock successful API key creation
    await page.route('**/api/v1/api-keys', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          json: {
            id: 'e2e-test-key-123',
            name: 'E2E Test API Key',
            api_key: 'adc_test_e2e_1234567890abcdef',
            key_preview: 'adc_test_e2e_1234...cdef',
            created_at: new Date().toISOString(),
            is_active: true
          }
        });
      }
    });
    
    // Submit form and verify success
    await page.locator('[data-testid="api-keys-create-submit-button"]').click();
    await expect(page.locator('[data-testid="api-keys-create-success-message"]')).toBeVisible();
    
    // Verify API key appears in table
    await expect(page.locator('[data-testid="api-keys-table-row-e2e-test-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-name-e2e-test-key-123"]')).toHaveText('E2E Test API Key');
    await expect(page.locator('[data-testid="api-keys-table-status-active-e2e-test-key-123"]')).toBeVisible();
  });

  test('should handle API key copy and usage instructions', async ({ page }) => {
    // Mock existing API key
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'copy-test-key-456',
            name: 'Copy Test Key',
            key_preview: 'adc_test_copy_5678...9999',
            api_key: 'adc_test_copy_56789999abcdefgh',
            is_active: true
          }
        ]
      });
    });

    await page.reload();
    
    // Test copy functionality
    await page.locator('[data-testid="api-keys-table-copy-button-copy-test-key-456"]').click();
    await expect(page.locator('[data-testid="api-keys-copy-success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-copy-success-message"]')).toContainText('API key copied');
    
    // Test usage instructions modal
    await page.locator('[data-testid="api-keys-table-info-button-copy-test-key-456"]').click();
    await expect(page.locator('[data-testid="api-keys-usage-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-usage-modal-title"]')).toHaveText('API Usage Instructions');
    await expect(page.locator('[data-testid="api-keys-usage-examples"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-usage-curl-example"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-usage-javascript-example"]')).toBeVisible();
  });

  test('should handle API key deactivation and reactivation', async ({ page }) => {
    // Mock API key for toggle testing
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'toggle-test-key-789',
            name: 'Toggle Test Key',
            key_preview: 'adc_test_toggle_7890...1234',
            is_active: true
          }
        ]
      });
    });

    await page.reload();
    
    // Test deactivation
    await page.locator('[data-testid="api-keys-table-toggle-button-toggle-test-key-789"]').click();
    await expect(page.locator('[data-testid="api-keys-deactivate-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-deactivate-modal-title"]')).toHaveText('Deactivate API Key');
    await expect(page.locator('[data-testid="api-keys-deactivate-warning"]')).toBeVisible();
    
    // Mock deactivation API call
    await page.route('**/api/v1/api-keys/toggle-test-key-789/toggle', async (route) => {
      await route.fulfill({
        status: 200,
        json: { is_active: false }
      });
    });
    
    await page.locator('[data-testid="api-keys-deactivate-confirm-button"]').click();
    await expect(page.locator('[data-testid="api-keys-table-status-inactive-toggle-test-key-789"]')).toBeVisible();
  });
});

// Test Group: Credit Consumption API Testing
test.describe('Enhanced Credit Consumption API', () => {
  test('should test credit consumption via external API', async ({ page }) => {
    // Navigate to usage analytics to monitor consumption
    await page.goto('/dashboard/usage');
    await expect(page.locator('[data-testid="usage-container"]')).toBeVisible();
    
    // Mock API consumption data
    await page.route('**/api/v1/usage/analytics', async (route) => {
      await route.fulfill({
        json: {
          total_requests: 150,
          successful_requests: 148,
          failed_requests: 2,
          average_response_time: 125,
          total_credits_consumed: 450,
          top_endpoints: [
            { endpoint: '/api/v1/external/verify', count: 100 },
            { endpoint: '/api/v1/external/consume', count: 50 }
          ],
          usage_by_hour: [
            { hour: '2025-01-01T10:00:00Z', requests: 25, credits: 75 },
            { hour: '2025-01-01T11:00:00Z', requests: 35, credits: 105 }
          ]
        }
      });
    });

    await page.reload();
    
    // Verify usage analytics display
    await expect(page.locator('[data-testid="usage-analytics-overview"]')).toBeVisible();
    await expect(page.locator('[data-testid="usage-total-requests"]')).toContainText('150');
    await expect(page.locator('[data-testid="usage-successful-requests"]')).toContainText('148');
    await expect(page.locator('[data-testid="usage-failed-requests"]')).toContainText('2');
    await expect(page.locator('[data-testid="usage-credits-consumed"]')).toContainText('450');
    
    // Test endpoint usage breakdown
    await expect(page.locator('[data-testid="usage-top-endpoints"]')).toBeVisible();
    await expect(page.locator('[data-testid="usage-endpoint-verify"]')).toContainText('/api/v1/external/verify');
    await expect(page.locator('[data-testid="usage-endpoint-consume"]')).toContainText('/api/v1/external/consume');
  });

  test('should display real-time credit balance updates', async ({ page }) => {
    // Navigate to dashboard to monitor credit balance
    await page.goto('/dashboard');
    
    // Mock current credit balance
    await page.route('**/api/v1/credits/balance', async (route) => {
      await route.fulfill({
        json: {
          total_credits: 1500,
          available_credits: 1200,
          consumed_credits: 300,
          last_updated: new Date().toISOString()
        }
      });
    });

    await page.reload();
    
    // Verify credit balance display
    await expect(page.locator('[data-testid="dashboard-credit-balance-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="dashboard-total-credits"]')).toContainText('1,500');
    await expect(page.locator('[data-testid="dashboard-available-credits"]')).toContainText('1,200');
    await expect(page.locator('[data-testid="dashboard-consumed-credits"]')).toContainText('300');
    
    // Test credit consumption simulation
    // Mock API consumption request
    await page.route('**/api/v1/external/consume', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          success: true,
          credits_consumed: 50,
          remaining_credits: 1150,
          message: 'Credits consumed successfully'
        }
      });
    });
    
    // Simulate external API call (would normally be done by external system)
    // For E2E testing, we'll verify the UI can handle balance updates
    await page.route('**/api/v1/credits/balance', async (route) => {
      await route.fulfill({
        json: {
          total_credits: 1500,
          available_credits: 1150,
          consumed_credits: 350,
          last_updated: new Date().toISOString()
        }
      });
    });

    // Refresh to see updated balance
    await page.reload();
    await expect(page.locator('[data-testid="dashboard-available-credits"]')).toContainText('1,150');
    await expect(page.locator('[data-testid="dashboard-consumed-credits"]')).toContainText('350');
  });
});

// Test Group: Rate Limiting & Subscription Testing
test.describe('Enhanced Rate Limiting & Subscription Enforcement', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/subscription');
  });

  test('should display subscription limits and usage', async ({ page }) => {
    // Mock subscription data
    await page.route('**/api/v1/subscription', async (route) => {
      await route.fulfill({
        json: {
          tier: 'pro',
          status: 'active',
          billing_cycle: 'monthly',
          limits: {
            max_shops: 10,
            max_customers_per_shop: 1000,
            max_api_keys: 5,
            max_credits_per_month: 10000,
            rate_limit_per_minute: 60
          },
          current_usage: {
            shops: 3,
            customers: 245,
            api_keys: 2,
            credits_this_month: 2750,
            requests_this_minute: 12
          }
        }
      });
    });

    await page.reload();
    
    // Verify subscription overview
    await expect(page.locator('[data-testid="subscription-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-current-tier"]')).toHaveText('Pro');
    await expect(page.locator('[data-testid="subscription-status"]')).toHaveText('Active');
    
    // Test limits display
    await expect(page.locator('[data-testid="subscription-limits-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-shops-limit"]')).toContainText('3 / 10 shops');
    await expect(page.locator('[data-testid="subscription-customers-limit"]')).toContainText('245 / 1,000 customers');
    await expect(page.locator('[data-testid="subscription-api-keys-limit"]')).toContainText('2 / 5 API keys');
    await expect(page.locator('[data-testid="subscription-credits-limit"]')).toContainText('2,750 / 10,000 credits');
    
    // Test rate limiting display
    await expect(page.locator('[data-testid="subscription-rate-limit-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-current-rate"]')).toContainText('12 / 60 requests/minute');
  });

  test('should handle subscription tier upgrades', async ({ page }) => {
    // Test upgrade flow
    await expect(page.locator('[data-testid="subscription-upgrade-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-upgrade-enterprise-button"]')).toBeVisible();
    
    // Click upgrade button
    await page.locator('[data-testid="subscription-upgrade-enterprise-button"]').click();
    await expect(page.locator('[data-testid="subscription-upgrade-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-upgrade-modal-title"]')).toHaveText('Upgrade to Enterprise');
    
    // Test upgrade comparison
    await expect(page.locator('[data-testid="subscription-upgrade-comparison"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-upgrade-current-features"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-upgrade-new-features"]')).toBeVisible();
    
    // Test upgrade confirmation
    await expect(page.locator('[data-testid="subscription-upgrade-confirm-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-upgrade-cancel-button"]')).toBeVisible();
  });

  test('should display rate limiting warnings and errors', async ({ page }) => {
    // Mock rate limit approaching scenario
    await page.route('**/api/v1/subscription', async (route) => {
      await route.fulfill({
        json: {
          tier: 'free',
          limits: { rate_limit_per_minute: 10 },
          current_usage: { requests_this_minute: 9 },
          warnings: ['Rate limit almost reached']
        }
      });
    });

    await page.reload();
    
    // Test rate limit warning
    await expect(page.locator('[data-testid="subscription-rate-limit-warning"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-rate-limit-warning"]')).toContainText('Rate limit almost reached');
    
    // Mock rate limit exceeded scenario
    await page.route('**/api/v1/subscription', async (route) => {
      await route.fulfill({
        json: {
          tier: 'free',
          limits: { rate_limit_per_minute: 10 },
          current_usage: { requests_this_minute: 11 },
          errors: ['Rate limit exceeded']
        }
      });
    });

    await page.reload();
    
    // Test rate limit error
    await expect(page.locator('[data-testid="subscription-rate-limit-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscription-rate-limit-error"]')).toContainText('Rate limit exceeded');
  });
});

// Test Group: Webhook Management Testing
test.describe('Enhanced Webhook Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/webhooks');
  });

  test('should complete webhook creation and configuration', async ({ page }) => {
    // Verify webhooks page structure
    await expect(page.locator('[data-testid="webhooks-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-title"]')).toHaveText('Webhooks');
    await expect(page.locator('[data-testid="webhooks-description"]')).toBeVisible();
    
    // Test create webhook
    await expect(page.locator('[data-testid="webhooks-create-button"]')).toBeVisible();
    await page.locator('[data-testid="webhooks-create-button"]').click();
    
    // Test webhook creation form
    await expect(page.locator('[data-testid="webhooks-create-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-create-modal-title"]')).toHaveText('Create Webhook');
    await expect(page.locator('[data-testid="webhooks-create-url-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-create-events-section"]')).toBeVisible();
    
    // Fill webhook form
    await page.locator('[data-testid="webhooks-create-url-input"]').fill('https://api.example.com/webhooks/adc-credits');
    await page.locator('[data-testid="webhooks-create-description-input"]').fill('Webhook for credit consumption events');
    
    // Select events
    await expect(page.locator('[data-testid="webhooks-create-event-credit-consumed"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-create-event-credit-refunded"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-create-event-subscription-changed"]')).toBeVisible();
    
    await page.locator('[data-testid="webhooks-create-event-credit-consumed"]').check();
    await page.locator('[data-testid="webhooks-create-event-subscription-changed"]').check();
    
    // Test form validation
    await expect(page.locator('[data-testid="webhooks-create-submit-button"]')).toBeEnabled();
  });

  test('should display webhook list with dynamic test IDs', async ({ page }) => {
    // Mock webhooks data
    await page.route('**/api/v1/webhooks', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'webhook-123',
            url: 'https://api.example.com/webhooks/credits',
            description: 'Credit events webhook',
            events: ['credit.consumed', 'credit.refunded'],
            is_active: true,
            created_at: '2025-01-01T10:00:00Z',
            last_triggered: '2025-01-01T15:30:00Z',
            success_rate: 98.5
          },
          {
            id: 'webhook-456',
            url: 'https://hooks.slack.com/services/...',
            description: 'Slack notifications',
            events: ['subscription.changed'],
            is_active: false,
            created_at: '2025-01-02T10:00:00Z',
            last_triggered: null,
            success_rate: 0
          }
        ]
      });
    });

    await page.reload();
    
    // Test webhooks table
    await expect(page.locator('[data-testid="webhooks-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-row-webhook-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-row-webhook-456"]')).toBeVisible();
    
    // Test webhook data display
    await expect(page.locator('[data-testid="webhooks-table-url-webhook-123"]')).toContainText('api.example.com');
    await expect(page.locator('[data-testid="webhooks-table-events-webhook-123"]')).toContainText('2 events');
    await expect(page.locator('[data-testid="webhooks-table-status-active-webhook-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-status-inactive-webhook-456"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-success-rate-webhook-123"]')).toContainText('98.5%');
    
    // Test webhook actions
    await expect(page.locator('[data-testid="webhooks-table-test-button-webhook-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-edit-button-webhook-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-delete-button-webhook-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-table-toggle-button-webhook-123"]')).toBeVisible();
  });

  test('should handle webhook testing and validation', async ({ page }) => {
    // Mock webhook for testing
    await page.route('**/api/v1/webhooks', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'test-webhook-789',
            url: 'https://webhook.site/test-endpoint',
            description: 'Test webhook endpoint',
            events: ['credit.consumed'],
            is_active: true
          }
        ]
      });
    });

    await page.reload();
    
    // Test webhook endpoint
    await page.locator('[data-testid="webhooks-table-test-button-test-webhook-789"]').click();
    await expect(page.locator('[data-testid="webhooks-test-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-test-modal-title"]')).toHaveText('Test Webhook');
    
    // Mock test webhook response
    await page.route('**/api/v1/webhooks/test-webhook-789/test', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          success: true,
          response_time: 145,
          status_code: 200,
          response_body: '{"received": true}',
          message: 'Webhook test successful'
        }
      });
    });
    
    await page.locator('[data-testid="webhooks-test-send-button"]').click();
    
    // Verify test results
    await expect(page.locator('[data-testid="webhooks-test-result-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="webhooks-test-response-time"]')).toContainText('145ms');
    await expect(page.locator('[data-testid="webhooks-test-status-code"]')).toContainText('200');
    await expect(page.locator('[data-testid="webhooks-test-response-body"]')).toContainText('{"received": true}');
  });
});

// Test Group: Error Handling & Edge Cases
test.describe('Enhanced API Error Handling', () => {
  test('should handle various API error scenarios', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Test network error handling
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.abort('failed');
    });

    await page.reload();
    await expect(page.locator('[data-testid="api-keys-network-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-network-error-message"]')).toContainText('Network error');
    await expect(page.locator('[data-testid="api-keys-retry-button"]')).toBeVisible();
    
    // Test server error handling
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        status: 500,
        json: { error: 'Internal server error' }
      });
    });

    await page.locator('[data-testid="api-keys-retry-button"]').click();
    await expect(page.locator('[data-testid="api-keys-server-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-server-error-message"]')).toContainText('Server error');
    
    // Test authentication error handling
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        status: 401,
        json: { error: 'Unauthorized' }
      });
    });

    await page.locator('[data-testid="api-keys-retry-button"]').click();
    await expect(page.locator('[data-testid="api-keys-auth-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-auth-error-message"]')).toContainText('Authentication required');
  });

  test('should handle rate limiting scenarios gracefully', async ({ page }) => {
    await page.goto('/dashboard/usage');
    
    // Mock rate limit exceeded response
    await page.route('**/api/v1/usage/analytics', async (route) => {
      await route.fulfill({
        status: 429,
        json: { 
          error: 'Rate limit exceeded',
          retry_after: 60,
          limit: 10,
          remaining: 0
        }
      });
    });

    await page.reload();
    
    // Verify rate limit error display
    await expect(page.locator('[data-testid="usage-rate-limit-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="usage-rate-limit-message"]')).toContainText('Rate limit exceeded');
    await expect(page.locator('[data-testid="usage-rate-limit-retry-after"]')).toContainText('60 seconds');
    await expect(page.locator('[data-testid="usage-rate-limit-upgrade-suggestion"]')).toBeVisible();
  });

  test('should handle subscription limit exceeded scenarios', async ({ page }) => {
    await page.goto('/dashboard/shops/create');
    
    // Mock shop creation limit exceeded
    await page.route('**/api/v1/shops', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 403,
          json: {
            error: 'Shop limit exceeded',
            current_count: 5,
            max_allowed: 5,
            subscription_tier: 'pro'
          }
        });
      }
    });

    // Fill and submit form
    await page.locator('[data-testid="shops-create-name-input"]').fill('Test Shop');
    await page.locator('[data-testid="shops-create-submit-button"]').click();
    
    // Verify limit exceeded error
    await expect(page.locator('[data-testid="shops-create-limit-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-create-limit-message"]')).toContainText('Shop limit exceeded');
    await expect(page.locator('[data-testid="shops-create-limit-current"]')).toContainText('5 / 5 shops');
    await expect(page.locator('[data-testid="shops-create-upgrade-suggestion"]')).toBeVisible();
  });
});

// Test Group: Cross-Feature API Integration
test.describe('Enhanced Cross-Feature API Integration', () => {
  test('should complete full API lifecycle workflow', async ({ page }) => {
    // Start with API key creation
    await page.goto('/dashboard/api-keys');
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Full Lifecycle Test Key');
    
    // Navigate to webhook setup
    await page.goto('/dashboard/webhooks');
    await page.locator('[data-testid="webhooks-create-button"]').click();
    await page.locator('[data-testid="webhooks-create-url-input"]').fill('https://api.test.com/webhooks');
    
    // Navigate to usage monitoring
    await page.goto('/dashboard/usage');
    await expect(page.locator('[data-testid="usage-container"]')).toBeVisible();
    
    // Navigate to subscription management
    await page.goto('/dashboard/subscription');
    await expect(page.locator('[data-testid="subscription-container"]')).toBeVisible();
    
    // Verify integration between features
    await expect(page.locator('[data-testid="subscription-api-usage-section"]')).toBeVisible();
  });

  test('should maintain data consistency across API features', async ({ page }) => {
    // Test that API key count is consistent across pages
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        json: [
          { id: 'key-1', name: 'Key 1', is_active: true },
          { id: 'key-2', name: 'Key 2', is_active: true },
          { id: 'key-3', name: 'Key 3', is_active: false }
        ]
      });
    });

    // Check API keys page
    await page.goto('/dashboard/api-keys');
    const apiKeyRows = page.locator('[data-testid^="api-keys-table-row-"]');
    await expect(apiKeyRows).toHaveCount(3);
    
    // Check subscription page shows correct API key usage
    await page.goto('/dashboard/subscription');
    await expect(page.locator('[data-testid="subscription-api-keys-limit"]')).toContainText('3 /');
    
    // Check dashboard shows correct API key count
    await page.goto('/dashboard');
    await expect(page.locator('[data-testid="dashboard-api-keys-count"]')).toContainText('3');
  });
});