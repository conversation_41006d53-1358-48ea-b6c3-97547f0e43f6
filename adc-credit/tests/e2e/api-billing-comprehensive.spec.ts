/**
 * API Keys and Billing Comprehensive E2E Tests
 * ADC Credit Service - API Management and Subscription Billing Testing
 * 
 * Tests API key management, subscription billing workflows,
 * payment processing, and subscription lifecycle management.
 * 
 * Test Categories:
 * - API Keys Management (CRUD operations, permissions)
 * - Subscription Management (plans, billing, cancellation)
 * - Billing Integration (Stripe checkout, payment processing)
 * - Subscription Tiers (feature limits, usage tracking)
 * - Payment Workflows (upgrades, downgrades, proration)
 */

import { test, expect } from '@playwright/test';

// Test Group: API Keys Management
test.describe('API Keys Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock user authentication
    await page.goto('/dashboard/api-keys');
  });

  test('should render API keys page with test IDs', async ({ page }) => {
    // Main container
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    
    // Page title and create button
    await expect(page.locator('[data-testid="api-keys-title"]')).toHaveText('API Keys');
    await expect(page.locator('[data-testid="api-keys-create-button"]')).toBeVisible();
    
    // Table container
    await expect(page.locator('[data-testid="api-keys-table-container"]')).toBeVisible();
  });

  test('should display loading state correctly', async ({ page }) => {
    // Mock delayed response
    await page.route('**/api/api-keys', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    await expect(page.locator('[data-testid="api-keys-loading-spinner"]')).toBeVisible();
  });

  test('should show empty state when no API keys exist', async ({ page }) => {
    // Mock empty API keys response
    await page.route('**/api/api-keys', async (route) => {
      await route.fulfill({ json: [] });
    });

    await page.reload();
    await expect(page.locator('[data-testid="api-keys-table-empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-empty-state"]')).toContainText('No API keys found');
  });

  test('should display API keys table with test IDs', async ({ page }) => {
    // Mock API keys data
    await page.route('**/api/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'key1',
            name: 'Production API Key',
            created_at: '2025-01-01T10:00:00Z',
            last_used: '2025-01-02T10:00:00Z',
            enabled: true,
            permissions: ['read', 'write']
          },
          {
            id: 'key2',
            name: 'Development API Key',
            created_at: '2025-01-01T11:00:00Z',
            last_used: null,
            enabled: false,
            permissions: ['read']
          }
        ]
      });
    });

    await page.reload();
    
    // Table structure
    await expect(page.locator('[data-testid="api-keys-table-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-body"]')).toBeVisible();
    
    // Individual key rows
    await expect(page.locator('[data-testid="api-keys-table-row-key1"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-row-key2"]')).toBeVisible();
    
    // Key details
    await expect(page.locator('[data-testid="api-keys-table-name-key1"]')).toHaveText('Production API Key');
    await expect(page.locator('[data-testid="api-keys-table-status-key1"]')).toContainText('Active');
    await expect(page.locator('[data-testid="api-keys-table-status-key2"]')).toContainText('Disabled');
    
    // Action buttons
    await expect(page.locator('[data-testid="api-keys-table-view-key1"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-toggle-key1"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-delete-key1"]')).toBeVisible();
  });

  test('should open create API key dialog', async ({ page }) => {
    // Click create button
    await page.locator('[data-testid="api-keys-create-button"]').click();
    
    // Dialog should be visible
    await expect(page.locator('[data-testid="api-keys-create-dialog"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-dialog-title"]')).toHaveText('Create New API Key');
    
    // Form elements
    await expect(page.locator('[data-testid="api-keys-create-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-permissions-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-dialog-create-button"]')).toBeVisible();
    
    // Permission checkboxes
    await expect(page.locator('[data-testid="api-keys-permission-read"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-permission-write"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-permission-delete"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-permission-admin"]')).toBeVisible();
  });

  test('should create new API key with form validation', async ({ page }) => {
    // Mock API key creation
    await page.route('**/api/api-keys', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          json: {
            id: 'new-key',
            name: 'Test API Key',
            key: 'adc_test_12345678901234567890',
            created_at: '2025-01-01T12:00:00Z',
            enabled: true,
            permissions: ['read', 'write']
          }
        });
      } else {
        await route.continue();
      }
    });

    // Open create dialog
    await page.locator('[data-testid="api-keys-create-button"]').click();
    
    // Fill form
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Test API Key');
    await page.locator('[data-testid="api-keys-permission-read"]').check();
    await page.locator('[data-testid="api-keys-permission-write"]').check();
    
    // Submit form
    await page.locator('[data-testid="api-keys-create-dialog-create-button"]').click();
    
    // Should show new key alert
    await expect(page.locator('[data-testid="api-keys-new-key-alert"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-new-key-value"]')).toContainText('adc_test_');
    await expect(page.locator('[data-testid="api-keys-copy-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-dismiss-button"]')).toBeVisible();
  });

  test('should handle API key actions', async ({ page }) => {
    // Mock API keys data
    await page.route('**/api/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'key1',
            name: 'Test Key',
            created_at: '2025-01-01T10:00:00Z',
            last_used: null,
            enabled: true,
            permissions: ['read']
          }
        ]
      });
    });

    await page.reload();
    
    // Test view button
    await page.locator('[data-testid="api-keys-table-view-key1"]').click();
    await expect(page).toHaveURL(/.*\/api-keys\/key1/);
    
    await page.goBack();
    
    // Test toggle button (mock the API call)
    await page.route('**/api/api-keys/key1', async (route) => {
      if (route.request().method() === 'PATCH') {
        await route.fulfill({ json: { id: 'key1', enabled: false } });
      } else {
        await route.continue();
      }
    });
    
    await page.locator('[data-testid="api-keys-table-toggle-key1"]').click();
    
    // Test delete button (mock the API call)
    await page.route('**/api/api-keys/key1', async (route) => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({ status: 204 });
      } else {
        await route.continue();
      }
    });
    
    await page.locator('[data-testid="api-keys-table-delete-key1"]').click();
  });

  test('should copy API key to clipboard', async ({ page }) => {
    // Grant clipboard permissions
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
    
    // Mock new API key creation
    await page.route('**/api/api-keys', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          json: {
            id: 'new-key',
            name: 'Test Key',
            key: 'adc_test_clipboardtest123456789',
            created_at: '2025-01-01T12:00:00Z',
            enabled: true
          }
        });
      } else {
        await route.continue();
      }
    });

    // Create new key
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Test Key');
    await page.locator('[data-testid="api-keys-create-dialog-create-button"]').click();
    
    // Copy to clipboard
    await page.locator('[data-testid="api-keys-copy-button"]').click();
    
    // Verify clipboard content
    const clipboardContent = await page.evaluate(() => navigator.clipboard.readText());
    expect(clipboardContent).toBe('adc_test_clipboardtest123456789');
  });
});

// Test Group: Subscription Management
test.describe('Subscription Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication and subscription data
    await page.route('**/api/subscriptions', async (route) => {
      await route.fulfill({ json: [] });
    });
    
    await page.route('**/api/subscription-tiers', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 1,
            name: 'Free',
            description: 'Basic features for getting started',
            price: 0,
            credit_limit: 1000,
            features: ['Basic API access', 'Email support']
          },
          {
            id: 2,
            name: 'Pro',
            description: 'Advanced features for growing businesses',
            price: 29.99,
            credit_limit: 10000,
            features: ['Full API access', 'Priority support', 'Advanced analytics']
          },
          {
            id: 3,
            name: 'Enterprise',
            description: 'Complete solution for large organizations',
            price: 99.99,
            credit_limit: 50000,
            features: ['Unlimited API access', '24/7 support', 'Custom integrations']
          }
        ]
      });
    });
    
    await page.goto('/dashboard/subscriptions');
  });

  test('should render subscriptions page with test IDs', async ({ page }) => {
    // Main container
    await expect(page.locator('[data-testid="subscriptions-container"]')).toBeVisible();
    
    // Page title and refresh button
    await expect(page.locator('[data-testid="subscriptions-title"]')).toHaveText('Subscriptions');
    await expect(page.locator('[data-testid="subscriptions-refresh-button"]')).toBeVisible();
    
    // Tabs
    await expect(page.locator('[data-testid="subscriptions-tabs-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-tabs-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-tab-personal"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-tab-shop"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-tab-customer"]')).toBeVisible();
  });

  test('should display loading state', async ({ page }) => {
    // Mock delayed response
    await page.route('**/api/subscription-tiers', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    await expect(page.locator('[data-testid="subscriptions-loading-spinner"]')).toBeVisible();
  });

  test('should display available subscription plans', async ({ page }) => {
    // Plans section
    await expect(page.locator('[data-testid="subscriptions-plans-section-title"]')).toHaveText('Available Plans');
    await expect(page.locator('[data-testid="subscriptions-plans-grid"]')).toBeVisible();
    
    // Individual plan cards
    await expect(page.locator('[data-testid="subscriptions-plan-card-1"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-plan-card-2"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-plan-card-3"]')).toBeVisible();
    
    // Plan details
    await expect(page.locator('[data-testid="subscriptions-plan-name-1"]')).toHaveText('Free');
    await expect(page.locator('[data-testid="subscriptions-plan-name-2"]')).toHaveText('Pro');
    await expect(page.locator('[data-testid="subscriptions-plan-name-3"]')).toHaveText('Enterprise');
    
    // Plan descriptions
    await expect(page.locator('[data-testid="subscriptions-plan-description-1"]')).toContainText('Basic features');
    await expect(page.locator('[data-testid="subscriptions-plan-description-2"]')).toContainText('Advanced features');
    await expect(page.locator('[data-testid="subscriptions-plan-description-3"]')).toContainText('Complete solution');
    
    // Select buttons
    await expect(page.locator('[data-testid="subscriptions-plan-select-1"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-plan-select-2"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-plan-select-3"]')).toBeVisible();
  });

  test('should display current subscription when user has one', async ({ page }) => {
    // Mock active subscription
    await page.route('**/api/subscriptions', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'sub1',
            status: 'active',
            subscription_type: 'personal',
            credit_balance: 7500,
            auto_renew: true,
            start_date: '2025-01-01T00:00:00Z',
            subscription_tier: {
              id: 2,
              name: 'Pro',
              description: 'Advanced features for growing businesses',
              price: 29.99,
              credit_limit: 10000
            }
          }
        ]
      });
    });

    await page.reload();
    
    // Current subscription card
    await expect(page.locator('[data-testid="subscriptions-current-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-current-title"]')).toHaveText('Current Subscription');
    await expect(page.locator('[data-testid="subscriptions-current-plan-name"]')).toContainText('Pro Plan');
    await expect(page.locator('[data-testid="subscriptions-current-plan-description"]')).toContainText('Advanced features');
    
    // Credit information
    await expect(page.locator('[data-testid="subscriptions-current-credit-balance"]')).toContainText('7,500 / 10,000');
    await expect(page.locator('[data-testid="subscriptions-current-credit-progress"]')).toBeVisible();
    
    // Action buttons
    await expect(page.locator('[data-testid="subscriptions-current-auto-renew-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-current-cancel-button"]')).toBeVisible();
  });

  test('should open subscription confirmation dialog', async ({ page }) => {
    // Click on Pro plan select button
    await page.locator('[data-testid="subscriptions-plan-select-2"]').click();
    
    // Confirmation dialog should be visible
    await expect(page.locator('[data-testid="subscriptions-confirm-dialog"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-confirm-dialog-title"]')).toHaveText('Confirm Subscription');
    await expect(page.locator('[data-testid="subscriptions-confirm-dialog-description"]')).toContainText('Pro plan');
    
    // Action buttons
    await expect(page.locator('[data-testid="subscriptions-confirm-cancel-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-confirm-confirm-button"]')).toBeVisible();
  });

  test('should handle subscription cancellation dialog', async ({ page }) => {
    // Mock active subscription
    await page.route('**/api/subscriptions', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'sub1',
            status: 'active',
            subscription_type: 'personal',
            auto_renew: true,
            subscription_tier: { id: 2, name: 'Pro', price: 29.99, credit_limit: 10000 }
          }
        ]
      });
    });

    await page.reload();
    
    // Click cancel subscription button
    await page.locator('[data-testid="subscriptions-current-cancel-button"]').click();
    
    // Cancel dialog should be visible
    await expect(page.locator('[data-testid="subscriptions-cancel-dialog"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-cancel-dialog-title"]')).toHaveText('Cancel Subscription');
    await expect(page.locator('[data-testid="subscriptions-cancel-dialog-description"]')).toContainText('Pro subscription');
    
    // Warning alert
    await expect(page.locator('[data-testid="subscriptions-cancel-warning-alert"]')).toBeVisible();
    
    // Action buttons
    await expect(page.locator('[data-testid="subscriptions-cancel-keep-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="subscriptions-cancel-cancel-button"]')).toBeVisible();
  });

  test('should handle subscription creation for free tier', async ({ page }) => {
    // Mock subscription creation
    await page.route('**/api/subscriptions', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          json: {
            id: 'new-sub',
            status: 'active',
            subscription_tier_id: 1,
            auto_renew: true,
            subscription_type: 'personal'
          }
        });
      } else {
        await route.continue();
      }
    });

    // Select free plan
    await page.locator('[data-testid="subscriptions-plan-select-1"]').click();
    
    // Confirm subscription
    await page.locator('[data-testid="subscriptions-confirm-confirm-button"]').click();
    
    // Should handle free tier subscription creation
    // (Implementation depends on specific flow)
  });

  test('should handle subscription upgrade', async ({ page }) => {
    // Mock current subscription
    await page.route('**/api/subscriptions', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'sub1',
            status: 'active',
            subscription_type: 'personal',
            subscription_tier: { id: 1, name: 'Free', price: 0, credit_limit: 1000 }
          }
        ]
      });
    });

    await page.reload();
    
    // Try to upgrade to Pro plan
    await page.locator('[data-testid="subscriptions-plan-select-2"]').click();
    
    // Should show replacement alert
    await expect(page.locator('[data-testid="subscriptions-confirm-replacement-alert"]')).toBeVisible();
  });

  test('should handle refresh functionality', async ({ page }) => {
    let refreshCount = 0;
    
    await page.route('**/api/subscriptions', async (route) => {
      refreshCount++;
      await route.fulfill({ json: [] });
    });

    // Click refresh button
    await page.locator('[data-testid="subscriptions-refresh-button"]').click();
    
    // Should trigger API call
    expect(refreshCount).toBeGreaterThan(1);
  });

  test('should handle tab navigation', async ({ page }) => {
    // Test personal tab (default)
    await expect(page.locator('[data-testid="subscriptions-tab-personal"]')).toHaveAttribute('data-state', 'active');
    
    // Click shop tab
    await page.locator('[data-testid="subscriptions-tab-shop"]').click();
    await expect(page.locator('[data-testid="subscriptions-tab-shop"]')).toHaveAttribute('data-state', 'active');
    
    // Click customer tab
    await page.locator('[data-testid="subscriptions-tab-customer"]').click();
    await expect(page.locator('[data-testid="subscriptions-tab-customer"]')).toHaveAttribute('data-state', 'active');
  });
});

// Test Group: Authentication Required
test.describe('Authentication Required States', () => {
  test('should show auth required state for unauthenticated users', async ({ page }) => {
    // Mock unauthenticated state
    await page.route('**/api/auth/session', async (route) => {
      await route.fulfill({ json: null });
    });

    await page.goto('/dashboard/subscriptions');
    
    await expect(page.locator('[data-testid="subscriptions-auth-required"]')).toBeVisible();
  });
});

// Test Group: Error Handling
test.describe('API and Billing Error Handling', () => {
  test('should handle API key creation errors', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Mock API error
    await page.route('**/api/api-keys', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({ status: 400, json: { error: 'Invalid permissions' } });
      } else {
        await route.continue();
      }
    });

    // Try to create API key
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Test Key');
    await page.locator('[data-testid="api-keys-create-dialog-create-button"]').click();
    
    // Should handle error gracefully
    // (Error handling depends on implementation)
  });

  test('should handle subscription API errors', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    
    // Mock API error
    await page.route('**/api/subscription-tiers', async (route) => {
      await route.fulfill({ status: 500, json: { error: 'Internal server error' } });
    });

    await page.reload();
    
    // Should handle error gracefully
    // (Error handling depends on implementation)
  });

  test('should handle network errors', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Mock network failure
    await page.route('**/api/**', async (route) => {
      await route.abort('failed');
    });

    await page.reload();
    
    // Should handle network errors gracefully
  });
});

// Test Group: Responsive Design
test.describe('API and Billing - Responsive Design', () => {
  ['iphone-12', 'ipad', 'desktop'].forEach(device => {
    test(`should render properly on ${device}`, async ({ page }) => {
      if (device === 'iphone-12') {
        await page.setViewportSize({ width: 390, height: 844 });
      } else if (device === 'ipad') {
        await page.setViewportSize({ width: 768, height: 1024 });
      } else {
        await page.setViewportSize({ width: 1920, height: 1080 });
      }

      await page.goto('/dashboard/api-keys');
      
      // Essential elements should be visible on all devices
      await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="api-keys-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="api-keys-create-button"]')).toBeVisible();
      
      // Navigate to subscriptions
      await page.goto('/dashboard/subscriptions');
      
      await expect(page.locator('[data-testid="subscriptions-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="subscriptions-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="subscriptions-tabs-container"]')).toBeVisible();
    });
  });
});

// Test Group: Performance
test.describe('API and Billing - Performance', () => {
  test('should load API keys page within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/dashboard/api-keys');
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('should load subscriptions page within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/dashboard/subscriptions');
    await expect(page.locator('[data-testid="subscriptions-container"]')).toBeVisible();
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('should handle large API keys list efficiently', async ({ page }) => {
    // Mock large API keys list
    const largeApiKeysList = Array.from({ length: 100 }, (_, i) => ({
      id: `key${i}`,
      name: `API Key ${i}`,
      created_at: '2025-01-01T10:00:00Z',
      last_used: null,
      enabled: true,
      permissions: ['read']
    }));

    await page.route('**/api/api-keys', async (route) => {
      await route.fulfill({ json: largeApiKeysList });
    });

    await page.goto('/dashboard/api-keys');
    
    // Should handle large datasets without performance issues
    await expect(page.locator('[data-testid="api-keys-table-container"]')).toBeVisible();
  });
});

// Test Group: Accessibility
test.describe('API and Billing - Accessibility', () => {
  test('should have proper ARIA labels and roles', async ({ page }) => {
    await page.goto('/dashboard/api-keys');
    
    // Check for proper heading structure
    const title = page.locator('[data-testid="api-keys-title"]');
    await expect(title).toBeVisible();
    
    // Check for button accessibility
    const createButton = page.locator('[data-testid="api-keys-create-button"]');
    await expect(createButton).toBeVisible();
    
    // Check form accessibility in dialog
    await createButton.click();
    const nameInput = page.locator('[data-testid="api-keys-create-name-input"]');
    await expect(nameInput).toHaveAttribute('id', 'name');
  });

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/dashboard/subscriptions');
    
    // Test tab navigation through subscription plans
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Verify focus management
    // Note: Specific focus tests depend on component implementation
  });
});