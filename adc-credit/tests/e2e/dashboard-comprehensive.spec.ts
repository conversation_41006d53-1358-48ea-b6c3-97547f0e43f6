/**
 * ADC Credit Dashboard Comprehensive E2E Tests
 * 
 * Tests all dashboard activities, API integrations, and form submissions
 * with stable test ID selectors
 */

import { test, expect } from '@playwright/test';

// Dashboard Test IDs (matching implementation)
const DASHBOARD_TEST_IDS = {
  // Main Structure
  MAIN_CONTAINER: 'dashboard-main-container',
  HEADER: 'dashboard-main-header',
  LOADING_SPINNER: 'dashboard-loading-spinner',
  REFRESH_BUTTON: 'dashboard-refresh-button',
  
  // Tab Navigation
  TABS: 'dashboard-tabs',
  TABS_LIST: 'dashboard-tabs-list',
  TAB_OVERVIEW: 'dashboard-tab-overview',
  TAB_USAGE: 'dashboard-tab-usage',
  TAB_ACTIVITY: 'dashboard-tab-activity',
  
  // Overview Tab
  OVERVIEW_TAB: 'dashboard-overview-tab',
  OVERVIEW_CARDS: 'dashboard-overview-cards',
  TRIAL_PROMOTION: 'dashboard-trial-promotion',
  
  // Credit Balance Card
  BALANCE_CARD: 'credit-dashboard-balance-card',
  CREDIT_BALANCE: 'dashboard-credit-balance',
  CREDIT_LIMIT: 'dashboard-credit-limit',
  CREDIT_PROGRESS: 'dashboard-credit-progress',
  MANAGE_SUBSCRIPTION_BUTTON: 'dashboard-manage-subscription-button',
  
  // API Keys Card
  API_KEYS_CARD: 'dashboard-api-keys-card',
  API_KEYS_TOTAL: 'dashboard-api-keys-total',
  API_KEYS_ACTIVE_COUNT: 'dashboard-api-keys-active-count',
  API_KEYS_LIST: 'dashboard-api-keys-list',
  API_KEYS_MORE_COUNT: 'dashboard-api-keys-more-count',
  MANAGE_API_KEYS_BUTTON: 'dashboard-manage-api-keys-button',
  
  // Real Usage & Scheduled Credits
  REAL_USAGE_CONTAINER: 'dashboard-real-usage-container',
  SCHEDULED_CREDITS_CONTAINER: 'dashboard-scheduled-credits-container',
  
  // Quick Actions
  ACTION_BUTTONS: 'dashboard-main-action-buttons',
  QUICK_ACTIONS_GRID: 'dashboard-quick-actions-grid',
  QUICK_ACTION_CREATE_API_KEY: 'dashboard-quick-action-create-api-key',
  QUICK_ACTION_MANAGE_SHOPS: 'dashboard-quick-action-manage-shops',
  QUICK_ACTION_UPGRADE_PLAN: 'dashboard-quick-action-upgrade-plan',
  QUICK_ACTION_SETTINGS: 'dashboard-quick-action-settings',
  
  // Usage Tab
  USAGE_TAB: 'dashboard-usage-tab',
  
  // Activity Tab
  ACTIVITY_TAB: 'dashboard-activity-tab',
  RECENT_ACTIVITY_CARD: 'dashboard-recent-activity-card',
  ACTIVITY_LIST: 'dashboard-activity-list',
  ACTIVITY_EMPTY_STATE: 'dashboard-activity-empty-state',
  VIEW_ALL_ACTIVITY_BUTTON: 'dashboard-view-all-activity-button',
  
  // API Usage
  API_USAGE_CARD: 'dashboard-api-usage-card',
  API_USAGE_LIST: 'dashboard-api-usage-list',
  API_USAGE_EMPTY_STATE: 'dashboard-api-usage-empty-state',
} as const;

test.describe('Dashboard Comprehensive Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/dashboard');
    
    // Wait for either the dashboard content or login redirect
    await page.waitForLoadState('networkidle');
  });

  test.describe('Dashboard Structure and Loading', () => {
    test('should render dashboard structure with all test IDs', async ({ page }) => {
      // Check if redirected to login (handle authentication)
      if (page.url().includes('/auth/') || page.url().includes('/signin') || page.url().includes('/login')) {
        console.log('⚠️ Dashboard requires authentication - testing structure after potential login');
        // For now, skip if authentication is required
        test.skip('Authentication required for dashboard access');
        return;
      }
      
      // Verify main container
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`)).toBeVisible();
      
      // Verify header
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.HEADER}"]`)).toBeVisible();
      
      // Verify tab navigation
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TABS}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TABS_LIST}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_OVERVIEW}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_USAGE}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_ACTIVITY}"]`)).toBeVisible();
      
      console.log('✅ Dashboard structure verified');
    });
    
    test('should handle loading states correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check if loading spinner appears briefly
      const loadingSpinner = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.LOADING_SPINNER}"]`);
      
      // Either loading should be visible initially or main content should be visible
      const isLoading = await loadingSpinner.isVisible();
      const hasMainContent = await page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`).isVisible();
      
      expect(isLoading || hasMainContent).toBeTruthy();
      
      if (isLoading) {
        // Wait for loading to complete
        await expect(loadingSpinner).not.toBeVisible({ timeout: 10000 });
        // Main content should appear after loading
        await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`)).toBeVisible();
      }
      
      console.log('✅ Loading states handled correctly');
    });
  });
  
  test.describe('Tab Navigation', () => {
    test('should navigate between tabs correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Verify Overview tab is active by default
      const overviewTab = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_OVERVIEW}"]`);
      const usageTab = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_USAGE}"]`);
      const activityTab = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_ACTIVITY}"]`);
      
      // Check overview content is visible
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.OVERVIEW_TAB}"]`)).toBeVisible();
      
      // Click Usage tab
      await usageTab.click();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.USAGE_TAB}"]`)).toBeVisible();
      
      // Click Activity tab
      await activityTab.click();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.ACTIVITY_TAB}"]`)).toBeVisible();
      
      // Go back to Overview
      await overviewTab.click();
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.OVERVIEW_TAB}"]`)).toBeVisible();
      
      console.log('✅ Tab navigation working correctly');
    });
  });
  
  test.describe('Refresh Functionality and API Integration', () => {
    test('should refresh data when refresh button is clicked', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor network requests
      const apiRequests: string[] = [];
      page.on('request', request => {
        const url = request.url();
        if (url.includes('/api/')) {
          apiRequests.push(url);
        }
      });
      
      // Click refresh button
      const refreshButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.REFRESH_BUTTON}"]`);
      await expect(refreshButton).toBeVisible();
      
      await refreshButton.click();
      
      // Button should show loading state
      await expect(refreshButton).toHaveText(/Loading.../);
      
      // Wait for refresh to complete
      await expect(refreshButton).toHaveText(/Refresh/);
      
      // Should have made API calls
      expect(apiRequests.length).toBeGreaterThan(0);
      
      console.log(`✅ Refresh triggered ${apiRequests.length} API calls:`, apiRequests);
    });
    
    test('should verify API endpoints are called correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      const expectedEndpoints = [
        '/api/v1/credits/balance',
        '/api/v1/api-keys',
        '/api/v1/usage/summary',
        '/api/v1/transactions'
      ];
      
      const apiCalls: { url: string; method: string; status?: number }[] = [];
      
      page.on('request', request => {
        const url = request.url();
        if (url.includes('/api/v1/')) {
          apiCalls.push({ url, method: request.method() });
        }
      });
      
      page.on('response', response => {
        const url = response.url();
        if (url.includes('/api/v1/')) {
          const existingCall = apiCalls.find(call => call.url === url);
          if (existingCall) {
            existingCall.status = response.status();
          }
        }
      });
      
      // Wait for initial load
      await page.waitForTimeout(2000);
      
      // Check that expected endpoints were called
      for (const endpoint of expectedEndpoints) {
        const matchingCall = apiCalls.find(call => call.url.includes(endpoint));
        if (matchingCall) {
          console.log(`✅ API call made to ${endpoint} (${matchingCall.status})`);
        } else {
          console.log(`⚠️ No API call to ${endpoint} detected`);
        }
      }
      
      console.log(`✅ Total API calls made: ${apiCalls.length}`);
    });
  });
  
  test.describe('Credit Balance Card', () => {
    test('should display credit balance information correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Verify balance card exists
      const balanceCard = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.BALANCE_CARD}"]`);
      await expect(balanceCard).toBeVisible();
      
      // Check balance display
      const balanceDisplay = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.CREDIT_BALANCE}"]`);
      await expect(balanceDisplay).toBeVisible();
      
      // Check limit display
      const limitDisplay = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.CREDIT_LIMIT}"]`);
      await expect(limitDisplay).toBeVisible();
      
      // Check progress bar
      const progressBar = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.CREDIT_PROGRESS}"]`);
      await expect(progressBar).toBeVisible();
      
      // Check manage subscription button
      const manageButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MANAGE_SUBSCRIPTION_BUTTON}"]`);
      await expect(manageButton).toBeVisible();
      
      console.log('✅ Credit balance card verified');
    });
    
    test('should navigate to subscription management when button clicked', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      const manageButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MANAGE_SUBSCRIPTION_BUTTON}"]`);
      await expect(manageButton).toBeVisible();
      
      await manageButton.click();
      
      // Should navigate to subscriptions page
      await expect(page).toHaveURL(/.*\/dashboard\/subscriptions/);
      
      console.log('✅ Subscription navigation working');
    });
  });
  
  test.describe('API Keys Card', () => {
    test('should display API keys information correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Verify API keys card exists
      const apiKeysCard = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.API_KEYS_CARD}"]`);
      await expect(apiKeysCard).toBeVisible();
      
      // Check total count
      const totalCount = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.API_KEYS_TOTAL}"]`);
      await expect(totalCount).toBeVisible();
      
      // Check active count
      const activeCount = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.API_KEYS_ACTIVE_COUNT}"]`);
      await expect(activeCount).toBeVisible();
      
      // Check if API keys list is present (may be empty)
      const apiKeysList = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.API_KEYS_LIST}"]`);
      await expect(apiKeysList).toBeVisible();
      
      // Check manage button
      const manageButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MANAGE_API_KEYS_BUTTON}"]`);
      await expect(manageButton).toBeVisible();
      
      console.log('✅ API keys card verified');
    });
    
    test('should navigate to API keys management when button clicked', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      const manageButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MANAGE_API_KEYS_BUTTON}"]`);
      await expect(manageButton).toBeVisible();
      
      await manageButton.click();
      
      // Should navigate to API keys page
      await expect(page).toHaveURL(/.*\/dashboard\/api-keys/);
      
      console.log('✅ API keys navigation working');
    });
  });
  
  test.describe('Quick Actions', () => {
    test('should display all quick action buttons', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Verify quick actions section
      const quickActionsCard = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.ACTION_BUTTONS}"]`);
      await expect(quickActionsCard).toBeVisible();
      
      const quickActionsGrid = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.QUICK_ACTIONS_GRID}"]`);
      await expect(quickActionsGrid).toBeVisible();
      
      // Check all quick action buttons
      const quickActions = [
        DASHBOARD_TEST_IDS.QUICK_ACTION_CREATE_API_KEY,
        DASHBOARD_TEST_IDS.QUICK_ACTION_MANAGE_SHOPS,
        DASHBOARD_TEST_IDS.QUICK_ACTION_UPGRADE_PLAN,
        DASHBOARD_TEST_IDS.QUICK_ACTION_SETTINGS
      ];
      
      for (const actionId of quickActions) {
        const actionButton = page.locator(`[data-testid="${actionId}"]`);
        await expect(actionButton).toBeVisible();
      }
      
      console.log('✅ All quick action buttons verified');
    });
    
    test('should navigate correctly when quick actions are clicked', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      const quickActions = [
        { testId: DASHBOARD_TEST_IDS.QUICK_ACTION_CREATE_API_KEY, expectedUrl: '/dashboard/api-keys/new' },
        { testId: DASHBOARD_TEST_IDS.QUICK_ACTION_MANAGE_SHOPS, expectedUrl: '/dashboard/shops' },
        { testId: DASHBOARD_TEST_IDS.QUICK_ACTION_UPGRADE_PLAN, expectedUrl: '/dashboard/subscriptions' },
        { testId: DASHBOARD_TEST_IDS.QUICK_ACTION_SETTINGS, expectedUrl: '/dashboard/settings' }
      ];
      
      for (const action of quickActions) {
        // Go back to dashboard
        await page.goto('/dashboard');
        await page.waitForLoadState('networkidle');
        
        if (page.url().includes('/auth/') || page.url().includes('/signin')) {
          console.log('⚠️ Authentication required for this test');
          continue;
        }
        
        const actionButton = page.locator(`[data-testid="${action.testId}"]`);
        await expect(actionButton).toBeVisible();
        
        await actionButton.click();
        
        // Verify navigation
        await expect(page).toHaveURL(new RegExp(`.*${action.expectedUrl}`));
        console.log(`✅ Quick action navigation to ${action.expectedUrl} working`);
      }
    });
  });
  
  test.describe('Activity Tab Functionality', () => {
    test('should display activity tab content correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Navigate to activity tab
      const activityTab = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TAB_ACTIVITY}"]`);
      await activityTab.click();
      
      // Verify activity tab content
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.ACTIVITY_TAB}"]`)).toBeVisible();
      
      // Check recent activity card
      const activityCard = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.RECENT_ACTIVITY_CARD}"]`);
      await expect(activityCard).toBeVisible();
      
      // Check activity list (may show empty state)
      const activityList = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.ACTIVITY_LIST}"]`);
      const emptyState = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.ACTIVITY_EMPTY_STATE}"]`);
      
      const hasActivity = await activityList.isVisible();
      const isEmpty = await emptyState.isVisible();
      
      expect(hasActivity || isEmpty).toBeTruthy();
      
      if (isEmpty) {
        console.log('✅ Activity empty state displayed correctly');
      } else {
        console.log('✅ Activity list displayed with data');
      }
      
      // Check view all button
      const viewAllButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.VIEW_ALL_ACTIVITY_BUTTON}"]`);
      await expect(viewAllButton).toBeVisible();
      
      // Check API usage card
      const apiUsageCard = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.API_USAGE_CARD}"]`);
      await expect(apiUsageCard).toBeVisible();
      
      console.log('✅ Activity tab content verified');
    });
  });
  
  test.describe('Real-time Components', () => {
    test('should verify real usage and scheduled credits components', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check real usage container
      const realUsageContainer = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.REAL_USAGE_CONTAINER}"]`);
      await expect(realUsageContainer).toBeVisible();
      
      // Check scheduled credits container
      const scheduledCreditsContainer = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.SCHEDULED_CREDITS_CONTAINER}"]`);
      await expect(scheduledCreditsContainer).toBeVisible();
      
      console.log('✅ Real-time components verified');
    });
  });
  
  test.describe('Error Handling and Edge Cases', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor for JavaScript errors
      const jsErrors: string[] = [];
      page.on('pageerror', err => {
        jsErrors.push(err.message);
      });
      
      // Monitor for failed network requests
      const failedRequests: string[] = [];
      page.on('response', response => {
        if (response.status() >= 400 && response.url().includes('/api/')) {
          failedRequests.push(`${response.status()} - ${response.url()}`);
        }
      });
      
      // Try to refresh data
      const refreshButton = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.REFRESH_BUTTON}"]`);
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
      }
      
      // Wait for any async operations
      await page.waitForTimeout(3000);
      
      // Dashboard should still be functional even with some API failures
      await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`)).toBeVisible();
      
      if (jsErrors.length > 0) {
        console.log(`⚠️ JavaScript errors detected:`, jsErrors);
      } else {
        console.log('✅ No JavaScript errors detected');
      }
      
      if (failedRequests.length > 0) {
        console.log(`⚠️ Failed API requests:`, failedRequests);
      } else {
        console.log('✅ All API requests successful');
      }
    });
    
    test('should handle trial promotion display correctly', async ({ page }) => {
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check if trial promotion appears (conditional based on credit balance)
      const trialPromotion = page.locator(`[data-testid="${DASHBOARD_TEST_IDS.TRIAL_PROMOTION}"]`);
      
      const isVisible = await trialPromotion.isVisible();
      
      if (isVisible) {
        console.log('✅ Trial promotion displayed for low-credit users');
      } else {
        console.log('✅ Trial promotion not displayed (sufficient credits)');
      }
    });
  });
  
  test.describe('Performance and Responsiveness', () => {
    test('should load dashboard within reasonable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Wait for main content to be visible
      await page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`).waitFor({ state: 'visible' });
      
      const loadTime = Date.now() - startTime;
      
      // Dashboard should load within 10 seconds
      expect(loadTime).toBeLessThan(10000);
      
      console.log(`✅ Dashboard loaded in ${loadTime}ms`);
    });
    
    test('should be responsive on different screen sizes', async ({ page }) => {
      // Skip if redirected to auth
      await page.goto('/dashboard');
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      const viewports = [
        { width: 375, height: 667, name: 'Mobile' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 1280, height: 720, name: 'Desktop' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        
        // Main content should be visible
        await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.MAIN_CONTAINER}"]`)).toBeVisible();
        
        // Quick actions should be visible
        await expect(page.locator(`[data-testid="${DASHBOARD_TEST_IDS.QUICK_ACTIONS_GRID}"]`)).toBeVisible();
        
        console.log(`✅ Dashboard responsive on ${viewport.name} (${viewport.width}x${viewport.height})`);
      }
    });
  });
});