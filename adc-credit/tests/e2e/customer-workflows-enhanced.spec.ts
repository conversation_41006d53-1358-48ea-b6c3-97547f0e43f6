/**
 * Enhanced Customer Workflows E2E Tests
 * ADC Credit Service - Comprehensive Customer Journey Testing
 * 
 * Tests complete customer workflows using the comprehensive test ID system
 * implemented in Phases 1-6. This test suite covers:
 * 
 * Test Categories:
 * - QR Code Scanning & Redemption Workflows
 * - Shop Browsing & Discovery Workflows  
 * - Manual Code Redemption Workflows
 * - Customer Navigation & Mobile Experience
 * - Error Handling & Edge Cases
 * 
 * Features the new test ID constants:
 * - CUSTOMER_SCAN_TEST_IDS
 * - CUSTOMER_SHOPS_TEST_IDS  
 * - CUSTOMER_REDEEM_TEST_IDS
 */

import { test, expect } from '@playwright/test';

// Test Group: QR Code Scanning Workflow
test.describe('Enhanced QR Code Scanning Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/customer/scan');
  });

  test('should complete QR scanning workflow with comprehensive test IDs', async ({ page }) => {
    // Verify page structure using new test IDs
    await expect(page.locator('[data-testid="customer-scan-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-breadcrumbs"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-header"]')).toBeVisible();
    
    // Verify scan card structure
    await expect(page.locator('[data-testid="customer-scan-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-card-title"]')).toHaveText('Scan Credit Code');
    await expect(page.locator('[data-testid="customer-scan-card-description"]')).toContainText('Scan a QR code to redeem credits');
    
    // Check camera permission state
    const permissionState = page.locator('[data-testid="customer-scan-camera-permission-state"]');
    if (await permissionState.isVisible()) {
      await expect(page.locator('[data-testid="customer-scan-camera-icon"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-scan-permission-message"]')).toBeVisible();
      
      // Test start scanner button
      await expect(page.locator('[data-testid="customer-scan-start-scanner-button"]')).toBeVisible();
    }
    
    // Verify troubleshooting section
    await expect(page.locator('[data-testid="customer-scan-troubleshooting-section"]')).toBeVisible();
    
    // Test manual entry navigation
    await page.locator('[data-testid="customer-scan-manual-entry-button"]').click();
    await expect(page).toHaveURL(/.*\/customer\/redeem/);
  });

  test('should handle camera permission scenarios', async ({ page }) => {
    // Mock camera permission denied
    await page.context().grantPermissions([]);
    
    const permissionState = page.locator('[data-testid="customer-scan-camera-permission-state"]');
    if (await permissionState.isVisible()) {
      // Verify permission denied actions
      const permissionActions = page.locator('[data-testid="customer-scan-permission-denied-actions"]');
      if (await permissionActions.isVisible()) {
        await expect(page.locator('[data-testid="customer-scan-help-button"]')).toBeVisible();
        await expect(page.locator('[data-testid="customer-scan-refresh-button"]')).toBeVisible();
        
        // Test help button functionality
        await page.locator('[data-testid="customer-scan-help-button"]').click();
        // Verify toast or help message appears
      }
    }
  });

  test('should handle redemption result states', async ({ page }) => {
    // Mock successful redemption
    await page.route('**/api/v1/credits/redeem', async (route) => {
      await route.fulfill({
        status: 200,
        json: { 
          success: true,
          credit_balance: 150,
          shop_name: 'Test Coffee Shop',
          message: 'Credit redeemed successfully!'
        }
      });
    });

    // Simulate successful scan (would need to mock QR scanner)
    // For now, test the UI elements that would appear
    
    // Verify result actions are available
    await expect(page.locator('[data-testid="customer-scan-scan-actions"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-manual-entry-button"]')).toBeVisible();
  });

  test('should navigate footer correctly', async ({ page }) => {
    // Test footer navigation
    await expect(page.locator('[data-testid="customer-scan-footer"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-my-credits-footer-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-scan-credit-icon"]')).toBeVisible();
    
    // Navigate to credits dashboard
    await page.locator('[data-testid="customer-scan-my-credits-footer-button"]').click();
    await expect(page).toHaveURL(/.*\/customer$/);
  });
});

// Test Group: Shop Browsing Workflow
test.describe('Enhanced Shop Browsing Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/customer/shops');
  });

  test('should complete shop browsing workflow with test IDs', async ({ page }) => {
    // Verify page structure
    await expect(page.locator('[data-testid="customer-shops-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-breadcrumbs"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-header"]')).toBeVisible();
    
    // Test search functionality
    await expect(page.locator('[data-testid="customer-shops-search-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-search-icon"]')).toBeVisible();
    
    const searchInput = page.locator('[data-testid="customer-shops-search-input"]');
    await expect(searchInput).toBeVisible();
    
    // Test search interaction
    await searchInput.fill('coffee');
    await expect(searchInput).toHaveValue('coffee');
  });

  test('should handle shops list with dynamic test IDs', async ({ page }) => {
    // Mock shops data
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({
        json: [
          { 
            id: 'shop-123', 
            slug: 'coffee-central',
            name: 'Coffee Central', 
            description: 'Premium coffee experience',
            credit_balance: 75,
            image_url: 'https://example.com/coffee.jpg'
          },
          { 
            id: 'shop-456', 
            slug: 'pizza-palace',
            name: 'Pizza Palace', 
            description: 'Authentic Italian pizza',
            credit_balance: 0
          }
        ]
      });
    });

    await page.reload();
    
    // Wait for shops list to load
    await expect(page.locator('[data-testid="customer-shops-shops-list"]')).toBeVisible();
    
    // Test dynamic shop cards using function-based test IDs
    await expect(page.locator('[data-testid="customer-shops-shop-card-shop-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-shop-card-shop-456"]')).toBeVisible();
    
    // Test shop content elements
    await expect(page.locator('[data-testid="customer-shops-shop-name-shop-123"]')).toHaveText('Coffee Central');
    await expect(page.locator('[data-testid="customer-shops-shop-description-shop-123"]')).toHaveText('Premium coffee experience');
    await expect(page.locator('[data-testid="customer-shops-shop-credits-value-shop-123"]')).toContainText('75 credits');
    
    // Test shop with no credits
    await expect(page.locator('[data-testid="customer-shops-shop-no-credits-shop-456"]')).toHaveText('No credits yet');
    
    // Test shop navigation
    await page.locator('[data-testid="customer-shops-shop-link-shop-123"]').click();
    await expect(page).toHaveURL(/.*\/customer\/shops\/coffee-central/);
  });

  test('should handle loading and empty states', async ({ page }) => {
    // Test loading state
    await page.route('**/api/customers/shops', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.fulfill({ json: [] });
    });

    await page.reload();
    
    // Check for loading state (briefly)
    const loadingState = page.locator('[data-testid="customer-shops-loading-state"]');
    
    // Wait for empty state
    await expect(page.locator('[data-testid="customer-shops-empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-empty-state-icon"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-empty-state-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-empty-state-message"]')).toBeVisible();
  });

  test('should handle search functionality and clear search', async ({ page }) => {
    // Mock search results
    await page.route('**/api/customers/shops', async (route) => {
      await route.fulfill({
        json: [
          { id: 'shop-789', slug: 'bakery-fresh', name: 'Fresh Bakery', description: 'Daily fresh bread', credit_balance: 25 }
        ]
      });
    });

    await page.reload();
    
    // Test search that returns no results
    const searchInput = page.locator('[data-testid="customer-shops-search-input"]');
    await searchInput.fill('nonexistent');
    
    // Should show empty state with clear search option
    await expect(page.locator('[data-testid="customer-shops-empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-clear-search-button"]')).toBeVisible();
    
    // Test clear search functionality
    await page.locator('[data-testid="customer-shops-clear-search-button"]').click();
    await expect(searchInput).toHaveValue('');
  });

  test('should navigate footer correctly', async ({ page }) => {
    // Test footer navigation structure
    await expect(page.locator('[data-testid="customer-shops-footer"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-navigation-bar"]')).toBeVisible();
    
    // Test home navigation
    await expect(page.locator('[data-testid="customer-shops-home-link"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-home-icon"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-home-label"]')).toHaveText('Home');
    
    // Test active shops tab
    await expect(page.locator('[data-testid="customer-shops-shops-tab-active"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-shops-icon-active"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-shops-shops-label-active"]')).toHaveText('Shops');
    
    // Navigate to home
    await page.locator('[data-testid="customer-shops-home-link"]').click();
    await expect(page).toHaveURL(/.*\/customer$/);
  });
});

// Test Group: Manual Code Redemption Workflow
test.describe('Enhanced Manual Code Redemption Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/customer/redeem');
  });

  test('should complete manual redemption workflow with test IDs', async ({ page }) => {
    // Verify page structure
    await expect(page.locator('[data-testid="customer-redeem-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-main-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-breadcrumbs"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-header"]')).toBeVisible();
    
    // Test form structure
    await expect(page.locator('[data-testid="customer-redeem-redeem-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-card-title"]')).toHaveText('Redeem a Credit Code');
    await expect(page.locator('[data-testid="customer-redeem-card-description"]')).toContainText('Enter a credit code or scan a QR code');
    
    // Test form fields
    await expect(page.locator('[data-testid="customer-redeem-form-fields"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-code-label"]')).toHaveText('Credit Code');
    await expect(page.locator('[data-testid="customer-redeem-code-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-scan-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-scan-icon"]')).toBeVisible();
  });

  test('should handle form interaction and validation', async ({ page }) => {
    const codeInput = page.locator('[data-testid="customer-redeem-code-input"]');
    const redeemButton = page.locator('[data-testid="customer-redeem-redeem-button"]');
    
    // Test input functionality
    await codeInput.fill('TEST123CODE456');
    await expect(codeInput).toHaveValue('TEST123CODE456');
    
    // Test button state changes based on input
    await expect(redeemButton).toBeEnabled();
    
    // Clear input and test disabled state
    await codeInput.clear();
    await expect(redeemButton).toBeDisabled();
  });

  test('should handle successful redemption flow', async ({ page }) => {
    // Mock successful redemption
    await page.route('**/api/v1/credits/redeem', async (route) => {
      await route.fulfill({
        status: 200,
        json: { 
          success: true,
          credit_balance: 200,
          shop_name: 'Coffee Central',
          message: 'Credit code redeemed successfully!'
        }
      });
    });

    // Fill form and submit
    await page.locator('[data-testid="customer-redeem-code-input"]').fill('VALID123CODE456');
    await page.locator('[data-testid="customer-redeem-redeem-button"]').click();
    
    // Verify success result
    await expect(page.locator('[data-testid="customer-redeem-success-result"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-result-status"]')).toHaveText('Success!');
    await expect(page.locator('[data-testid="customer-redeem-success-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-balance-info"]')).toContainText('Coffee Central');
    await expect(page.locator('[data-testid="customer-redeem-balance-info"]')).toContainText('200 credits');
  });

  test('should handle redemption errors', async ({ page }) => {
    // Mock error response
    await page.route('**/api/v1/credits/redeem', async (route) => {
      await route.fulfill({
        status: 400,
        json: { 
          error: 'Invalid or expired credit code'
        }
      });
    });

    // Fill form and submit
    await page.locator('[data-testid="customer-redeem-code-input"]').fill('INVALID123');
    await page.locator('[data-testid="customer-redeem-redeem-button"]').click();
    
    // Verify error result
    await expect(page.locator('[data-testid="customer-redeem-error-result"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-result-status"]')).toHaveText('Error');
    await expect(page.locator('[data-testid="customer-redeem-result-message"]')).toContainText('Invalid or expired');
  });

  test('should navigate to QR scanner', async ({ page }) => {
    // Test scan button navigation
    await page.locator('[data-testid="customer-redeem-scan-button"]').click();
    await expect(page).toHaveURL(/.*\/customer\/scan/);
  });

  test('should handle footer navigation', async ({ page }) => {
    // Test footer structure
    await expect(page.locator('[data-testid="customer-redeem-footer"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-navigation-bar"]')).toBeVisible();
    
    // Test back navigation
    await expect(page.locator('[data-testid="customer-redeem-back-link"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-back-icon"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-back-label"]')).toHaveText('Back');
    
    // Test active redeem tab
    await expect(page.locator('[data-testid="customer-redeem-redeem-tab-active"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-redeem-icon-active"]')).toBeVisible();
    await expect(page.locator('[data-testid="customer-redeem-redeem-label-active"]')).toHaveText('Redeem');
    
    // Navigate back
    await page.locator('[data-testid="customer-redeem-back-link"]').click();
    await expect(page).toHaveURL(/.*\/customer$/);
  });
});

// Test Group: Cross-Page Customer Navigation
test.describe('Enhanced Customer Navigation Flow', () => {
  test('should navigate complete customer journey with test IDs', async ({ page }) => {
    // Start from customer dashboard
    await page.goto('/dashboard/customer');
    
    // Navigate to shops
    await page.goto('/dashboard/customer/shops');
    await expect(page.locator('[data-testid="customer-shops-container"]')).toBeVisible();
    
    // Navigate to scan page
    await page.goto('/dashboard/customer/scan');
    await expect(page.locator('[data-testid="customer-scan-container"]')).toBeVisible();
    
    // Navigate to manual redeem from scan page
    await page.locator('[data-testid="customer-scan-manual-entry-button"]').click();
    await expect(page.locator('[data-testid="customer-redeem-container"]')).toBeVisible();
    
    // Navigate to scan from redeem page
    await page.locator('[data-testid="customer-redeem-scan-button"]').click();
    await expect(page.locator('[data-testid="customer-scan-container"]')).toBeVisible();
    
    // Return to dashboard via footer
    await page.locator('[data-testid="customer-scan-my-credits-footer-button"]').click();
    await expect(page).toHaveURL(/.*\/customer$/);
  });

  test('should maintain consistent navigation patterns', async ({ page }) => {
    const pages = [
      { url: '/dashboard/customer/scan', container: 'customer-scan-container' },
      { url: '/dashboard/customer/shops', container: 'customer-shops-container' },
      { url: '/dashboard/customer/redeem', container: 'customer-redeem-container' }
    ];

    for (const pageInfo of pages) {
      await page.goto(pageInfo.url);
      
      // Verify consistent elements exist
      await expect(page.locator(`[data-testid="${pageInfo.container}"]`)).toBeVisible();
      
      // Check for breadcrumbs (all customer pages should have them)
      const breadcrumbs = page.locator('[data-testid*="breadcrumbs"]');
      await expect(breadcrumbs).toBeVisible();
      
      // Check for footer navigation
      const footer = page.locator('[data-testid*="footer"]');
      await expect(footer).toBeVisible();
    }
  });
});

// Test Group: Responsive Customer Experience
test.describe('Enhanced Customer Mobile Experience', () => {
  ['iphone-13', 'ipad', 'desktop'].forEach(device => {
    test(`should provide optimal customer experience on ${device}`, async ({ page }) => {
      // Set viewport for device
      if (device === 'iphone-13') {
        await page.setViewportSize({ width: 390, height: 844 });
      } else if (device === 'ipad') {
        await page.setViewportSize({ width: 768, height: 1024 });
      } else {
        await page.setViewportSize({ width: 1920, height: 1080 });
      }

      // Test QR scan page responsiveness
      await page.goto('/dashboard/customer/scan');
      await expect(page.locator('[data-testid="customer-scan-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-scan-card"]')).toBeVisible();
      
      // Test shops page responsiveness
      await page.goto('/dashboard/customer/shops');
      await expect(page.locator('[data-testid="customer-shops-search-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-shops-navigation-bar"]')).toBeVisible();
      
      // Test redeem page responsiveness
      await page.goto('/dashboard/customer/redeem');
      await expect(page.locator('[data-testid="customer-redeem-code-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-redeem-redeem-button"]')).toBeVisible();
    });
  });
});