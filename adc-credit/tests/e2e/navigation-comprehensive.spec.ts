/**
 * ADC Credit Navigation E2E Tests
 * 
 * Comprehensive testing of navigation components with stable test IDs
 */

import { test, expect } from '@playwright/test';

test.describe('Navigation Component Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up console monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test.describe('Main Navbar', () => {
    test('should render all navbar elements with test IDs', async ({ page }) => {
      await page.goto('/');
      
      // Check main navbar container
      await expect(page.locator('[data-testid="layout-navbar-container"]')).toBeVisible();
      
      // Check logo
      await expect(page.locator('[data-testid="layout-navbar-logo"]')).toBeVisible();
      
      // Check desktop navigation menu
      const desktopNav = page.locator('[data-testid="layout-navbar-navigation-menu"]');
      if (await desktopNav.isVisible()) {
        console.log('✅ Desktop navigation menu visible');
      }
      
      // Check mobile menu toggle (should be visible on smaller screens)
      const mobileToggle = page.locator('[data-testid="layout-navbar-menu-toggle"]');
      if (await mobileToggle.isVisible()) {
        console.log('✅ Mobile menu toggle visible');
      }
      
      console.log('✅ Main navbar elements rendered correctly');
    });

    test('should handle mobile navigation menu', async ({ page }) => {
      // Set mobile viewport to ensure mobile menu is shown
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/');
      
      // Check if mobile menu toggle is visible
      const mobileToggle = page.locator('[data-testid="layout-navbar-menu-toggle"]');
      
      if (await mobileToggle.isVisible()) {
        // Click mobile menu toggle
        await mobileToggle.click();
        
        // Check if mobile menu content appears
        const mobileMenu = page.locator('[data-testid="layout-navbar-mobile-menu"]');
        await expect(mobileMenu).toBeVisible();
        
        console.log('✅ Mobile navigation menu opens correctly');
        
        // Try to close the menu (click outside or close button)
        await page.keyboard.press('Escape');
        await page.waitForTimeout(500);
        
        // Menu should be closed now
        if (!(await mobileMenu.isVisible())) {
          console.log('✅ Mobile navigation menu closes correctly');
        }
      } else {
        console.log('⚠️ Mobile menu toggle not visible at current viewport');
      }
    });

    test('should handle user authentication menu', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip test if redirected to auth (not authenticated)
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        console.log('⚠️ User not authenticated - skipping user menu test');
        test.skip('User authentication required');
        return;
      }
      
      // Check for user avatar/menu
      const userAvatar = page.locator('[data-testid="layout-navbar-user-avatar"]');
      
      if (await userAvatar.isVisible()) {
        // Click user avatar to open menu
        await userAvatar.click();
        
        // Check if user menu appears
        const userMenu = page.locator('[data-testid="layout-navbar-user-menu"]');
        await expect(userMenu).toBeVisible();
        
        // Check for settings link
        const settingsLink = page.locator('[data-testid="layout-navbar-settings-link"]');
        await expect(settingsLink).toBeVisible();
        
        // Check for logout button
        const logoutButton = page.locator('[data-testid="layout-navbar-logout-button"]');
        await expect(logoutButton).toBeVisible();
        
        console.log('✅ User authentication menu working correctly');
        
        // Close menu by clicking outside
        await page.click('body');
      } else {
        console.log('⚠️ User avatar not visible - may not be authenticated');
      }
    });

    test('should navigate to different sections using navbar', async ({ page }) => {
      await page.goto('/');
      
      // Test logo navigation to home
      const logo = page.locator('[data-testid="layout-navbar-logo"]');
      const currentUrl = page.url();
      
      if (currentUrl !== page.url().replace(/\/.*/, '/')) {
        await logo.click();
        await page.waitForTimeout(1000);
        
        const newUrl = page.url();
        if (newUrl.includes('/') && !newUrl.includes('/dashboard')) {
          console.log('✅ Logo navigation to home works');
        }
      }
      
      // Test navigation menu items (if authenticated)
      await page.goto('/dashboard');
      
      if (!page.url().includes('/auth/')) {
        // Try clicking on navigation items if they exist
        const navMenu = page.locator('[data-testid="layout-navbar-navigation-menu"]');
        
        if (await navMenu.isVisible()) {
          // Look for navigation links within the menu
          const dashboardLinks = page.locator('a[href*="/dashboard"]').first();
          
          if (await dashboardLinks.isVisible()) {
            await dashboardLinks.click();
            await page.waitForTimeout(1000);
            
            if (page.url().includes('/dashboard')) {
              console.log('✅ Dashboard navigation works');
            }
          }
        }
      }
    });
  });

  test.describe('Breadcrumbs Navigation', () => {
    test('should render breadcrumbs with test IDs', async ({ page }) => {
      // Navigate to a deep page to trigger breadcrumbs
      await page.goto('/dashboard/settings');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check for breadcrumbs container
      const breadcrumbsContainer = page.locator('[data-testid="layout-breadcrumbs-container"]');
      
      if (await breadcrumbsContainer.isVisible()) {
        console.log('✅ Breadcrumbs container found');
        
        // Check for breadcrumb items
        const breadcrumbItems = page.locator('[data-testid^="layout-breadcrumbs-item-"]');
        const itemCount = await breadcrumbItems.count();
        
        if (itemCount > 0) {
          console.log(`✅ Found ${itemCount} breadcrumb items`);
          
          // Check for separators
          const separators = page.locator('[data-testid^="layout-breadcrumbs-separator-"]');
          const separatorCount = await separators.count();
          
          if (separatorCount > 0) {
            console.log(`✅ Found ${separatorCount} breadcrumb separators`);
          }
          
          // Check for current page indicator
          const currentPage = page.locator('[data-testid="layout-breadcrumbs-current"]');
          if (await currentPage.isVisible()) {
            console.log('✅ Current page breadcrumb indicator found');
          }
        }
      } else {
        console.log('⚠️ Breadcrumbs not visible on this page');
      }
    });

    test('should navigate using breadcrumb links', async ({ page }) => {
      // Navigate to a page with breadcrumbs
      await page.goto('/dashboard/settings');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Wait for page to load
      await page.waitForTimeout(1000);
      
      // Look for clickable breadcrumb items (not the current page)
      const breadcrumbLinks = page.locator('[data-testid^="layout-breadcrumbs-item-"] a');
      const linkCount = await breadcrumbLinks.count();
      
      if (linkCount > 0) {
        // Click the first breadcrumb link
        const firstLink = breadcrumbLinks.first();
        const href = await firstLink.getAttribute('href');
        
        if (href) {
          await firstLink.click();
          await page.waitForTimeout(1000);
          
          // Verify navigation occurred
          if (page.url().includes(href)) {
            console.log('✅ Breadcrumb navigation works');
          } else {
            console.log(`✅ Breadcrumb clicked - navigated from settings to ${page.url()}`);
          }
        }
      } else {
        console.log('⚠️ No clickable breadcrumb links found');
      }
    });
  });

  test.describe('Page Headers', () => {
    test('should render page headers with test IDs', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check for page header container
      const pageHeader = page.locator('[data-testid="layout-page-header-container"]');
      
      if (await pageHeader.isVisible()) {
        console.log('✅ Page header container found');
        
        // Check for page title
        const pageTitle = page.locator('[data-testid="layout-page-header-title"]');
        if (await pageTitle.isVisible()) {
          const titleText = await pageTitle.textContent();
          console.log(`✅ Page title found: "${titleText}"`);
        }
        
        // Check for subtitle
        const pageSubtitle = page.locator('[data-testid="layout-page-header-subtitle"]');
        if (await pageSubtitle.isVisible()) {
          const subtitleText = await pageSubtitle.textContent();
          console.log(`✅ Page subtitle found: "${subtitleText}"`);
        }
        
        // Check for action buttons
        const actionButtons = page.locator('[data-testid="layout-page-header-action-button"]');
        if (await actionButtons.isVisible()) {
          console.log('✅ Page header action buttons found');
        }
      } else {
        console.log('⚠️ Page header not found on this page');
      }
    });

    test('should handle mobile page headers', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/dashboard/settings');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Look for mobile page header elements
      const backButton = page.locator('[data-testid="layout-page-header-back-button"]');
      
      if (await backButton.isVisible()) {
        console.log('✅ Mobile back button found');
        
        // Test back button functionality
        await backButton.click();
        await page.waitForTimeout(1000);
        
        // Should have navigated back
        console.log(`✅ Back button navigation - current URL: ${page.url()}`);
      } else {
        console.log('⚠️ Mobile back button not found');
      }
    });
  });

  test.describe('Dashboard Drawer', () => {
    test('should render dashboard drawer with test IDs', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Look for dashboard drawer trigger
      const drawerTrigger = page.locator('[data-testid="dashboard-drawer-trigger"]');
      
      if (await drawerTrigger.isVisible()) {
        console.log('✅ Dashboard drawer trigger found');
        
        // Click to open drawer
        await drawerTrigger.click();
        
        // Check for drawer container
        const drawerContainer = page.locator('[data-testid="layout-drawer-container"]');
        await expect(drawerContainer).toBeVisible();
        
        // Check for menu section
        const menuSection = page.locator('[data-testid="layout-drawer-menu-section"]');
        await expect(menuSection).toBeVisible();
        
        // Check for user section
        const userSection = page.locator('[data-testid="layout-drawer-user-section"]');
        await expect(userSection).toBeVisible();
        
        console.log('✅ Dashboard drawer opened with all sections');
        
        // Check for menu items
        const menuItems = page.locator('[data-testid^="layout-drawer-menu-item-"]');
        const itemCount = await menuItems.count();
        
        if (itemCount > 0) {
          console.log(`✅ Found ${itemCount} drawer menu items`);
          
          // Test clicking a menu item
          const firstMenuItem = menuItems.first();
          const href = await firstMenuItem.getAttribute('href');
          
          if (href) {
            await firstMenuItem.click();
            await page.waitForTimeout(1000);
            
            console.log(`✅ Drawer menu item navigation works - went to ${page.url()}`);
          }
        }
      } else {
        console.log('⚠️ Dashboard drawer trigger not found');
      }
    });
  });

  test.describe('Cross-Navigation Flow', () => {
    test('should maintain consistent navigation across different pages', async ({ page }) => {
      const pagesToTest = ['/dashboard', '/dashboard/settings'];
      
      for (const testPage of pagesToTest) {
        await page.goto(testPage);
        
        // Skip if redirected to auth
        if (page.url().includes('/auth/')) {
          console.log(`⚠️ Redirected to auth for ${testPage}`);
          continue;
        }
        
        console.log(`\n📄 Testing navigation on: ${testPage}`);
        
        // Check navbar presence
        const navbar = page.locator('[data-testid="layout-navbar-container"]');
        if (await navbar.isVisible()) {
          console.log('✅ Navbar present');
        }
        
        // Check page header presence
        const pageHeader = page.locator('[data-testid="layout-page-header-container"]');
        if (await pageHeader.isVisible()) {
          console.log('✅ Page header present');
        }
        
        // Check breadcrumbs presence (may not be on all pages)
        const breadcrumbs = page.locator('[data-testid="layout-breadcrumbs-container"]');
        if (await breadcrumbs.isVisible()) {
          console.log('✅ Breadcrumbs present');
        }
        
        await page.waitForTimeout(500);
      }
    });

    test('should handle navigation state and active indicators', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if redirected to auth
      if (page.url().includes('/auth/')) {
        test.skip('Authentication required');
        return;
      }
      
      // Check for active navigation indicators
      const activeNavItems = page.locator('[class*="active"], [class*="current"], [aria-current="page"]');
      const activeCount = await activeNavItems.count();
      
      if (activeCount > 0) {
        console.log(`✅ Found ${activeCount} active navigation indicators`);
      }
      
      // Test navigation to another page and check active state changes
      await page.goto('/dashboard/settings');
      
      if (!page.url().includes('/auth/')) {
        await page.waitForTimeout(1000);
        
        // Check if active states updated
        const newActiveItems = page.locator('[class*="active"], [class*="current"], [aria-current="page"]');
        const newActiveCount = await newActiveItems.count();
        
        console.log(`✅ Active navigation indicators updated: ${newActiveCount} items`);
      }
    });
  });

  test.describe('Responsive Navigation', () => {
    test('should adapt navigation for different screen sizes', async ({ page }) => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/dashboard');
        
        // Skip if redirected to auth
        if (page.url().includes('/auth/')) {
          console.log(`⚠️ Auth required for ${viewport.name} test`);
          continue;
        }
        
        console.log(`\n📱 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
        
        // Check navbar
        const navbar = page.locator('[data-testid="layout-navbar-container"]');
        await expect(navbar).toBeVisible();
        
        // Check responsive elements
        if (viewport.width < 768) {
          // Mobile - check for mobile toggle
          const mobileToggle = page.locator('[data-testid="layout-navbar-menu-toggle"]');
          if (await mobileToggle.isVisible()) {
            console.log('✅ Mobile navigation toggle visible');
          }
        } else {
          // Desktop/Tablet - check for desktop nav
          const desktopNav = page.locator('[data-testid="layout-navbar-navigation-menu"]');
          if (await desktopNav.isVisible()) {
            console.log('✅ Desktop navigation menu visible');
          }
        }
        
        await page.waitForTimeout(500);
      }
    });
  });
});