/**
 * ADC Credit API Integration and Form Verification Tests
 * 
 * Tests API integrations, form submissions, and data validation
 * with stable test ID selectors
 */

import { test, expect } from '@playwright/test';

// API endpoints to test
const API_ENDPOINTS = {
  CREDIT_BALANCE: '/api/v1/credits/balance',
  API_KEYS: '/api/v1/api-keys',
  USAGE_SUMMARY: '/api/v1/usage/summary',
  TRANSACTIONS: '/api/v1/transactions',
  SHOPS: '/api/v1/shops',
  USER_ME: '/api/v1/users/me',
  SUBSCRIPTIONS: '/api/v1/subscriptions',
} as const;

// Test data for form submissions
const TEST_DATA = {
  API_KEY: {
    name: 'Test API Key',
    description: 'Automated test API key',
    scopes: ['read', 'write']
  },
  SHOP: {
    name: 'Test Shop',
    description: 'Automated test shop',
    email: '<EMAIL>',
    address: '123 Test Street'
  }
} as const;

test.describe('API Integration and Form Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up API monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test.describe('API Health and Connectivity', () => {
    test('should verify all dashboard API endpoints are accessible', async ({ page }) => {
      const apiResponses: { endpoint: string; status: number; responseTime: number }[] = [];
      
      // Monitor API responses
      page.on('response', response => {
        const url = response.url();
        if (url.includes('/api/v1/')) {
          const endpoint = url.split('/api/v1/')[1].split('?')[0];
          apiResponses.push({
            endpoint: `/api/v1/${endpoint}`,
            status: response.status(),
            responseTime: Date.now() // Simplified timing
          });
        }
      });
      
      // Navigate to dashboard to trigger API calls
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        console.log('⚠️ Authentication required - testing public API endpoints');
        
        // Test health endpoint if available
        const healthResponse = await page.request.get('/api/health');
        expect(healthResponse.status()).toBeLessThan(500);
        console.log(`✅ Health endpoint status: ${healthResponse.status()}`);
        
        test.skip('Authentication required for dashboard APIs');
        return;
      }
      
      // Wait for API calls to complete
      await page.waitForTimeout(3000);
      
      // Verify essential endpoints were called and responded successfully
      const essentialEndpoints = [
        '/api/v1/credits/balance',
        '/api/v1/api-keys',
        '/api/v1/usage/summary',
        '/api/v1/transactions'
      ];
      
      for (const endpoint of essentialEndpoints) {
        const response = apiResponses.find(r => r.endpoint === endpoint);
        if (response) {
          expect(response.status).toBeLessThan(500); // No server errors
          console.log(`✅ ${endpoint}: ${response.status}`);
        } else {
          console.log(`⚠️ No response recorded for ${endpoint}`);
        }
      }
      
      console.log(`✅ Total API calls monitored: ${apiResponses.length}`);
    });
    
    test('should handle API errors gracefully', async ({ page }) => {
      // Navigate to dashboard
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor for failed requests
      const failedRequests: { url: string; status: number; error?: string }[] = [];
      
      page.on('response', response => {
        if (response.status() >= 400 && response.url().includes('/api/')) {
          failedRequests.push({
            url: response.url(),
            status: response.status()
          });
        }
      });
      
      page.on('requestfailed', request => {
        if (request.url().includes('/api/')) {
          failedRequests.push({
            url: request.url(),
            status: 0,
            error: request.failure()?.errorText
          });
        }
      });
      
      // Try to refresh data multiple times to stress test
      const refreshButton = page.locator('[data-testid="dashboard-refresh-button"]');
      
      if (await refreshButton.isVisible()) {
        for (let i = 0; i < 3; i++) {
          await refreshButton.click();
          await page.waitForTimeout(1000);
        }
      }
      
      // Dashboard should still be functional despite errors
      await expect(page.locator('[data-testid="dashboard-main-container"]')).toBeVisible();
      
      if (failedRequests.length > 0) {
        console.log(`⚠️ Failed requests detected:`, failedRequests);
        // Should still render UI even with some API failures
      } else {
        console.log('✅ All API requests successful');
      }
    });
  });
  
  test.describe('API Key Management Forms', () => {
    test('should create new API key via form submission', async ({ page }) => {
      // Navigate to API key creation page
      await page.goto('/dashboard/api-keys/new');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor API calls
      const apiCalls: { method: string; url: string; status?: number; body?: any }[] = [];
      
      page.on('request', request => {
        if (request.url().includes('/api/v1/api-keys')) {
          apiCalls.push({
            method: request.method(),
            url: request.url(),
            body: request.postDataJSON()
          });
        }
      });
      
      page.on('response', response => {
        if (response.url().includes('/api/v1/api-keys')) {
          const existingCall = apiCalls.find(call => call.url === response.url());
          if (existingCall) {
            existingCall.status = response.status();
          }
        }
      });
      
      // Check if API key creation form exists
      const nameInput = page.locator('[data-testid="api-key-create-name-input"]');
      const descriptionInput = page.locator('[data-testid="api-key-create-description-input"]');
      const createButton = page.locator('[data-testid="api-key-create-create-button"]');
      
      if (await nameInput.isVisible()) {
        // Fill form
        await nameInput.fill(TEST_DATA.API_KEY.name);
        
        if (await descriptionInput.isVisible()) {
          await descriptionInput.fill(TEST_DATA.API_KEY.description);
        }
        
        // Submit form
        await createButton.click();
        
        // Wait for API call
        await page.waitForTimeout(2000);
        
        // Verify API call was made
        const createCall = apiCalls.find(call => call.method === 'POST');
        if (createCall) {
          console.log(`✅ API key creation API call made: ${createCall.method} ${createCall.url}`);
          console.log(`✅ Status: ${createCall.status}`);
          
          if (createCall.body) {
            expect(createCall.body.name).toBe(TEST_DATA.API_KEY.name);
            console.log('✅ Form data submitted correctly');
          }
        } else {
          console.log('⚠️ No API key creation call detected');
        }
      } else {
        console.log('⚠️ API key creation form not found - may require different navigation');
      }
    });
    
    test('should validate API key form inputs', async ({ page }) => {
      await page.goto('/dashboard/api-keys/new');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Try to find form elements
      const nameInput = page.locator('[data-testid="api-key-create-name-input"]');
      const createButton = page.locator('[data-testid="api-key-create-create-button"]');
      
      if (await nameInput.isVisible() && await createButton.isVisible()) {
        // Try to submit empty form
        await createButton.click();
        
        // Should show validation error
        const errorMessage = page.locator('[data-testid="api-key-create-error-message"]');
        const isErrorVisible = await errorMessage.isVisible();
        
        if (isErrorVisible) {
          console.log('✅ Form validation working - error shown for empty form');
        } else {
          console.log('⚠️ No validation error detected - may have different validation approach');
        }
        
        // Fill with valid data
        await nameInput.fill(TEST_DATA.API_KEY.name);
        
        // Form should be submittable now
        await expect(createButton).toBeEnabled();
        console.log('✅ Form enables after valid input');
      } else {
        console.log('⚠️ API key form elements not found');
      }
    });
  });
  
  test.describe('Shop Management Forms', () => {
    test('should create new shop via form submission', async ({ page }) => {
      // Navigate to shop creation page
      await page.goto('/dashboard/shops/create');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor shop creation API calls
      const shopApiCalls: { method: string; url: string; status?: number; body?: any }[] = [];
      
      page.on('request', request => {
        if (request.url().includes('/api/v1/shops')) {
          shopApiCalls.push({
            method: request.method(),
            url: request.url(),
            body: request.postDataJSON()
          });
        }
      });
      
      page.on('response', response => {
        if (response.url().includes('/api/v1/shops')) {
          const existingCall = shopApiCalls.find(call => call.url === response.url());
          if (existingCall) {
            existingCall.status = response.status();
          }
        }
      });
      
      // Check if shop creation form exists
      const nameInput = page.locator('[data-testid="shop-create-name-input"]');
      const descriptionInput = page.locator('[data-testid="shop-create-description-input"]');
      const emailInput = page.locator('[data-testid="shop-create-email-input"]');
      const submitButton = page.locator('[data-testid="shop-create-submit-button"]');
      
      if (await nameInput.isVisible()) {
        // Fill shop form
        await nameInput.fill(TEST_DATA.SHOP.name);
        
        if (await descriptionInput.isVisible()) {
          await descriptionInput.fill(TEST_DATA.SHOP.description);
        }
        
        if (await emailInput.isVisible()) {
          await emailInput.fill(TEST_DATA.SHOP.email);
        }
        
        // Submit form
        await submitButton.click();
        
        // Wait for API call
        await page.waitForTimeout(2000);
        
        // Verify API call was made
        const createCall = shopApiCalls.find(call => call.method === 'POST');
        if (createCall) {
          console.log(`✅ Shop creation API call made: ${createCall.method} ${createCall.url}`);
          console.log(`✅ Status: ${createCall.status}`);
          
          if (createCall.body) {
            expect(createCall.body.name).toBe(TEST_DATA.SHOP.name);
            console.log('✅ Shop form data submitted correctly');
          }
        } else {
          console.log('⚠️ No shop creation call detected');
        }
      } else {
        console.log('⚠️ Shop creation form not found - may require different navigation');
      }
    });
  });
  
  test.describe('Settings and Preferences Forms', () => {
    test('should handle settings form submissions', async ({ page }) => {
      await page.goto('/dashboard/settings');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Look for settings forms
      const settingsForm = page.locator('[data-testid="settings-user-form"]');
      const saveButton = page.locator('[data-testid="settings-user-save-button"]');
      
      if (await settingsForm.isVisible() && await saveButton.isVisible()) {
        // Monitor settings API calls
        const settingsApiCalls: string[] = [];
        
        page.on('request', request => {
          if (request.url().includes('/api/v1/users') || request.url().includes('/api/v1/settings')) {
            settingsApiCalls.push(`${request.method()} ${request.url()}`);
          }
        });
        
        // Try to save settings
        await saveButton.click();
        
        await page.waitForTimeout(1000);
        
        if (settingsApiCalls.length > 0) {
          console.log('✅ Settings API calls made:', settingsApiCalls);
        } else {
          console.log('⚠️ No settings API calls detected');
        }
      } else {
        console.log('⚠️ Settings form not found');
      }
    });
  });
  
  test.describe('Data Persistence and State Management', () => {
    test('should maintain data consistency across page refreshes', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Get initial credit balance
      const balanceElement = page.locator('[data-testid="dashboard-credit-balance"]');
      
      if (await balanceElement.isVisible()) {
        const initialBalance = await balanceElement.textContent();
        
        // Refresh page
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        // Check balance after refresh
        const refreshedBalance = await balanceElement.textContent();
        
        expect(refreshedBalance).toBe(initialBalance);
        console.log('✅ Credit balance consistent after refresh');
      } else {
        console.log('⚠️ Credit balance not visible for comparison');
      }
    });
    
    test('should handle concurrent API requests correctly', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor concurrent requests
      const concurrentRequests: string[] = [];
      
      page.on('request', request => {
        if (request.url().includes('/api/v1/')) {
          concurrentRequests.push(request.url());
        }
      });
      
      // Trigger multiple refreshes quickly
      const refreshButton = page.locator('[data-testid="dashboard-refresh-button"]');
      
      if (await refreshButton.isVisible()) {
        // Click refresh multiple times
        await refreshButton.click();
        await refreshButton.click();
        await refreshButton.click();
        
        await page.waitForTimeout(3000);
        
        console.log(`✅ Handled ${concurrentRequests.length} concurrent requests`);
        
        // Dashboard should still be functional
        await expect(page.locator('[data-testid="dashboard-main-container"]')).toBeVisible();
      }
    });
  });
  
  test.describe('Authentication and Authorization', () => {
    test('should handle expired sessions gracefully', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Clear all cookies to simulate expired session
      await page.context().clearCookies();
      
      // Try to refresh data
      const refreshButton = page.locator('[data-testid="dashboard-refresh-button"]');
      
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
        
        // Should either redirect to login or show appropriate error
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        if (currentUrl.includes('/auth/') || currentUrl.includes('/signin') || currentUrl.includes('/login')) {
          console.log('✅ Correctly redirected to authentication after session expiry');
        } else {
          console.log('⚠️ No redirect detected - may have different session handling');
        }
      }
    });
    
    test('should include proper authorization headers in API requests', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor request headers
      const requestHeaders: { url: string; headers: any }[] = [];
      
      page.on('request', request => {
        if (request.url().includes('/api/v1/')) {
          requestHeaders.push({
            url: request.url(),
            headers: request.headers()
          });
        }
      });
      
      // Trigger API calls
      const refreshButton = page.locator('[data-testid="dashboard-refresh-button"]');
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
      }
      
      await page.waitForTimeout(2000);
      
      // Check for authorization headers
      const hasAuthHeaders = requestHeaders.some(req => 
        req.headers.authorization || req.headers['x-api-key'] || req.headers.cookie
      );
      
      if (hasAuthHeaders) {
        console.log('✅ API requests include authorization headers');
      } else {
        console.log('⚠️ No authorization headers detected in API requests');
      }
      
      console.log(`✅ Monitored ${requestHeaders.length} API requests for auth headers`);
    });
  });
  
  test.describe('Real-time Updates and WebSocket Integration', () => {
    test('should handle real-time data updates if available', async ({ page }) => {
      await page.goto('/dashboard');
      
      // Skip if authentication required
      if (page.url().includes('/auth/') || page.url().includes('/signin')) {
        test.skip('Authentication required');
        return;
      }
      
      // Monitor WebSocket connections
      const wsConnections: string[] = [];
      
      page.on('websocket', ws => {
        wsConnections.push(ws.url());
        console.log('WebSocket connection:', ws.url());
      });
      
      // Wait for potential WebSocket connections
      await page.waitForTimeout(3000);
      
      if (wsConnections.length > 0) {
        console.log(`✅ WebSocket connections established: ${wsConnections.length}`);
      } else {
        console.log('⚠️ No WebSocket connections detected - may use polling instead');
      }
      
      // Check if real-time components are present
      const realUsageContainer = page.locator('[data-testid="dashboard-real-usage-container"]');
      await expect(realUsageContainer).toBeVisible();
      
      console.log('✅ Real-time usage components are present');
    });
  });
});