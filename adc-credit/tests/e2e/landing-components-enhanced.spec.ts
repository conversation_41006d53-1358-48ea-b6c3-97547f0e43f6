/**
 * Enhanced Landing Components E2E Tests
 * ADC Credit Service - Marketing Landing Page Components Testing
 * 
 * Tests landing page components using the comprehensive test ID system
 * implemented in Phase 6. This test suite covers:
 * 
 * Test Categories:
 * - Hero Section Interactive Elements & CTAs
 * - Features Section Dynamic Content & Grid
 * - Call-to-Action Section Conversion Flow
 * - Cross-Component Integration & User Flow
 * - Responsive Design & Performance
 * 
 * Features the new test ID constants:
 * - HERO_SECTION_TEST_IDS
 * - FEATURES_SECTION_TEST_IDS
 * - CTA_SECTION_TEST_IDS
 */

import { test, expect } from '@playwright/test';

// Test Group: Hero Section Components
test.describe('Enhanced Hero Section Component', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should render hero section with comprehensive test IDs', async ({ page }) => {
    // Verify hero section structure
    await expect(page.locator('[data-testid="hero-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-wrapper"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-hero-card"]')).toBeVisible();
    
    // Test background and visual elements
    await expect(page.locator('[data-testid="hero-section-background-overlay"]')).toBeVisible();
    
    // Test content section
    await expect(page.locator('[data-testid="hero-section-content-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-title"]')).toHaveText('Illuminate Your API Management');
    await expect(page.locator('[data-testid="hero-section-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-description"]')).toContainText('Streamline your API operations');
  });

  test('should have functional CTA buttons with test IDs', async ({ page }) => {
    // Test CTA buttons container
    await expect(page.locator('[data-testid="hero-section-cta-buttons"]')).toBeVisible();
    
    // Test Get Started button
    const getStartedButton = page.locator('[data-testid="hero-section-get-started-button"]');
    await expect(getStartedButton).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-get-started-text"]')).toHaveText('Get Started');
    
    // Test Learn More button
    const learnMoreButton = page.locator('[data-testid="hero-section-learn-more-button"]');
    await expect(learnMoreButton).toBeVisible();
    await expect(page.locator('[data-testid="hero-section-learn-more-text"]')).toHaveText('Learn More');
    
    // Test button interactions
    await expect(getStartedButton).toBeEnabled();
    await expect(learnMoreButton).toBeEnabled();
  });

  test('should handle CTA button clicks and navigation', async ({ page }) => {
    // Test Get Started button functionality
    const getStartedButton = page.locator('[data-testid="hero-section-get-started-button"]');
    
    // Note: In a real implementation, these buttons should navigate somewhere
    // For now, we test that they're clickable
    await getStartedButton.hover();
    await expect(getStartedButton).toBeVisible();
    
    // Test Learn More button functionality
    const learnMoreButton = page.locator('[data-testid="hero-section-learn-more-button"]');
    await learnMoreButton.hover();
    await expect(learnMoreButton).toBeVisible();
  });

  test('should have proper visual hierarchy and styling', async ({ page }) => {
    // Test title prominence
    const title = page.locator('[data-testid="hero-section-title"]');
    await expect(title).toBeVisible();
    
    // Test description readability
    const description = page.locator('[data-testid="hero-section-description"]');
    await expect(description).toBeVisible();
    
    // Test CTA button prominence
    const ctaButtons = page.locator('[data-testid="hero-section-cta-buttons"]');
    await expect(ctaButtons).toBeVisible();
  });
});

// Test Group: Features Section Components
test.describe('Enhanced Features Section Component', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should render features section with comprehensive test IDs', async ({ page }) => {
    // Verify features section structure
    await expect(page.locator('[data-testid="features-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="features-section-header-section"]')).toBeVisible();
    
    // Test header content
    await expect(page.locator('[data-testid="features-section-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="features-section-title"]')).toHaveText('Complete Shop Credit Solution');
    await expect(page.locator('[data-testid="features-section-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="features-section-description"]')).toContainText('Everything you need to run');
    
    // Test features grid
    await expect(page.locator('[data-testid="features-section-features-grid"]')).toBeVisible();
  });

  test('should display feature cards with dynamic test IDs', async ({ page }) => {
    // Test that feature cards are present (assuming 4 features based on the component)
    const expectedFeatures = [0, 1, 2, 3]; // Based on features array in component
    
    for (const index of expectedFeatures) {
      // Test feature card structure
      await expect(page.locator(`[data-testid="features-section-feature-card-${index}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="features-section-feature-icon-${index}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="features-section-feature-content-${index}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="features-section-feature-title-${index}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="features-section-feature-description-${index}"]`)).toBeVisible();
    }
  });

  test('should have specific feature content', async ({ page }) => {
    // Test specific feature titles (based on the component)
    await expect(page.locator('[data-testid="features-section-feature-title-0"]')).toHaveText('Shop Management');
    await expect(page.locator('[data-testid="features-section-feature-title-1"]')).toHaveText('QR Code Payments');
    await expect(page.locator('[data-testid="features-section-feature-title-2"]')).toHaveText('Customer Management');
    await expect(page.locator('[data-testid="features-section-feature-title-3"]')).toHaveText('Analytics & Insights');
    
    // Test feature descriptions contain relevant content
    await expect(page.locator('[data-testid="features-section-feature-description-0"]')).toContainText('Create and manage multiple shops');
    await expect(page.locator('[data-testid="features-section-feature-description-1"]')).toContainText('Generate secure QR codes');
    await expect(page.locator('[data-testid="features-section-feature-description-2"]')).toContainText('Track customer interactions');
    await expect(page.locator('[data-testid="features-section-feature-description-3"]')).toContainText('Comprehensive analytics');
  });

  test('should handle feature card interactions', async ({ page }) => {
    // Test hover effects and interactions
    for (let i = 0; i < 4; i++) {
      const featureCard = page.locator(`[data-testid="features-section-feature-card-${i}"]`);
      await featureCard.hover();
      await expect(featureCard).toBeVisible();
      
      // Test that icons and content remain visible during interaction
      await expect(page.locator(`[data-testid="features-section-feature-icon-${i}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="features-section-feature-title-${i}"]`)).toBeVisible();
    }
  });

  test('should maintain grid layout integrity', async ({ page }) => {
    // Test that all feature cards are properly laid out
    const featuresGrid = page.locator('[data-testid="features-section-features-grid"]');
    await expect(featuresGrid).toBeVisible();
    
    // Test that all 4 feature cards are present
    const featureCards = page.locator('[data-testid^="features-section-feature-card-"]');
    await expect(featureCards).toHaveCount(4);
  });
});

// Test Group: Call-to-Action Section Components
test.describe('Enhanced Call-to-Action Section Component', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should render CTA section with comprehensive test IDs', async ({ page }) => {
    // Verify CTA section structure
    await expect(page.locator('[data-testid="cta-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-background-overlay"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-content-section"]')).toBeVisible();
    
    // Test content elements
    await expect(page.locator('[data-testid="cta-section-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-title"]')).toHaveText('Ready to Transform Your Business?');
    await expect(page.locator('[data-testid="cta-section-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-description"]')).toContainText('Join businesses worldwide');
  });

  test('should have functional CTA elements with test IDs', async ({ page }) => {
    // Test CTA container and elements
    await expect(page.locator('[data-testid="cta-section-cta-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-cta-link"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-cta-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-cta-text"]')).toHaveText('Start Your Shop Today');
  });

  test('should handle CTA button interaction and navigation', async ({ page }) => {
    // Test CTA button functionality
    const ctaButton = page.locator('[data-testid="cta-section-cta-button"]');
    await expect(ctaButton).toBeVisible();
    await expect(ctaButton).toBeEnabled();
    
    // Test button hover state
    await ctaButton.hover();
    await expect(ctaButton).toBeVisible();
    
    // Test navigation (assuming it should go to dashboard)
    const ctaLink = page.locator('[data-testid="cta-section-cta-link"]');
    await expect(ctaLink).toHaveAttribute('href', '/dashboard');
  });

  test('should have proper visual emphasis and styling', async ({ page }) => {
    // Test that CTA section stands out visually
    const ctaContainer = page.locator('[data-testid="cta-section-container"]');
    await expect(ctaContainer).toBeVisible();
    
    // Test background overlay for visual depth
    await expect(page.locator('[data-testid="cta-section-background-overlay"]')).toBeVisible();
    
    // Test content hierarchy
    const title = page.locator('[data-testid="cta-section-title"]');
    const description = page.locator('[data-testid="cta-section-description"]');
    const button = page.locator('[data-testid="cta-section-cta-button"]');
    
    await expect(title).toBeVisible();
    await expect(description).toBeVisible();
    await expect(button).toBeVisible();
  });
});

// Test Group: Landing Page Component Integration
test.describe('Enhanced Landing Components Integration', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display all landing components in proper order', async ({ page }) => {
    // Test that all major components are present on the page
    await expect(page.locator('[data-testid="hero-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="features-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-container"]')).toBeVisible();
    
    // Test visual hierarchy - hero should be at top
    const heroSection = page.locator('[data-testid="hero-section-container"]');
    const featuresSection = page.locator('[data-testid="features-section-container"]');
    const ctaSection = page.locator('[data-testid="cta-section-container"]');
    
    // Verify components are visible
    await expect(heroSection).toBeVisible();
    await expect(featuresSection).toBeVisible();
    await expect(ctaSection).toBeVisible();
  });

  test('should maintain consistent design language across components', async ({ page }) => {
    // Test that buttons across components follow similar patterns
    const heroButton = page.locator('[data-testid="hero-section-get-started-button"]');
    const ctaButton = page.locator('[data-testid="cta-section-cta-button"]');
    
    await expect(heroButton).toBeVisible();
    await expect(ctaButton).toBeVisible();
    
    // Both should be interactive
    await expect(heroButton).toBeEnabled();
    await expect(ctaButton).toBeEnabled();
  });

  test('should create cohesive user journey from hero to CTA', async ({ page }) => {
    // Test the flow: Hero → Features → CTA
    
    // Start with hero visibility
    await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
    
    // Scroll to features
    await page.locator('[data-testid="features-section-container"]').scrollIntoViewIfNeeded();
    await expect(page.locator('[data-testid="features-section-title"]')).toBeVisible();
    
    // Scroll to CTA
    await page.locator('[data-testid="cta-section-container"]').scrollIntoViewIfNeeded();
    await expect(page.locator('[data-testid="cta-section-title"]')).toBeVisible();
    
    // Test final conversion point
    const finalCTA = page.locator('[data-testid="cta-section-cta-button"]');
    await expect(finalCTA).toBeVisible();
  });

  test('should handle cross-component navigation consistently', async ({ page }) => {
    // Test that all major CTA buttons lead to dashboard
    const heroCTA = page.locator('[data-testid="hero-section-get-started-button"]');
    const finalCTA = page.locator('[data-testid="cta-section-cta-link"]');
    
    // Both should navigate to dashboard (or appropriate conversion page)
    await expect(heroCTA).toBeVisible();
    await expect(finalCTA).toHaveAttribute('href', '/dashboard');
  });
});

// Test Group: Landing Components Responsive Design
test.describe('Enhanced Landing Components Responsive Design', () => {
  ['mobile', 'tablet', 'desktop', 'large-desktop'].forEach(device => {
    test(`should render landing components optimally on ${device}`, async ({ page }) => {
      // Set viewport for device
      const viewports = {
        'mobile': { width: 375, height: 667 },
        'tablet': { width: 768, height: 1024 },
        'desktop': { width: 1440, height: 900 },
        'large-desktop': { width: 1920, height: 1080 }
      };
      
      await page.setViewportSize(viewports[device]);
      await page.goto('/');

      // Test Hero Section responsiveness
      await expect(page.locator('[data-testid="hero-section-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="hero-section-cta-buttons"]')).toBeVisible();
      
      // Test Features Section responsiveness
      await expect(page.locator('[data-testid="features-section-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="features-section-features-grid"]')).toBeVisible();
      
      // On mobile, features should stack vertically but still be visible
      await expect(page.locator('[data-testid="features-section-feature-card-0"]')).toBeVisible();
      await expect(page.locator('[data-testid="features-section-feature-card-1"]')).toBeVisible();
      
      // Test CTA Section responsiveness
      await expect(page.locator('[data-testid="cta-section-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="cta-section-cta-button"]')).toBeVisible();
    });
  });

  test('should maintain readability across all device sizes', async ({ page }) => {
    const devices = [
      { width: 320, height: 568 }, // Small mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1920, height: 1080 }  // Large desktop
    ];

    for (const viewport of devices) {
      await page.setViewportSize(viewport);
      await page.goto('/');
      
      // Test text readability
      await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="hero-section-description"]')).toBeVisible();
      await expect(page.locator('[data-testid="features-section-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="cta-section-title"]')).toBeVisible();
      
      // Test button accessibility
      await expect(page.locator('[data-testid="hero-section-get-started-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="cta-section-cta-button"]')).toBeVisible();
    }
  });
});

// Test Group: Landing Components Performance
test.describe('Enhanced Landing Components Performance', () => {
  test('should load landing components efficiently', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/');
    
    // Wait for all major components to be visible
    await expect(page.locator('[data-testid="hero-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="features-section-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="cta-section-container"]')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('should handle smooth scrolling between components', async ({ page }) => {
    await page.goto('/');
    
    // Test scrolling behavior
    await page.locator('[data-testid="features-section-container"]').scrollIntoViewIfNeeded();
    await expect(page.locator('[data-testid="features-section-title"]')).toBeVisible();
    
    await page.locator('[data-testid="cta-section-container"]').scrollIntoViewIfNeeded();
    await expect(page.locator('[data-testid="cta-section-title"]')).toBeVisible();
    
    // Return to top
    await page.locator('[data-testid="hero-section-container"]').scrollIntoViewIfNeeded();
    await expect(page.locator('[data-testid="hero-section-title"]')).toBeVisible();
  });

  test('should maintain interactivity during scroll', async ({ page }) => {
    await page.goto('/');
    
    // Test that buttons remain clickable during and after scroll
    const heroButton = page.locator('[data-testid="hero-section-get-started-button"]');
    await expect(heroButton).toBeEnabled();
    
    // Scroll to features
    await page.locator('[data-testid="features-section-container"]').scrollIntoViewIfNeeded();
    
    // Scroll to CTA
    await page.locator('[data-testid="cta-section-container"]').scrollIntoViewIfNeeded();
    const ctaButton = page.locator('[data-testid="cta-section-cta-button"]');
    await expect(ctaButton).toBeEnabled();
    
    // Buttons should still be interactive
    await ctaButton.hover();
    await expect(ctaButton).toBeVisible();
  });
});