import { test, expect } from "@playwright/test";

test("i18n integration test", async ({ page }) => {
  // Go to the landing page
  await page.goto("/");
  
  // Wait for page to load
  await page.waitForLoadState("networkidle");
  
  // Check if language switcher is present
  const languageButton = page.locator("text=EN");
  await expect(languageButton).toBeVisible();
  
  console.log("✅ Language switcher is visible");
  
  // Test direct API call to translation endpoint
  const response = await page.request.get("/api/translations/ui/test_key/en");
  expect(response.status()).toBe(200);
  
  const data = await response.json();
  expect(data.success).toBe(true);
  
  console.log("✅ Translation API endpoint working");
  console.log("✅ i18n integration test passed\!");
});
