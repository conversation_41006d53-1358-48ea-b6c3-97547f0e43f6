/**
 * ADC Credit Shop Management E2E Tests
 * 
 * Comprehensive testing of shop management features with stable test IDs
 */

import { test, expect } from '@playwright/test';

test.describe('Shop Management Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up console monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test.describe('Shops Listing Page', () => {
    test('should render shops listing page with test IDs', async ({ page }) => {
      await page.goto('/dashboard/shops');
      
      // Check main container
      await expect(page.locator('[data-testid="credit-shops-list-container"]')).toBeVisible();
      
      // Check page title and description
      await expect(page.locator('[data-testid="shops-page-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="shops-page-description"]')).toBeVisible();
      
      // Check create button
      await expect(page.locator('[data-testid="credit-shops-create-button"]')).toBeVisible();
      
      console.log('✅ Shops listing page elements rendered correctly');
    });

    test('should handle empty state with proper test IDs', async ({ page }) => {
      await page.goto('/dashboard/shops');
      
      // Wait for loading to complete
      await page.waitForTimeout(2000);
      
      // Check for empty state (if user has no shops)
      const emptyState = page.locator('[data-testid="shops-empty-state"]');
      const shopsGrid = page.locator('[data-testid="shops-grid"]');
      
      if (await emptyState.isVisible()) {
        // Verify empty state elements
        await expect(page.locator('[data-testid="shops-empty-icon"]')).toBeVisible();
        await expect(page.locator('[data-testid="shops-empty-title"]')).toBeVisible();
        await expect(page.locator('[data-testid="shops-empty-description"]')).toBeVisible();
        await expect(page.locator('[data-testid="shops-empty-create-button"]')).toBeVisible();
        
        console.log('✅ Empty state elements rendered correctly');
      } else if (await shopsGrid.isVisible()) {
        // Verify shops grid
        const shopCards = page.locator('[data-testid^="credit-shop-card-"]');
        const cardCount = await shopCards.count();
        
        if (cardCount > 0) {
          console.log(`✅ Found ${cardCount} shop cards in grid`);
          
          // Check first shop card elements
          const firstCard = shopCards.first();
          const shopSlug = await firstCard.getAttribute('data-testid');
          const slug = shopSlug?.replace('credit-shop-card-', '') || '';
          
          if (slug) {
            await expect(page.locator(`[data-testid="shops-card-title-${slug}"]`)).toBeVisible();
            await expect(page.locator(`[data-testid="shops-card-description-${slug}"]`)).toBeVisible();
            await expect(page.locator(`[data-testid="shops-card-content-${slug}"]`)).toBeVisible();
            
            console.log(`✅ Shop card elements verified for: ${slug}`);
          }
        }
      }
    });

    test('should handle loading state with test IDs', async ({ page }) => {
      // Simulate slow network to catch loading state
      await page.route('**/api/v1/shops*', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        await route.continue();
      });
      
      await page.goto('/dashboard/shops');
      
      // Check for loading skeleton
      const loadingSkeleton = page.locator('[data-testid="shops-loading-skeleton"]');
      
      if (await loadingSkeleton.isVisible()) {
        // Check loading cards
        const loadingCards = page.locator('[data-testid^="shops-loading-card-"]');
        const loadingCount = await loadingCards.count();
        
        expect(loadingCount).toBeGreaterThan(0);
        console.log(`✅ Loading skeleton with ${loadingCount} cards displayed`);
      }
      
      // Wait for loading to complete
      await page.waitForTimeout(2000);
      await expect(loadingSkeleton).not.toBeVisible();
      
      console.log('✅ Loading state handled correctly');
    });

    test('should navigate to shop creation when create button clicked', async ({ page }) => {
      await page.goto('/dashboard/shops');
      
      // Click create button
      const createButton = page.locator('[data-testid="credit-shops-create-button"]');
      await createButton.click();
      
      // Should navigate to merchant shops page (as per current implementation)
      await page.waitForTimeout(1000);
      
      // Check if URL changed
      const currentUrl = page.url();
      expect(currentUrl).toContain('/dashboard/merchant');
      
      console.log('✅ Create button navigation works');
    });
  });

  test.describe('Shop Creation Form', () => {
    test('should render shop creation form with all test IDs', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Check main container
      await expect(page.locator('[data-testid="shop-create-form-container"]')).toBeVisible();
      
      // Check page header elements
      await expect(page.locator('[data-testid="shop-create-page-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-page-description"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-back-button"]')).toBeVisible();
      
      // Check form elements
      await expect(page.locator('[data-testid="shop-create-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-name-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-description-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-type-select"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-phone-input"]')).toBeVisible();
      
      // Check action buttons
      await expect(page.locator('[data-testid="shop-create-submit-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="shop-create-cancel-button"]')).toBeVisible();
      
      console.log('✅ Shop creation form elements rendered correctly');
    });

    test('should validate required fields', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Try to submit without filling required fields
      const submitButton = page.locator('[data-testid="shop-create-submit-button"]');
      await submitButton.click();
      
      // Check for form validation (HTML5 validation or custom validation)
      const nameInput = page.locator('[data-testid="shop-create-name-input"]');
      const isInvalid = await nameInput.evaluate((input: HTMLInputElement) => !input.validity.valid);
      
      if (isInvalid) {
        console.log('✅ Form validation working for required fields');
      }
      
      // Fill name field and check validation clears
      await nameInput.fill('Test Shop');
      
      console.log('✅ Form validation tested');
    });

    test('should handle shop type selection', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Click shop type selector
      const typeSelect = page.locator('[data-testid="shop-create-type-select"]');
      await typeSelect.click();
      
      // Check for type options
      const retailOption = page.locator('[data-testid="shop-create-type-option-retail"]');
      const apiServiceOption = page.locator('[data-testid="shop-create-type-option-api_service"]');
      const enterpriseOption = page.locator('[data-testid="shop-create-type-option-enterprise"]');
      
      if (await retailOption.isVisible()) {
        await retailOption.click();
        console.log('✅ Retail shop type option works');
        
        // Check for type description
        const typeDescription = page.locator('[data-testid="shop-create-type-description"]');
        if (await typeDescription.isVisible()) {
          console.log('✅ Shop type description displayed');
        }
      }
    });

    test('should handle form submission with loading state', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Fill out the form
      await page.locator('[data-testid="shop-create-name-input"]').fill('Test E2E Shop');
      await page.locator('[data-testid="shop-create-description-input"]').fill('Test shop description');
      await page.locator('[data-testid="shop-create-email-input"]').fill('<EMAIL>');
      await page.locator('[data-testid="shop-create-phone-input"]').fill('************');
      
      // Mock the API response to simulate slow submission
      await page.route('**/api/v1/shops*', async route => {
        if (route.request().method() === 'POST') {
          await new Promise(resolve => setTimeout(resolve, 1000));
          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              data: {
                id: '123',
                slug: 'test-e2e-shop',
                name: 'Test E2E Shop'
              }
            })
          });
        } else {
          await route.continue();
        }
      });
      
      // Submit form
      const submitButton = page.locator('[data-testid="shop-create-submit-button"]');
      await submitButton.click();
      
      // Check for loading state
      const loadingSpinner = page.locator('[data-testid="shop-create-loading-spinner"]');
      if (await loadingSpinner.isVisible()) {
        console.log('✅ Loading state displayed during submission');
      }
      
      // Wait for submission to complete
      await page.waitForTimeout(2000);
      
      console.log('✅ Form submission handling tested');
    });

    test('should handle back button navigation', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Click back button
      const backButton = page.locator('[data-testid="shop-create-back-button"]');
      await backButton.click();
      
      // Should navigate back
      await page.waitForTimeout(1000);
      
      const currentUrl = page.url();
      expect(currentUrl).not.toContain('/create');
      
      console.log('✅ Back button navigation works');
    });

    test('should handle cancel button', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Click cancel button
      const cancelButton = page.locator('[data-testid="shop-create-cancel-button"]');
      await cancelButton.click();
      
      // Should navigate back
      await page.waitForTimeout(1000);
      
      const currentUrl = page.url();
      expect(currentUrl).not.toContain('/create');
      
      console.log('✅ Cancel button navigation works');
    });

    test('should handle form error states', async ({ page }) => {
      await page.goto('/dashboard/shops/create');
      
      // Mock API error response
      await page.route('**/api/v1/shops*', async route => {
        if (route.request().method() === 'POST') {
          await route.fulfill({
            status: 400,
            contentType: 'application/json',
            body: JSON.stringify({
              error: 'Shop name already exists'
            })
          });
        } else {
          await route.continue();
        }
      });
      
      // Fill and submit form
      await page.locator('[data-testid="shop-create-name-input"]').fill('Existing Shop');
      const submitButton = page.locator('[data-testid="shop-create-submit-button"]');
      await submitButton.click();
      
      // Wait for error message
      await page.waitForTimeout(1000);
      
      // Check for error message
      const errorMessage = page.locator('[data-testid="shop-create-error-message"]');
      if (await errorMessage.isVisible()) {
        console.log('✅ Error state displayed correctly');
        
        const errorText = await errorMessage.textContent();
        expect(errorText).toContain('Shop name already exists');
      }
    });
  });

  test.describe('Merchant Shops Tab', () => {
    test('should render merchant shops tab with test IDs', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      
      // Look for merchant shops tab content
      const shopsTabContent = page.locator('[data-testid="merchant-shops-tab-content"]');
      const shopsCard = page.locator('[data-testid="merchant-shops-card"]');
      
      if (await shopsTabContent.isVisible()) {
        await expect(page.locator('[data-testid="merchant-shops-title"]')).toBeVisible();
        await expect(page.locator('[data-testid="merchant-shops-description"]')).toBeVisible();
        
        console.log('✅ Merchant shops tab elements rendered');
        
        // Check for shops content (loading, grid, or empty state)
        const loading = page.locator('[data-testid="merchant-shops-loading"]');
        const grid = page.locator('[data-testid="merchant-shops-grid"]');
        const emptyState = page.locator('[data-testid="merchant-shops-empty-state"]');
        
        if (await loading.isVisible()) {
          const loadingCards = page.locator('[data-testid^="merchant-shops-loading-card-"]');
          const loadingCount = await loadingCards.count();
          console.log(`✅ Merchant shops loading state with ${loadingCount} cards`);
        } else if (await grid.isVisible()) {
          const shopCards = page.locator('[data-testid^="credit-shop-card-"]');
          const cardCount = await shopCards.count();
          console.log(`✅ Merchant shops grid with ${cardCount} shops`);
        } else if (await emptyState.isVisible()) {
          await expect(page.locator('[data-testid="merchant-shops-empty-icon"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-shops-empty-message"]')).toBeVisible();
          await expect(page.locator('[data-testid="merchant-shops-create-first-button"]')).toBeVisible();
          console.log('✅ Merchant shops empty state elements verified');
        }
      } else {
        console.log('⚠️ Merchant shops tab not visible - may need to click tab or authenticate');
      }
    });

    test('should handle shop navigation from merchant tab', async ({ page }) => {
      await page.goto('/dashboard/merchant');
      
      // Wait for content to load
      await page.waitForTimeout(2000);
      
      // Look for shop links
      const shopLinks = page.locator('[data-testid^="merchant-shop-link-"]');
      const linkCount = await shopLinks.count();
      
      if (linkCount > 0) {
        const firstLink = shopLinks.first();
        const href = await firstLink.getAttribute('href');
        
        if (href) {
          await firstLink.click();
          await page.waitForTimeout(1000);
          
          const currentUrl = page.url();
          expect(currentUrl).toContain(href);
          
          console.log(`✅ Shop navigation works - navigated to: ${currentUrl}`);
        }
      } else {
        console.log('⚠️ No shop links found - user may not have shops');
      }
    });
  });

  test.describe('Cross-Shop Management Flow', () => {
    test('should maintain consistent shop management across pages', async ({ page }) => {
      const pagesToTest = ['/dashboard/shops', '/dashboard/merchant'];
      
      for (const testPage of pagesToTest) {
        await page.goto(testPage);
        await page.waitForTimeout(1000);
        
        console.log(`\n📄 Testing shop management on: ${testPage}`);
        
        // Check for shop-related elements
        const shopCards = page.locator('[data-testid^="credit-shop-card-"]');
        const createButtons = page.locator('[data-testid*="create"], [data-testid*="Create"]');
        
        const cardCount = await shopCards.count();
        const buttonCount = await createButtons.count();
        
        console.log(`✅ Found ${cardCount} shop cards and ${buttonCount} create buttons`);
        
        await page.waitForTimeout(500);
      }
    });

    test('should handle shop management responsive design', async ({ page }) => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/dashboard/shops');
        
        console.log(`\n📱 Testing shop management on ${viewport.name} (${viewport.width}x${viewport.height})`);
        
        // Check main container
        const container = page.locator('[data-testid="credit-shops-list-container"]');
        await expect(container).toBeVisible();
        
        // Check responsive elements
        const shopsGrid = page.locator('[data-testid="shops-grid"]');
        if (await shopsGrid.isVisible()) {
          console.log('✅ Shops grid visible on this viewport');
        }
        
        const createButton = page.locator('[data-testid="credit-shops-create-button"]');
        if (await createButton.isVisible()) {
          console.log('✅ Create button visible on this viewport');
        }
        
        await page.waitForTimeout(500);
      }
    });
  });
});