/**
 * ADC Credit Application Pages E2E Tests - VERIFIED VERSION
 * 
 * Tests that match the actual implementation in the ADC Credit application
 * Updated to use real HTML structure instead of non-existent data-testid attributes
 */

import { test, expect } from '@playwright/test';

test.describe('ADC Credit Application Pages - Verified', () => {
  
  test.describe('Public Pages', () => {
    test('should render landing page correctly', async ({ page }) => {
      await page.goto('/');
      
      // Check main content area
      const main = page.locator('main');
      await expect(main).toBeVisible();
      
      // Check for layout container (from your actual code structure)
      const layoutContainer = page.locator('.layout-content-container');
      if (await layoutContainer.count() > 0) {
        await expect(layoutContainer).toBeVisible();
      }
      
      // Check for any heading on the page
      const headings = page.locator('h1, h2, h3, [role="heading"]');
      const headingCount = await headings.count();
      if (headingCount > 0) {
        await expect(headings.first()).toBeVisible();
        console.log(`✅ Found ${headingCount} headings on landing page`);
      }
      
      // Check for interactive elements (buttons, links)
      const interactiveElements = page.locator('button, a, [role="button"]');
      const buttonCount = await interactiveElements.count();
      console.log(`✅ Found ${buttonCount} interactive elements`);
      
      console.log('✅ Landing page rendered successfully');
    });
    
    test('should render cookies page with actual structure', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check page title (from your actual implementation)
      await expect(page.locator('h1:has-text("Cookie Settings")')).toBeVisible();
      
      // Check for cookie icon (from your implementation: Cookie component)
      const cookieIcon = page.locator('svg').first(); // Cookie icon from lucide-react
      if (await cookieIcon.count() > 0) {
        await expect(cookieIcon).toBeVisible();
      }
      
      // Check cookie overview card (using your actual Card components)
      await expect(page.locator('text="What are cookies?"')).toBeVisible();
      
      // Check all cookie category cards (from your actual implementation)
      const cookieCategories = [
        'Essential Cookies',
        'Functional Cookies', 
        'Analytics Cookies',
        'Marketing Cookies'
      ];
      
      for (const category of cookieCategories) {
        await expect(page.locator(`text="${category}"`)).toBeVisible();
      }
      
      // Check switches using your actual Switch component structure
      // Essential cookies switch should be disabled (from your code: disabled={true})
      const switches = page.locator('[role="switch"]');
      const switchCount = await switches.count();
      expect(switchCount).toBe(4); // 4 cookie categories
      
      // Check that essential switch is disabled
      const essentialSwitch = switches.first(); // Assuming essential is first
      await expect(essentialSwitch).toBeDisabled();
      
      // Check action buttons (from your actual Button components)
      await expect(page.locator('button:has-text("Save Preferences")')).toBeVisible();
      await expect(page.locator('button:has-text("Accept All Cookies")')).toBeVisible();
      await expect(page.locator('button:has-text("Essential Only")')).toBeVisible();
      
      // Check contact email link (from your actual implementation)
      await expect(page.locator('a[href="mailto:<EMAIL>"]')).toBeVisible();
      
      console.log('✅ Cookies page structure verified');
    });
    
    test('should handle cookie preferences interactions correctly', async ({ page }) => {
      await page.goto('/cookies');
      
      // Get all switches (your Switch components)
      const switches = page.locator('[role="switch"]');
      
      // Skip the first switch (essential - disabled)
      // Click functional cookies switch (should be second)
      if (await switches.count() >= 2) {
        await switches.nth(1).click();
      }
      
      // Click analytics cookies switch (should be third)
      if (await switches.count() >= 3) {
        await switches.nth(2).click();
      }
      
      // Click marketing cookies switch (should be fourth)
      if (await switches.count() >= 4) {
        await switches.nth(3).click();
      }
      
      // Save preferences using your actual Button
      await page.click('button:has-text("Save Preferences")');
      
      // Check for toast notification (from your sonner toast implementation)
      // Your code shows: toast.success("Cookie preferences saved successfully!");
      const toastLocator = page.locator('text="Cookie preferences saved successfully!"');
      await expect(toastLocator).toBeVisible({ timeout: 5000 });
      
      console.log('✅ Cookie preferences interactions verified');
    });
    
    test('should persist cookie preferences using localStorage', async ({ page }) => {
      await page.goto('/cookies');
      
      // Set preferences
      const switches = page.locator('[role="switch"]');
      
      // Enable functional and analytics (switches 1 and 2, skipping essential at 0)
      if (await switches.count() >= 3) {
        await switches.nth(1).click(); // Functional
        await switches.nth(2).click(); // Analytics
      }
      
      await page.click('button:has-text("Save Preferences")');
      
      // Wait for toast
      await expect(page.locator('text="Cookie preferences saved successfully!"')).toBeVisible();
      
      // Check localStorage (your code uses these keys)
      const consent = await page.evaluate(() => localStorage.getItem('adc-credit-cookie-consent'));
      const preferences = await page.evaluate(() => localStorage.getItem('adc-credit-cookie-preferences'));
      
      expect(consent).toBe('true');
      expect(preferences).toBeTruthy();
      
      // Parse preferences to verify structure
      const parsedPrefs = await page.evaluate(() => {
        const prefs = localStorage.getItem('adc-credit-cookie-preferences');
        return prefs ? JSON.parse(prefs) : null;
      });
      
      expect(parsedPrefs).toHaveProperty('essential', true);
      expect(parsedPrefs).toHaveProperty('functional');
      expect(parsedPrefs).toHaveProperty('analytics');
      expect(parsedPrefs).toHaveProperty('marketing');
      
      // Reload page and verify persistence
      await page.reload();
      
      // Check that switches maintain their state
      const switchesAfterReload = page.locator('[role="switch"]');
      
      // Essential should always be checked and disabled
      await expect(switchesAfterReload.nth(0)).toBeChecked();
      await expect(switchesAfterReload.nth(0)).toBeDisabled();
      
      console.log('✅ Cookie preferences persistence verified');
    });
    
    test('should test Accept All Cookies functionality', async ({ page }) => {
      await page.goto('/cookies');
      
      // Click Accept All button
      await page.click('button:has-text("Accept All Cookies")');
      
      // Check for success toast (from your code: toast.success("All cookies accepted!"))
      await expect(page.locator('text="All cookies accepted!"')).toBeVisible({ timeout: 5000 });
      
      // Verify all switches are now checked
      const switches = page.locator('[role="switch"]');
      const switchCount = await switches.count();
      
      for (let i = 0; i < switchCount; i++) {
        await expect(switches.nth(i)).toBeChecked();
      }
      
      // Verify localStorage
      const preferences = await page.evaluate(() => {
        const prefs = localStorage.getItem('adc-credit-cookie-preferences');
        return prefs ? JSON.parse(prefs) : null;
      });
      
      expect(preferences).toEqual({
        essential: true,
        functional: true,
        analytics: true,
        marketing: true
      });
      
      console.log('✅ Accept All Cookies functionality verified');
    });
    
    test('should test Essential Only functionality', async ({ page }) => {
      await page.goto('/cookies');
      
      // First enable some cookies
      const switches = page.locator('[role="switch"]');
      if (await switches.count() >= 3) {
        await switches.nth(1).click(); // Functional
        await switches.nth(2).click(); // Analytics
      }
      
      // Click Essential Only button
      await page.click('button:has-text("Essential Only")');
      
      // Check for success toast (from your code: toast.success("Reset to essential cookies only!"))
      await expect(page.locator('text="Reset to essential cookies only!"')).toBeVisible({ timeout: 5000 });
      
      // Verify only essential is checked
      await expect(switches.nth(0)).toBeChecked(); // Essential
      if (await switches.count() >= 2) {
        await expect(switches.nth(1)).not.toBeChecked(); // Functional
      }
      if (await switches.count() >= 3) {
        await expect(switches.nth(2)).not.toBeChecked(); // Analytics
      }
      if (await switches.count() >= 4) {
        await expect(switches.nth(3)).not.toBeChecked(); // Marketing
      }
      
      console.log('✅ Essential Only functionality verified');
    });
  });
  
  test.describe('Navigation and Layout', () => {
    test('should have consistent page structure', async ({ page }) => {
      const pagesToTest = ['/', '/cookies'];
      
      for (const pagePath of pagesToTest) {
        await page.goto(pagePath);
        
        // Check for main content area
        const main = page.locator('main');
        await expect(main).toBeVisible();
        
        // Check page doesn't have JavaScript errors
        page.on('pageerror', err => {
          console.error(`Page error on ${pagePath}:`, err.message);
        });
        
        console.log(`✅ Page structure verified for ${pagePath}`);
      }
    });
    
    test('should handle responsive design', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/cookies');
      
      // Main content should still be visible
      await expect(page.locator('main')).toBeVisible();
      
      // Cookie cards should still be visible (your Card components should be responsive)
      await expect(page.locator('text="Essential Cookies"')).toBeVisible();
      
      // Buttons should still be accessible
      await expect(page.locator('button:has-text("Save Preferences")')).toBeVisible();
      
      console.log('✅ Mobile responsive design verified');
    });
  });
  
  test.describe('Error Handling', () => {
    test('should handle invalid localStorage data gracefully', async ({ page }) => {
      await page.goto('/cookies');
      
      // Inject invalid localStorage data (your code has try-catch for this)
      await page.evaluate(() => {
        localStorage.setItem('adc-credit-cookie-preferences', 'invalid-json');
      });
      
      // Reload page
      await page.reload();
      
      // Page should still load without errors
      await expect(page.locator('h1:has-text("Cookie Settings")')).toBeVisible();
      
      // Should fall back to default preferences
      const switches = page.locator('[role="switch"]');
      
      // Essential should be checked, others should be unchecked (default behavior)
      await expect(switches.nth(0)).toBeChecked();
      if (await switches.count() >= 2) {
        await expect(switches.nth(1)).not.toBeChecked();
      }
      
      console.log('✅ Invalid localStorage handling verified');
    });
  });
  
  test.describe('Accessibility', () => {
    test('should have proper accessibility attributes', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check page title
      const title = await page.title();
      expect(title).toBeTruthy();
      expect(title.length).toBeGreaterThan(0);
      
      // Check main heading
      const mainHeading = page.locator('h1');
      await expect(mainHeading).toBeVisible();
      
      // Check that switches have proper roles
      const switches = page.locator('[role="switch"]');
      const switchCount = await switches.count();
      expect(switchCount).toBeGreaterThan(0);
      
      // Check that buttons are keyboard accessible
      const buttons = page.locator('button');
      for (let i = 0; i < Math.min(await buttons.count(), 3); i++) {
        const button = buttons.nth(i);
        const isVisible = await button.isVisible();
        if (isVisible) {
          await button.focus();
          // Button should receive focus
          const isFocused = await button.evaluate(el => document.activeElement === el);
          expect(isFocused).toBeTruthy();
        }
      }
      
      console.log('✅ Accessibility attributes verified');
    });
  });
});