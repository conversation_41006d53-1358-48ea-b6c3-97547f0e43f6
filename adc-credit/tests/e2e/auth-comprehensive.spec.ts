/**
 * ADC Credit Authentication E2E Tests
 * 
 * Comprehensive testing of authentication flows with stable test IDs
 */

import { test, expect } from '@playwright/test';

// Authentication test data
const TEST_DATA = {
  VALID_USER: {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
  },
  DEMO_USER: {
    email: '<EMAIL>',
    password: 'password123',
  },
  INVALID_USER: {
    email: '<EMAIL>',
    password: 'wrongpassword',
  },
} as const;

test.describe('Authentication Flow Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up console monitoring
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text());
      }
    });
  });

  test.describe('Sign In Page', () => {
    test('should render all sign-in elements with test IDs', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Check main container
      await expect(page.locator('[data-testid="auth-signin-container"]')).toBeVisible();
      
      // Check title and form elements
      await expect(page.locator('[data-testid="auth-signin-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-form"]')).toBeVisible();
      
      // Check input fields
      await expect(page.locator('[data-testid="auth-signin-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-remember-checkbox"]')).toBeVisible();
      
      // Check buttons
      await expect(page.locator('[data-testid="auth-signin-submit-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-google-button"]')).toBeVisible();
      
      // Check links
      await expect(page.locator('[data-testid="auth-signin-forgot-password-link"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-register-link"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-signin-back-home-link"]')).toBeVisible();
      
      console.log('✅ All sign-in elements with test IDs are visible');
    });

    test('should handle sign-in form submission', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Monitor network requests
      const authRequests: string[] = [];
      page.on('request', request => {
        if (request.url().includes('/auth/') || request.url().includes('/api/auth/')) {
          authRequests.push(request.url());
        }
      });
      
      // Fill in credentials
      await page.locator('[data-testid="auth-signin-email-input"]').fill(TEST_DATA.DEMO_USER.email);
      await page.locator('[data-testid="auth-signin-password-input"]').fill(TEST_DATA.DEMO_USER.password);
      
      // Check remember me
      await page.locator('[data-testid="auth-signin-remember-checkbox"]').check();
      
      // Submit form
      await page.locator('[data-testid="auth-signin-submit-button"]').click();
      
      // Wait for either redirect or error
      await page.waitForTimeout(3000);
      
      // Verify form submission triggered authentication flow
      console.log(`✅ Authentication requests: ${authRequests.length} calls made`);
      
      // Check if redirected to dashboard or still on auth page
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ Successfully redirected to dashboard');
      } else if (currentUrl.includes('/auth/')) {
        console.log('⚠️ Still on auth page - may need valid credentials');
        
        // Check if error message is displayed
        const errorMessage = page.locator('[data-testid="auth-signin-error-message"]');
        if (await errorMessage.isVisible()) {
          const errorText = await errorMessage.textContent();
          console.log(`⚠️ Authentication error: ${errorText}`);
        }
      }
    });

    test('should validate required fields', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Try to submit empty form
      await page.locator('[data-testid="auth-signin-submit-button"]').click();
      
      // Check for HTML5 validation or custom validation
      const emailInput = page.locator('[data-testid="auth-signin-email-input"]');
      const passwordInput = page.locator('[data-testid="auth-signin-password-input"]');
      
      // Check if required attribute is working
      const emailValid = await emailInput.evaluate(el => (el as HTMLInputElement).validity.valid);
      const passwordValid = await passwordInput.evaluate(el => (el as HTMLInputElement).validity.valid);
      
      console.log(`✅ Email validation: ${emailValid ? 'Valid' : 'Invalid (required)'}`);
      console.log(`✅ Password validation: ${passwordValid ? 'Valid' : 'Invalid (required)'}`);
    });

    test('should handle Google OAuth button', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Monitor OAuth redirects
      const oauthRequests: string[] = [];
      page.on('request', request => {
        if (request.url().includes('google') || request.url().includes('oauth')) {
          oauthRequests.push(request.url());
        }
      });
      
      // Click Google sign-in button
      await page.locator('[data-testid="auth-signin-google-button"]').click();
      
      // Wait for potential OAuth redirect
      await page.waitForTimeout(2000);
      
      if (oauthRequests.length > 0) {
        console.log(`✅ Google OAuth flow initiated: ${oauthRequests.length} requests`);
      } else {
        console.log('⚠️ No OAuth requests detected - may require configuration');
      }
    });
  });

  test.describe('Login Page', () => {
    test('should render all login elements with test IDs', async ({ page }) => {
      await page.goto('/auth/login');
      
      // Check main elements
      await expect(page.locator('[data-testid="auth-login-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-form"]')).toBeVisible();
      
      // Check form elements
      await expect(page.locator('[data-testid="auth-login-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-submit-button"]')).toBeVisible();
      
      // Check additional elements
      await expect(page.locator('[data-testid="auth-login-test-account-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-login-register-link"]')).toBeVisible();
      
      console.log('✅ All login elements with test IDs are visible');
    });

    test('should handle test account login', async ({ page }) => {
      await page.goto('/auth/login');
      
      // Monitor API requests
      const apiRequests: { url: string; method: string }[] = [];
      page.on('request', request => {
        if (request.url().includes('/api/auth/login')) {
          apiRequests.push({
            url: request.url(),
            method: request.method()
          });
        }
      });
      
      // Click test account button
      await page.locator('[data-testid="auth-login-test-account-button"]').click();
      
      // Wait for API call and potential redirect
      await page.waitForTimeout(3000);
      
      // Verify API call was made
      if (apiRequests.length > 0) {
        console.log(`✅ Login API call made: ${apiRequests[0].method} ${apiRequests[0].url}`);
        
        // Check if redirected or error shown
        const currentUrl = page.url();
        if (currentUrl.includes('/dashboard')) {
          console.log('✅ Test account login successful - redirected to dashboard');
        } else {
          // Check for error message
          const errorMessage = page.locator('[data-testid="auth-login-error-message"]');
          if (await errorMessage.isVisible()) {
            const errorText = await errorMessage.textContent();
            console.log(`⚠️ Test account login error: ${errorText}`);
          }
        }
      } else {
        console.log('⚠️ No login API calls detected');
      }
    });
  });

  test.describe('Registration Page', () => {
    test('should render all registration elements with test IDs', async ({ page }) => {
      await page.goto('/auth/register');
      
      // Check main elements
      await expect(page.locator('[data-testid="auth-register-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-form"]')).toBeVisible();
      
      // Check form inputs
      await expect(page.locator('[data-testid="auth-register-name-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-confirm-password-input"]')).toBeVisible();
      
      // Check submit button
      await expect(page.locator('[data-testid="auth-register-submit-button"]')).toBeVisible();
      
      // Check links
      await expect(page.locator('[data-testid="auth-register-signin-link"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-terms-link"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-register-privacy-link"]')).toBeVisible();
      
      console.log('✅ All registration elements with test IDs are visible');
    });

    test('should validate password confirmation', async ({ page }) => {
      await page.goto('/auth/register');
      
      // Fill form with mismatched passwords
      await page.locator('[data-testid="auth-register-name-input"]').fill(TEST_DATA.VALID_USER.name);
      await page.locator('[data-testid="auth-register-email-input"]').fill(TEST_DATA.VALID_USER.email);
      await page.locator('[data-testid="auth-register-password-input"]').fill(TEST_DATA.VALID_USER.password);
      await page.locator('[data-testid="auth-register-confirm-password-input"]').fill('different-password');
      
      // Submit form
      await page.locator('[data-testid="auth-register-submit-button"]').click();
      
      // Wait for validation
      await page.waitForTimeout(1000);
      
      // Check for error message
      const errorMessage = page.locator('[data-testid="auth-register-error-message"]');
      if (await errorMessage.isVisible()) {
        const errorText = await errorMessage.textContent();
        console.log(`✅ Password validation working: ${errorText}`);
      } else {
        console.log('⚠️ No password validation error shown');
      }
    });

    test('should handle registration form submission', async ({ page }) => {
      await page.goto('/auth/register');
      
      // Monitor registration requests
      const registrationRequests: string[] = [];
      page.on('request', request => {
        if (request.url().includes('/api/auth/register')) {
          registrationRequests.push(request.url());
        }
      });
      
      // Fill valid registration form
      const uniqueEmail = `test-${Date.now()}@example.com`;
      await page.locator('[data-testid="auth-register-name-input"]').fill(TEST_DATA.VALID_USER.name);
      await page.locator('[data-testid="auth-register-email-input"]').fill(uniqueEmail);
      await page.locator('[data-testid="auth-register-password-input"]').fill(TEST_DATA.VALID_USER.password);
      await page.locator('[data-testid="auth-register-confirm-password-input"]').fill(TEST_DATA.VALID_USER.password);
      
      // Submit form
      await page.locator('[data-testid="auth-register-submit-button"]').click();
      
      // Wait for registration and potential auto-signin
      await page.waitForTimeout(5000);
      
      if (registrationRequests.length > 0) {
        console.log(`✅ Registration API call made: ${registrationRequests[0]}`);
        
        // Check if redirected to dashboard
        const currentUrl = page.url();
        if (currentUrl.includes('/dashboard')) {
          console.log('✅ Registration successful - auto-signed in and redirected');
        } else if (currentUrl.includes('/auth/signin')) {
          console.log('✅ Registration successful - redirected to sign in');
        } else {
          console.log(`⚠️ Registration completed but unexpected URL: ${currentUrl}`);
        }
      } else {
        console.log('⚠️ No registration API calls detected');
      }
    });
  });

  test.describe('Forgot Password Page', () => {
    test('should render all forgot password elements with test IDs', async ({ page }) => {
      await page.goto('/auth/forgot-password');
      
      // Check main elements
      await expect(page.locator('[data-testid="auth-forgot-password-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-forgot-password-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-forgot-password-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-forgot-password-form"]')).toBeVisible();
      
      // Check form elements
      await expect(page.locator('[data-testid="auth-forgot-password-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-forgot-password-submit-button"]')).toBeVisible();
      
      // Check links
      await expect(page.locator('[data-testid="auth-forgot-password-signin-link"]')).toBeVisible();
      await expect(page.locator('[data-testid="auth-forgot-password-back-link"]')).toBeVisible();
      
      console.log('✅ All forgot password elements with test IDs are visible');
    });

    test('should handle forgot password submission', async ({ page }) => {
      await page.goto('/auth/forgot-password');
      
      // Monitor password reset requests
      const resetRequests: string[] = [];
      page.on('request', request => {
        if (request.url().includes('/api/auth/forgot-password')) {
          resetRequests.push(request.url());
        }
      });
      
      // Fill email and submit
      await page.locator('[data-testid="auth-forgot-password-email-input"]').fill(TEST_DATA.VALID_USER.email);
      await page.locator('[data-testid="auth-forgot-password-submit-button"]').click();
      
      // Wait for API call
      await page.waitForTimeout(3000);
      
      if (resetRequests.length > 0) {
        console.log(`✅ Password reset API call made: ${resetRequests[0]}`);
        
        // Check for success message
        const successMessage = page.locator('[data-testid="auth-forgot-password-success-message"]');
        const errorMessage = page.locator('[data-testid="auth-forgot-password-error-message"]');
        
        if (await successMessage.isVisible()) {
          const successText = await successMessage.textContent();
          console.log(`✅ Password reset success: ${successText}`);
        } else if (await errorMessage.isVisible()) {
          const errorText = await errorMessage.textContent();
          console.log(`⚠️ Password reset error: ${errorText}`);
        }
      } else {
        console.log('⚠️ No password reset API calls detected');
      }
    });
  });

  test.describe('Navigation and Cross-Page Flow', () => {
    test('should navigate between authentication pages using links', async ({ page }) => {
      // Start at sign in page
      await page.goto('/auth/signin');
      await expect(page.locator('[data-testid="auth-signin-container"]')).toBeVisible();
      
      // Navigate to registration
      await page.locator('[data-testid="auth-signin-register-link"]').click();
      await expect(page.locator('[data-testid="auth-register-container"]')).toBeVisible();
      console.log('✅ Navigated from sign-in to registration');
      
      // Navigate back to sign in
      await page.locator('[data-testid="auth-register-signin-link"]').click();
      await expect(page.locator('[data-testid="auth-signin-container"]')).toBeVisible();
      console.log('✅ Navigated from registration to sign-in');
      
      // Navigate to forgot password
      await page.locator('[data-testid="auth-signin-forgot-password-link"]').click();
      await expect(page.locator('[data-testid="auth-forgot-password-container"]')).toBeVisible();
      console.log('✅ Navigated from sign-in to forgot password');
      
      // Navigate back to sign in
      await page.locator('[data-testid="auth-forgot-password-signin-link"]').click();
      await expect(page.locator('[data-testid="auth-signin-container"]')).toBeVisible();
      console.log('✅ Navigated from forgot password to sign-in');
    });

    test('should handle back to home navigation', async ({ page }) => {
      // Test from sign-in page
      await page.goto('/auth/signin');
      await page.locator('[data-testid="auth-signin-back-home-link"]').click();
      
      // Should navigate to home page
      await page.waitForTimeout(1000);
      const currentUrl = page.url();
      
      if (currentUrl === page.url().replace(/\/auth\/.*/, '/') || currentUrl.includes('localhost:3400')) {
        console.log('✅ Successfully navigated back to home');
      } else {
        console.log(`⚠️ Unexpected navigation result: ${currentUrl}`);
      }
    });
  });

  test.describe('Loading States and Error Handling', () => {
    test('should show loading spinners during form submission', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Fill form
      await page.locator('[data-testid="auth-signin-email-input"]').fill(TEST_DATA.DEMO_USER.email);
      await page.locator('[data-testid="auth-signin-password-input"]').fill(TEST_DATA.DEMO_USER.password);
      
      // Submit and immediately check for loading state
      await page.locator('[data-testid="auth-signin-submit-button"]').click();
      
      // Check if loading spinner appears
      const loadingSpinner = page.locator('[data-testid="auth-signin-loading-spinner"]');
      const buttonText = page.locator('[data-testid="auth-signin-submit-button"]');
      
      // Wait briefly for loading state
      await page.waitForTimeout(500);
      
      // Check button state
      const buttonTextContent = await buttonText.textContent();
      const isDisabled = await buttonText.isDisabled();
      
      console.log(`✅ Loading state - Button text: "${buttonTextContent}", Disabled: ${isDisabled}`);
    });

    test('should handle authentication errors gracefully', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Fill with invalid credentials
      await page.locator('[data-testid="auth-signin-email-input"]').fill(TEST_DATA.INVALID_USER.email);
      await page.locator('[data-testid="auth-signin-password-input"]').fill(TEST_DATA.INVALID_USER.password);
      
      // Submit form
      await page.locator('[data-testid="auth-signin-submit-button"]').click();
      
      // Wait for error
      await page.waitForTimeout(3000);
      
      // Check for error message
      const errorMessage = page.locator('[data-testid="auth-signin-error-message"]');
      if (await errorMessage.isVisible()) {
        const errorText = await errorMessage.textContent();
        console.log(`✅ Error handling working: ${errorText}`);
      } else {
        console.log('⚠️ No error message shown for invalid credentials');
      }
      
      // Verify form is still usable after error
      await expect(page.locator('[data-testid="auth-signin-email-input"]')).toBeEnabled();
      await expect(page.locator('[data-testid="auth-signin-password-input"]')).toBeEnabled();
      await expect(page.locator('[data-testid="auth-signin-submit-button"]')).toBeEnabled();
      
      console.log('✅ Form remains usable after error');
    });
  });
});