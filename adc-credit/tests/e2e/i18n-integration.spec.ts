/**
 * Multi-Language E2E Tests - Multi-Languages Service Integration
 * ADC Credit Service - Translation Service Integration Testing
 * 
 * This test suite verifies integration with the Multi-Languages service:
 * 
 * Test Categories:
 * - Translation API Integration and Authentication
 * - Real Translation Loading and Caching
 * - Service Error Handling and Fallbacks
 * - Translation Namespace Management
 * - Performance and Caching Behavior
 * 
 * Features tested:
 * - Internal key authentication with Multi-Languages service
 * - Real translation fetching and caching
 * - Network error handling and graceful degradation
 * - Translation service response validation
 * - Performance impact of translation loading
 */

import { test, expect } from '@playwright/test';

// Test configuration
const MULTI_LANG_SERVICE_URL = process.env.NEXT_PUBLIC_MULTILANG_URL || 'http://localhost:8300';
const CREDIT_PROJECT_ID = process.env.NEXT_PUBLIC_CREDIT_PROJECT_ID || 'adc-credit-project';
const INTERNAL_KEY = 'adc-internal-2024';

// Helper function to change language
async function changeLanguage(page: any, languageText: string, localeCode: string) {
  const languageSwitcherButton = page.locator('[data-testid*="language-switcher-trigger"]').first();
  await languageSwitcherButton.click();
  
  const languageDropdown = page.locator('[data-testid*="language-switcher-content"]').first();
  await expect(languageDropdown).toBeVisible();
  
  const languageOption = languageDropdown.locator(`text=${languageText}`);
  await languageOption.click();
  
  await page.waitForTimeout(1000); // Allow time for translation loading
  
  const storedLocale = await page.evaluate(() => localStorage.getItem('locale'));
  expect(storedLocale).toBe(localeCode);
}

// Helper function to mock successful translation response
function mockSuccessfulTranslationResponse() {
  return {
    success: true,
    data: {
      translations: {
        'dashboard.title': 'Panel de Control',
        'dashboard.description': 'Resumen de su uso de API y créditos',
        'dashboard.credit_balance': 'Saldo de Créditos',
        'dashboard.api_keys': 'Claves API',
        'shops.title': 'Tiendas',
        'shops.create_shop': 'Crear Tienda',
        'shops.description': 'Gestione sus tiendas minoristas, servicios API y ubicaciones empresariales'
      }
    }
  };
}

// Helper function to mock namespace translation response
function mockNamespaceResponse(namespace: string) {
  const responses = {
    'dashboard': {
      success: true,
      data: {
        translations: {
          'dashboard.title': 'Tableau de Bord',
          'dashboard.description': 'Aperçu de votre utilisation API et crédits',
          'dashboard.credit_balance': 'Solde de Crédits',
          'dashboard.api_keys': 'Clés API',
          'dashboard.quick_actions': 'Actions Rapides'
        }
      }
    },
    'shops': {
      success: true,
      data: {
        translations: {
          'shops.title': 'Magasins',
          'shops.create_shop': 'Créer un Magasin',
          'shops.description': 'Gérez vos magasins de détail, services API et emplacements d\'entreprise',
          'shops.no_shops_yet': 'Aucun magasin pour le moment'
        }
      }
    }
  };
  
  return responses[namespace] || { success: false, error: 'Namespace not found' };
}

// Test Group: Translation API Authentication and Basic Integration
test.describe('Translation API Authentication E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should authenticate with Multi-Languages service using internal key', async ({ page }) => {
    // Mock successful authentication
    await page.route('**/api/v2/internal/translations/fetch', async route => {
      const request = route.request();
      const headers = request.headers();
      
      // Verify internal key is sent
      expect(headers['x-internal-key']).toBe(INTERNAL_KEY);
      expect(headers['content-type']).toBe('application/json');
      
      const postData = JSON.parse(request.postData() || '{}');
      expect(postData.project_id).toBe(CREDIT_PROJECT_ID);
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    // Change language to trigger translation request
    await changeLanguage(page, 'Español', 'es');
    
    // Should have made the API call with proper authentication
    // The test passes if no exceptions are thrown above
  });

  test('should handle authentication failures gracefully', async ({ page }) => {
    // Mock authentication failure
    await page.route('**/api/v2/internal/translations/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Unauthorized - Invalid internal key'
        })
      });
    });
    
    await page.goto('/');
    
    // Change language (should fail to get translations but not break the app)
    await changeLanguage(page, 'Français', 'fr');
    
    // App should still be functional
    await expect(page.locator('body')).toBeVisible();
    
    // Language should still be set locally
    const storedLocale = await page.evaluate(() => localStorage.getItem('locale'));
    expect(storedLocale).toBe('fr');
  });

  test('should include correct project ID in translation requests', async ({ page }) => {
    let requestReceived = false;
    
    await page.route('**/api/v2/internal/translations/fetch', async route => {
      const request = route.request();
      const postData = JSON.parse(request.postData() || '{}');
      
      // Verify project ID is correct
      expect(postData.project_id).toBe(CREDIT_PROJECT_ID);
      expect(postData.locale).toBeTruthy();
      expect(postData.key || postData.namespace).toBeTruthy();
      
      requestReceived = true;
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    await changeLanguage(page, 'Español', 'es');
    
    // Wait for request to be made
    await page.waitForTimeout(1500);
    expect(requestReceived).toBeTruthy();
  });
});

// Test Group: Real Translation Loading and Display
test.describe('Translation Loading and Display E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should load and display real translations when available', async ({ page }) => {
    // Mock successful translation loading
    await page.route('**/api/v2/internal/translations/fetch', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            translations: {
              'dashboard.title': 'Panel de Control',
              'shops.title': 'Tiendas',
              'ui.loading': 'Cargando...',
              'ui.error': 'Error'
            }
          }
        })
      });
    });
    
    await changeLanguage(page, 'Español', 'es');
    
    // Check if any Spanish translations might be visible
    // Note: This depends on the actual implementation of translation loading
    await page.waitForTimeout(1500);
    
    // App should remain functional
    await expect(page.locator('body')).toBeVisible();
  });

  test('should handle namespace-based translation loading', async ({ page }) => {
    // Mock namespace endpoint
    await page.route('**/api/v2/internal/translations/namespace', route => {
      const url = new URL(route.request().url());
      const namespace = url.searchParams.get('namespace') || 'ui';
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockNamespaceResponse(namespace))
      });
    });
    
    await changeLanguage(page, 'Français', 'fr');
    
    // Wait for potential namespace loading
    await page.waitForTimeout(1500);
    
    // App should be functional
    await expect(page.locator('body')).toBeVisible();
  });

  test('should cache translations and avoid duplicate requests', async ({ page }) => {
    let requestCount = 0;
    
    await page.route('**/api/v2/internal/translations/**', route => {
      requestCount++;
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    // Change to Spanish
    await changeLanguage(page, 'Español', 'es');
    await page.waitForTimeout(1000);
    
    const firstRequestCount = requestCount;
    
    // Navigate to different page and back
    await page.goto('/docs');
    await page.waitForTimeout(500);
    await page.goto('/');
    await page.waitForTimeout(500);
    
    // Should not make additional requests for cached translations
    const secondRequestCount = requestCount;
    expect(secondRequestCount).toBeLessThanOrEqual(firstRequestCount + 1); // Allow minimal additional requests
  });
});

// Test Group: Error Handling and Fallback Behavior
test.describe('Translation Service Error Handling E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Mock network failure
    await page.route('**/api/v2/internal/translations/**', route => {
      route.abort('failed');
    });
    
    await changeLanguage(page, 'Deutsch', 'de');
    
    // App should remain functional despite translation loading failure
    await expect(page.locator('body')).toBeVisible();
    
    // Language should still be set
    const storedLocale = await page.evaluate(() => localStorage.getItem('locale'));
    expect(storedLocale).toBe('de');
    
    // Language switcher should still work
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    await expect(languageSwitcher).toBeVisible();
  });

  test('should handle server errors with fallback', async ({ page }) => {
    // Mock server error
    await page.route('**/api/v2/internal/translations/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Internal server error'
        })
      });
    });
    
    await changeLanguage(page, '日本語', 'ja');
    
    // App should handle the error gracefully
    await expect(page.locator('body')).toBeVisible();
    
    // Should be able to change language again
    await changeLanguage(page, 'English', 'en');
    await expect(page.locator('body')).toBeVisible();
  });

  test('should handle malformed translation responses', async ({ page }) => {
    // Mock malformed response
    await page.route('**/api/v2/internal/translations/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: 'invalid json response'
      });
    });
    
    await changeLanguage(page, '한국어', 'ko');
    
    // App should handle malformed JSON gracefully
    await expect(page.locator('body')).toBeVisible();
    
    // Language should still be set
    const storedLocale = await page.evaluate(() => localStorage.getItem('locale'));
    expect(storedLocale).toBe('ko');
  });

  test('should handle partial translation loading', async ({ page }) => {
    let requestCount = 0;
    
    await page.route('**/api/v2/internal/translations/**', route => {
      requestCount++;
      
      // Simulate intermittent failures
      if (requestCount % 2 === 0) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              translations: {
                'ui.loading': 'Carregando...',
                'ui.error': 'Erro'
              }
            }
          })
        });
      } else {
        route.abort('failed');
      }
    });
    
    await changeLanguage(page, 'Português', 'pt');
    
    // App should handle partial loading
    await expect(page.locator('body')).toBeVisible();
  });
});

// Test Group: Performance and Caching
test.describe('Translation Performance and Caching E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should load translations efficiently without blocking UI', async ({ page }) => {
    // Mock slow translation service
    await page.route('**/api/v2/internal/translations/**', async route => {
      // Delay response to simulate slow network
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    const startTime = Date.now();
    
    // Change language
    await changeLanguage(page, 'Español', 'es');
    
    const changeTime = Date.now() - startTime;
    
    // Language change itself should be fast (UI not blocked)
    expect(changeTime).toBeLessThan(1500);
    
    // UI should remain responsive
    await expect(page.locator('body')).toBeVisible();
    
    // Language switcher should still be interactive
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    await expect(languageSwitcher).toBeVisible();
  });

  test('should batch translation requests efficiently', async ({ page }) => {
    let requestCount = 0;
    const requestKeys = [];
    
    await page.route('**/api/v2/internal/translations/**', route => {
      requestCount++;
      const url = new URL(route.request().url());
      const postData = route.request().postData();
      
      if (postData) {
        const data = JSON.parse(postData);
        requestKeys.push(data.key || data.namespace || 'unknown');
      }
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    // Rapidly change languages to test batching
    await changeLanguage(page, 'Français', 'fr');
    await page.waitForTimeout(200);
    await changeLanguage(page, 'Deutsch', 'de');
    await page.waitForTimeout(200);
    await changeLanguage(page, 'Español', 'es');
    
    await page.waitForTimeout(1500);
    
    // Should not make excessive requests
    expect(requestCount).toBeLessThan(10);
  });

  test('should handle concurrent translation requests correctly', async ({ page }) => {
    let concurrentRequests = 0;
    let maxConcurrent = 0;
    
    await page.route('**/api/v2/internal/translations/**', async route => {
      concurrentRequests++;
      maxConcurrent = Math.max(maxConcurrent, concurrentRequests);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 500));
      
      concurrentRequests--;
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSuccessfulTranslationResponse())
      });
    });
    
    // Trigger multiple rapid language changes
    const languageChanges = [
      changeLanguage(page, 'Français', 'fr'),
      changeLanguage(page, 'Deutsch', 'de'),
      changeLanguage(page, 'Español', 'es')
    ];
    
    // Wait for all changes to complete
    await Promise.all(languageChanges);
    
    // Should handle concurrent requests without excessive parallelism
    expect(maxConcurrent).toBeLessThanOrEqual(5);
    
    // Final language should be correctly set
    const finalLocale = await page.evaluate(() => localStorage.getItem('locale'));
    expect(['fr', 'de', 'es'].includes(finalLocale || '')).toBeTruthy();
  });
});

// Test Group: Service Integration Edge Cases
test.describe('Service Integration Edge Cases E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('should handle missing translation keys gracefully', async ({ page }) => {
    // Mock response with missing keys
    await page.route('**/api/v2/internal/translations/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            translations: {
              // Only provide a few translations, missing most keys
              'ui.loading': 'Laden...'
            }
          }
        })
      });
    });
    
    await changeLanguage(page, 'Deutsch', 'de');
    
    // App should handle missing translations gracefully
    await expect(page.locator('body')).toBeVisible();
    
    // Should be able to interact with elements even with missing translations
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    await expect(languageSwitcher).toBeVisible();
  });

  test('should handle translation service unavailability', async ({ page }) => {
    // Mock service completely unavailable
    await page.route('**/api/v2/internal/translations/**', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Service temporarily unavailable'
        })
      });
    });
    
    // App should start normally
    await expect(page.locator('body')).toBeVisible();
    
    // Should be able to change languages (stored locally)
    await changeLanguage(page, '中文', 'zh');
    
    // App should remain functional
    await expect(page.locator('body')).toBeVisible();
    
    // Should display fallback content
    const languageSwitcher = page.locator('[data-testid*="language-switcher-trigger"]').first();
    await expect(languageSwitcher).toBeVisible();
  });

  test('should recover from service restoration', async ({ page }) => {
    // Start with service unavailable
    let serviceAvailable = false;
    
    await page.route('**/api/v2/internal/translations/**', route => {
      if (serviceAvailable) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSuccessfulTranslationResponse())
        });
      } else {
        route.abort('failed');
      }
    });
    
    // Try to change language while service is down
    await changeLanguage(page, 'Español', 'es');
    await expect(page.locator('body')).toBeVisible();
    
    // Restore service
    serviceAvailable = true;
    
    // Change language again
    await changeLanguage(page, 'Français', 'fr');
    await page.waitForTimeout(1000);
    
    // Should now work properly
    await expect(page.locator('body')).toBeVisible();
  });
});