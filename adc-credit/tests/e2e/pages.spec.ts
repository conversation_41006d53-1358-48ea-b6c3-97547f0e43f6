/**
 * ADC Credit Application Pages E2E Tests
 * 
 * Tests all public and authenticated pages in the ADC Credit application
 */

import { test, expect } from '@playwright/test';

test.describe('ADC Credit Application Pages', () => {
  
  test.describe('Public Pages', () => {
    test('should render landing page correctly', async ({ page }) => {
      await page.goto('/');
      
      // Check main landing page components
      await expect(page.locator('h1, [role="heading"][aria-level="1"]')).toBeVisible();
      
      // Check hero section
      const heroSection = page.locator('main').first();
      await expect(heroSection).toBeVisible();
      
      // Check if features section is present
      const featuresSection = page.locator('text="Features"').or(page.locator('[data-testid*="feature"]')).first();
      if (await featuresSection.isVisible()) {
        await expect(featuresSection).toBeVisible();
      }
      
      // Check if testimonials section is present
      const testimonialsSection = page.locator('text="Testimonials"').or(page.locator('[data-testid*="testimonial"]')).first();
      if (await testimonialsSection.isVisible()) {
        await expect(testimonialsSection).toBeVisible();
      }
      
      // Check if pricing section is present
      const pricingSection = page.locator('text="Pricing"').or(page.locator('[data-testid*="pricing"]')).first();
      if (await pricingSection.isVisible()) {
        await expect(pricingSection).toBeVisible();
      }
      
      // Check for call-to-action buttons
      const ctaButtons = page.locator('button:has-text("Get Started"), a:has-text("Get Started"), button:has-text("Start Free"), a:has-text("Start Free")');
      const ctaCount = await ctaButtons.count();
      if (ctaCount > 0) {
        await expect(ctaButtons.first()).toBeVisible();
      }
      
      console.log('✅ Landing page rendered successfully');
    });
    
    test('should render cookies page correctly', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check page title and main heading
      await expect(page.locator('h1:has-text("Cookie Settings")')).toBeVisible();
      
      // Check cookie overview section
      await expect(page.locator('text="What are cookies?"')).toBeVisible();
      
      // Check all cookie categories
      const cookieCategories = [
        'Essential Cookies',
        'Functional Cookies', 
        'Analytics Cookies',
        'Marketing Cookies'
      ];
      
      for (const category of cookieCategories) {
        await expect(page.locator(`text="${category}"`)).toBeVisible();
      }
      
      // Check that essential cookies toggle is disabled
      const essentialToggle = page.locator('[aria-label="Essential cookies (always enabled)"]');
      await expect(essentialToggle).toBeDisabled();
      await expect(essentialToggle).toBeChecked();
      
      // Check that other toggles are enabled
      const functionalToggle = page.locator('[aria-label="Functional cookies"]');
      const analyticsToggle = page.locator('[aria-label="Analytics cookies"]');
      const marketingToggle = page.locator('[aria-label="Marketing cookies"]');
      
      await expect(functionalToggle).toBeEnabled();
      await expect(analyticsToggle).toBeEnabled();
      await expect(marketingToggle).toBeEnabled();
      
      // Check action buttons
      await expect(page.locator('button:has-text("Save Preferences")')).toBeVisible();
      await expect(page.locator('button:has-text("Accept All Cookies")')).toBeVisible();
      await expect(page.locator('button:has-text("Essential Only")')).toBeVisible();
      
      // Check contact information
      await expect(page.locator('a[href="mailto:<EMAIL>"]')).toBeVisible();
      
      console.log('✅ Cookies page rendered successfully');
    });
    
    test('should handle cookie preferences interactions', async ({ page }) => {
      await page.goto('/cookies');
      
      // Test functional cookies toggle
      const functionalToggle = page.locator('[aria-label="Functional cookies"]');
      await functionalToggle.click();
      
      // Test analytics cookies toggle
      const analyticsToggle = page.locator('[aria-label="Analytics cookies"]');
      await analyticsToggle.click();
      
      // Test marketing cookies toggle
      const marketingToggle = page.locator('[aria-label="Marketing cookies"]');
      await marketingToggle.click();
      
      // Save preferences
      await page.click('button:has-text("Save Preferences")');
      
      // Should show success toast
      await expect(page.locator('text="Cookie preferences saved successfully!"')).toBeVisible({ timeout: 5000 });
      
      // Test Accept All button
      await page.click('button:has-text("Accept All Cookies")');
      await expect(page.locator('text="All cookies accepted!"')).toBeVisible({ timeout: 5000 });
      
      // Test Essential Only button
      await page.click('button:has-text("Essential Only")');
      await expect(page.locator('text="Reset to essential cookies only!"')).toBeVisible({ timeout: 5000 });
      
      console.log('✅ Cookie preferences interactions working');
    });
    
    test('should persist cookie preferences', async ({ page }) => {
      await page.goto('/cookies');
      
      // Set specific preferences
      await page.click('[aria-label="Functional cookies"]');
      await page.click('[aria-label="Analytics cookies"]');
      // Leave marketing unchecked
      
      await page.click('button:has-text("Save Preferences")');
      await expect(page.locator('text="Cookie preferences saved successfully!"')).toBeVisible();
      
      // Reload page
      await page.reload();
      
      // Check that preferences are persisted
      await expect(page.locator('[aria-label="Essential cookies (always enabled)"]')).toBeChecked();
      await expect(page.locator('[aria-label="Functional cookies"]')).toBeChecked();
      await expect(page.locator('[aria-label="Analytics cookies"]')).toBeChecked();
      await expect(page.locator('[aria-label="Marketing cookies"]')).not.toBeChecked();
      
      console.log('✅ Cookie preferences persistence working');
    });
    
    test('should navigate to auth pages', async ({ page }) => {
      await page.goto('/');
      
      // Look for sign in/login links
      const signInLinks = page.locator('a:has-text("Sign In"), a:has-text("Login"), a[href*="signin"], a[href*="login"]');
      const signInCount = await signInLinks.count();
      
      if (signInCount > 0) {
        await signInLinks.first().click();
        
        // Should navigate to sign in page
        await expect(page).toHaveURL(/\/(signin|login|auth)/);
        
        // Should show sign in form or redirect to OAuth
        const authForm = page.locator('form, button:has-text("Sign in"), button:has-text("Login")');
        await expect(authForm.first()).toBeVisible({ timeout: 10000 });
        
        console.log('✅ Navigation to auth pages working');
      } else {
        console.log('⚠️ No sign in links found on landing page');
      }
    });
    
    test('should handle responsive design', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/');
      
      // Page should still be functional on mobile
      await expect(page.locator('main')).toBeVisible();
      
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      
      await expect(page.locator('main')).toBeVisible();
      
      // Test desktop viewport
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.reload();
      
      await expect(page.locator('main')).toBeVisible();
      
      console.log('✅ Responsive design working');
    });
  });
  
  test.describe('Error Pages', () => {
    test('should handle 404 pages gracefully', async ({ page }) => {
      await page.goto('/non-existent-page');
      
      // Should show 404 or redirect to home
      const is404 = await page.locator('text="404"').or(page.locator('text="Not Found"')).isVisible();
      const isRedirected = page.url().endsWith('/');
      
      expect(is404 || isRedirected).toBeTruthy();
      
      if (is404) {
        // Should have link back to home
        const homeLink = page.locator('a[href="/"], a:has-text("Home"), a:has-text("Back to Home")');
        const homeLinkCount = await homeLink.count();
        if (homeLinkCount > 0) {
          await expect(homeLink.first()).toBeVisible();
        }
      }
      
      console.log('✅ 404 handling working');
    });
  });
  
  test.describe('Performance and Accessibility', () => {
    test('should load pages within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      
      // Wait for main content to be visible
      await page.locator('main').waitFor({ state: 'visible' });
      
      const loadTime = Date.now() - startTime;
      
      // Page should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      console.log(`✅ Landing page loaded in ${loadTime}ms`);
    });
    
    test('should have proper page titles and meta tags', async ({ page }) => {
      await page.goto('/');
      
      // Check page title
      const title = await page.title();
      expect(title.length).toBeGreaterThan(0);
      expect(title).not.toBe('');
      
      // Check meta description
      const metaDescription = await page.locator('meta[name="description"]').getAttribute('content');
      if (metaDescription) {
        expect(metaDescription.length).toBeGreaterThan(0);
      }
      
      // Check cookies page
      await page.goto('/cookies');
      const cookiesTitle = await page.title();
      expect(cookiesTitle.length).toBeGreaterThan(0);
      expect(cookiesTitle).toContain('Cookie');
      
      console.log('✅ Page titles and meta tags present');
    });
    
    test('should have accessible form elements', async ({ page }) => {
      await page.goto('/cookies');
      
      // Check that toggles have proper labels
      const toggles = page.locator('button[role="switch"]');
      const toggleCount = await toggles.count();
      
      for (let i = 0; i < toggleCount; i++) {
        const toggle = toggles.nth(i);
        const ariaLabel = await toggle.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        expect(ariaLabel!.length).toBeGreaterThan(0);
      }
      
      // Check buttons have accessible text
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) { // Check first 10 buttons
        const button = buttons.nth(i);
        const buttonText = await button.textContent();
        const ariaLabel = await button.getAttribute('aria-label');
        
        // Button should have either text content or aria-label
        expect(buttonText || ariaLabel).toBeTruthy();
      }
      
      console.log('✅ Accessibility checks passed');
    });
  });
  
  test.describe('SEO and Social Media', () => {
    test('should have proper Open Graph tags', async ({ page }) => {
      await page.goto('/');
      
      // Check for Open Graph tags
      const ogTags = [
        'meta[property="og:title"]',
        'meta[property="og:description"]',
        'meta[property="og:type"]',
        'meta[property="og:url"]'
      ];
      
      for (const tagSelector of ogTags) {
        const tag = page.locator(tagSelector);
        if (await tag.count() > 0) {
          const content = await tag.getAttribute('content');
          expect(content).toBeTruthy();
          console.log(`✅ Found ${tagSelector}: ${content}`);
        }
      }
    });
    
    test('should have proper favicon and icons', async ({ page }) => {
      await page.goto('/');
      
      // Check for favicon
      const favicon = page.locator('link[rel="icon"], link[rel="shortcut icon"]');
      if (await favicon.count() > 0) {
        const href = await favicon.first().getAttribute('href');
        expect(href).toBeTruthy();
        console.log('✅ Favicon found');
      }
      
      // Check for apple touch icon
      const appleTouchIcon = page.locator('link[rel="apple-touch-icon"]');
      if (await appleTouchIcon.count() > 0) {
        const href = await appleTouchIcon.first().getAttribute('href');
        expect(href).toBeTruthy();
        console.log('✅ Apple touch icon found');
      }
    });
  });
  
  test.describe('Navigation and Layout', () => {
    test('should have consistent navigation across pages', async ({ page }) => {
      await page.goto('/');
      
      // Check for navigation elements
      const nav = page.locator('nav, [role="navigation"], header');
      if (await nav.count() > 0) {
        await expect(nav.first()).toBeVisible();
        
        // Navigate to cookies page
        await page.goto('/cookies');
        
        // Navigation should still be present
        const navOnCookies = page.locator('nav, [role="navigation"], header');
        if (await navOnCookies.count() > 0) {
          await expect(navOnCookies.first()).toBeVisible();
        }
        
        console.log('✅ Consistent navigation found');
      }
    });
    
    test('should have proper footer', async ({ page }) => {
      await page.goto('/');
      
      // Check for footer
      const footer = page.locator('footer, [role="contentinfo"]');
      if (await footer.count() > 0) {
        await expect(footer.first()).toBeVisible();
        
        // Footer should contain useful links
        const footerLinks = footer.locator('a');
        const linkCount = await footerLinks.count();
        
        if (linkCount > 0) {
          // Check some common footer links
          const commonFooterLinks = ['Privacy', 'Terms', 'Cookie', 'Contact'];
          for (const linkText of commonFooterLinks) {
            const link = footer.locator(`a:has-text("${linkText}")`);
            if (await link.count() > 0) {
              console.log(`✅ Found footer link: ${linkText}`);
            }
          }
        }
        
        console.log('✅ Footer found and functional');
      }
    });
  });
});