/**
 * Enhanced Merchant Workflows E2E Tests
 * ADC Credit Service - Comprehensive Merchant Journey Testing
 * 
 * Tests complete merchant workflows using the comprehensive test ID system
 * implemented in Phases 1-3. This test suite covers:
 * 
 * Test Categories:
 * - Shop Management & Creation Workflows
 * - Customer Management & Analytics
 * - API Key Management & Integration Testing
 * - QR Code Generation & Credit Distribution
 * - Subscription & Settings Management
 * 
 * Features the new test ID constants:
 * - SHOPS_TEST_IDS (create, edit, delete shops)
 * - CUSTOMERS_TEST_IDS (customer management)
 * - API_KEYS_TEST_IDS (API key management)
 * - GENERATE_TEST_IDS (QR code generation)
 * - SETTINGS_TEST_IDS (merchant settings)
 */

import { test, expect } from '@playwright/test';

// Test Group: Shop Management Workflow
test.describe('Enhanced Shop Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/shops');
  });

  test('should complete shop creation workflow with comprehensive test IDs', async ({ page }) => {
    // Verify shops listing page structure
    await expect(page.locator('[data-testid="shops-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-title"]')).toHaveText('My Shops');
    
    // Test create shop navigation
    await expect(page.locator('[data-testid="shops-create-button"]')).toBeVisible();
    await page.locator('[data-testid="shops-create-button"]').click();
    await expect(page).toHaveURL(/.*\/shops\/create/);
    
    // Verify create shop page structure
    await expect(page.locator('[data-testid="shops-create-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-create-title"]')).toHaveText('Create New Shop');
    await expect(page.locator('[data-testid="shops-create-form"]')).toBeVisible();
    
    // Test form fields
    await expect(page.locator('[data-testid="shops-create-name-label"]')).toHaveText('Shop Name');
    await expect(page.locator('[data-testid="shops-create-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-create-description-label"]')).toHaveText('Description');
    await expect(page.locator('[data-testid="shops-create-description-textarea"]')).toBeVisible();
    
    // Fill form with test data
    await page.locator('[data-testid="shops-create-name-input"]').fill('Test Coffee Shop');
    await page.locator('[data-testid="shops-create-description-textarea"]').fill('Premium coffee and pastries for the discerning customer');
    
    // Test form actions
    await expect(page.locator('[data-testid="shops-create-cancel-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-create-submit-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-create-submit-button"]')).toBeEnabled();
  });

  test('should handle shop listing with dynamic test IDs', async ({ page }) => {
    // Mock shops data for testing
    await page.route('**/api/v1/shops', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'shop-123',
            slug: 'coffee-central',
            name: 'Coffee Central',
            description: 'Premium coffee experience',
            customer_count: 45,
            total_credits_distributed: 2500,
            created_at: '2025-01-01T10:00:00Z'
          },
          {
            id: 'shop-456', 
            slug: 'pizza-palace',
            name: 'Pizza Palace',
            description: 'Authentic Italian pizza',
            customer_count: 23,
            total_credits_distributed: 1200,
            created_at: '2025-01-02T10:00:00Z'
          }
        ]
      });
    });

    await page.reload();
    
    // Test shops grid and individual shop cards
    await expect(page.locator('[data-testid="shops-grid"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-shop-card-shop-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-shop-card-shop-456"]')).toBeVisible();
    
    // Test shop card content using dynamic test IDs
    await expect(page.locator('[data-testid="shops-shop-name-shop-123"]')).toHaveText('Coffee Central');
    await expect(page.locator('[data-testid="shops-shop-description-shop-123"]')).toHaveText('Premium coffee experience');
    await expect(page.locator('[data-testid="shops-shop-customers-count-shop-123"]')).toContainText('45 customers');
    await expect(page.locator('[data-testid="shops-shop-credits-distributed-shop-123"]')).toContainText('2,500 credits');
    
    // Test shop actions
    await expect(page.locator('[data-testid="shops-shop-view-button-shop-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-shop-edit-button-shop-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-shop-delete-button-shop-123"]')).toBeVisible();
    
    // Test navigation to shop details
    await page.locator('[data-testid="shops-shop-view-button-shop-123"]').click();
    await expect(page).toHaveURL(/.*\/shops\/coffee-central/);
  });

  test('should handle empty shops state', async ({ page }) => {
    // Mock empty shops response
    await page.route('**/api/v1/shops', async (route) => {
      await route.fulfill({ json: [] });
    });

    await page.reload();
    
    // Test empty state
    await expect(page.locator('[data-testid="shops-empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-empty-state-icon"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-empty-state-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-empty-state-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="shops-empty-state-button"]')).toBeVisible();
    
    // Test create shop from empty state
    await page.locator('[data-testid="shops-empty-state-button"]').click();
    await expect(page).toHaveURL(/.*\/shops\/create/);
  });
});

// Test Group: Customer Management Workflow
test.describe('Enhanced Customer Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/customers');
  });

  test('should complete customer management workflow with test IDs', async ({ page }) => {
    // Verify customers page structure
    await expect(page.locator('[data-testid="customers-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-title"]')).toHaveText('Customers');
    
    // Test search functionality
    await expect(page.locator('[data-testid="customers-search-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-search-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-search-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-add-customer-button"]')).toBeVisible();
    
    // Test search interaction
    const searchInput = page.locator('[data-testid="customers-search-input"]');
    await searchInput.fill('<EMAIL>');
    await expect(searchInput).toHaveValue('<EMAIL>');
    
    // Test add customer navigation
    await page.locator('[data-testid="customers-add-customer-button"]').click();
    await expect(page).toHaveURL(/.*\/customers\/create/);
  });

  test('should display customer list with dynamic test IDs', async ({ page }) => {
    // Mock customers data
    await page.route('**/api/v1/customers', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'customer-123',
            email: '<EMAIL>',
            name: 'John Doe',
            phone: '******-0123',
            total_credits: 150,
            total_spent: 450,
            last_activity: '2025-01-01T10:00:00Z'
          },
          {
            id: 'customer-456',
            email: '<EMAIL>', 
            name: 'Jane Smith',
            phone: '******-0456',
            total_credits: 75,
            total_spent: 225,
            last_activity: '2025-01-02T10:00:00Z'
          }
        ]
      });
    });

    await page.reload();
    
    // Test customers table structure
    await expect(page.locator('[data-testid="customers-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-table-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-table-body"]')).toBeVisible();
    
    // Test dynamic customer rows
    await expect(page.locator('[data-testid="customers-table-row-customer-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-table-row-customer-456"]')).toBeVisible();
    
    // Test customer data cells using dynamic test IDs
    await expect(page.locator('[data-testid="customers-table-email-customer-123"]')).toHaveText('<EMAIL>');
    await expect(page.locator('[data-testid="customers-table-name-customer-123"]')).toHaveText('John Doe');
    await expect(page.locator('[data-testid="customers-table-credits-customer-123"]')).toContainText('150');
    await expect(page.locator('[data-testid="customers-table-spent-customer-123"]')).toContainText('450');
    
    // Test customer actions
    await expect(page.locator('[data-testid="customers-table-view-button-customer-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-table-edit-button-customer-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-table-delete-button-customer-123"]')).toBeVisible();
  });

  test('should handle customer creation workflow', async ({ page }) => {
    await page.goto('/dashboard/customers/create');
    
    // Test create customer form
    await expect(page.locator('[data-testid="customers-create-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-title"]')).toHaveText('Add New Customer');
    await expect(page.locator('[data-testid="customers-create-form"]')).toBeVisible();
    
    // Test form fields
    await expect(page.locator('[data-testid="customers-create-email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-phone-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-shop-select"]')).toBeVisible();
    
    // Fill form
    await page.locator('[data-testid="customers-create-email-input"]').fill('<EMAIL>');
    await page.locator('[data-testid="customers-create-name-input"]').fill('New Customer');
    await page.locator('[data-testid="customers-create-phone-input"]').fill('******-0789');
    
    // Test form actions
    await expect(page.locator('[data-testid="customers-create-cancel-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-submit-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="customers-create-submit-button"]')).toBeEnabled();
  });
});

// Test Group: API Key Management Workflow  
test.describe('Enhanced API Key Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/api-keys');
  });

  test('should complete API key management workflow with test IDs', async ({ page }) => {
    // Verify API keys page structure
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-title"]')).toHaveText('API Keys');
    await expect(page.locator('[data-testid="api-keys-description"]')).toBeVisible();
    
    // Test create API key functionality
    await expect(page.locator('[data-testid="api-keys-create-button"]')).toBeVisible();
    await page.locator('[data-testid="api-keys-create-button"]').click();
    
    // Test create API key modal/form
    await expect(page.locator('[data-testid="api-keys-create-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-modal-title"]')).toHaveText('Create New API Key');
    await expect(page.locator('[data-testid="api-keys-create-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-description-input"]')).toBeVisible();
    
    // Fill API key form
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Test Integration Key');
    await page.locator('[data-testid="api-keys-create-description-input"]').fill('API key for testing integration workflows');
    
    // Test form actions
    await expect(page.locator('[data-testid="api-keys-create-cancel-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-create-submit-button"]')).toBeVisible();
  });

  test('should display API keys list with dynamic test IDs', async ({ page }) => {
    // Mock API keys data
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'key-123',
            name: 'Production API Key',
            description: 'Main production integration',
            key_preview: 'adc_live_1234...abcd',
            last_used: '2025-01-01T10:00:00Z',
            created_at: '2024-12-01T10:00:00Z',
            is_active: true
          },
          {
            id: 'key-456',
            name: 'Development API Key',
            description: 'Testing and development',
            key_preview: 'adc_test_5678...efgh',
            last_used: null,
            created_at: '2024-12-15T10:00:00Z',
            is_active: false
          }
        ]
      });
    });

    await page.reload();
    
    // Test API keys table
    await expect(page.locator('[data-testid="api-keys-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-header"]')).toBeVisible();
    
    // Test dynamic API key rows
    await expect(page.locator('[data-testid="api-keys-table-row-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-row-key-456"]')).toBeVisible();
    
    // Test API key data with dynamic test IDs
    await expect(page.locator('[data-testid="api-keys-table-name-key-123"]')).toHaveText('Production API Key');
    await expect(page.locator('[data-testid="api-keys-table-preview-key-123"]')).toHaveText('adc_live_1234...abcd');
    await expect(page.locator('[data-testid="api-keys-table-status-active-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-status-inactive-key-456"]')).toBeVisible();
    
    // Test API key actions
    await expect(page.locator('[data-testid="api-keys-table-copy-button-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-edit-button-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-delete-button-key-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-table-toggle-button-key-123"]')).toBeVisible();
  });

  test('should handle API key actions and modals', async ({ page }) => {
    // Mock API key for actions
    await page.route('**/api/v1/api-keys', async (route) => {
      await route.fulfill({
        json: [
          {
            id: 'key-789',
            name: 'Test API Key',
            description: 'Testing key actions',
            key_preview: 'adc_test_9999...zzzz',
            is_active: true
          }
        ]
      });
    });

    await page.reload();
    
    // Test copy key functionality
    await page.locator('[data-testid="api-keys-table-copy-button-key-789"]').click();
    await expect(page.locator('[data-testid="api-keys-copy-success-message"]')).toBeVisible();
    
    // Test edit key modal
    await page.locator('[data-testid="api-keys-table-edit-button-key-789"]').click();
    await expect(page.locator('[data-testid="api-keys-edit-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-edit-modal-title"]')).toHaveText('Edit API Key');
    
    // Test delete confirmation
    await page.locator('[data-testid="api-keys-table-delete-button-key-789"]').click();
    await expect(page.locator('[data-testid="api-keys-delete-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-delete-modal-title"]')).toHaveText('Delete API Key');
    await expect(page.locator('[data-testid="api-keys-delete-confirm-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="api-keys-delete-cancel-button"]')).toBeVisible();
  });
});

// Test Group: QR Code Generation Workflow
test.describe('Enhanced QR Code Generation Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/generate');
  });

  test('should complete QR code generation workflow with test IDs', async ({ page }) => {
    // Verify generate page structure
    await expect(page.locator('[data-testid="generate-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-main-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-title"]')).toHaveText('Generate QR Codes');
    
    // Test form structure
    await expect(page.locator('[data-testid="generate-form"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-shop-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-credit-amount-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-quantity-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-expiry-date-input"]')).toBeVisible();
    
    // Fill form
    await page.locator('[data-testid="generate-credit-amount-input"]').fill('50');
    await page.locator('[data-testid="generate-quantity-input"]').fill('10');
    
    // Test generate button
    await expect(page.locator('[data-testid="generate-submit-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-submit-button"]')).toBeEnabled();
  });

  test('should handle QR code generation results', async ({ page }) => {
    // Mock successful generation
    await page.route('**/api/v1/generate-codes', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          success: true,
          codes: [
            { id: 'code-123', code: 'QR123456', credit_amount: 50 },
            { id: 'code-456', code: 'QR789012', credit_amount: 50 }
          ],
          total_generated: 2,
          total_credits: 100
        }
      });
    });

    // Fill and submit form
    await page.locator('[data-testid="generate-credit-amount-input"]').fill('50');
    await page.locator('[data-testid="generate-quantity-input"]').fill('2');
    await page.locator('[data-testid="generate-submit-button"]').click();
    
    // Test success result
    await expect(page.locator('[data-testid="generate-success-result"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-result-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-result-codes-count"]')).toContainText('2 codes');
    await expect(page.locator('[data-testid="generate-result-total-credits"]')).toContainText('100 credits');
    
    // Test generated codes list
    await expect(page.locator('[data-testid="generate-codes-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-code-item-code-123"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-code-item-code-456"]')).toBeVisible();
    
    // Test download functionality
    await expect(page.locator('[data-testid="generate-download-csv-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-download-pdf-button"]')).toBeVisible();
  });

  test('should handle form validation and errors', async ({ page }) => {
    // Test validation - empty form
    await page.locator('[data-testid="generate-submit-button"]').click();
    await expect(page.locator('[data-testid="generate-shop-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-credit-amount-error"]')).toBeVisible();
    
    // Test invalid credit amount
    await page.locator('[data-testid="generate-credit-amount-input"]').fill('0');
    await expect(page.locator('[data-testid="generate-credit-amount-error"]')).toHaveText('Credit amount must be greater than 0');
    
    // Test invalid quantity
    await page.locator('[data-testid="generate-quantity-input"]').fill('101');
    await expect(page.locator('[data-testid="generate-quantity-error"]')).toHaveText('Maximum 100 codes per generation');
  });
});

// Test Group: Cross-Feature Integration Testing
test.describe('Enhanced Merchant Integration Workflows', () => {
  test('should complete end-to-end merchant journey', async ({ page }) => {
    // Start from dashboard
    await page.goto('/dashboard');
    
    // Navigate to shops and create shop
    await page.locator('[data-testid="dashboard-shops-nav-button"]').click();
    await expect(page).toHaveURL(/.*\/shops/);
    
    // Create new shop
    await page.locator('[data-testid="shops-create-button"]').click();
    await page.locator('[data-testid="shops-create-name-input"]').fill('Integration Test Shop');
    await page.locator('[data-testid="shops-create-description-textarea"]').fill('Shop for testing integration workflows');
    
    // Navigate to customers
    await page.goto('/dashboard/customers');
    await expect(page.locator('[data-testid="customers-container"]')).toBeVisible();
    
    // Add customer
    await page.locator('[data-testid="customers-add-customer-button"]').click();
    await page.locator('[data-testid="customers-create-email-input"]').fill('<EMAIL>');
    
    // Navigate to QR generation
    await page.goto('/dashboard/generate');
    await expect(page.locator('[data-testid="generate-container"]')).toBeVisible();
    
    // Generate QR codes
    await page.locator('[data-testid="generate-credit-amount-input"]').fill('25');
    await page.locator('[data-testid="generate-quantity-input"]').fill('5');
    
    // Navigate to API keys
    await page.goto('/dashboard/api-keys');
    await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
    
    // Create API key for integration
    await page.locator('[data-testid="api-keys-create-button"]').click();
    await page.locator('[data-testid="api-keys-create-name-input"]').fill('Integration Test Key');
  });

  test('should maintain consistent navigation across merchant features', async ({ page }) => {
    const merchantPages = [
      { url: '/dashboard/shops', container: 'shops-container', title: 'shops-title' },
      { url: '/dashboard/customers', container: 'customers-container', title: 'customers-title' },
      { url: '/dashboard/api-keys', container: 'api-keys-container', title: 'api-keys-title' },
      { url: '/dashboard/generate', container: 'generate-container', title: 'generate-title' }
    ];

    for (const pageInfo of merchantPages) {
      await page.goto(pageInfo.url);
      
      // Verify consistent structure
      await expect(page.locator(`[data-testid="${pageInfo.container}"]`)).toBeVisible();
      await expect(page.locator(`[data-testid="${pageInfo.title}"]`)).toBeVisible();
      
      // Check for consistent navigation elements
      // Note: Actual nav structure depends on implementation
    }
  });
});

// Test Group: Responsive Merchant Experience
test.describe('Enhanced Merchant Responsive Design', () => {
  ['mobile', 'tablet', 'desktop'].forEach(device => {
    test(`should provide optimal merchant experience on ${device}`, async ({ page }) => {
      // Set viewport for device
      const viewports = {
        'mobile': { width: 375, height: 667 },
        'tablet': { width: 768, height: 1024 },
        'desktop': { width: 1440, height: 900 }
      };
      
      await page.setViewportSize(viewports[device]);
      
      // Test shops page responsiveness
      await page.goto('/dashboard/shops');
      await expect(page.locator('[data-testid="shops-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="shops-title"]')).toBeVisible();
      
      // Test customers page responsiveness
      await page.goto('/dashboard/customers');
      await expect(page.locator('[data-testid="customers-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="customers-search-input"]')).toBeVisible();
      
      // Test API keys page responsiveness
      await page.goto('/dashboard/api-keys');
      await expect(page.locator('[data-testid="api-keys-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="api-keys-create-button"]')).toBeVisible();
      
      // Test generate page responsiveness
      await page.goto('/dashboard/generate');
      await expect(page.locator('[data-testid="generate-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="generate-form"]')).toBeVisible();
    });
  });
});