/**
 * ADC Credit Business Flows E2E Tests
 * 
 * Tests complete business workflows in the ADC Credit application
 */

import { test, expect } from '@playwright/test';

test.describe('ADC Credit Business Flows', () => {
  
  test.describe('Customer Credit Redemption Flow', () => {
    test('should handle QR code credit redemption', async ({ page }) => {
      // Test the public QR code redemption flow
      await page.goto('/redeem');
      
      // Should show QR code scanner or input field
      const qrInput = page.locator('input[placeholder*="code"], input[name*="code"], [data-testid*="qr"]');
      const scanButton = page.locator('button:has-text("Scan"), [data-testid*="scan"]');
      
      if (await qrInput.count() > 0) {
        // Test with a sample QR code
        await qrInput.fill('TEST-QR-CODE-12345');
        
        const submitButton = page.locator('button[type="submit"], button:has-text("Redeem")');
        if (await submitButton.count() > 0) {
          await submitButton.click();
          
          // Should show result (success or error)
          const result = page.locator('[data-testid*="result"], .alert, .message');
          await expect(result.first()).toBeVisible({ timeout: 10000 });
          
          console.log('✅ QR code redemption flow functional');
        }
      } else if (await scanButton.count() > 0) {
        console.log('✅ QR code scanner interface available');
      } else {
        console.log('⚠️ QR code redemption interface not found');
      }
    });
    
    test('should display redemption history for customers', async ({ page }) => {
      // Test if there's a way for customers to view their redemption history
      await page.goto('/history');
      
      if (page.url().includes('/auth/')) {
        // Try alternative routes
        await page.goto('/my-credits');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/credits/history');
      }
      
      if (!page.url().includes('/auth/')) {
        // Look for transaction history
        const historyContent = page.locator('table, [data-testid*="history"], [data-testid*="transaction"]');
        const emptyState = page.locator('text*="No history", text*="No transactions"');
        
        const hasHistory = await historyContent.count() > 0;
        const isEmpty = await emptyState.count() > 0;
        
        expect(hasHistory || isEmpty).toBeTruthy();
        console.log('✅ Customer history page accessible');
      }
    });
  });
  
  test.describe('Shop Owner Management Flow', () => {
    test('should handle shop registration process', async ({ page }) => {
      await page.goto('/register-shop');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/merchant/register');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/shops/register');
      }
      
      // Look for shop registration form
      const registrationForm = page.locator('form, [data-testid*="register"], [data-testid*="shop-form"]');
      
      if (await registrationForm.count() > 0) {
        // Test form fields
        const nameField = page.locator('input[name*="name"], input[placeholder*="name"]');
        const emailField = page.locator('input[type="email"], input[name*="email"]');
        const phoneField = page.locator('input[type="tel"], input[name*="phone"]');
        
        if (await nameField.count() > 0) {
          await nameField.fill('Test Coffee Shop');
        }
        
        if (await emailField.count() > 0) {
          await emailField.fill('<EMAIL>');
        }
        
        if (await phoneField.count() > 0) {
          await phoneField.fill('555-0123');
        }
        
        // Look for category or business type field
        const categoryField = page.locator('select[name*="category"], select[name*="type"]');
        if (await categoryField.count() > 0) {
          await categoryField.selectOption({ index: 1 }); // Select first option
        }
        
        console.log('✅ Shop registration form is functional');
      } else {
        console.log('⚠️ Shop registration form not found');
      }
    });
    
    test('should display shop dashboard for owners', async ({ page }) => {
      // Try to access a shop-specific dashboard
      await page.goto('/merchant/dashboard');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/shop/dashboard');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/dashboard/shop');
      }
      
      if (!page.url().includes('/auth/')) {
        // Look for shop-specific content
        const shopContent = page.locator('[data-testid*="shop"], [data-testid*="merchant"]');
        
        if (await shopContent.count() > 0) {
          await expect(shopContent.first()).toBeVisible();
          console.log('✅ Shop dashboard accessible');
          
          // Look for key shop metrics
          const metrics = [
            'customers',
            'credits',
            'redemptions',
            'revenue',
            'transactions'
          ];
          
          for (const metric of metrics) {
            const metricElement = page.locator(`text*="${metric}", [data-testid*="${metric}"]`);
            if (await metricElement.count() > 0) {
              console.log(`✅ Found ${metric} metric`);
            }
          }
        }
      }
    });
  });
  
  test.describe('Credit Management Flow', () => {
    test('should handle credit purchase process', async ({ page }) => {
      await page.goto('/buy-credits');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/credits/buy');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/purchase');
      }
      
      // Look for credit purchase interface
      const purchaseInterface = page.locator('[data-testid*="purchase"], [data-testid*="buy"]');
      
      if (await purchaseInterface.count() > 0) {
        // Look for credit packages
        const packages = page.locator('[data-testid*="package"], .package, .tier');
        
        if (await packages.count() > 0) {
          const packageCount = await packages.count();
          console.log(`✅ Found ${packageCount} credit packages`);
          
          // Test selecting a package
          await packages.first().click();
          
          // Look for payment or checkout button
          const checkoutButton = page.locator(
            'button:has-text("Buy"), button:has-text("Purchase"), button:has-text("Checkout")'
          );
          
          if (await checkoutButton.count() > 0) {
            console.log('✅ Credit purchase flow is functional');
          }
        }
      } else {
        console.log('⚠️ Credit purchase interface not found');
      }
    });
    
    test('should display credit balance and usage', async ({ page }) => {
      await page.goto('/');
      
      // Look for credit balance display anywhere on the site
      const creditDisplay = page.locator(
        '[data-testid*="credit"], [data-testid*="balance"], text*="credits", text*="balance"'
      );
      
      if (await creditDisplay.count() > 0) {
        await expect(creditDisplay.first()).toBeVisible();
        console.log('✅ Credit balance is displayed');
        
        // Check if the balance shows a number
        const balanceText = await creditDisplay.first().textContent();
        const hasNumber = /\d/.test(balanceText || '');
        
        if (hasNumber) {
          console.log('✅ Credit balance shows numeric value');
        }
      }
      
      // Look for usage history or recent transactions
      const usageHistory = page.locator(
        '[data-testid*="usage"], [data-testid*="history"], [data-testid*="transaction"]'
      );
      
      if (await usageHistory.count() > 0) {
        console.log('✅ Credit usage information available');
      }
    });
  });
  
  test.describe('Analytics and Reporting Flow', () => {
    test('should provide business analytics', async ({ page }) => {
      await page.goto('/analytics');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/reports');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/dashboard/analytics');
      }
      
      if (!page.url().includes('/auth/')) {
        // Look for analytics content
        const analyticsContent = page.locator('[data-testid*="analytics"], [data-testid*="chart"]');
        
        if (await analyticsContent.count() > 0) {
          await expect(analyticsContent.first()).toBeVisible();
          console.log('✅ Analytics dashboard accessible');
          
          // Look for different types of analytics
          const chartTypes = ['chart', 'graph', 'metric', 'stat'];
          
          for (const chartType of chartTypes) {
            const charts = page.locator(`[data-testid*="${chartType}"], .${chartType}`);
            const chartCount = await charts.count();
            
            if (chartCount > 0) {
              console.log(`✅ Found ${chartCount} ${chartType} elements`);
            }
          }
          
          // Look for export functionality
          const exportButton = page.locator('button:has-text("Export"), button:has-text("Download")');
          if (await exportButton.count() > 0) {
            console.log('✅ Export functionality available');
          }
        }
      }
    });
    
    test('should show real-time usage statistics', async ({ page }) => {
      await page.goto('/dashboard');
      
      if (!page.url().includes('/auth/')) {
        // Look for real-time or recent activity
        const realtimeElements = page.locator(
          'text*="real-time", text*="live", text*="recent", [data-testid*="realtime"]'
        );
        
        if (await realtimeElements.count() > 0) {
          console.log('✅ Real-time statistics available');
        }
        
        // Look for timestamp or "last updated" information
        const timestamps = page.locator(
          'text*="ago", text*="updated", text*="last", [data-testid*="timestamp"]'
        );
        
        if (await timestamps.count() > 0) {
          console.log('✅ Timestamp information displayed');
        }
      }
    });
  });
  
  test.describe('API Integration Flow', () => {
    test('should provide API documentation', async ({ page }) => {
      await page.goto('/api');
      
      if (page.url().includes('/auth/')) {
        await page.goto('/docs');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/api-docs');
      }
      
      if (page.url().includes('/auth/')) {
        await page.goto('/developers');
      }
      
      // Look for API documentation
      const apiDocs = page.locator('[data-testid*="api"], [data-testid*="docs"]');
      
      if (await apiDocs.count() > 0) {
        await expect(apiDocs.first()).toBeVisible();
        console.log('✅ API documentation accessible');
        
        // Look for common API doc elements
        const docElements = [
          'endpoints',\n          'authentication',\n          'examples',\n          'response',\n          'request'\n        ];\n        \n        for (const element of docElements) {\n          const elementLocator = page.locator(`text*=\"${element}\", [data-testid*=\"${element}\"]`);\n          if (await elementLocator.count() > 0) {\n            console.log(`✅ Found ${element} documentation`);\n          }\n        }\n      }\n    });\n    \n    test('should display API key management', async ({ page }) => {\n      await page.goto('/api-keys');\n      \n      if (page.url().includes('/auth/')) {\n        await page.goto('/developers/keys');\n      }\n      \n      if (page.url().includes('/auth/')) {\n        await page.goto('/settings/api');\n      }\n      \n      if (!page.url().includes('/auth/')) {\n        // Look for API key management interface\n        const apiKeyInterface = page.locator('[data-testid*=\"api\"], [data-testid*=\"key\"]');\n        \n        if (await apiKeyInterface.count() > 0) {\n          await expect(apiKeyInterface.first()).toBeVisible();\n          console.log('✅ API key management accessible');\n          \n          // Look for create API key functionality\n          const createButton = page.locator(\n            'button:has-text(\"Create\"), button:has-text(\"Generate\"), [data-testid*=\"create\"]'\n          );\n          \n          if (await createButton.count() > 0) {\n            console.log('✅ API key creation available');\n          }\n          \n          // Look for existing API keys list\n          const keysList = page.locator('table, [data-testid*=\"keys-list\"], .api-keys');\n          const emptyState = page.locator('text*=\"No API keys\", text*=\"No keys\"');\n          \n          const hasKeys = await keysList.count() > 0;\n          const isEmpty = await emptyState.count() > 0;\n          \n          expect(hasKeys || isEmpty).toBeTruthy();\n          console.log('✅ API keys display is functional');\n        }\n      }\n    });\n  });\n  \n  test.describe('Mobile Responsiveness', () => {\n    test('should work on mobile devices', async ({ page }) => {\n      // Set mobile viewport\n      await page.setViewportSize({ width: 375, height: 667 });\n      \n      // Test key pages on mobile\n      const pagesToTest = ['/', '/redeem', '/dashboard'];\n      \n      for (const pagePath of pagesToTest) {\n        await page.goto(pagePath);\n        \n        if (!page.url().includes('/auth/')) {\n          // Page should be visible and functional\n          const mainContent = page.locator('main, body');\n          await expect(mainContent.first()).toBeVisible();\n          \n          // Check for mobile-friendly navigation\n          const mobileNav = page.locator('[data-testid*=\"mobile\"], .mobile-menu, button[aria-label*=\"menu\"]');\n          \n          if (await mobileNav.count() > 0) {\n            console.log(`✅ Mobile navigation found on ${pagePath}`);\n          }\n          \n          console.log(`✅ ${pagePath} is mobile responsive`);\n        }\n      }\n    });\n    \n    test('should handle touch interactions', async ({ page }) => {\n      // Set mobile viewport with touch\n      await page.setViewportSize({ width: 375, height: 667 });\n      \n      await page.goto('/');\n      \n      // Test touch interactions\n      const touchableElements = page.locator('button, a, [role=\"button\"]');\n      const elementCount = await touchableElements.count();\n      \n      if (elementCount > 0) {\n        // Test first few elements\n        for (let i = 0; i < Math.min(elementCount, 3); i++) {\n          const element = touchableElements.nth(i);\n          \n          if (await element.isVisible()) {\n            // Simulate touch\n            await element.tap();\n            \n            // Wait a moment for any navigation or state change\n            await page.waitForTimeout(500);\n            \n            console.log(`✅ Touch interaction ${i + 1} successful`);\n          }\n        }\n      }\n    });\n  });\n});