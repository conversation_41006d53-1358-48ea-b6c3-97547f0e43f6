{"permissions": {"allow": ["Bash(rm:*)", "Bash(grep:*)", "Bash(npm run lint)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(go run:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "Bash(find:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm run test:integration:*)", "Bash(npm test:*)", "Bash(node:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(go build:*)", "<PERSON><PERSON>(curl:*)", "Bash(TEST_API_URL=http://localhost:8100 npx jest src/__tests__/integration/users.integration.test.ts --verbose)", "Bash(TEST_API_URL=http://localhost:8100 npx jest src/__tests__/integration/users.integration.test.ts --verbose --no-coverage)", "Bash(TEST_API_URL=http://localhost:8100 npx jest src/__tests__/integration/users.integration.test.ts --verbose --no-coverage --maxWorkers=1)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git merge:*)", "Bash(git branch:*)", "Bash(bunx:*)", "Bash(bun add:*)", "Bash(npm run dev:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(go test:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mv:*)", "Bash(npm run test:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./debug-slug.sh:*)", "Bash(/Users/<USER>/Desktop/adc/adc-credit/debug-slug.sh)", "Bash(cp:*)", "<PERSON><PERSON>(make:*)", "Bash(gcloud builds:*)", "Bash(gcloud run services list:*)", "Bash(gcloud run services describe:*)", "Bash(npm run lint:*)", "Bash(psql:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(true)", "Bash(go mod init:*)", "Bash(go mod:*)", "Bash(GOOS=linux go run -c '\npackage main\nimport (\n\t\"fmt\"\n\t\"gorm.io/driver/postgres\"\n\t\"gorm.io/gorm\"\n)\ntype ST struct {\n\tID uint `json:\"id\"`\n\tName string `json:\"name\"`\n\tPrice float64 `json:\"price\"`\n}\nfunc main() {\n\tdb, _ := gorm.Open(postgres.Open(\"postgresql://postgres:<EMAIL>:5432/postgres\"), &gorm.Config{})\n\tvar tiers []ST\n\tdb.Find(&tiers)\n\tfmt.Printf(\"Found %d tiers\\n\", len(tiers))\n\tfor _, t := range tiers {\n\t\tfmt.Printf(\"ID: %d, Name: %s, Price: $%.2f\\n\", t.ID, t.Name, t.Price)\n\t}\n}')", "Bash(go get:*)", "Bash(./replace-log-with-logrus.sh:*)", "Bash(echo \"Checking recent Cloud Run logs...\" gcloud logging read \"resource.type=cloud_run_revision AND resource.labels.service_name=adc-credit-backend\" --limit=5 --format=\"value(timestamp,jsonPayload.message,jsonPayload.correlation_id)\" 2 > /dev/null)", "Bash(git reset:*)", "Bash(echo \"Checking frontend build status...\" gcloud builds list --limit=2 --format=\"table(id,status,createTime,source.storageSource.object)\")", "Bash(gcloud run domain-mappings create:*)", "Bash(--service=adc-credit-frontend )", "Bash(--domain=adcshop.store )", "Bash(--region=us-central1)", "Bash(__NEW_LINE__ echo \"\")", "Bash(--service=adc-credit-backend )", "Bash(--domain=api.adcshop.store )", "Bash(gcloud beta run domain-mappings create:*)", "Bash(--service=adc-credit-frontend )", "Bash(--domain=adcshop.store )", "Bash(--service=adc-credit-backend )", "Bash(--domain=api.adcshop.store )", "Bash(gcloud config get-value:*)", "Bash(gcloud run domain-mappings:*)", "Bash(gcloud beta run domain-mappings list:*)", "Bash(gcloud domains:*)", "Bash(gcloud dns managed-zones:*)", "<PERSON><PERSON>(gcloud dns record-sets list:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(gcloud beta run domain-mappings describe:*)", "Bash(gcloud run services update:*)", "Bash(gcloud sql instances create:*)", "Bash(--database-version=POSTGRES_15 )", "Bash(--tier=db-f1-micro )", "Bash(--storage-type=SSD )", "Bash(--storage-size=10GB )", "Bash(--storage-auto-increase )", "<PERSON><PERSON>(--backup )", "Bash(--maintenance-window-day=SUN )", "Bash(--maintenance-window-hour=03 )", "Bash(--maintenance-release-channel=production)", "Bash(gcloud sql instances describe:*)", "Bash(gcloud sql databases create:*)", "Bash(./scripts/setup-cloud-sql.sh:*)", "<PERSON><PERSON>(openssl rand:*)", "Bash(gcloud logs read:*)", "Bash(gcloud logging read:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker:*)", "Bash(bun run:*)", "Bash(gcloud run services get-iam-policy:*)", "Bash(gcloud run services add-iam-policy-binding:*)", "Bash(echo \"After completing verification in Google Search Console, run:\n\n# Check if domain is now verified\ngcloud domains list-user-verified\n\n# If adccredit.store appears in the list, create the domain mapping:\ngcloud beta run domain-mappings create --service=adc-credit-frontend-prod --domain=credit.adccredit.store --region=us-central1\n\n# Then update DNS to point credit.adccredit.store to Google Cloud Run:\n# Add CNAME record: credit.adccredit.store -> ghs.googlehosted.com\")", "Bash(stripe:*)", "Bash(--url https://api.credit.adcshop.store/webhook/stripe )", "Bash(--events payment_intent.succeeded,payment_intent.payment_failed,checkout.session.completed,invoice.payment_succeeded,invoice.payment_failed,customer.subscription.created,customer.subscription.updated,customer.subscription.deleted)", "Bash(--url https://api.credit.adcshop.store/webhook/stripe )", "<PERSON><PERSON>(--live )", "Bash(--url https://api.credit.adcshop.store/webhook/stripe )", "<PERSON><PERSON>(--description \"ADC Credit Production Webhook\" )", "Bash(--enabled-events \"checkout.session.completed,payment_intent.succeeded,payment_intent.payment_failed,invoice.payment_succeeded,invoice.payment_failed,customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,customer.created,customer.updated\")", "Bash(PROJECT_ID=scandine-457107 make deploy-prod)", "Bash(gcloud run deploy:*)", "Bash(--source=./backend )", "Bash(--platform=managed )", "Bash(--allow-unauthenticated )", "Bash(--port=8400 )", "Bash(--memory=512Mi )", "Bash(--cpu=1 )", "Bash(--max-instances=10 )", "Bash(--env-vars-file=backend/.env.prod)", "Bash(gcloud run services logs read:*)", "Bash(PROJECT_ID=scandine-457107 ./scripts/deploy-prod.sh)", "Bash(gcloud run revisions describe:*)", "Bash(git commit:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(gcloud auth:*)", "Bash(gcloud config set:*)", "Bash(gcloud beta run domain-mappings replace:*)", "Bash(gcloud beta run domain-mappings delete:*)", "Bash(git remote set-url:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(gh pr create:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(gh pr view:*)", "Bash(gh workflow run:*)", "<PERSON><PERSON>(gh secret:*)", "Bash(PORT=8401 DB_MAX_IDLE_CONNECTIONS=5 DB_MAX_OPEN_CONNECTIONS=50 go run cmd/api/main.go)", "Bash(PORT=8401 go run cmd/api/main.go)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(touch:*)", "Bash(GOOS=linux GOARCH=amd64 go build -o /tmp/test-build ../adc-muti-languages/adc-subscription-service/cmd/api/main.go 2 >& 1)", "Bash(GOPATH=\"\" go build -C ../adc-muti-languages/adc-subscription-service ./internal/application/services)", "Bash(GOPATH=\"\" go run -C ../adc-muti-languages/adc-subscription-service -buildonly=true ./internal/application/services/analytics_service.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./internal/application/services/analytics_service.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./internal/presentation/handlers/analytics_handler.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./cmd/api/main.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./internal/infrastructure/database/repositories/analytics_repository.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./internal/infrastructure/database/repositories/organization_subscription_repository_impl.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./internal/domain/entities/analytics.go)", "Bash(GOPATH=\"\" go build -C /Users/<USER>/Desktop/adc/adc-muti-languages/adc-subscription-service ./cmd/api/main.go 2 >& 1)", "Bash(GOWORK=off go build -C \"/Users/<USER>/Desktop/adc/adc-subscription-service\" ./cmd/api)", "Bash(GOWORK=off go run -C \"/Users/<USER>/Desktop/adc/adc-subscription-service\" ./cmd/api --help)", "Bash(GOWORK=off cd \"/Users/<USER>/Desktop/adc/adc-subscription-service\")"], "deny": []}}