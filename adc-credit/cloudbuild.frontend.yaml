steps:
  # Build the frontend container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/adc-credit-frontend:latest', '--target', 'frontend-prod', '.']

  # Push the frontend container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/adc-credit-frontend:latest']

  # Deploy frontend container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'adc-credit-frontend'
      - '--image=gcr.io/$PROJECT_ID/adc-credit-frontend:latest'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--set-env-vars=NEXT_PUBLIC_BACKEND_URL=${_BACKEND_URL}'
      - '--set-env-vars=NEXTAUTH_URL=${_FRONTEND_URL}'
      - '--set-env-vars=NEXTAUTH_SECRET=${_NEXTAUTH_SECRET}'
      - '--set-env-vars=GOOGLE_CLIENT_ID=${_GOOGLE_CLIENT_ID}'
      - '--set-env-vars=GOOGLE_CLIENT_SECRET=${_GOOGLE_CLIENT_SECRET}'
      - '--set-env-vars=NODE_ENV=production'

  # Map custom domain for frontend
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "Domain mapping needs to be configured manually."
        echo "Run the following command after DNS is configured:"
        echo "gcloud run domain-mappings create --service=adc-credit-frontend --domain=credit.adcshop.store --region=${_REGION}"

  # Print deployment information
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "Deployment completed successfully!"
        frontend_url=$(gcloud run services describe adc-credit-frontend --region=${_REGION} --format="value(status.url)")
        echo "Frontend URL: $frontend_url"

images:
  - 'gcr.io/$PROJECT_ID/adc-credit-frontend:latest'

substitutions:
  _REGION: 'us-central1'
  _BACKEND_URL: 'https://api.credit.adcshop.store'
  _FRONTEND_URL: 'https://credit.adcshop.store'
  _NEXTAUTH_SECRET: 'a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq'
  _GOOGLE_CLIENT_ID: '457647006078-urjli7nlssmv9tjd9bo5uddmi4hdn0ej.apps.googleusercontent.com'
  _GOOGLE_CLIENT_SECRET: '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'


options:
  logging: CLOUD_LOGGING_ONLY
