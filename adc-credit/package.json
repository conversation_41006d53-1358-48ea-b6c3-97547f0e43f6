{"name": "adc-credit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https --port ${NEXT_PUBLIC_PORT:-3400}", "dev:generate": "GENERATE_LOCALES=true next dev --turbopack --experimental-https --port ${NEXT_PUBLIC_PORT:-3400}", "dev:fast": "next dev --port ${NEXT_PUBLIC_PORT:-3800}", "build": "next build", "build:docker": "NODE_ENV=production ESLINT_CONFIG_FILE=.eslintrc.js TS_NODE_PROJECT=tsconfig.build.json next build", "start": "next start", "start:server": "node server.js", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:sdk": "jest src/__tests__/sdk", "test:integration": "jest src/__tests__/integration", "test:slug": "jest src/__tests__/utils/slug.test.ts src/__tests__/components/slug-routing.test.tsx src/__tests__/integration/slug-migration.integration.test.ts", "test:all": "jest --coverage --verbose", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:performance": "playwright test --config=tests/performance/performance.config.ts", "test:performance:monitor": "node scripts/performance-monitor.js", "test:performance:baseline": "npm run test:performance && node scripts/performance-monitor.js --baseline", "test:performance:report": "node scripts/performance-monitor.js --report-only", "test:performance:mobile": "playwright test --config=tests/performance/performance.config.ts --project=performance-mobile", "test:performance:desktop": "playwright test --config=tests/performance/performance.config.ts --project=performance-desktop", "test:performance:slow-network": "playwright test --config=tests/performance/performance.config.ts --project=performance-slow-network", "sync-translations": "node scripts/sync-translations.js", "sync-translations:namespace": "node scripts/sync-translations.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.2", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.80.6", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "html5-qrcode": "^2.3.8", "intl-messageformat": "^10.7.16", "lucide-react": "^0.513.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "phosphor-react": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "recharts": "^2.15.3", "shiki": "^3.4.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.48.0", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "description": "The ADC Credit Service is a full-stack application that provides a comprehensive system for managing API credits. It allows users to manage API keys, track usage, and limit access based on subscription tiers. The service is composed of a Go backend and a Next.js frontend.", "main": ".eslintrc.js", "directories": {"doc": "docs", "example": "examples"}, "repository": {"type": "git", "url": "git+https://github.com/verawat1234/adc-credit.git"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "bugs": {"url": "https://github.com/verawat1234/adc-credit/issues"}, "homepage": "https://github.com/verawat1234/adc-credit#readme"}