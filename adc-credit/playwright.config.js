/**
 * ADC Credit Playwright Configuration
 * 
 * E2E testing configuration for the ADC Credit application
 */

import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  testMatch: [
    '**/pages-verified.spec.ts',
    '**/authenticated-verified.spec.ts', 
    '**/cookies-with-test-ids.spec.ts',
    '**/dashboard-comprehensive.spec.ts',
    '**/api-integration-forms.spec.ts',
    '**/auth-comprehensive.spec.ts',
    '**/navigation-comprehensive.spec.ts',
    '**/shop-management-comprehensive.spec.ts',
    '**/merchant-components-comprehensive.spec.ts',
    '**/business-flows.spec.ts',
    // Enhanced Test ID Validation Suite
    '**/test-id-validation-enhanced.spec.ts',
    '**/cross-component-flows-enhanced.spec.ts',
    '**/dynamic-content-test-ids.spec.ts',
    '**/dashboard-analytics-test-ids.spec.ts',
    '**/test-id-quick-validation.spec.ts',
    // i18n Integration Tests
    '**/i18n-language-switching.spec.ts',
    '**/i18n-integration.spec.ts',
    '**/i18n-real-integration-test.spec.ts'
  ],
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: 'https://localhost:3400',
    ignoreHTTPSErrors: true,
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000,
    navigationTimeout: 30000
  },

  projects: [
    // Setup project for authentication state
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
    },

    // Chromium tests
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
      dependencies: ['setup'],
    },

    // Firefox tests
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
      dependencies: ['setup'],
    },

    // Safari tests
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
      dependencies: ['setup'],
    },

    // Mobile Chrome tests
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
      dependencies: ['setup'],
    },

    // Mobile Safari tests
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
      dependencies: ['setup'],
    },

    // Edge tests
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Edge'], channel: 'msedge' },
      dependencies: ['setup'],
    },

    // Google Chrome tests
    {
      name: 'Google Chrome',
      use: { ...devices['Desktop Chrome'], channel: 'chrome' },
      dependencies: ['setup'],
    },
  ],

  // Web server configuration (commented out since server is already running)
  // webServer: {
  //   command: 'npm run dev',
  //   url: 'http://localhost:3400',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120000,
  // },

  // Global setup and teardown
  globalSetup: './tests/e2e/global-setup.ts',
  globalTeardown: './tests/e2e/global-teardown.ts',

  // Test directory structure
  testIgnore: [
    '**/node_modules/**',
    '**/build/**',
    '**/dist/**',
    '**/.next/**'
  ],

  // Expect configuration
  expect: {
    timeout: 5000,
    toHaveScreenshot: { threshold: 0.2, mode: 'pixel' },
    toMatchSnapshot: { threshold: 0.2 },
  },

  // Output directories
  outputDir: 'test-results/',

  // Metadata
  metadata: {
    'test-suite': 'ADC Credit E2E Tests',
    'application': 'ADC Credit Management Platform',
    'environment': process.env.NODE_ENV || 'development',
  }
});