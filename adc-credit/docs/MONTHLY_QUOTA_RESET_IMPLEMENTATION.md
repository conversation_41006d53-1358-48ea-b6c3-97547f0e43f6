# Monthly Quota Reset Implementation

This document describes the comprehensive monthly quota reset system implemented for the ADC Credit platform.

## Overview

The monthly quota reset system automatically resets subscription-based usage limits on a monthly basis, providing a fresh allocation of resources for users according to their subscription tiers.

## What Resets Monthly

### ✅ Monthly Limits (Reset Every Month)
- **QR Code Generation**: Number of QR codes generated per month
- **API Calls**: Number of API calls made per month
- **Credit Consumption**: Amount of credits consumed per month (separate from balance)

### ❌ Persistent Limits (Do NOT Reset)
- **Total Shops**: Maximum number of shops a user can create
- **Customers per Shop**: Maximum customers allowed per shop
- **API Keys per Shop**: Maximum API keys per shop
- **Branches per Shop**: Maximum branches per shop
- **Webhooks**: Maximum number of webhooks
- **Credit Balance**: User's actual credit balance (handled separately by credit reset system)

## Architecture Components

### 1. Database Schema

#### New Table: `monthly_usage_tracking`
```sql
CREATE TABLE monthly_usage_tracking (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    qr_codes_generated INTEGER DEFAULT 0,
    api_calls_made INTEGER DEFAULT 0,
    credits_consumed INTEGER DEFAULT 0,
    last_reset_date TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(user_id, year, month)
);
```

#### Updated Tables
- **users**: Added `monthly_usage_reset_date` field
- **subscription_tiers**: Added `max_api_calls_per_month` and `max_credits_per_month` fields

### 2. Core Services

#### MonthlyQuotaResetService (`/backend/internal/services/monthly_quota_reset.go`)
- **ProcessMonthlyQuotaResets()**: Main reset process for all users
- **GetUserMonthlyUsage()**: Retrieve/create monthly usage records
- **IncrementQRCodeUsage()**: Track QR code generation
- **IncrementAPICallUsage()**: Track API call usage
- **IncrementCreditUsage()**: Track credit consumption
- **ForceResetUserQuota()**: Manual reset for specific users

#### Updated SubscriptionGuard (`/backend/internal/services/subscription_guard.go`)
- **CheckQRCodeLimit()**: Now uses monthly tracking table
- **CheckAPICallLimit()**: New method for API call limits
- **CheckMonthlyCreditsLimit()**: New method for monthly credit limits

### 3. API Endpoints

#### Admin Routes (`/api/v1/admin/quota/`)
- `POST /reset-monthly` - Process monthly reset for all users
- `GET /usage/:user_id` - Get user's monthly usage statistics
- `POST /reset-user/:user_id` - Force reset for specific user
- `GET /stats` - Get aggregated monthly usage statistics

#### Scheduled Task
- `POST /api/v1/tasks/process-monthly-quota-reset` - Automated reset endpoint

### 4. Cloud Function

#### Automated Monthly Reset (`/backend/cmd/cloud-functions/process_monthly_quota_reset/`)
- **Trigger**: Cloud Scheduler (1st of every month at midnight UTC)
- **Function**: Calls the backend API to process monthly resets
- **Environment Variables**: `API_BASE_URL`, `API_KEY`

## Implementation Flow

### 1. Monthly Reset Process
1. **Identify Users**: Find users whose `monthly_usage_reset_date` is past due
2. **Reset Tracking**: Set monthly counters to 0 for current month
3. **Update Dates**: Set next reset date to first day of next month
4. **Create Records**: Ensure all users have tracking records for current month

### 2. Usage Tracking Flow
1. **On QR Code Generation**: Call `IncrementQRCodeUsage(userID)`
2. **On API Call**: Call `IncrementAPICallUsage(userID)`
3. **On Credit Consumption**: Call `IncrementCreditUsage(userID, amount)`

### 3. Limit Checking Flow
1. **Get Current Usage**: Query monthly tracking table for current month
2. **Compare with Limits**: Check against subscription tier limits
3. **Return Result**: Allow/deny with usage information

## Deployment Steps

### 1. Database Migration
```bash
# Apply the migration
cd backend
go run cmd/api/main.go migrate
```

### 2. Deploy Backend Updates
```bash
# Build and deploy backend with new routes
make deploy
```

### 3. Deploy Cloud Function
```bash
# Deploy the monthly reset function
cd backend/cmd/cloud-functions/process_monthly_quota_reset
gcloud functions deploy process-monthly-quota-reset \
  --runtime go124 \
  --trigger-topic monthly-quota-reset-topic \
  --entry-point ProcessMonthlyQuotaReset \
  --set-env-vars API_BASE_URL=https://your-backend.run.app,API_KEY=your-api-key
```

### 4. Setup Cloud Scheduler
```bash
# Create pub/sub topic
gcloud pubsub topics create monthly-quota-reset-topic

# Create scheduler job (1st of every month at midnight UTC)
gcloud scheduler jobs create pubsub monthly-quota-reset-job \
  --schedule="0 0 1 * *" \
  --topic=monthly-quota-reset-topic \
  --message-body='{"action":"reset_monthly_quotas"}' \
  --time-zone="UTC"
```

## Configuration

### Subscription Tier Limits
Update subscription tiers with monthly limits:
```json
{
  "max_qr_codes_per_month": 500,
  "max_api_calls_per_month": 10000,
  "max_credits_per_month": 1000
}
```

### Environment Variables
- `API_BASE_URL`: Backend API URL for Cloud Function
- `API_KEY`: Authentication key for scheduled tasks

## Monitoring & Maintenance

### 1. Check Monthly Reset Status
```bash
# View Cloud Function logs
gcloud functions logs read process-monthly-quota-reset

# Check scheduler job status  
gcloud scheduler jobs describe monthly-quota-reset-job
```

### 2. Manual Reset (Emergency)
```bash
# Reset all users manually
curl -X POST "https://your-backend.run.app/api/v1/admin/quota/reset-monthly" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Reset specific user
curl -X POST "https://your-backend.run.app/api/v1/admin/quota/reset-user/USER_ID" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 3. Usage Statistics
```bash
# Get monthly usage stats
curl "https://your-backend.run.app/api/v1/admin/quota/stats?year=2024&month=12" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Testing

### Integration Tests
Run the comprehensive test suite:
```bash
cd backend
go test ./tests/integration/monthly_quota_reset_test.go -v
```

### Manual Testing
1. **Create Test User**: Register a new user
2. **Generate Usage**: Create QR codes, make API calls
3. **Check Limits**: Verify usage tracking and limits
4. **Trigger Reset**: Call reset endpoint manually
5. **Verify Reset**: Confirm counters are reset to 0

## Key Benefits

1. **Automated Management**: No manual intervention required for monthly resets
2. **Accurate Tracking**: Real-time usage monitoring instead of calculated queries
3. **Scalable**: Efficient database design handles large user bases
4. **Flexible**: Easy to add new monthly limits or modify existing ones
5. **Reliable**: Automated Cloud Function with error handling and logging
6. **Auditable**: Complete usage history maintained for analytics

## Future Enhancements

1. **Real-time Notifications**: Alert users when approaching limits
2. **Custom Reset Dates**: Allow different reset dates per subscription
3. **Usage Analytics**: Advanced reporting on monthly usage patterns
4. **Rollover Limits**: Allow unused quota to carry over to next month
5. **Tiered Notifications**: Warn at 50%, 80%, 90% of limits