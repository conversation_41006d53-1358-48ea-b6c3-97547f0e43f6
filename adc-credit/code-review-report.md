# Code Review Report: ADC Credit Service Backend

**Date:** June 27, 2025
**Reviewer:** Gemini

## 1. Overall Summary

The backend for the ADC Credit Service is a well-structured Go application built with the Gin framework. The project demonstrates a good separation of concerns, with distinct packages for configuration, database interactions, HTTP handlers, and models.

The code is generally readable and follows common Go practices. However, the review has identified several areas for improvement, including some **critical security vulnerabilities** that should be addressed immediately.

## 2. Directory-Specific Findings

### 2.1. `config` Directory (`config/config.go`)

*   **Summary:** This package handles application configuration loaded from environment variables. It effectively separates configuration into logical structs (`DatabaseConfig`, `RateLimitConfig`, `SubscriptionConfig`).
*   **Strengths:**
    *   Clear separation of configuration concerns.
    *   Use of helper functions (`utils.GetEnvAs...`) to load and parse environment variables with defaults.
    *   Clean interface for accessing tier-specific configuration.
*   **Areas for Improvement:**
    *   **Hardcoded Strings:** Tier names ("Free", "Pro", "Enterprise") are hardcoded in methods like `GetTierConfig`. These should be defined as constants to prevent typos and improve maintainability.

### 2.2. `database` Directory (`database/database.go`)

*   **Summary:** This package manages the database connection (using GORM), migrations, and initial data seeding.
*   **Strengths:**
    *   Properly configures the database connection pool for better performance.
    *   `AutoMigrate` is used to keep the schema in sync with the models.
    *   The `seedSubscriptionTiers` function is a good pattern for populating necessary initial data.
*   **Areas for Improvement:**
    *   **Error Handling:** The `seedSubscriptionTiers` function logs but does not return an error if the seeding process fails. This could prevent the application from starting in a known good state. The error should be returned to the caller (`InitDB`).

### 2.3. `handlers` Directory

#### `handlers/apikey.go`

*   **Summary:** This file contains the CRUD handlers for managing API keys. The logic is straightforward and functional.
*   **Strengths:**
    *   Follows a consistent and readable pattern for handling requests.
    *   Uses request-specific structs for binding JSON payloads.
    *   Securely generates random API keys using `crypto/rand`.
*   **Areas for Improvement:**
    *   **API Key Storage:** API keys are stored in plain text. For enhanced security, keys should be hashed (e.g., with SHA-256) before being stored. Verification would then involve hashing the provided key and comparing it to the stored hash.
    *   **Permissions Validation:** The `Permissions` field is accepted but not validated against a predefined list of allowed permissions. This could lead to invalid permissions being stored.

#### `handlers/auth.go`

*   **Summary:** This file handles user authentication, including email/password, Google OAuth, and password reset functionality. It contains several security flaws that require immediate attention.
*   **Strengths:**
    *   Provides multiple authentication methods.
    *   Correctly uses JWTs for access and refresh tokens.
    *   The password reset flow is logically sound (though has other flaws).
*   **Critical Security Vulnerabilities:**
    *   **Plain Text Passwords:** The `CustomerLogin` and `ChangeCustomerPassword` functions compare passwords in plain text. This is a critical vulnerability. **All password comparisons must be done using a secure hashing algorithm like bcrypt**, which is already used correctly in the `RegisterUser` function. This needs to be fixed immediately.
*   **Other Areas for Improvement:**
    *   **Google OAuth Token Verification:** The `GoogleAuth` handler uses the access token to fetch user info but does not verify the token's integrity with Google first. An attacker could potentially provide a token for a different application. The token should be verified using Google's token info endpoint.
    *   **Inconsistent Error Messages:** Some error responses could reveal whether a user exists (e.g., "User with this email already exists"). While the `ForgotPassword` handler correctly avoids this, it should be applied consistently to prevent user enumeration attacks.
    *   **Unresolved TODOs:** The code contains several `TODO` comments, particularly around creating a default subscription for a new user. These should be implemented to ensure complete functionality.

## 3. Recommendations

Based on this review, the following actions are recommended, in order of priority:

1.  **CRITICAL: Fix Plain Text Password Comparison:** Immediately update the `CustomerLogin` and `ChangeCustomerPassword` functions to use `bcrypt.CompareHashAndPassword` for password verification, consistent with the `RegisterUser` function.
2.  **CRITICAL: Hash Stored API Keys:** Modify the API key creation and validation logic to store and compare hashed keys instead of plain text keys.
3.  **HIGH: Implement Google OAuth Token Verification:** Add a step in the `GoogleAuth` handler to verify the received Google access token before using it.
4.  **MEDIUM: Improve Error Handling:**
    *   Standardize error messages to avoid revealing sensitive information (like user existence).
    *   Ensure that functions like `seedSubscriptionTiers` propagate errors to their callers.
5.  **MEDIUM: Address TODOs:** Implement the logic marked with `TODO` comments, especially for creating user subscriptions.
6.  **LOW: Refactor for Maintainability:**
    *   Replace hardcoded strings (like subscription tier names) with constants.
    *   Add validation for API key permissions.

Addressing these points will significantly improve the security, reliability, and maintainability of the ADC Credit Service.
