version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend
    ports:
      - "8400:8400"
    env_file:
      - backend/.env.prod
    restart: unless-stopped
    networks:
      - adc-network-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8400/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: frontend-prod
    ports:
      - "3400:3400"
    env_file:
      - .env.prod
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - adc-network-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3400/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  adc-network-prod:
    driver: bridge