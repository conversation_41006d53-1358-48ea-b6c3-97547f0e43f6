package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/database"
	"gorm.io/gorm"
)

// Project represents the projects table structure
type Project struct {
	ID             string `gorm:"column:id;primaryKey"`
	Name           string `gorm:"column:name"`
	Slug           string `gorm:"column:slug"`
	OrganizationID string `gorm:"column:organization_id"`
	CreatedAt      string `gorm:"column:created_at"`
	UpdatedAt      string `gorm:"column:updated_at"`
}

// Locale represents the locales table structure
type Locale struct {
	ID        string `gorm:"column:id;primaryKey"`
	Code      string `gorm:"column:code"`
	Name      string `gorm:"column:name"`
	CreatedAt string `gorm:"column:created_at"`
}

// ProjectLocale represents the project_locales table structure
type ProjectLocale struct {
	ID        string `gorm:"column:id;primaryKey"`
	ProjectID string `gorm:"column:project_id"`
	LocaleID  string `gorm:"column:locale_id"`
	IsSource  bool   `gorm:"column:is_source"`
	IsActive  bool   `gorm:"column:is_active"`
	CreatedAt string `gorm:"column:created_at"`
}

// Organization represents the organizations table structure
type Organization struct {
	ID   string `gorm:"column:id;primaryKey"`
	Name string `gorm:"column:name"`
	Slug string `gorm:"column:slug"`
}

// TranslationKey represents the translation_keys table structure
type TranslationKey struct {
	ID        string `gorm:"column:id;primaryKey"`
	KeyName   string `gorm:"column:key_name"`
	ProjectID string `gorm:"column:project_id"`
	CreatedAt string `gorm:"column:created_at"`
}

// Translation represents the translations table structure
type Translation struct {
	ID       string `gorm:"column:id;primaryKey"`
	KeyID    string `gorm:"column:key_id"`
	LocaleID string `gorm:"column:locale_id"`
	Content  string `gorm:"column:content"`
	CreatedAt string `gorm:"column:created_at"`
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Connect to database
	db, err := database.Connect(cfg.GetDatabaseURL())
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Get SQL DB instance
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal("Failed to get SQL DB instance:", err)
	}
	defer sqlDB.Close()

	fmt.Println("Connected to database successfully")
	fmt.Println("=====================================")

	switch command {
	case "projects":
		queryProjects(db)
	case "locales":
		queryLocales(db)
	case "project-locales":
		queryProjectLocales(db)
	case "organizations":
		queryOrganizations(db)
	case "translations":
		queryTranslations(db)
	case "translation-keys":
		queryTranslationKeys(db)
	case "project-details":
		if len(os.Args) < 3 {
			fmt.Println("Usage: go run main.go project-details <project-id>")
			return
		}
		queryProjectDetails(db, os.Args[2])
	case "org-projects":
		if len(os.Args) < 3 {
			fmt.Println("Usage: go run main.go org-projects <organization-slug>")
			return
		}
		queryOrgProjects(db, os.Args[2])
	case "raw":
		if len(os.Args) < 3 {
			fmt.Println("Usage: go run main.go raw \"<SQL_QUERY>\"")
			return
		}
		runRawQuery(db, os.Args[2])
	default:
		printUsage()
	}
}

func printUsage() {
	fmt.Println("Database Query Tool")
	fmt.Println("Usage: go run main.go <command> [args]")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  projects           - List all projects")
	fmt.Println("  locales            - List all locales")
	fmt.Println("  project-locales    - List project-locale relationships")
	fmt.Println("  organizations      - List all organizations") 
	fmt.Println("  translations       - List all translations")
	fmt.Println("  translation-keys   - List all translation keys")
	fmt.Println("  project-details <id> - Show detailed info for a project")
	fmt.Println("  org-projects <slug>  - Show projects for an organization")
	fmt.Println("  raw \"<query>\"     - Run raw SQL query")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  go run main.go projects")
	fmt.Println("  go run main.go project-details 123e4567-e89b-12d3-a456-426614174000")
	fmt.Println("  go run main.go org-projects my-org")
	fmt.Println("  go run main.go raw \"SELECT COUNT(*) FROM projects\"")
}

func queryProjects(db *gorm.DB) {
	var projects []Project
	result := db.Find(&projects)
	if result.Error != nil {
		log.Fatal("Failed to fetch projects:", result.Error)
	}

	fmt.Printf("Found %d projects:\n\n", len(projects))
	fmt.Printf("%-36s | %-30s | %-20s | %-36s\n", "ID", "Name", "Slug", "Organization ID")
	fmt.Println(strings.Repeat("-", 36+3+30+3+20+3+36))

	for _, project := range projects {
		fmt.Printf("%-36s | %-30s | %-20s | %-36s\n", 
			project.ID, 
			truncateString(project.Name, 30), 
			truncateString(project.Slug, 20), 
			project.OrganizationID)
	}
}

func queryLocales(db *gorm.DB) {
	var locales []Locale
	result := db.Find(&locales)
	if result.Error != nil {
		log.Fatal("Failed to fetch locales:", result.Error)
	}

	fmt.Printf("Found %d locales:\n\n", len(locales))
	fmt.Printf("%-36s | %-10s | %-20s\n", "ID", "Code", "Name")
	fmt.Println(strings.Repeat("-", 36+3+10+3+20))

	for _, locale := range locales {
		fmt.Printf("%-36s | %-10s | %-20s\n", 
			locale.ID, 
			locale.Code, 
			truncateString(locale.Name, 20))
	}
}

func queryProjectLocales(db *gorm.DB) {
	var projectLocales []ProjectLocale
	result := db.Find(&projectLocales)
	if result.Error != nil {
		log.Fatal("Failed to fetch project locales:", result.Error)
	}

	fmt.Printf("Found %d project-locale relationships:\n\n", len(projectLocales))
	fmt.Printf("%-36s | %-36s | %-36s | %-8s | %-8s\n", "ID", "Project ID", "Locale ID", "Source", "Active")
	fmt.Println(strings.Repeat("-", 36+3+36+3+36+3+8+3+8))

	for _, pl := range projectLocales {
		fmt.Printf("%-36s | %-36s | %-36s | %-8t | %-8t\n", 
			pl.ID, 
			pl.ProjectID, 
			pl.LocaleID,
			pl.IsSource,
			pl.IsActive)
	}
}

func queryOrganizations(db *gorm.DB) {
	var organizations []Organization
	result := db.Find(&organizations)
	if result.Error != nil {
		log.Fatal("Failed to fetch organizations:", result.Error)
	}

	fmt.Printf("Found %d organizations:\n\n", len(organizations))
	fmt.Printf("%-36s | %-30s | %-20s\n", "ID", "Name", "Slug")
	fmt.Println(strings.Repeat("-", 36+3+30+3+20))

	for _, org := range organizations {
		fmt.Printf("%-36s | %-30s | %-20s\n", 
			org.ID, 
			truncateString(org.Name, 30), 
			truncateString(org.Slug, 20))
	}
}

func queryTranslations(db *gorm.DB) {
	var translations []Translation
	result := db.Limit(50).Find(&translations) // Limit to avoid too much output
	if result.Error != nil {
		log.Fatal("Failed to fetch translations:", result.Error)
	}

	var count int64
	db.Model(&Translation{}).Count(&count)

	fmt.Printf("Found %d translations (showing first 50):\n\n", count)
	fmt.Printf("%-36s | %-36s | %-36s | %-50s\n", "ID", "Key ID", "Locale ID", "Content")
	fmt.Println(strings.Repeat("-", 36+3+36+3+36+3+50))

	for _, translation := range translations {
		fmt.Printf("%-36s | %-36s | %-36s | %-50s\n", 
			translation.ID, 
			translation.KeyID, 
			translation.LocaleID, 
			truncateString(translation.Content, 50))
	}
}

func queryTranslationKeys(db *gorm.DB) {
	var keys []TranslationKey  
	result := db.Limit(50).Find(&keys) // Limit to avoid too much output
	if result.Error != nil {
		log.Fatal("Failed to fetch translation keys:", result.Error)
	}

	var count int64
	db.Model(&TranslationKey{}).Count(&count)

	fmt.Printf("Found %d translation keys (showing first 50):\n\n", count)
	fmt.Printf("%-36s | %-30s | %-36s\n", "ID", "Key Name", "Project ID")
	fmt.Println(strings.Repeat("-", 36+3+30+3+36))

	for _, key := range keys {
		fmt.Printf("%-36s | %-30s | %-36s\n", 
			key.ID, 
			truncateString(key.KeyName, 30), 
			key.ProjectID)
	}
}

func queryProjectDetails(db *gorm.DB, projectID string) {
	// Get project
	var project Project
	result := db.Where("id = ?", projectID).First(&project)
	if result.Error != nil {
		fmt.Printf("Project not found: %v\n", result.Error)
		return
	}

	// Get organization
	var org Organization
	db.Where("id = ?", project.OrganizationID).First(&org)

	// Get project-locales count
	var projectLocalesCount int64
	db.Model(&ProjectLocale{}).Where("project_id = ?", projectID).Count(&projectLocalesCount)

	// Get translation keys count
	var keysCount int64
	db.Model(&TranslationKey{}).Where("project_id = ?", projectID).Count(&keysCount)

	// Get translations count
	var translationsCount int64
	db.Raw("SELECT COUNT(*) FROM translations t JOIN translation_keys tk ON t.key_id = tk.id WHERE tk.project_id = ?", projectID).Scan(&translationsCount)

	fmt.Printf("Project Details:\n")
	fmt.Printf("================\n")
	fmt.Printf("ID: %s\n", project.ID)
	fmt.Printf("Name: %s\n", project.Name)
	fmt.Printf("Slug: %s\n", project.Slug)
	fmt.Printf("Organization: %s (%s)\n", org.Name, org.Slug)
	fmt.Printf("Created: %s\n", project.CreatedAt)
	fmt.Printf("Updated: %s\n", project.UpdatedAt)
	fmt.Printf("\nStatistics:\n")
	fmt.Printf("- Project Locales: %d\n", projectLocalesCount)
	fmt.Printf("- Translation Keys: %d\n", keysCount)
	fmt.Printf("- Translations: %d\n", translationsCount)

	// Show project locales with locale details
	if projectLocalesCount > 0 {
		var projectLocales []ProjectLocale
		db.Where("project_id = ?", projectID).Find(&projectLocales)
		fmt.Printf("\nProject Locales:\n")
		for _, pl := range projectLocales {
			var locale Locale
			db.Where("id = ?", pl.LocaleID).First(&locale)
			sourceFlag := ""
			if pl.IsSource {
				sourceFlag = " (source)"
			}
			activeFlag := ""
			if !pl.IsActive {
				activeFlag = " (inactive)"
			}
			fmt.Printf("- %s (%s)%s%s - %s\n", locale.Code, locale.Name, sourceFlag, activeFlag, locale.ID)
		}
	}
}

func queryOrgProjects(db *gorm.DB, orgSlug string) {
	// Get organization
	var org Organization
	result := db.Where("slug = ?", orgSlug).First(&org)
	if result.Error != nil {
		fmt.Printf("Organization not found: %v\n", result.Error)
		return
	}

	// Get projects
	var projects []Project
	db.Where("organization_id = ?", org.ID).Find(&projects)

	fmt.Printf("Organization: %s (%s)\n", org.Name, org.Slug)
	fmt.Printf("Projects (%d):\n\n", len(projects))

	if len(projects) == 0 {
		fmt.Println("No projects found for this organization.")
		return
	}

	fmt.Printf("%-36s | %-30s | %-20s\n", "ID", "Name", "Slug")
	fmt.Println(strings.Repeat("-", 36+3+30+3+20))

	for _, project := range projects {
		fmt.Printf("%-36s | %-30s | %-20s\n", 
			project.ID, 
			truncateString(project.Name, 30), 
			truncateString(project.Slug, 20))
	}
}

func runRawQuery(db *gorm.DB, query string) {
	fmt.Printf("Executing query: %s\n\n", query)

	rows, err := db.Raw(query).Rows()
	if err != nil {
		log.Fatal("Failed to execute query:", err)
	}
	defer rows.Close()

	// Get column names
	columns, err := rows.Columns()
	if err != nil {
		log.Fatal("Failed to get columns:", err)
	}

	fmt.Printf("Columns: %s\n", strings.Join(columns, " | "))
	fmt.Println(strings.Repeat("-", len(strings.Join(columns, " | "))))

	// Print rows
	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))
	for i := range columns {
		valuePtrs[i] = &values[i]
	}

	rowCount := 0
	for rows.Next() {
		rowCount++
		err := rows.Scan(valuePtrs...)
		if err != nil {
			log.Fatal("Failed to scan row:", err)
		}

		row := make([]string, len(columns))
		for i, val := range values {
			if val != nil {
				row[i] = fmt.Sprintf("%v", val)
			} else {
				row[i] = "NULL"
			}
		}
		fmt.Println(strings.Join(row, " | "))
	}

	fmt.Printf("\nReturned %d rows.\n", rowCount)
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}