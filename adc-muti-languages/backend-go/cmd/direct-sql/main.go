package main

import (
	"database/sql"
	"fmt"
	"log"

	"adc-multi-languages/internal/infrastructure/config"
	_ "github.com/lib/pq"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Connect directly with database/sql
	db, err := sql.Open("postgres", cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("✅ Connected to database successfully")

	// List all organizations
	fmt.Println("\n=== ALL ORGANIZATIONS ===")
	rows, err := db.Query("SELECT id, name, slug, owner_id FROM organizations ORDER BY created_at DESC")
	if err != nil {
		log.Fatalf("Failed to query organizations: %v", err)
	}
	defer rows.Close()

	orgFound := false
	targetOrgID := "5d0da36f-7700-4bd3-a486-8f7db32f2d0a"
	var targetOwnerID string

	for rows.Next() {
		var id, name, slug, ownerID string
		if err := rows.Scan(&id, &name, &slug, &ownerID); err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}
		fmt.Printf("- %s (ID: %s, Owner: %s)\n", name, id, ownerID)
		
		if id == targetOrgID {
			orgFound = true
			targetOwnerID = ownerID
		}
	}

	if !orgFound {
		fmt.Printf("\n❌ Organization %s not found in database\n", targetOrgID)
		return
	}

	fmt.Printf("\n=== TARGET ORGANIZATION ===")
	fmt.Printf("Organization ID: %s\n", targetOrgID)
	fmt.Printf("Owner ID: %s\n", targetOwnerID)

	// Get owner user info
	var ownerEmail, ownerName string
	err = db.QueryRow("SELECT email, name FROM users WHERE id = $1", targetOwnerID).Scan(&ownerEmail, &ownerName)
	if err != nil {
		log.Printf("Failed to get owner info: %v", err)
	} else {
		fmt.Printf("Owner: %s (%s)\n", ownerName, ownerEmail)
	}

	// Check membership
	fmt.Println("\n=== CHECKING MEMBERSHIP ===")
	var membershipCount int
	err = db.QueryRow("SELECT COUNT(*) FROM organization_memberships WHERE organization_id = $1 AND user_id = $2", 
		targetOrgID, targetOwnerID).Scan(&membershipCount)
	
	if err != nil {
		log.Printf("Error checking membership: %v", err)
		return
	}

	if membershipCount == 0 {
		fmt.Printf("❌ No membership found for owner %s in organization %s\n", ownerEmail, targetOrgID)
		fmt.Println("Creating membership...")

		// Create membership
		_, err = db.Exec(`INSERT INTO organization_memberships (organization_id, user_id, role, status, created_at, updated_at) 
			VALUES ($1, $2, 'owner', 'active', NOW(), NOW())`,
			targetOrgID, targetOwnerID)
		
		if err != nil {
			log.Fatalf("Failed to create membership: %v", err)
		}

		fmt.Printf("✅ Successfully created owner membership for %s\n", ownerEmail)
	} else {
		fmt.Printf("✅ Membership already exists for %s\n", ownerEmail)
		
		// Show membership details
		var role, status string
		err = db.QueryRow("SELECT role, status FROM organization_memberships WHERE organization_id = $1 AND user_id = $2", 
			targetOrgID, targetOwnerID).Scan(&role, &status)
		if err == nil {
			fmt.Printf("Role: %s, Status: %s\n", role, status)
		}
	}

	// Check subscription
	fmt.Println("\n=== CHECKING SUBSCRIPTION ===")
	var subscriptionCount int
	err = db.QueryRow("SELECT COUNT(*) FROM organization_subscriptions WHERE organization_id = $1", targetOrgID).Scan(&subscriptionCount)
	
	if err != nil {
		log.Printf("Error checking subscription: %v", err)
	} else if subscriptionCount == 0 {
		fmt.Printf("❌ No subscription found for organization %s\n", targetOrgID)
	} else {
		fmt.Printf("✅ Subscription exists for organization %s\n", targetOrgID)
		
		// Show subscription details
		var planID, status string
		err = db.QueryRow("SELECT subscription_plan_id, status FROM organization_subscriptions WHERE organization_id = $1", 
			targetOrgID).Scan(&planID, &status)
		if err == nil {
			fmt.Printf("Plan ID: %s, Status: %s\n", planID, status)
		}
	}

	fmt.Println("\n🎉 Fix completed! Try accessing the subscription endpoint now.")
}