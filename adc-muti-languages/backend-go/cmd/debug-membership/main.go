package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

func main() {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		dbURL = "postgresql://postgres.hsroaoiorfxgwzdiwsmh:<EMAIL>:6543/postgres?prefer_simple_protocol=true"
	}

	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	orgID := "f5427631-2edc-4c72-a7ea-19a5cfd2b7f5"

	// Check if organization exists
	fmt.Printf("=== CHECKING ORGANIZATION %s ===\n", orgID)
	var orgName, ownerID string
	err = db.QueryRow("SELECT name, owner_id FROM organizations WHERE id = $1", orgID).Scan(&orgName, &ownerID)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("❌ Organization %s not found\n", orgID)
			
			// List all organizations
			fmt.Println("\n=== ALL ORGANIZATIONS ===")
			rows, err := db.Query("SELECT id, name, slug, owner_id FROM organizations LIMIT 10")
			if err != nil {
				log.Fatalf("Failed to list organizations: %v", err)
			}
			defer rows.Close()

			for rows.Next() {
				var id, name, slug, owner string
				if err := rows.Scan(&id, &name, &slug, &owner); err != nil {
					log.Printf("Error scanning row: %v", err)
					continue
				}
				fmt.Printf("- %s: %s (slug: %s, owner: %s)\n", id, name, slug, owner)
			}
			return
		}
		log.Fatalf("Error checking organization: %v", err)
	}

	fmt.Printf("✅ Organization found: %s (owner: %s)\n", orgName, ownerID)

	// Check owner user
	var ownerEmail, ownerName string
	err = db.QueryRow("SELECT email, COALESCE(first_name || ' ' || last_name, first_name, last_name, email) as display_name FROM users WHERE id = $1", ownerID).Scan(&ownerEmail, &ownerName)
	if err != nil {
		log.Fatalf("Error getting owner: %v", err)
	}
	fmt.Printf("Owner: %s (%s)\n", ownerName, ownerEmail)

	// Check membership
	fmt.Printf("\n=== CHECKING MEMBERSHIP ===\n")
	var membershipRole, membershipStatus string
	err = db.QueryRow("SELECT role, status FROM organization_memberships WHERE organization_id = $1 AND user_id = $2", orgID, ownerID).Scan(&membershipRole, &membershipStatus)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Printf("❌ No membership found for %s in %s\n", ownerEmail, orgName)
			
			// Create membership
			fmt.Println("Creating membership...")
			_, err = db.Exec("INSERT INTO organization_memberships (organization_id, user_id, role, status, is_active) VALUES ($1, $2, $3, $4, $5)", 
				orgID, ownerID, "owner", "active", true)
			if err != nil {
				log.Fatalf("Failed to create membership: %v", err)
			}
			fmt.Printf("✅ Created membership for %s as owner\n", ownerEmail)
		} else {
			log.Fatalf("Error checking membership: %v", err)
		}
	} else {
		fmt.Printf("✅ Membership exists: %s is %s (status: %s)\n", ownerEmail, membershipRole, membershipStatus)
	}

	// Check subscription
	fmt.Printf("\n=== CHECKING SUBSCRIPTION ===\n")
	var subCount int
	err = db.QueryRow("SELECT COUNT(*) FROM organization_subscriptions WHERE organization_id = $1", orgID).Scan(&subCount)
	if err != nil {
		log.Printf("Error checking subscription: %v", err)
	} else if subCount == 0 {
		fmt.Printf("⚠️  No subscription found for %s\n", orgName)
	} else {
		fmt.Printf("✅ Subscription exists for %s\n", orgName)
	}
}