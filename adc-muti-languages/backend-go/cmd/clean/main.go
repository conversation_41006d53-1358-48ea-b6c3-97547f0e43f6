package main

import (
	"fmt"
	"log"
	"regexp"
	"strings"

	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/database"
)

// Organization represents the organization table structure
type Organization struct {
	ID   string `gorm:"column:id;primaryKey"`
	Slug string `gorm:"column:slug"`
	Name string `gorm:"column:name"`
}

func main() {
	fmt.Println("Starting database cleanup...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Connect to database
	db, err := database.Connect(cfg.GetDatabaseURL())
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Get SQL DB instance for cleanup
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal("Failed to get SQL DB instance:", err)
	}
	defer sqlDB.Close()

	fmt.Println("Connected to database successfully")

	// Find organizations with invalid slugs
	var organizations []Organization
	result := db.Find(&organizations)
	if result.Error != nil {
		log.Fatal("Failed to fetch organizations:", result.Error)
	}

	fmt.Printf("Found %d organizations to check\n", len(organizations))

	slugRegex := regexp.MustCompile(`^[a-z0-9-]+$`)
	invalidOrgs := []Organization{}

	for _, org := range organizations {
		slug := strings.TrimSpace(org.Slug)
		
		// Check if slug is invalid
		isInvalid := false
		
		if slug == "" {
			isInvalid = true
		} else if len(slug) < 2 || len(slug) > 100 {
			isInvalid = true
		} else if !slugRegex.MatchString(strings.ToLower(slug)) {
			isInvalid = true
		} else if strings.HasPrefix(slug, "-") || strings.HasSuffix(slug, "-") {
			isInvalid = true
		} else if strings.Contains(slug, "--") {
			isInvalid = true
		}

		if isInvalid {
			invalidOrgs = append(invalidOrgs, org)
			fmt.Printf("Invalid slug found: ID=%s, Slug='%s', Name='%s'\n", org.ID, org.Slug, org.Name)
		}
	}

	if len(invalidOrgs) == 0 {
		fmt.Println("No invalid slugs found. Database is clean!")
		return
	}

	fmt.Printf("Found %d organizations with invalid slugs\n", len(invalidOrgs))
	fmt.Println("Fixing invalid slugs...")

	// Fix invalid slugs
	for _, org := range invalidOrgs {
		newSlug := generateValidSlug(org.Name, org.ID)
		fmt.Printf("Fixing: '%s' -> '%s' (ID: %s)\n", org.Slug, newSlug, org.ID)
		
		// Update the slug
		result := db.Model(&org).Where("id = ?", org.ID).Update("slug", newSlug)
		if result.Error != nil {
			fmt.Printf("Failed to update organization %s: %v\n", org.ID, result.Error)
		} else {
			fmt.Printf("✓ Updated organization %s slug to '%s'\n", org.ID, newSlug)
		}
	}

	fmt.Println("Database cleanup completed!")
}

// generateValidSlug creates a valid slug from organization name and ID
func generateValidSlug(name, id string) string {
	// Start with the name
	slug := strings.ToLower(strings.TrimSpace(name))
	
	// Replace spaces and invalid characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9-]`)
	slug = reg.ReplaceAllString(slug, "-")
	
	// Remove consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}
	
	// Remove leading/trailing hyphens
	slug = strings.Trim(slug, "-")
	
	// If slug is empty or too short, use a fallback
	if len(slug) < 2 {
		slug = "org-" + strings.ToLower(id[:8])
	}
	
	// Ensure it's not too long
	if len(slug) > 100 {
		slug = slug[:97] + "..."
	}
	
	// Final validation - if still invalid, use ID-based slug
	slugRegex := regexp.MustCompile(`^[a-z0-9-]+$`)
	if !slugRegex.MatchString(slug) || strings.HasPrefix(slug, "-") || strings.HasSuffix(slug, "-") {
		slug = "org-" + strings.ToLower(id[:8])
	}
	
	return slug
}