package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
)

func main() {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		dbURL = "postgresql://postgres.hsroaoiorfxgwzdiwsmh:<EMAIL>:6543/postgres?prefer_simple_protocol=true"
	}

	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("=== USERS ===")
	rows, err := db.Query("SELECT id, email, COALESCE(first_name, '') as first_name, COALESCE(last_name, '') as last_name, created_at FROM users ORDER BY created_at DESC LIMIT 10")
	if err != nil {
		log.Fatalf("Failed to list users: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var id, email, firstName, lastName, createdAt string
		if err := rows.Scan(&id, &email, &firstName, &lastName, &createdAt); err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}
		fmt.Printf("- %s: %s %s (%s) - %s\n", id, firstName, lastName, email, createdAt)
	}
}