package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/container"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/database"
	"github.com/google/uuid"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Initialize container
	container := container.NewContainer(db, cfg)

	ctx := context.Background()

	// Check if we have command line arguments
	if len(os.Args) < 3 {
		fmt.Println("Usage: go run main.go <user_id> <organization_id>")
		fmt.Println("Example: go run main.go 123e4567-e89b-12d3-a456-************ 5d0da36f-7700-4bd3-a486-8f7db32f2d0a")
		os.Exit(1)
	}

	userIDStr := os.Args[1]
	orgIDStr := os.Args[2]

	// Parse UUIDs
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Fatalf("Invalid user ID: %v", err)
	}

	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		log.Fatalf("Invalid organization ID: %v", err)
	}

	// Check if organization exists
	org, err := container.OrganizationRepository.GetByID(ctx, orgID)
	if err != nil {
		log.Fatalf("Organization not found: %v", err)
	}

	// Check if user exists
	user, err := container.UserRepository.GetByID(ctx, userID)
	if err != nil {
		log.Fatalf("User not found: %v", err)
	}

	// Check if membership already exists
	membership, err := container.OrganizationMembershipRepository.GetByUserAndOrganization(ctx, userID, orgID)
	if err == nil {
		fmt.Printf("Membership already exists for user %s in organization %s\n", user.Email().String(), org.Name())
		fmt.Printf("Role: %s, IsActive: %t\n", membership.Role, membership.IsActive)
		return
	}

	// Create membership
	newMembership := entities.NewOrganizationMembership(orgID, userID, entities.RoleOwner)
	
	if err := container.OrganizationMembershipRepository.Create(ctx, newMembership); err != nil {
		log.Fatalf("Failed to create membership: %v", err)
	}

	fmt.Printf("✅ Successfully created membership for user %s in organization %s\n", user.Email().String(), org.Name())
	fmt.Printf("Role: %s, IsActive: %t\n", newMembership.Role, newMembership.IsActive)

	// Check if organization subscription exists
	subscription, err := container.SubscriptionService.GetSubscriptionByOrganization(ctx, orgID.String())
	if err != nil {
		fmt.Printf("⚠️  No subscription found for organization %s\n", org.Name())
		fmt.Println("Creating default free subscription...")
		
		// Create default free subscription
		freePlanID := "8a05a86f-28cc-4b35-9cba-5c11e35bed4f" // Free plan ID
		createSubReq := &dtos.CreateSubscriptionRequest{
			OrganizationID:     orgID.String(),
			SubscriptionPlanID: freePlanID,
			StartTrial:         false,
		}
		
		_, err := container.SubscriptionService.CreateSubscription(ctx, createSubReq)
		if err != nil {
			log.Printf("Failed to create default subscription: %v", err)
		} else {
			fmt.Println("✅ Successfully created default free subscription")
		}
	} else {
		fmt.Printf("✅ Subscription already exists for organization %s\n", org.Name())
		fmt.Printf("Plan: %s, Status: %s\n", subscription.SubscriptionPlanID, subscription.Status)
	}
}