package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"adc-multi-languages/internal/container"
	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/database"
	"adc-multi-languages/internal/infrastructure/utils"
	"adc-multi-languages/internal/presentation/routes"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load .env file from backend-go directory
	envPath := filepath.Join(".env")
	if err := godotenv.Load(envPath); err != nil {
		log.Printf("Warning: Could not load .env file from %s: %v", envPath, err)
	}

	// Load application configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Initialize logger
	utils.InitLogger(cfg.LogLevel)

	// Set up database connection with retry logic for stability
	db, err := database.ConnectWithRetry(cfg.GetDatabaseURL(), 5)
	if err != nil {
		log.Fatal("Failed to connect to database after retries:", err)
	}
	log.Println("Database connected successfully with retry logic")

	// Auto-migrate database models
	if err := database.AutoMigrate(db); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}
	
	// Force clean up all prepared statements after migration
	if sqlDB, err := db.DB(); err == nil {
		sqlDB.Exec("DEALLOCATE ALL")
		log.Println("All prepared statements deallocated after migration")
	}

	// Initialize dependency injection container
	container := container.NewContainer(db, cfg)

	// Set up Gin router
	router := gin.Default()

	// Add logger middleware
	router.Use(utils.LoggerMiddleware())

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Set up routes
	routes.SetupRoutes(router, container)

	// Get port from configuration
	port := cfg.Port

	// Create HTTP server with custom timeouts for large translation batches
	srv := &http.Server{
		Addr:         ":" + port,
		Handler:      router,
		ReadTimeout:  10 * time.Minute,  // 10 minutes for large translation requests
		WriteTimeout: 10 * time.Minute,  // 10 minutes for large translation responses
		IdleTimeout:  120 * time.Second, // 2 minutes idle timeout
	}

	// Start server
	logger := utils.Logger()
	logger.Info().
		Str("port", port).
		Str("version", cfg.APIVersion).
		Str("health_check", "http://localhost:"+port+"/health").
		Str("users_api", "http://localhost:"+port+cfg.GetAPIVersionPath()+"/users").
		Str("read_timeout", "10m").
		Str("write_timeout", "10m").
		Msg("Starting Clean Architecture server with extended timeouts")

	// Run server in a goroutine so it doesn't block
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal().
				Err(err).
				Msg("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info().Msg("Shutting down server...")

	// Give outstanding requests time to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal().
			Err(err).
			Msg("Server forced to shutdown")
	}

	logger.Info().Msg("Server exited")
}
