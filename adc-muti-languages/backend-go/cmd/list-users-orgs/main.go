package main

import (
	"context"
	"fmt"
	"log"

	"adc-multi-languages/internal/container"
	"adc-multi-languages/internal/domain/repositories"
	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/database"
	"github.com/google/uuid"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Initialize container
	container := container.NewContainer(db, cfg)

	ctx := context.Background()

	// List all users
	fmt.Println("=== USERS ===")
	users, _, err := container.UserRepository.List(ctx, repositories.UserFilter{}, repositories.Pagination{Page: 1, PageSize: 100})
	if err != nil {
		log.Fatalf("Failed to list users: %v", err)
	}

	for _, user := range users {
		fmt.Printf("ID: %s, Email: %s, Name: %s\n", user.ID(), user.Email().String(), user.FullName())
	}

	fmt.Println("\n=== ORGANIZATIONS ===")
	
	// List all organizations
	orgs, _, err := container.OrganizationRepository.List(ctx, repositories.OrganizationFilter{}, repositories.Pagination{Page: 1, PageSize: 100})
	if err != nil {
		log.Fatalf("Failed to list organizations: %v", err)
	}

	for _, org := range orgs {
		fmt.Printf("ID: %s, Name: %s, Slug: %s, Owner: %s\n", org.ID(), org.Name(), org.Slug(), org.OwnerID())
	}

	// Check the specific organization
	orgID := uuid.MustParse("5d0da36f-7700-4bd3-a486-8f7db32f2d0a")
	fmt.Printf("\n=== ORGANIZATION MEMBERSHIP FOR %s ===\n", orgID)
	
	memberships, _, err := container.OrganizationMembershipRepository.ListByOrganization(ctx, orgID, repositories.MembershipFilter{}, repositories.Pagination{Page: 1, PageSize: 100})
	if err != nil {
		fmt.Printf("Error getting memberships: %v\n", err)
	} else {
		fmt.Printf("Found %d memberships:\n", len(memberships))
		for _, membership := range memberships {
			fmt.Printf("User: %s, Role: %s, IsActive: %t\n", membership.UserID, membership.Role, membership.IsActive)
		}
	}
}