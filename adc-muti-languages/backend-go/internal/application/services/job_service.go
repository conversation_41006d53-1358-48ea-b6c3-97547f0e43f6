package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// JobService handles job business logic
type JobService struct {
	jobRepo         repositories.JobRepository
	jobScheduleRepo repositories.JobScheduleRepository
	organizationRepo repositories.OrganizationRepository
	userRepo        repositories.UserRepository
	projectRepo     repositories.ProjectRepository
}

// NewJobService creates a new job service
func NewJobService(
	jobRepo repositories.JobRepository,
	jobScheduleRepo repositories.JobScheduleRepository,
	organizationRepo repositories.OrganizationRepository,
	userRepo repositories.UserRepository,
	projectRepo repositories.ProjectRepository,
) *JobService {
	return &JobService{
		jobRepo:         jobRepo,
		jobScheduleRepo: jobScheduleRepo,
		organizationRepo: organizationRepo,
		userRepo:        userRepo,
		projectRepo:     projectRepo,
	}
}

// Job Management

func (s *JobService) CreateJob(ctx context.Context, req *dtos.CreateJobRequest) (*dtos.JobResponse, error) {
	// Validate references
	if req.OrganizationID != "" {
		orgID, err := uuid.Parse(req.OrganizationID)
		if err != nil {
			return nil, fmt.Errorf("invalid organization ID: %w", err)
		}
		if _, err := s.organizationRepo.GetByID(ctx, orgID); err != nil {
			return nil, fmt.Errorf("organization not found: %w", err)
		}
	}
	
	if req.UserID != "" {
		userID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		if _, err := s.userRepo.GetByID(ctx, userID); err != nil {
			return nil, fmt.Errorf("user not found: %w", err)
		}
	}
	
	if req.ProjectID != "" {
		projectID, err := uuid.Parse(req.ProjectID)
		if err != nil {
			return nil, fmt.Errorf("invalid project ID: %w", err)
		}
		if _, err := s.projectRepo.GetByID(ctx, projectID); err != nil {
			return nil, fmt.Errorf("project not found: %w", err)
		}
	}
	
	// Create job entity
	job := entities.NewJob(req.Type, req.Queue, req.Payload)
	
	// Set optional fields
	if req.Priority != 0 {
		job.SetPriority(req.Priority)
	}
	
	if req.MaxRetries != 0 {
		job.MaxRetries = req.MaxRetries
	}
	
	if req.TimeoutSeconds != 0 {
		job.SetTimeout(req.TimeoutSeconds)
	}
	
	if req.ScheduledAt != nil {
		job.Schedule(*req.ScheduledAt)
	}
	
	if req.ExpiresAt != nil {
		job.SetExpiration(*req.ExpiresAt)
	}
	
	job.SetContext(req.OrganizationID, req.UserID, req.ProjectID)
	
	// Add dependencies
	for _, depID := range req.DependsOn {
		job.AddDependency(depID)
	}
	
	if err := job.Validate(); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to create job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) GetJob(ctx context.Context, id string) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) UpdateJob(ctx context.Context, id string, req *dtos.UpdateJobRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Update fields if provided
	if req.Status != nil {
		job.Status = *req.Status
	}
	
	if req.Priority != nil {
		job.SetPriority(*req.Priority)
	}
	
	if req.ScheduledAt != nil {
		job.Schedule(*req.ScheduledAt)
	}
	
	if req.MaxRetries != nil {
		job.MaxRetries = *req.MaxRetries
	}
	
	if req.TimeoutSeconds != nil {
		job.SetTimeout(*req.TimeoutSeconds)
	}
	
	if req.ExpiresAt != nil {
		job.SetExpiration(*req.ExpiresAt)
	}
	
	if err := job.Validate(); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to update job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) DeleteJob(ctx context.Context, id string) error {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	
	// Only allow deletion of pending, completed, failed, or canceled jobs
	if job.Status == entities.JobStatusRunning {
		return errors.New("cannot delete running job")
	}
	
	return s.jobRepo.Delete(ctx, id)
}

func (s *JobService) ListJobs(ctx context.Context, req *dtos.ListJobsRequest) (*dtos.PaginatedResponse[*dtos.JobResponse], error) {
	// Calculate limit and offset from pagination parameters
	limit, offset := req.PaginationRequest.GetLimitOffset()
	
	// Convert request to repository filter
	filter := &repositories.JobFilter{
		OrganizationID: req.OrganizationID,
		UserID:         req.UserID,
		ProjectID:      req.ProjectID,
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		HasErrors:      req.HasErrors,
		MinProgress:    req.MinProgress,
		MaxProgress:    req.MaxProgress,
		Limit:          limit,
		Offset:         offset,
		SortBy:         req.SortBy,
		SortOrder:      req.SortOrder,
	}
	
	// Convert string arrays to enum arrays
	if len(req.Status) > 0 {
		filter.Status = make([]entities.JobStatus, len(req.Status))
		for i, s := range req.Status {
			filter.Status[i] = entities.JobStatus(s)
		}
	}
	
	if len(req.Type) > 0 {
		filter.Type = make([]entities.JobType, len(req.Type))
		for i, t := range req.Type {
			filter.Type[i] = entities.JobType(t)
		}
	}
	
	filter.Queue = req.Queue
	
	if len(req.Priority) > 0 {
		filter.Priority = make([]entities.JobPriority, len(req.Priority))
		for i, p := range req.Priority {
			filter.Priority[i] = entities.JobPriority(p)
		}
	}
	
	// Get jobs using advanced repository
	advancedRepo, ok := s.jobRepo.(repositories.AdvancedJobRepository)
	if !ok {
		return nil, errors.New("advanced job queries not supported")
	}
	
	jobs, total, err := advancedRepo.GetWithFilters(ctx, filter)
	if err != nil {
		return nil, err
	}
	
	responses := make([]*dtos.JobResponse, len(jobs))
	for i, job := range jobs {
		responses[i] = dtos.JobToResponse(job)
	}
	
	return &dtos.PaginatedResponse[*dtos.JobResponse]{
		Data:       responses,
		Total:      total,
		Page:       req.Page,
		PerPage:    req.PageSize,
		TotalPages: (total + int64(req.PageSize) - 1) / int64(req.PageSize),
	}, nil
}

// Job Control Operations

func (s *JobService) StartJob(ctx context.Context, id string, req *dtos.StartJobRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if err := job.Start(); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to start job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) CompleteJob(ctx context.Context, id string, req *dtos.CompleteJobRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if err := job.Complete(req.Result); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to complete job: %w", err)
	}
	
	// Process dependent jobs
	go s.processDependentJobs(context.Background(), id)
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) FailJob(ctx context.Context, id string, req *dtos.FailJobRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if err := job.Fail(req.ErrorMessage); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to fail job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) CancelJob(ctx context.Context, id string) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if err := job.Cancel(); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to cancel job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) RetryJob(ctx context.Context, id string, req *dtos.RetryJobRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if req.ForceRetry {
		// Force retry by resetting retry count
		job.RetryCount = 0
	}
	
	if err := job.Retry(); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to retry job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) UpdateProgress(ctx context.Context, id string, req *dtos.UpdateProgressRequest) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	if err := job.UpdateProgress(req.Progress, req.Message); err != nil {
		return nil, err
	}
	
	if err := s.jobRepo.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to update progress: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

// Bulk Operations

func (s *JobService) BulkJobOperation(ctx context.Context, req *dtos.BulkJobOperationRequest) (*dtos.BulkJobOperationResponse, error) {
	response := &dtos.BulkJobOperationResponse{}
	
	for _, jobID := range req.JobIDs {
		var err error
		
		switch req.Action {
		case "cancel":
			_, err = s.CancelJob(ctx, jobID)
		case "retry":
			_, err = s.RetryJob(ctx, jobID, &dtos.RetryJobRequest{})
		case "delete":
			err = s.DeleteJob(ctx, jobID)
		default:
			err = fmt.Errorf("unknown action: %s", req.Action)
		}
		
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("Job %s: %s", jobID, err.Error()))
		} else {
			response.SuccessCount++
		}
	}
	
	return response, nil
}

// Job Schedule Management

func (s *JobService) CreateJobSchedule(ctx context.Context, req *dtos.CreateJobScheduleRequest) (*dtos.JobScheduleResponse, error) {
	// Validate references
	if req.OrganizationID != "" {
		orgID, err := uuid.Parse(req.OrganizationID)
		if err != nil {
			return nil, fmt.Errorf("invalid organization ID: %w", err)
		}
		if _, err := s.organizationRepo.GetByID(ctx, orgID); err != nil {
			return nil, fmt.Errorf("organization not found: %w", err)
		}
	}
	
	if req.UserID != "" {
		userID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		if _, err := s.userRepo.GetByID(ctx, userID); err != nil {
			return nil, fmt.Errorf("user not found: %w", err)
		}
	}
	
	// Create schedule entity
	schedule := entities.NewJobSchedule(req.Name, req.JobType, req.Queue, req.CronExpression)
	
	schedule.Description = req.Description
	
	if req.TimeZone != "" {
		schedule.TimeZone = req.TimeZone
	}
	
	if req.MaxRetries != 0 {
		schedule.MaxRetries = req.MaxRetries
	}
	
	if req.TimeoutSeconds != 0 {
		schedule.TimeoutSeconds = req.TimeoutSeconds
	}
	
	schedule.OrganizationID = req.OrganizationID
	schedule.UserID = req.UserID
	
	// Set payload
	if req.Payload != nil {
		for k, v := range req.Payload {
			schedule.SetPayload(k, v)
		}
	}
	
	if err := schedule.Validate(); err != nil {
		return nil, err
	}
	
	if err := s.jobScheduleRepo.Create(ctx, schedule); err != nil {
		return nil, fmt.Errorf("failed to create job schedule: %w", err)
	}
	
	return dtos.JobScheduleToResponse(schedule), nil
}

func (s *JobService) GetJobSchedule(ctx context.Context, id string) (*dtos.JobScheduleResponse, error) {
	schedule, err := s.jobScheduleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	return dtos.JobScheduleToResponse(schedule), nil
}

func (s *JobService) UpdateJobSchedule(ctx context.Context, id string, req *dtos.UpdateJobScheduleRequest) (*dtos.JobScheduleResponse, error) {
	schedule, err := s.jobScheduleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Update fields if provided
	if req.Name != nil {
		schedule.Name = *req.Name
	}
	
	if req.Description != nil {
		schedule.Description = *req.Description
	}
	
	if req.CronExpression != nil {
		schedule.CronExpression = *req.CronExpression
	}
	
	if req.TimeZone != nil {
		schedule.TimeZone = *req.TimeZone
	}
	
	if req.MaxRetries != nil {
		schedule.MaxRetries = *req.MaxRetries
	}
	
	if req.TimeoutSeconds != nil {
		schedule.TimeoutSeconds = *req.TimeoutSeconds
	}
	
	if req.IsActive != nil {
		if *req.IsActive {
			schedule.Activate()
		} else {
			schedule.Deactivate()
		}
	}
	
	// Update payload
	if req.Payload != nil {
		schedule.Payload = req.Payload
	}
	
	if err := schedule.Validate(); err != nil {
		return nil, err
	}
	
	if err := s.jobScheduleRepo.Update(ctx, schedule); err != nil {
		return nil, fmt.Errorf("failed to update job schedule: %w", err)
	}
	
	return dtos.JobScheduleToResponse(schedule), nil
}

func (s *JobService) DeleteJobSchedule(ctx context.Context, id string) error {
	return s.jobScheduleRepo.Delete(ctx, id)
}

func (s *JobService) ListJobSchedules(ctx context.Context, req *dtos.ListJobSchedulesRequest) (*dtos.PaginatedResponse[*dtos.JobScheduleResponse], error) {
	// Calculate limit and offset from pagination parameters
	limit, offset := req.PaginationRequest.GetLimitOffset()
	
	var schedules []*entities.JobSchedule
	var total int64
	var err error
	
	if req.IsActive != nil && *req.IsActive {
		schedules, total, err = s.jobScheduleRepo.ListActive(ctx, limit, offset)
	} else {
		schedules, total, err = s.jobScheduleRepo.List(ctx, limit, offset)
	}
	
	if err != nil {
		return nil, err
	}
	
	// Apply additional filters
	filtered := make([]*entities.JobSchedule, 0)
	for _, schedule := range schedules {
		if req.JobType != "" && string(schedule.JobType) != req.JobType {
			continue
		}
		if req.Queue != "" && schedule.Queue != req.Queue {
			continue
		}
		if req.OrganizationID != "" && schedule.OrganizationID != req.OrganizationID {
			continue
		}
		if req.UserID != "" && schedule.UserID != req.UserID {
			continue
		}
		
		filtered = append(filtered, schedule)
	}
	
	responses := make([]*dtos.JobScheduleResponse, len(filtered))
	for i, schedule := range filtered {
		responses[i] = dtos.JobScheduleToResponse(schedule)
	}
	
	return &dtos.PaginatedResponse[*dtos.JobScheduleResponse]{
		Data:       responses,
		Total:      total,  // Use original total count from repository
		Page:       req.Page,
		PerPage:    req.PageSize,
		TotalPages: (total + int64(req.PageSize) - 1) / int64(req.PageSize),
	}, nil
}

// Statistics and Monitoring

func (s *JobService) GetQueueStats(ctx context.Context, queue string) (*dtos.QueueStatsResponse, error) {
	stats, err := s.jobRepo.GetQueueStats(ctx, queue)
	if err != nil {
		return nil, err
	}
	
	return &dtos.QueueStatsResponse{
		Queue:              stats.Queue,
		PendingJobs:        stats.PendingJobs,
		RunningJobs:        stats.RunningJobs,
		CompletedJobs:      stats.CompletedJobs,
		FailedJobs:         stats.FailedJobs,
		ScheduledJobs:      stats.ScheduledJobs,
		TotalJobs:          stats.TotalJobs,
		AverageWaitTime:    stats.AverageWaitTime,
		AverageProcessTime: stats.AverageProcessTime,
		LastJobAt:          stats.LastJobAt,
		OldestPendingAt:    stats.OldestPendingAt,
	}, nil
}

func (s *JobService) GetJobStats(ctx context.Context, req *dtos.JobStatsRequest) (*dtos.JobStatsResponse, error) {
	startDate := time.Now().AddDate(0, 0, -30) // Default to last 30 days
	endDate := time.Now()
	
	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}
	
	stats, err := s.jobRepo.GetJobStats(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}
	
	return &dtos.JobStatsResponse{
		TotalJobs:          stats.TotalJobs,
		CompletedJobs:      stats.CompletedJobs,
		FailedJobs:         stats.FailedJobs,
		RunningJobs:        stats.RunningJobs,
		PendingJobs:        stats.PendingJobs,
		SuccessRate:        stats.SuccessRate,
		AverageProcessTime: stats.AverageProcessTime,
		ByStatus:           stats.ByStatus,
		ByType:             stats.ByType,
		ByQueue:            stats.ByQueue,
		ByHour:             stats.ByHour,
		ByDay:              stats.ByDay,
	}, nil
}

// Worker Operations

func (s *JobService) GetNextJob(ctx context.Context, queue, workerID string) (*dtos.JobResponse, error) {
	job, err := s.jobRepo.GetNextJob(ctx, queue, workerID)
	if err != nil {
		return nil, err
	}
	
	if job == nil {
		return nil, nil // No jobs available
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) GetReadyJobs(ctx context.Context, queue string, limit int) ([]*dtos.JobResponse, error) {
	jobs, err := s.jobRepo.GetReadyJobs(ctx, queue, limit)
	if err != nil {
		return nil, err
	}
	
	responses := make([]*dtos.JobResponse, len(jobs))
	for i, job := range jobs {
		responses[i] = dtos.JobToResponse(job)
	}
	
	return responses, nil
}

// Specialized Job Creation Methods

func (s *JobService) CreateAITranslationJob(ctx context.Context, req *dtos.CreateAITranslationJobRequest) (*dtos.JobResponse, error) {
	// Validate project exists
	projectID, err := uuid.Parse(req.ProjectID)
	if err != nil {
		return nil, fmt.Errorf("invalid project ID: %w", err)
	}
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("project not found: %w", err)
	}
	
	payload := map[string]interface{}{
		"project_id":         req.ProjectID,
		"source_locale_id":   req.SourceLocaleID,
		"target_locale_ids":  req.TargetLocaleIDs,
		"key_ids":           req.KeyIDs,
		"overwrite_existing": req.OverwriteExisting,
	}
	
	job := entities.NewJob(entities.JobTypeAITranslation, "ai-translation", payload)
	job.SetContext(project.OrganizationID().String(), "", req.ProjectID)
	
	if req.Priority != 0 {
		job.SetPriority(req.Priority)
	}
	
	// Set longer timeout for AI translation jobs
	job.SetTimeout(7200) // 2 hours
	
	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to create AI translation job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) CreateCreditResetJob(ctx context.Context, req *dtos.CreateCreditResetJobRequest) (*dtos.JobResponse, error) {
	payload := map[string]interface{}{
		"subscription_ids": req.SubscriptionIDs,
		"organization_id":  req.OrganizationID,
		"force_reset":      req.ForceReset,
	}
	
	job := entities.NewJob(entities.JobTypeCreditReset, "credit-reset", payload)
	job.SetContext(req.OrganizationID, "", "")
	job.SetPriority(entities.JobPriorityHigh)
	
	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to create credit reset job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

func (s *JobService) CreateEmailJob(ctx context.Context, req *dtos.CreateEmailJobRequest) (*dtos.JobResponse, error) {
	payload := map[string]interface{}{
		"to":       req.To,
		"subject":  req.Subject,
		"template": req.Template,
		"data":     req.Data,
	}
	
	job := entities.NewJob(entities.JobTypeEmailNotification, "email", payload)
	
	if req.Priority != 0 {
		job.SetPriority(req.Priority)
	}
	
	if req.ScheduledAt != nil {
		job.Schedule(*req.ScheduledAt)
	}
	
	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to create email job: %w", err)
	}
	
	return dtos.JobToResponse(job), nil
}

// Maintenance Operations

func (s *JobService) CleanupOldJobs(ctx context.Context, olderThan time.Time) (int64, error) {
	return s.jobRepo.CleanupOldJobs(ctx, olderThan)
}

func (s *JobService) ProcessExpiredJobs(ctx context.Context) error {
	expiredJobs, err := s.jobRepo.GetExpiredJobs(ctx)
	if err != nil {
		return err
	}
	
	for _, job := range expiredJobs {
		job.Cancel()
		s.jobRepo.Update(ctx, job)
	}
	
	return nil
}

func (s *JobService) ProcessTimedOutJobs(ctx context.Context) error {
	timedOutJobs, err := s.jobRepo.GetTimedOutJobs(ctx)
	if err != nil {
		return err
	}
	
	for _, job := range timedOutJobs {
		job.Fail("Job timed out")
		s.jobRepo.Update(ctx, job)
	}
	
	return nil
}

// Helper methods

func (s *JobService) processDependentJobs(ctx context.Context, completedJobID string) {
	dependentJobs, err := s.jobRepo.GetDependentJobs(ctx, completedJobID)
	if err != nil {
		return
	}
	
	for _, depJob := range dependentJobs {
		// Check if all dependencies are completed
		dependencies, err := s.jobRepo.GetJobDependencies(ctx, depJob.ID.String())
		if err != nil {
			continue
		}
		
		allCompleted := true
		for _, dep := range dependencies {
			if dep.Status != entities.JobStatusCompleted {
				allCompleted = false
				break
			}
		}
		
		// If all dependencies are completed, make the job ready
		if allCompleted && depJob.Status == entities.JobStatusPending {
			depJob.Status = entities.JobStatusPending // Ready to run
			s.jobRepo.Update(ctx, depJob)
		}
	}
}