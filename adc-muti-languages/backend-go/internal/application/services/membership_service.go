package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// Helper function to convert membership to status string
func membershipToStatusString(membership *entities.OrganizationMembership) string {
	if membership.IsActive {
		return "active"
	}
	if membership.IsPending() {
		return "pending"
	}
	if membership.IsExpired() {
		return "expired"
	}
	return "suspended"
}

// MembershipService handles organization membership operations
type MembershipService struct {
	membershipRepo   repositories.OrganizationMembershipRepository
	userRepo         repositories.UserRepository
	organizationRepo repositories.OrganizationRepository
	// emailService     EmailService // Will implement later
}

// NewMembershipService creates a new membership service
func NewMembershipService(
	membershipRepo repositories.OrganizationMembershipRepository,
	userRepo repositories.UserRepository,
	organizationRepo repositories.OrganizationRepository,
) *MembershipService {
	return &MembershipService{
		membershipRepo:   membershipRepo,
		userRepo:         userRepo,
		organizationRepo: organizationRepo,
	}
}

// CreateMembership creates a new organization membership
func (s *MembershipService) CreateMembership(ctx context.Context, req *dtos.CreateMembershipRequest) (*dtos.MembershipResponse, error) {
	// Validate organization exists
	_, err := s.organizationRepo.GetByID(ctx, req.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %w", err)
	}

	// Validate user exists
	_, err = s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Check if membership already exists
	existing, err := s.membershipRepo.GetByUserAndOrganization(ctx, req.UserID, req.OrganizationID)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("user is already a member of this organization")
	}

	// Validate role
	if !entities.IsValidRole(req.Role) {
		return nil, fmt.Errorf("invalid role: %s", req.Role)
	}

	// Create membership
	membership := entities.NewOrganizationMembership(req.OrganizationID, req.UserID, req.Role)
	
	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		return nil, fmt.Errorf("failed to create membership: %w", err)
	}

	return s.membershipToResponse(membership), nil
}

// InviteUser sends an invitation to join an organization
func (s *MembershipService) InviteUser(ctx context.Context, inviterID uuid.UUID, req *dtos.InviteUserRequest) (*dtos.InvitationResponse, error) {
	// Validate organization exists
	_, err := s.organizationRepo.GetByID(ctx, req.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %w", err)
	}

	// Check if inviter has permission to invite users
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, inviterID, req.OrganizationID, entities.PermissionManageMembers)
	if err != nil {
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}
	if !hasPermission {
		return nil, fmt.Errorf("insufficient permissions to invite users")
	}

	// Check if user already exists and get/create user
	var userID uuid.UUID
	// This is simplified - in real implementation, we'd need to handle user creation or lookup by email
	// For now, we'll assume the user ID is provided or create a placeholder
	userID = uuid.New() // Placeholder

	// Check if membership already exists
	existing, err := s.membershipRepo.GetByUserAndOrganization(ctx, userID, req.OrganizationID)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("user is already a member of this organization")
	}

	// Validate role
	if !entities.IsValidRole(req.Role) {
		return nil, fmt.Errorf("invalid role: %s", req.Role)
	}

	// Owners can only be set directly, not through invitation
	if req.Role == entities.RoleOwner {
		return nil, fmt.Errorf("cannot invite user as owner")
	}

	// Generate invitation token
	token, err := s.generateInviteToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate invite token: %w", err)
	}

	// Create pending membership with invitation
	expiresAt := time.Now().Add(7 * 24 * time.Hour) // 7 days
	membership := entities.NewPendingMembership(req.OrganizationID, userID, inviterID, req.Role, token, expiresAt)

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		return nil, fmt.Errorf("failed to create invitation: %w", err)
	}

	// TODO: Send invitation email
	// s.emailService.SendInvitation(ctx, req.Email, token, inviterID, req.OrganizationID)

	return &dtos.InvitationResponse{
		ID:             membership.ID,
		OrganizationID: membership.OrganizationID,
		Email:          req.Email, // Store email separately in real implementation
		Role:           membership.Role,
		DisplayRole:    membership.GetDisplayRole(),
		Status:         membershipToStatusString(membership),
		InvitedBy:      *membership.InvitedBy,
		InvitedAt:      *membership.InvitedAt,
		ExpiresAt:      membership.TokenExpiresAt,
		CreatedAt:      membership.CreatedAt,
	}, nil
}

// AcceptInvitation accepts a pending invitation
func (s *MembershipService) AcceptInvitation(ctx context.Context, userID uuid.UUID, req *dtos.AcceptInvitationRequest) (*dtos.MembershipResponse, error) {
	// Find invitation by token
	membership, err := s.membershipRepo.GetByInviteToken(ctx, req.Token)
	if err != nil {
		return nil, fmt.Errorf("invitation not found or invalid: %w", err)
	}

	// Validate invitation can be accepted
	if !membership.CanAcceptInvitation() {
		return nil, fmt.Errorf("invitation cannot be accepted (expired or invalid)")
	}

	// Validate user matches invitation
	if membership.UserID != userID {
		return nil, fmt.Errorf("invitation is not for this user")
	}

	// Accept invitation
	if err := membership.AcceptInvitation(); err != nil {
		return nil, fmt.Errorf("failed to accept invitation: %w", err)
	}

	// Update membership
	if err := s.membershipRepo.Update(ctx, membership); err != nil {
		return nil, fmt.Errorf("failed to update membership: %w", err)
	}

	return s.membershipToResponse(membership), nil
}

// UpdateMemberRole updates a member's role
func (s *MembershipService) UpdateMemberRole(ctx context.Context, updaterID uuid.UUID, req *dtos.UpdateMemberRoleRequest) (*dtos.MembershipResponse, error) {
	// Check if updater has permission
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, updaterID, req.OrganizationID, entities.PermissionManageMembers)
	if err != nil {
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}
	if !hasPermission {
		return nil, fmt.Errorf("insufficient permissions to update member roles")
	}

	// Get existing membership
	membership, err := s.membershipRepo.GetByUserAndOrganization(ctx, req.UserID, req.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("membership not found: %w", err)
	}

	// Validate role
	if !entities.IsValidRole(req.Role) {
		return nil, fmt.Errorf("invalid role: %s", req.Role)
	}

	// Prevent changing owner role (requires special handling)
	if membership.Role == entities.RoleOwner || req.Role == entities.RoleOwner {
		return nil, fmt.Errorf("owner role changes require special handling")
	}

	// Update role
	membership.UpdateRole(req.Role)

	if err := s.membershipRepo.Update(ctx, membership); err != nil {
		return nil, fmt.Errorf("failed to update membership: %w", err)
	}

	return s.membershipToResponse(membership), nil
}

// RemoveMember removes a member from an organization
func (s *MembershipService) RemoveMember(ctx context.Context, removerID uuid.UUID, organizationID, userID uuid.UUID) error {
	// Check if remover has permission
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, removerID, organizationID, entities.PermissionManageMembers)
	if err != nil {
		return fmt.Errorf("failed to check permissions: %w", err)
	}
	if !hasPermission {
		return fmt.Errorf("insufficient permissions to remove members")
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndOrganization(ctx, userID, organizationID)
	if err != nil {
		return fmt.Errorf("membership not found: %w", err)
	}

	// Prevent removing owner
	if membership.Role == entities.RoleOwner {
		return fmt.Errorf("cannot remove organization owner")
	}

	// Prevent self-removal (unless user is removing themselves)
	if removerID != userID && removerID == userID {
		return fmt.Errorf("cannot remove yourself")
	}

	// Delete membership
	if err := s.membershipRepo.Delete(ctx, membership.ID); err != nil {
		return fmt.Errorf("failed to remove member: %w", err)
	}

	return nil
}

// SuspendMember suspends a member
func (s *MembershipService) SuspendMember(ctx context.Context, suspenderID uuid.UUID, req *dtos.SuspendMemberRequest) error {
	// Check if suspender has permission
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, suspenderID, req.OrganizationID, entities.PermissionManageMembers)
	if err != nil {
		return fmt.Errorf("failed to check permissions: %w", err)
	}
	if !hasPermission {
		return fmt.Errorf("insufficient permissions to suspend members")
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndOrganization(ctx, req.UserID, req.OrganizationID)
	if err != nil {
		return fmt.Errorf("membership not found: %w", err)
	}

	// Prevent suspending owner
	if membership.Role == entities.RoleOwner {
		return fmt.Errorf("cannot suspend organization owner")
	}

	// Suspend membership
	membership.Suspend()

	if err := s.membershipRepo.Update(ctx, membership); err != nil {
		return fmt.Errorf("failed to suspend member: %w", err)
	}

	return nil
}

// ReactivateMember reactivates a suspended member
func (s *MembershipService) ReactivateMember(ctx context.Context, reactivatorID uuid.UUID, organizationID, userID uuid.UUID) error {
	// Check if reactivator has permission
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, reactivatorID, organizationID, entities.PermissionManageMembers)
	if err != nil {
		return fmt.Errorf("failed to check permissions: %w", err)
	}
	if !hasPermission {
		return fmt.Errorf("insufficient permissions to reactivate members")
	}

	// Get membership
	membership, err := s.membershipRepo.GetByUserAndOrganization(ctx, userID, organizationID)
	if err != nil {
		return fmt.Errorf("membership not found: %w", err)
	}

	// Reactivate membership
	membership.Reactivate()

	if err := s.membershipRepo.Update(ctx, membership); err != nil {
		return fmt.Errorf("failed to reactivate member: %w", err)
	}

	return nil
}

// ListMembers lists organization members with pagination and filtering
func (s *MembershipService) ListMembers(ctx context.Context, organizationID uuid.UUID, filter dtos.MembershipFilter, pagination dtos.PaginationRequest) (*dtos.MembershipListResponse, error) {
	// Convert DTOs to repository types
	repoFilter := repositories.MembershipFilter{
		Role:      filter.Role,
		IsActive:  filter.IsActive,
		IsPending: filter.IsPending,
		IsExpired: filter.IsExpired,
		InvitedBy: filter.InvitedBy,
		Search:    filter.Search,
	}

	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	// Get memberships
	memberships, paginationResult, err := s.membershipRepo.ListByOrganization(ctx, organizationID, repoFilter, repoPagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list members: %w", err)
	}

	// Convert to response DTOs with user data
	responses := make([]*dtos.MembershipResponse, len(memberships))
	for i, membership := range memberships {
		membershipResponse := s.membershipToResponse(membership)
		
		// Fetch user data
		user, err := s.userRepo.GetByID(ctx, membership.UserID)
		if err == nil {
			membershipResponse.User = s.userToResponse(user)
		}
		
		responses[i] = membershipResponse
	}

	return &dtos.MembershipListResponse{
		Memberships: responses,
		Pagination:  s.paginationToDTO(paginationResult),
	}, nil
}

// CheckPermission checks if a user has a specific permission in an organization
func (s *MembershipService) CheckPermission(ctx context.Context, req *dtos.CheckPermissionRequest) (*dtos.CheckPermissionResponse, error) {
	hasPermission, err := s.membershipRepo.CheckUserPermission(ctx, req.UserID, req.OrganizationID, req.Permission)
	if err != nil {
		return nil, fmt.Errorf("failed to check permission: %w", err)
	}

	response := &dtos.CheckPermissionResponse{
		HasPermission: hasPermission,
	}

	// Get additional details if user is a member
	if membership, err := s.membershipRepo.GetByUserAndOrganization(ctx, req.UserID, req.OrganizationID); err == nil {
		response.Role = membership.Role
		response.Status = membershipToStatusString(membership)
	}

	return response, nil
}

// GetMembershipStats gets membership statistics for an organization
func (s *MembershipService) GetMembershipStats(ctx context.Context, organizationID uuid.UUID) (*dtos.MembershipStatsResponse, error) {
	stats, err := s.membershipRepo.GetMembershipStats(ctx, organizationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get membership stats: %w", err)
	}

	// Convert recent joins
	recentJoins := make([]*dtos.MembershipResponse, len(stats.RecentJoins))
	for i, membership := range stats.RecentJoins {
		recentJoins[i] = s.membershipToResponse(membership)
	}

	// Convert expiring soon to invitation responses
	expiringSoon := make([]*dtos.InvitationResponse, len(stats.ExpiringSoon))
	for i, membership := range stats.ExpiringSoon {
		expiringSoon[i] = &dtos.InvitationResponse{
			ID:             membership.ID,
			OrganizationID: membership.OrganizationID,
			// Email would be stored separately in real implementation
			Role:        membership.Role,
			DisplayRole: membership.GetDisplayRole(),
			Status:      membershipToStatusString(membership),
			InvitedBy:   *membership.InvitedBy,
			InvitedAt:   *membership.InvitedAt,
			ExpiresAt:   membership.TokenExpiresAt,
			CreatedAt:   membership.CreatedAt,
		}
	}

	return &dtos.MembershipStatsResponse{
		TotalMembers:     stats.TotalMembers,
		ActiveMembers:    stats.ActiveMembers,
		PendingMembers:   stats.PendingMembers,
		SuspendedMembers: stats.SuspendedMembers,
		MembersByRole:    stats.MembersByRole,
		RecentJoins:      recentJoins,
		ExpiringSoon:     expiringSoon,
	}, nil
}

// Helper methods

func (s *MembershipService) membershipToResponse(membership *entities.OrganizationMembership) *dtos.MembershipResponse {
	return &dtos.MembershipResponse{
		ID:             membership.ID,
		OrganizationID: membership.OrganizationID,
		UserID:         membership.UserID,
		Role:           membership.Role,
		DisplayRole:    membership.GetDisplayRole(),
		Status:         membershipToStatusString(membership),
		Permissions:    membership.Permissions,
		InvitedBy:      membership.InvitedBy,
		InvitedAt:      membership.InvitedAt,
		JoinedAt:       membership.JoinedAt,
		CreatedAt:      membership.CreatedAt,
		UpdatedAt:      membership.UpdatedAt,
	}
}

func (s *MembershipService) paginationToDTO(pagination *repositories.PaginationResult) *dtos.PaginationResult {
	return &dtos.PaginationResult{
		Total:       pagination.Total,
		TotalPages:  int64(pagination.TotalPages),
		CurrentPage: pagination.CurrentPage,
		PageSize:    pagination.PageSize,
		HasNext:     pagination.HasNext,
		HasPrev:     pagination.HasPrev,
	}
}

func (s *MembershipService) userToResponse(user *entities.User) *dtos.UserResponse {
	var languageStr *string
	if user.Language() != nil {
		langStr := user.Language().String()
		languageStr = &langStr
	}

	return &dtos.UserResponse{
		ID:                user.ID(),
		Email:             user.Email().String(),
		Username:          user.Username(),
		FirstName:         user.FirstName(),
		LastName:          user.LastName(),
		FullName:          user.FullName(),
		EmailVerified:     user.EmailVerified(),
		EmailVerifiedAt:   user.EmailVerifiedAt(),
		LastLoginAt:       user.LastLoginAt(),
		IsActive:          user.IsActive(),
		ProfilePictureURL: user.ProfilePictureURL(),
		Timezone:          user.Timezone(),
		Language:          languageStr,
		CreatedAt:         user.CreatedAt(),
		UpdatedAt:         user.UpdatedAt(),
	}
}

func (s *MembershipService) generateInviteToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}