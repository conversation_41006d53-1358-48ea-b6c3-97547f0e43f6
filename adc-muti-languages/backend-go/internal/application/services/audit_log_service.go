package services

import (
	"context"
	"fmt"
	"time"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// AuditLogService provides business logic for audit log management
type AuditLogService struct {
	auditLogRepo repositories.AuditLogRepository
	userRepo     repositories.UserRepository
}

// NewAuditLogService creates a new audit log service
func NewAuditLogService(
	auditLogRepo repositories.AuditLogRepository,
	userRepo repositories.UserRepository,
) *AuditLogService {
	return &AuditLogService{
		auditLogRepo: auditLogRepo,
		userRepo:     userRepo,
	}
}

// CreateAuditLog creates a new audit log entry
func (s *AuditLogService) CreateAuditLog(ctx context.Context, req *dtos.CreateAuditLogRequest) (*dtos.AuditLogDTO, error) {
	// Create audit log entity
	auditLog := entities.NewAuditLog(req.UserID, req.Action, req.ResourceType)
	
	// Set optional fields
	if req.ResourceID != nil {
		auditLog.SetResourceID(*req.ResourceID)
	}
	
	auditLog.SetDetails(req.Details)
	auditLog.Success = req.Success
	
	if req.IPAddress != nil {
		auditLog.SetIPAddress(*req.IPAddress)
	}
	
	if req.UserAgent != nil {
		auditLog.SetUserAgent(*req.UserAgent)
	}
	
	if req.ErrorMessage != nil {
		auditLog.ErrorMessage = req.ErrorMessage
	}
	
	if req.OrganizationID != nil {
		auditLog.SetOrganizationID(*req.OrganizationID)
	}
	
	if req.SessionID != nil {
		auditLog.SetSessionID(*req.SessionID)
	}
	
	if req.APIKeyID != nil {
		auditLog.SetAPIKeyID(*req.APIKeyID)
	}
	
	if req.Duration != nil {
		auditLog.SetDuration(*req.Duration)
	}
	
	if req.Metadata != nil {
		for key, value := range req.Metadata {
			auditLog.AddMetadata(key, value)
		}
	}
	
	// Validate the audit log
	if err := auditLog.Validate(); err != nil {
		return nil, fmt.Errorf("invalid audit log: %w", err)
	}
	
	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		return nil, fmt.Errorf("failed to create audit log: %w", err)
	}
	
	return s.toDTO(auditLog), nil
}

// LogAction creates an audit log for a specific action
func (s *AuditLogService) LogAction(ctx context.Context, userID string, action entities.AuditAction, resourceType entities.ResourceType, resourceID string) error {
	auditLog := entities.NewSuccessAuditLog(userID, action, resourceType, resourceID)
	
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		return fmt.Errorf("failed to log action: %w", err)
	}
	
	return nil
}

// LogFailure creates an audit log for a failed action
func (s *AuditLogService) LogFailure(ctx context.Context, userID string, action entities.AuditAction, resourceType entities.ResourceType, errorMsg string) error {
	auditLog := entities.NewFailureAuditLog(userID, action, resourceType, errorMsg)
	
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		return fmt.Errorf("failed to log failure: %w", err)
	}
	
	return nil
}

// GetAuditLog retrieves an audit log by ID
func (s *AuditLogService) GetAuditLog(ctx context.Context, id string) (*dtos.AuditLogDTO, error) {
	auditLogID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid audit log ID: %w", err)
	}
	
	auditLog, err := s.auditLogRepo.GetByID(ctx, auditLogID)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit log: %w", err)
	}
	
	return s.toDTOWithUserInfo(ctx, auditLog), nil
}

// ListAuditLogs lists audit logs with pagination and filtering
func (s *AuditLogService) ListAuditLogs(ctx context.Context, req *dtos.ListAuditLogsRequest) (interface{}, error) {
	filter := s.buildAuditLogFilter(req)
	pagination := repositories.Pagination{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
		Order:    req.Order,
	}
	
	auditLogs, paginationResult, err := s.auditLogRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list audit logs: %w", err)
	}
	
	// Convert to DTOs with user information
	dtos := make([]*dtos.AuditLogDTO, len(auditLogs))
	for i, auditLog := range auditLogs {
		dtos[i] = s.toDTOWithUserInfo(ctx, auditLog)
	}
	
	return map[string]interface{}{
		"data":         dtos,
		"total":        paginationResult.Total,
		"current_page": paginationResult.CurrentPage,
		"page_size":    paginationResult.PageSize,
		"total_pages":  paginationResult.TotalPages,
		"has_next":     paginationResult.HasNext,
		"has_prev":     paginationResult.HasPrev,
	}, nil
}

// ListAuditLogsByUser lists audit logs for a specific user
func (s *AuditLogService) ListAuditLogsByUser(ctx context.Context, userID string, req *dtos.ListAuditLogsRequest) (interface{}, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	filter := s.buildAuditLogFilter(req)
	pagination := repositories.Pagination{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
		Order:    req.Order,
	}
	
	auditLogs, paginationResult, err := s.auditLogRepo.ListByUser(ctx, userUUID, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list audit logs for user: %w", err)
	}
	
	// Convert to DTOs
	dtos := make([]*dtos.AuditLogDTO, len(auditLogs))
	for i, auditLog := range auditLogs {
		dtos[i] = s.toDTOWithUserInfo(ctx, auditLog)
	}
	
	return map[string]interface{}{
		"data":         dtos,
		"total":        paginationResult.Total,
		"current_page": paginationResult.CurrentPage,
		"page_size":    paginationResult.PageSize,
		"total_pages":  paginationResult.TotalPages,
		"has_next":     paginationResult.HasNext,
		"has_prev":     paginationResult.HasPrev,
	}, nil
}

// ListAuditLogsByOrganization lists audit logs for a specific organization
func (s *AuditLogService) ListAuditLogsByOrganization(ctx context.Context, organizationID string, req *dtos.ListAuditLogsRequest) (interface{}, error) {
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return nil, fmt.Errorf("invalid organization ID: %w", err)
	}
	
	filter := s.buildAuditLogFilter(req)
	pagination := repositories.Pagination{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
		Order:    req.Order,
	}
	
	auditLogs, paginationResult, err := s.auditLogRepo.ListByOrganization(ctx, orgUUID, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list audit logs for organization: %w", err)
	}
	
	// Convert to DTOs
	dtos := make([]*dtos.AuditLogDTO, len(auditLogs))
	for i, auditLog := range auditLogs {
		dtos[i] = s.toDTOWithUserInfo(ctx, auditLog)
	}
	
	return map[string]interface{}{
		"data":         dtos,
		"total":        paginationResult.Total,
		"current_page": paginationResult.CurrentPage,
		"page_size":    paginationResult.PageSize,
		"total_pages":  paginationResult.TotalPages,
		"has_next":     paginationResult.HasNext,
		"has_prev":     paginationResult.HasPrev,
	}, nil
}

// GetAuditLogStats retrieves audit log statistics
func (s *AuditLogService) GetAuditLogStats(ctx context.Context, req *dtos.AuditLogStatsRequest) (*dtos.AuditLogStatsResponse, error) {
	filter := repositories.AuditLogStatsFilter{
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		OrganizationID: s.parseUUIDPtr(req.OrganizationID),
		UserID:         s.parseUUIDPtr(req.UserID),
		ResourceType:   req.ResourceType,
		GroupBy:        req.GroupBy,
	}
	
	stats, err := s.auditLogRepo.GetActionStats(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit log stats: %w", err)
	}
	
	response := &dtos.AuditLogStatsResponse{
		TotalLogs:      stats.TotalLogs,
		SuccessfulLogs: stats.SuccessfulLogs,
		FailedLogs:     stats.FailedLogs,
		SuccessRate:    stats.SuccessRate,
		ActionStats:    stats.ActionStats,
		ResourceStats:  stats.ResourceStats,
		UserStats:      stats.UserStats,
		DailyStats:     stats.DailyStats,
		HourlyStats:    stats.HourlyStats,
		ErrorSummary:   stats.ErrorSummary,
	}
	
	// Convert top users
	response.TopUsers = make([]dtos.UserActivitySummaryDTO, len(stats.TopUsers))
	for i, user := range stats.TopUsers {
		response.TopUsers[i] = dtos.UserActivitySummaryDTO{
			UserID:       user.UserID.String(),
			UserEmail:    user.UserEmail,
			ActionCount:  user.ActionCount,
			LastActivity: user.LastActivity,
		}
	}
	
	// Convert top actions
	response.TopActions = make([]dtos.ActionSummaryDTO, len(stats.TopActions))
	for i, action := range stats.TopActions {
		response.TopActions[i] = dtos.ActionSummaryDTO{
			Action:      action.Action,
			Count:       action.Count,
			SuccessRate: action.SuccessRate,
		}
	}
	
	// Convert top resources
	response.TopResources = make([]dtos.ResourceSummaryDTO, len(stats.TopResources))
	for i, resource := range stats.TopResources {
		response.TopResources[i] = dtos.ResourceSummaryDTO{
			ResourceType: resource.ResourceType,
			Count:        resource.Count,
			SuccessRate:  resource.SuccessRate,
		}
	}
	
	// Add time range if specified
	if req.StartDate != nil && req.EndDate != nil {
		response.TimeRange = &dtos.TimeRangeDTO{
			StartDate: *req.StartDate,
			EndDate:   *req.EndDate,
		}
	}
	
	return response, nil
}

// GetUserActivityStats retrieves activity statistics for a specific user
func (s *AuditLogService) GetUserActivityStats(ctx context.Context, userID string, req *dtos.AuditLogStatsRequest) (*dtos.UserActivityStatsResponse, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	filter := repositories.AuditLogStatsFilter{
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		OrganizationID: s.parseUUIDPtr(req.OrganizationID),
		ResourceType:   req.ResourceType,
		GroupBy:        req.GroupBy,
	}
	
	stats, err := s.auditLogRepo.GetUserActivityStats(ctx, userUUID, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity stats: %w", err)
	}
	
	response := &dtos.UserActivityStatsResponse{
		UserID:            stats.UserID.String(),
		TotalActions:      stats.TotalActions,
		SuccessfulActions: stats.SuccessfulActions,
		FailedActions:     stats.FailedActions,
		SuccessRate:       stats.SuccessRate,
		LastActivity:      stats.LastActivity,
		MostCommonAction:  stats.MostCommonAction,
		ActionBreakdown:   stats.ActionBreakdown,
		ResourceBreakdown: stats.ResourceBreakdown,
		DailyActivity:     stats.DailyActivity,
	}
	
	// Convert recent actions
	response.RecentActions = make([]dtos.RecentActionSummaryDTO, len(stats.RecentActions))
	for i, action := range stats.RecentActions {
		var resourceID *string
		if action.ResourceID != nil {
			resourceIDStr := action.ResourceID.String()
			resourceID = &resourceIDStr
		}
		
		response.RecentActions[i] = dtos.RecentActionSummaryDTO{
			Action:       action.Action,
			ResourceType: action.ResourceType,
			ResourceID:   resourceID,
			Success:      action.Success,
			Timestamp:    action.Timestamp,
			Details:      action.Details,
		}
	}
	
	return response, nil
}

// SearchAuditLogs searches audit logs based on query
func (s *AuditLogService) SearchAuditLogs(ctx context.Context, req *dtos.SearchAuditLogsRequest) (interface{}, error) {
	filter := repositories.AuditLogFilter{
		Search:         &req.Query,
		OrganizationID: s.parseUUIDPtr(req.OrganizationID),
		UserID:         s.parseUUIDPtr(req.UserID),
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
	}
	
	// Handle success/failure filtering
	if req.IncludeSuccess != nil && req.IncludeFailure != nil {
		if *req.IncludeSuccess && !*req.IncludeFailure {
			success := true
			filter.Success = &success
		} else if !*req.IncludeSuccess && *req.IncludeFailure {
			success := false
			filter.Success = &success
		}
		// If both true or both false, don't filter
	}
	
	pagination := repositories.Pagination{
		Page:     req.Page,
		PageSize: req.PageSize,
		OrderBy:  req.OrderBy,
		Order:    req.Order,
	}
	
	auditLogs, paginationResult, err := s.auditLogRepo.Search(ctx, req.Query, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to search audit logs: %w", err)
	}
	
	// Convert to DTOs
	dtos := make([]*dtos.AuditLogDTO, len(auditLogs))
	for i, auditLog := range auditLogs {
		dtos[i] = s.toDTOWithUserInfo(ctx, auditLog)
	}
	
	return map[string]interface{}{
		"data":         dtos,
		"total":        paginationResult.Total,
		"current_page": paginationResult.CurrentPage,
		"page_size":    paginationResult.PageSize,
		"total_pages":  paginationResult.TotalPages,
		"has_next":     paginationResult.HasNext,
		"has_prev":     paginationResult.HasPrev,
	}, nil
}

// BulkCreateAuditLogs creates multiple audit logs in a batch
func (s *AuditLogService) BulkCreateAuditLogs(ctx context.Context, req *dtos.BulkCreateAuditLogsRequest) (*dtos.BulkCreateAuditLogsResponse, error) {
	auditLogs := make([]*entities.AuditLog, 0, len(req.AuditLogs))
	var errors []string
	
	// Validate and create entities
	for i, logReq := range req.AuditLogs {
		auditLog := entities.NewAuditLog(logReq.UserID, logReq.Action, logReq.ResourceType)
		
		// Set optional fields (similar to CreateAuditLog)
		if logReq.ResourceID != nil {
			auditLog.SetResourceID(*logReq.ResourceID)
		}
		auditLog.SetDetails(logReq.Details)
		auditLog.Success = logReq.Success
		
		// Validate
		if err := auditLog.Validate(); err != nil {
			errors = append(errors, fmt.Sprintf("Log %d: %s", i+1, err.Error()))
			continue
		}
		
		auditLogs = append(auditLogs, auditLog)
	}
	
	// Create batch
	var created int
	if len(auditLogs) > 0 {
		if err := s.auditLogRepo.CreateBatch(ctx, auditLogs); err != nil {
			return nil, fmt.Errorf("failed to create audit logs batch: %w", err)
		}
		created = len(auditLogs)
	}
	
	return &dtos.BulkCreateAuditLogsResponse{
		Created:        created,
		Failed:         len(errors),
		Errors:         errors,
		TotalProcessed: len(req.AuditLogs),
	}, nil
}

// CleanupOldAuditLogs removes old audit logs
func (s *AuditLogService) CleanupOldAuditLogs(ctx context.Context, req *dtos.CleanupAuditLogsRequest) (*dtos.CleanupAuditLogsResponse, error) {
	cutoffDate := time.Now().AddDate(0, 0, -req.OlderThanDays)
	
	if req.DryRun {
		// For dry run, just count what would be deleted
		filter := repositories.AuditLogFilter{
			EndDate: &cutoffDate,
		}
		if req.OrganizationID != nil {
			filter.OrganizationID = s.parseUUIDPtr(req.OrganizationID)
		}
		
		count, err := s.auditLogRepo.Count(ctx, filter)
		if err != nil {
			return nil, fmt.Errorf("failed to count audit logs for cleanup: %w", err)
		}
		
		return &dtos.CleanupAuditLogsResponse{
			DeletedCount: count,
			DryRun:       true,
		}, nil
	}
	
	// Perform actual cleanup
	deletedCount, err := s.auditLogRepo.DeleteOlderThan(ctx, cutoffDate)
	if err != nil {
		return nil, fmt.Errorf("failed to cleanup audit logs: %w", err)
	}
	
	return &dtos.CleanupAuditLogsResponse{
		DeletedCount:  deletedCount,
		OldestDeleted: &cutoffDate,
		DryRun:        false,
	}, nil
}

// GetAvailableActions returns all available audit actions
func (s *AuditLogService) GetAvailableActions(ctx context.Context) (*dtos.GetAvailableActionsResponse, error) {
	actions := []dtos.AuditActionInfo{
		{Action: entities.AuditActionUserCreated, Category: "user", Name: "created", Description: "User account created"},
		{Action: entities.AuditActionUserUpdated, Category: "user", Name: "updated", Description: "User account updated"},
		{Action: entities.AuditActionUserDeleted, Category: "user", Name: "deleted", Description: "User account deleted"},
		{Action: entities.AuditActionUserSignedIn, Category: "auth", Name: "signed_in", Description: "User signed in"},
		{Action: entities.AuditActionUserSignedOut, Category: "auth", Name: "signed_out", Description: "User signed out"},
		{Action: entities.AuditActionOrganizationCreated, Category: "organization", Name: "created", Description: "Organization created"},
		{Action: entities.AuditActionProjectCreated, Category: "project", Name: "created", Description: "Project created"},
		{Action: entities.AuditActionTranslationCreated, Category: "translation", Name: "created", Description: "Translation created"},
		{Action: entities.AuditActionAPIKeyCreated, Category: "api_key", Name: "created", Description: "API key created"},
		// Add more as needed
	}
	
	return &dtos.GetAvailableActionsResponse{
		Actions: actions,
		Total:   len(actions),
	}, nil
}

// GetAvailableResourceTypes returns all available resource types
func (s *AuditLogService) GetAvailableResourceTypes(ctx context.Context) (*dtos.GetAvailableResourceTypesResponse, error) {
	resourceTypes := []dtos.ResourceTypeInfo{
		{ResourceType: entities.ResourceTypeUser, Name: "User", Description: "User accounts"},
		{ResourceType: entities.ResourceTypeOrganization, Name: "Organization", Description: "Organizations"},
		{ResourceType: entities.ResourceTypeProject, Name: "Project", Description: "Translation projects"},
		{ResourceType: entities.ResourceTypeTranslation, Name: "Translation", Description: "Translations"},
		{ResourceType: entities.ResourceTypeAPIKey, Name: "API Key", Description: "API keys"},
		{ResourceType: entities.ResourceTypeSubscription, Name: "Subscription", Description: "Subscriptions"},
		// Add more as needed
	}
	
	return &dtos.GetAvailableResourceTypesResponse{
		ResourceTypes: resourceTypes,
		Total:         len(resourceTypes),
	}, nil
}

// Helper methods

// buildAuditLogFilter builds a repository filter from DTO request
func (s *AuditLogService) buildAuditLogFilter(req *dtos.ListAuditLogsRequest) repositories.AuditLogFilter {
	return repositories.AuditLogFilter{
		UserID:         s.parseUUIDPtr(req.UserID),
		OrganizationID: s.parseUUIDPtr(req.OrganizationID),
		Action:         req.Action,
		ResourceType:   req.ResourceType,
		ResourceID:     s.parseUUIDPtr(req.ResourceID),
		Success:        req.Success,
		IPAddress:      req.IPAddress,
		SessionID:      s.parseUUIDPtr(req.SessionID),
		APIKeyID:       s.parseUUIDPtr(req.APIKeyID),
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		Search:         req.Search,
		Actions:        req.Actions,
		ResourceTypes:  req.ResourceTypes,
	}
}

// parseUUIDPtr parses a string pointer to a UUID pointer
func (s *AuditLogService) parseUUIDPtr(idStr *string) *uuid.UUID {
	if idStr == nil {
		return nil
	}
	
	if id, err := uuid.Parse(*idStr); err == nil {
		return &id
	}
	
	return nil
}

// toDTO converts audit log entity to DTO
func (s *AuditLogService) toDTO(auditLog *entities.AuditLog) *dtos.AuditLogDTO {
	return &dtos.AuditLogDTO{
		ID:             auditLog.ID.String(),
		UserID:         auditLog.UserID,
		Action:         auditLog.Action,
		ResourceType:   auditLog.ResourceType,
		ResourceID:     auditLog.ResourceID,
		Details:        auditLog.Details,
		IPAddress:      auditLog.IPAddress,
		UserAgent:      auditLog.UserAgent,
		Success:        auditLog.Success,
		ErrorMessage:   auditLog.ErrorMessage,
		OrganizationID: auditLog.OrganizationID,
		SessionID:      auditLog.SessionID,
		APIKeyID:       auditLog.APIKeyID,
		Duration:       auditLog.Duration,
		Metadata:       auditLog.Metadata,
		CreatedAt:      auditLog.CreatedAt,
		UpdatedAt:      auditLog.UpdatedAt,
		ActionCategory: auditLog.GetActionCategory(),
		ActionName:     auditLog.GetActionName(),
	}
}

// toDTOWithUserInfo converts audit log entity to DTO with user information
func (s *AuditLogService) toDTOWithUserInfo(ctx context.Context, auditLog *entities.AuditLog) *dtos.AuditLogDTO {
	dto := s.toDTO(auditLog)
	
	// Try to get user email (don't fail if we can't get it)
	if userID, err := uuid.Parse(auditLog.UserID); err == nil {
		if user, err := s.userRepo.GetByID(ctx, userID); err == nil {
			email := user.Email().String()
			dto.UserEmail = &email
		}
	}
	
	return dto
}