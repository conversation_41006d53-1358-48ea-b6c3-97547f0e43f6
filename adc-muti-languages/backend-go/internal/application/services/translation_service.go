package services

import (
	"context"
	"fmt"
	"strings"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// TranslationService handles translation-related business logic
type TranslationService struct {
	translationRepo    repositories.TranslationRepository
	translationKeyRepo repositories.TranslationKeyRepository
	localeRepo         repositories.LocaleRepository
	historyRepo        repositories.TranslationHistoryRepository
	settingsClient     SettingsClient
}

// SettingsClient interface for dependency injection
type SettingsClient interface {
	IsAutoTranslationEnabled(ctx context.Context, organizationID *uuid.UUID) bool
	GetAutoTranslationSettings(ctx context.Context, organizationID *uuid.UUID) (*AutoTranslationSettings, error)
}

// AutoTranslationSettings represents auto-translation configuration
type AutoTranslationSettings struct {
	Enabled           bool    `json:"enabled"`
	Provider          string  `json:"provider"`
	QualityThreshold  float64 `json:"quality_threshold"`
	MaxBatchSize      int     `json:"max_batch_size"`
	CacheExpiryHours  int     `json:"cache_expiry_hours"`
	FallbackStrategy  string  `json:"fallback_strategy"`
}

// NewTranslationService creates a new translation service
func NewTranslationService(
	translationRepo repositories.TranslationRepository,
	translationKeyRepo repositories.TranslationKeyRepository,
	localeRepo repositories.LocaleRepository,
	historyRepo repositories.TranslationHistoryRepository,
	settingsClient SettingsClient,
) *TranslationService {
	return &TranslationService{
		translationRepo:    translationRepo,
		translationKeyRepo: translationKeyRepo,
		localeRepo:         localeRepo,
		historyRepo:        historyRepo,
		settingsClient:     settingsClient,
	}
}

// Translation Key Methods

// CreateTranslationKey creates a new translation key
func (s *TranslationService) CreateTranslationKey(ctx context.Context, req *dtos.CreateTranslationKeyRequest, userID uuid.UUID) (*dtos.TranslationKeyResponse, error) {
	// Check if key name already exists in project
	exists, err := s.translationKeyRepo.KeyNameExistsInProject(ctx, req.ProjectID, req.KeyName)
	if err != nil {
		return nil, fmt.Errorf("failed to check key existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("translation key '%s' already exists in this project", req.KeyName)
	}

	// Create translation key entity
	key := entities.NewTranslationKey(req.KeyName, &req.ProjectID, &userID)
	key.UpdateInfo(req.Description, req.Context, req.IsPlural, req.MaxLength, req.ScreenshotURL)
	
	if req.ResourceID != nil {
		key.SetResource(*req.ResourceID)
	}

	// Save to repository
	if err := s.translationKeyRepo.Create(ctx, key); err != nil {
		return nil, fmt.Errorf("failed to create translation key: %w", err)
	}

	return s.translationKeyToResponse(key), nil
}

// GetTranslationKey retrieves a translation key by ID
func (s *TranslationService) GetTranslationKey(ctx context.Context, id uuid.UUID) (*dtos.TranslationKeyResponse, error) {
	key, err := s.translationKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation key: %w", err)
	}
	if key == nil {
		return nil, fmt.Errorf("translation key not found")
	}

	return s.translationKeyToResponse(key), nil
}

// UpdateTranslationKey updates a translation key
func (s *TranslationService) UpdateTranslationKey(ctx context.Context, id uuid.UUID, req *dtos.UpdateTranslationKeyRequest, userID uuid.UUID) (*dtos.TranslationKeyResponse, error) {
	// Get existing key
	key, err := s.translationKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation key: %w", err)
	}
	if key == nil {
		return nil, fmt.Errorf("translation key not found")
	}

	// Check key name uniqueness if changing
	if req.KeyName != nil && *req.KeyName != key.KeyName {
		exists, err := s.translationKeyRepo.KeyNameExistsInProjectExcluding(ctx, *key.ProjectID, *req.KeyName, id)
		if err != nil {
			return nil, fmt.Errorf("failed to check key existence: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("translation key '%s' already exists in this project", *req.KeyName)
		}
	}

	// Update key
	key.UpdateInfo(req.Description, req.Context, req.IsPlural, req.MaxLength, req.ScreenshotURL)
	if req.KeyName != nil {
		key.KeyName = *req.KeyName
		key.Touch()
	}

	// Save changes
	if err := s.translationKeyRepo.Update(ctx, key); err != nil {
		return nil, fmt.Errorf("failed to update translation key: %w", err)
	}

	return s.translationKeyToResponse(key), nil
}

// DeleteTranslationKey deletes a translation key
func (s *TranslationService) DeleteTranslationKey(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// Get existing key
	key, err := s.translationKeyRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get translation key: %w", err)
	}
	if key == nil {
		return fmt.Errorf("translation key not found")
	}

	// Delete the key (this should cascade to translations)
	if err := s.translationKeyRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete translation key: %w", err)
	}

	return nil
}

// ListTranslationKeys lists translation keys with pagination and filters
func (s *TranslationService) ListTranslationKeys(ctx context.Context, filter dtos.TranslationKeyFilter, pagination dtos.PaginationRequest) (*dtos.TranslationKeyListResponse, error) {
	repoFilter := repositories.TranslationKeyFilters{
		ProjectID:  filter.ProjectID,
		ResourceID: filter.ResourceID,
		KeyName:    filter.KeyName,
		IsPlural:   filter.IsPlural,
		CreatedBy:  filter.CreatedBy,
		HasContext: filter.HasContext,
	}

	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	var keys []*entities.TranslationKey
	var total int64
	var err error

	if filter.Search != "" {
		keys, total, err = s.translationKeyRepo.SearchKeys(ctx, *filter.ProjectID, filter.Search, repoPagination)
	} else {
		keys, total, err = s.translationKeyRepo.List(ctx, repoFilter, repoPagination)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to list translation keys: %w", err)
	}

	responses := make([]dtos.TranslationKeyResponse, len(keys))
	for i, key := range keys {
		responses[i] = *s.translationKeyToResponse(key)
	}

	totalPages := int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
	return &dtos.TranslationKeyListResponse{
		Keys: responses,
		Pagination: dtos.PaginationResult{
			Total:       total,
			TotalPages:  int64(totalPages),
			CurrentPage: pagination.Page,
			PageSize:    pagination.PageSize,
			HasNext:     pagination.Page < totalPages,
			HasPrev:     pagination.Page > 1,
		},
	}, nil
}

// Translation Methods

// CreateTranslation creates a new translation
func (s *TranslationService) CreateTranslation(ctx context.Context, req *dtos.CreateTranslationRequest, userID uuid.UUID) (*dtos.TranslationResponse, error) {
	// Check if translation already exists
	exists, err := s.translationRepo.TranslationExists(ctx, req.KeyID, req.LocaleID)
	if err != nil {
		return nil, fmt.Errorf("failed to check translation existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("translation already exists for this key and locale")
	}

	// Validate key exists
	key, err := s.translationKeyRepo.GetByID(ctx, req.KeyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation key: %w", err)
	}
	if key == nil {
		return nil, fmt.Errorf("translation key not found")
	}

	// Validate locale exists
	locale, err := s.localeRepo.GetByID(ctx, req.LocaleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	// Create translation entity
	translation := entities.NewTranslation(req.KeyID, req.LocaleID, req.Content, &userID)
	
	if req.IsFuzzy != nil && *req.IsFuzzy {
		translation.MarkAsFuzzy()
	}

	// Validate content against key constraints
	if err := translation.ValidateContent(key.MaxLength); err != nil {
		return nil, fmt.Errorf("invalid translation content: %w", err)
	}

	// Save to repository
	if err := s.translationRepo.Create(ctx, translation); err != nil {
		return nil, fmt.Errorf("failed to create translation: %w", err)
	}

	// Create history entry
	history := entities.NewTranslationHistory(translation.ID, translation.Content, "created", &userID)
	_ = s.historyRepo.Create(ctx, history) // Don't fail if history creation fails

	return s.translationToResponse(translation), nil
}

// GetTranslation retrieves a translation by ID
func (s *TranslationService) GetTranslation(ctx context.Context, id uuid.UUID) (*dtos.TranslationResponse, error) {
	translation, err := s.translationRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation: %w", err)
	}
	if translation == nil {
		return nil, fmt.Errorf("translation not found")
	}

	return s.translationToResponse(translation), nil
}

// GetTranslationByKeyAndLocale retrieves a translation by key and locale
func (s *TranslationService) GetTranslationByKeyAndLocale(ctx context.Context, keyID, localeID uuid.UUID) (*dtos.TranslationResponse, error) {
	translation, err := s.translationRepo.GetByKeyAndLocale(ctx, keyID, localeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation: %w", err)
	}
	if translation == nil {
		return nil, fmt.Errorf("translation not found")
	}

	return s.translationToResponse(translation), nil
}

// UpdateTranslation updates a translation
func (s *TranslationService) UpdateTranslation(ctx context.Context, id uuid.UUID, req *dtos.UpdateTranslationRequest, userID uuid.UUID) (*dtos.TranslationResponse, error) {
	// Get existing translation
	translation, err := s.translationRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation: %w", err)
	}
	if translation == nil {
		return nil, fmt.Errorf("translation not found")
	}

	// Get translation key for validation
	key, err := s.translationKeyRepo.GetByID(ctx, translation.KeyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation key: %w", err)
	}

	// Store old content for history
	oldContent := translation.Content

	// Update content
	translation.UpdateContent(req.Content)

	// Validate content
	if err := translation.ValidateContent(key.MaxLength); err != nil {
		return nil, fmt.Errorf("invalid translation content: %w", err)
	}

	// Save changes
	if err := s.translationRepo.Update(ctx, translation); err != nil {
		return nil, fmt.Errorf("failed to update translation: %w", err)
	}

	// Create history entry
	if oldContent != req.Content {
		history := entities.NewTranslationHistory(translation.ID, req.Content, "updated", &userID)
		_ = s.historyRepo.Create(ctx, history)
	}

	return s.translationToResponse(translation), nil
}

// ReviewTranslation reviews a translation (approve/reject)
func (s *TranslationService) ReviewTranslation(ctx context.Context, id uuid.UUID, req *dtos.ReviewTranslationRequest, reviewerID uuid.UUID) (*dtos.TranslationResponse, error) {
	// Get existing translation
	translation, err := s.translationRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation: %w", err)
	}
	if translation == nil {
		return nil, fmt.Errorf("translation not found")
	}

	// Review translation
	if req.Approve {
		translation.Approve(reviewerID)
	} else {
		translation.Reject(reviewerID)
	}

	// Save changes
	if err := s.translationRepo.Update(ctx, translation); err != nil {
		return nil, fmt.Errorf("failed to update translation: %w", err)
	}

	// Create history entry
	action := "approved"
	if !req.Approve {
		action = "rejected"
	}
	history := entities.NewTranslationHistory(translation.ID, translation.Content, action, &reviewerID)
	_ = s.historyRepo.Create(ctx, history)

	return s.translationToResponse(translation), nil
}

// DeleteTranslation deletes a translation
func (s *TranslationService) DeleteTranslation(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	// Get existing translation
	translation, err := s.translationRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get translation: %w", err)
	}
	if translation == nil {
		return fmt.Errorf("translation not found")
	}

	// Create history entry before deletion
	history := entities.NewTranslationHistory(translation.ID, translation.Content, "deleted", &userID)
	_ = s.historyRepo.Create(ctx, history)

	// Delete translation
	if err := s.translationRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete translation: %w", err)
	}

	return nil
}

// ListTranslations lists translations with pagination and filters
// Enhanced version that auto-creates missing translations based on settings
func (s *TranslationService) ListTranslations(ctx context.Context, filter dtos.TranslationFilter, pagination dtos.PaginationRequest) (*dtos.TranslationListResponse, error) {
	repoFilter := repositories.TranslationFilters{
		KeyID:      filter.KeyID,
		LocaleID:   filter.LocaleID,
		ProjectID:  filter.ProjectID,
		Namespace:  filter.Namespace,
		IsFuzzy:    filter.IsFuzzy,
		IsReviewed: filter.IsReviewed,
		ReviewedBy: filter.ReviewedBy,
		CreatedBy:  filter.CreatedBy,
	}

	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	// Get existing translations first
	var translations []*entities.Translation
	var total int64
	var err error

	if filter.Search != "" {
		translations, total, err = s.translationRepo.SearchTranslations(ctx, *filter.ProjectID, filter.Search, repoPagination)
	} else {
		translations, total, err = s.translationRepo.List(ctx, repoFilter, repoPagination)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to list translations: %w", err)
	}

	// Auto-create missing translations if specific locale/project is requested
	var autoGeneratedCount int
	if filter.ProjectID != nil && filter.LocaleID != nil {
		autoGeneratedCount, err = s.autoCreateMissingTranslations(ctx, *filter.ProjectID, *filter.LocaleID, filter.Namespace)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Warning: Failed to auto-create missing translations: %v\n", err)
		}

		// If we auto-created translations, re-fetch to include them
		if autoGeneratedCount > 0 {
			if filter.Search != "" {
				translations, total, err = s.translationRepo.SearchTranslations(ctx, *filter.ProjectID, filter.Search, repoPagination)
			} else {
				translations, total, err = s.translationRepo.List(ctx, repoFilter, repoPagination)
			}
			if err != nil {
				return nil, fmt.Errorf("failed to re-fetch translations after auto-creation: %w", err)
			}
		}
	}

	responses := make([]dtos.TranslationResponse, len(translations))
	for i, translation := range translations {
		responses[i] = *s.translationToResponse(translation)
	}

	totalPages := int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
	return &dtos.TranslationListResponse{
		Translations: responses,
		Pagination: dtos.PaginationResult{
			Total:       total,
			TotalPages:  int64(totalPages),
			CurrentPage: pagination.Page,
			PageSize:    pagination.PageSize,
			HasNext:     pagination.Page < totalPages,
			HasPrev:     pagination.Page > 1,
		},
		Meta: map[string]interface{}{
			"auto_generated_count": autoGeneratedCount,
		},
	}, nil
}

// GetTranslationProgress gets translation progress for a project-locale combination
func (s *TranslationService) GetTranslationProgress(ctx context.Context, projectID, localeID uuid.UUID) (*dtos.TranslationProgressResponse, error) {
	progress, err := s.translationRepo.GetTranslationProgress(ctx, projectID, localeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get translation progress: %w", err)
	}

	return &dtos.TranslationProgressResponse{
		ProjectID:      projectID,
		LocaleID:       localeID,
		TotalKeys:      progress.TotalKeys,
		TranslatedKeys: progress.TranslatedKeys,
		ReviewedKeys:   progress.ReviewedKeys,
		FuzzyKeys:      progress.FuzzyKeys,
		CompletionRate: progress.CompletionRate,
		ReviewRate:     progress.ReviewRate,
	}, nil
}

// Helper methods

func (s *TranslationService) translationKeyToResponse(key *entities.TranslationKey) *dtos.TranslationKeyResponse {
	return &dtos.TranslationKeyResponse{
		ID:            key.ID,
		ResourceID:    key.ResourceID,
		ProjectID:     key.ProjectID,
		KeyName:       key.KeyName,
		Description:   key.Description,
		Context:       key.Context,
		IsPlural:      key.IsPlural,
		MaxLength:     key.MaxLength,
		ScreenshotURL: key.ScreenshotURL,
		CreatedBy:     key.CreatedBy,
		CreatedAt:     key.CreatedAt,
		UpdatedAt:     key.UpdatedAt,
	}
}

func (s *TranslationService) translationToResponse(translation *entities.Translation) *dtos.TranslationResponse {
	response := &dtos.TranslationResponse{
		ID:         translation.ID,
		KeyID:      translation.KeyID,
		LocaleID:   translation.LocaleID,
		Content:    translation.Content,
		IsFuzzy:    translation.IsFuzzy,
		IsReviewed: translation.IsReviewed,
		ReviewedBy: translation.ReviewedBy,
		ReviewedAt: translation.ReviewedAt,
		CreatedBy:  translation.CreatedBy,
		CreatedAt:  translation.CreatedAt,
		UpdatedAt:  translation.UpdatedAt,
	}

	// Try to get the key name for enriched responses
	if key, err := s.translationKeyRepo.GetByID(context.Background(), translation.KeyID); err == nil && key != nil {
		response.KeyName = &key.KeyName
		fmt.Printf("✅ Enriched translation %s with key_name: %s\n", translation.ID, key.KeyName)
	} else {
		fmt.Printf("❌ Failed to get key for translation %s, keyID: %s, error: %v\n", translation.ID, translation.KeyID, err)
	}

	return response
}

// autoCreateMissingTranslations identifies missing translation keys for a project/locale combination
// and creates them either with AI translation (if enabled) or fallback content (if disabled)
// If namespace is provided, only keys matching that namespace will be processed
func (s *TranslationService) autoCreateMissingTranslations(ctx context.Context, projectID, localeID uuid.UUID, namespace *string) (int, error) {
	// Check auto-translation settings first
	isEnabled := s.settingsClient.IsAutoTranslationEnabled(ctx, nil) // Using nil for global settings for now
	settings, err := s.settingsClient.GetAutoTranslationSettings(ctx, nil)
	if err != nil {
		// Log error but continue with defaults
		fmt.Printf("Warning: Failed to get auto-translation settings, using defaults: %v\n", err)
		settings = &AutoTranslationSettings{
			Enabled:           false,
			Provider:          "openai",
			QualityThreshold:  0.8,
			MaxBatchSize:      10,
			CacheExpiryHours:  24,
			FallbackStrategy:  "source_text",
		}
	}

	// Get all translation keys for the project (using large pagination to get all keys)
	keysPagination := repositories.Pagination{
		Page:     1,
		PageSize: 1000, // Large page size to get all keys
		OrderBy:  "created_at",
		Order:    "ASC",
	}
	allKeys, _, err := s.translationKeyRepo.GetByProjectID(ctx, projectID, keysPagination)
	if err != nil {
		return 0, fmt.Errorf("failed to get translation keys for project: %w", err)
	}

	if len(allKeys) == 0 {
		return 0, nil // No keys to translate
	}

	// Get existing translations for this project/locale combination (using large pagination to get all translations)
	translationsPagination := repositories.Pagination{
		Page:     1,
		PageSize: 1000, // Large page size to get all translations
		OrderBy:  "created_at",
		Order:    "ASC",
	}
	existingTranslations, _, err := s.translationRepo.GetByProjectAndLocale(ctx, projectID, localeID, translationsPagination)
	if err != nil {
		return 0, fmt.Errorf("failed to get existing translations: %w", err)
	}

	// Create a map of existing translations by key ID for quick lookup
	existingKeyIDs := make(map[uuid.UUID]bool)
	for _, translation := range existingTranslations {
		existingKeyIDs[translation.KeyID] = true
	}

	// Find missing translation keys (with optional namespace filtering)
	var missingKeys []*entities.TranslationKey
	for _, key := range allKeys {
		// Skip if translation already exists
		if existingKeyIDs[key.ID] {
			continue
		}
		
		// Apply namespace filtering if specified
		if namespace != nil && *namespace != "" {
			namespacePrefix := *namespace + "."
			if !strings.HasPrefix(key.KeyName, namespacePrefix) {
				continue // Skip keys not in the requested namespace
			}
		}
		
		missingKeys = append(missingKeys, key)
	}

	if len(missingKeys) == 0 {
		return 0, nil // No missing translations
	}

	// Create translations for missing keys
	var createdCount int
	batchSize := settings.MaxBatchSize
	if batchSize <= 0 {
		batchSize = 10 // Default batch size
	}

	// Process in batches to avoid overwhelming the system
	for i := 0; i < len(missingKeys); i += batchSize {
		end := i + batchSize
		if end > len(missingKeys) {
			end = len(missingKeys)
		}
		
		batch := missingKeys[i:end]
		batchCreated, err := s.createTranslationBatch(ctx, batch, localeID, isEnabled, settings)
		if err != nil {
			// Log error but continue with next batch
			fmt.Printf("Warning: Failed to create translation batch: %v\n", err)
			continue
		}
		createdCount += batchCreated
	}

	return createdCount, nil
}

// createTranslationBatch creates a batch of translations for missing keys
func (s *TranslationService) createTranslationBatch(ctx context.Context, keys []*entities.TranslationKey, localeID uuid.UUID, autoTranslateEnabled bool, settings *AutoTranslationSettings) (int, error) {
	var createdCount int

	// Get locale info for translation context
	locale, err := s.localeRepo.GetByID(ctx, localeID)
	if err != nil {
		return 0, fmt.Errorf("failed to get locale: %w", err)
	}

	for _, key := range keys {
		var content string
		var isFuzzy bool = false

		if autoTranslateEnabled {
			// TODO: Implement AI translation integration
			// For now, use a placeholder that indicates auto-translation is intended
			content = fmt.Sprintf("[AUTO-TRANSLATE] %s", key.KeyName)
			isFuzzy = true // Mark as fuzzy since it's auto-generated
		} else {
			// Use fallback strategy
			content = s.getFallbackContent(key, locale, settings.FallbackStrategy)
			isFuzzy = true // Mark as fuzzy since it's a fallback
		}

		// Create the translation entity
		// Using nil for userID since this is system-generated
		translation := entities.NewTranslation(key.ID, localeID, content, nil)
		
		if isFuzzy {
			translation.MarkAsFuzzy()
		}

		// Validate content against key constraints
		if err := translation.ValidateContent(key.MaxLength); err != nil {
			// If validation fails, use a shorter fallback
			content = s.getShortFallbackContent(key, locale)
			translation.UpdateContent(content)
			translation.MarkAsFuzzy()
		}

		// Save to repository
		if err := s.translationRepo.Create(ctx, translation); err != nil {
			fmt.Printf("Warning: Failed to create translation for key %s: %v\n", key.KeyName, err)
			continue
		}

		// Create history entry for auto-generation
		historyAction := "auto_created"
		if autoTranslateEnabled {
			historyAction = "auto_translated"
		}
		history := entities.NewTranslationHistory(translation.ID, translation.Content, historyAction, nil)
		_ = s.historyRepo.Create(ctx, history) // Don't fail if history creation fails

		createdCount++
	}

	return createdCount, nil
}

// getFallbackContent generates fallback content based on the configured strategy
func (s *TranslationService) getFallbackContent(key *entities.TranslationKey, locale *entities.Locale, fallbackStrategy string) string {
	switch fallbackStrategy {
	case "key":
		return key.KeyName
	case "placeholder":
		return fmt.Sprintf("[%s: %s]", locale.Code, key.KeyName)
	case "source_text":
		fallthrough
	default:
		// Return the key name as source text
		return key.KeyName
	}
}

// getShortFallbackContent generates a very short fallback for length-constrained keys
func (s *TranslationService) getShortFallbackContent(key *entities.TranslationKey, locale *entities.Locale) string {
	if key.MaxLength != nil && *key.MaxLength > 0 {
		maxLen := *key.MaxLength
		keyNameLen := len(key.KeyName)
		if maxLen < 10 {
			if keyNameLen <= maxLen {
				return key.KeyName
			}
			return key.KeyName[:maxLen]
		}
		if keyNameLen <= maxLen-2 {
			return fmt.Sprintf("[%s]", key.KeyName)
		}
		return fmt.Sprintf("[%s]", key.KeyName[:maxLen-2])
	}
	return key.KeyName
}