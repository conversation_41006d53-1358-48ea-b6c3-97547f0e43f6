package services

import (
	"context"
	"fmt"
	"time"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// APIKeyService handles API key related operations
type APIKeyService struct {
	apiKeyRepo repositories.APIKeyRepository
	userRepo   repositories.UserRepository
	orgRepo    repositories.OrganizationRepository
}

// NewAPIKeyService creates a new API key service
func NewAPIKeyService(
	apiKeyRepo repositories.APIKeyRepository,
	userRepo repositories.UserRepository,
	orgRepo repositories.OrganizationRepository,
) *APIKeyService {
	return &APIKeyService{
		apiKeyRepo: apiKeyRepo,
		userRepo:   userRepo,
		orgRepo:    orgRepo,
	}
}

// CreateAPIKey creates a new API key
func (s *APIKeyService) CreateAPIKey(ctx context.Context, userID uuid.UUID, req *dtos.CreateAPIKeyRequest) (*dtos.CreateAPIKeyResponse, error) {
	// Validate user exists
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Validate organization exists and user has access
	_, err = s.orgRepo.GetByID(ctx, req.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %w", err)
	}

	// TODO: Check if user has permission to create API keys for this organization

	// Check API key limits for the organization
	count, err := s.apiKeyRepo.CountByOrganization(ctx, req.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("failed to check API key count: %w", err)
	}

	maxAPIKeys := int64(50) // TODO: Get from organization subscription plan
	if count >= maxAPIKeys {
		return nil, fmt.Errorf("organization has reached the maximum number of API keys (%d)", maxAPIKeys)
	}

	// Set default environment if not provided
	environment := req.Environment
	if environment == "" {
		environment = "production"
	}

	// Create API key entity
	apiKey, plainKey, err := entities.NewAPIKey(req.Name, userID, req.OrganizationID, req.Scopes, environment)
	if err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	// Set expiration if provided
	if req.ExpiresAt != nil {
		if err := apiKey.SetExpiration(*req.ExpiresAt); err != nil {
			return nil, fmt.Errorf("invalid expiration date: %w", err)
		}
	}

	// Set custom rate limits if provided
	if req.RateLimit != nil {
		apiKey.RateLimit = &entities.RateLimit{
			RequestsPerMinute: req.RateLimit.RequestsPerMinute,
			RequestsPerHour:   req.RateLimit.RequestsPerHour,
			RequestsPerDay:    req.RateLimit.RequestsPerDay,
		}
	}

	// Validate the API key
	if err := apiKey.Validate(); err != nil {
		return nil, fmt.Errorf("invalid API key: %w", err)
	}

	// Save to repository
	if err := s.apiKeyRepo.Create(ctx, apiKey); err != nil {
		return nil, fmt.Errorf("failed to save API key: %w", err)
	}

	response := &dtos.CreateAPIKeyResponse{
		APIKey:   s.entityToDTO(apiKey),
		PlainKey: plainKey,
		Warning:  "Store this API key securely. It will not be shown again.",
	}

	return response, nil
}

// GetAPIKey retrieves an API key by ID
func (s *APIKeyService) GetAPIKey(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*dtos.APIKeyResponse, error) {
	apiKey, err := s.apiKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("API key not found: %w", err)
	}

	// TODO: Check if user has permission to view this API key
	if apiKey.UserID != userID {
		// For now, only allow users to see their own keys
		// TODO: Add organization-level permissions
		return nil, fmt.Errorf("access denied")
	}

	return s.entityToDTO(apiKey), nil
}

// ListAPIKeys retrieves API keys for a user or organization
func (s *APIKeyService) ListAPIKeys(ctx context.Context, userID uuid.UUID, organizationID *uuid.UUID, filter dtos.APIKeyFilter, pagination dtos.PaginationRequest) (*dtos.APIKeyListResponse, error) {
	repoFilter := repositories.APIKeyFilter{
		IsActive:      filter.IsActive,
		Environment:   filter.Environment,
		HasScope:      filter.HasScope,
		CreatedAfter:  filter.CreatedAfter,
		CreatedBefore: filter.CreatedBefore,
		ExpiresAfter:  filter.ExpiresAfter,
		ExpiresBefore: filter.ExpiresBefore,
		Search:        filter.Search,
	}

	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	var apiKeys []*entities.APIKey
	var paginationResult *repositories.PaginationResult
	var err error

	if organizationID != nil {
		// TODO: Check if user has permission to list organization API keys
		apiKeys, paginationResult, err = s.apiKeyRepo.ListByOrganization(ctx, *organizationID, repoFilter, repoPagination)
	} else {
		apiKeys, paginationResult, err = s.apiKeyRepo.ListByUser(ctx, userID, repoFilter, repoPagination)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to list API keys: %w", err)
	}

	// Convert to DTOs
	dtoAPIKeys := make([]*dtos.APIKeyResponse, len(apiKeys))
	for i, apiKey := range apiKeys {
		dtoAPIKeys[i] = s.entityToDTO(apiKey)
	}

	return &dtos.APIKeyListResponse{
		APIKeys: dtoAPIKeys,
		Pagination: &dtos.PaginationResult{
			Total:       paginationResult.Total,
			TotalPages:  int64(paginationResult.TotalPages),
			CurrentPage: paginationResult.CurrentPage,
			PageSize:    paginationResult.PageSize,
			HasNext:     paginationResult.HasNext,
			HasPrev:     paginationResult.HasPrev,
		},
	}, nil
}

// UpdateAPIKey updates an existing API key
func (s *APIKeyService) UpdateAPIKey(ctx context.Context, id uuid.UUID, userID uuid.UUID, req *dtos.UpdateAPIKeyRequest) (*dtos.APIKeyResponse, error) {
	apiKey, err := s.apiKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("API key not found: %w", err)
	}

	// TODO: Check if user has permission to update this API key
	if apiKey.UserID != userID {
		return nil, fmt.Errorf("access denied")
	}

	// Update fields if provided
	if req.Name != nil {
		apiKey.Name = *req.Name
	}

	if req.Scopes != nil {
		if err := apiKey.UpdateScopes(req.Scopes); err != nil {
			return nil, fmt.Errorf("failed to update scopes: %w", err)
		}
	}

	if req.ExpiresAt != nil {
		if err := apiKey.SetExpiration(*req.ExpiresAt); err != nil {
			return nil, fmt.Errorf("failed to set expiration: %w", err)
		}
	}

	if req.RateLimit != nil {
		apiKey.RateLimit = &entities.RateLimit{
			RequestsPerMinute: req.RateLimit.RequestsPerMinute,
			RequestsPerHour:   req.RateLimit.RequestsPerHour,
			RequestsPerDay:    req.RateLimit.RequestsPerDay,
		}
	}

	// Validate the updated API key
	if err := apiKey.Validate(); err != nil {
		return nil, fmt.Errorf("invalid API key update: %w", err)
	}

	// Save changes
	if err := s.apiKeyRepo.Update(ctx, apiKey); err != nil {
		return nil, fmt.Errorf("failed to update API key: %w", err)
	}

	return s.entityToDTO(apiKey), nil
}

// DeleteAPIKey deletes (deactivates) an API key
func (s *APIKeyService) DeleteAPIKey(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	apiKey, err := s.apiKeyRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("API key not found: %w", err)
	}

	// TODO: Check if user has permission to delete this API key
	if apiKey.UserID != userID {
		return fmt.Errorf("access denied")
	}

	if err := s.apiKeyRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete API key: %w", err)
	}

	return nil
}

// RotateAPIKey generates a new key for an existing API key
func (s *APIKeyService) RotateAPIKey(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*dtos.RotateAPIKeyResponse, error) {
	apiKey, err := s.apiKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("API key not found: %w", err)
	}

	// TODO: Check if user has permission to rotate this API key
	if apiKey.UserID != userID {
		return nil, fmt.Errorf("access denied")
	}

	// Generate a new key
	_, plainKey, err := entities.NewAPIKey(apiKey.Name, apiKey.UserID, apiKey.OrganizationID, apiKey.Scopes, apiKey.Environment)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new API key: %w", err)
	}

	// Update the key hash
	apiKey.KeyHash = entities.HashKey(plainKey)
	apiKey.Touch()

	// Save changes
	if err := s.apiKeyRepo.Update(ctx, apiKey); err != nil {
		return nil, fmt.Errorf("failed to update API key: %w", err)
	}

	return &dtos.RotateAPIKeyResponse{
		APIKey:   s.entityToDTO(apiKey),
		PlainKey: plainKey,
		Warning:  "Store this new API key securely. The old key is now invalid.",
	}, nil
}

// ValidateAPIKey validates an API key for API requests
func (s *APIKeyService) ValidateAPIKey(ctx context.Context, req *dtos.ValidateAPIKeyRequest) (*dtos.ValidateAPIKeyResponse, error) {
	keyHash := entities.HashKey(req.Key)
	
	apiKey, err := s.apiKeyRepo.GetByKeyHash(ctx, keyHash)
	if err != nil {
		return &dtos.ValidateAPIKeyResponse{
			Valid: false,
			Error: "Invalid API key",
		}, nil
	}

	// Check if API key is active
	if !apiKey.IsActive {
		return &dtos.ValidateAPIKeyResponse{
			Valid: false,
			Error: "API key is inactive",
		}, nil
	}

	// Check if API key is expired
	if apiKey.IsExpired() {
		return &dtos.ValidateAPIKeyResponse{
			Valid: false,
			Error: "API key has expired",
		}, nil
	}

	// Check scope if provided
	if req.Scope != "" && !apiKey.HasScope(req.Scope) {
		return &dtos.ValidateAPIKeyResponse{
			Valid: false,
			Error: "API key does not have required scope",
		}, nil
	}

	// Check rate limits
	rateLimitUsage, err := s.apiKeyRepo.GetRateLimitUsage(ctx, apiKey.ID)
	if err != nil {
		// Log error but don't fail validation
		rateLimitUsage = &entities.RateLimitUsage{}
	}

	if !apiKey.CanMakeRequest(rateLimitUsage) {
		return &dtos.ValidateAPIKeyResponse{
			Valid: false,
			Error: "Rate limit exceeded",
			RateLimitUsage: &dtos.RateLimitUsageResponse{
				RequestsThisMinute: rateLimitUsage.RequestsThisMinute,
				RequestsThisHour:   rateLimitUsage.RequestsThisHour,
				RequestsThisDay:    rateLimitUsage.RequestsThisDay,
				LimitReached:       true,
			},
		}, nil
	}

	// Record the API usage
	usage := entities.NewAPIUsage(
		apiKey.ID,
		req.Endpoint,
		req.Method,
		200, // Assuming successful validation
		0,   // No response time for validation
		"",  // IP address would come from request context
		"",  // User agent would come from request context
	)

	// Record usage (async, don't fail validation if this fails)
	go func() {
		if err := s.apiKeyRepo.RecordUsage(context.Background(), usage); err != nil {
			// Log error
		}
	}()

	return &dtos.ValidateAPIKeyResponse{
		Valid:  true,
		APIKey: s.entityToDTO(apiKey),
		RateLimitUsage: &dtos.RateLimitUsageResponse{
			RequestsThisMinute: rateLimitUsage.RequestsThisMinute,
			RequestsThisHour:   rateLimitUsage.RequestsThisHour,
			RequestsThisDay:    rateLimitUsage.RequestsThisDay,
			LimitReached:       false,
		},
	}, nil
}

// GetUsageStats retrieves usage statistics for an API key
func (s *APIKeyService) GetUsageStats(ctx context.Context, id uuid.UUID, userID uuid.UUID, period string) (*dtos.APIKeyUsageStatsResponse, error) {
	apiKey, err := s.apiKeyRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("API key not found: %w", err)
	}

	// TODO: Check if user has permission to view stats for this API key
	if apiKey.UserID != userID {
		return nil, fmt.Errorf("access denied")
	}

	stats, err := s.apiKeyRepo.GetUsageStats(ctx, id, period)
	if err != nil {
		return nil, fmt.Errorf("failed to get usage stats: %w", err)
	}

	return &dtos.APIKeyUsageStatsResponse{
		TotalRequests:       stats.TotalRequests,
		SuccessfulRequests:  stats.SuccessfulRequests,
		FailedRequests:      stats.FailedRequests,
		SuccessRate:         stats.SuccessRate,
		AverageResponseTime: stats.AverageResponseTime.String(),
		RequestsByEndpoint:  stats.RequestsByEndpoint,
		RequestsByStatus:    stats.RequestsByStatus,
		RequestsByDay:       stats.RequestsByDay,
		LastUsed:            stats.LastUsed,
		Period:              stats.Period,
	}, nil
}

// GetExpiringSoon retrieves API keys expiring soon
func (s *APIKeyService) GetExpiringSoon(ctx context.Context, userID uuid.UUID, within time.Duration) ([]*dtos.APIKeyResponse, error) {
	// TODO: Check if user has permission to see organization API keys
	apiKeys, err := s.apiKeyRepo.GetExpiringSoon(ctx, within)
	if err != nil {
		return nil, fmt.Errorf("failed to get expiring API keys: %w", err)
	}

	// Filter by user access (for now, only show user's own keys)
	var userAPIKeys []*entities.APIKey
	for _, apiKey := range apiKeys {
		if apiKey.UserID == userID {
			userAPIKeys = append(userAPIKeys, apiKey)
		}
	}

	// Convert to DTOs
	dtoAPIKeys := make([]*dtos.APIKeyResponse, len(userAPIKeys))
	for i, apiKey := range userAPIKeys {
		dtoAPIKeys[i] = s.entityToDTO(apiKey)
	}

	return dtoAPIKeys, nil
}

// Helper methods

func (s *APIKeyService) entityToDTO(entity *entities.APIKey) *dtos.APIKeyResponse {
	dto := &dtos.APIKeyResponse{
		ID:             entity.ID,
		Name:           entity.Name,
		Prefix:         entity.Prefix,
		UserID:         entity.UserID,
		OrganizationID: entity.OrganizationID,
		Scopes:         entity.Scopes,
		IsActive:       entity.IsActive,
		ExpiresAt:      entity.ExpiresAt,
		LastUsedAt:     entity.LastUsedAt,
		UsageCount:     entity.UsageCount,
		Environment:    entity.Environment,
		CreatedAt:      entity.CreatedAt,
		UpdatedAt:      entity.UpdatedAt,
	}

	if entity.RateLimit != nil {
		dto.RateLimit = &dtos.APIKeyRateLimitResponse{
			RequestsPerMinute: entity.RateLimit.RequestsPerMinute,
			RequestsPerHour:   entity.RateLimit.RequestsPerHour,
			RequestsPerDay:    entity.RateLimit.RequestsPerDay,
		}
	}

	return dto
}