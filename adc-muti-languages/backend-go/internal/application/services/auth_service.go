package services

import (
	"context"
	"fmt"
	"time"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"adc-multi-languages/internal/domain/valueobjects"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// AuthService handles authentication-related business logic
type AuthService struct {
	userRepo                  repositories.UserRepository
	sessionRepo               repositories.SessionRepository
	refreshTokenRepo          repositories.RefreshTokenRepository
	passwordResetTokenRepo    repositories.PasswordResetTokenRepository
	emailVerificationTokenRepo repositories.EmailVerificationTokenRepository
	loginAttemptRepo          repositories.LoginAttemptRepository
	authAuditRepo             repositories.AuthAuditRepository
	jwtService                JWTService
	emailService              EmailService
	config                    AuthConfig
}

// AuthConfig represents authentication configuration
type AuthConfig struct {
	JWTSecret                string
	JWTExpirationHours       int
	JWTRefreshExpirationDays int
	PasswordMinLength        int
	RequireEmailVerification bool
	AllowSelfRegistration    bool
	SessionTimeoutHours      int
	MaxLoginAttempts         int
	LockoutDurationMinutes   int
	TokenExpirationMinutes   int
}

// JWTService interface for JWT operations
type JWTService interface {
	GenerateAccessToken(userID, email string, expiresIn time.Duration) (string, error)
	GenerateRefreshToken(userID string, expiresIn time.Duration) (string, error)
	ValidateAccessToken(token string) (*dtos.JWTClaims, error)
	ValidateRefreshToken(token string) (*dtos.JWTClaims, error)
}

// EmailService interface for email operations
type EmailService interface {
	SendPasswordResetEmail(email, token string) error
	SendEmailVerificationEmail(email, token string) error
	SendWelcomeEmail(email, name string) error
}

// NewAuthService creates a new authentication service
func NewAuthService(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	refreshTokenRepo repositories.RefreshTokenRepository,
	passwordResetTokenRepo repositories.PasswordResetTokenRepository,
	emailVerificationTokenRepo repositories.EmailVerificationTokenRepository,
	loginAttemptRepo repositories.LoginAttemptRepository,
	authAuditRepo repositories.AuthAuditRepository,
	jwtService JWTService,
	emailService EmailService,
	config AuthConfig,
) *AuthService {
	return &AuthService{
		userRepo:                   userRepo,
		sessionRepo:                sessionRepo,
		refreshTokenRepo:           refreshTokenRepo,
		passwordResetTokenRepo:     passwordResetTokenRepo,
		emailVerificationTokenRepo: emailVerificationTokenRepo,
		loginAttemptRepo:           loginAttemptRepo,
		authAuditRepo:              authAuditRepo,
		jwtService:                 jwtService,
		emailService:               emailService,
		config:                     config,
	}
}

// SignUp handles user registration
func (s *AuthService) SignUp(ctx context.Context, req *dtos.SignUpRequest, ipAddress, userAgent string) (*dtos.AuthResponse, error) {
	// Check if self-registration is allowed
	if !s.config.AllowSelfRegistration {
		return nil, fmt.Errorf("self-registration is not allowed")
	}

	// Check if user already exists
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}
	existingUser, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}
	if existingUser != nil {
		// Log failed signup attempt
		s.logLoginAttempt(ctx, nil, req.Email, ipAddress, userAgent, false, "email already exists")
		return nil, fmt.Errorf("user with email '%s' already exists", req.Email)
	}

	// Check username if provided
	if req.Username != nil {
		existingUser, err := s.userRepo.GetByUsername(ctx, *req.Username)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing username: %w", err)
		}
		if existingUser != nil {
			s.logLoginAttempt(ctx, nil, req.Email, ipAddress, userAgent, false, "username already taken")
			return nil, fmt.Errorf("username '%s' is already taken", *req.Username)
		}
	}

	// Validate password strength
	if len(req.Password) < s.config.PasswordMinLength {
		return nil, fmt.Errorf("password must be at least %d characters long", s.config.PasswordMinLength)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user entity
	user, err := entities.NewUser(*email, string(hashedPassword))
	if err != nil {
		return nil, fmt.Errorf("failed to create user entity: %w", err)
	}
	if req.Username != nil {
		user.SetUsername(*req.Username)
	}
	if req.FirstName != nil || req.LastName != nil {
		user.UpdateProfile(req.FirstName, req.LastName, nil, nil)
	}

	// Set email verification status
	if !s.config.RequireEmailVerification {
		user.VerifyEmail()
	}

	// Save user
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Log successful signup
	userID := user.ID()
	s.logAuthAudit(ctx, &userID, req.Email, "signup", ipAddress, userAgent, true, nil, nil)
	s.logLoginAttempt(ctx, &userID, req.Email, ipAddress, userAgent, true, "signup successful")

	// Send email verification if required
	if s.config.RequireEmailVerification {
		if err := s.sendEmailVerification(ctx, user.ID(), req.Email); err != nil {
			// Don't fail signup if email sending fails, just log it
			errMsg := err.Error()
			s.logAuthAudit(ctx, &userID, req.Email, "email_verification_send_failed", ipAddress, userAgent, false, nil, &errMsg)
		}
	}

	// Send welcome email
	fullName := ""
	if req.FirstName != nil {
		fullName = *req.FirstName
		if req.LastName != nil {
			fullName += " " + *req.LastName
		}
	}
	if err := s.emailService.SendWelcomeEmail(req.Email, fullName); err != nil {
		// Don't fail signup if welcome email fails
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, req.Email, "welcome_email_send_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Generate tokens
	return s.generateAuthResponse(ctx, user, ipAddress, userAgent)
}

// SignIn handles user authentication
func (s *AuthService) SignIn(ctx context.Context, req *dtos.SignInRequest, ipAddress, userAgent string) (*dtos.AuthResponse, error) {
	// Check for rate limiting
	if err := s.checkRateLimit(ctx, req.Email, ipAddress); err != nil {
		s.logLoginAttempt(ctx, nil, req.Email, ipAddress, userAgent, false, "rate limited")
		return nil, err
	}

	// Get user by email
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}
	user, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		s.logLoginAttempt(ctx, nil, req.Email, ipAddress, userAgent, false, "user not found")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive() {
		userID := user.ID()
		s.logLoginAttempt(ctx, &userID, req.Email, ipAddress, userAgent, false, "account inactive")
		return nil, fmt.Errorf("account is inactive")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash()), []byte(req.Password)); err != nil {
		userID := user.ID()
		s.logLoginAttempt(ctx, &userID, req.Email, ipAddress, userAgent, false, "invalid password")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check email verification if required
	if s.config.RequireEmailVerification && !user.EmailVerified() {
		userID := user.ID()
		s.logLoginAttempt(ctx, &userID, req.Email, ipAddress, userAgent, false, "email not verified")
		return nil, fmt.Errorf("email address not verified")
	}

	// Update last login
	user.UpdateLastLogin()
	if err := s.userRepo.Update(ctx, user); err != nil {
		// Don't fail login if this fails, just log it
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, req.Email, "last_login_update_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Log successful login
	userID := user.ID()
	s.logLoginAttempt(ctx, &userID, req.Email, ipAddress, userAgent, true, "login successful")
	s.logAuthAudit(ctx, &userID, req.Email, "login", ipAddress, userAgent, true, nil, nil)

	// Generate tokens
	return s.generateAuthResponse(ctx, user, ipAddress, userAgent)
}

// SignOut handles user logout
func (s *AuthService) SignOut(ctx context.Context, sessionToken string, userID uuid.UUID, ipAddress, userAgent string) error {
	// Get user for logging
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Deactivate session
	session, err := s.sessionRepo.GetByToken(ctx, sessionToken)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	if session != nil {
		session.Deactivate()
		if err := s.sessionRepo.Update(ctx, session); err != nil {
			return fmt.Errorf("failed to deactivate session: %w", err)
		}
	}

	// Log logout
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "logout", ipAddress, userAgent, true, nil, nil)

	return nil
}

// RefreshToken handles token refresh
func (s *AuthService) RefreshToken(ctx context.Context, req *dtos.RefreshTokenRequest, ipAddress, userAgent string) (*dtos.RefreshTokenResponse, error) {
	// Validate refresh token
	claims, err := s.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID in token: %w", err)
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// Check if user is active
	if !user.IsActive() {
		return nil, fmt.Errorf("account is inactive")
	}

	// Generate new access token
	accessToken, err := s.jwtService.GenerateAccessToken(
		user.ID().String(),
		user.Email().Value(),
		time.Duration(s.config.JWTExpirationHours)*time.Hour,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Log token refresh
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "token_refresh", ipAddress, userAgent, true, nil, nil)

	return &dtos.RefreshTokenResponse{
		AccessToken: accessToken,
		ExpiresIn:   s.config.JWTExpirationHours * 3600, // convert to seconds
		TokenType:   "Bearer",
	}, nil
}

// RequestPasswordReset handles password reset requests
func (s *AuthService) RequestPasswordReset(ctx context.Context, req *dtos.RequestPasswordResetRequest, ipAddress, userAgent string) (*dtos.PasswordResetResponse, error) {
	// Get user by email
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}
	user, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Always return success to prevent email enumeration
	response := &dtos.PasswordResetResponse{
		Message: "If an account with that email exists, a password reset link has been sent",
		Email:   req.Email,
	}

	if user == nil {
		// Log attempt for non-existent user
		s.logAuthAudit(ctx, nil, req.Email, "password_reset_request", ipAddress, userAgent, false, nil, stringPtr("user not found"))
		return response, nil
	}

	// Check if user is active
	if !user.IsActive() {
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "password_reset_request", ipAddress, userAgent, false, nil, stringPtr("account inactive"))
		return response, nil
	}

	// Invalidate existing password reset tokens
	if err := s.passwordResetTokenRepo.InvalidateUserTokens(ctx, user.ID()); err != nil {
		return nil, fmt.Errorf("failed to invalidate existing tokens: %w", err)
	}

	// Create password reset token
	expiresAt := time.Now().Add(time.Duration(s.config.TokenExpirationMinutes) * time.Minute)
	token, err := entities.NewPasswordResetToken(user.ID(), expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create password reset token: %w", err)
	}

	if err := s.passwordResetTokenRepo.Create(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to save password reset token: %w", err)
	}

	// Send password reset email
	if err := s.emailService.SendPasswordResetEmail(user.Email().Value(), token.Token); err != nil {
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "password_reset_email_failed", ipAddress, userAgent, false, nil, &errMsg)
		return nil, fmt.Errorf("failed to send password reset email: %w", err)
	}

	// Log successful password reset request
	userID := user.ID()
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "password_reset_request", ipAddress, userAgent, true, nil, nil)

	return response, nil
}

// ConfirmPasswordReset handles password reset confirmation
func (s *AuthService) ConfirmPasswordReset(ctx context.Context, req *dtos.ConfirmPasswordResetRequest, ipAddress, userAgent string) error {
	// Get password reset token
	token, err := s.passwordResetTokenRepo.GetByToken(ctx, req.Token)
	if err != nil {
		return fmt.Errorf("failed to get password reset token: %w", err)
	}
	if token == nil {
		return fmt.Errorf("invalid or expired password reset token")
	}

	// Validate token
	if !token.IsValid() {
		return fmt.Errorf("invalid or expired password reset token")
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, token.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Validate new password
	if len(req.NewPassword) < s.config.PasswordMinLength {
		return fmt.Errorf("password must be at least %d characters long", s.config.PasswordMinLength)
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update user password
	user.ChangePassword(string(hashedPassword))
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user password: %w", err)
	}

	// Mark token as used
	token.MarkAsUsed()
	if err := s.passwordResetTokenRepo.Update(ctx, token); err != nil {
		return fmt.Errorf("failed to mark token as used: %w", err)
	}

	// Revoke all refresh tokens for security
	if err := s.refreshTokenRepo.RevokeUserTokens(ctx, user.ID()); err != nil {
		// Don't fail the password reset if this fails, just log it
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "refresh_token_revocation_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Deactivate all sessions for security
	if err := s.sessionRepo.DeactivateUserSessions(ctx, user.ID()); err != nil {
		// Don't fail the password reset if this fails, just log it
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "session_deactivation_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Log successful password reset
	userID := user.ID()
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "password_reset_confirm", ipAddress, userAgent, true, nil, nil)

	return nil
}

// Helper methods

func (s *AuthService) generateAuthResponse(ctx context.Context, user *entities.User, ipAddress, userAgent string) (*dtos.AuthResponse, error) {
	// Generate access token
	accessToken, err := s.jwtService.GenerateAccessToken(
		user.ID().String(),
		user.Email().Value(),
		time.Duration(s.config.JWTExpirationHours)*time.Hour,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Generate refresh token
	refreshToken, err := s.jwtService.GenerateRefreshToken(
		user.ID().String(),
		time.Duration(s.config.JWTRefreshExpirationDays)*24*time.Hour,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Create session
	sessionExpiresAt := time.Now().Add(time.Duration(s.config.SessionTimeoutHours) * time.Hour)
	session, err := entities.NewSession(user.ID(), &userAgent, &ipAddress, sessionExpiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to save session: %w", err)
	}

	// Create refresh token entity
	refreshTokenExpiresAt := time.Now().Add(time.Duration(s.config.JWTRefreshExpirationDays) * 24 * time.Hour)
	refreshTokenEntity, err := entities.NewRefreshToken(user.ID(), refreshTokenExpiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create refresh token entity: %w", err)
	}

	if err := s.refreshTokenRepo.Create(ctx, refreshTokenEntity); err != nil {
		return nil, fmt.Errorf("failed to save refresh token: %w", err)
	}

	return &dtos.AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    s.config.JWTExpirationHours * 3600, // convert to seconds
		TokenType:    "Bearer",
		User:         s.userToProfile(user),
	}, nil
}

func (s *AuthService) userToProfile(user *entities.User) dtos.UserProfile {
	var languageStr *string
	if user.Language() != nil {
		lang := user.Language().String()
		languageStr = &lang
	}
	
	return dtos.UserProfile{
		ID:                user.ID(),
		Email:             user.Email().Value(),
		Username:          user.Username(),
		FirstName:         user.FirstName(),
		LastName:          user.LastName(),
		FullName:          user.FullName(),
		DisplayName:       user.FullName(), // Use FullName as DisplayName
		EmailVerified:     user.EmailVerified(),
		EmailVerifiedAt:   user.EmailVerifiedAt(),
		IsActive:          user.IsActive(),
		ProfilePictureURL: user.ProfilePictureURL(),
		Timezone:          user.Timezone(),
		Language:          languageStr,
		LastLoginAt:       user.LastLoginAt(),
		CreatedAt:         user.CreatedAt(),
		UpdatedAt:         user.UpdatedAt(),
	}
}

func (s *AuthService) checkRateLimit(ctx context.Context, email, ipAddress string) error {
	// Check failed attempts in the last lockout duration
	since := time.Now().Add(-time.Duration(s.config.LockoutDurationMinutes) * time.Minute)
	failedCount, err := s.loginAttemptRepo.CountFailedAttempts(ctx, email, since)
	if err != nil {
		return fmt.Errorf("failed to check rate limit: %w", err)
	}

	if failedCount >= int64(s.config.MaxLoginAttempts) {
		return fmt.Errorf("too many failed login attempts, account temporarily locked")
	}

	return nil
}

func (s *AuthService) sendEmailVerification(ctx context.Context, userID uuid.UUID, email string) error {
	// Create email verification token
	expiresAt := time.Now().Add(time.Duration(s.config.TokenExpirationMinutes) * time.Minute)
	token, err := entities.NewEmailVerificationToken(userID, email, expiresAt)
	if err != nil {
		return fmt.Errorf("failed to create email verification token: %w", err)
	}

	if err := s.emailVerificationTokenRepo.Create(ctx, token); err != nil {
		return fmt.Errorf("failed to save email verification token: %w", err)
	}

	// Send email
	return s.emailService.SendEmailVerificationEmail(email, token.Token)
}

func (s *AuthService) logLoginAttempt(ctx context.Context, userID *uuid.UUID, email, ipAddress, userAgent string, success bool, reason string) {
	attempt := entities.NewLoginAttempt(userID, email, ipAddress, userAgent, success, &reason)
	_ = s.loginAttemptRepo.Create(ctx, attempt) // Don't fail operations if logging fails
}

func (s *AuthService) logAuthAudit(ctx context.Context, userID *uuid.UUID, email, action, ipAddress, userAgent string, success bool, details map[string]interface{}, reason *string) {
	audit := entities.NewAuthAudit(userID, email, action, ipAddress, userAgent, success, details, reason)
	_ = s.authAuditRepo.Create(ctx, audit) // Don't fail operations if logging fails
}


// RequestEmailVerification handles email verification requests
func (s *AuthService) RequestEmailVerification(ctx context.Context, req *dtos.RequestEmailVerificationRequest, ipAddress, userAgent string) (*dtos.EmailVerificationResponse, error) {
	// Get user by email
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}
	user, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Always return success to prevent email enumeration
	response := &dtos.EmailVerificationResponse{
		Message: "If an account with that email exists, a verification link has been sent",
		Email:   req.Email,
	}

	if user == nil {
		// Log attempt for non-existent user
		s.logAuthAudit(ctx, nil, req.Email, "email_verification_request", ipAddress, userAgent, false, nil, stringPtr("user not found"))
		return response, nil
	}

	// Check if user is active
	if !user.IsActive() {
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "email_verification_request", ipAddress, userAgent, false, nil, stringPtr("account inactive"))
		return response, nil
	}

	// Check if email is already verified
	if user.EmailVerified() {
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "email_verification_request", ipAddress, userAgent, false, nil, stringPtr("email already verified"))
		return response, nil
	}

	// Send email verification
	if err := s.sendEmailVerification(ctx, user.ID(), req.Email); err != nil {
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, req.Email, "email_verification_send_failed", ipAddress, userAgent, false, nil, &errMsg)
		return nil, fmt.Errorf("failed to send email verification: %w", err)
	}

	// Log successful email verification request
	userID := user.ID()
	s.logAuthAudit(ctx, &userID, req.Email, "email_verification_request", ipAddress, userAgent, true, nil, nil)

	return response, nil
}

// VerifyEmail handles email verification confirmation
func (s *AuthService) VerifyEmail(ctx context.Context, req *dtos.VerifyEmailRequest, ipAddress, userAgent string) error {
	// Get email verification token
	token, err := s.emailVerificationTokenRepo.GetByToken(ctx, req.Token)
	if err != nil {
		return fmt.Errorf("failed to get email verification token: %w", err)
	}
	if token == nil {
		return fmt.Errorf("invalid or expired email verification token")
	}

	// Validate token
	if !token.IsValid() {
		return fmt.Errorf("invalid or expired email verification token")
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, token.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Verify email
	user.VerifyEmail()
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to verify user email: %w", err)
	}

	// Mark token as used
	token.MarkAsUsed()
	if err := s.emailVerificationTokenRepo.Update(ctx, token); err != nil {
		return fmt.Errorf("failed to mark token as used: %w", err)
	}

	// Log successful email verification
	userID := user.ID()
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "email_verification_confirm", ipAddress, userAgent, true, nil, nil)

	return nil
}

// ChangePassword handles password changes for authenticated users
func (s *AuthService) ChangePassword(ctx context.Context, userID uuid.UUID, req *dtos.AuthChangePasswordRequest, ipAddress, userAgent string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash()), []byte(req.CurrentPassword)); err != nil {
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "password_change_failed", ipAddress, userAgent, false, nil, stringPtr("invalid current password"))
		return fmt.Errorf("current password is incorrect")
	}

	// Validate new password strength
	if len(req.NewPassword) < s.config.PasswordMinLength {
		return fmt.Errorf("password must be at least %d characters long", s.config.PasswordMinLength)
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update user password
	user.ChangePassword(string(hashedPassword))
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user password: %w", err)
	}

	// Revoke all refresh tokens for security
	if err := s.refreshTokenRepo.RevokeUserTokens(ctx, user.ID()); err != nil {
		// Don't fail the password change if this fails, just log it
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "refresh_token_revocation_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Deactivate all other sessions for security (keep current session)
	if err := s.sessionRepo.DeactivateUserSessions(ctx, user.ID()); err != nil {
		// Don't fail the password change if this fails, just log it
		userID := user.ID()
		errMsg := err.Error()
		s.logAuthAudit(ctx, &userID, user.Email().Value(), "session_deactivation_failed", ipAddress, userAgent, false, nil, &errMsg)
	}

	// Log successful password change
	s.logAuthAudit(ctx, &userID, user.Email().Value(), "password_change", ipAddress, userAgent, true, nil, nil)

	return nil
}

// GetUserProfile returns the current user's profile
func (s *AuthService) GetUserProfile(ctx context.Context, userID uuid.UUID) (*dtos.UserProfile, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	profile := s.userToProfile(user)
	return &profile, nil
}

// UpdateUserProfile updates the current user's profile
func (s *AuthService) UpdateUserProfile(ctx context.Context, userID uuid.UUID, req *dtos.UpdateUserProfileRequest) (*dtos.UserProfile, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// Update profile fields if provided
	if req.Username != nil || req.FullName != nil || req.Bio != nil {
		user.UpdateProfile(req.FullName, nil, nil, req.Bio)
	}

	// Update username if provided
	if req.Username != nil {
		user.SetUsername(*req.Username)
	}

	// Save changes
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	profile := s.userToProfile(user)
	return &profile, nil
}

// GetUserSessions returns the current user's active sessions
func (s *AuthService) GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*dtos.SessionResponse, error) {
	// Get user sessions
	sessions, err := s.sessionRepo.GetActiveSessions(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	// Convert to DTOs
	sessionResponses := make([]*dtos.SessionResponse, len(sessions))
	for i, session := range sessions {
		sessionResponses[i] = &dtos.SessionResponse{
			ID:         session.ID,
			UserID:     session.UserID,
			Token:      session.Token,
			UserAgent:  session.UserAgent,
			IPAddress:  session.IPAddress,
			IsActive:   session.IsActive,
			ExpiresAt:  session.ExpiresAt,
			LastUsedAt: session.LastUsedAt,
			CreatedAt:  session.CreatedAt,
		}
	}

	return sessionResponses, nil
}

// RevokeSession revokes a specific session
func (s *AuthService) RevokeSession(ctx context.Context, userID, sessionID uuid.UUID, ipAddress, userAgent string) error {
	// Get session
	session, err := s.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	if session == nil {
		return fmt.Errorf("session not found")
	}

	// Verify session belongs to user
	if session.UserID != userID {
		return fmt.Errorf("session does not belong to user")
	}

	// Deactivate session
	session.Deactivate()
	if err := s.sessionRepo.Update(ctx, session); err != nil {
		return fmt.Errorf("failed to deactivate session: %w", err)
	}

	// Log session revocation
	s.logAuthAudit(ctx, &userID, "", "session_revocation", ipAddress, userAgent, true, map[string]interface{}{
		"session_id": sessionID.String(),
	}, nil)

	return nil
}

// RevokeAllSessions revokes all sessions for the current user
func (s *AuthService) RevokeAllSessions(ctx context.Context, userID uuid.UUID, ipAddress, userAgent string) error {
	// Deactivate all user sessions
	if err := s.sessionRepo.DeactivateUserSessions(ctx, userID); err != nil {
		return fmt.Errorf("failed to deactivate user sessions: %w", err)
	}

	// Log all sessions revocation
	s.logAuthAudit(ctx, &userID, "", "all_sessions_revocation", ipAddress, userAgent, true, nil, nil)

	return nil
}

// GoogleAuth handles Google OAuth authentication
func (s *AuthService) GoogleAuth(ctx context.Context, req *dtos.GoogleAuthRequest, ipAddress, userAgent string) (*dtos.AuthResponse, error) {
	// Create email value object
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}

	// Check if user exists
	user, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// If user doesn't exist, create a new one
	if user == nil {
		// Create new user from Google auth
		user, err = entities.NewUser(*email, "") // Empty password for OAuth users
		if err != nil {
			return nil, fmt.Errorf("failed to create user entity: %w", err)
		}

		// Set user details from Google
		user.SetUsername(req.Name)
		user.UpdateProfile(&req.Name, nil, req.Picture, nil)
		user.VerifyEmail() // Google accounts are pre-verified

		// Save new user
		if err := s.userRepo.Create(ctx, user); err != nil {
			return nil, fmt.Errorf("failed to create user: %w", err)
		}

		// Log successful Google signup
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "google_signup", ipAddress, userAgent, true, map[string]interface{}{
			"google_id": req.GoogleID,
			"name":      req.Name,
		}, nil)

		// Send welcome email
		if err := s.emailService.SendWelcomeEmail(req.Email, req.Name); err != nil {
			// Don't fail auth if welcome email fails
			errMsg := err.Error()
			s.logAuthAudit(ctx, &userID, req.Email, "welcome_email_send_failed", ipAddress, userAgent, false, nil, &errMsg)
		}
	} else {
		// Check if user is active
		if !user.IsActive() {
			userID := user.ID()
			s.logAuthAudit(ctx, &userID, req.Email, "google_login_failed", ipAddress, userAgent, false, nil, stringPtr("account inactive"))
			return nil, fmt.Errorf("account is inactive")
		}

		// Update last login
		user.UpdateLastLogin()
		if err := s.userRepo.Update(ctx, user); err != nil {
			// Don't fail login if this fails, just log it
			userID := user.ID()
			errMsg := err.Error()
			s.logAuthAudit(ctx, &userID, req.Email, "last_login_update_failed", ipAddress, userAgent, false, nil, &errMsg)
		}

		// Log successful Google login
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "google_login", ipAddress, userAgent, true, map[string]interface{}{
			"google_id": req.GoogleID,
		}, nil)
	}

	// Generate tokens and return auth response
	return s.generateAuthResponse(ctx, user, ipAddress, userAgent)
}

// SSOAuth handles SSO authentication
func (s *AuthService) SSOAuth(ctx context.Context, req *dtos.SSOAuthRequest, ipAddress, userAgent string) (*dtos.AuthResponse, error) {
	// Create email value object
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email format: %w", err)
	}

	// Check if user exists
	user, err := s.userRepo.GetByEmail(ctx, *email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// If user doesn't exist, create a new one
	if user == nil {
		// Create new user from SSO auth
		user, err = entities.NewUser(*email, "") // Empty password for SSO users
		if err != nil {
			return nil, fmt.Errorf("failed to create user entity: %w", err)
		}

		// Set user details from SSO
		if req.Username != "" {
			user.SetUsername(req.Username)
		}
		user.UpdateProfile(&req.FullName, nil, nil, nil)
		user.VerifyEmail() // SSO accounts are pre-verified

		// Save new user
		if err := s.userRepo.Create(ctx, user); err != nil {
			return nil, fmt.Errorf("failed to create user: %w", err)
		}

		// Log successful SSO signup
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "sso_signup", ipAddress, userAgent, true, map[string]interface{}{
			"sso_user_id": req.UserID,
			"username":    req.Username,
			"full_name":   req.FullName,
			"sso_source":  req.SSOSource,
			"is_new_user": req.IsNewUser,
		}, nil)

		// Send welcome email
		if err := s.emailService.SendWelcomeEmail(req.Email, req.FullName); err != nil {
			// Don't fail auth if welcome email fails
			errMsg := err.Error()
			s.logAuthAudit(ctx, &userID, req.Email, "welcome_email_send_failed", ipAddress, userAgent, false, nil, &errMsg)
		}
	} else {
		// Check if user is active
		if !user.IsActive() {
			userID := user.ID()
			s.logAuthAudit(ctx, &userID, req.Email, "sso_login_failed", ipAddress, userAgent, false, nil, stringPtr("account inactive"))
			return nil, fmt.Errorf("account is inactive")
		}

		// Update user profile from SSO if needed (optional - sync profile data)
		if req.FullName != "" && user.FullName() != req.FullName {
			user.UpdateProfile(&req.FullName, nil, nil, nil)
		}
		if req.Username != "" && (user.Username() == nil || *user.Username() != req.Username) {
			user.SetUsername(req.Username)
		}

		// Update last login
		user.UpdateLastLogin()
		if err := s.userRepo.Update(ctx, user); err != nil {
			// Don't fail login if this fails, just log it
			userID := user.ID()
			errMsg := err.Error()
			s.logAuthAudit(ctx, &userID, req.Email, "last_login_update_failed", ipAddress, userAgent, false, nil, &errMsg)
		}

		// Log successful SSO login
		userID := user.ID()
		s.logAuthAudit(ctx, &userID, req.Email, "sso_login", ipAddress, userAgent, true, map[string]interface{}{
			"sso_user_id": req.UserID,
			"username":    req.Username,
			"sso_source":  req.SSOSource,
		}, nil)
	}

	// Generate tokens and return auth response
	return s.generateAuthResponse(ctx, user, ipAddress, userAgent)
}

// stringPtr is a helper function to convert string to *string
func stringPtr(s string) *string {
	return &s
}