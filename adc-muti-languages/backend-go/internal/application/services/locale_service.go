package services

import (
	"context"
	"fmt"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"github.com/google/uuid"
)

// LocaleService handles locale-related business logic
type LocaleService struct {
	localeRepo repositories.LocaleRepository
}

// NewLocaleService creates a new locale service
func NewLocaleService(localeRepo repositories.LocaleRepository) *LocaleService {
	return &LocaleService{
		localeRepo: localeRepo,
	}
}

// CreateLocale creates a new locale
func (s *LocaleService) CreateLocale(ctx context.Context, req *dtos.CreateLocaleRequest) (*dtos.LocaleResponse, error) {
	// Check if locale code already exists
	exists, err := s.localeRepo.CodeExists(ctx, req.Code)
	if err != nil {
		return nil, fmt.Errorf("failed to check locale code existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("locale with code '%s' already exists", req.Code)
	}

	// Create locale entity
	locale := entities.NewLocale(req.Code, req.Name, req.NativeName)
	
	// Set optional fields
	if req.Direction != "" {
		if err := locale.SetDirection(req.Direction); err != nil {
			return nil, fmt.Errorf("invalid direction: %w", err)
		}
	}
	
	if req.IsActive != nil && !*req.IsActive {
		locale.Deactivate()
	}

	// Save to repository
	if err := s.localeRepo.Create(ctx, locale); err != nil {
		return nil, fmt.Errorf("failed to create locale: %w", err)
	}

	return s.localeToResponse(locale), nil
}

// GetLocale retrieves a locale by ID
func (s *LocaleService) GetLocale(ctx context.Context, id uuid.UUID) (*dtos.LocaleResponse, error) {
	locale, err := s.localeRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	return s.localeToResponse(locale), nil
}

// GetLocaleByCode retrieves a locale by code
func (s *LocaleService) GetLocaleByCode(ctx context.Context, code string) (*dtos.LocaleResponse, error) {
	locale, err := s.localeRepo.GetByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	return s.localeToResponse(locale), nil
}

// UpdateLocale updates a locale
func (s *LocaleService) UpdateLocale(ctx context.Context, id uuid.UUID, req *dtos.UpdateLocaleRequest) (*dtos.LocaleResponse, error) {
	// Get existing locale
	locale, err := s.localeRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	// Update locale information
	locale.UpdateInfo(req.Name, req.NativeName)
	
	// Update direction if provided
	if req.Direction != nil {
		if err := locale.SetDirection(*req.Direction); err != nil {
			return nil, fmt.Errorf("invalid direction: %w", err)
		}
	}
	
	// Update active status if provided
	if req.IsActive != nil {
		if *req.IsActive {
			locale.Activate()
		} else {
			locale.Deactivate()
		}
	}

	// Save changes
	if err := s.localeRepo.Update(ctx, locale); err != nil {
		return nil, fmt.Errorf("failed to update locale: %w", err)
	}

	return s.localeToResponse(locale), nil
}

// DeleteLocale deletes a locale
func (s *LocaleService) DeleteLocale(ctx context.Context, id uuid.UUID) error {
	// Get existing locale
	locale, err := s.localeRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return fmt.Errorf("locale not found")
	}

	// Delete locale
	if err := s.localeRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete locale: %w", err)
	}

	return nil
}

// ListLocales lists locales with pagination and filters
func (s *LocaleService) ListLocales(ctx context.Context, filter dtos.LocaleFilter, pagination dtos.PaginationRequest) (*dtos.LocaleListResponse, error) {
	repoFilter := repositories.LocaleFilters{
		Code:      filter.Code,
		Name:      filter.Name,
		IsActive:  filter.IsActive,
		Direction: filter.Direction,
	}

	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	var locales []*entities.Locale
	var total int64
	var err error

	if filter.Search != "" {
		locales, total, err = s.localeRepo.SearchLocales(ctx, filter.Search, repoPagination)
	} else {
		locales, total, err = s.localeRepo.List(ctx, repoFilter, repoPagination)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to list locales: %w", err)
	}

	responses := make([]dtos.LocaleResponse, len(locales))
	for i, locale := range locales {
		responses[i] = *s.localeToResponse(locale)
	}

	totalPages := int64((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
	return &dtos.LocaleListResponse{
		Locales: responses,
		Pagination: dtos.PaginationResult{
			Total:       total,
			TotalPages:  totalPages,
			CurrentPage: pagination.Page,
			PageSize:    pagination.PageSize,
			HasNext:     int64(pagination.Page) < totalPages,
			HasPrev:     pagination.Page > 1,
		},
	}, nil
}

// GetActiveLocales retrieves all active locales
func (s *LocaleService) GetActiveLocales(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.LocaleListResponse, error) {
	repoPagination := repositories.Pagination{
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		OrderBy:  pagination.OrderBy,
		Order:    pagination.Order,
	}

	locales, total, err := s.localeRepo.GetActive(ctx, repoPagination)
	if err != nil {
		return nil, fmt.Errorf("failed to get active locales: %w", err)
	}

	responses := make([]dtos.LocaleResponse, len(locales))
	for i, locale := range locales {
		responses[i] = *s.localeToResponse(locale)
	}

	totalPages := int64((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
	return &dtos.LocaleListResponse{
		Locales: responses,
		Pagination: dtos.PaginationResult{
			Total:       total,
			TotalPages:  totalPages,
			CurrentPage: pagination.Page,
			PageSize:    pagination.PageSize,
			HasNext:     int64(pagination.Page) < totalPages,
			HasPrev:     pagination.Page > 1,
		},
	}, nil
}

// ActivateLocale activates a locale
func (s *LocaleService) ActivateLocale(ctx context.Context, id uuid.UUID) (*dtos.LocaleResponse, error) {
	// Get existing locale
	locale, err := s.localeRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	// Activate locale
	locale.Activate()

	// Save changes
	if err := s.localeRepo.Update(ctx, locale); err != nil {
		return nil, fmt.Errorf("failed to activate locale: %w", err)
	}

	return s.localeToResponse(locale), nil
}

// DeactivateLocale deactivates a locale
func (s *LocaleService) DeactivateLocale(ctx context.Context, id uuid.UUID) (*dtos.LocaleResponse, error) {
	// Get existing locale
	locale, err := s.localeRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get locale: %w", err)
	}
	if locale == nil {
		return nil, fmt.Errorf("locale not found")
	}

	// Deactivate locale
	locale.Deactivate()

	// Save changes
	if err := s.localeRepo.Update(ctx, locale); err != nil {
		return nil, fmt.Errorf("failed to deactivate locale: %w", err)
	}

	return s.localeToResponse(locale), nil
}

// Helper method

func (s *LocaleService) localeToResponse(locale *entities.Locale) *dtos.LocaleResponse {
	return &dtos.LocaleResponse{
		ID:         locale.ID,
		Code:       locale.Code,
		Name:       locale.Name,
		NativeName: locale.NativeName,
		IsActive:   locale.IsActive,
		Direction:  locale.Direction,
		CreatedAt:  locale.CreatedAt,
		UpdatedAt:  locale.UpdatedAt,
	}
}