package entities

import (
	"errors"
	"time"
)

// SubscriptionStatus represents the status of a subscription
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusPastDue   SubscriptionStatus = "past_due"
	SubscriptionStatusCanceled  SubscriptionStatus = "canceled"
	SubscriptionStatusUnpaid    SubscriptionStatus = "unpaid"
	SubscriptionStatusPending   SubscriptionStatus = "pending"
	SubscriptionStatusTrialing  SubscriptionStatus = "trialing"
)

// BillingInterval represents how often the subscription is billed
type BillingInterval string

const (
	BillingIntervalMonthly BillingInterval = "monthly"
	BillingIntervalYearly  BillingInterval = "yearly"
)

// SubscriptionPlan represents a subscription plan
type SubscriptionPlan struct {
	BaseEntity
	Name                string          `json:"name"`
	Description         string          `json:"description"`
	Price               int64           `json:"price"`                // Price in cents (legacy field)
	MonthlyPrice        int64           `json:"monthly_price"`        // Monthly price in cents
	YearlyPrice         int64           `json:"yearly_price"`         // Yearly price in cents
	Currency            string          `json:"currency"`             // ISO currency code
	BillingInterval     BillingInterval `json:"billing_interval"`
	AICreditsPerMonth   int64           `json:"ai_credits_per_month"`
	MaxProjects         int32           `json:"max_projects"`
	MaxMembers          int32           `json:"max_members"`
	StripeProductID     string          `json:"stripe_product_id"`
	StripePriceID       string          `json:"stripe_price_id"`
	StripePriceMonthly  string          `json:"stripe_price_monthly"`
	StripePriceYearly   string          `json:"stripe_price_yearly"`
	IsActive            bool            `json:"is_active"`
	Features            []string        `json:"features"`
	TrialDays           int32           `json:"trial_days"`
}

// NewSubscriptionPlan creates a new subscription plan
func NewSubscriptionPlan(name, description, currency string, monthlyPrice, yearlyPrice int64) *SubscriptionPlan {
	return &SubscriptionPlan{
		BaseEntity:        NewBaseEntity(),
		Name:              name,
		Description:       description,
		Price:             monthlyPrice, // Legacy field for backward compatibility
		MonthlyPrice:      monthlyPrice,
		YearlyPrice:       yearlyPrice,
		Currency:          currency,
		BillingInterval:   BillingIntervalMonthly, // Default to monthly
		AICreditsPerMonth: 0,
		MaxProjects:       1,
		MaxMembers:        1,
		IsActive:          true,
		Features:          make([]string, 0),
		TrialDays:         0,
	}
}

// SetStripeIDs sets the Stripe product and price IDs (legacy method)
func (sp *SubscriptionPlan) SetStripeIDs(productID, priceID string) {
	sp.StripeProductID = productID
	sp.StripePriceID = priceID
}

// SetStripePriceIDs sets the monthly and yearly Stripe price IDs
func (sp *SubscriptionPlan) SetStripePriceIDs(productID, monthlyPriceID, yearlyPriceID string) {
	sp.StripeProductID = productID
	sp.StripePriceMonthly = monthlyPriceID
	sp.StripePriceYearly = yearlyPriceID
	// Keep legacy field for backward compatibility
	sp.StripePriceID = monthlyPriceID
}

// SetLimits sets the subscription limits
func (sp *SubscriptionPlan) SetLimits(aiCredits int64, maxProjects, maxMembers int32) {
	sp.AICreditsPerMonth = aiCredits
	sp.MaxProjects = maxProjects
	sp.MaxMembers = maxMembers
}

// AddFeature adds a feature to the plan
func (sp *SubscriptionPlan) AddFeature(feature string) {
	sp.Features = append(sp.Features, feature)
}

// HasFeature checks if the plan has a specific feature
func (sp *SubscriptionPlan) HasFeature(feature string) bool {
	for _, f := range sp.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// Validate validates the subscription plan
func (sp *SubscriptionPlan) Validate() error {
	if sp.Name == "" {
		return ErrInvalidInput{Field: "name", Message: "name is required"}
	}
	if sp.Price < 0 {
		return ErrInvalidInput{Field: "price", Message: "price cannot be negative"}
	}
	if sp.Currency == "" {
		return ErrInvalidInput{Field: "currency", Message: "currency is required"}
	}
	if sp.BillingInterval != BillingIntervalMonthly && sp.BillingInterval != BillingIntervalYearly {
		return ErrInvalidInput{Field: "billing_interval", Message: "invalid billing interval"}
	}
	if sp.AICreditsPerMonth < 0 {
		return ErrInvalidInput{Field: "ai_credits_per_month", Message: "AI credits cannot be negative"}
	}
	if sp.MaxProjects < 0 {
		return ErrInvalidInput{Field: "max_projects", Message: "max projects cannot be negative"}
	}
	if sp.MaxMembers < 0 {
		return ErrInvalidInput{Field: "max_members", Message: "max members cannot be negative"}
	}
	return nil
}

// GetMonthlyPrice returns the monthly price
func (sp *SubscriptionPlan) GetMonthlyPrice() int64 {
	if sp.MonthlyPrice > 0 {
		return sp.MonthlyPrice
	}
	// Fallback to legacy Price field if MonthlyPrice is not set
	if sp.BillingInterval == BillingIntervalYearly {
		return sp.Price / 12
	}
	return sp.Price
}

// GetYearlyPrice returns the yearly price
func (sp *SubscriptionPlan) GetYearlyPrice() int64 {
	return sp.YearlyPrice
}

// GetPriceForInterval returns the price for a specific billing interval
func (sp *SubscriptionPlan) GetPriceForInterval(interval BillingInterval) int64 {
	if interval == BillingIntervalYearly {
		return sp.GetYearlyPrice()
	}
	return sp.GetMonthlyPrice()
}

// OrganizationSubscription represents an organization's subscription
type OrganizationSubscription struct {
	BaseEntity
	OrganizationID         string             `json:"organization_id"`
	SubscriptionPlanID     string             `json:"subscription_plan_id"`
	StripeSubscriptionID   string             `json:"stripe_subscription_id"`
	StripeCustomerID       string             `json:"stripe_customer_id"`
	Status                 SubscriptionStatus `json:"status"`
	CurrentPeriodStart     time.Time          `json:"current_period_start"`
	CurrentPeriodEnd       time.Time          `json:"current_period_end"`
	TrialStart             *time.Time         `json:"trial_start,omitempty"`
	TrialEnd               *time.Time         `json:"trial_end,omitempty"`
	CanceledAt             *time.Time         `json:"canceled_at,omitempty"`
	CancelAtPeriodEnd      bool               `json:"cancel_at_period_end"`
	AICreditsUsed          int64              `json:"ai_credits_used"`
	AICreditsRemaining     int64              `json:"ai_credits_remaining"`
	LastCreditReset        time.Time          `json:"last_credit_reset"`
	BillingPeriod          string             `json:"billing_period"`
	
	// Relations
	Organization     *Organization     `json:"organization,omitempty"`
	SubscriptionPlan *SubscriptionPlan `json:"subscription_plan,omitempty"`
}

// NewOrganizationSubscription creates a new organization subscription
func NewOrganizationSubscription(organizationID, planID string) *OrganizationSubscription {
	now := time.Now()
	return &OrganizationSubscription{
		BaseEntity:         NewBaseEntity(),
		OrganizationID:     organizationID,
		SubscriptionPlanID: planID,
		Status:             SubscriptionStatusPending,
		CurrentPeriodStart: now,
		CurrentPeriodEnd:   now.AddDate(0, 1, 0), // Default to monthly
		CancelAtPeriodEnd:  false,
		AICreditsUsed:      0,
		AICreditsRemaining: 0,
		LastCreditReset:    now,
		BillingPeriod:      "monthly", // Default billing period
	}
}

// SetStripeIDs sets the Stripe subscription and customer IDs
func (os *OrganizationSubscription) SetStripeIDs(subscriptionID, customerID string) {
	os.StripeSubscriptionID = subscriptionID
	os.StripeCustomerID = customerID
}

// StartTrial starts a trial period
func (os *OrganizationSubscription) StartTrial(days int32) error {
	if os.Status != SubscriptionStatusPending {
		return errors.New("can only start trial for pending subscriptions")
	}
	
	now := time.Now()
	trialEnd := now.AddDate(0, 0, int(days))
	
	os.Status = SubscriptionStatusTrialing
	os.TrialStart = &now
	os.TrialEnd = &trialEnd
	os.CurrentPeriodEnd = trialEnd
	
	return nil
}

// Activate activates the subscription
func (os *OrganizationSubscription) Activate() error {
	if os.Status == SubscriptionStatusCanceled {
		return errors.New("cannot activate canceled subscription")
	}
	
	os.Status = SubscriptionStatusActive
	return nil
}

// Cancel cancels the subscription
func (os *OrganizationSubscription) Cancel(immediate bool) error {
	if os.Status == SubscriptionStatusCanceled {
		return errors.New("subscription already canceled")
	}
	
	now := time.Now()
	os.CanceledAt = &now
	
	if immediate {
		os.Status = SubscriptionStatusCanceled
		os.CurrentPeriodEnd = now
	} else {
		os.CancelAtPeriodEnd = true
	}
	
	return nil
}

// SetPastDue marks the subscription as past due
func (os *OrganizationSubscription) SetPastDue() {
	os.Status = SubscriptionStatusPastDue
}

// SetUnpaid marks the subscription as unpaid
func (os *OrganizationSubscription) SetUnpaid() {
	os.Status = SubscriptionStatusUnpaid
}

// RenewPeriod renews the subscription period
func (os *OrganizationSubscription) RenewPeriod(interval BillingInterval) {
	now := time.Now()
	os.CurrentPeriodStart = now
	
	if interval == BillingIntervalYearly {
		os.CurrentPeriodEnd = now.AddDate(1, 0, 0)
	} else {
		os.CurrentPeriodEnd = now.AddDate(0, 1, 0)
	}
	
	if os.CancelAtPeriodEnd {
		os.Status = SubscriptionStatusCanceled
	}
}

// ResetAICredits resets the AI credits for the billing period
func (os *OrganizationSubscription) ResetAICredits(credits int64) {
	os.AICreditsUsed = 0
	os.AICreditsRemaining = credits
	os.LastCreditReset = time.Now()
}

// UseAICredits uses AI credits
func (os *OrganizationSubscription) UseAICredits(amount int64) error {
	if os.AICreditsRemaining < amount {
		return errors.New("insufficient AI credits")
	}
	
	os.AICreditsUsed += amount
	os.AICreditsRemaining -= amount
	return nil
}

// AddAICredits adds AI credits (for purchases or bonuses)
func (os *OrganizationSubscription) AddAICredits(amount int64) {
	os.AICreditsRemaining += amount
}

// IsActive checks if the subscription is active
func (os *OrganizationSubscription) IsActive() bool {
	return os.Status == SubscriptionStatusActive
}

// IsTrialing checks if the subscription is in trial
func (os *OrganizationSubscription) IsTrialing() bool {
	return os.Status == SubscriptionStatusTrialing && 
		   os.TrialEnd != nil && 
		   time.Now().Before(*os.TrialEnd)
}

// IsExpired checks if the subscription is expired
func (os *OrganizationSubscription) IsExpired() bool {
	return time.Now().After(os.CurrentPeriodEnd)
}

// DaysUntilExpiry returns days until subscription expires
func (os *OrganizationSubscription) DaysUntilExpiry() int {
	if os.IsExpired() {
		return 0
	}
	duration := time.Until(os.CurrentPeriodEnd)
	return int(duration.Hours() / 24)
}

// Validate validates the organization subscription
func (os *OrganizationSubscription) Validate() error {
	if os.OrganizationID == "" {
		return ErrInvalidInput{Field: "organization_id", Message: "organization ID is required"}
	}
	if os.SubscriptionPlanID == "" {
		return ErrInvalidInput{Field: "subscription_plan_id", Message: "subscription plan ID is required"}
	}
	if os.AICreditsUsed < 0 {
		return ErrInvalidInput{Field: "ai_credits_used", Message: "AI credits used cannot be negative"}
	}
	if os.AICreditsRemaining < 0 {
		return ErrInvalidInput{Field: "ai_credits_remaining", Message: "AI credits remaining cannot be negative"}
	}
	return nil
}

// AICreditTransaction represents a transaction for AI credits
type AICreditTransaction struct {
	BaseEntity
	OrganizationID   string                     `json:"organization_id"`
	SubscriptionID   string                     `json:"subscription_id"`
	TransactionType  AICreditTransactionType    `json:"transaction_type"`
	Amount           int64                      `json:"amount"`
	Description      string                     `json:"description"`
	ReferenceID      string                     `json:"reference_id,omitempty"` // For relating to translations, purchases, etc.
	BalanceBefore    int64                      `json:"balance_before"`
	BalanceAfter     int64                      `json:"balance_after"`
	
	// Relations
	Organization *Organization             `json:"organization,omitempty"`
	Subscription *OrganizationSubscription `json:"subscription,omitempty"`
}

// AICreditTransactionType represents the type of AI credit transaction
type AICreditTransactionType string

const (
	AICreditTransactionTypeUsage    AICreditTransactionType = "usage"
	AICreditTransactionTypePurchase AICreditTransactionType = "purchase"
	AICreditTransactionTypeRefund   AICreditTransactionType = "refund"
	AICreditTransactionTypeReset    AICreditTransactionType = "reset"
	AICreditTransactionTypeBonus    AICreditTransactionType = "bonus"
)

// NewAICreditTransaction creates a new AI credit transaction
func NewAICreditTransaction(orgID, subID string, txType AICreditTransactionType, amount int64, description string) *AICreditTransaction {
	return &AICreditTransaction{
		BaseEntity:      NewBaseEntity(),
		OrganizationID:  orgID,
		SubscriptionID:  subID,
		TransactionType: txType,
		Amount:          amount,
		Description:     description,
	}
}

// SetBalances sets the before and after balances
func (act *AICreditTransaction) SetBalances(before, after int64) {
	act.BalanceBefore = before
	act.BalanceAfter = after
}

// SetReference sets a reference ID for tracking
func (act *AICreditTransaction) SetReference(referenceID string) {
	act.ReferenceID = referenceID
}

// Validate validates the AI credit transaction
func (act *AICreditTransaction) Validate() error {
	if act.OrganizationID == "" {
		return ErrInvalidInput{Field: "organization_id", Message: "organization ID is required"}
	}
	if act.SubscriptionID == "" {
		return ErrInvalidInput{Field: "subscription_id", Message: "subscription ID is required"}
	}
	if act.Amount == 0 {
		return ErrInvalidInput{Field: "amount", Message: "amount cannot be zero"}
	}
	if act.Description == "" {
		return ErrInvalidInput{Field: "description", Message: "description is required"}
	}
	return nil
}