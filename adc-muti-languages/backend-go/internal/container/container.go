package container

import (
	"os"
	"strconv"

	"adc-multi-languages/internal/application/services"
	"adc-multi-languages/internal/domain/repositories"
	domainServices "adc-multi-languages/internal/domain/services"
	"adc-multi-languages/internal/infrastructure/config"
	"adc-multi-languages/internal/infrastructure/external"
	"adc-multi-languages/internal/infrastructure/external/ai"
	"adc-multi-languages/internal/infrastructure/external/payments"
	"adc-multi-languages/internal/infrastructure/external/storage"
	repositoriesImpl "adc-multi-languages/internal/infrastructure/repositories"
	servicesImpl "adc-multi-languages/internal/infrastructure/services"
	"adc-multi-languages/internal/presentation/handlers"
	"github.com/verawat1234/adc-sso-service/sdk"
	subscriptionSDK "github.com/adc-subscription-service/sdk"
	"gorm.io/gorm"
)

// Container holds all dependencies
type Container struct {
	// Configuration
	Config *config.Config
	
	// Database connection
	DB *gorm.DB
	
	// Repositories
	UserRepository                      repositories.UserRepository
	OrganizationRepository              repositories.OrganizationRepository
	ProjectRepository                   repositories.ProjectRepository
	ProjectLocaleRepository             repositories.ProjectLocaleRepository
	ResourceRepository                  repositories.ResourceRepository
	TranslationRepository               repositories.TranslationRepository
	TranslationKeyRepository            repositories.TranslationKeyRepository
	TranslationHistoryRepository        repositories.TranslationHistoryRepository
	LocaleRepository                    repositories.LocaleRepository
	NamespaceRepository                 interface{} // TODO: repositories.NamespaceRepository
	SessionRepository                   repositories.SessionRepository
	RefreshTokenRepository              repositories.RefreshTokenRepository
	PasswordResetTokenRepository        repositories.PasswordResetTokenRepository
	EmailVerificationTokenRepository    repositories.EmailVerificationTokenRepository
	LoginAttemptRepository              repositories.LoginAttemptRepository
	AuthAuditRepository                 repositories.AuthAuditRepository
	APIKeyRepository                    repositories.APIKeyRepository
	SubscriptionPlanRepository          repositories.SubscriptionPlanRepository
	OrganizationSubscriptionRepository  repositories.OrganizationSubscriptionRepository
	AICreditTransactionRepository       repositories.AICreditTransactionRepository
	JobRepository                       repositories.JobRepository
	JobScheduleRepository               repositories.JobScheduleRepository
	OrganizationMembershipRepository    repositories.OrganizationMembershipRepository
	PermissionGroupRepository           repositories.PermissionGroupRepository
	AuditLogRepository                  repositories.AuditLogRepository
	// WorkspaceRepository                 repositories.WorkspaceRepository // TODO: Implement
	WorkspaceMemberRepository           interface{} // TODO: repositories.WorkspaceMemberRepository
	
	// Services
	UserService         *services.UserService
	OrganizationService *services.OrganizationService
	ProjectService      *services.ProjectService
	TranslationService  *services.TranslationService
	LocaleService       *services.LocaleService
	AuthService         *services.AuthService
	APIKeyService       *services.APIKeyService
	SubscriptionService *services.SubscriptionService
	JobService          *services.JobService
	MembershipService   *services.MembershipService
	PermissionGroupService *services.PermissionGroupService
	AuditLogService     *services.AuditLogService
	WorkspaceService    interface{} // TODO: *services.WorkspaceService
	AITranslationService interface{} // TODO: *services.AITranslationService
	CollaborativeTranslationService interface{} // TODO: *services.CollaborativeTranslationService
	APIIntegrationService interface{} // TODO: *services.APIIntegrationService
	TranslationWorkflowService interface{} // TODO: *services.TranslationWorkflowService
	SmartTranslationService interface{} // TODO: *services.SmartTranslationService
	JWTService          services.JWTService
	LoggerService       *servicesImpl.LoggerService
	StripeService       *payments.StripeService
	FileUploadService   *storage.FileUploadService
	SSOClient           *sdk.SSOClient
	SubscriptionServiceClient *subscriptionSDK.Client
	SettingsClient      *external.SettingsClient
	
	// Domain Services
	TranslationDomainService domainServices.TranslationDomainService
	AITranslationProvider    domainServices.AITranslationProvider
	
	// Handlers
	UserHandler         *handlers.UserHandler
	OrganizationHandler *handlers.OrganizationHandler
	ProjectHandler      *handlers.ProjectHandler
	TranslationHandler  *handlers.TranslationHandler
	LocaleHandler       *handlers.LocaleHandler
	AuthHandler         *handlers.AuthHandler
	APIKeyHandler       *handlers.APIKeyHandler
	SubscriptionHandler *handlers.SubscriptionHandler
	JobHandler          *handlers.JobHandler
	MembershipHandler   *handlers.MembershipHandler
	PermissionGroupHandler *handlers.PermissionGroupHandler
	AuditLogHandler     *handlers.AuditLogHandler
	AIHandler           *handlers.AIHandler
	StorageHandler      *handlers.StorageHandler
	SmartTranslationHandler interface{} // TODO: *handlers.SmartTranslationHandler
	SubscriptionServiceTestHandler interface{} // TODO: *handlers.SubscriptionServiceTestHandler
}

// NewContainer creates and wires up all dependencies
func NewContainer(db *gorm.DB, cfg *config.Config) *Container {
	container := &Container{
		Config: cfg,
		DB:     db,
	}
	
	// Initialize repositories
	container.UserRepository = repositoriesImpl.NewUserRepository(db)
	container.OrganizationRepository = repositoriesImpl.NewOrganizationRepository(db)
	container.ProjectRepository = repositoriesImpl.NewProjectRepository(db)
	container.ProjectLocaleRepository = repositoriesImpl.NewProjectLocaleRepository(db)
	container.ResourceRepository = repositoriesImpl.NewResourceRepository(db)
	container.TranslationRepository = repositoriesImpl.NewTranslationRepository(db)
	container.TranslationKeyRepository = repositoriesImpl.NewTranslationKeyRepository(db)
	container.TranslationHistoryRepository = repositoriesImpl.NewTranslationHistoryRepository(db)
	container.LocaleRepository = repositoriesImpl.NewLocaleRepository(db)
	container.NamespaceRepository = nil // TODO: repositoriesImpl.NewNamespaceRepository(db)
	
	// Initialize auth repositories
	container.SessionRepository = repositoriesImpl.NewSessionRepository(db)
	container.RefreshTokenRepository = repositoriesImpl.NewRefreshTokenRepository(db)
	container.PasswordResetTokenRepository = repositoriesImpl.NewPasswordResetTokenRepository(db)
	container.EmailVerificationTokenRepository = repositoriesImpl.NewEmailVerificationTokenRepository(db)
	container.LoginAttemptRepository = repositoriesImpl.NewLoginAttemptRepository(db)
	container.AuthAuditRepository = repositoriesImpl.NewAuthAuditRepository(db)
	container.APIKeyRepository = repositoriesImpl.NewAPIKeyRepository(db)
	container.SubscriptionPlanRepository = repositoriesImpl.NewSubscriptionPlanRepository(db)
	container.OrganizationSubscriptionRepository = repositoriesImpl.NewOrganizationSubscriptionRepository(db)
	container.AICreditTransactionRepository = repositoriesImpl.NewAICreditTransactionRepository(db)
	container.JobRepository = repositoriesImpl.NewJobRepository(db)
	container.JobScheduleRepository = repositoriesImpl.NewJobScheduleRepository(db)
	container.OrganizationMembershipRepository = repositoriesImpl.NewOrganizationMembershipRepository(db)
	container.PermissionGroupRepository = repositoriesImpl.NewPermissionGroupRepository(db)
	container.AuditLogRepository = repositoriesImpl.NewAuditLogRepository(db)
	// container.WorkspaceRepository = repositoriesImpl.NewWorkspaceRepository(db) // TODO: Implement
	container.WorkspaceMemberRepository = nil // TODO: repositoriesImpl.NewWorkspaceMemberRepository(db)
	
	// Initialize logger service
	logLevel := os.Getenv("LOG_LEVEL")
	if logLevel == "" {
		logLevel = "info" // Default log level
	}
	container.LoggerService = servicesImpl.NewLoggerService(logLevel)
	
	// Initialize JWT service
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "default-secret-key" // Should be set in environment
	}
	container.JWTService = servicesImpl.NewJWTService(jwtSecret)
	
	// Initialize Stripe service
	stripeSecretKey := os.Getenv("STRIPE_SECRET_KEY")
	stripeWebhookKey := os.Getenv("STRIPE_WEBHOOK_SECRET")
	stripeSuccessURL := os.Getenv("STRIPE_SUCCESS_URL")
	stripeCancelURL := os.Getenv("STRIPE_CANCEL_URL")
	if stripeSecretKey == "" {
		stripeSecretKey = "sk_test_placeholder" // Should be set in environment
	}
	if stripeWebhookKey == "" {
		stripeWebhookKey = "whsec_placeholder" // Should be set in environment
	}
	if stripeSuccessURL == "" {
		stripeSuccessURL = "http://localhost:3300/success"
	}
	if stripeCancelURL == "" {
		stripeCancelURL = "http://localhost:3300/cancel"
	}
	container.StripeService = payments.NewStripeService(stripeSecretKey, stripeWebhookKey, stripeSuccessURL, stripeCancelURL)
	
	// Initialize file upload service
	gcsProjectID := os.Getenv("GCS_PROJECT_ID")
	gcsBucketName := os.Getenv("GCS_BUCKET_NAME")
	gcsCredentialsPath := os.Getenv("GCS_CREDENTIALS_PATH")
	
	if gcsProjectID == "" {
		gcsProjectID = "your-project-id" // Should be set in environment
	}
	if gcsBucketName == "" {
		gcsBucketName = "your-bucket-name" // Should be set in environment
	}
	
	// Initialize GCS storage provider
	gcsStorage, err := storage.NewGCSStorageService(gcsProjectID, gcsBucketName, gcsCredentialsPath)
	if err != nil {
		// Fall back to a mock storage service or handle error
		gcsStorage = nil // For now, set to nil - service will handle this gracefully
	}
	
	// Configure file upload service
	maxFileSize := int64(10 * 1024 * 1024) // 10MB default
	if maxSizeEnv := os.Getenv("MAX_FILE_SIZE_MB"); maxSizeEnv != "" {
		if maxSizeMB, err := strconv.ParseInt(maxSizeEnv, 10, 64); err == nil {
			maxFileSize = maxSizeMB * 1024 * 1024
		}
	}
	
	// Use all common file types by default
	allowedTypes := storage.AllCommonTypes
	
	container.FileUploadService = storage.NewFileUploadService(gcsStorage, maxFileSize, allowedTypes)
	
	// Initialize SSO client
	container.SSOClient = sdk.NewSSOClient(cfg.SSOServiceURL)
	
	// Initialize Subscription Service client
	subscriptionServiceURL := os.Getenv("SUBSCRIPTION_SERVICE_URL")
	if subscriptionServiceURL == "" {
		subscriptionServiceURL = "http://localhost:8500"
	}
	subscriptionServiceAPIKey := os.Getenv("SUBSCRIPTION_SERVICE_API_KEY")
	
	if subscriptionServiceAPIKey != "" {
		container.SubscriptionServiceClient = subscriptionSDK.NewClient(subscriptionServiceURL, subscriptionSDK.WithAPIKey(subscriptionServiceAPIKey))
	} else {
		container.SubscriptionServiceClient = subscriptionSDK.NewClient(subscriptionServiceURL)
	}
	
	// Initialize Settings Service client
	settingsServiceURL := os.Getenv("SETTINGS_SERVICE_URL")
	if settingsServiceURL == "" {
		settingsServiceURL = "http://localhost:8600" // Default settings service URL
	}
	settingsServiceAPIKey := os.Getenv("SETTINGS_SERVICE_API_KEY")
	if settingsServiceAPIKey == "" {
		settingsServiceAPIKey = "default-settings-key" // Should be set in environment
	}
	
	container.SettingsClient = external.NewSettingsClient(settingsServiceURL, settingsServiceAPIKey, nil)
	
	// Initialize services
	container.UserService = services.NewUserService(container.UserRepository)
	
	// Initialize subscription service first (needed by organization service)
	container.SubscriptionService = services.NewSubscriptionService(
		container.SubscriptionPlanRepository,
		container.OrganizationSubscriptionRepository,
		container.AICreditTransactionRepository,
		container.OrganizationRepository,
	)
	
	// Now initialize organization service with subscription service dependency
	container.OrganizationService = services.NewOrganizationService(container.OrganizationRepository, container.OrganizationMembershipRepository, container.SubscriptionService, container.SubscriptionServiceClient)
	
	container.ProjectService = services.NewProjectService(
		container.ProjectRepository,
		container.ProjectLocaleRepository,
		container.ResourceRepository,
		container.OrganizationRepository,
		container.LocaleRepository,
		// container.WorkspaceRepository, // TODO: Implement
	)
	container.TranslationService = services.NewTranslationService(
		container.TranslationRepository,
		container.TranslationKeyRepository,
		container.LocaleRepository,
		container.TranslationHistoryRepository,
		nil, // TODO: Fix SettingsClient interface compatibility
	)
	container.LocaleService = services.NewLocaleService(container.LocaleRepository)
	
	// Initialize AI translation provider (infrastructure implementation of domain interface)
	container.AITranslationProvider = ai.NewGeminiTranslationProvider(
		os.Getenv("GEMINI_API_KEY"),
		os.Getenv("GEMINI_MODEL"),
	)
	
	// Initialize domain services
	container.TranslationDomainService = servicesImpl.NewTranslationDomainService(
		container.TranslationRepository,
		container.TranslationKeyRepository,
		container.AITranslationProvider,
	)
	
	// Initialize auth service with configuration
	authConfig := services.AuthConfig{
		JWTSecret:                jwtSecret,
		JWTExpirationHours:       getIntEnv("JWT_EXPIRATION_HOURS", 24),
		JWTRefreshExpirationDays: getIntEnv("JWT_REFRESH_EXPIRATION_DAYS", 30),
		PasswordMinLength:        getIntEnv("PASSWORD_MIN_LENGTH", 8),
		RequireEmailVerification: os.Getenv("REQUIRE_EMAIL_VERIFICATION") == "true",
		AllowSelfRegistration:    os.Getenv("ALLOW_SELF_REGISTRATION") != "false",
		SessionTimeoutHours:      getIntEnv("SESSION_TIMEOUT_HOURS", 24),
		MaxLoginAttempts:         getIntEnv("MAX_LOGIN_ATTEMPTS", 5),
		LockoutDurationMinutes:   getIntEnv("LOCKOUT_DURATION_MINUTES", 15),
		TokenExpirationMinutes:   getIntEnv("TOKEN_EXPIRATION_MINUTES", 60),
	}
	
	// Initialize email service
	emailService := servicesImpl.NewEmailService(
		os.Getenv("SMTP_HOST"),
		getIntEnv("SMTP_PORT", 587),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("SMTP_PASSWORD"),
		os.Getenv("FROM_EMAIL"),
		os.Getenv("FROM_NAME"),
	)
	
	container.AuthService = services.NewAuthService(
		container.UserRepository,
		container.SessionRepository,
		container.RefreshTokenRepository,
		container.PasswordResetTokenRepository,
		container.EmailVerificationTokenRepository,
		container.LoginAttemptRepository,
		container.AuthAuditRepository,
		container.JWTService,
		emailService,
		authConfig,
	)
	
	// Initialize API key service
	container.APIKeyService = services.NewAPIKeyService(
		container.APIKeyRepository,
		container.UserRepository,
		container.OrganizationRepository,
	)
	
	container.JobService = services.NewJobService(
		container.JobRepository,
		container.JobScheduleRepository,
		container.OrganizationRepository,
		container.UserRepository,
		container.ProjectRepository,
	)
	
	// Initialize membership service
	container.MembershipService = services.NewMembershipService(
		container.OrganizationMembershipRepository,
		container.UserRepository,
		container.OrganizationRepository,
	)
	
	// Initialize permission group service
	container.PermissionGroupService = services.NewPermissionGroupService(
		container.PermissionGroupRepository,
		container.OrganizationMembershipRepository,
	)
	
	// Initialize audit log service
	container.AuditLogService = services.NewAuditLogService(
		container.AuditLogRepository,
		container.UserRepository,
	)
	
	// Initialize workspace service
	container.WorkspaceService = nil // TODO: services.NewWorkspaceService(
		// container.WorkspaceRepository, // TODO: Implement
		// container.WorkspaceMemberRepository,
		// container.OrganizationRepository,
		// container.ProjectRepository,
		// container.SubscriptionService,
	// )
	
	// Initialize AI translation service
	container.AITranslationService = nil // TODO: services.NewAITranslationService(
		// container.SubscriptionServiceClient,
		// container.ProjectRepository,
		// container.TranslationService,
		// container.WorkspaceRepository, // TODO: Implement
	// )
	
	// Initialize smart translation service
	container.SmartTranslationService = nil // TODO: services.NewSmartTranslationService(
		// container.ProjectRepository,
		// container.TranslationRepository,
		// container.TranslationKeyRepository,
		// container.LocaleRepository,
		// container.TranslationHistoryRepository,
		// container.NamespaceRepository,
		// container.AITranslationProvider,
		// container.SubscriptionServiceClient,
		// container.DB, // Add DB for raw SQL fallback
	// )
	
	// Initialize collaborative translation service
	container.CollaborativeTranslationService = nil // TODO: services.NewCollaborativeTranslationService(
		// container.WorkspaceRepository, // TODO: Implement
		// container.WorkspaceMemberRepository,
		// container.ProjectRepository,
		// container.TranslationService,
		// container.OrganizationRepository,
	// )
	
	// Initialize API integration service
	container.APIIntegrationService = nil // TODO: services.NewAPIIntegrationService(
		// container.SSOClient,
		// container.SubscriptionServiceClient,
		// container.ProjectRepository,
		// container.TranslationService,
		// container.OrganizationRepository,
	// )
	
	// Initialize translation workflow service (main orchestrator)
	container.TranslationWorkflowService = nil // TODO: services.NewTranslationWorkflowService(
		// container.WorkspaceService,
		// container.ProjectService,
		// container.AITranslationService,
		// container.CollaborativeTranslationService,
		// container.APIIntegrationService,
	// )
	
	// Initialize handlers
	container.UserHandler = handlers.NewUserHandler(container.UserService)
	container.OrganizationHandler = handlers.NewOrganizationHandler(container.OrganizationService)
	container.ProjectHandler = handlers.NewProjectHandler(container.ProjectService, container.OrganizationService, container.OrganizationMembershipRepository)
	container.TranslationHandler = handlers.NewTranslationHandler(container.TranslationService, container.LocaleService)
	container.LocaleHandler = handlers.NewLocaleHandler(container.LocaleService)
	container.AuthHandler = handlers.NewAuthHandler(container.AuthService, container.SSOClient)
	container.APIKeyHandler = handlers.NewAPIKeyHandler(container.APIKeyService)
	container.SubscriptionHandler = handlers.NewSubscriptionHandler(container.SubscriptionService, container.StripeService, container.SubscriptionServiceClient)
	container.JobHandler = handlers.NewJobHandler(container.JobService)
	container.MembershipHandler = handlers.NewMembershipHandler(container.MembershipService)
	container.PermissionGroupHandler = handlers.NewPermissionGroupHandler(container.PermissionGroupService)
	container.AuditLogHandler = handlers.NewAuditLogHandler(container.AuditLogService)
	container.AIHandler = handlers.NewAIHandler(container.AITranslationProvider, container.SubscriptionService, container.SubscriptionServiceClient)
	container.StorageHandler = handlers.NewStorageHandler(container.FileUploadService)
	container.SmartTranslationHandler = nil // TODO: handlers.NewSmartTranslationHandler(container.SmartTranslationService)
	container.SubscriptionServiceTestHandler = nil // TODO: handlers.NewSubscriptionServiceTestHandler(container.SubscriptionServiceClient)
	
	return container
}

// getIntEnv gets an integer environment variable with a default value
func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}