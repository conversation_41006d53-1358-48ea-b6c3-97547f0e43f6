name: Deploy to Cloud Run

on:
  push:
    branches: [main, develop]
  workflow_dispatch:

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: ${{ secrets.GCP_REGION || 'us-central1' }}
  FRONTEND_SERVICE_NAME: adc-frontend
  BACKEND_SERVICE_NAME: adc-backend

jobs:
  deploy-backend:
    name: Deploy Backend to Cloud Run
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Build and push Docker image
        run: |
          cd backend-go
          docker build -t gcr.io/$PROJECT_ID/$BACKEND_SERVICE_NAME:$GITHUB_SHA .
          docker push gcr.io/$PROJECT_ID/$BACKEND_SERVICE_NAME:$GITHUB_SHA

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy $BACKEND_SERVICE_NAME \
            --image gcr.io/$PROJECT_ID/$BACKEND_SERVICE_NAME:$GITHUB_SHA \
            --region $REGION \
            --platform managed \
            --allow-unauthenticated \
            --set-env-vars="JWT_SECRET=${{ secrets.JWT_SECRET }},DATABASE_URL=${{ secrets.DATABASE_URL }},SMTP_HOST=${{ secrets.SMTP_HOST }},SMTP_PORT=${{ secrets.SMTP_PORT }},SMTP_USERNAME=${{ secrets.SMTP_USERNAME }},SMTP_PASSWORD=${{ secrets.SMTP_PASSWORD }},STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }},STRIPE_WEBHOOK_SECRET=${{ secrets.STRIPE_WEBHOOK_SECRET }}" \
            --memory 1Gi \
            --cpu 1 \
            --port 8080 \
            --min-instances 0 \
            --max-instances 10

  deploy-frontend:
    name: Deploy Frontend to Cloud Run
    runs-on: ubuntu-latest
    needs: deploy-backend
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Get backend URL
        id: backend-url
        run: |
          BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE_NAME --region=$REGION --format='value(status.url)')
          echo "backend_url=$BACKEND_URL" >> $GITHUB_OUTPUT

      - name: Build and push Docker image
        run: |
          cd frontend
          docker build \
            --build-arg NEXT_PUBLIC_API_URL=${{ steps.backend-url.outputs.backend_url }} \
            --build-arg BACKEND_API_URL=${{ steps.backend-url.outputs.backend_url }} \
            --build-arg NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }} \
            --build-arg NEXTAUTH_URL=${{ secrets.NEXTAUTH_URL }} \
            --build-arg GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }} \
            --build-arg GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET }} \
            -t gcr.io/$PROJECT_ID/$FRONTEND_SERVICE_NAME:$GITHUB_SHA .
          docker push gcr.io/$PROJECT_ID/$FRONTEND_SERVICE_NAME:$GITHUB_SHA

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy $FRONTEND_SERVICE_NAME \
            --image gcr.io/$PROJECT_ID/$FRONTEND_SERVICE_NAME:$GITHUB_SHA \
            --region $REGION \
            --platform managed \
            --allow-unauthenticated \
            --update-env-vars NEXT_PUBLIC_API_URL="${{ steps.backend-url.outputs.backend_url }}" \
            --update-env-vars BACKEND_API_URL="${{ steps.backend-url.outputs.backend_url }}" \
            --update-env-vars NEXTAUTH_SECRET="${{ secrets.NEXTAUTH_SECRET }}" \
            --update-env-vars NEXTAUTH_URL="${{ secrets.NEXTAUTH_URL }}" \
            --update-env-vars GOOGLE_CLIENT_ID="${{ secrets.GOOGLE_CLIENT_ID }}" \
            --update-env-vars GOOGLE_CLIENT_SECRET="${{ secrets.GOOGLE_CLIENT_SECRET }}" \
            --memory 1Gi \
            --cpu 1 \
            --port 3000 \
            --min-instances 0 \
            --max-instances 10

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-backend, deploy-frontend]
    if: always()
    
    steps:
      - name: Get service URLs
        if: success()
        run: |
          FRONTEND_URL=$(gcloud run services describe $FRONTEND_SERVICE_NAME --region=$REGION --format='value(status.url)')
          BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE_NAME --region=$REGION --format='value(status.url)')
          echo "Frontend deployed at: $FRONTEND_URL"
          echo "Backend deployed at: $BACKEND_URL"