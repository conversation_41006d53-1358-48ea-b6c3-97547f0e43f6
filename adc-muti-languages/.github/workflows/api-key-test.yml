name: Comprehensive API Testing Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test against'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      test_suite:
        description: 'Test suite to run'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - auth
        - rbac
        - organization
        - translation
        - api-keys
        - advanced-api-keys

jobs:
  deploy-and-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: adc_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.23'

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Install dependencies
        run: |
          cd backend-go
          go mod download
          go mod tidy

      - name: Set up test environment
        run: |
          cd backend-go
          cat > .env.test << 'EOF'
          export PORT=8300
          export GIN_MODE=debug
          export BASE_URL=http://localhost:8300
          export DATABASE_URL=postgres://postgres:postgres@localhost:5432/adc_test?sslmode=disable
          export APP_ENV=test
          export JWT_SECRET=test-secret-key-for-ci-testing
          export JWT_REFRESH_SECRET=test-refresh-secret-for-ci
          export SMTP_HOST=localhost
          export SMTP_PORT=587
          export SMTP_USERNAME=test
          export SMTP_PASSWORD=test
          export STRIPE_SECRET_KEY=sk_test_dummy
          export STRIPE_PUBLISHABLE_KEY=pk_test_dummy
          export STRIPE_WEBHOOK_SECRET=whsec_dummy
          EOF

      - name: Build application
        run: |
          cd backend-go
          go build -o adc-backend cmd/main.go

      - name: Run database migrations
        run: |
          cd backend-go
          source .env.test
          go run cmd/migrate/main.go -action=up

      - name: Start backend server
        run: |
          cd backend-go
          source .env.test
          ./adc-backend &
          echo $! > backend.pid
          # Wait for server to start
          for i in {1..30}; do
            if curl -s http://localhost:8300/health > /dev/null; then
              echo "Server started successfully"
              break
            fi
            sleep 1
            if [ $i -eq 30 ]; then
              echo "Server failed to start"
              exit 1
            fi
          done

      - name: Make test scripts executable
        run: |
          cd backend-go
          chmod +x scripts/*.sh

      - name: Run Comprehensive Test Suite
        run: |
          cd backend-go
          source .env.test
          
          # Determine which test suite to run based on input
          if [ "${{ github.event.inputs.test_suite }}" = "auth" ]; then
            ./scripts/test-auth-flows.sh
          elif [ "${{ github.event.inputs.test_suite }}" = "rbac" ]; then
            ./scripts/test-rbac-flows.sh  
          elif [ "${{ github.event.inputs.test_suite }}" = "organization" ]; then
            ./scripts/test-organization-flows.sh
          elif [ "${{ github.event.inputs.test_suite }}" = "translation" ]; then
            ./scripts/test-translation-flows.sh
          elif [ "${{ github.event.inputs.test_suite }}" = "api-keys" ]; then
            ./scripts/test-api-keys.sh
          elif [ "${{ github.event.inputs.test_suite }}" = "advanced-api-keys" ]; then
            ./scripts/test-advanced-api-keys.sh
          else
            # Run all test suites
            ./scripts/run-all-tests.sh
          fi

      - name: Run Go integration tests (if available)
        continue-on-error: true
        run: |
          cd backend-go
          source .env.test
          if [ -d "tests/integration" ]; then
            go test -v ./tests/integration/... -timeout 30m
          else
            echo "No Go integration tests found"
          fi

      - name: Run Go flow tests (if available)
        continue-on-error: true
        run: |
          cd backend-go
          source .env.test
          if [ -d "tests/flow" ]; then
            go test -v ./tests/flow/... -timeout 30m
          else
            echo "No Go flow tests found"
          fi

      - name: Generate test report
        if: always()
        run: |
          echo "## Comprehensive API Test Results" >> $GITHUB_STEP_SUMMARY
          echo "### Test Environment" >> $GITHUB_STEP_SUMMARY
          echo "- Database: PostgreSQL 15" >> $GITHUB_STEP_SUMMARY
          echo "- Backend URL: http://localhost:8300" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: test" >> $GITHUB_STEP_SUMMARY
          echo "- Test Suite: ${{ github.event.inputs.test_suite || 'all' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Add results from comprehensive test suite
          if [ -f backend-go/test-results/overall-test-results-*.txt ]; then
            echo "### Overall Test Results" >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
            cat backend-go/test-results/overall-test-results-*.txt | head -50 >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
          fi
          
          # Add individual test suite results
          echo "### Individual Test Suite Results" >> $GITHUB_STEP_SUMMARY
          
          for result_file in backend-go/scripts/*test-results*.txt backend-go/test-results.txt; do
            if [ -f "$result_file" ]; then
              suite_name=$(basename "$result_file" | sed 's/-test-results.txt//' | sed 's/test-results.txt/Original API Key Flow/')
              echo "#### $suite_name" >> $GITHUB_STEP_SUMMARY
              echo '```' >> $GITHUB_STEP_SUMMARY
              tail -10 "$result_file" >> $GITHUB_STEP_SUMMARY
              echo '```' >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi
          done

      - name: Stop backend server
        if: always()
        run: |
          if [ -f backend-go/backend.pid ]; then
            kill $(cat backend-go/backend.pid) || true
            rm backend-go/backend.pid
          fi

      - name: Upload test artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-test-results-${{ github.run_number }}
          path: |
            backend-go/test-results/
            backend-go/scripts/*test-results*.txt
            backend-go/test-results.txt
            backend-go/logs/
          retention-days: 14